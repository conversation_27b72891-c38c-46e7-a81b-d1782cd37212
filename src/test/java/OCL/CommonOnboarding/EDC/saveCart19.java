package OCL.CommonOnboarding.EDC;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import Request.CommonOnboardingEDC.getLeadStatus;
import com.goldengate.common.RetryAnalyzer;
import io.restassured.path.json.JsonPath;
import io.restassured.path.json.config.JsonPathConfig;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.cartsave;
import Request.CommonOnboardingEDC.deviceSummary;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class saveCart19 extends BaseMethod {

	private MiddlewareServices middlewareServicesObject;
	private ConfirmPlan15 planConfirm;
	private CreateMCOLead2 mcoLead;
	private CreateDeviceOnboardingLead13 leadObj;
	private AddBank8 objbank;
	private PartialSave18 parobj;
	private static final Logger LOGGER = LogManager.getLogger(saveCart19.class);

	public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
	public static String version = "5.1.1";
	public static String NewMerchantmobileNo = "**********";
	public static String planID="11608";
	public static String deviceID="10001";

	public static String Individualentity = "INDIVIDUAL";
	public static String user_Type = "common_merchant";
	boolean skipOTPTRUE=true;    
	boolean skipOTPFalse=false;
	boolean onlySaveTncFalse=false;  
	boolean onlySaveTncTrue=true;
	public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";

	public static String expecteddisplayMessage="Cart is empty. Please select a plan and proceed ahead (Ref: G-Kfza-119-400)";
	public static String message="version is empty in header";
	public static String errorCode="VERSION_FAILURE";

	private String sToken;

	public saveCart19() {
		super();
		try {
			middlewareServicesObject = new MiddlewareServices();
			planConfirm = new ConfirmPlan15();
			mcoLead = new CreateMCOLead2();
			leadObj = new CreateDeviceOnboardingLead13();
			objbank = new AddBank8();
			parobj = new PartialSave18();
		} catch (Exception e) {
			LOGGER.error("Error initializing saveCart19: " + e.getMessage());
		}
	}

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("**********", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
		waitForLoad(3000);
	}

	@Test(priority = 0, description = "TC_1_saveCart", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_saveCart() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> list=TC_1_savingCart("**********");
		//Add logger
		LOGGER.info("Cart List"+list.toString());

	}


	@Test(priority = 0, description = "TC_1_saveCartWithAMEXDisable", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_saveCartWithAMEXDisable() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> list=TC_11_saveCart_DisableAMEXMDR("**********");
		//Add logger
		LOGGER.info("Cart List"+list.toString());

	}


	@Test(priority = 0, description = "TC_1_saveCartWithdinersDisable", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_saveCartWithdinersDisable() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> list=TC_12_saveCart_DisableAMEXDINERSMDR("**********");
		//Add logger
		LOGGER.info("Cart List"+list.toString());

	}

	@Test(priority = 0, description = "TC_1_saveCartWithCorporateDisable", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_saveCartWithCorporateDisable() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> list=TC_13_saveCart_DisableAMEXDINERSCORPORATEMDR("**********");
		//Add logger
		LOGGER.info("Cart List"+list.toString());

	}

	@Test(priority = 0, description = "TC_1_saveCartWithPrepaidMDRDisable", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_saveCartWithPrepaidMDRDisable() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> list=TC_14_saveCart_DisablePREPAIDMDR("**********");
		//Add logger
		LOGGER.info("Cart List"+list.toString());

	}







	@Test(priority = 0, description = "TC_1_savingCart", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public List<Object> TC_1_savingCart(String mobilenumber) throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC(mobilenumber);
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
//		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", deviceOnboardingLead);
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();
//				        Assert.assertTrue(httpcodenew == 200, "Testcase Failed");

		// Validate Json Response
				        String response = summaryrespObj.asString();
				        System.out.println("Response is : " + response);
				        JsonPath jsonPathEvaluator = summaryrespObj.jsonPath();
		Object plan_id = jsonPathEvaluator.get("cart_details.cart_items[0].plan_id");
		Object plan_name = jsonPathEvaluator.get("cart_details.cart_items[0].plan_name");
		Object device_id = jsonPathEvaluator.get("cart_details.cart_items[0].device_id");
		Object icon_url = jsonPathEvaluator.get("cart_details.cart_items[0].icon_url");
				        Object addOnsApplicable = jsonPathEvaluator.get("cart_details.cart_items[0].addOnsApplicable");
		Object addOntype = jsonPathEvaluator.get("cart_details.cart_items[0].add_ons[0].type");
		Object addOntitle = jsonPathEvaluator.get("cart_details.cart_items[0].add_ons[0].title");
		JsonPath.config = new JsonPathConfig().numberReturnType(JsonPathConfig.NumberReturnType.BIG_DECIMAL);
		float peritemprice = jsonPathEvaluator.get("cart_details.cart_items[0].add_ons[0].per_item_price");
		float totalprice = jsonPathEvaluator.get("cart_details.cart_items[0].add_ons[0].total_price");
		Object quantity = jsonPathEvaluator.get("cart_details.cart_items[0].quantity");
		Object typeUpfrontCharge = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].type");
		Object titleUpfrontCharge = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].title");
		float peritempriceUpfrontCharge = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].per_item_price");
		float total_priceUpfrontCharge = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].total_price");
		Object amcType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[0].type");
		Object amcTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[0].title");
		float amcTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[0].total_price");
		float amcitemPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[0].per_item_price");
		Object deviceRentType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[1].type");
		Object deviceRentTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[1].title");
		float deviceRentTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[1].total_price");
		float deviceRentitemPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[1].per_item_price");
		Object installationchargeType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[2].type");
		Object installationchargeTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[2].title");
		float installationchargeTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[2].total_price");
		float installationchargeitemPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[2].per_item_price");
		Object usagedepositType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[3].type");
		Object usagedepositTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[3].title");
		float usagedepositTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[3].total_price");
		float usagedepositPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[3].per_item_price");
		Object typeMonthlyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].type");
		Object titleMonthlyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].title");
		float peritempriceMonthlyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].per_item_price");
		float total_priceMonthlyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].total_price");

		Object simChargeType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[0].type");
		Object simChargeTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[0].title");
		float simChargeTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[0].total_price");
		float simChargePrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[0].per_item_price");

		Object deviceRentalType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[1].type");
		Object deviceRentalTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[1].title");
		float deviceRentalTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[1].total_price");
		float deviceRentalPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[1].per_item_price");
		Object subTitleMonthlyNextduedate = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].subtitle");
		Object typeAnnualyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].type");
		Object titleAnnualyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].title");
		float peritempriceAnnualyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].per_item_price");
		float total_priceAnnualyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].total_price");
		Object amcannualyType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].items[0].type");
		Object amcannualyTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].items[0].title");
		float amcannualyTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].items[0].total_price");
		float amcannualyPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].items[0].per_item_price");
		Object subTitleAnnualyNextduedate = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].subtitle");
		Object cartaddOnType = jsonPathEvaluator.get("cart_details.additional_items[0].type");
		Object cartaddOnTitle = jsonPathEvaluator.get("cart_details.additional_items[0].title");
		Object cartaddOnicon_url = jsonPathEvaluator.get("cart_details.additional_items[0].icon_url");
		Object cartaddOnAddOnsType = jsonPathEvaluator.get("cart_details.additional_items[0].add_ons[0].type");
		Object cartaddOnAddOnsTitle = jsonPathEvaluator.get("cart_details.additional_items[0].add_ons[0].title");
		Object cartaddOnAddOnsUpfronttype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].type");
		Object cartaddOnAddOnsUpfronttitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].title");
		float cartaddOnAddOnsUpfrontper_item_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].per_item_price");
		float cartaddOnAddOnsUpfronttotal_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].total_price");

		Object emiOneTimeChargetype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[0].type");
		Object emiOneTimeChargetitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[0].title");
		float emiOneTimeCharge_item_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[0].per_item_price");
		float emiOneTimeChargetotal_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[0].total_price");
		Object emiRentalChargetype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[1].type");
		Object emiRentalChargetitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[1].title");
		float emiRentalCharge_item_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[1].per_item_price");
		float emiRentalChargetotal_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[1].total_price");

		Object additionalmonthly_rentaltype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].type");
		Object additionalmonthly_rentaltitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].title");
		float additionalmonthly_rental_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].per_item_price");
		float additionalmonthly_total_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].total_price");
		Object additionalmonthly_emiRentalChargetype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].items[0].type");
		Object additionalmonthly_emiRentalChargeTitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].items[0].title");
		float additionalmonthly_emiRentalCharge_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].items[0].per_item_price");
		float additionalmonthly_emiRentalCharge_total_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].items[0].total_price");
		Object additionalmonthly_subtitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].subtitle");

		Object summaryamounttype = jsonPathEvaluator.get("cart_details.summary.type");
		Object summaryamounttitle = jsonPathEvaluator.get("cart_details.summary.title");
		Object summaryitemtitle = jsonPathEvaluator.get("cart_details.summary.items[0].title");
		Object summaryitemsubtitle = jsonPathEvaluator.get("cart_details.summary.items[0].subTitle");
		float summaryitemtoalPrice = jsonPathEvaluator.get("cart_details.summary.items[0].total_price");
		Object summaryitemtitleNew = jsonPathEvaluator.get("cart_details.summary.items[1].title");
		Object summaryitemsubtitleNew = jsonPathEvaluator.get("cart_details.summary.items[1].subTitle");
		float summaryitemtoalPriceNew = jsonPathEvaluator.get("cart_details.summary.items[1].total_price");
		float summaryTotalPrice = jsonPathEvaluator.get("cart_details.summary.total_price");
		Object summarytotal_price_label = jsonPathEvaluator.get("cart_details.summary.total_price_label");

		Object mdrCC = jsonPathEvaluator.get("cart_details.mdr[0].paymode");
		Boolean mdrCCdisable = jsonPathEvaluator.get("cart_details.mdr[0].disable");
		Object mdrAMEX = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[0].factorType");
		Boolean mdrAMEXdisable = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[0].disable");
		Object mdrDINERS = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[1].factorType");
		Boolean mdrDINERSdisable = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[1].disable");
		Object mdrCorporationCard = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[2].factorType");
		Boolean mdrCorporationCarddisable = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[2].disable");
		Object mdrDC = jsonPathEvaluator.get("cart_details.mdr[1].paymode");
		Boolean mdrDCdisable = jsonPathEvaluator.get("cart_details.mdr[1].disable");
		Object mdrPREPAID = jsonPathEvaluator.get("cart_details.mdr[1].feeRateFactor[0].factorType");
		Boolean mdrPREPAIDdisable = jsonPathEvaluator.get("cart_details.mdr[1].feeRateFactor[0].disable");






		System.out.println("Cart Details : "+plan_id);
		Object expectedPlan_id="11608";
//		Assert.assertEquals(plan_id,expectedPlan_id);
		System.out.println("plan_name : "+plan_name);
		Object expectedPlan_name="REFUND INFO Super Saver Plan A910 30th Jan";
//		Assert.assertEquals(plan_name,expectedPlan_name);
		System.out.println("device_id : "+device_id);
		Object expecteddevice_id="10001";
//		Assert.assertEquals(device_id,expecteddevice_id);
		System.out.println("icon_url : "+icon_url);
		Object expectedicon_url="https://d2hrf3g3dwv6bl.cloudfront.net/Icons/deviceIcons/A910.svg";
//		Assert.assertEquals(icon_url,expectedicon_url);
		System.out.println("addOntype : "+addOntype);
		Object expectedaddOntype="amc";
//		Assert.assertEquals(addOntype,expectedaddOntype);
		System.out.println("addOntitle : "+addOntitle);
		Object expectedaddOntitle="Add Ons:AMC";
//		Assert.assertEquals(addOntitle,expectedaddOntitle);
		System.out.println("peritempricefloat : "+peritemprice);
		float expectedperitemprice = 250.0f;
//		Assert.assertEquals(peritemprice,expectedperitemprice);
		System.out.println("total_price : "+totalprice);
		float expectedpertotal_price = 250.0f;
//		Assert.assertEquals(totalprice,expectedpertotal_price);
		System.out.println("quantity : "+quantity);

		System.out.println("typeUpfrontCharge : "+typeUpfrontCharge);
		Object expectedtypeUpfrontCharge="up_front_charge";
//		Assert.assertEquals(typeUpfrontCharge,expectedtypeUpfrontCharge);
		System.out.println("titleUpfrontCharge : "+titleUpfrontCharge);
		Object expectedtitleUpfrontCharge="Total Upfront Charge";
//		Assert.assertEquals(titleUpfrontCharge,expectedtitleUpfrontCharge);

		System.out.println("peritempriceUpfrontCharge : "+peritempriceUpfrontCharge);
		float expectedpedperitempriceUpfrontCharge = 6663.0f;
//		Assert.assertEquals(peritempriceUpfrontCharge,expectedpedperitempriceUpfrontCharge);
		System.out.println("total_priceUpfrontCharge : "+total_priceUpfrontCharge);
		float expectedtotal_priceUpfrontCharge = 6663.0f;
//		Assert.assertEquals(total_priceUpfrontCharge,expectedtotal_priceUpfrontCharge);

		System.out.println("amcType : "+amcType);
		Object expectedtypeamcType="amc";
//		Assert.assertEquals(amcType,expectedtypeamcType);
		System.out.println("amcTitle : "+amcTitle);
		Object expectedamcTitle="AMC";
//		Assert.assertEquals(amcTitle,expectedamcTitle);
		System.out.println("amcTotal : "+amcTotal);
		float expectedtotalamcTotal = 250.0f;
//		Assert.assertEquals(amcTotal,expectedtotalamcTotal);
		System.out.println("amcitemPrice : "+amcitemPrice);
		float expectedtotalamcitemPrice = 250.0f;
//		Assert.assertEquals(amcitemPrice,expectedtotalamcitemPrice);
		System.out.println("deviceRentType : "+deviceRentType);
		Object expecteddeviceRentType="deviceRent";
//		Assert.assertEquals(deviceRentType,expecteddeviceRentType);
		System.out.println("deviceRentTitle : "+deviceRentTitle);
		Object expecteddeviceRentTitle="Device Rent";
//		Assert.assertEquals(deviceRentTitle,expecteddeviceRentTitle);
		System.out.println("deviceRentTotal : "+deviceRentTotal);
		float expectedtotaldeviceRentTotal = 350.0f;
//		Assert.assertEquals(deviceRentTotal,expectedtotaldeviceRentTotal);
//		System.out.println("deviceRentitemPrice : "+deviceRentitemPrice);
		float expectedtotaldeviceRentitemPrice = 350.0f;
//		Assert.assertEquals(deviceRentitemPrice,expectedtotaldeviceRentitemPrice);
		System.out.println("installationchargeType : "+installationchargeType);
		Object expecteddeviceinstallationchargeType="installationCharge";
//		Assert.assertEquals(installationchargeType,expecteddeviceinstallationchargeType);
		System.out.println("installationchargeTitle : "+installationchargeTitle);
		Object expecteddeviceinstallationchargeTitle="Installation Charge";
//		Assert.assertEquals(installationchargeTitle,expecteddeviceinstallationchargeTitle);
		System.out.println("installationchargeTotal : "+installationchargeTotal);
		float expectedtotalinstallationchargeTotal = 5000.0f;
//		Assert.assertEquals(installationchargeTotal,expectedtotalinstallationchargeTotal);
		System.out.println("installationchargeitemPrice : "+installationchargeitemPrice);
		float expectedtotalinstallationchargeitemPrice = 5000.0f;
//		Assert.assertEquals(installationchargeitemPrice,expectedtotalinstallationchargeitemPrice);
		System.out.println("usagedepositType : "+usagedepositType);
		Object expecteddeviceusagedepositType="usageDeposit";
//		Assert.assertEquals(usagedepositType,expecteddeviceusagedepositType);
		System.out.println("usagedepositTitle : "+usagedepositTitle);
		Object expecteddeviceusagedepositTitle="Usage Deposit";
//		Assert.assertEquals(usagedepositTitle,expecteddeviceusagedepositTitle);
		System.out.println("usagedepositTotal : "+usagedepositTotal);
		float expectedtotalusagedepositTotal = 1000.0f;
//		Assert.assertEquals(usagedepositTotal,expectedtotalusagedepositTotal);
		System.out.println("usagedepositPrice : "+usagedepositPrice);
		float expectedusagedepositPrice = 1000.0f;
//		Assert.assertEquals(usagedepositPrice,expectedusagedepositPrice);
		System.out.println("typeMonthlyRental : "+typeMonthlyRental);
		Object expectedtypeMonthlyRental="monthly_rental";
//		Assert.assertEquals(typeMonthlyRental,expectedtypeMonthlyRental);
		System.out.println("titleMonthlyRental : "+titleMonthlyRental);
		Object expectedtitleMonthlyRental="Monthly Rental";
//		Assert.assertEquals(titleMonthlyRental,expectedtitleMonthlyRental);
		System.out.println("peritempriceMonthlyRental : "+peritempriceMonthlyRental);
		float expectedperitempriceMonthlyRental = 350.0f;
//		Assert.assertEquals(peritempriceMonthlyRental,expectedperitempriceMonthlyRental);
		System.out.println("total_priceMonthlyRental : "+total_priceMonthlyRental);
		float expectedtotal_priceMonthlyRental = 350.0f;
//		Assert.assertEquals(total_priceMonthlyRental,expectedtotal_priceMonthlyRental);
		System.out.println("simChargeType : "+simChargeType);
		Object expectedsimChargeType="simCharge";
//		Assert.assertEquals(simChargeType,expectedsimChargeType);
		System.out.println("simChargeTitle : "+simChargeTitle);
		Object expectedsimChargeTitle="Sim Charge";
//		Assert.assertEquals(simChargeTitle,expectedsimChargeTitle);
		System.out.println("simChargeTotal : "+simChargeTotal);
		float expectedsimChargeTotal = 0.0f;
//		Assert.assertEquals(simChargeTotal,expectedsimChargeTotal);
		System.out.println("simChargePrice : "+simChargePrice);
		float expectedtotalsimChargePrice = 0.0f;
//		Assert.assertEquals(simChargePrice,expectedtotalsimChargePrice);
		System.out.println("deviceRentalType : "+deviceRentalType);
		Object expecteddeviceRentalType="deviceRent";
//		Assert.assertEquals(deviceRentalType,expecteddeviceRentalType);
		System.out.println("deviceRentalTitle : "+deviceRentalTitle);
		Object expecteddeviceRentalTitle="Device Rent";
//		Assert.assertEquals(deviceRentalTitle,expecteddeviceRentalTitle);
		System.out.println("deviceRentalTotal : "+deviceRentalTotal);
		float expecteddeviceRentalTotal = 350.0f;
//		Assert.assertEquals(deviceRentalTotal,expecteddeviceRentalTotal);
		System.out.println("deviceRentalPrice : "+deviceRentalPrice);
		float expectedtotaldeviceRentalPrice = 350.0f;
//		Assert.assertEquals(deviceRentalPrice,expectedtotaldeviceRentalPrice);
		System.out.println("subTitleMonthlyNextduedate : "+subTitleMonthlyNextduedate);
		System.out.println("typeAnnualyRental : "+typeAnnualyRental);
		Object expectedtypeAnnualyRental="annual_rental";
//		Assert.assertEquals(typeAnnualyRental,expectedtypeAnnualyRental);
		System.out.println("titleAnnualyRental : "+titleAnnualyRental);
		Object expecteddevicetitleAnnualyRental="Annual Rental";
//		Assert.assertEquals(titleAnnualyRental,expecteddevicetitleAnnualyRental);
		System.out.println("peritempriceAnnualyRental : "+peritempriceAnnualyRental);
		float expectedtotalperitempriceAnnualyRental = 250.0f;
//		Assert.assertEquals(peritempriceAnnualyRental,expectedtotalperitempriceAnnualyRental);
		System.out.println("total_priceAnnualyRental : "+total_priceAnnualyRental);
		float expectedtotal_priceAnnualyRental = 250.0f;
//		Assert.assertEquals(total_priceAnnualyRental,expectedtotal_priceAnnualyRental);
		System.out.println("amcannualyType : "+amcannualyType);
		Object expectedamcannualyType="amc";
//		Assert.assertEquals(amcannualyType,expectedamcannualyType);
		System.out.println("amcannualyTitle : "+amcannualyTitle);
		Object expectedamcannualyTitle="AMC";
//		Assert.assertEquals(amcannualyTitle,expectedamcannualyTitle);
		System.out.println("amcannualyTotal : "+amcannualyTotal);
		float expectedamcannualyTotal = 250.0f;
//		Assert.assertEquals(amcannualyTotal,expectedamcannualyTotal);
		System.out.println("amcannualyPrice : "+amcannualyPrice);
		float expectedamcannualyPrice = 250.0f;
//		Assert.assertEquals(amcannualyPrice,expectedamcannualyPrice);
		System.out.println("subTitleAnnualyNextduedate : "+subTitleAnnualyNextduedate);
		System.out.println("cartaddOnType : "+cartaddOnType);
		Object expectedcartaddOnType="cart_add_ons";
//		Assert.assertEquals(cartaddOnType,expectedcartaddOnType);
		System.out.println("cartaddOnTitle : "+cartaddOnTitle);
		Object expectedcartaddOnTitle="Add-On Services on All Devices";
//		Assert.assertEquals(cartaddOnTitle,expectedcartaddOnTitle);
		System.out.println("cartaddOnicon_url : "+cartaddOnicon_url);
		Object expectedcartaddOnicon_url="https://d2hrf3g3dwv6bl.cloudfront.net/Icons/deviceIcons/icon_lg32_category_main_paytm_mall_bag.jpg";
//		Assert.assertEquals(cartaddOnicon_url,expectedcartaddOnicon_url);
		System.out.println("cartaddOnAddOnsType : "+cartaddOnAddOnsType);
		Object expectedcartaddOnAddOnsType="emiRentalCharge";
//		Assert.assertEquals(cartaddOnAddOnsType,expectedcartaddOnAddOnsType);
		System.out.println("cartaddOnAddOnsTitle : "+cartaddOnAddOnsTitle);
		Object expectedcartaddOnAddOnsTitle="EMI Rental Charge";
//		Assert.assertEquals(cartaddOnAddOnsTitle,expectedcartaddOnAddOnsTitle);
		System.out.println("cartaddOnAddOnsUpfronttype : "+cartaddOnAddOnsUpfronttype);
		Object expectedcartaddOnAddOnsUpfronttype="up_front_charge";
//		Assert.assertEquals(cartaddOnAddOnsUpfronttype,expectedcartaddOnAddOnsUpfronttype);
		System.out.println("cartaddOnAddOnsUpfronttitle : "+cartaddOnAddOnsUpfronttitle);
		Object expectedcartaddOnAddOnsUpfronttitle="Total Upfront Charge";
//		Assert.assertEquals(cartaddOnAddOnsUpfronttitle,expectedcartaddOnAddOnsUpfronttitle);
		System.out.println("cartaddOnAddOnsUpfrontper_item_price : "+cartaddOnAddOnsUpfrontper_item_price);
		float expectedcartaddOnAddOnsUpfrontper_item_price = 0.0f;
//		Assert.assertEquals(cartaddOnAddOnsUpfrontper_item_price,expectedcartaddOnAddOnsUpfrontper_item_price);
		System.out.println("cartaddOnAddOnsUpfronttotal_price : "+cartaddOnAddOnsUpfronttotal_price);
		float expectedcartaddOnAddOnsUpfronttotal_price = 0.0f;
//		Assert.assertEquals(cartaddOnAddOnsUpfronttotal_price,expectedcartaddOnAddOnsUpfronttotal_price);

		System.out.println("emiOneTimeChargetype : "+emiOneTimeChargetype);
		Object expectedemiOneTimeChargetype="emiOneTimeCharge";
//		Assert.assertEquals(emiOneTimeChargetype,expectedemiOneTimeChargetype);
		System.out.println("emiOneTimeChargetitle : "+emiOneTimeChargetitle);
		Object expectedemiOneTimeChargetitle="EMI One Time Charge";
//		Assert.assertEquals(emiOneTimeChargetitle,expectedemiOneTimeChargetitle);
		System.out.println("emiOneTimeCharge_item_price : "+emiOneTimeCharge_item_price);
		float expectedemiOneTimeCharge_item_price = 0.0f;
//		Assert.assertEquals(emiOneTimeCharge_item_price,expectedemiOneTimeCharge_item_price);
		System.out.println("emiOneTimeChargetotal_price : "+emiOneTimeChargetotal_price);
		float expectedemiOneTimeChargetotal_price = 0.0f;
//		Assert.assertEquals(emiOneTimeChargetotal_price,expectedemiOneTimeChargetotal_price);

		System.out.println("emiRentalChargetype : "+emiRentalChargetype);
		Object expectedemiRentalChargetype="emiRentalCharge";
//		Assert.assertEquals(emiRentalChargetype,expectedemiRentalChargetype);
		System.out.println("emiRentalChargetitle : "+emiRentalChargetitle);
		Object expectedemiRentalChargetitle="EMI Rental Charge";
//		Assert.assertEquals(emiRentalChargetitle,expectedemiRentalChargetitle);
		System.out.println("emiRentalCharge_item_price : "+emiRentalCharge_item_price);
		float expectedemiRentalCharge_item_price = 0.0f;
//		Assert.assertEquals(emiRentalCharge_item_price,expectedemiRentalCharge_item_price);
		System.out.println("emiRentalChargetotal_price : "+emiRentalChargetotal_price);
		float expectedemiRentalChargetotal_price = 0.0f;
//		Assert.assertEquals(emiRentalChargetotal_price,expectedemiRentalChargetotal_price);

		System.out.println("additionalmonthly_rentaltype : "+additionalmonthly_rentaltype);
		Object expectedadditionalmonthly_rentaltype="monthly_rental";
//		Assert.assertEquals(additionalmonthly_rentaltype,expectedadditionalmonthly_rentaltype);
		System.out.println("additionalmonthly_rentaltitle : "+additionalmonthly_rentaltitle);
		Object expectedadditionalmonthly_rentaltitle="Monthly Rental";
//		Assert.assertEquals(additionalmonthly_rentaltitle,expectedadditionalmonthly_rentaltitle);

		System.out.println("additionalmonthly_rental_price : "+additionalmonthly_rental_price);
		float expectedadditionalmonthly_rental_price = 0.0f;
//		Assert.assertEquals(additionalmonthly_rental_price,expectedadditionalmonthly_rental_price);
		System.out.println("additionalmonthly_total_price : "+additionalmonthly_total_price);
		float expectedadditionalmonthly_total_price = 0.0f;
//		Assert.assertEquals(additionalmonthly_total_price,expectedadditionalmonthly_total_price);

		System.out.println("additionalmonthly_emiRentalChargetype : "+additionalmonthly_emiRentalChargetype);
		Object expectedadditionalmonthly_emiRentalChargetype="emiRentalCharge";
//		Assert.assertEquals(additionalmonthly_emiRentalChargetype,expectedadditionalmonthly_emiRentalChargetype);
		System.out.println("additionalmonthly_emiRentalChargeTitle : "+additionalmonthly_emiRentalChargeTitle);
		Object expectedadditionalmonthly_emiRentalChargeTitle="EMI Rental Charge";
//		Assert.assertEquals(additionalmonthly_emiRentalChargeTitle,expectedadditionalmonthly_emiRentalChargeTitle);
		System.out.println("additionalmonthly_emiRentalCharge_price : "+additionalmonthly_emiRentalCharge_price);
		float expectedadditionalmonthly_emiRentalCharge_price = 0.0f;
//		Assert.assertEquals(additionalmonthly_emiRentalCharge_price,expectedadditionalmonthly_emiRentalCharge_price);
		System.out.println("additionalmonthly_emiRentalCharge_total_price : "+additionalmonthly_emiRentalCharge_total_price);
		float expectedadditionalmonthly_emiRentalCharge_total_price = 0.0f;
//		Assert.assertEquals(additionalmonthly_emiRentalCharge_total_price,expectedadditionalmonthly_emiRentalCharge_total_price);

		System.out.println("summaryamounttype : "+summaryamounttype);
		Object expectedsummaryamounttype="amount_to_be_collected";
//		Assert.assertEquals(summaryamounttype,expectedsummaryamounttype);
		System.out.println("summaryamounttitle : "+summaryamounttitle);
		Object expectedsummaryamounttitle="Amount to be Collected";
//		Assert.assertEquals(summaryamounttitle,expectedsummaryamounttitle);
		System.out.println("summaryitemtitle : "+summaryitemtitle);
		Object expectedsummaryitemtitle="Add-On Services on All Devices";
//		Assert.assertEquals(summaryitemtitle,expectedsummaryitemtitle);
		System.out.println("summaryitemsubtitle : "+summaryitemsubtitle);
		Object expectedsummaryitemsubtitle=" EMI Rental Charge";
//		Assert.assertEquals(summaryitemsubtitle,expectedsummaryitemsubtitle);
		System.out.println("summaryitemtoalPrice : "+summaryitemtoalPrice);
		float expectedsummaryitemtoalPrice = 0.0f;
//		Assert.assertEquals(summaryitemtoalPrice,expectedsummaryitemtoalPrice);

		System.out.println("summaryitemtitleNew : "+summaryitemtitleNew);
		Object expectedsummaryitemtitleNew="Android with printer A910 123 REFUND INFO Super Saver Plan A910 30th Jan";
//		Assert.assertEquals(summaryitemtitleNew,expectedsummaryitemtitleNew);
		System.out.println("summaryitemsubtitleNew : "+summaryitemsubtitleNew);
		Object expectedsummaryitemsubtitleNew="Add Ons: amc";
//		Assert.assertEquals(summaryitemsubtitleNew,expectedsummaryitemsubtitleNew);
		System.out.println("summaryitemtoalPriceNew : "+summaryitemtoalPriceNew);
		float expectedsummaryitemtoalPriceNew = 6663.0f;
//		Assert.assertEquals(summaryitemtoalPriceNew,expectedsummaryitemtoalPriceNew);
		System.out.println("summaryTotalPrice : "+summaryTotalPrice);
		float expectedsummaryTotalPrice = 6663.0f;
//		Assert.assertEquals(summaryTotalPrice,expectedsummaryTotalPrice);
		System.out.println("summarytotal_price_label : "+summarytotal_price_label);
		Object expectedsummarytotal_price_label="Total Amount";
//		Assert.assertEquals(summarytotal_price_label,expectedsummarytotal_price_label);

		System.out.println("mdrCC : "+mdrCC);
		Object expectedmdrCC="CC";
//		Assert.assertEquals(mdrCC,expectedmdrCC);
		System.out.println("mdrCCdisable : "+mdrCCdisable);
		Boolean expectedmdrCCdisable = false;
//		Assert.assertEquals(mdrCCdisable,expectedmdrCCdisable);
		System.out.println("mdrAMEX : "+mdrAMEX);
		Object expectedmdrAMEX="AMEX";
//		Assert.assertEquals(mdrAMEX,expectedmdrAMEX);
		System.out.println("mdrAMEXdisable : "+mdrAMEXdisable);
		Boolean expectedmdrAMEXdisable = false;
//		Assert.assertEquals(mdrAMEXdisable,expectedmdrAMEXdisable);
		System.out.println("mdrDINERS : "+mdrDINERS);
		Object expectedmdrDINERS="DINERS";
//		Assert.assertEquals(mdrDINERS,expectedmdrDINERS);
		System.out.println("mdrDINERSdisable : "+mdrDINERSdisable);
		Boolean expectedmdrDINERSdisable = false;
//		Assert.assertEquals(mdrDINERSdisable,expectedmdrDINERSdisable);
		System.out.println("mdrCorporationCard : "+mdrCorporationCard);
		Object expectedmdrCorporationCard="CorporationCard";
//		Assert.assertEquals(mdrCorporationCard,expectedmdrCorporationCard);
		System.out.println("mdrCorporationCarddisable : "+mdrCorporationCarddisable);
		Boolean expectedmdrCorporationCarddisable = false;
//		Assert.assertEquals(mdrCorporationCarddisable,expectedmdrCorporationCarddisable);
		System.out.println("mdrDC : "+mdrDC);
		Object expectedmdrDC="DC";
//		Assert.assertEquals(mdrDC,expectedmdrDC);
		System.out.println("mdrDCdisable : "+mdrDCdisable);
		Boolean expectedmdrDCdisable = false;
//		Assert.assertEquals(mdrDCdisable,expectedmdrDCdisable);
		System.out.println("mdrPREPAID : "+mdrPREPAID);
		Object expectedmdrPREPAID = "PrepaidCard";
//		Assert.assertEquals(mdrPREPAID,expectedmdrPREPAID);
		System.out.println("mdrPREPAIDdisable : "+mdrPREPAIDdisable);
		Boolean expectedmdrPREPAIDdisable = false;
//		Assert.assertEquals(mdrPREPAIDdisable,expectedmdrPREPAIDdisable);

		List<Object> lt=new ArrayList<Object>();
		lt.add(plan_id);
		lt.add(plan_name);
		lt.add(device_id);
		lt.add(icon_url);
		lt.add(addOntype);
		lt.add(addOntitle);
		lt.add(peritemprice);
		lt.add(totalprice);
		lt.add(quantity);
		lt.add(typeUpfrontCharge);
		lt.add(titleUpfrontCharge);
		lt.add(peritempriceUpfrontCharge);
		lt.add(total_priceUpfrontCharge);
		lt.add(amcType);
		lt.add(amcTitle);
		lt.add(amcTotal);
		lt.add(amcitemPrice);
		lt.add(deviceRentType);
		lt.add(deviceRentTitle);
		lt.add(deviceRentTotal);
		lt.add(deviceRentitemPrice);
		lt.add(installationchargeType);
		lt.add(installationchargeTitle);
		lt.add(installationchargeTotal);
		lt.add(installationchargeitemPrice);
		lt.add(usagedepositType);
		lt.add(usagedepositTitle);
		lt.add(usagedepositTotal);
		lt.add(usagedepositPrice);
		lt.add(typeMonthlyRental);
		lt.add(titleMonthlyRental);
		lt.add(peritempriceMonthlyRental);
		lt.add(total_priceMonthlyRental);
		lt.add(simChargeType);
		lt.add(simChargeTitle);
		lt.add(simChargePrice);
		lt.add(deviceRentalType);
		lt.add(deviceRentalTitle);
		lt.add(deviceRentalTotal);
		lt.add(deviceRentalPrice);
		lt.add(subTitleMonthlyNextduedate);
		lt.add(typeAnnualyRental);
		lt.add(titleAnnualyRental);
		lt.add(peritempriceAnnualyRental);
		lt.add(total_priceAnnualyRental);
		lt.add(amcannualyType);
		lt.add(amcannualyTitle);
		lt.add(amcannualyTotal);
		lt.add(amcannualyPrice);
		lt.add(subTitleAnnualyNextduedate);
		lt.add(cartaddOnType);
		lt.add(cartaddOnTitle);
		lt.add(cartaddOnicon_url);
		lt.add(cartaddOnAddOnsType);
		lt.add(cartaddOnAddOnsTitle);
		lt.add(cartaddOnAddOnsUpfronttype);
		lt.add(cartaddOnAddOnsUpfronttitle);
		lt.add(cartaddOnAddOnsUpfrontper_item_price);
		lt.add(cartaddOnAddOnsUpfronttotal_price);
		lt.add(emiOneTimeChargetype);
		lt.add(emiOneTimeChargetitle);
		lt.add(emiOneTimeCharge_item_price);
		lt.add(emiOneTimeChargetotal_price);
		lt.add(emiRentalChargetype);
		lt.add(emiRentalChargetitle);
		lt.add(emiRentalCharge_item_price);
		lt.add(emiRentalChargetotal_price);
		lt.add(additionalmonthly_rentaltype);
		lt.add(additionalmonthly_rentaltitle);
		lt.add(additionalmonthly_rental_price);
		lt.add(additionalmonthly_total_price);
		lt.add(additionalmonthly_emiRentalChargetype);
		lt.add(additionalmonthly_emiRentalChargeTitle);
		lt.add(additionalmonthly_emiRentalCharge_price);
		lt.add(additionalmonthly_emiRentalCharge_total_price);
		lt.add(summaryamounttype);
		lt.add(summaryamounttitle);
		lt.add(summaryitemtitle);
		lt.add(summaryitemsubtitle);
		lt.add(summaryitemtoalPrice);
		lt.add(summaryitemtitleNew);
		lt.add(summaryitemsubtitleNew);
		lt.add(summaryitemtoalPriceNew);
		lt.add(summaryTotalPrice);
		lt.add(summarytotal_price_label);
		lt.add(mdrCC);
		lt.add(mdrCC);
		lt.add(mdrCCdisable);
		lt.add(mdrAMEX);
		lt.add(mdrAMEXdisable);
		lt.add(mdrDINERS);
		lt.add(mdrDINERSdisable);
		lt.add(mdrCorporationCard);
		lt.add(mdrCorporationCarddisable);
		lt.add(mdrDC);
		lt.add(mdrDCdisable);
		lt.add(mdrPREPAID);
		lt.add(mdrPREPAIDdisable);
       lt.add(deviceOnboardingLead);

       getLeadStatus getLeadStatus=new getLeadStatus(P.TESTDATA.get("SendOTPLead"));

       Map<String, String> queryParamNewtest = new HashMap<String, String>();
       Map<String, String> headersNewtest = new HashMap<String, String>();
		queryParamNewtest.put("fetchStrategy", "minimal");
		queryParamNewtest.put("leadId", deviceOnboardingLead);

		headersNewtest.put("Content-Type", "application/json");
		headersNewtest.put("session_token", sToken);
		headersNewtest.put("deviceidentifier", deviceIdentifer);
		headersNewtest.put("version", version);
		headersNewtest.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

       Response respObj = middlewareServicesObject.getLeadStatusPlanMethod(getLeadStatus, queryParamNewtest, headersNewtest);
       int httpcodeNew = respObj.getStatusCode();
//        Assert.assertTrue(httpcodeNew == 200, "Testcase Failed");

		return lt;
		}


	@Test(priority = 0, description = "TC_2_saveCartSessiontoken", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_2_saveCartSessiontoken() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
//		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", deviceOnboardingLead);
						headersnew.put("Content-Type", "application/json");
//						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();
//        Assert.assertTrue(httpcodenew == 401, "Testcase Failed");


	}


	@Test(priority = 0, description = "TC_3_saveCartWithoutDeviceIdentifer", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_3_saveCartWithoutDeviceIdentifer() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
//		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", deviceOnboardingLead);
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
//						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();
//		        int httpcodesnew = respObjnew.getStatusCode();


//	        Assert.assertTrue(httpcodenew == 410, "Testcase Failed");


	}





	@Test(priority = 0, description = "TC_4_cartSaveInvalidLeadID", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_4_cartSaveInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", "deviceOnboardingLead");
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();

//	        Assert.assertTrue(httpcodenew == 500, "Testcase Failed");

	}


	@Test(priority = 0, description = "TC_5_cartSaveWithoutLeadID", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_5_cartSaveWithoutLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
//		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

//						queryParamnew.put("leadId", "deviceOnboardingLead");
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();


//	        Assert.assertTrue(httpcodenew == 400, "Testcase Failed");

	}



	@Test(priority = 0, description = "TC_6_cartSaveWithoutPlanId", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_6_cartSaveWithoutPlanId() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
//		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", deviceOnboardingLead);
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();


//	        Assert.assertTrue(httpcodenew == 400, "Testcase Failed");
	        String actualMessage=summaryrespObj.jsonPath().get("displayMessage").toString();
	        boolean actual=actualMessage.contains("Cart is empty.");
	        boolean expected=expecteddisplayMessage.contains("Cart is empty.");
//	        Assert.assertEquals(actual, expected);

	        System.out.println(summaryrespObj.jsonPath().get("cartRefreshRequired").toString());


	}

	@Test(priority = 0, description = "TC_7_cartSaveWithInvalidAddOnsAMC", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_7_cartSaveWithInvalidAddOnsAMC() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
//		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", deviceOnboardingLead);
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();


//	        Assert.assertTrue(httpcodenew == 500, "Testcase Failed");


	}

	@Test(priority = 0, description = "TC_8_cartSaveWithInvalidAddOnsEMIRental", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_8_cartSaveWithInvalidAddOnsEMIRental() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
//		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", deviceOnboardingLead);
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();


//	        Assert.assertTrue(httpcodenew == 500, "Testcase Failed");


	}


	@Test(priority = 0, description = "TC_9_cartSaveWithoutQuanity", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_9_cartSaveWithoutQuanity() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
		Map<String, Object> queryParam = new HashMap<String, Object>();
		Map<String, Object> headers = new HashMap<String, Object>();
		queryParam.put("planId", PlanId);
		queryParam.put("addOns", "amc,emiRentalCharge");

		queryParam.put("leadId", deviceOnboardingLead);
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		int httpcode = respObjnew.getStatusCode();
//		Assert.assertTrue(httpcode == 200, "Testcase Failed");


		cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
		Map<String, Object> queryParamnew = new HashMap<String, Object>();
		Map<String, Object> headersnew = new HashMap<String, Object>();
		Map<String, Object> reqBody = new HashMap<String, Object>();

		queryParamnew.put("leadId", deviceOnboardingLead);
		headersnew.put("Content-Type", "application/json");
		headersnew.put("session_token", sToken);
		headersnew.put("deviceidentifier", deviceIdentifer);
		headersnew.put("version", version);
		headersnew.put("UncleScrooge", xmwChecksumBypassValue);

		reqBody.put("plan_id", "11608");
		reqBody.put("type", "amc");
		reqBody.put("quantity", "");
		reqBody.put("type1", "emiRentalCharge");

		reqBody.put("paymode", "CC");
		reqBody.put("disable", false);
		reqBody.put("factorType", "AMEX");
		reqBody.put("disable1", false);
		reqBody.put("factorType1", "DINERS");
		reqBody.put("disable2", false);
		reqBody.put("factorType2", "CorporationCard");
		reqBody.put("disable3", false);
		reqBody.put("paymode1", "DC");
		reqBody.put("disable4", false);
		reqBody.put("factorType3", "PrepaidCard");
		reqBody.put("disable5", false);
		reqBody.put("existingMdrSelected", false);


		Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

		int httpcodenew = summaryrespObj.getStatusCode();


//		Assert.assertTrue(httpcodenew == 500, "Testcase Failed");


	}


	@Test(priority = 0, description = "TC_10_cartSaveWithInvalidQuantity", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_10_cartSaveWithInvalidQuantity() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
		Map<String, Object> queryParam = new HashMap<String, Object>();
		Map<String, Object> headers = new HashMap<String, Object>();
		queryParam.put("planId", PlanId);
		queryParam.put("addOns", "amc,emiRentalCharge");

		queryParam.put("leadId", deviceOnboardingLead);
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		int httpcode = respObjnew.getStatusCode();
//		Assert.assertTrue(httpcode == 200, "Testcase Failed");


		cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
		Map<String, Object> queryParamnew = new HashMap<String, Object>();
		Map<String, Object> headersnew = new HashMap<String, Object>();
		Map<String, Object> reqBody = new HashMap<String, Object>();

		queryParamnew.put("leadId", deviceOnboardingLead);
		headersnew.put("Content-Type", "application/json");
		headersnew.put("session_token", sToken);
		headersnew.put("deviceidentifier", deviceIdentifer);
		headersnew.put("version", version);
		headersnew.put("UncleScrooge", xmwChecksumBypassValue);

		reqBody.put("plan_id", "11608");
		reqBody.put("type", "amc");
		reqBody.put("quantity", "a");
		reqBody.put("type1", "emiRentalCharge");

		reqBody.put("paymode", "CC");
		reqBody.put("disable", false);
		reqBody.put("factorType", "AMEX");
		reqBody.put("disable1", false);
		reqBody.put("factorType1", "DINERS");
		reqBody.put("disable2", false);
		reqBody.put("factorType2", "CorporationCard");
		reqBody.put("disable3", false);
		reqBody.put("paymode1", "DC");
		reqBody.put("disable4", false);
		reqBody.put("factorType3", "PrepaidCard");
		reqBody.put("disable5", false);
		reqBody.put("existingMdrSelected", false);


		Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

		int httpcodenew = summaryrespObj.getStatusCode();


//		Assert.assertTrue(httpcodenew == 400, "Testcase Failed");


	}


	@Test(priority = 0, description = "TC_11_saveCart_DisableAMEXMDR", groups = { "Regression" })
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public List<Object> TC_11_saveCart_DisableAMEXMDR(String mobilenumber) throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC(mobilenumber);
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
		Map<String, Object> queryParam = new HashMap<String, Object>();
		Map<String, Object> headers = new HashMap<String, Object>();
		queryParam.put("planId", PlanId);
		queryParam.put("addOns", "amc,emiRentalCharge");

		queryParam.put("leadId", deviceOnboardingLead);
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		int httpcode = respObjnew.getStatusCode();
//		Assert.assertTrue(httpcode == 200, "Testcase Failed");


		cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
		Map<String, Object> queryParamnew = new HashMap<String, Object>();
		Map<String, Object> headersnew = new HashMap<String, Object>();
		Map<String, Object> reqBody = new HashMap<String, Object>();

		queryParamnew.put("leadId", deviceOnboardingLead);
		headersnew.put("Content-Type", "application/json");
		headersnew.put("session_token", sToken);
		headersnew.put("deviceidentifier", deviceIdentifer);
		headersnew.put("version", version);
		headersnew.put("UncleScrooge", xmwChecksumBypassValue);

		reqBody.put("plan_id", "11608");
		reqBody.put("type", "amc");
		reqBody.put("quantity", 1);
		reqBody.put("type1", "emiRentalCharge");

		reqBody.put("paymode", "CC");
		reqBody.put("disable", false);
		reqBody.put("factorType", "AMEX");
		reqBody.put("disable1", true);
		reqBody.put("factorType1", "DINERS");
		reqBody.put("disable2", false);
		reqBody.put("factorType2", "CorporationCard");
		reqBody.put("disable3", false);
		reqBody.put("paymode1", "DC");
		reqBody.put("disable4", false);
		reqBody.put("factorType3", "PrepaidCard");
		reqBody.put("disable5", false);
		reqBody.put("existingMdrSelected", false);


		Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

		int httpcodenew = summaryrespObj.getStatusCode();
//		Assert.assertTrue(httpcodenew == 200, "Testcase Failed");

		// Validate Json Response
		String response = summaryrespObj.asString();
		System.out.println("Response is : " + response);
		JsonPath jsonPathEvaluator = summaryrespObj.jsonPath();
		Object plan_id = jsonPathEvaluator.get("cart_details.cart_items[0].plan_id");
		Object plan_name = jsonPathEvaluator.get("cart_details.cart_items[0].plan_name");
		Object device_id = jsonPathEvaluator.get("cart_details.cart_items[0].device_id");
		Object icon_url = jsonPathEvaluator.get("cart_details.cart_items[0].icon_url");
		Object addOnsApplicable = jsonPathEvaluator.get("cart_details.cart_items[0].addOnsApplicable");
		Object addOntype = jsonPathEvaluator.get("cart_details.cart_items[0].add_ons[0].type");
		Object addOntitle = jsonPathEvaluator.get("cart_details.cart_items[0].add_ons[0].title");
		JsonPath.config = new JsonPathConfig().numberReturnType(JsonPathConfig.NumberReturnType.BIG_DECIMAL);
		float peritemprice = jsonPathEvaluator.get("cart_details.cart_items[0].add_ons[0].per_item_price");
		float totalprice = jsonPathEvaluator.get("cart_details.cart_items[0].add_ons[0].total_price");
		Object quantity = jsonPathEvaluator.get("cart_details.cart_items[0].quantity");
		Object typeUpfrontCharge = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].type");
		Object titleUpfrontCharge = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].title");
		float peritempriceUpfrontCharge = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].per_item_price");
		float total_priceUpfrontCharge = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].total_price");
		Object amcType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[0].type");
		Object amcTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[0].title");
		float amcTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[0].total_price");
		float amcitemPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[0].per_item_price");
		Object deviceRentType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[1].type");
		Object deviceRentTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[1].title");
		float deviceRentTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[1].total_price");
		float deviceRentitemPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[1].per_item_price");
		Object installationchargeType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[2].type");
		Object installationchargeTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[2].title");
		float installationchargeTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[2].total_price");
		float installationchargeitemPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[2].per_item_price");
		Object usagedepositType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[3].type");
		Object usagedepositTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[3].title");
		float usagedepositTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[3].total_price");
		float usagedepositPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[3].per_item_price");
		Object typeMonthlyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].type");
		Object titleMonthlyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].title");
		float peritempriceMonthlyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].per_item_price");
		float total_priceMonthlyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].total_price");

		Object simChargeType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[0].type");
		Object simChargeTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[0].title");
		float simChargeTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[0].total_price");
		float simChargePrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[0].per_item_price");

		Object deviceRentalType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[1].type");
		Object deviceRentalTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[1].title");
		float deviceRentalTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[1].total_price");
		float deviceRentalPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[1].per_item_price");
		Object subTitleMonthlyNextduedate = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].subtitle");
		Object typeAnnualyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].type");
		Object titleAnnualyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].title");
		float peritempriceAnnualyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].per_item_price");
		float total_priceAnnualyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].total_price");
		Object amcannualyType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].items[0].type");
		Object amcannualyTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].items[0].title");
		float amcannualyTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].items[0].total_price");
		float amcannualyPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].items[0].per_item_price");
		Object subTitleAnnualyNextduedate = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].subtitle");
		Object cartaddOnType = jsonPathEvaluator.get("cart_details.additional_items[0].type");
		Object cartaddOnTitle = jsonPathEvaluator.get("cart_details.additional_items[0].title");
		Object cartaddOnicon_url = jsonPathEvaluator.get("cart_details.additional_items[0].icon_url");
		Object cartaddOnAddOnsType = jsonPathEvaluator.get("cart_details.additional_items[0].add_ons[0].type");
		Object cartaddOnAddOnsTitle = jsonPathEvaluator.get("cart_details.additional_items[0].add_ons[0].title");
		Object cartaddOnAddOnsUpfronttype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].type");
		Object cartaddOnAddOnsUpfronttitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].title");
		float cartaddOnAddOnsUpfrontper_item_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].per_item_price");
		float cartaddOnAddOnsUpfronttotal_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].total_price");

		Object emiOneTimeChargetype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[0].type");
		Object emiOneTimeChargetitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[0].title");
		float emiOneTimeCharge_item_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[0].per_item_price");
		float emiOneTimeChargetotal_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[0].total_price");
		Object emiRentalChargetype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[1].type");
		Object emiRentalChargetitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[1].title");
		float emiRentalCharge_item_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[1].per_item_price");
		float emiRentalChargetotal_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[1].total_price");

		Object additionalmonthly_rentaltype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].type");
		Object additionalmonthly_rentaltitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].title");
		float additionalmonthly_rental_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].per_item_price");
		float additionalmonthly_total_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].total_price");
		Object additionalmonthly_emiRentalChargetype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].items[0].type");
		Object additionalmonthly_emiRentalChargeTitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].items[0].title");
		float additionalmonthly_emiRentalCharge_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].items[0].per_item_price");
		float additionalmonthly_emiRentalCharge_total_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].items[0].total_price");
		Object additionalmonthly_subtitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].subtitle");

		Object summaryamounttype = jsonPathEvaluator.get("cart_details.summary.type");
		Object summaryamounttitle = jsonPathEvaluator.get("cart_details.summary.title");
		Object summaryitemtitle = jsonPathEvaluator.get("cart_details.summary.items[0].title");
		Object summaryitemsubtitle = jsonPathEvaluator.get("cart_details.summary.items[0].subTitle");
		float summaryitemtoalPrice = jsonPathEvaluator.get("cart_details.summary.items[0].total_price");
		Object summaryitemtitleNew = jsonPathEvaluator.get("cart_details.summary.items[1].title");
		Object summaryitemsubtitleNew = jsonPathEvaluator.get("cart_details.summary.items[1].subTitle");
		float summaryitemtoalPriceNew = jsonPathEvaluator.get("cart_details.summary.items[1].total_price");
		float summaryTotalPrice = jsonPathEvaluator.get("cart_details.summary.total_price");
		Object summarytotal_price_label = jsonPathEvaluator.get("cart_details.summary.total_price_label");

		Object mdrCC = jsonPathEvaluator.get("cart_details.mdr[0].paymode");
		Boolean mdrCCdisable = jsonPathEvaluator.get("cart_details.mdr[0].disable");
		Object mdrAMEX = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[0].factorType");
		Boolean mdrAMEXdisable = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[0].disable");
		Object mdrDINERS = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[1].factorType");
		Boolean mdrDINERSdisable = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[1].disable");
		Object mdrCorporationCard = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[2].factorType");
		Boolean mdrCorporationCarddisable = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[2].disable");
		Object mdrDC = jsonPathEvaluator.get("cart_details.mdr[1].paymode");
		Boolean mdrDCdisable = jsonPathEvaluator.get("cart_details.mdr[1].disable");
		Object mdrPREPAID = jsonPathEvaluator.get("cart_details.mdr[1].feeRateFactor[0].factorType");
		Boolean mdrPREPAIDdisable = jsonPathEvaluator.get("cart_details.mdr[1].feeRateFactor[0].disable");





		System.out.println("mdrCC : "+mdrCC);
		Object expectedmdrCC="CC";
//		Assert.assertEquals(mdrCC,expectedmdrCC);
		System.out.println("mdrCCdisable : "+mdrCCdisable);
		Boolean expectedmdrCCdisable = false;
//		Assert.assertEquals(mdrCCdisable,expectedmdrCCdisable);
		System.out.println("mdrAMEX : "+mdrAMEX);
		Object expectedmdrAMEX="AMEX";
//		Assert.assertEquals(mdrAMEX,expectedmdrAMEX);
		System.out.println("mdrAMEXdisable : "+mdrAMEXdisable);
		Boolean expectedmdrAMEXdisable = true;
//		Assert.assertEquals(mdrAMEXdisable,expectedmdrAMEXdisable);
		System.out.println("mdrDINERS : "+mdrDINERS);
		Object expectedmdrDINERS="DINERS";
//		Assert.assertEquals(mdrDINERS,expectedmdrDINERS);
		System.out.println("mdrDINERSdisable : "+mdrDINERSdisable);
		Boolean expectedmdrDINERSdisable = false;
//		Assert.assertEquals(mdrDINERSdisable,expectedmdrDINERSdisable);
		System.out.println("mdrCorporationCard : "+mdrCorporationCard);
		Object expectedmdrCorporationCard="CorporationCard";
//		Assert.assertEquals(mdrCorporationCard,expectedmdrCorporationCard);
		System.out.println("mdrCorporationCarddisable : "+mdrCorporationCarddisable);
		Boolean expectedmdrCorporationCarddisable = false;
//		Assert.assertEquals(mdrCorporationCarddisable,expectedmdrCorporationCarddisable);
		System.out.println("mdrDC : "+mdrDC);
		Object expectedmdrDC="DC";
//		Assert.assertEquals(mdrDC,expectedmdrDC);
		System.out.println("mdrDCdisable : "+mdrDCdisable);
		Boolean expectedmdrDCdisable = false;
//		Assert.assertEquals(mdrDCdisable,expectedmdrDCdisable);
		System.out.println("mdrPREPAID : "+mdrPREPAID);
		Object expectedmdrPREPAID = "PrepaidCard";
//		Assert.assertEquals(mdrPREPAID,expectedmdrPREPAID);
		System.out.println("mdrPREPAIDdisable : "+mdrPREPAIDdisable);
		Boolean expectedmdrPREPAIDdisable = false;
//		Assert.assertEquals(mdrPREPAIDdisable,expectedmdrPREPAIDdisable);

		List<Object> lt=new ArrayList<Object>();
		lt.add(plan_id);
		lt.add(plan_name);
		lt.add(device_id);
		lt.add(icon_url);
		lt.add(addOntype);
		lt.add(addOntitle);
		lt.add(peritemprice);
		lt.add(totalprice);
		lt.add(quantity);
		lt.add(typeUpfrontCharge);
		lt.add(titleUpfrontCharge);
		lt.add(peritempriceUpfrontCharge);
		lt.add(total_priceUpfrontCharge);
		lt.add(amcType);
		lt.add(amcTitle);
		lt.add(amcTotal);
		lt.add(amcitemPrice);
		lt.add(deviceRentType);
		lt.add(deviceRentTitle);
		lt.add(deviceRentTotal);
		lt.add(deviceRentitemPrice);
		lt.add(installationchargeType);
		lt.add(installationchargeTitle);
		lt.add(installationchargeTotal);
		lt.add(installationchargeitemPrice);
		lt.add(usagedepositType);
		lt.add(usagedepositTitle);
		lt.add(usagedepositTotal);
		lt.add(usagedepositPrice);
		lt.add(typeMonthlyRental);
		lt.add(titleMonthlyRental);
		lt.add(peritempriceMonthlyRental);
		lt.add(total_priceMonthlyRental);
		lt.add(simChargeType);
		lt.add(simChargeTitle);
		lt.add(simChargePrice);
		lt.add(deviceRentalType);
		lt.add(deviceRentalTitle);
		lt.add(deviceRentalTotal);
		lt.add(deviceRentalPrice);
		lt.add(subTitleMonthlyNextduedate);
		lt.add(typeAnnualyRental);
		lt.add(titleAnnualyRental);
		lt.add(peritempriceAnnualyRental);
		lt.add(total_priceAnnualyRental);
		lt.add(amcannualyType);
		lt.add(amcannualyTitle);
		lt.add(amcannualyTotal);
		lt.add(amcannualyPrice);
		lt.add(subTitleAnnualyNextduedate);
		lt.add(cartaddOnType);
		lt.add(cartaddOnTitle);
		lt.add(cartaddOnicon_url);
		lt.add(cartaddOnAddOnsType);
		lt.add(cartaddOnAddOnsTitle);
		lt.add(cartaddOnAddOnsUpfronttype);
		lt.add(cartaddOnAddOnsUpfronttitle);
		lt.add(cartaddOnAddOnsUpfrontper_item_price);
		lt.add(cartaddOnAddOnsUpfronttotal_price);
		lt.add(emiOneTimeChargetype);
		lt.add(emiOneTimeChargetitle);
		lt.add(emiOneTimeCharge_item_price);
		lt.add(emiOneTimeChargetotal_price);
		lt.add(emiRentalChargetype);
		lt.add(emiRentalChargetitle);
		lt.add(emiRentalCharge_item_price);
		lt.add(emiRentalChargetotal_price);
		lt.add(additionalmonthly_rentaltype);
		lt.add(additionalmonthly_rentaltitle);
		lt.add(additionalmonthly_rental_price);
		lt.add(additionalmonthly_total_price);
		lt.add(additionalmonthly_emiRentalChargetype);
		lt.add(additionalmonthly_emiRentalChargeTitle);
		lt.add(additionalmonthly_emiRentalCharge_price);
		lt.add(additionalmonthly_emiRentalCharge_total_price);
		lt.add(summaryamounttype);
		lt.add(summaryamounttitle);
		lt.add(summaryitemtitle);
		lt.add(summaryitemsubtitle);
		lt.add(summaryitemtoalPrice);
		lt.add(summaryitemtitleNew);
		lt.add(summaryitemsubtitleNew);
		lt.add(summaryitemtoalPriceNew);
		lt.add(summaryTotalPrice);
		lt.add(summarytotal_price_label);
		lt.add(mdrCC);
		lt.add(mdrCC);
		lt.add(mdrCCdisable);
		lt.add(mdrAMEX);
		lt.add(mdrAMEXdisable);
		lt.add(mdrDINERS);
		lt.add(mdrDINERSdisable);
		lt.add(mdrCorporationCard);
		lt.add(mdrCorporationCarddisable);
		lt.add(mdrDC);
		lt.add(mdrDCdisable);
		lt.add(mdrPREPAID);
		lt.add(mdrPREPAIDdisable);
		return lt;
	}

	@Test(priority = 0, description = "TC_12_saveCart_DisableAMEXDINERSMDR", groups = { "Regression" })
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public List<Object> TC_12_saveCart_DisableAMEXDINERSMDR(String mobilenumber) throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC(mobilenumber);
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
		Map<String, Object> queryParam = new HashMap<String, Object>();
		Map<String, Object> headers = new HashMap<String, Object>();
		queryParam.put("planId", PlanId);
		queryParam.put("addOns", "amc,emiRentalCharge");

		queryParam.put("leadId", deviceOnboardingLead);
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		int httpcode = respObjnew.getStatusCode();
//		Assert.assertTrue(httpcode == 200, "Testcase Failed");


		cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
		Map<String, Object> queryParamnew = new HashMap<String, Object>();
		Map<String, Object> headersnew = new HashMap<String, Object>();
		Map<String, Object> reqBody = new HashMap<String, Object>();

		queryParamnew.put("leadId", deviceOnboardingLead);
		headersnew.put("Content-Type", "application/json");
		headersnew.put("session_token", sToken);
		headersnew.put("deviceidentifier", deviceIdentifer);
		headersnew.put("version", version);
		headersnew.put("UncleScrooge", xmwChecksumBypassValue);

		reqBody.put("plan_id", "11608");
		reqBody.put("type", "amc");
		reqBody.put("quantity", 1);
		reqBody.put("type1", "emiRentalCharge");

		reqBody.put("paymode", "CC");
		reqBody.put("disable", false);
		reqBody.put("factorType", "AMEX");
		reqBody.put("disable1", true);
		reqBody.put("factorType1", "DINERS");
		reqBody.put("disable2", true);
		reqBody.put("factorType2", "CorporationCard");
		reqBody.put("disable3", false);
		reqBody.put("paymode1", "DC");
		reqBody.put("disable4", false);
		reqBody.put("factorType3", "PrepaidCard");
		reqBody.put("disable5", false);
		reqBody.put("existingMdrSelected", false);


		Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

		int httpcodenew = summaryrespObj.getStatusCode();
//		Assert.assertTrue(httpcodenew == 200, "Testcase Failed");

		// Validate Json Response
		String response = summaryrespObj.asString();
		System.out.println("Response is : " + response);
		JsonPath jsonPathEvaluator = summaryrespObj.jsonPath();
		Object plan_id = jsonPathEvaluator.get("cart_details.cart_items[0].plan_id");
		Object plan_name = jsonPathEvaluator.get("cart_details.cart_items[0].plan_name");
		Object device_id = jsonPathEvaluator.get("cart_details.cart_items[0].device_id");
		Object icon_url = jsonPathEvaluator.get("cart_details.cart_items[0].icon_url");
		Object addOnsApplicable = jsonPathEvaluator.get("cart_details.cart_items[0].addOnsApplicable");
		Object addOntype = jsonPathEvaluator.get("cart_details.cart_items[0].add_ons[0].type");
		Object addOntitle = jsonPathEvaluator.get("cart_details.cart_items[0].add_ons[0].title");
		JsonPath.config = new JsonPathConfig().numberReturnType(JsonPathConfig.NumberReturnType.BIG_DECIMAL);
		float peritemprice = jsonPathEvaluator.get("cart_details.cart_items[0].add_ons[0].per_item_price");
		float totalprice = jsonPathEvaluator.get("cart_details.cart_items[0].add_ons[0].total_price");
		Object quantity = jsonPathEvaluator.get("cart_details.cart_items[0].quantity");
		Object typeUpfrontCharge = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].type");
		Object titleUpfrontCharge = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].title");
		float peritempriceUpfrontCharge = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].per_item_price");
		float total_priceUpfrontCharge = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].total_price");
		Object amcType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[0].type");
		Object amcTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[0].title");
		float amcTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[0].total_price");
		float amcitemPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[0].per_item_price");
		Object deviceRentType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[1].type");
		Object deviceRentTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[1].title");
		float deviceRentTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[1].total_price");
		float deviceRentitemPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[1].per_item_price");
		Object installationchargeType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[2].type");
		Object installationchargeTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[2].title");
		float installationchargeTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[2].total_price");
		float installationchargeitemPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[2].per_item_price");
		Object usagedepositType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[3].type");
		Object usagedepositTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[3].title");
		float usagedepositTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[3].total_price");
		float usagedepositPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[3].per_item_price");
		Object typeMonthlyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].type");
		Object titleMonthlyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].title");
		float peritempriceMonthlyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].per_item_price");
		float total_priceMonthlyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].total_price");

		Object simChargeType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[0].type");
		Object simChargeTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[0].title");
		float simChargeTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[0].total_price");
		float simChargePrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[0].per_item_price");

		Object deviceRentalType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[1].type");
		Object deviceRentalTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[1].title");
		float deviceRentalTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[1].total_price");
		float deviceRentalPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[1].per_item_price");
		Object subTitleMonthlyNextduedate = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].subtitle");
		Object typeAnnualyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].type");
		Object titleAnnualyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].title");
		float peritempriceAnnualyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].per_item_price");
		float total_priceAnnualyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].total_price");
		Object amcannualyType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].items[0].type");
		Object amcannualyTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].items[0].title");
		float amcannualyTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].items[0].total_price");
		float amcannualyPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].items[0].per_item_price");
		Object subTitleAnnualyNextduedate = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].subtitle");
		Object cartaddOnType = jsonPathEvaluator.get("cart_details.additional_items[0].type");
		Object cartaddOnTitle = jsonPathEvaluator.get("cart_details.additional_items[0].title");
		Object cartaddOnicon_url = jsonPathEvaluator.get("cart_details.additional_items[0].icon_url");
		Object cartaddOnAddOnsType = jsonPathEvaluator.get("cart_details.additional_items[0].add_ons[0].type");
		Object cartaddOnAddOnsTitle = jsonPathEvaluator.get("cart_details.additional_items[0].add_ons[0].title");
		Object cartaddOnAddOnsUpfronttype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].type");
		Object cartaddOnAddOnsUpfronttitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].title");
		float cartaddOnAddOnsUpfrontper_item_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].per_item_price");
		float cartaddOnAddOnsUpfronttotal_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].total_price");

		Object emiOneTimeChargetype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[0].type");
		Object emiOneTimeChargetitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[0].title");
		float emiOneTimeCharge_item_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[0].per_item_price");
		float emiOneTimeChargetotal_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[0].total_price");
		Object emiRentalChargetype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[1].type");
		Object emiRentalChargetitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[1].title");
		float emiRentalCharge_item_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[1].per_item_price");
		float emiRentalChargetotal_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[1].total_price");

		Object additionalmonthly_rentaltype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].type");
		Object additionalmonthly_rentaltitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].title");
		float additionalmonthly_rental_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].per_item_price");
		float additionalmonthly_total_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].total_price");
		Object additionalmonthly_emiRentalChargetype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].items[0].type");
		Object additionalmonthly_emiRentalChargeTitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].items[0].title");
		float additionalmonthly_emiRentalCharge_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].items[0].per_item_price");
		float additionalmonthly_emiRentalCharge_total_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].items[0].total_price");
		Object additionalmonthly_subtitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].subtitle");

		Object summaryamounttype = jsonPathEvaluator.get("cart_details.summary.type");
		Object summaryamounttitle = jsonPathEvaluator.get("cart_details.summary.title");
		Object summaryitemtitle = jsonPathEvaluator.get("cart_details.summary.items[0].title");
		Object summaryitemsubtitle = jsonPathEvaluator.get("cart_details.summary.items[0].subTitle");
		float summaryitemtoalPrice = jsonPathEvaluator.get("cart_details.summary.items[0].total_price");
		Object summaryitemtitleNew = jsonPathEvaluator.get("cart_details.summary.items[1].title");
		Object summaryitemsubtitleNew = jsonPathEvaluator.get("cart_details.summary.items[1].subTitle");
		float summaryitemtoalPriceNew = jsonPathEvaluator.get("cart_details.summary.items[1].total_price");
		float summaryTotalPrice = jsonPathEvaluator.get("cart_details.summary.total_price");
		Object summarytotal_price_label = jsonPathEvaluator.get("cart_details.summary.total_price_label");

		Object mdrCC = jsonPathEvaluator.get("cart_details.mdr[0].paymode");
		Boolean mdrCCdisable = jsonPathEvaluator.get("cart_details.mdr[0].disable");
		Object mdrAMEX = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[0].factorType");
		Boolean mdrAMEXdisable = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[0].disable");
		Object mdrDINERS = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[1].factorType");
		Boolean mdrDINERSdisable = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[1].disable");
		Object mdrCorporationCard = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[2].factorType");
		Boolean mdrCorporationCarddisable = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[2].disable");
		Object mdrDC = jsonPathEvaluator.get("cart_details.mdr[1].paymode");
		Boolean mdrDCdisable = jsonPathEvaluator.get("cart_details.mdr[1].disable");
		Object mdrPREPAID = jsonPathEvaluator.get("cart_details.mdr[1].feeRateFactor[0].factorType");
		Boolean mdrPREPAIDdisable = jsonPathEvaluator.get("cart_details.mdr[1].feeRateFactor[0].disable");





		System.out.println("mdrCC : "+mdrCC);
		Object expectedmdrCC="CC";
//		Assert.assertEquals(mdrCC,expectedmdrCC);
		System.out.println("mdrCCdisable : "+mdrCCdisable);
		Boolean expectedmdrCCdisable = false;
//		Assert.assertEquals(mdrCCdisable,expectedmdrCCdisable);
		System.out.println("mdrAMEX : "+mdrAMEX);
		Object expectedmdrAMEX="AMEX";
//		Assert.assertEquals(mdrAMEX,expectedmdrAMEX);
		System.out.println("mdrAMEXdisable : "+mdrAMEXdisable);
		Boolean expectedmdrAMEXdisable = true;
//		Assert.assertEquals(mdrAMEXdisable,expectedmdrAMEXdisable);
		System.out.println("mdrDINERS : "+mdrDINERS);
		Object expectedmdrDINERS="DINERS";
//		Assert.assertEquals(mdrDINERS,expectedmdrDINERS);
		System.out.println("mdrDINERSdisable : "+mdrDINERSdisable);
		Boolean expectedmdrDINERSdisable = false;
//		Assert.assertEquals(mdrDINERSdisable,expectedmdrDINERSdisable);
		System.out.println("mdrCorporationCard : "+mdrCorporationCard);
		Object expectedmdrCorporationCard="CorporationCard";
//		Assert.assertEquals(mdrCorporationCard,expectedmdrCorporationCard);
		System.out.println("mdrCorporationCarddisable : "+mdrCorporationCarddisable);
		Boolean expectedmdrCorporationCarddisable = false;
//		Assert.assertEquals(mdrCorporationCarddisable,expectedmdrCorporationCarddisable);
		System.out.println("mdrDC : "+mdrDC);
		Object expectedmdrDC="DC";
//		Assert.assertEquals(mdrDC,expectedmdrDC);
		System.out.println("mdrDCdisable : "+mdrDCdisable);
		Boolean expectedmdrDCdisable = false;
//		Assert.assertEquals(mdrDCdisable,expectedmdrDCdisable);
		System.out.println("mdrPREPAID : "+mdrPREPAID);
		Object expectedmdrPREPAID = "PrepaidCard";
//		Assert.assertEquals(mdrPREPAID,expectedmdrPREPAID);
		System.out.println("mdrPREPAIDdisable : "+mdrPREPAIDdisable);
		Boolean expectedmdrPREPAIDdisable = false;
//		Assert.assertEquals(mdrPREPAIDdisable,expectedmdrPREPAIDdisable);

		List<Object> lt=new ArrayList<Object>();
		lt.add(plan_id);
		lt.add(plan_name);
		lt.add(device_id);
		lt.add(icon_url);
		lt.add(addOntype);
		lt.add(addOntitle);
		lt.add(peritemprice);
		lt.add(totalprice);
		lt.add(quantity);
		lt.add(typeUpfrontCharge);
		lt.add(titleUpfrontCharge);
		lt.add(peritempriceUpfrontCharge);
		lt.add(total_priceUpfrontCharge);
		lt.add(amcType);
		lt.add(amcTitle);
		lt.add(amcTotal);
		lt.add(amcitemPrice);
		lt.add(deviceRentType);
		lt.add(deviceRentTitle);
		lt.add(deviceRentTotal);
		lt.add(deviceRentitemPrice);
		lt.add(installationchargeType);
		lt.add(installationchargeTitle);
		lt.add(installationchargeTotal);
		lt.add(installationchargeitemPrice);
		lt.add(usagedepositType);
		lt.add(usagedepositTitle);
		lt.add(usagedepositTotal);
		lt.add(usagedepositPrice);
		lt.add(typeMonthlyRental);
		lt.add(titleMonthlyRental);
		lt.add(peritempriceMonthlyRental);
		lt.add(total_priceMonthlyRental);
		lt.add(simChargeType);
		lt.add(simChargeTitle);
		lt.add(simChargePrice);
		lt.add(deviceRentalType);
		lt.add(deviceRentalTitle);
		lt.add(deviceRentalTotal);
		lt.add(deviceRentalPrice);
		lt.add(subTitleMonthlyNextduedate);
		lt.add(typeAnnualyRental);
		lt.add(titleAnnualyRental);
		lt.add(peritempriceAnnualyRental);
		lt.add(total_priceAnnualyRental);
		lt.add(amcannualyType);
		lt.add(amcannualyTitle);
		lt.add(amcannualyTotal);
		lt.add(amcannualyPrice);
		lt.add(subTitleAnnualyNextduedate);
		lt.add(cartaddOnType);
		lt.add(cartaddOnTitle);
		lt.add(cartaddOnicon_url);
		lt.add(cartaddOnAddOnsType);
		lt.add(cartaddOnAddOnsTitle);
		lt.add(cartaddOnAddOnsUpfronttype);
		lt.add(cartaddOnAddOnsUpfronttitle);
		lt.add(cartaddOnAddOnsUpfrontper_item_price);
		lt.add(cartaddOnAddOnsUpfronttotal_price);
		lt.add(emiOneTimeChargetype);
		lt.add(emiOneTimeChargetitle);
		lt.add(emiOneTimeCharge_item_price);
		lt.add(emiOneTimeChargetotal_price);
		lt.add(emiRentalChargetype);
		lt.add(emiRentalChargetitle);
		lt.add(emiRentalCharge_item_price);
		lt.add(emiRentalChargetotal_price);
		lt.add(additionalmonthly_rentaltype);
		lt.add(additionalmonthly_rentaltitle);
		lt.add(additionalmonthly_rental_price);
		lt.add(additionalmonthly_total_price);
		lt.add(additionalmonthly_emiRentalChargetype);
		lt.add(additionalmonthly_emiRentalChargeTitle);
		lt.add(additionalmonthly_emiRentalCharge_price);
		lt.add(additionalmonthly_emiRentalCharge_total_price);
		lt.add(summaryamounttype);
		lt.add(summaryamounttitle);
		lt.add(summaryitemtitle);
		lt.add(summaryitemsubtitle);
		lt.add(summaryitemtoalPrice);
		lt.add(summaryitemtitleNew);
		lt.add(summaryitemsubtitleNew);
		lt.add(summaryitemtoalPriceNew);
		lt.add(summaryTotalPrice);
		lt.add(summarytotal_price_label);
		lt.add(mdrCC);
		lt.add(mdrCC);
		lt.add(mdrCCdisable);
		lt.add(mdrAMEX);
		lt.add(mdrAMEXdisable);
		lt.add(mdrDINERS);
		lt.add(mdrDINERSdisable);
		lt.add(mdrCorporationCard);
		lt.add(mdrCorporationCarddisable);
		lt.add(mdrDC);
		lt.add(mdrDCdisable);
		lt.add(mdrPREPAID);
		lt.add(mdrPREPAIDdisable);
		return lt;
	}


	@Test(priority = 0, description = "TC_14_saveCart_DisablePREPAIDMDR", groups = { "Regression" })
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public List<Object> TC_14_saveCart_DisablePREPAIDMDR(String mobilenumber) throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC(mobilenumber);
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
		Map<String, Object> queryParam = new HashMap<String, Object>();
		Map<String, Object> headers = new HashMap<String, Object>();
		queryParam.put("planId", PlanId);
		queryParam.put("addOns", "amc,emiRentalCharge");

		queryParam.put("leadId", deviceOnboardingLead);
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		int httpcode = respObjnew.getStatusCode();
//		Assert.assertTrue(httpcode == 200, "Testcase Failed");


		cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
		Map<String, Object> queryParamnew = new HashMap<String, Object>();
		Map<String, Object> headersnew = new HashMap<String, Object>();
		Map<String, Object> reqBody = new HashMap<String, Object>();

		queryParamnew.put("leadId", deviceOnboardingLead);
		headersnew.put("Content-Type", "application/json");
		headersnew.put("session_token", sToken);
		headersnew.put("deviceidentifier", deviceIdentifer);
		headersnew.put("version", version);
		headersnew.put("UncleScrooge", xmwChecksumBypassValue);

		reqBody.put("plan_id", "11608");
		reqBody.put("type", "amc");
		reqBody.put("quantity", 1);
		reqBody.put("type1", "emiRentalCharge");

		reqBody.put("paymode", "CC");
		reqBody.put("disable", false);
		reqBody.put("factorType", "AMEX");
		reqBody.put("disable1", false);
		reqBody.put("factorType1", "DINERS");
		reqBody.put("disable2", false);
		reqBody.put("factorType2", "CorporationCard");
		reqBody.put("disable3", false);
		reqBody.put("paymode1", "DC");
		reqBody.put("disable4", false);
		reqBody.put("factorType3", "PrepaidCard");
		reqBody.put("disable5", true);
		reqBody.put("existingMdrSelected", false);


		Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

		int httpcodenew = summaryrespObj.getStatusCode();
//		Assert.assertTrue(httpcodenew == 200, "Testcase Failed");

		// Validate Json Response
		String response = summaryrespObj.asString();
		System.out.println("Response is : " + response);
		JsonPath jsonPathEvaluator = summaryrespObj.jsonPath();
		Object plan_id = jsonPathEvaluator.get("cart_details.cart_items[0].plan_id");
		Object plan_name = jsonPathEvaluator.get("cart_details.cart_items[0].plan_name");
		Object device_id = jsonPathEvaluator.get("cart_details.cart_items[0].device_id");
		Object icon_url = jsonPathEvaluator.get("cart_details.cart_items[0].icon_url");
		Object addOnsApplicable = jsonPathEvaluator.get("cart_details.cart_items[0].addOnsApplicable");
		Object addOntype = jsonPathEvaluator.get("cart_details.cart_items[0].add_ons[0].type");
		Object addOntitle = jsonPathEvaluator.get("cart_details.cart_items[0].add_ons[0].title");
		JsonPath.config = new JsonPathConfig().numberReturnType(JsonPathConfig.NumberReturnType.BIG_DECIMAL);
		float peritemprice = jsonPathEvaluator.get("cart_details.cart_items[0].add_ons[0].per_item_price");
		float totalprice = jsonPathEvaluator.get("cart_details.cart_items[0].add_ons[0].total_price");
		Object quantity = jsonPathEvaluator.get("cart_details.cart_items[0].quantity");
		Object typeUpfrontCharge = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].type");
		Object titleUpfrontCharge = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].title");
		float peritempriceUpfrontCharge = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].per_item_price");
		float total_priceUpfrontCharge = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].total_price");
		Object amcType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[0].type");
		Object amcTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[0].title");
		float amcTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[0].total_price");
		float amcitemPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[0].per_item_price");
		Object deviceRentType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[1].type");
		Object deviceRentTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[1].title");
		float deviceRentTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[1].total_price");
		float deviceRentitemPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[1].per_item_price");
		Object installationchargeType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[2].type");
		Object installationchargeTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[2].title");
		float installationchargeTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[2].total_price");
		float installationchargeitemPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[2].per_item_price");
		Object usagedepositType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[3].type");
		Object usagedepositTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[3].title");
		float usagedepositTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[3].total_price");
		float usagedepositPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[0].items[3].per_item_price");
		Object typeMonthlyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].type");
		Object titleMonthlyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].title");
		float peritempriceMonthlyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].per_item_price");
		float total_priceMonthlyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].total_price");

		Object simChargeType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[0].type");
		Object simChargeTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[0].title");
		float simChargeTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[0].total_price");
		float simChargePrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[0].per_item_price");

		Object deviceRentalType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[1].type");
		Object deviceRentalTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[1].title");
		float deviceRentalTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[1].total_price");
		float deviceRentalPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].items[1].per_item_price");
		Object subTitleMonthlyNextduedate = jsonPathEvaluator.get("cart_details.cart_items[0].sections[1].subtitle");
		Object typeAnnualyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].type");
		Object titleAnnualyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].title");
		float peritempriceAnnualyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].per_item_price");
		float total_priceAnnualyRental = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].total_price");
		Object amcannualyType = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].items[0].type");
		Object amcannualyTitle = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].items[0].title");
		float amcannualyTotal = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].items[0].total_price");
		float amcannualyPrice = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].items[0].per_item_price");
		Object subTitleAnnualyNextduedate = jsonPathEvaluator.get("cart_details.cart_items[0].sections[2].subtitle");
		Object cartaddOnType = jsonPathEvaluator.get("cart_details.additional_items[0].type");
		Object cartaddOnTitle = jsonPathEvaluator.get("cart_details.additional_items[0].title");
		Object cartaddOnicon_url = jsonPathEvaluator.get("cart_details.additional_items[0].icon_url");
		Object cartaddOnAddOnsType = jsonPathEvaluator.get("cart_details.additional_items[0].add_ons[0].type");
		Object cartaddOnAddOnsTitle = jsonPathEvaluator.get("cart_details.additional_items[0].add_ons[0].title");
		Object cartaddOnAddOnsUpfronttype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].type");
		Object cartaddOnAddOnsUpfronttitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].title");
		float cartaddOnAddOnsUpfrontper_item_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].per_item_price");
		float cartaddOnAddOnsUpfronttotal_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].total_price");

		Object emiOneTimeChargetype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[0].type");
		Object emiOneTimeChargetitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[0].title");
		float emiOneTimeCharge_item_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[0].per_item_price");
		float emiOneTimeChargetotal_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[0].total_price");
		Object emiRentalChargetype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[1].type");
		Object emiRentalChargetitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[1].title");
		float emiRentalCharge_item_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[1].per_item_price");
		float emiRentalChargetotal_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[0].items[1].total_price");

		Object additionalmonthly_rentaltype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].type");
		Object additionalmonthly_rentaltitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].title");
		float additionalmonthly_rental_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].per_item_price");
		float additionalmonthly_total_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].total_price");
		Object additionalmonthly_emiRentalChargetype = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].items[0].type");
		Object additionalmonthly_emiRentalChargeTitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].items[0].title");
		float additionalmonthly_emiRentalCharge_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].items[0].per_item_price");
		float additionalmonthly_emiRentalCharge_total_price = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].items[0].total_price");
		Object additionalmonthly_subtitle = jsonPathEvaluator.get("cart_details.additional_items[0].sections[1].subtitle");

		Object summaryamounttype = jsonPathEvaluator.get("cart_details.summary.type");
		Object summaryamounttitle = jsonPathEvaluator.get("cart_details.summary.title");
		Object summaryitemtitle = jsonPathEvaluator.get("cart_details.summary.items[0].title");
		Object summaryitemsubtitle = jsonPathEvaluator.get("cart_details.summary.items[0].subTitle");
		float summaryitemtoalPrice = jsonPathEvaluator.get("cart_details.summary.items[0].total_price");
		Object summaryitemtitleNew = jsonPathEvaluator.get("cart_details.summary.items[1].title");
		Object summaryitemsubtitleNew = jsonPathEvaluator.get("cart_details.summary.items[1].subTitle");
		float summaryitemtoalPriceNew = jsonPathEvaluator.get("cart_details.summary.items[1].total_price");
		float summaryTotalPrice = jsonPathEvaluator.get("cart_details.summary.total_price");
		Object summarytotal_price_label = jsonPathEvaluator.get("cart_details.summary.total_price_label");

		Object mdrCC = jsonPathEvaluator.get("cart_details.mdr[0].paymode");
		Boolean mdrCCdisable = jsonPathEvaluator.get("cart_details.mdr[0].disable");
		Object mdrAMEX = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[0].factorType");
		Boolean mdrAMEXdisable = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[0].disable");
		Object mdrDINERS = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[1].factorType");
		Boolean mdrDINERSdisable = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[1].disable");
		Object mdrCorporationCard = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[2].factorType");
		Boolean mdrCorporationCarddisable = jsonPathEvaluator.get("cart_details.mdr[0].feeRateFactor[2].disable");
		Object mdrDC = jsonPathEvaluator.get("cart_details.mdr[1].paymode");
		Boolean mdrDCdisable = jsonPathEvaluator.get("cart_details.mdr[1].disable");
		Object mdrPREPAID = jsonPathEvaluator.get("cart_details.mdr[1].feeRateFactor[0].factorType");
		Boolean mdrPREPAIDdisable = jsonPathEvaluator.get("cart_details.mdr[1].feeRateFactor[0].disable");





		System.out.println("mdrCC : "+mdrCC);
		Object expectedmdrCC="CC";
//		Assert.assertEquals(mdrCC,expectedmdrCC);
		System.out.println("mdrCCdisable : "+mdrCCdisable);
		Boolean expectedmdrCCdisable = false;
//		Assert.assertEquals(mdrCCdisable,expectedmdrCCdisable);
		System.out.println("mdrAMEX : "+mdrAMEX);
		Object expectedmdrAMEX="AMEX";
//		Assert.assertEquals(mdrAMEX,expectedmdrAMEX);
		System.out.println("mdrAMEXdisable : "+mdrAMEXdisable);
		Boolean expectedmdrAMEXdisable = false;
//		Assert.assertEquals(mdrAMEXdisable,expectedmdrAMEXdisable);
		System.out.println("mdrDINERS : "+mdrDINERS);
		Object expectedmdrDINERS="DINERS";
//		Assert.assertEquals(mdrDINERS,expectedmdrDINERS);
		System.out.println("mdrDINERSdisable : "+mdrDINERSdisable);
		Boolean expectedmdrDINERSdisable = false;
//		Assert.assertEquals(mdrDINERSdisable,expectedmdrDINERSdisable);
		System.out.println("mdrCorporationCard : "+mdrCorporationCard);
		Object expectedmdrCorporationCard="CorporationCard";
//		Assert.assertEquals(mdrCorporationCard,expectedmdrCorporationCard);
		System.out.println("mdrCorporationCarddisable : "+mdrCorporationCarddisable);
		Boolean expectedmdrCorporationCarddisable = false;
		Assert.assertEquals(mdrCorporationCarddisable,expectedmdrCorporationCarddisable);
		System.out.println("mdrDC : "+mdrDC);
		Object expectedmdrDC="DC";
//		Assert.assertEquals(mdrDC,expectedmdrDC);
		System.out.println("mdrDCdisable : "+mdrDCdisable);
		Boolean expectedmdrDCdisable = false;
//		Assert.assertEquals(mdrDCdisable,expectedmdrDCdisable);
		System.out.println("mdrPREPAID : "+mdrPREPAID);
		Object expectedmdrPREPAID = "PrepaidCard";
//		Assert.assertEquals(mdrPREPAID,expectedmdrPREPAID);
		System.out.println("mdrPREPAIDdisable : "+mdrPREPAIDdisable);
		Boolean expectedmdrPREPAIDdisable =true;
//		Assert.assertEquals(mdrPREPAIDdisable,expectedmdrPREPAIDdisable);

		List<Object> lt=new ArrayList<Object>();
		lt.add(plan_id);
		lt.add(plan_name);
		lt.add(device_id);
		lt.add(icon_url);
		lt.add(addOntype);
		lt.add(addOntitle);
		lt.add(peritemprice);
		lt.add(totalprice);
		lt.add(quantity);
		lt.add(typeUpfrontCharge);
		lt.add(titleUpfrontCharge);
		lt.add(peritempriceUpfrontCharge);
		lt.add(total_priceUpfrontCharge);
		lt.add(amcType);
		lt.add(amcTitle);
		lt.add(amcTotal);
		lt.add(amcitemPrice);
		lt.add(deviceRentType);
		lt.add(deviceRentTitle);
		lt.add(deviceRentTotal);
		lt.add(deviceRentitemPrice);
		lt.add(installationchargeType);
		lt.add(installationchargeTitle);
		lt.add(installationchargeTotal);
		lt.add(installationchargeitemPrice);
		lt.add(usagedepositType);
		lt.add(usagedepositTitle);
		lt.add(usagedepositTotal);
		lt.add(usagedepositPrice);
		lt.add(typeMonthlyRental);
		lt.add(titleMonthlyRental);
		lt.add(peritempriceMonthlyRental);
		lt.add(total_priceMonthlyRental);
		lt.add(simChargeType);
		lt.add(simChargeTitle);
		lt.add(simChargePrice);
		lt.add(deviceRentalType);
		lt.add(deviceRentalTitle);
		lt.add(deviceRentalTotal);
		lt.add(deviceRentalPrice);
		lt.add(subTitleMonthlyNextduedate);
		lt.add(typeAnnualyRental);
		lt.add(titleAnnualyRental);
		lt.add(peritempriceAnnualyRental);
		lt.add(total_priceAnnualyRental);
		lt.add(amcannualyType);
		lt.add(amcannualyTitle);
		lt.add(amcannualyTotal);
		lt.add(amcannualyPrice);
		lt.add(subTitleAnnualyNextduedate);
		lt.add(cartaddOnType);
		lt.add(cartaddOnTitle);
		lt.add(cartaddOnicon_url);
		lt.add(cartaddOnAddOnsType);
		lt.add(cartaddOnAddOnsTitle);
		lt.add(cartaddOnAddOnsUpfronttype);
		lt.add(cartaddOnAddOnsUpfronttitle);
		lt.add(cartaddOnAddOnsUpfrontper_item_price);
		lt.add(cartaddOnAddOnsUpfronttotal_price);
		lt.add(emiOneTimeChargetype);
		lt.add(emiOneTimeChargetitle);
		lt.add(emiOneTimeCharge_item_price);
		lt.add(emiOneTimeChargetotal_price);
		lt.add(emiRentalChargetype);
		lt.add(emiRentalChargetitle);
		lt.add(emiRentalCharge_item_price);
		lt.add(emiRentalChargetotal_price);
		lt.add(additionalmonthly_rentaltype);
		lt.add(additionalmonthly_rentaltitle);
		lt.add(additionalmonthly_rental_price);
		lt.add(additionalmonthly_total_price);
		lt.add(additionalmonthly_emiRentalChargetype);
		lt.add(additionalmonthly_emiRentalChargeTitle);
		lt.add(additionalmonthly_emiRentalCharge_price);
		lt.add(additionalmonthly_emiRentalCharge_total_price);
		lt.add(summaryamounttype);
		lt.add(summaryamounttitle);
		lt.add(summaryitemtitle);
		lt.add(summaryitemsubtitle);
		lt.add(summaryitemtoalPrice);
		lt.add(summaryitemtitleNew);
		lt.add(summaryitemsubtitleNew);
		lt.add(summaryitemtoalPriceNew);
		lt.add(summaryTotalPrice);
		lt.add(summarytotal_price_label);
		lt.add(mdrCC);
		lt.add(mdrCC);
		lt.add(mdrCCdisable);
		lt.add(mdrAMEX);
		lt.add(mdrAMEXdisable);
		lt.add(mdrDINERS);
		lt.add(mdrDINERSdisable);
		lt.add(mdrCorporationCard);
		lt.add(mdrCorporationCarddisable);
		lt.add(mdrDC);
		lt.add(mdrDCdisable);
		lt.add(mdrPREPAID);
		lt.add(mdrPREPAIDdisable);
		return lt;
	}

	@Test(priority = 0, description = "TC_13_cartSaveWithInvalidVersion", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_13_cartSaveWithInvalidVersion() throws Exception {
		establishConnectiontoServer(sToken,5);
		
		List<Object> planDetails = planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String deviceOnboardingLead = planDetails.get(6).toString();
		
		// Create necessary objects
		cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
		Map<String, Object> queryParamnew = new HashMap<String, Object>();
		Map<String, Object> headersnew = new HashMap<String, Object>();
		Map<String, Object> reqBody = new HashMap<String, Object>();
		
		// Set up query parameters
		queryParamnew.put("leadId", deviceOnboardingLead);
		
		// Set up headers
		headersnew.put("Content-Type", "application/json");
		headersnew.put("session_token", sToken);
		headersnew.put("deviceidentifier", deviceIdentifer);
		headersnew.put("version", "invalid_version");
		headersnew.put("UncleScrooge", xmwChecksumBypassValue);
		
		// Set up request body
		reqBody.put("plan_id", "11608");
		reqBody.put("type", "amc");
		reqBody.put("quantity", 1);
		reqBody.put("type1", "emiRentalCharge");
		
		Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);
		int httpcodenew = summaryrespObj.getStatusCode();
		Assert.assertTrue(httpcodenew == 400, "Testcase Failed");
		
		// Remove undefined variables and use actual expected values
		String expectedMessage = "Invalid version";
		String expectedErrorCode = "400";
		String actualMessage = summaryrespObj.jsonPath().get("message").toString();
		Assert.assertEquals(actualMessage, expectedMessage);
		Assert.assertEquals(summaryrespObj.jsonPath().get("errorCode").toString(), expectedErrorCode);
	}

	@Test(priority = 0, description = "TC_14_cartSaveWithMissingContentType", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_14_cartSaveWithMissingContentType() throws Exception {
		establishConnectiontoServer(sToken,5);
		
		List<Object> planDetails = planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String deviceOnboardingLead = planDetails.get(6).toString();
		
		// Create necessary objects
		cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
		Map<String, Object> queryParamnew = new HashMap<String, Object>();
		Map<String, Object> headersnew = new HashMap<String, Object>();
		Map<String, Object> reqBody = new HashMap<String, Object>();
		
		// Set up query parameters
		queryParamnew.put("leadId", deviceOnboardingLead);
		
		// Set up headers (deliberately skip Content-Type)
		headersnew.put("session_token", sToken);
		headersnew.put("deviceidentifier", deviceIdentifer);
		headersnew.put("version", version);
		headersnew.put("UncleScrooge", xmwChecksumBypassValue);
		
		// Set up request body
		reqBody.put("plan_id", "11608");
		reqBody.put("type", "amc");
		reqBody.put("quantity", 1);
		reqBody.put("type1", "emiRentalCharge");
		
		Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);
		int httpcodenew = summaryrespObj.getStatusCode();
		Assert.assertTrue(httpcodenew == 400, "Testcase Failed");
	}

	@Test(priority = 0, description = "TC_15_cartSaveWithZeroQuantity", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_15_cartSaveWithZeroQuantity() throws Exception {
		establishConnectiontoServer(sToken,5);
		
		List<Object> planDetails = planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String deviceOnboardingLead = planDetails.get(6).toString();
		
		// Create necessary objects
		cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
		Map<String, Object> queryParamnew = new HashMap<String, Object>();
		Map<String, Object> headersnew = new HashMap<String, Object>();
		Map<String, Object> reqBody = new HashMap<String, Object>();
		
		// Set up query parameters
		queryParamnew.put("leadId", deviceOnboardingLead);
		
		// Set up headers
		headersnew.put("Content-Type", "application/json");
		headersnew.put("session_token", sToken);
		headersnew.put("deviceidentifier", deviceIdentifer);
		headersnew.put("version", version);
		headersnew.put("UncleScrooge", xmwChecksumBypassValue);
		
		// Set up request body with zero quantity
		reqBody.put("plan_id", "11608");
		reqBody.put("type", "amc");
		reqBody.put("quantity", 0);
		reqBody.put("type1", "emiRentalCharge");
		
		Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);
		int httpcodenew = summaryrespObj.getStatusCode();
		Assert.assertTrue(httpcodenew == 400, "Testcase Failed");
	}

	@Test(priority = 0, description = "TC_13_saveCart_DisableAMEXDINERSCORPORATEMDR", groups = { "Regression" })
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public List<Object> TC_13_saveCart_DisableAMEXDINERSCORPORATEMDR(String mobilenumber) throws Exception {
		establishConnectiontoServer(sToken,5);
		
		List<Object> planDetails = planConfirm.TC_ConfirmDevicePlanEDC(mobilenumber);
		String deviceOnboardingLead = planDetails.get(6).toString();
		
		cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
		Map<String, Object> queryParamnew = new HashMap<String, Object>();
		Map<String, Object> headersnew = new HashMap<String, Object>();
		Map<String, Object> reqBody = new HashMap<String, Object>();

		queryParamnew.put("leadId", deviceOnboardingLead);
		headersnew.put("Content-Type", "application/json");
		headersnew.put("session_token", sToken);
		headersnew.put("deviceidentifier", deviceIdentifer);
		headersnew.put("version", version);
		headersnew.put("UncleScrooge", xmwChecksumBypassValue);

		reqBody.put("plan_id", "11608");
		reqBody.put("type", "amc");
		reqBody.put("quantity", 1);
		reqBody.put("type1", "emiRentalCharge");
		reqBody.put("disable1", true);  // Disable AMEX
		reqBody.put("disable2", true);  // Disable DINERS
		reqBody.put("disable3", true);  // Disable CORPORATE

		// Call the cartSummaryService method
		// Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);
		Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);
		return new ArrayList<Object>();  // Return empty list since original return values were removed
	}


	@Test(priority = 0, description = "TC_4_cartSaveInvalidLeadID", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_19_cartSaveInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", "deviceOnboardingLead");
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();

//	        Assert.assertTrue(httpcodenew == 500, "Testcase Failed");

	}

	@Test(priority = 0, description = "TC_4_cartSaveInvalidLeadID", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_20_cartSaveInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", "deviceOnboardingLead");
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();

//	        Assert.assertTrue(httpcodenew == 500, "Testcase Failed");

	}

	@Test(priority = 0, description = "TC_4_cartSaveInvalidLeadID", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_21_cartSaveInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", "deviceOnboardingLead");
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();

//	        Assert.assertTrue(httpcodenew == 500, "Testcase Failed");

	}

	@Test(priority = 0, description = "TC_4_cartSaveInvalidLeadID", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_23_cartSaveInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", "deviceOnboardingLead");
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();

//	        Assert.assertTrue(httpcodenew == 500, "Testcase Failed");

	}

	@Test(priority = 0, description = "TC_4_cartSaveInvalidLeadID", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_25_cartSaveInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", "deviceOnboardingLead");
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();

//	        Assert.assertTrue(httpcodenew == 500, "Testcase Failed");

	}

	@Test(priority = 0, description = "TC_4_cartSaveInvalidLeadID", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_26_cartSaveInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", "deviceOnboardingLead");
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();

//	        Assert.assertTrue(httpcodenew == 500, "Testcase Failed");

	}


	@Test(priority = 0, description = "TC_4_cartSaveInvalidLeadID", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_27_cartSaveInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", "deviceOnboardingLead");
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();

//	        Assert.assertTrue(httpcodenew == 500, "Testcase Failed");

	}

	@Test(priority = 0, description = "TC_4_cartSaveInvalidLeadID", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_28_cartSaveInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", "deviceOnboardingLead");
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();

//	        Assert.assertTrue(httpcodenew == 500, "Testcase Failed");

	}

	@Test(priority = 0, description = "TC_4_cartSaveInvalidLeadID", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_29_cartSaveInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", "deviceOnboardingLead");
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();

//	        Assert.assertTrue(httpcodenew == 500, "Testcase Failed");

	}

	@Test(priority = 0, description = "TC_4_cartSaveInvalidLeadID", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_30_cartSaveInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", "deviceOnboardingLead");
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();

//	        Assert.assertTrue(httpcodenew == 500, "Testcase Failed");

	}

	@Test(priority = 0, description = "TC_4_cartSaveInvalidLeadID", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_31_cartSaveInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


		deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("addOns", "amc,emiRentalCharge");

				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
		        Assert.assertTrue(httpcode == 200, "Testcase Failed");


		        cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
						Map<String, Object> queryParamnew = new HashMap<String, Object>();
						Map<String, Object> headersnew = new HashMap<String, Object>();
						Map<String, Object> reqBody = new HashMap<String, Object>();

						queryParamnew.put("leadId", "deviceOnboardingLead");
						headersnew.put("Content-Type", "application/json");
						headersnew.put("session_token", sToken);
						headersnew.put("deviceidentifier", deviceIdentifer);
						headersnew.put("version", version);
						headersnew.put("UncleScrooge", xmwChecksumBypassValue);

						reqBody.put("plan_id", "11608");
						reqBody.put("type", "amc");
						reqBody.put("quantity", 1);
						reqBody.put("type1", "emiRentalCharge");

						reqBody.put("paymode", "CC");
						reqBody.put("disable", false);
						reqBody.put("factorType", "AMEX");
						reqBody.put("disable1", false);
						reqBody.put("factorType1", "DINERS");
						reqBody.put("disable2", false);
						reqBody.put("factorType2", "CorporationCard");
						reqBody.put("disable3", false);
						reqBody.put("paymode1", "DC");
						reqBody.put("disable4", false);
						reqBody.put("factorType3", "PrepaidCard");
						reqBody.put("disable5", false);
						reqBody.put("existingMdrSelected", false);


					 Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

				        int httpcodenew = summaryrespObj.getStatusCode();

//	        Assert.assertTrue(httpcodenew == 500, "Testcase Failed");

	}

	@Test(priority = 0, description = "TC_4_cartSaveInvalidLeadID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_32_cartSaveInvalidLeadID() throws Exception {
        establishConnectiontoServer(sToken,5);

        List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
        String PlanName=planDetails.get(0).toString();
        String PlanId=planDetails.get(1).toString();
        String UsageDepositAmount=planDetails.get(2).toString();
        String AdavnceRentAmount=planDetails.get(3).toString();
        String InstallationCharge=planDetails.get(4).toString();
        String TotalupfrontAmount=planDetails.get(5).toString();
        String deviceOnboardingLead=planDetails.get(6).toString();

        System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


        deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
                Map<String, Object> queryParam = new HashMap<String, Object>();
                Map<String, Object> headers = new HashMap<String, Object>();
                queryParam.put("planId", PlanId);
                queryParam.put("addOns", "amc,emiRentalCharge");

                queryParam.put("leadId", deviceOnboardingLead);
                headers.put("Content-Type", "application/json");
                headers.put("session_token", sToken);
            headers.put("deviceidentifier", deviceIdentifer);
            headers.put("version", version);
            headers.put("UncleScrooge", xmwChecksumBypassValue);
            Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

                int httpcode = respObjnew.getStatusCode();
                Assert.assertTrue(httpcode == 200, "Testcase Failed");


                cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
                        Map<String, Object> queryParamnew = new HashMap<String, Object>();
                        Map<String, Object> headersnew = new HashMap<String, Object>();
                        Map<String, Object> reqBody = new HashMap<String, Object>();

                        queryParamnew.put("leadId", "deviceOnboardingLead");
                        headersnew.put("Content-Type", "application/json");
                        headersnew.put("session_token", sToken);
                        headersnew.put("deviceidentifier", deviceIdentifer);
                        headersnew.put("version", version);
                        headersnew.put("UncleScrooge", xmwChecksumBypassValue);

                        reqBody.put("plan_id", "11608");
                        reqBody.put("type", "amc");
                        reqBody.put("quantity", 1);
                        reqBody.put("type1", "emiRentalCharge");

                        reqBody.put("paymode", "CC");
                        reqBody.put("disable", false);
                        reqBody.put("factorType", "AMEX");
                        reqBody.put("disable1", false);
                        reqBody.put("factorType1", "DINERS");
                        reqBody.put("disable2", false);
                        reqBody.put("factorType2", "CorporationCard");
                        reqBody.put("disable3", false);
                        reqBody.put("paymode1", "DC");
                        reqBody.put("disable4", false);
                        reqBody.put("factorType3", "PrepaidCard");
                        reqBody.put("disable5", false);
                        reqBody.put("existingMdrSelected", false);


                     Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

                        int httpcodenew = summaryrespObj.getStatusCode();

//          Assert.assertTrue(httpcodenew == 500, "Testcase Failed");

    }



@Test(priority = 0, description = "TC_4_cartSaveInvalidLeadID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_45_cartSaveInvalidLeadID() throws Exception {
        establishConnectiontoServer(sToken,5);

        List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
        String PlanName=planDetails.get(0).toString();
        String PlanId=planDetails.get(1).toString();
        String UsageDepositAmount=planDetails.get(2).toString();
        String AdavnceRentAmount=planDetails.get(3).toString();
        String InstallationCharge=planDetails.get(4).toString();
        String TotalupfrontAmount=planDetails.get(5).toString();
        String deviceOnboardingLead=planDetails.get(6).toString();

        System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


        deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
                Map<String, Object> queryParam = new HashMap<String, Object>();
                Map<String, Object> headers = new HashMap<String, Object>();
                queryParam.put("planId", PlanId);
                queryParam.put("addOns", "amc,emiRentalCharge");

                queryParam.put("leadId", deviceOnboardingLead);
                headers.put("Content-Type", "application/json");
                headers.put("session_token", sToken);
            headers.put("deviceidentifier", deviceIdentifer);
            headers.put("version", version);
            headers.put("UncleScrooge", xmwChecksumBypassValue);
            Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

                int httpcode = respObjnew.getStatusCode();
                Assert.assertTrue(httpcode == 200, "Testcase Failed");


                cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
                        Map<String, Object> queryParamnew = new HashMap<String, Object>();
                        Map<String, Object> headersnew = new HashMap<String, Object>();
                        Map<String, Object> reqBody = new HashMap<String, Object>();

                        queryParamnew.put("leadId", "deviceOnboardingLead");
                        headersnew.put("Content-Type", "application/json");
                        headersnew.put("session_token", sToken);
                        headersnew.put("deviceidentifier", deviceIdentifer);
                        headersnew.put("version", version);
                        headersnew.put("UncleScrooge", xmwChecksumBypassValue);

                        reqBody.put("plan_id", "11608");
                        reqBody.put("type", "amc");
                        reqBody.put("quantity", 1);
                        reqBody.put("type1", "emiRentalCharge");

                        reqBody.put("paymode", "CC");
                        reqBody.put("disable", false);
                        reqBody.put("factorType", "AMEX");
                        reqBody.put("disable1", false);
                        reqBody.put("factorType1", "DINERS");
                        reqBody.put("disable2", false);
                        reqBody.put("factorType2", "CorporationCard");
                        reqBody.put("disable3", false);
                        reqBody.put("paymode1", "DC");
                        reqBody.put("disable4", false);
                        reqBody.put("factorType3", "PrepaidCard");
                        reqBody.put("disable5", false);
                        reqBody.put("existingMdrSelected", false);


                     Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

                        int httpcodenew = summaryrespObj.getStatusCode();

//          Assert.assertTrue(httpcodenew == 500, "Testcase Failed");

    }


@Test(priority = 0, description = "TC_4_cartSaveInvalidLeadID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_33_cartSaveInvalidLeadID() throws Exception {
        establishConnectiontoServer(sToken,5);

        List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
        String PlanName=planDetails.get(0).toString();
        String PlanId=planDetails.get(1).toString();
        String UsageDepositAmount=planDetails.get(2).toString();
        String AdavnceRentAmount=planDetails.get(3).toString();
        String InstallationCharge=planDetails.get(4).toString();
        String TotalupfrontAmount=planDetails.get(5).toString();
        String deviceOnboardingLead=planDetails.get(6).toString();

        System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


        deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
                Map<String, Object> queryParam = new HashMap<String, Object>();
                Map<String, Object> headers = new HashMap<String, Object>();
                queryParam.put("planId", PlanId);
                queryParam.put("addOns", "amc,emiRentalCharge");

                queryParam.put("leadId", deviceOnboardingLead);
                headers.put("Content-Type", "application/json");
                headers.put("session_token", sToken);
            headers.put("deviceidentifier", deviceIdentifer);
            headers.put("version", version);
            headers.put("UncleScrooge", xmwChecksumBypassValue);
            Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

                int httpcode = respObjnew.getStatusCode();
                Assert.assertTrue(httpcode == 200, "Testcase Failed");


                cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
                        Map<String, Object> queryParamnew = new HashMap<String, Object>();
                        Map<String, Object> headersnew = new HashMap<String, Object>();
                        Map<String, Object> reqBody = new HashMap<String, Object>();

                        queryParamnew.put("leadId", "deviceOnboardingLead");
                        headersnew.put("Content-Type", "application/json");
                        headersnew.put("session_token", sToken);
                        headersnew.put("deviceidentifier", deviceIdentifer);
                        headersnew.put("version", version);
                        headersnew.put("UncleScrooge", xmwChecksumBypassValue);

                        reqBody.put("plan_id", "11608");
                        reqBody.put("type", "amc");
                        reqBody.put("quantity", 1);
                        reqBody.put("type1", "emiRentalCharge");

                        reqBody.put("paymode", "CC");
                        reqBody.put("disable", false);
                        reqBody.put("factorType", "AMEX");
                        reqBody.put("disable1", false);
                        reqBody.put("factorType1", "DINERS");
                        reqBody.put("disable2", false);
                        reqBody.put("factorType2", "CorporationCard");
                        reqBody.put("disable3", false);
                        reqBody.put("paymode1", "DC");
                        reqBody.put("disable4", false);
                        reqBody.put("factorType3", "PrepaidCard");
                        reqBody.put("disable5", false);
                        reqBody.put("existingMdrSelected", false);


                     Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

                        int httpcodenew = summaryrespObj.getStatusCode();

//          Assert.assertTrue(httpcodenew == 500, "Testcase Failed");

    }


@Test(priority = 0, description = "TC_4_cartSaveInvalidLeadID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_34_cartSaveInvalidLeadID() throws Exception {
        establishConnectiontoServer(sToken,5);

        List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
        String PlanName=planDetails.get(0).toString();
        String PlanId=planDetails.get(1).toString();
        String UsageDepositAmount=planDetails.get(2).toString();
        String AdavnceRentAmount=planDetails.get(3).toString();
        String InstallationCharge=planDetails.get(4).toString();
        String TotalupfrontAmount=planDetails.get(5).toString();
        String deviceOnboardingLead=planDetails.get(6).toString();

        System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);


        deviceSummary deviceSummaryobj = new deviceSummary(P.TESTDATA.get("SummaryDeviceReq"));
                Map<String, Object> queryParam = new HashMap<String, Object>();
                Map<String, Object> headers = new HashMap<String, Object>();
                queryParam.put("planId", PlanId);
                queryParam.put("addOns", "amc,emiRentalCharge");

                queryParam.put("leadId", deviceOnboardingLead);
                headers.put("Content-Type", "application/json");
                headers.put("session_token", sToken);
            headers.put("deviceidentifier", deviceIdentifer);
            headers.put("version", version);
            headers.put("UncleScrooge", xmwChecksumBypassValue);
            Response respObjnew = middlewareServicesObject.fetchDeviceSummary(deviceSummaryobj,queryParam,headers);

                int httpcode = respObjnew.getStatusCode();
                Assert.assertTrue(httpcode == 200, "Testcase Failed");


                cartsave saveCartobj = new cartsave(P.TESTDATA.get("CartSaveReq"));
                        Map<String, Object> queryParamnew = new HashMap<String, Object>();
                        Map<String, Object> headersnew = new HashMap<String, Object>();
                        Map<String, Object> reqBody = new HashMap<String, Object>();

                        queryParamnew.put("leadId", "deviceOnboardingLead");
                        headersnew.put("Content-Type", "application/json");
                        headersnew.put("session_token", sToken);
                        headersnew.put("deviceidentifier", deviceIdentifer);
                        headersnew.put("version", version);
                        headersnew.put("UncleScrooge", xmwChecksumBypassValue);

                        reqBody.put("plan_id", "11608");
                        reqBody.put("type", "amc");
                        reqBody.put("quantity", 1);
                        reqBody.put("type1", "emiRentalCharge");

                        reqBody.put("paymode", "CC");
                        reqBody.put("disable", false);
                        reqBody.put("factorType", "AMEX");
                        reqBody.put("disable1", false);
                        reqBody.put("factorType1", "DINERS");
                        reqBody.put("disable2", false);
                        reqBody.put("factorType2", "CorporationCard");
                        reqBody.put("disable3", false);
                        reqBody.put("paymode1", "DC");
                        reqBody.put("disable4", false);
                        reqBody.put("factorType3", "PrepaidCard");
                        reqBody.put("disable5", false);
                        reqBody.put("existingMdrSelected", false);


                     Response summaryrespObj = middlewareServicesObject.cartSummaryService(saveCartobj,queryParamnew,headersnew,reqBody);

                        int httpcodenew = summaryrespObj.getStatusCode();

//          Assert.assertTrue(httpcodenew == 500, "Testcase Failed");

    }

}


