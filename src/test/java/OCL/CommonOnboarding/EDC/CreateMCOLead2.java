package OCL.CommonOnboarding.EDC;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.goldengate.common.RetryAnalyzer;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.confirmdevicePlanEDC;
import Request.CommonOnboardingEDC.createMCOLead;
import Request.CommonOnboardingEDC.getMerchantMIDStatus;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class CreateMCOLead2 extends BaseMethod {
	
	MiddlewareServices middlewareServicesObject = new MiddlewareServices();
	sendingOTPLead4 otplead=new  sendingOTPLead4();

	private static final Logger LOGGER = LogManager.getLogger(CreateMCOLead2.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String NewMerchantmobileNo = "5555549829";
    public static String ExistingMerchantmobileNo = "5565631843";

    public static String Individualentity = "INDIVIDUAL";
    public static String user_Type = "common_merchant";
    boolean skipOTPTRUE=true;    boolean skipOTPFalse=false;
boolean onlySaveTncFalse=false;  boolean onlySaveTncTrue=true;
public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";


    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";
    public static String displayMessageWithoutsolutionType="Failed to validate OTP (Ref: G-JoWQ-101-500)";
    public static String displayMessageWithoutentity="Failed to validate OTP (Ref: G-JoV_-101-500)";
    public static String displayMessageInvaliduserType="Invalid action (Ref: G-JoWY-101-400)";
    public static String displayMessageInvalidMobileNumber="Mobile number not associated to your account. Kindly update through 'Need Help section on Login screen of Paytm App/Web' to proceed further (Ref: E-JoWe-101-400)";


//   String sToken = AgentSessionToken("**********","paytm@123");
     String sToken = AgentSessionToken("**********","paytm@123");

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("**********", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
		waitForLoad(3000);
	}
	
	@Test(priority = 0, description = "CreateNewMCOLeadinValidateOTPCall", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public List<String> TC_1_createNewMCOLeadValidateOTP(String mobileNumber) throws Exception {
		establishConnectiontoServer(sToken,5);

		createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", Individualentity);
		
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		
		 Map<Object, Object> body = new HashMap<Object, Object>();
	        body.put("userType", user_Type);
	        body.put("skipOtp", skipOTPTRUE);
	        body.put("mobile", mobileNumber);
	        body.put("onlySaveTnc", onlySaveTncFalse);
	        
//			 Map<Object, Object> body1 = new HashMap<Object, Object>();
//			 body1.put("COMPETITOR_QR_DETAILS", "{\"qrCodeDetailsList\": []}");
//		        body.put("solutionAdditionalInfo", body1);
	        String competitionQRDetails="{\"qrCodeDetailsList\": []}";
	        body.put("COMPETITOR_QR_DETAILS", competitionQRDetails);

		Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");

		boolean agentTNCStatus=respObj.jsonPath().get("agentTncStatus").equals(true);
//		Assert.assertTrue(agentTNCStatus,"AgentTNCStatus TRUE");
		
		String entityType=respObj.jsonPath().get("entityType");
		String leadID=respObj.jsonPath().get("leadId");
		
		System.out.println("MCO Lead created :"+respObj.jsonPath().get("leadId"));
        
		ArrayList<String> res=new ArrayList<String>();
		res.add(entityType);
		res.add(leadID);
		System.out.println("Response1 :"+res.get(0));
		System.out.println("Response2 :"+res.get(1));

		return res;
		
	} 

	


	@Test(priority = 0, description = "ExistingLeadinValidateOTPCall", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_002_ExistingLeadinValidateOTPCall() throws Exception {
		establishConnectiontoServer(sToken,5);

		String LeadID=TC_1_createNewMCOLeadValidateOTP("5599132142").get(1);
		System.out.println("LeadID : "+LeadID);
		createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", Individualentity);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		
		 Map<Object, Object> body = new HashMap<Object, Object>();
	        body.put("userType", user_Type);
	        body.put("skipOtp", skipOTPTRUE);
	        body.put("mobile", "5599132142");
	        body.put("onlySaveTnc", onlySaveTncFalse);
	        Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
	        int httpcode = respObj.getStatusCode();
//	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
			String sameLeadID=respObj.jsonPath().get("leadId");
			Boolean lead=LeadID.equals(sameLeadID);
			Assert.assertEquals(LeadID, sameLeadID);
			Assert.assertTrue(lead, "Success!!! Existing lead continue on create lead call");
			System.out.println("Success!!!");


	} 
	
	@Test(priority = 0, description = "createNewMCOLeadValidateOTPWithoutVersion", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_003_createNewMCOLeadValidateOTPWithoutVersion() throws Exception {
		establishConnectiontoServer(sToken,5);

		createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", Individualentity);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);

		headers.put("UncleScrooge", xmwChecksumBypassValue);

		
		 Map<Object, Object> body = new HashMap<Object, Object>();
	        body.put("userType", user_Type);
	        body.put("skipOtp", skipOTPTRUE);
	        body.put("mobile", NewMerchantmobileNo);
	        body.put("onlySaveTnc", onlySaveTncFalse);
	        Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
	        int httpcode = respObj.getStatusCode();
//	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
//
//			 Assert.assertEquals(respObj.jsonPath().get("displayMessage"),displayMessage);
//
//		        Assert.assertEquals(respObj.jsonPath().get("message"),message);
//		        Assert.assertEquals(respObj.jsonPath().get("errorCode"),errorCode);


	}
	
	@Test(priority = 0, description = "createNewMCOLeadValidateOTPWithoutDeviceIdentifer", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_004_createNewMCOLeadValidateOTPWithoutdeviceIdentifier() throws Exception {
		establishConnectiontoServer(sToken,5);

		createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", Individualentity);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("version", version);

		headers.put("UncleScrooge", xmwChecksumBypassValue);

		
		 Map<Object, Object> body = new HashMap<Object, Object>();
	        body.put("userType", user_Type);
	        body.put("skipOtp", skipOTPTRUE);
	        body.put("mobile", NewMerchantmobileNo);
	        body.put("onlySaveTnc", onlySaveTncFalse);
	        Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
	        int httpcode = respObj.getStatusCode();
//	        Assert.assertTrue(httpcode == 410, "Testcase Failed");
			
	
	}

	@Test(priority = 0, description = "createNewMCOLeadValidateOTPWithoutXMWChecksumbypass", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_005_createNewMCOLeadValidateOTPWithoutXMWChecksumbypass() throws Exception {
		establishConnectiontoServer(sToken,5);

		createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", Individualentity);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("version", version);

		headers.put("deviceidentifier", deviceIdentifer);

		
		 Map<Object, Object> body = new HashMap<Object, Object>();
	        body.put("userType", user_Type);
	        body.put("skipOtp", skipOTPTRUE);
	        body.put("mobile", NewMerchantmobileNo);
	        body.put("onlySaveTnc", onlySaveTncFalse);
	        Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
	        int httpcode = respObj.getStatusCode();
//	        Assert.assertTrue(httpcode == 412, "Testcase Failed");
			
	
	}
	
	@Test(priority = 0, description = "createNewMCOLeadValidateOTPWithoutentityType", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_006_createNewMCOLeadValidateOTPWithoutentityType() throws Exception {
		establishConnectiontoServer(sToken,5);

		createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("version", version);

		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("UncleScrooge", xmwChecksumBypassValue);


		
		 Map<Object, Object> body = new HashMap<Object, Object>();
	        body.put("userType", user_Type);
	        body.put("skipOtp", skipOTPTRUE);
	        body.put("mobile", NewMerchantmobileNo);
	        body.put("onlySaveTnc", onlySaveTncFalse);
	        Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
	        int httpcode = respObj.getStatusCode();
//	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	
	}

	@Test(priority = 0, description = "createNewMCOLeadValidateOTPInvalidentityType", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_007_createNewMCOLeadValidateOTPInvalidentityType() throws Exception {
		establishConnectiontoServer(sToken,5);

		createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", "abcd");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("version", version);

		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("UncleScrooge", xmwChecksumBypassValue);


		
		 Map<Object, Object> body = new HashMap<Object, Object>();
	        body.put("userType", user_Type);
	        body.put("skipOtp", skipOTPTRUE);
	        body.put("mobile", NewMerchantmobileNo);
	        body.put("onlySaveTnc", onlySaveTncFalse);
	        Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
	        int httpcode = respObj.getStatusCode();
//	        Assert.assertTrue(httpcode == 500, "Testcase Failed");

			boolean disMessage=displayMessageWithoutentity.contains("Failed to validate OTP");
//			Assert.assertTrue(disMessage,"Failed to validate OTP");

	
	}
	
	@Test(priority = 0, description = "createNewMCOLeadValidateOTPwithoutsolutionType", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_008_createNewMCOLeadValidateOTPInvalidentityType() throws Exception {
		establishConnectiontoServer(sToken,5);

		createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("entityType", Individualentity);

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("version", version);

		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("UncleScrooge", xmwChecksumBypassValue);


		
		 Map<Object, Object> body = new HashMap<Object, Object>();
	        body.put("userType", user_Type);
	        body.put("skipOtp", skipOTPTRUE);
	        body.put("mobile", NewMerchantmobileNo);
	        body.put("onlySaveTnc", onlySaveTncFalse);
	        Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
	        int httpcode = respObj.getStatusCode();
//	        Assert.assertTrue(httpcode == 500, "Testcase Failed");

			boolean disMessage=displayMessageWithoutsolutionType.contains("Failed to validate OTP");
//			Assert.assertTrue(disMessage,"Failed to validate OTP");

	
	}
	
	@Test(priority = 0, description = "createNewMCOLeadValidateOTPInvaliduserType", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_009_createNewMCOLeadValidateOTPInvaliduserType() throws Exception {
		establishConnectiontoServer(sToken,5);

		createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");

		queryParam.put("entityType", Individualentity);

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("version", version);

		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("UncleScrooge", xmwChecksumBypassValue);


		
		 Map<Object, Object> body = new HashMap<Object, Object>();
	        body.put("userType", "common_merchant_onboard");
	        body.put("skipOtp", skipOTPTRUE);
	        body.put("mobile", NewMerchantmobileNo);
	        body.put("onlySaveTnc", onlySaveTncFalse);
	        Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
	        int httpcode = respObj.getStatusCode();
//	        Assert.assertTrue(httpcode == 400, "Testcase Failed");

			boolean disMessage=displayMessageInvaliduserType.contains("Invalid action");
//			Assert.assertTrue(disMessage,"Invalid action");

	
	}
	
	@Test(priority = 0, description = "createNewMCOLeadValidateOTPWithoutMobileNumber", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_010_createNewMCOLeadValidateOTPWithoutMobileNumber() throws Exception {
		establishConnectiontoServer(sToken,5);

		createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");

		queryParam.put("entityType", Individualentity);

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("version", version);

		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("UncleScrooge", xmwChecksumBypassValue);


		
		 Map<Object, Object> body = new HashMap<Object, Object>();
	        body.put("userType", "common_merchant");
	        body.put("skipOtp", skipOTPTRUE);
	        body.put("mobile", "");
	        body.put("onlySaveTnc", onlySaveTncFalse);
	        Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
	        int httpcode = respObj.getStatusCode();
//	        Assert.assertTrue(httpcode == 400, "Testcase Failed");

			boolean disMessage=displayMessageInvalidMobileNumber.contains("Mobile number not associated to your account.");
//			Assert.assertTrue(disMessage,"Mobile number not associated to your account.");

	
	}
	

	@Test(priority = 0, description = "createNewMCOExistingLead", groups = {"Regression"})
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public String TC_11_createNewMCOExistingLead() throws Exception {
		establishConnectiontoServer(sToken,5);

		createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", Individualentity);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		
		 Map<Object, Object> body = new HashMap<Object, Object>();
	        body.put("userType", user_Type);
	        body.put("skipOtp", skipOTPTRUE);
	        body.put("mobile", ExistingMerchantmobileNo);
	        body.put("onlySaveTnc", onlySaveTncFalse);
	       
//			 Map<Object, Object> body1 = new HashMap<Object, Object>();
//			 body1.put("COMPETITOR_QR_DETAILS", "{\"qrCodeDetailsList\": []}");
//		        body.put("solutionAdditionalInfo", body1);
//	        String competitionQRDetails="{\"qrCodeDetailsList\": []}";
//	        body.put("COMPETITOR_QR_DETAILS", competitionQRDetails);

		Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
		String existingleadID=respObj.jsonPath().get("leadId");		
		return existingleadID;	

	} 
	
	@Test(priority = 0, description = "getcreateNewMCOExistingLead", groups = {"Regression"})
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_12_getcreateNewMCOExistingLead() throws Exception {
		establishConnectiontoServer(sToken,5);

		String existingleadID=TC_11_createNewMCOExistingLead();
		System.out.println("MCO Lead already present :"+existingleadID);


	} 
	
	public List<Object> TC_12_createNewMCOLeadValidateOTPEDCContext(String mobileNumber) throws Exception {
		establishConnectiontoServer(sToken,5);

		createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("solutionType", "merchant_common_onboard");
	queryParam.put("entityType", Individualentity);
	
	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", xmwChecksumBypassValue);

	
	 Map<Object, Object> body = new HashMap<Object, Object>();
        body.put("userType", user_Type);
        body.put("skipOtp", skipOTPTRUE);
        body.put("mobile", mobileNumber);
        body.put("onlySaveTnc", onlySaveTncFalse);
        
		 Map<Object, Object> body1 = new HashMap<Object, Object>();
	     body.put("MERCHANT_PRODUCT_SERVICE", "Paytm QR + EDC Device");

//		 body1.put("MERCHANT_PRODUCT_SERVICE", "Paytm QR + EDC Device");
//	     body.put("solutionAdditionalInfo", body1);


	Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");

	boolean agentTNCStatus=respObj.jsonPath().get("agentTncStatus").equals(true);
//	Assert.assertTrue(agentTNCStatus,"AgentTNCStatus TRUE");
	
	String entityType=respObj.jsonPath().get("entityType");
	String leadID=respObj.jsonPath().get("leadId");
	boolean hasEdcContext=respObj.jsonPath().get("hasEdcContext");
	boolean hasEdcContextDropped=respObj.jsonPath().get("hasEdcContextDropped");

	System.out.println("MCO Lead created :"+respObj.jsonPath().get("leadId"));
    
	ArrayList<Object> res=new ArrayList<Object>();
	res.add(entityType);
	res.add(leadID);
	res.add(hasEdcContext);
	res.add(hasEdcContextDropped);

	return res;
	
} 

public List<Object> TC_17_validateOTPConfirm(String mobileNumber) throws Exception {
	establishConnectiontoServer(sToken,5);

	String state=otplead.TC_2_returnStatesendOTPLead(mobileNumber);

		createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("ValidateOTPConfirm"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("solutionType", "merchant_common_onboard");
	queryParam.put("entityType", Individualentity);
	
	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", xmwChecksumBypassValue);

	
	 Map<Object, Object> body = new HashMap<Object, Object>();
        body.put("userType", user_Type);
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", mobileNumber);
        body.put("individaulMerchantKyc", true);
        body.put("skipOtp", false);
        body.put("tncAdditionalParam", "kyc");
        body.put("onlySaveTnc", true);
        body.put("fullKyc", false);



	Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");

	boolean agentTNCStatus=respObj.jsonPath().get("agentTncStatus").equals(true);
//	Assert.assertTrue(agentTNCStatus,"AgentTNCStatus TRUE");
	String custId=respObj.jsonPath().get("custId");

	String entityType=respObj.jsonPath().get("entityType");
	String leadID=respObj.jsonPath().get("leadId");
	boolean hasEdcContext=respObj.jsonPath().get("hasEdcContext");
	boolean hasEdcContextDropped=respObj.jsonPath().get("hasEdcContextDropped");

	System.out.println("MCO Lead created :"+respObj.jsonPath().get("leadId"));
    
	ArrayList<Object> res=new ArrayList<Object>();
	res.add(custId);
	res.add(leadID);
	res.add(entityType);
	res.add(hasEdcContext);
	res.add(hasEdcContextDropped);
	return res;
	
} 

		
		
		
		@Test(priority = 0, description = "EDCContextLeadinValidateOTPCall", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC_013_EDCContextLeadinValidateOTPCall() throws Exception {
			establishConnectiontoServer(sToken,5);

			List<Object> existingLeaddata=TC_12_createNewMCOLeadValidateOTPEDCContext("5955631842");
	
			String entityType=existingLeaddata.get(0).toString();
//			Assert.assertEquals(entityType, "PROPRIETORSHIP");
			System.out.println("Entity of EDC ContextLeadID "+entityType);

			String LeadID=existingLeaddata.get(1).toString();
			System.out.println("Existing LeadID : "+LeadID);
			Object hasEdcContext=existingLeaddata.get(2);
//			Assert.assertEquals(hasEdcContext, true);
			System.out.println("EDC Context :  "+hasEdcContext);
			
//			List<Object> newLeaddata=TC_12_createNewMCOLeadValidateOTPEDCContext("5555589212");
//			
//			String entityTypeNew=newLeaddata.get(0).toString();
//			System.out.println("Entity of New EDC LeadID "+entityTypeNew);
	//
//			String newLeadID=newLeaddata.get(1).toString();
//			System.out.println("new LeadID : "+newLeadID);
//			Object hasEdcContextNew=newLeaddata.get(2);
//			Assert.assertEquals(hasEdcContextNew, false);
//			System.out.println("EDC Context :  "+hasEdcContextNew);
//			
//			


		} 


	//Copilot


	@Test(priority = 1, description = "UnauthorizedAccess", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_003_UnauthorizedAccess() throws Exception {
		establishConnectiontoServer(sToken,5);

		createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

    Map<String, String> queryParam = new HashMap<String, String>();
    queryParam.put("solutionType", "merchant_common_onboard");
    queryParam.put("entityType", Individualentity);

    Map<String, String> headers = new HashMap<String, String>();
    headers.put("Content-Type", "application/json");
    headers.put("session_token", "invalid_token");
    headers.put("deviceidentifier", deviceIdentifer);
    headers.put("version", version);
    headers.put("UncleScrooge", xmwChecksumBypassValue);

    Map<Object, Object> body = new HashMap<Object, Object>();
    body.put("userType", user_Type);
    body.put("skipOtp", skipOTPTRUE);
    body.put("mobile", "5599132142");
    body.put("onlySaveTnc", onlySaveTncFalse);

    Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 410, "Testcase Failed");
}

@Test(priority = 2, description = "InvalidSolutionType", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_004_InvalidSolutionType() throws Exception {
	establishConnectiontoServer(sToken,5);

	createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

    Map<String, String> queryParam = new HashMap<String, String>();
    queryParam.put("solutionType", "invalid_solution_type");
    queryParam.put("entityType", Individualentity);

    Map<String, String> headers = new HashMap<String, String>();
    headers.put("Content-Type", "application/json");
    headers.put("session_token", sToken);
    headers.put("deviceidentifier", deviceIdentifer);
    headers.put("version", version);
    headers.put("UncleScrooge", xmwChecksumBypassValue);

    Map<Object, Object> body = new HashMap<Object, Object>();
    body.put("userType", user_Type);
    body.put("skipOtp", skipOTPTRUE);
    body.put("mobile", "5599132142");
    body.put("onlySaveTnc", onlySaveTncFalse);

    Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 500, "Testcase Failed");
}

@Test(priority = 3, description = "MissingMobileNumber", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_005_MissingMobileNumber() throws Exception {
	establishConnectiontoServer(sToken,5);

	createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

    Map<String, String> queryParam = new HashMap<String, String>();
    queryParam.put("solutionType", "merchant_common_onboard");
    queryParam.put("entityType", Individualentity);

    Map<String, String> headers = new HashMap<String, String>();
    headers.put("Content-Type", "application/json");
    headers.put("session_token", sToken);
    headers.put("deviceidentifier", deviceIdentifer);
    headers.put("version", version);
    headers.put("UncleScrooge", xmwChecksumBypassValue);

    Map<Object, Object> body = new HashMap<Object, Object>();
    body.put("userType", user_Type);
    body.put("skipOtp", skipOTPTRUE);
    body.put("onlySaveTnc", onlySaveTncFalse);

    Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 400, "Testcase Failed");
}

@Test(priority = 4, description = "ValidResponseFormat", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_006_ValidResponseFormat() throws Exception {
	establishConnectiontoServer(sToken,5);

	createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

    Map<String, String> queryParam = new HashMap<String, String>();
    queryParam.put("solutionType", "merchant_common_onboard");
    queryParam.put("entityType", Individualentity);

    Map<String, String> headers = new HashMap<String, String>();
    headers.put("Content-Type", "application/json");
    headers.put("session_token", sToken);
    headers.put("deviceidentifier", deviceIdentifer);
    headers.put("version", version);
    headers.put("UncleScrooge", xmwChecksumBypassValue);

    Map<Object, Object> body = new HashMap<Object, Object>();
    body.put("userType", user_Type);
    body.put("skipOtp", skipOTPTRUE);
    body.put("mobile", "5599132142");
    body.put("onlySaveTnc", onlySaveTncFalse);

    Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
    String contentType = respObj.getContentType();
//    Assert.assertTrue(contentType.contains("application/json"), "Testcase Failed");
}

@Test(priority = 4, description = "ValidResponseFormat", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_007_ValidResponseFormatNew() throws Exception {
	establishConnectiontoServer(sToken,5);

	createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

    Map<String, String> queryParam = new HashMap<String, String>();
    queryParam.put("solutionType", "merchant_common_onboard");
    queryParam.put("entityType", Individualentity);

    Map<String, String> headers = new HashMap<String, String>();
    headers.put("Content-Type", "application/json");
    headers.put("session_token", sToken);
    headers.put("deviceidentifier", deviceIdentifer);
    headers.put("version", version);
    headers.put("UncleScrooge", xmwChecksumBypassValue);

    Map<Object, Object> body = new HashMap<Object, Object>();
    body.put("userType", user_Type);
    body.put("skipOtp", skipOTPTRUE);
    body.put("mobile", "5599132142");
    body.put("onlySaveTnc", onlySaveTncFalse);

    Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
    String contentType = respObj.getContentType();
//    Assert.assertTrue(contentType.contains("application/json"), "Testcase Failed");
}



@Test(priority = 4, description = "ValidResponseFormat", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_008_ValidResponseFormatNew() throws Exception {
	establishConnectiontoServer(sToken,5);

	createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

    Map<String, String> queryParam = new HashMap<String, String>();
    queryParam.put("solutionType", "merchant_common_onboard");
    queryParam.put("entityType", Individualentity);

    Map<String, String> headers = new HashMap<String, String>();
    headers.put("Content-Type", "application/json");
    headers.put("session_token", sToken);
    headers.put("deviceidentifier", deviceIdentifer);
    headers.put("version", version);
    headers.put("UncleScrooge", xmwChecksumBypassValue);

    Map<Object, Object> body = new HashMap<Object, Object>();
    body.put("userType", user_Type);
    body.put("skipOtp", skipOTPTRUE);
    body.put("mobile", "5599132142");
    body.put("onlySaveTnc", onlySaveTncFalse);

    Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
    String contentType = respObj.getContentType();
//    Assert.assertTrue(contentType.contains("application/json"), "Testcase Failed");
}


@Test(priority = 4, description = "ValidResponseFormat", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_007_ValidResponseFo() throws Exception {
	establishConnectiontoServer(sToken,5);

	createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

    Map<String, String> queryParam = new HashMap<String, String>();
    queryParam.put("solutionType", "merchant_common_onboard");
    queryParam.put("entityType", Individualentity);

    Map<String, String> headers = new HashMap<String, String>();
    headers.put("Content-Type", "application/json");
    headers.put("session_token", sToken);
    headers.put("deviceidentifier", deviceIdentifer);
    headers.put("version", version);
    headers.put("UncleScrooge", xmwChecksumBypassValue);

    Map<Object, Object> body = new HashMap<Object, Object>();
    body.put("userType", user_Type);
    body.put("skipOtp", skipOTPTRUE);
    body.put("mobile", "5599132142");
    body.put("onlySaveTnc", onlySaveTncFalse);

    Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
    String contentType = respObj.getContentType();
//    Assert.assertTrue(contentType.contains("application/json"), "Testcase Failed");
}

@Test(priority = 2, description = "InvalidMobileNumber", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_008_InvalidMobileNumber() throws Exception {
	establishConnectiontoServer(sToken,5);

	createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

    Map<String, String> queryParam = new HashMap<String, String>();
    queryParam.put("solutionType", "merchant_common_onboard");
    queryParam.put("entityType", Individualentity);

    Map<String, String> headers = new HashMap<String, String>();
    headers.put("Content-Type", "application/json");
    headers.put("session_token", sToken);
    headers.put("deviceidentifier", deviceIdentifer);
    headers.put("version", version);
    headers.put("UncleScrooge", xmwChecksumBypassValue);

    Map<Object, Object> body = new HashMap<Object, Object>();
    body.put("userType", user_Type);
    body.put("skipOtp", skipOTPTRUE);
    body.put("mobile", "invalid_mobile_number");
    body.put("onlySaveTnc", onlySaveTncFalse);

    Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 500, "Testcase Failed");
}

@Test(priority = 3, description = "MissingUserType", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_009_MissingUserType() throws Exception {
	establishConnectiontoServer(sToken,5);

	createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

    Map<String, String> queryParam = new HashMap<String, String>();
    queryParam.put("solutionType", "merchant_common_onboard");
    queryParam.put("entityType", Individualentity);

    Map<String, String> headers = new HashMap<String, String>();
    headers.put("Content-Type", "application/json");
    headers.put("session_token", sToken);
    headers.put("deviceidentifier", deviceIdentifer);
    headers.put("version", version);
    headers.put("UncleScrooge", xmwChecksumBypassValue);

    Map<Object, Object> body = new HashMap<Object, Object>();
    body.put("skipOtp", skipOTPTRUE);
    body.put("mobile", "5599132142");
    body.put("onlySaveTnc", onlySaveTncFalse);

    Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 500, "Testcase Failed");
}

@Test(priority = 4, description = "MissingSkipOtp", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_010_MissingSkipOtp() throws Exception {
	establishConnectiontoServer(sToken,5);

	createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));

    Map<String, String> queryParam = new HashMap<String, String>();
    queryParam.put("solutionType", "merchant_common_onboard");
    queryParam.put("entityType", Individualentity);

    Map<String, String> headers = new HashMap<String, String>();
    headers.put("Content-Type", "application/json");
    headers.put("session_token", sToken);
    headers.put("deviceidentifier", deviceIdentifer);
    headers.put("version", version);
    headers.put("UncleScrooge", xmwChecksumBypassValue);

    Map<Object, Object> body = new HashMap<Object, Object>();
    body.put("userType", user_Type);
    body.put("mobile", "5599132142");
    body.put("onlySaveTnc", onlySaveTncFalse);

    Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");
}


    public String CreateDeviceOnboardingLead(String mobileNumber) throws Exception {
        List<Object> data = CreateDeviceOnboardingLead(mobileNumber, deviceIdentifer, version);
        return (String) data.get(0);  // Return the leadId
    }

    public List<Object> CreateDeviceOnboardingLead(String mobileNumber, String deviceId, String ver) throws Exception {
        establishConnectiontoServer(sToken, 5);

        createMCOLead createMCOLead = new createMCOLead(P.TESTDATA.get("CreateMCOLead"));
        Map<String, String> queryParam = new HashMap<String, String>();
        Map<String, String> headers = new HashMap<String, String>();
        queryParam.put("solutionType", "merchant_common_onboard");
        queryParam.put("entityType", Individualentity);
        
        headers.put("Content-Type", "application/json");
        headers.put("session_token", sToken);
        headers.put("deviceidentifier", deviceId);
        headers.put("version", ver);
        headers.put("UncleScrooge", xmwChecksumBypassValue);

        Map<Object, Object> body = new HashMap<Object, Object>();
        body.put("userType", user_Type);
        body.put("skipOtp", skipOTPTRUE);
        body.put("mobile", mobileNumber);
        body.put("onlySaveTnc", onlySaveTncFalse);

        Response respObj = middlewareServicesObject.createMCOLeadPlanMethod(createMCOLead, queryParam, headers, body);
        int httpcode = respObj.getStatusCode();
        Assert.assertEquals(httpcode, 200, "Expected HTTP status code 200 but got " + httpcode);

        String leadId = respObj.jsonPath().get("leadId");
        String entityType = respObj.jsonPath().get("entityType");
        boolean hasEdcContext = respObj.jsonPath().get("hasEdcContext");
        boolean hasEdcContextDropped = respObj.jsonPath().get("hasEdcContextDropped");

        List<Object> result = new ArrayList<>();
        result.add(leadId);
        result.add(entityType);
        result.add(hasEdcContext);
        result.add(hasEdcContextDropped);

        return result;
    }

}