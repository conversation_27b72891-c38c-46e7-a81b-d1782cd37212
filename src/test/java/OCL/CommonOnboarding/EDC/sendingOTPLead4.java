package OCL.CommonOnboarding.EDC;

import java.util.HashMap;
import java.util.Map;

import com.goldengate.common.RetryAnalyzer;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.sendOTPLead;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class sendingOTPLead4 extends BaseMethod {


	MiddlewareServices middlewareServicesObject = new MiddlewareServices();

	private static final Logger LOGGER = LogManager.getLogger(sendingOTPLead4.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String NewMerchantmobileNo = "5555549829";
//    public static String ExistingMerchantmobileNo = "5555595699";
    public static String ExistingMerchantmobileNo = "5555631843";


    public static String Individualentity = "INDIVIDUAL";
    public static String user_Type = "common_merchant";
    boolean skipOTPTRUE=true;    boolean skipOTPFalse=false;
boolean onlySaveTncFalse=false;  boolean onlySaveTncTrue=true;
public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";
public static String STATUS="SUCCESS";
public static String SUCCESSMESSAGE="Otp sent to phone";


    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";
    public static String displayMessageWithoutsolutionType="Failed to validate OTP (Ref: G-JoWQ-101-500)";
    public static String displayMessageWithoutentity="Failed to validate OTP (Ref: G-JoV_-101-500)";
    public static String displayMessageInvaliduserType="Invalid action (Ref: G-JoWY-101-400)";
    public static String displayMessageInvalidMobileNumber="Please provide a Paytm registered mobile number. (Ref: O-Jobi-101-200)";


    String sToken = AgentSessionToken("8010630022","paytm@123");
//    String sToken = AgentSessionToken("7771216290","paytm@123");

    String existingLead="6527b8d3-c576-4dc8-b386-0d2141ac0704";

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("8010630022", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
		waitForLoad(3000);
	}
    

	@Test(priority = 0, description = "TC_1_sendOTPLeadExistingMerchant", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_sendOTPLeadExistingMerchant() throws Exception {
		establishConnectiontoServer(sToken,5);

		String state=TC_2_returnStatesendOTPLead("5555631843");
//		Assert.assertNotNull(state,"Failure !!!");
		
		
	} 
	
	@Test(priority = 0, description = "TC_2_sendOTPLeadNewMerchant", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_2_sendOTPLeadNewMerchant() throws Exception {
		establishConnectiontoServer(sToken,5);

		String state=TC_2_returnStatesendOTPLead("5555512321");
//		Assert.assertNotNull(state,"Failure !!!");
		
		
	}
	
	@Test(priority = 0, description = "TC_3_sendOTPLeadNewMerchantWithoutVersion", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_3_sendOTPLeadNewMerchantWithoutVersion() throws Exception {
		establishConnectiontoServer(sToken,5);

		sendOTPLead sendOTPLead = new sendOTPLead(P.TESTDATA.get("SendOTPLead"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("solutionType", "merchant_common_onboard");
	queryParam.put("entityType", Individualentity);
	
	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	 Map<Object, Object> body = new HashMap<Object, Object>();
        body.put("mobile", ExistingMerchantmobileNo);
        body.put("userType", user_Type);
        body.put("call", false);
        body.put("tncAdditionalParam", "kyc");
        
        body.put("fullKyc", false);



	Response respObj = middlewareServicesObject.sendOTPLeadPlanMethod(sendOTPLead, queryParam, headers,body);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");
			
//			 Assert.assertEquals(respObj.jsonPath().get("displayMessage"),displayMessage);
//
//		        Assert.assertEquals(respObj.jsonPath().get("message"),message);
//		        Assert.assertEquals(respObj.jsonPath().get("errorCode"),errorCode);


	}
	
	@Test(priority = 0, description = "TC_4_sendOTPLeadNewMerchantWithoutdeviceIdentifier", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_4_sendOTPLeadNewMerchantWithoutdeviceIdentifier() throws Exception {
		establishConnectiontoServer(sToken,5);

		sendOTPLead sendOTPLead = new sendOTPLead(P.TESTDATA.get("SendOTPLead"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("solutionType", "merchant_common_onboard");
	queryParam.put("entityType", Individualentity);
	
	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	headers.put("version", version);


	 Map<Object, Object> body = new HashMap<Object, Object>();
        body.put("mobile", ExistingMerchantmobileNo);
        body.put("userType", user_Type);
        body.put("call", false);
        body.put("tncAdditionalParam", "kyc");
        
        body.put("fullKyc", false);



	Response respObj = middlewareServicesObject.sendOTPLeadPlanMethod(sendOTPLead, queryParam, headers,body);
    int httpcode = respObj.getStatusCode();
//	        Assert.assertTrue(httpcode == 410, "Testcase Failed");
			
	
	}
	
	@Test(priority = 0, description = "TC_005_sendOTPWithoutSessionToken", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_005_sendOTPWithoutSessionToken() throws Exception {
		establishConnectiontoServer(sToken,5);

		sendOTPLead sendOTPLead = new sendOTPLead(P.TESTDATA.get("SendOTPLead"));

		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("solutionType", "merchant_common_onboard");
	queryParam.put("entityType", Individualentity);
	
	headers.put("Content-Type", "application/json");
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	headers.put("version", version);


	 Map<Object, Object> body = new HashMap<Object, Object>();
        body.put("mobile", ExistingMerchantmobileNo);
        body.put("userType", user_Type);
        body.put("call", false);
        body.put("tncAdditionalParam", "kyc");
        
        body.put("fullKyc", false);



	Response respObj = middlewareServicesObject.sendOTPLeadPlanMethod(sendOTPLead, queryParam, headers,body);
    int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 401, "Testcase Failed");

	}

	@Test(priority = 0, description = "sendOTPInvalidMobile", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_006_sendOTPInvalidMobile() throws Exception {
		establishConnectiontoServer(sToken,5);

		sendOTPLead sendOTPLead = new sendOTPLead(P.TESTDATA.get("SendOTPLead"));

		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("solutionType", "merchant_common_onboard");
	queryParam.put("entityType", Individualentity);
	
	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);

	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	headers.put("version", version);


	 Map<Object, Object> body = new HashMap<Object, Object>();
        body.put("mobile", "5522341");
        body.put("userType", user_Type);
        body.put("call", false);
        body.put("tncAdditionalParam", "kyc");
        
        body.put("fullKyc", false);



	Response respObj = middlewareServicesObject.sendOTPLeadPlanMethod(sendOTPLead, queryParam, headers,body);
    int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 400, "Testcase Failed");
        boolean invalidMobile=respObj.jsonPath().get("message").toString().contains("Please provide a Paytm registered mobile number");
//	    Assert.assertEquals(invalidMobile, true);
	    
	}
	
	@Test(priority = 0, description = "sendOTPWithoutUserType", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_007_sendOTPWithoutUserType() throws Exception {
		establishConnectiontoServer(sToken,5);

		sendOTPLead sendOTPLead = new sendOTPLead(P.TESTDATA.get("SendOTPLead"));

		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("solutionType", "merchant_common_onboard");
	queryParam.put("entityType", Individualentity);
	
	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);

	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	headers.put("version", version);


	 Map<Object, Object> body = new HashMap<Object, Object>();
        body.put("mobile", NewMerchantmobileNo);
        body.put("call", false);
        body.put("tncAdditionalParam", "kyc");
        
        body.put("fullKyc", false);



	Response respObj = middlewareServicesObject.sendOTPLeadPlanMethod(sendOTPLead, queryParam, headers,body);
    int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        boolean withoutUserType=respObj.jsonPath().get("displayMessage").toString().contains("Invalid action");
//	    Assert.assertEquals(withoutUserType, true);

       
	}


	
	
	public String TC_2_returnStatesendOTPLead(String mobileNumber) throws Exception {
		establishConnectiontoServer(sToken,5);

		sendOTPLead sendOTPLead = new sendOTPLead(P.TESTDATA.get("SendOTPLead"));
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", Individualentity);
		
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

		 Map<Object, Object> body = new HashMap<Object, Object>();
	        body.put("mobile", mobileNumber);
            body.put("userType", user_Type);
	        body.put("call", false);
	        body.put("tncAdditionalParam", "kyc");
	        
	        body.put("fullKyc", false);



		Response respObj = middlewareServicesObject.sendOTPLeadPlanMethod(sendOTPLead, queryParam, headers,body);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        System.out.println(respObj.jsonPath().get("message").toString());
		return respObj.jsonPath().get("state");


		
		
	} 

}
