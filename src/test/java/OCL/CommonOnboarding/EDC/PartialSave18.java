package OCL.CommonOnboarding.EDC;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.goldengate.common.RetryAnalyzer;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.goldengate.common.BaseMethod;
import com.google.gson.JsonArray;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.addBank;
import Request.CommonOnboardingEDC.partialSaveCall;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import io.restassured.RestAssured;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;

public class PartialSave18 extends BaseMethod {
	
	MiddlewareServices middlewareServicesObject = new MiddlewareServices();
	CreateMCOLead2 mcoLead=new CreateMCOLead2();
	AddBank8 objbank=new AddBank8();
	private static final Logger LOGGER = LogManager.getLogger(AddBank8.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String NewMerchantmobileNo = "**********";
    public static String ExistingMerchantmobileNo = "**********";
public static String bankIfsc="ICIC0001070";
public static String bankName="ICICI BANK LIMITED";

    public static String Individualentity = "INDIVIDUAL";
    public static String user_Type = "common_merchant";
    boolean skipOTPTRUE=true;    boolean skipOTPFalse=false;
boolean onlySaveTncFalse=false;  boolean onlySaveTncTrue=true;
public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";

public static String expectedifscmessage="IFSC cannot be empty (Ref: G-J2MM-155-400)";
public static String expectedACCOUNTmessage="Failed to update merchant lead (Ref: G-J2MZ-155-500))";
public static String sameBankDetails="This bank account is already registered with us. Please enter different bank details. (Ref: G-J2Mb-155-400))";

    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";
    public static String displayMessageWithoutsolutionType="Failed to validate OTP (Ref: G-JoWQ-101-500)";
    public static String displayMessageWithoutentity="Failed to validate OTP (Ref: G-JoV_-101-500)";
    public static String displayMessageInvaliduserType="Invalid action (Ref: G-JoWY-101-400)";
    public static String displayMessageInvalidMobileNumber="Mobile number not associated to your account. Kindly update through 'Need Help section on Login screen of Paytm App/Web' to proceed further (Ref: E-JoWe-101-400)";


    String sToken = AgentSessionToken("**********","paytm@123");
//    String sToken = AgentSessionToken("**********","paytm@123");

//    String sToken = "4d97b959-9746-4633-8136-a39586555600";

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("**********", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
		waitForLoad(3000);
	}

	@Test(priority = 0, description = "TC_1_PartialSave", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_PartialSave() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> obj=objbank.TC_5_addBank("**********");
		
		String custId=obj.get(0);
		String leadId=obj.get(1);
		String entityType=obj.get(2);
		

		partialSaveCall partialSaveCallobject = new partialSaveCall(P.TESTDATA.get("PartialSaveCalling"),custId);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
			Map<String, Object> mybody = new HashMap<String, Object>();
			Map<String, Object> mybody1 = new HashMap<String, Object>();


		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", entityType);
		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		
		
	        
//		 Map<String, Object> bodynew = new HashMap<>();
		
//		Map<String, Object> bodynew = new LinkedHashMap<String, Object>();
//		JSONObject requestBody = new JSONObject("[{\"questionAlias\":\"cardPaymentsPerMonth\",\"answerAlias\":\"15 - 30 Lakh\"},{\"questionAlias\":\"numberOfEmployees\",\"answerAlias\":\"Upto 5\"},{\"questionAlias\":\"shopRentedOrOwned\",\"answerAlias\":\"Shop Rented\"}]");

	       String questionList="["
	       		+ "{\\\"questionAlias\\\":\\\"cardPaymentsPerMonth\\\","
	       		+ "\\\"answerAlias\\\":\\\"15 - 30 Lakh\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"numberOfEmployees\\\","
	       		+ "\\\"answerAlias\\\":\\\"Upto 5\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"shopRentedOrOwned\\\","
	       		+ "\\\"answerAlias\\\":\\\"Shop Rented\\\"}]";
	       
//       mybody1.put("questionAlias", "cardPaymentsPerMonth");
//	       Object maybody2=mybody1.put("answerAlias", "15 - 30 Lakh");
//	       Object mybody3=mybody1.put("questionAlias", "numberOfEmployees");
//	       Object mybody4=mybody1.put("answerAlias", "Upto 5");
//	       Object mybody5=mybody1.put("questionAlias", "shopRentedOrOwned");
//	       Object mybody6=mybody1.put("answerAlias", "Shop Rented");
//           ArrayList<Object> objnew=new ArrayList<Object>();
//           objnew.add(mybody1);         
//           objnew.add(maybody2);           
//           objnew.add(mybody3);
//           objnew.add(mybody4);
//           objnew.add(mybody5);
//           objnew.add(mybody6);


	       
//	       JsonPath js=new JsonPath("[{\"questionAlias\":\"cardPaymentsPerMonth\",\"answerAlias\":\"15 - 30 Lakh\"},{\"questionAlias\":\"numberOfEmployees\",\"answerAlias\":\"Upto 5\"},{\"questionAlias\":\"shopRentedOrOwned\",\"answerAlias\":\"Shop Rented\"}]");
		
//	       JsonPath.from(questionList);
	       mybody.put("syncChildLeads", true);
			mybody.put("partialSave", true);
			mybody.put("onlyValidateBankDetails", false);
			mybody.put("NUMBER_OF_EDC_MACHINES_REQUIRED", "Single");
			mybody.put("SMALL_MERCHANT_DECLARATION", "false");
			mybody.put("QUESTIONS_LIST", questionList);



			




			
//	        mybody.put("pennyDropDetails", bodynew);
			


		Response respObj = middlewareServicesObject.partialSaving(partialSaveCallobject,queryParam,headers,mybody);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
		
		
		
	} 
	




    
	
	

	
	@Test(priority = 0, description = "TC_2_PartialSaveWithoutSessiontoken", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_2_PartialSaveWithoutSessiontoken() throws Exception {

		establishConnectiontoServer(sToken,5);

		List<String> obj=objbank.TC_5_addBank("**********");
		
		String custId=obj.get(0);
		String leadId=obj.get(1);
		String entityType=obj.get(2);
		

		partialSaveCall partialSaveCallobject = new partialSaveCall(P.TESTDATA.get("PartialSaveCalling"),custId);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
			Map<String, Object> mybody = new HashMap<String, Object>();
			Map<String, Object> mybody1 = new HashMap<String, Object>();


		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", entityType);
		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
//		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		
		
	        
	       String questionList="["
	       		+ "{\\\"questionAlias\\\":\\\"cardPaymentsPerMonth\\\","
	       		+ "\\\"answerAlias\\\":\\\"15 - 30 Lakh\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"numberOfEmployees\\\","
	       		+ "\\\"answerAlias\\\":\\\"Upto 5\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"shopRentedOrOwned\\\","
	       		+ "\\\"answerAlias\\\":\\\"Shop Rented\\\"}]";
	       
	       mybody.put("syncChildLeads", true);
			mybody.put("partialSave", true);
			mybody.put("onlyValidateBankDetails", false);
			mybody.put("NUMBER_OF_EDC_MACHINES_REQUIRED", "Single");
			mybody.put("SMALL_MERCHANT_DECLARATION", "false");
			mybody.put("QUESTIONS_LIST", questionList);



			Response respObj = middlewareServicesObject.partialSaving(partialSaveCallobject,queryParam,headers,mybody);
	        int httpcode = respObj.getStatusCode();
//	        Assert.assertTrue(httpcode == 401, "Testcase Failed");
	
		
	} 


	@Test(priority = 0, description = "TC_3_partialSaveWithoutDeviceIdentifier", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_3_partialSaveWithoutDeviceIdentifier() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> obj=objbank.TC_5_addBank("**********");
		
		String custId=obj.get(0);
		String leadId=obj.get(1);
		String entityType=obj.get(2);
		

		partialSaveCall partialSaveCallobject = new partialSaveCall(P.TESTDATA.get("PartialSaveCalling"),custId);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
			Map<String, Object> mybody = new HashMap<String, Object>();
			Map<String, Object> mybody1 = new HashMap<String, Object>();


		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", entityType);
		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
//		headers.put("deviceidentifier", deviceIdentifer);
//		headers.put("deviceidentifier", deviceIdentifer);

		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		
		
	        
	       String questionList="["
	       		+ "{\\\"questionAlias\\\":\\\"cardPaymentsPerMonth\\\","
	       		+ "\\\"answerAlias\\\":\\\"15 - 30 Lakh\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"numberOfEmployees\\\","
	       		+ "\\\"answerAlias\\\":\\\"Upto 5\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"shopRentedOrOwned\\\","
	       		+ "\\\"answerAlias\\\":\\\"Shop Rented\\\"}]";
	       
	       mybody.put("syncChildLeads", true);
			mybody.put("partialSave", true);
			mybody.put("onlyValidateBankDetails", false);
			mybody.put("NUMBER_OF_EDC_MACHINES_REQUIRED", "Single");
			mybody.put("SMALL_MERCHANT_DECLARATION", "false");
			mybody.put("QUESTIONS_LIST", questionList);



			Response respObj = middlewareServicesObject.partialSaving(partialSaveCallobject,queryParam,headers,mybody);
	        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 410, "Testcase Failed");
	
		
	} 
	

	@Test(priority = 0, description = "TC_4_partialSaveWithoutVersion", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_4_partialSaveWithoutVersion() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> obj=objbank.TC_5_addBank("**********");
		
		String custId=obj.get(0);
		String leadId=obj.get(1);
		String entityType=obj.get(2);
		

		partialSaveCall partialSaveCallobject = new partialSaveCall(P.TESTDATA.get("PartialSaveCalling"),custId);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
			Map<String, Object> mybody = new HashMap<String, Object>();
			Map<String, Object> mybody1 = new HashMap<String, Object>();


		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", entityType);
		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
//		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		
		
	        
	       String questionList="["
	       		+ "{\\\"questionAlias\\\":\\\"cardPaymentsPerMonth\\\","
	       		+ "\\\"answerAlias\\\":\\\"15 - 30 Lakh\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"numberOfEmployees\\\","
	       		+ "\\\"answerAlias\\\":\\\"Upto 5\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"shopRentedOrOwned\\\","
	       		+ "\\\"answerAlias\\\":\\\"Shop Rented\\\"}]";
	       
	       mybody.put("syncChildLeads", true);
			mybody.put("partialSave", true);
			mybody.put("onlyValidateBankDetails", false);
			mybody.put("NUMBER_OF_EDC_MACHINES_REQUIRED", "Single");
			mybody.put("SMALL_MERCHANT_DECLARATION", "false");
			mybody.put("QUESTIONS_LIST", questionList);



			Response respObj = middlewareServicesObject.partialSaving(partialSaveCallobject,queryParam,headers,mybody);
	        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	
		
	}
	
	

	@Test(priority = 0, description = "TC_5_partialSaveWithoutleadID", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_5_partialSaveWithoutleadID() throws Exception {
			establishConnectiontoServer(sToken,5);

			List<String> obj=objbank.TC_5_addBank("**********");
				
				String custId=obj.get(0);
				String leadId=obj.get(1);
				String entityType=obj.get(2);
				

				partialSaveCall partialSaveCallobject = new partialSaveCall(P.TESTDATA.get("PartialSaveCalling"),custId);
					Map<String, String> queryParam = new HashMap<String, String>();
					Map<String, String> headers = new HashMap<String, String>();
					Map<String, Object> mybody = new HashMap<String, Object>();
					Map<String, Object> mybody1 = new HashMap<String, Object>();


				queryParam.put("solutionType", "merchant_common_onboard");
				queryParam.put("entityType", entityType);
//				queryParam.put("leadId", leadId);

				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
				headers.put("deviceidentifier", deviceIdentifer);
				headers.put("version", version);
				headers.put("UncleScrooge", xmwChecksumBypassValue);
				
				
			        
			       String questionList="["
			       		+ "{\\\"questionAlias\\\":\\\"cardPaymentsPerMonth\\\","
			       		+ "\\\"answerAlias\\\":\\\"15 - 30 Lakh\\\"},"
			       		+ "{\\\"questionAlias\\\":\\\"numberOfEmployees\\\","
			       		+ "\\\"answerAlias\\\":\\\"Upto 5\\\"},"
			       		+ "{\\\"questionAlias\\\":\\\"shopRentedOrOwned\\\","
			       		+ "\\\"answerAlias\\\":\\\"Shop Rented\\\"}]";
			       
			       mybody.put("syncChildLeads", true);
					mybody.put("partialSave", true);
					mybody.put("onlyValidateBankDetails", false);
					mybody.put("NUMBER_OF_EDC_MACHINES_REQUIRED", "Single");
					mybody.put("SMALL_MERCHANT_DECLARATION", "false");
					mybody.put("QUESTIONS_LIST", questionList);



					Response respObj = middlewareServicesObject.partialSaving(partialSaveCallobject,queryParam,headers,mybody);
			        int httpcode = respObj.getStatusCode();
//		        Assert.assertTrue(httpcode == 500, "Testcase Failed");
		
	} 
	


	@Test(priority = 0, description = "TC_6_partialSaveWithoutentity", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_6_partialSaveWithoutentity() throws Exception {
			establishConnectiontoServer(sToken,5);

			List<String> obj=objbank.TC_5_addBank("**********");
				
				String custId=obj.get(0);
				String leadId=obj.get(1);
				String entityType=obj.get(2);
				

				partialSaveCall partialSaveCallobject = new partialSaveCall(P.TESTDATA.get("PartialSaveCalling"),custId);
					Map<String, String> queryParam = new HashMap<String, String>();
					Map<String, String> headers = new HashMap<String, String>();
					Map<String, Object> mybody = new HashMap<String, Object>();
					Map<String, Object> mybody1 = new HashMap<String, Object>();


				queryParam.put("solutionType", "merchant_common_onboard");
//				queryParam.put("entityType", entityType);
				queryParam.put("leadId", leadId);

				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
				headers.put("deviceidentifier", deviceIdentifer);
				headers.put("version", version);
				headers.put("UncleScrooge", xmwChecksumBypassValue);
				
				
			        
			       String questionList="["
			       		+ "{\\\"questionAlias\\\":\\\"cardPaymentsPerMonth\\\","
			       		+ "\\\"answerAlias\\\":\\\"15 - 30 Lakh\\\"},"
			       		+ "{\\\"questionAlias\\\":\\\"numberOfEmployees\\\","
			       		+ "\\\"answerAlias\\\":\\\"Upto 5\\\"},"
			       		+ "{\\\"questionAlias\\\":\\\"shopRentedOrOwned\\\","
			       		+ "\\\"answerAlias\\\":\\\"Shop Rented\\\"}]";
			       
			       mybody.put("syncChildLeads", true);
					mybody.put("partialSave", true);
					mybody.put("onlyValidateBankDetails", false);
					mybody.put("NUMBER_OF_EDC_MACHINES_REQUIRED", "Single");
					mybody.put("SMALL_MERCHANT_DECLARATION", "false");
					mybody.put("QUESTIONS_LIST", questionList);



					Response respObj = middlewareServicesObject.partialSaving(partialSaveCallobject,queryParam,headers,mybody);
			        int httpcode = respObj.getStatusCode();
//		        Assert.assertTrue(httpcode == 400, "Testcase Failed");
		
	} 
	

	@Test(priority = 0, description = "TC_7_partialSaveWithoutsolutionType", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_7_partialSaveWithoutsolutionType() throws Exception {
			establishConnectiontoServer(sToken,5);

			List<String> obj=objbank.TC_5_addBank("**********");
				
				String custId=obj.get(0);
				String leadId=obj.get(1);
				String entityType=obj.get(2);
				

				partialSaveCall partialSaveCallobject = new partialSaveCall(P.TESTDATA.get("PartialSaveCalling"),custId);
					Map<String, String> queryParam = new HashMap<String, String>();
					Map<String, String> headers = new HashMap<String, String>();
					Map<String, Object> mybody = new HashMap<String, Object>();
					Map<String, Object> mybody1 = new HashMap<String, Object>();


//				queryParam.put("solutionType", "merchant_common_onboard");
				queryParam.put("entityType", entityType);
				queryParam.put("leadId", leadId);

				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
				headers.put("deviceidentifier", deviceIdentifer);
				headers.put("version", version);
				headers.put("UncleScrooge", xmwChecksumBypassValue);
				
				
			        
			       String questionList="["
			       		+ "{\\\"questionAlias\\\":\\\"cardPaymentsPerMonth\\\","
			       		+ "\\\"answerAlias\\\":\\\"15 - 30 Lakh\\\"},"
			       		+ "{\\\"questionAlias\\\":\\\"numberOfEmployees\\\","
			       		+ "\\\"answerAlias\\\":\\\"Upto 5\\\"},"
			       		+ "{\\\"questionAlias\\\":\\\"shopRentedOrOwned\\\","
			       		+ "\\\"answerAlias\\\":\\\"Shop Rented\\\"}]";
			       
			       mybody.put("syncChildLeads", true);
					mybody.put("partialSave", true);
					mybody.put("onlyValidateBankDetails", false);
					mybody.put("NUMBER_OF_EDC_MACHINES_REQUIRED", "Single");
					mybody.put("SMALL_MERCHANT_DECLARATION", "false");
					mybody.put("QUESTIONS_LIST", questionList);



					Response respObj = middlewareServicesObject.partialSaving(partialSaveCallobject,queryParam,headers,mybody);
			        int httpcode = respObj.getStatusCode();
//		        Assert.assertTrue(httpcode == 400, "Testcase Failed");
		
	} 

	
	
}

