package OCL.CommonOnboarding.EDC;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.goldengate.common.RetryAnalyzer;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.getBank;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class GetMerchantBank7 extends BaseMethod {
	
	MiddlewareServices middlewareServicesObject = new MiddlewareServices();
	CreateMCOLead2 mcoLead=new  CreateMCOLead2();
	sendingOTPLead4 otplead=new  sendingOTPLead4();

	private static final Logger LOGGER = LogManager.getLogger(GettingMerchantBusiness6.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String NewMerchantmobileNo = "**********";
//    public static String ExistingMerchantmobileNo = "**********";
    public static String ExistingMerchantmobileNo = "**********";

    public static String Individualentity = "INDIVIDUAL";
    public static String user_Type = "common_merchant";
    boolean skipOTPTRUE=true;    boolean skipOTPFalse=false;
boolean onlySaveTncFalse=false;  boolean onlySaveTncTrue=true;
public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";
public static String STATUS="SUCCESS";
public static String SUCCESSMESSAGE="Otp sent to phone";


    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";
    public static String displayMessageWithoutsolutionType="Failed to validate OTP (Ref: G-JoWQ-101-500)";
    public static String displayMessageWithoutentity="Failed to validate OTP (Ref: G-JoV_-101-500)";
    public static String displayMessageInvaliduserType="Invalid action (Ref: G-JoWY-101-400)";
    public static String displayMessageInvalidMobileNumber="Please provide a Paytm registered mobile number. (Ref: O-Jobi-101-200)";
    public static String displayMessageWithoutLeadID="Please provide a Paytm registered mobile number. (Ref: O-Jobi-101-200)";


//    String sToken = AgentSessionToken("**********","paytm@123");
    String sToken = AgentSessionToken("**********","paytm@123");

    String existingLead="6527b8d3-c576-4dc8-b386-0d2141ac0704";

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("**********", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
		waitForLoad(3000);
	}

//	@Test(priority = 0, description = "TC_1_getExistingLeadBankDetails", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC_1_getExistingLeadBankDetailsUPIFalse() {
//
//		getBankDetailsUpiFetchFalse("4652b488-ea95-4808-b01e-48aa5ab21493","**********");
////	    String bankName=bankDetails.get(0);
////		String bankAccoutnumber=bankDetails.get(1);
////		String ifsc=bankDetails.get(2);
////		String bankAccountHolderName=bankDetails.get(3);
////		String upiAccountId=bankDetails.get(4);
////		String accountType=bankDetails.get(5);
////
////
////		System.out.println("BankName :"+bankName);
////		System.out.println("BankAccountnumber :"+bankAccoutnumber);
////		System.out.println("IFSC :"+ifsc);
////		System.out.println("Account Holder Name :"+bankAccountHolderName);
////		System.out.println("upiAccountId :"+upiAccountId);
////
////		System.out.println("accountType :"+accountType);
//
//
//	}
	
// upi api decommissioned, bank test case no longer valid
	
//	@Test(priority = 0, description = "TC_2_getExistingLeadBankDetailsUPITRUE", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC_2_getExistingLeadBankDetailsUPITRUE() {
//				
//		List<String> bankDetails=getBankDetailsUpiFetchTrue("558a5d9f-4b1b-4841-b303-5c9108bf35c1","**********");
//	    String bankName=bankDetails.get(0);
//		String bankAccoutnumber=bankDetails.get(1);
//		String ifsc=bankDetails.get(2);
//		String bankAccountHolderName=bankDetails.get(3);
//		String branchName=bankDetails.get(4);
//		String accountType=bankDetails.get(5);
//		String OtherbankName=bankDetails.get(6);
//		String OtherbankAccoutnumber=bankDetails.get(7);
//		String Otherifsc=bankDetails.get(8);
//		String OtherbankAccountHolderName=bankDetails.get(9);
//		String OtherupiAccountId=bankDetails.get(10);
//		String OtheraccountType=bankDetails.get(11);
//		System.out.println("BankName :"+bankName);
//		System.out.println("BankAccountnumber :"+bankAccoutnumber);
//		System.out.println("IFSC :"+ifsc);
//		System.out.println("Account Holder Name :"+bankAccountHolderName);
//		System.out.println("branchName :"+branchName);
//		
//		System.out.println("accountType :"+accountType);
//		System.out.println("OtherbankName :"+OtherbankName);
//		System.out.println("OtherbankAccoutnumber :"+OtherbankAccoutnumber);
//		System.out.println("Otherifsc :"+Otherifsc);
//		System.out.println("OtherbankAccountHolderName :"+OtherbankAccountHolderName);
//		System.out.println("OtherupiAccountId :"+OtherupiAccountId);
//		
//		System.out.println("OtheraccountType :"+OtheraccountType);
//
//
//	} 
//	
	
	
	@Test(priority = 0, description = "TC_3_GetBankWithUPIFetchTrue", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_3_GetBankWithUPIFetchTrue() throws Exception {
		establishConnectiontoServer(sToken,5);


		getBank getBank=new getBank(P.TESTDATA.get("GetBank"),"**********");
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("leadId", "4652b488-ea95-4808-b01e-48aa5ab21493");
	queryParam.put("upiFetchOnly", "true");

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getBankPlanMethod(getBank, queryParam, headers);
	
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");
	} 
	
	@Test(priority = 0, description = "TC_4_GetBankErrorWithoutSessionToken", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_4_GetBankErrorWithoutSessionToken() throws Exception {
		establishConnectiontoServer(sToken,5);

		getBank getBank=new getBank(P.TESTDATA.get("GetBank"),"**********");
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("leadId", "4652b488-ea95-4808-b01e-48aa5ab21493");
	queryParam.put("upiFetchOnly", "false");

	headers.put("Content-Type", "application/json");
//	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getBankPlanMethod(getBank, queryParam, headers);
	
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 401, "Testcase Failed");
	} 
	
	@Test(priority = 0, description = "TC_5_GetBankErrorWithoutVersion", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_5_GetBankErrorWithoutVersion() throws Exception {
		establishConnectiontoServer(sToken,5);

		getBank getBank=new getBank(P.TESTDATA.get("GetBank"),"**********");
	
	Map<String, String> queryParam = new HashMap<String, String>();
	Map<String, String> headers = new HashMap<String, String>();
queryParam.put("leadId", "4652b488-ea95-4808-b01e-48aa5ab21493");
queryParam.put("upiFetchOnly", "false");

headers.put("Content-Type", "application/json");
headers.put("session_token", sToken);
headers.put("deviceidentifier", deviceIdentifer);
//headers.put("version", version);
headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

Response respObj = middlewareServicesObject.getBankPlanMethod(getBank, queryParam, headers);

int httpcode = respObj.getStatusCode();
		
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");
    String actualdisplaymessage=respObj.jsonPath().get("displayMessage");
//    Assert.assertEquals(actualdisplaymessage,displayMessage);
    String actualmessage=respObj.jsonPath().get("message");
//    Assert.assertEquals(actualmessage,message);
    String actualerrorCode=respObj.jsonPath().get("errorCode");
//    Assert.assertEquals(actualerrorCode,errorCode);
    
	} 
	

	@Test(priority = 0, description = "TC_6_GetBankErrorWithoutDeviceIdentifer", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_6_GetBankErrorWithoutDeviceIdentifer() throws Exception {
		establishConnectiontoServer(sToken,5);


		getBank getBank=new getBank(P.TESTDATA.get("GetBank"),"**********");
	
	Map<String, String> queryParam = new HashMap<String, String>();
	Map<String, String> headers = new HashMap<String, String>();
queryParam.put("leadId", "4652b488-ea95-4808-b01e-48aa5ab21493");
queryParam.put("upiFetchOnly", "false");

headers.put("Content-Type", "application/json");
headers.put("session_token", sToken);
//headers.put("deviceidentifier", deviceIdentifer);
headers.put("version", version);
headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

Response respObj = middlewareServicesObject.getBankPlanMethod(getBank, queryParam, headers);

int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 410, "Testcase Failed");
    
	} 
	
	
    
	public List<String> getBankDetailsUpiFetchFalse(String LeadID,String custId) throws Exception {
		establishConnectiontoServer(sToken,5);

		getBank getBank=new getBank(P.TESTDATA.get("GetBank"),custId);
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("leadId", LeadID);
	queryParam.put("upiFetchOnly", "false");

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getBankPlanMethod(getBank, queryParam, headers);
	List<String> ltnew=new ArrayList<String>();
	ltnew.add(respObj.jsonPath().get("bankDetails[0].bankName").toString());
	ltnew.add(respObj.jsonPath().get("bankDetails[0].bankAccountNumber").toString());
	ltnew.add(respObj.jsonPath().get("bankDetails[0].ifsc").toString());
	ltnew.add(respObj.jsonPath().get("bankDetails[0].bankAccountHolderName").toString());
	ltnew.add(respObj.jsonPath().get("bankDetails[0].branchName").toString());
	ltnew.add(respObj.jsonPath().get("bankDetails[0].accountType").toString());

	return ltnew;
	}

	public List<String> getBankDetailsUpiFetchTrue(String LeadID,String custId) throws Exception {
		establishConnectiontoServer(sToken,5);

		getBank getBank=new getBank(P.TESTDATA.get("GetBank"),custId);
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("leadId", LeadID);
	queryParam.put("upiFetchOnly", "true");

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getBankPlanMethod(getBank, queryParam, headers);
	List<String> ltnew=new ArrayList<String>();
	ltnew.add(respObj.jsonPath().get("bankDetails[0].bankName").toString());
	ltnew.add(respObj.jsonPath().get("bankDetails[0].bankAccountNumber").toString());
	ltnew.add(respObj.jsonPath().get("bankDetails[0].ifsc").toString());
	ltnew.add(respObj.jsonPath().get("bankDetails[0].bankAccountHolderName").toString());
	ltnew.add(respObj.jsonPath().get("bankDetails[0].upiAccountId").toString());
	ltnew.add(respObj.jsonPath().get("bankDetails[0].accountType").toString());

	ltnew.add(respObj.jsonPath().get("bankDetails[1].bankName").toString());
	ltnew.add(respObj.jsonPath().get("bankDetails[1].bankAccountNumber").toString());
	ltnew.add(respObj.jsonPath().get("bankDetails[1].ifsc").toString());
	ltnew.add(respObj.jsonPath().get("bankDetails[1].bankAccountHolderName").toString());
	ltnew.add(respObj.jsonPath().get("bankDetails[1].upiAccountId").toString());
	ltnew.add(respObj.jsonPath().get("bankDetails[1].accountType").toString());

	
	return ltnew;
	}
	

}

