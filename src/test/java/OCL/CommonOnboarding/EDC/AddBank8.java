package OCL.CommonOnboarding.EDC;

import java.util.*;

import com.goldengate.common.RetryAnalyzer;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.addBank;
import Request.CommonOnboardingEDC.fetchBank;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import io.restassured.response.Response;

public class AddBank8 extends BaseMethod {
	
	MiddlewareServices middlewareServicesObject = new MiddlewareServices();
	CreateMCOLead2 mcoLead=new CreateMCOLead2();
	private static final Logger LOGGER = LogManager.getLogger(AddBank8.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String NewMerchantmobileNo = "**********";
    public static String ExistingMerchantmobileNo = "**********";
public static String bankIfsc="ICIC0001070";
public static String bankName="ICICI BANK LIMITED";

    public static String Individualentity = "INDIVIDUAL";
    public static String user_Type = "common_merchant";
    boolean skipOTPTRUE=true;    boolean skipOTPFalse=false;
boolean onlySaveTncFalse=false;  boolean onlySaveTncTrue=true;
public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";

public static String expectedifscmessage="IFSC cannot be empty (Ref: G-J2MM-155-400)";
public static String expectedACCOUNTmessage="Failed to update merchant lead (Ref: G-J2MZ-155-500))";
public static String sameBankDetails="This bank account is already registered with us. Please enter different bank details. (Ref: G-J2Mb-155-400))";

    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";
    public static String displayMessageWithoutsolutionType="Failed to validate OTP (Ref: G-JoWQ-101-500)";
    public static String displayMessageWithoutentity="Failed to validate OTP (Ref: G-JoV_-101-500)";
    public static String displayMessageInvaliduserType="Invalid action (Ref: G-JoWY-101-400)";
    public static String displayMessageInvalidMobileNumber="Mobile number not associated to your account. Kindly update through 'Need Help section on Login screen of Paytm App/Web' to proceed further (Ref: E-JoWe-101-400)";


    String sToken = AgentSessionToken("**********","paytm@123");
//    String sToken = AgentSessionToken("**********","paytm@123");

//    String sToken = "4d97b959-9746-4633-8136-a39586555600";

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("**********", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
		waitForLoad(3000);
	}

	@Test(priority = 0, description = "TC_1_addBank", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_CreatingLeadAndAddingBankDetails() throws Exception {
		establishConnectiontoServer(sToken,5);

		TC_5_addBank("**********");	
		
	} 
	

	@Test(priority = 0, description = "TC_2_addBank", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_2_ContinuewithExistingLeadAndAddingBankDetails() throws Exception {
		establishConnectiontoServer(sToken,5);

		TC_5_addBank("**********");	
		
	}


	public static String generateUpdatedAadharNumber() {
		// Generate a 5-digit random number
		Random random = new Random();
		int randomFiveDigitNumber = 10000 + random.nextInt(90000); // Ensures it's a 5-digit number

		// Original Aadhar number
		String aadharNumber = "*************";

		// Replace the last 5 digits with the generated random number
		String newAadharNumber = aadharNumber.substring(0, aadharNumber.length() - 5) + randomFiveDigitNumber;

		// Use the new Aadhar number in your map (optional)
		Map<String, String> mybodyadharnew = new HashMap<>();
		mybodyadharnew.put("aadharNumber", newAadharNumber);

		// Return the new Aadhar number
		return newAadharNumber;
	}

	



    
	
	@Test(priority = 0, description = "TC_5_addBank", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public List<String> TC_5_addBank(String newcustaMobileno) throws Exception {
		establishConnectiontoServer(sToken,5);

		mcoLead.TC_1_createNewMCOLeadValidateOTP(newcustaMobileno);
		mcoLead.TC_12_createNewMCOLeadValidateOTPEDCContext(newcustaMobileno);
		List<Object> lt=mcoLead.TC_17_validateOTPConfirm(newcustaMobileno);	
		String custId=lt.get(0).toString();
		String leadId=lt.get(1).toString();
		String entityType=lt.get(2).toString();
		
				
	
		addBank addbankMerchant = new addBank(P.TESTDATA.get("AddBanking"),custId);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
			Map<String, Object> mybody = new LinkedHashMap<String, Object>();



		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", Individualentity);
		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		
//		requestParams.put("pennyDropDetails", anotherObject);

		
		
//		Map<String, Object> mybody = new HashMap<String, Object>();
		mybody.put("onlyValidateBankDetails", false);
		mybody.put("skipPennyDrop", false);
		mybody.put("partialSave", true);
	        
//		 Map<String, Object> bodynew = new HashMap<>();
		
//		Map<String, Object> bodynew = new LinkedHashMap<String, Object>();
	        long randomno=Utilities.generateRandom(10);
			String bankAccountno=String.valueOf(randomno);
			System.out.println("Bank account no is :"+bankAccountno);
			mybody.put("bankAccountNumber", bankAccountno);
			mybody.put("ifsc", bankIfsc);
			mybody.put("bankName", bankName);
//	        mybody.put("pennyDropDetails", bodynew);
			Response respObj = middlewareServicesObject.addBankPlanmethod(addbankMerchant,queryParam,headers,mybody);
	        int httpcode = respObj.getStatusCode();
//	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
			
			addBank addbankMerchant1 = new addBank(P.TESTDATA.get("AddBanking"),custId);

			Map<String, Object> mybodyNew = new LinkedHashMap<String, Object>();

			mybodyNew.put("onlyValidateBankDetails", false);
			mybodyNew.put("skipPennyDrop", false);
			mybodyNew.put("partialSave", true);
			mybodyNew.put("bankAccountNumber", bankAccountno);
			mybodyNew.put("ifsc", bankIfsc);
			mybodyNew.put("bankName", bankName);
			
	
		Response respObj1 = middlewareServicesObject.addBankPlanmethod(addbankMerchant1,queryParam,headers,mybodyNew);

        String relatedBusinessUuid=respObj1.jsonPath().get("relatedBusinessUuid");
        System.out.println(relatedBusinessUuid);

    	
		addBank addbankMerchantNew = new addBank(P.TESTDATA.get("AddBankingBeauru"),custId);
			Map<String, String> queryParam1 = new HashMap<String, String>();
			Map<String, String> headers1 = new HashMap<String, String>();
			Map<String, Object> mybody1 = new LinkedHashMap<String, Object>();



			queryParam1.put("solutionType", "merchant_common_onboard");
			queryParam1.put("entityType", Individualentity);
			queryParam1.put("leadId", leadId);

			headers1.put("Content-Type", "application/json");
			headers1.put("session_token", sToken);
			headers1.put("deviceidentifier", deviceIdentifer);
			headers1.put("version", version);
			headers1.put("UncleScrooge", xmwChecksumBypassValue);
        
        
        mybody1.put("onlyValidateBankDetails", false);
		mybody1.put("skipPennyDrop", false);
		mybody1.put("partialSave", true);
		mybody1.put("bankAccountNumber", bankAccountno);
		mybody1.put("ifsc", bankIfsc);
		mybody1.put("bankName", bankName);
		mybody1.put("MERCHANT_DETAILS_CONFIRMED_BUREAU", "true");
		mybody1.put("BUSINESS_ENTITY", "PROPRIETORSHIP");
		mybody1.put("relatedBusinessUuid", relatedBusinessUuid);
		Response respObjNew = middlewareServicesObject.addBankPlanmethod(addbankMerchantNew,queryParam1,headers1,mybody1);

		   int httpcode1 = respObjNew.getStatusCode();
//	        Assert.assertTrue(httpcode1 == 200, "Testcase Failed");
	        String entityTypeNew =respObjNew.jsonPath().get("entityType");
	        

			addBank addbankMerchantNewAadhar = new addBank(P.TESTDATA.get("AddBankingBeauruAadhar"),custId);
				Map<String, String> queryParamNew = new HashMap<String, String>();
				Map<String, String> headersNew = new HashMap<String, String>();
				Map<String, Object> mybodyadharnew = new LinkedHashMap<String, Object>();



				queryParamNew.put("solutionType", "merchant_common_onboard");
				queryParamNew.put("entityType", Individualentity);
				queryParamNew.put("leadId", leadId);

				headersNew.put("Content-Type", "application/json");
				headersNew.put("session_token", sToken);
				headersNew.put("deviceidentifier", deviceIdentifer);
				headersNew.put("version", version);
				headersNew.put("UncleScrooge", xmwChecksumBypassValue);
	        
	        
				mybodyadharnew.put("onlyValidateBankDetails", false);
				mybodyadharnew.put("skipPennyDrop", false);
				mybodyadharnew.put("partialSave", true);
				mybodyadharnew.put("bankAccountNumber", bankAccountno);
				mybodyadharnew.put("ifsc", bankIfsc);
				mybodyadharnew.put("bankName", bankName);
				mybodyadharnew.put("BUSINESS_ENTITY", "PROPRIETORSHIP");
				mybodyadharnew.put("BUSINESS_TYPE", "PROPRIETORSHIP");
				mybodyadharnew.put("NAME_AS_PER_AADHAR", "ARVIND KUMAR");
				mybodyadharnew.put("MERCHANT_DOB", "08/11/1963");
				mybodyadharnew.put("GENDER", "male");
				mybodyadharnew.put("AADHAR_NO_NOT_READABLE", "true");
//				mybodyadharnew.put("aadharNumber", "*************");
		String aadharNumber=generateUpdatedAadharNumber();
		mybodyadharnew.put("aadharNumber", aadharNumber);

		mybodyadharnew.put("relatedBusinessUuid", relatedBusinessUuid);
			Response respObjNewAadhar = middlewareServicesObject.addBankPlanmethod(addbankMerchantNewAadhar,queryParamNew,headersNew,mybodyadharnew);

			   int httpcode2 = respObjNewAadhar.getStatusCode();
//		        Assert.assertTrue(httpcode2 == 200, "Testcase Failed");
	        
		        fetchBank fetchBankobj = new fetchBank(P.TESTDATA.get("FetchBankReq"),custId);
		        Map<String, String> queryparamfetchBank = new HashMap<String, String>();
				Map<String, String> headersfetchBank = new HashMap<String, String>();


				queryparamfetchBank.put("upiFetchOnly", "false");
				queryparamfetchBank.put("leadId", leadId);

				headersfetchBank.put("Content-Type", "application/json");
				headersfetchBank.put("session_token", sToken);
				headersfetchBank.put("deviceidentifier", deviceIdentifer);
				headersfetchBank.put("version", version);
				headersfetchBank.put("UncleScrooge", xmwChecksumBypassValue);
	        
				Response respObjNewPAN = middlewareServicesObject.fetchBankPlanmethod(fetchBankobj,queryparamfetchBank,headersfetchBank);
				   int httpcode3 = respObjNewPAN.getStatusCode();
//			        Assert.assertTrue(httpcode3 == 200, "Testcase Failed");
			        String bankDetailsUUID=respObjNewPAN.jsonPath().get("bankDetail.bankDetailsUuid");
                  System.out.println(bankDetailsUUID);
			        
			        
					addBank addbankMerchantNewPAN = new addBank(P.TESTDATA.get("AddBankingBeauruAadharPAN"),custId);
					Map<String, String> queryParamNewpan = new HashMap<String, String>();
					Map<String, String> headersNewpan = new HashMap<String, String>();
					Map<String, Object> mybodyadharnewpan = new LinkedHashMap<String, Object>();



					queryParamNewpan.put("solutionType", "merchant_common_onboard");
					queryParamNewpan.put("entityType", "PROPRIETORSHIP");
					queryParamNewpan.put("leadId", leadId);

					headersNewpan.put("Content-Type", "application/json");
					headersNewpan.put("session_token", sToken);
					headersNewpan.put("deviceidentifier", deviceIdentifer);
					headersNewpan.put("version", version);
					headersNewpan.put("UncleScrooge", xmwChecksumBypassValue);
		        
		        
					mybodyadharnewpan.put("onlyValidateBankDetails", false);
					mybodyadharnewpan.put("partialSave", true);
					mybodyadharnewpan.put("bankAccountNumber", bankAccountno);
					mybodyadharnewpan.put("ifsc", bankIfsc);
					mybodyadharnewpan.put("bankName", bankName);
					mybodyadharnewpan.put("bankDetailsUuid", bankDetailsUUID);
					mybodyadharnewpan.put("bankAccountHolderName", "ANMOL JAIN");
					mybodyadharnewpan.put("accountType", "Saving Account");


					mybodyadharnewpan.put("BUSINESS_ENTITY", "PROPRIETORSHIP");
					mybodyadharnewpan.put("BUSINESS_TYPE", "PROPRIETORSHIP");

					 long randomnopan=Utilities.generateRandom(4);
						String pannoRandom=String.valueOf(randomnopan);
						String actualpanno="ZCSPG"+pannoRandom+"k";
						mybodyadharnewpan.put("pan", actualpanno);
						mybodyadharnewpan.put("gstin", "");
						mybodyadharnewpan.put("PAN", actualpanno);
						mybodyadharnewpan.put("relatedBusinessUuid", relatedBusinessUuid);
				Response respObjNewAadharPAN = middlewareServicesObject.addBankPlanmethod(addbankMerchantNewPAN,queryParamNewpan,headersNewpan,mybodyadharnewpan);

				   int httpcode4 = respObjNewAadharPAN.getStatusCode();
//			        Assert.assertTrue(httpcode4 == 200, "Testcase Failed");
			        
			        

					addBank addbankMerchantNewPANMerchantbasicdetails = new addBank(P.TESTDATA.get("AddBankingBeauruAadharPANbasicdetails"),custId);
					Map<String, String> queryParamNewpandetails = new HashMap<String, String>();
					Map<String, String> headersNewpandetails = new HashMap<String, String>();
					Map<String, Object> mybodyadharnewpandetails = new LinkedHashMap<String, Object>();

					queryParamNewpandetails.put("solutionType", "merchant_common_onboard");
					queryParamNewpandetails.put("entityType", "PROPRIETORSHIP");
					queryParamNewpandetails.put("leadId", leadId);

					headersNewpandetails.put("Content-Type", "application/json");
					headersNewpandetails.put("session_token", sToken);
					headersNewpandetails.put("deviceidentifier", deviceIdentifer);
					headersNewpandetails.put("version", version);
					headersNewpandetails.put("UncleScrooge", xmwChecksumBypassValue);
		        
		        
					mybodyadharnewpandetails.put("onlyValidateBankDetails", false);
					mybodyadharnewpandetails.put("partialSave", true);
					mybodyadharnewpandetails.put("bankAccountNumber", bankAccountno);
					mybodyadharnewpandetails.put("ifsc", bankIfsc);
					mybodyadharnewpandetails.put("bankName", bankName);
					mybodyadharnewpandetails.put("bankDetailsUuid", bankDetailsUUID);
					mybodyadharnewpandetails.put("bankAccountHolderName", "ANMOL JAIN");
					mybodyadharnewpandetails.put("relatedBusinessUuid", relatedBusinessUuid);
					mybodyadharnewpandetails.put("accountType", "Saving Account");		
					mybodyadharnewpandetails.put("pan", actualpanno);
					mybodyadharnewpandetails.put("gstin", "");
					mybodyadharnewpandetails.put("PAN", actualpanno);
					mybodyadharnewpandetails.put("BUSINESS_ENTITY", "PROPRIETORSHIP");
					mybodyadharnewpandetails.put("BUSINESS_TYPE", "PROPRIETORSHIP");
					mybodyadharnewpandetails.put("SEGMENT", "BFSI");	
					mybodyadharnewpandetails.put("SUB_SEGMENT", "Loans");	

					mybodyadharnewpandetails.put("NUMBER_OF_EDC_MACHINES_REQUIRED", "Single");	
					mybodyadharnewpandetails.put("SMALL_MERCHANT_DECLARATION", "false");
					String questionList="["
				       		+ "{\\\"questionAlias\\\":\\\"cardPaymentsPerMonth\\\","
				       		+ "\\\"answerAlias\\\":\\\"15 - 30 Lakh\\\"},"
				       		+ "{\\\"questionAlias\\\":\\\"numberOfEmployees\\\","
				       		+ "\\\"answerAlias\\\":\\\"Upto 5\\\"},"
				       		+ "{\\\"questionAlias\\\":\\\"shopRentedOrOwned\\\","
				       		+ "\\\"answerAlias\\\":\\\"Shop Rented\\\"}]";
				      
					mybodyadharnewpandetails.put("QUESTIONS_LIST", questionList);	
                    boolean syncChildLeads=true;
					mybodyadharnewpandetails.put("syncChildLeads", syncChildLeads);	

				Response respObjNewAadharPANdetails = middlewareServicesObject.addBankPlanmethod(addbankMerchantNewPANMerchantbasicdetails,queryParamNewpandetails,headersNewpandetails,mybodyadharnewpandetails);

				   int httpcode5 = respObjNewAadharPANdetails.getStatusCode();
//			        Assert.assertTrue(httpcode5 == 200, "Testcase Failed");
		        
        
        List<String> ltnew=new ArrayList<String>();
        ltnew.add(custId);
        ltnew.add(leadId);
        ltnew.add(entityType);
        ltnew.add(relatedBusinessUuid);
        ltnew.add(entityTypeNew);
        ltnew.add(bankDetailsUUID);


        return ltnew;
		
	} 


	
	@Test(priority = 0, description = "TC_4_addBankWithoutSessiontoken", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_4_addBankWithoutSessiontoken() throws Exception {
		establishConnectiontoServer(sToken,5);

		mcoLead.TC_1_createNewMCOLeadValidateOTP("**********");
		mcoLead.TC_12_createNewMCOLeadValidateOTPEDCContext("**********");
		List<Object> lt=mcoLead.TC_17_validateOTPConfirm("**********");	
		String custId=lt.get(0).toString();
		String leadId=lt.get(1).toString();
//		String entityType=lt.get(2).toString();
		
				
	
		addBank addbankMerchant = new addBank(P.TESTDATA.get("AddBanking"),custId);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
			Map<String, Object> mybody = new LinkedHashMap<String, Object>();



		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", Individualentity);
		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
//		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		
//		requestParams.put("pennyDropDetails", anotherObject);

		
		
//		Map<String, Object> mybody = new HashMap<String, Object>();
		mybody.put("onlyValidateBankDetails", false);
		mybody.put("skipPennyDrop", false);
		mybody.put("partialSave", true);
	        
//		 Map<String, Object> bodynew = new HashMap<>();
		
//		Map<String, Object> bodynew = new LinkedHashMap<String, Object>();
	        long randomno=Utilities.generateRandom(10);
			String bankAccountno=String.valueOf(randomno);
			System.out.println("Bank account no is :"+bankAccountno);
			mybody.put("bankAccountNumber", bankAccountno);
			mybody.put("ifsc", bankIfsc);
			mybody.put("bankName", bankName);
//	        mybody.put("pennyDropDetails", bodynew);
			


		Response respObj = middlewareServicesObject.addBankPlanmethod(addbankMerchant,queryParam,headers,mybody);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 401, "Testcase Failed");
	
		
	} 


	@Test(priority = 0, description = "TC_5_addBankWithoutDeviceIdentifier", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_5_addBankWithoutDeviceIdentifier() throws Exception {
		establishConnectiontoServer(sToken,5);

		mcoLead.TC_1_createNewMCOLeadValidateOTP("**********");
		mcoLead.TC_12_createNewMCOLeadValidateOTPEDCContext("**********");
		List<Object> lt=mcoLead.TC_17_validateOTPConfirm("**********");	
		String custId=lt.get(0).toString();
		String leadId=lt.get(1).toString();
//		String entityType=lt.get(2).toString();
		
				
	
		addBank addbankMerchant = new addBank(P.TESTDATA.get("AddBanking"),custId);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
			Map<String, Object> mybody = new LinkedHashMap<String, Object>();



		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", Individualentity);
		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
//		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		
//		requestParams.put("pennyDropDetails", anotherObject);

		
		
//		Map<String, Object> mybody = new HashMap<String, Object>();
		mybody.put("onlyValidateBankDetails", false);
		mybody.put("skipPennyDrop", false);
		mybody.put("partialSave", true);
	        
//		 Map<String, Object> bodynew = new HashMap<>();
		
//		Map<String, Object> bodynew = new LinkedHashMap<String, Object>();
	        long randomno=Utilities.generateRandom(10);
			String bankAccountno=String.valueOf(randomno);
			System.out.println("Bank account no is :"+bankAccountno);
			mybody.put("bankAccountNumber", bankAccountno);
			mybody.put("ifsc", bankIfsc);
			mybody.put("bankName", bankName);
//	        mybody.put("pennyDropDetails", bodynew);
			


		Response respObj = middlewareServicesObject.addBankPlanmethod(addbankMerchant,queryParam,headers,mybody);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 410, "Testcase Failed");
	
		
	} 
	

	@Test(priority = 0, description = "TC_6_addBankWithoutVersion", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_6_addBankWithoutVersion() throws Exception {
		establishConnectiontoServer(sToken,5);

		mcoLead.TC_1_createNewMCOLeadValidateOTP("**********");
		mcoLead.TC_12_createNewMCOLeadValidateOTPEDCContext("**********");
		List<Object> lt=mcoLead.TC_17_validateOTPConfirm("**********");	
		String custId=lt.get(0).toString();
		String leadId=lt.get(1).toString();
//		String entityType=lt.get(2).toString();
		
				
	
		addBank addbankMerchant = new addBank(P.TESTDATA.get("AddBanking"),custId);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
			Map<String, Object> mybody = new LinkedHashMap<String, Object>();



		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", Individualentity);
		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
//		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		
//		requestParams.put("pennyDropDetails", anotherObject);

		
		
//		Map<String, Object> mybody = new HashMap<String, Object>();
		mybody.put("onlyValidateBankDetails", false);
		mybody.put("skipPennyDrop", false);
		mybody.put("partialSave", true);
	        
//		 Map<String, Object> bodynew = new HashMap<>();
		
//		Map<String, Object> bodynew = new LinkedHashMap<String, Object>();
	        long randomno=Utilities.generateRandom(10);
			String bankAccountno=String.valueOf(randomno);
			System.out.println("Bank account no is :"+bankAccountno);
			mybody.put("bankAccountNumber", bankAccountno);
			mybody.put("ifsc", bankIfsc);
			mybody.put("bankName", bankName);
//	        mybody.put("pennyDropDetails", bodynew);
			


		Response respObj = middlewareServicesObject.addBankPlanmethod(addbankMerchant,queryParam,headers,mybody);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	
		
	}
	
	

	@Test(priority = 0, description = "TC_7_addBankWithoutIFSC", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_7_addBankWithoutIFSC() throws Exception {
		establishConnectiontoServer(sToken,5);

		mcoLead.TC_1_createNewMCOLeadValidateOTP("**********");
		mcoLead.TC_12_createNewMCOLeadValidateOTPEDCContext("**********");
		List<Object> lt=mcoLead.TC_17_validateOTPConfirm("**********");	
		String custId=lt.get(0).toString();
		String leadId=lt.get(1).toString();
//		String entityType=lt.get(2).toString();
		
				
	
		addBank addbankMerchant = new addBank(P.TESTDATA.get("AddBanking"),custId);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
			Map<String, Object> mybody = new LinkedHashMap<String, Object>();



		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", Individualentity);
		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		
//		requestParams.put("pennyDropDetails", anotherObject);

		
		
//		Map<String, Object> mybody = new HashMap<String, Object>();
		mybody.put("onlyValidateBankDetails", false);
		mybody.put("skipPennyDrop", false);
		mybody.put("partialSave", true);
	        
//		 Map<String, Object> bodynew = new HashMap<>();
		
//		Map<String, Object> bodynew = new LinkedHashMap<String, Object>();
	        long randomno=Utilities.generateRandom(10);
			String bankAccountno=String.valueOf(randomno);
			System.out.println("Bank account no is :"+bankAccountno);
			mybody.put("bankAccountNumber", bankAccountno);
			mybody.put("ifsc", "");
			mybody.put("bankName", bankName);
//	        mybody.put("pennyDropDetails", bodynew);
			


		Response respObj = middlewareServicesObject.addBankPlanmethod(addbankMerchant,queryParam,headers,mybody);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 400, "Testcase Failed");


        String actualMessage=respObj.jsonPath().get("displayMessage").toString();
        boolean actual=actualMessage.contains("IFSC cannot be empty");
        boolean expected=expectedifscmessage.contains("IFSC cannot be empty");
//        Assert.assertEquals(actual, expected);
		
	} 
	


	@Test(priority = 0, description = "TC_8_addBankWithoutAccountNumber", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_8_addBankWithoutAccountNumber() throws Exception {
		establishConnectiontoServer(sToken,5);

		mcoLead.TC_1_createNewMCOLeadValidateOTP("**********");
		mcoLead.TC_12_createNewMCOLeadValidateOTPEDCContext("**********");
		List<Object> lt=mcoLead.TC_17_validateOTPConfirm("**********");	
		String custId=lt.get(0).toString();
		String leadId=lt.get(1).toString();
//		String entityType=lt.get(2).toString();
		
				
	
		addBank addbankMerchant = new addBank(P.TESTDATA.get("AddBanking"),custId);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
			Map<String, Object> mybody = new LinkedHashMap<String, Object>();



		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", Individualentity);
		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		
//		requestParams.put("pennyDropDetails", anotherObject);

		
		
//		Map<String, Object> mybody = new HashMap<String, Object>();
		mybody.put("onlyValidateBankDetails", false);
		mybody.put("skipPennyDrop", false);
		mybody.put("partialSave", true);
	        
//		 Map<String, Object> bodynew = new HashMap<>();
		
//		Map<String, Object> bodynew = new LinkedHashMap<String, Object>();
			mybody.put("bankAccountNumber", "");
			mybody.put("ifsc", "ICIC0001070");
			mybody.put("bankName", bankName);
//	        mybody.put("pennyDropDetails", bodynew);
			


		Response respObj = middlewareServicesObject.addBankPlanmethod(addbankMerchant,queryParam,headers,mybody);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 500, "Testcase Failed");


        String actualMessage=respObj.jsonPath().get("displayMessage").toString();
        boolean actual=actualMessage.contains("Failed to update merchant lead");
        boolean expected=expectedACCOUNTmessage.contains("Failed to update merchant lead");
//        Assert.assertEquals(actual, expected);
		
	} 

	
	
	
}

