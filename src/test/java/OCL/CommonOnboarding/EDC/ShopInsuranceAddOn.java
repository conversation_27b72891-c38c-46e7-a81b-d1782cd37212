package OCL.CommonOnboarding.EDC;

import OCL.ManageSim.SIMupdateLead;
import Request.CommonOnboardingEDC.AddonShopInsurance;
import Request.managesim.SimupdateLead;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class ShopInsuranceAddOn extends BaseMethod
{
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	private static final Logger LOGGER = LogManager.getLogger(ShopInsuranceAddOn.class);
	public static String clientId = "OE";
	public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";


	String sessiontoken;
	String agentvar = "8010630022";

	//sessiontoken = ApplicantToken(agentvar, "paytm@123");
	//String sessiontoken = AgentSessionToken("9891497839", "paytm@123");
	String sToken = AgentSessionToken("8010630022","paytm@123");

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("8010630022", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
		waitForLoad(3000);
	}

	@Test(priority = 0,description = "Checking status code")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_001_StatusCode()
	{

		AddonShopInsurance reqobj = new AddonShopInsurance(P.TESTDATA.get("ShopInsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		sessiontoken = AgentSessionToken("9891497839", "paytm@123");

		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("androidid", "2fd53c7d86191a3a");
		headers.put("osversion", "13");
		headers.put("applanguage", "en");
		headers.put("session_token", sessiontoken);
		headers.put("devicename", "IV2201");
		headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");


		Map<String, String> queryparams = new HashMap<String, String>();
		queryparams.put("leadId", "eead7ca5-7c0c-45a5-9aa3-993cb0124af3");

		Response respObj = MiddlewareServicesObject.ShopInsuranceAddonMethod(reqobj, queryparams, headers);
		Assert.assertEquals(respObj.statusCode(), 200);
		LOGGER.info("Display Add-on : " +  respObj.jsonPath().getString("addOnInfo.displayName"));

	}


	@Test(priority = 0,description = "invalid device onbaording lead")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_002_StatusCode()
	{

		AddonShopInsurance reqobj = new AddonShopInsurance(P.TESTDATA.get("ShopInsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		sessiontoken = AgentSessionToken("9891497839", "paytm@123");

		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("androidid", "2fd53c7d86191a3a");
		headers.put("osversion", "13");
		headers.put("applanguage", "en");
		headers.put("session_token", sessiontoken);
		headers.put("devicename", "IV2201");
		headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");


		Map<String, String> queryparams = new HashMap<String, String>();
		queryparams.put("leadId", "3c30a4bc-1c89-4723-84cb-b1ba5f56c077");

		Response respObj = MiddlewareServicesObject.ShopInsuranceAddonMethod(reqobj, queryparams, headers);
		Assert.assertEquals(respObj.statusCode(), 500);
		LOGGER.info("Message : " +  respObj.jsonPath().getString("displayMessage"));

	}
	@Test(priority = 0,description = "invalidsessiontoken")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_003_invalidSessiontoken()
	{
		AddonShopInsurance reqobj = new AddonShopInsurance(P.TESTDATA.get("ShopInsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		sessiontoken = AgentSessionToken("9891497839", "paytm@123");

		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("androidid", "2fd53c7d86191a3a");
		headers.put("osversion", "13");
		headers.put("applanguage", "en");
		headers.put("session_token", "sessiontoken");
		headers.put("devicename", "IV2201");
		headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");


		Map<String, String> queryparams = new HashMap<String, String>();
		queryparams.put("leadId", "e3c1c7a3-32aa-4767-9218-55402d91babd");

		Response respObj = MiddlewareServicesObject.ShopInsuranceAddonMethod(reqobj, queryparams, headers);
		Assert.assertEquals(respObj.statusCode(), 410);
		LOGGER.info("Message : " +  respObj.jsonPath().getString("displayMessage"));


	}

	@Test(priority = 0,description = "leadId not specified")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_004_leadID_not_specified()
	{

		AddonShopInsurance reqobj = new AddonShopInsurance(P.TESTDATA.get("ShopInsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		sessiontoken = AgentSessionToken("9891497839", "paytm@123");

		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("androidid", "2fd53c7d86191a3a");
		headers.put("osversion", "13");
		headers.put("applanguage", "en");
		headers.put("session_token", sessiontoken);
		headers.put("devicename", "IV2201");
		headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");


		Map<String, String> queryparams = new HashMap<String, String>();
		queryparams.put("leadId", "");

		Response respObj = MiddlewareServicesObject.ShopInsuranceAddonMethod(reqobj, queryparams, headers);
		Assert.assertEquals(respObj.statusCode(), 400);
		LOGGER.info("Message : " +  respObj.jsonPath().getString("displayMessage"));

	}
	@Test(priority = 0,description = "device identifier is missing")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_005_without_agent_device_mentioned()
	{

		AddonShopInsurance reqobj = new AddonShopInsurance(P.TESTDATA.get("ShopInsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		sessiontoken = AgentSessionToken("9891497839", "paytm@123");

		headers.put("Host", "goldengate-staging5.paytm.com");
	//	headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("androidid", "2fd53c7d86191a3a");
		headers.put("osversion", "13");
		headers.put("applanguage", "en");
		headers.put("session_token", sessiontoken);
		headers.put("devicename", "IV2201");
		headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");


		Map<String, String> queryparams = new HashMap<String, String>();
		queryparams.put("leadId", "e3c1c7a3-32aa-4767-9218-55402d91babd");

		Response respObj = MiddlewareServicesObject.ShopInsuranceAddonMethod(reqobj, queryparams, headers);
		Assert.assertEquals(respObj.statusCode(), 410);

	}

	@Test(priority = 0,description = "without lead")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_006_without_lead()
	{

		AddonShopInsurance reqobj = new AddonShopInsurance(P.TESTDATA.get("ShopInsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		sessiontoken = AgentSessionToken("9891497839", "paytm@123");

		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("androidid", "2fd53c7d86191a3a");
		headers.put("osversion", "13");
		headers.put("applanguage", "en");
		headers.put("session_token", sessiontoken);
		headers.put("devicename", "IV2201");
		headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");


		Map<String, String> queryparams = new HashMap<String, String>();
	//	queryparams.put("leadId", "e3c1c7a3-32aa-4767-9218-55402d91babd");

		Response respObj = MiddlewareServicesObject.ShopInsuranceAddonMethod(reqobj, queryparams, headers);
		Assert.assertEquals(respObj.statusCode(), 400);

	}
}
