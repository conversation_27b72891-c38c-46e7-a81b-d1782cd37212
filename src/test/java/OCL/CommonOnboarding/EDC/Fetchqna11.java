package OCL.CommonOnboarding.EDC;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.goldengate.common.RetryAnalyzer;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.fetchqnadevices;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class Fetchqna11 extends BaseMethod {
	
	AddBank8 addBank=new AddBank8();
	
	MiddlewareServices middlewareServicesObject = new MiddlewareServices();
	CreateMCOLead2 mcoLead=new CreateMCOLead2();
	private static final Logger LOGGER = LogManager.getLogger(Fetchqna11.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String NewMerchantmobileNo = "**********";
    public static String ExistingMerchantmobileNo = "**********";
public static String bankIfsc="ICIC0001070";
public static String bankName="ICICI BANK LIMITED";

    public static String Individualentity = "INDIVIDUAL";
    public static String user_Type = "common_merchant";
    boolean skipOTPTRUE=true;    boolean skipOTPFalse=false;
boolean onlySaveTncFalse=false;  boolean onlySaveTncTrue=true;
public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";

public static String expectedifscmessage="IFSC cannot be empty (Ref: G-J2MM-155-400)";
public static String expectedACCOUNTmessage="Failed to update merchant lead (Ref: G-J2MZ-155-500))";
public static String sameBankDetails="This bank account is already registered with us. Please enter different bank details. (Ref: G-J2Mb-155-400))";

    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";
    public static String displayMessageWithoutsolutionType="Failed to validate OTP (Ref: G-JoWQ-101-500)";
    public static String displayMessageWithoutentity="Failed to validate OTP (Ref: G-JoV_-101-500)";
    public static String displayMessageInvaliduserType="Invalid action (Ref: G-JoWY-101-400)";
    public static String displayMessageInvalidMobileNumber="Mobile number not associated to your account. Kindly update through 'Need Help section on Login screen of Paytm App/Web' to proceed further (Ref: E-JoWe-101-400)";


    String sToken = AgentSessionToken("**********","paytm@123");
//    String sToken = AgentSessionToken("**********","paytm@123");

//    String sToken = "4d97b959-9746-4633-8136-a39586555600";
    //test

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("**********", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
		waitForLoad(3000);
	}
	@Test(priority = 0, description = "TC_1_fetchqnA", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_fetchqnA() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchqnadevices fetchqnadevicesobj = new fetchqnadevices(P.TESTDATA.get("fetchqnaPlanMethod"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

		queryParam.put("entityType", entityType);
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("questionType", "additional");

		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchqnaPlanMethod(fetchqnadevicesobj,queryParam,headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        String actualCardpaymentspermonthObject=respObj.jsonPath().get("questionList[0].text").toString();
        String expectedCardpaymentpermonthObject="Card payments per month";
//        Assert.assertEquals(actualCardpaymentspermonthObject, expectedCardpaymentpermonthObject);
        String actualcardpaymentpermonthmandatory=respObj.jsonPath().get("questionList[0].mandatory").toString();
        String expectedcardpaymentpermonthmandatory="true";
        
//        Assert.assertEquals(actualcardpaymentpermonthmandatory, expectedcardpaymentpermonthmandatory);
        
        String actualtext= respObj.jsonPath().get("questionList[0].options[0].text").toString();
        System.out.println(actualtext);
//        String expectedtext= "Upto 5 Lakh";
//        Assert.assertEquals(actualtext, expectedtext);
        

        String actualtext1= respObj.jsonPath().get("questionList[0].options[1].text").toString();
        System.out.println(actualtext1);
//
//        String expectedtext1= "5 - 15 Lakh";
//        Assert.assertEquals(actualtext1, expectedtext1);
        

        String actualtext2= respObj.jsonPath().get("questionList[0].options[2].text").toString();
        System.out.println(actualtext2);
//
//        String expectedtext2= "15 - 30 Lakh";
//        Assert.assertEquals(actualtext2, expectedtext2);
        

        String actualtext3= respObj.jsonPath().get("questionList[0].options[3].text").toString();
        System.out.println(actualtext3);
//
//        String expectedtext3= "Zero Card Payments";
//        Assert.assertEquals(actualtext3, expectedtext3);
//        
        
        String actualemployeesObject=respObj.jsonPath().get("questionList[1].text").toString();
        String expectedemployeesObject="Number of Employees";
//        Assert.assertEquals(actualemployeesObject, expectedemployeesObject);
        String actualemplmandatory=respObj.jsonPath().get("questionList[1].mandatory").toString();
        String expectedemplmandatory="true";
        
//        Assert.assertEquals(actualemplmandatory, expectedemplmandatory);
        
        String actualtextnew= respObj.jsonPath().get("questionList[1].options[0].text").toString();
        System.out.println(actualtextnew);
//
//        String expectedtextnew= "Self";
//        Assert.assertEquals(actualtextnew, expectedtextnew);
        

        String actualtext1new= respObj.jsonPath().get("questionList[1].options[1].text").toString();
        System.out.println(actualtext1new);
//
//        String expectedtext1new= "Upto 5";
//        Assert.assertEquals(actualtext1new, expectedtext1new);
        

        String actualtext2new2= respObj.jsonPath().get("questionList[1].options[2].text").toString();
        System.out.println(actualtext2new2);
//
//        String expectedtextnew2= "More Than 5";
//        Assert.assertEquals(actualtext2new2, expectedtextnew2);
//        
        

        String actualshoprentedObject=respObj.jsonPath().get("questionList[2].text").toString();
        String expectedshoprentedObject="Is the shop rented or owned by merchant?";
//        Assert.assertEquals(actualshoprentedObject, expectedshoprentedObject);
        String actualshoprentdmandatory=respObj.jsonPath().get("questionList[2].mandatory").toString();
        String expectedshoprentdmandatory="true";
        
//        Assert.assertEquals(actualshoprentdmandatory, expectedshoprentdmandatory);
        
        String actualtextnew3= respObj.jsonPath().get("questionList[2].options[0].text").toString();
        System.out.println(actualtextnew3);

        
//        String expectedtextnew3= "Rented";
//        Assert.assertEquals(actualtextnew3, expectedtextnew3);
        

        String actualtext1new4= respObj.jsonPath().get("questionList[2].options[1].text").toString();
        System.out.println(actualtext1new4);
//
//        String expectedtext1new4= "Self Owned";
//        Assert.assertEquals(actualtext1new4, expectedtext1new4);
        
        
        


        
	} 
	

	
	
	@Test(priority = 0, description = "TC_2_fetchqnAWithoutSessiontoken", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_2_fetchqnAWithoutSessiontoken() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchqnadevices fetchqnadevicesobj = new fetchqnadevices(P.TESTDATA.get("fetchqnaPlanMethod"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

		queryParam.put("entityType", entityType);
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("questionType", "additional");

		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
//		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchqnaPlanMethod(fetchqnadevicesobj,queryParam,headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
		
		
	} 
	


    

	@Test(priority = 0, description = "TC_3_fetchqnAWithoutDeviceIdentifier", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_3_fetchqnAWithoutDeviceIdentifier() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchqnadevices fetchqnadevicesobj = new fetchqnadevices(P.TESTDATA.get("fetchqnaPlanMethod"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

		queryParam.put("entityType", entityType);
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("questionType", "additional");

		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
//		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchqnaPlanMethod(fetchqnadevicesobj,queryParam,headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
		
		
	} 

	@Test(priority = 0, description = "TC_4_fetchqnAWithoutVersion", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_4_fetchqnAWithoutVersion() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchqnadevices fetchqnadevicesobj = new fetchqnadevices(P.TESTDATA.get("fetchqnaPlanMethod"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

		queryParam.put("entityType", entityType);
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("questionType", "additional");

		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
//		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchqnaPlanMethod(fetchqnadevicesobj,queryParam,headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//		 Assert.assertEquals(respObj.jsonPath().get("displayMessage"),displayMessage);
//	        
//	        Assert.assertEquals(respObj.jsonPath().get("message"),message);
//	        Assert.assertEquals(respObj.jsonPath().get("errorCode"),errorCode);

		
	} 




	@Test(priority = 0, description = "TC_5_fetchqnAWithoutleadID", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_5_fetchqnAWithoutleadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchqnadevices fetchqnadevicesobj = new fetchqnadevices(P.TESTDATA.get("fetchqnaPlanMethod"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

		queryParam.put("entityType", entityType);
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("questionType", "additional");

//		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchqnaPlanMethod(fetchqnadevicesobj,queryParam,headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
		
		
	} 
	
	


	@Test(priority = 0, description = "TC_6_fetchqnAWithoutquestionType", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_6_fetchqnAWithoutquestionType() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchqnadevices fetchqnadevicesobj = new fetchqnadevices(P.TESTDATA.get("fetchqnaPlanMethod"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

		queryParam.put("entityType", entityType);
		queryParam.put("solutionType", "merchant_common_onboard");
//		queryParam.put("questionType", "additional");

		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchqnaPlanMethod(fetchqnadevicesobj,queryParam,headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 400, "Testcase Failed");
		
		
	} 
	


	@Test(priority = 0, description = "TC_7_fetchqnAWithInvalidquestionType", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_7_fetchqnAWithInvalidquestionType() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchqnadevices fetchqnadevicesobj = new fetchqnadevices(P.TESTDATA.get("fetchqnaPlanMethod"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

		queryParam.put("entityType", entityType);
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("questionType", "add");

		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchqnaPlanMethod(fetchqnadevicesobj,queryParam,headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
		
		
	} 
	
	@Test(priority = 0, description = "TC_8_fetchqnAWithoutsolutiontype", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_8_fetchqnAWithoutsolutiontype() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchqnadevices fetchqnadevicesobj = new fetchqnadevices(P.TESTDATA.get("fetchqnaPlanMethod"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

		queryParam.put("entityType", entityType);
//		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("questionType", "add");

		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchqnaPlanMethod(fetchqnadevicesobj,queryParam,headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 400, "Testcase Failed");
		
		
	} 
	
	@Test(priority = 0, description = "TC_9_fetchqnAWithoutInvalidsolutiontype", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_9_fetchqnAWithoutInvalidsolutiontype() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchqnadevices fetchqnadevicesobj = new fetchqnadevices(P.TESTDATA.get("fetchqnaPlanMethod"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

		queryParam.put("entityType", entityType);
		queryParam.put("solutionType", "mer_common");
		queryParam.put("questionType", "add");

		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchqnaPlanMethod(fetchqnadevicesobj,queryParam,headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 500, "Testcase Failed");
		
		
	} 
	

	@Test(priority = 0, description = "TC_10_fetchqnAWithoutentityType", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_10_fetchqnAWithoutentityType() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchqnadevices fetchqnadevicesobj = new fetchqnadevices(P.TESTDATA.get("fetchqnaPlanMethod"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

//		queryParam.put("entityType", entityType);
		queryParam.put("solutionType", "mer_common");
		queryParam.put("questionType", "add");

		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchqnaPlanMethod(fetchqnadevicesobj,queryParam,headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 400, "Testcase Failed");
		
		
	} 

}
