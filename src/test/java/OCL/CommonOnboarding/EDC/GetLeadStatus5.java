package OCL.CommonOnboarding.EDC;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.goldengate.common.RetryAnalyzer;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.getLeadStatus;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class GetLeadStatus5 extends BaseMethod {
	MiddlewareServices middlewareServicesObject = new MiddlewareServices();
	CreateMCOLead2 mcoLead=new  CreateMCOLead2();
	private static final Logger LOGGER = LogManager.getLogger(GetLeadStatus5.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String NewMerchantmobileNo = "5555549829";
//    public static String ExistingMerchantmobileNo = "5555595699";
    public static String ExistingMerchantmobileNo = "5555631843";

    public static String Individualentity = "INDIVIDUAL";
    public static String user_Type = "common_merchant";
    boolean skipOTPTRUE=true;    boolean skipOTPFalse=false;
boolean onlySaveTncFalse=false;  boolean onlySaveTncTrue=true;
public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";
public static String STATUS="SUCCESS";
public static String SUCCESSMESSAGE="Otp sent to phone";


    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";
    public static String displayMessageWithoutsolutionType="Failed to validate OTP (Ref: G-JoWQ-101-500)";
    public static String displayMessageWithoutentity="Failed to validate OTP (Ref: G-JoV_-101-500)";
    public static String displayMessageInvaliduserType="Invalid action (Ref: G-JoWY-101-400)";
    public static String displayMessageInvalidMobileNumber="Please provide a Paytm registered mobile number. (Ref: O-Jobi-101-200)";
    public static String displayMessageWithoutLeadID="Please provide a Paytm registered mobile number. (Ref: O-Jobi-101-200)";


    String sToken = AgentSessionToken("8010630022","paytm@123");
//    String sToken = AgentSessionToken("7771216290","paytm@123");

    String existingLead="6527b8d3-c576-4dc8-b386-0d2141ac0704";

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("8010630022", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
		waitForLoad(3000);
	}

	@Test(priority = 0, description = "TC_1_getLeadStatus", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_getLeadStatus() throws Exception {
		establishConnectiontoServer(sToken,5);

		String LeadID=mcoLead.TC_12_createNewMCOLeadValidateOTPEDCContext("5598328912").get(1).toString();
		
		getLeadStatus getLeadStatus=new getLeadStatus(P.TESTDATA.get("SendOTPLead"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("fetchChildLead", "true");
	queryParam.put("leadId", LeadID);

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getLeadStatusPlanMethod(getLeadStatus, queryParam, headers);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");
  
	
	} 
	

	@Test(priority = 0, description = "TC_2_getexistingChildLeadStatus", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_2_getexistingChildLeadStatus() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> leadsdata=getMCOChildLeads();
		String deviceSolutiontype=leadsdata.get(0);
		String deviceLeadID=leadsdata.get(1);
		String devicesolutionType=leadsdata.get(4);
		String DEVICEworkflowStage=leadsdata.get(6);
		String DEVICEworkflowSUBStage=leadsdata.get(8);
		Object DEVICEpaymentdone=leadsdata.get(10);
		Object DEVICEleadstatus=leadsdata.get(12);
		Object deviceLeadcreationdate=leadsdata.get(14);

		String INSTANTMIDSolutiontype=leadsdata.get(2);
		String INSTANTMIDLeadID=leadsdata.get(3);
		String INSTANTMIDLeadIDSolutionType=leadsdata.get(5);
		String INSTANTMIDworkflowStage=leadsdata.get(7);
		String INSTANTMIDworkflowSubStage=leadsdata.get(9);
		Object InstantMIDpaymentdone=leadsdata.get(11);
		Object InstantMIDleadstatus=leadsdata.get(13);
		Object instantMIDLeadcreationdate=leadsdata.get(15);


       System.out.println("MCO Child solutionType : "+deviceSolutiontype);
       System.out.println("deviceLeadID : "+deviceLeadID);
       System.out.println("devicesolutionType : "+devicesolutionType);
       System.out.println("DEVICEworkflowStage : "+DEVICEworkflowStage);
       System.out.println("DEVICEworkflowSUBStage : "+DEVICEworkflowSUBStage);
       System.out.println("DEVICEpaymentdoneFlag : "+DEVICEpaymentdone);
       System.out.println("DEVICEleadstatus : "+DEVICEleadstatus);
       System.out.println("deviceLeadcreationdate : "+deviceLeadcreationdate);

       System.out.println("MCO Child solutionType : "+INSTANTMIDSolutiontype);
       System.out.println("instantLeadID : "+INSTANTMIDLeadID);
       System.out.println("instantMIDsolutionType : "+INSTANTMIDLeadIDSolutionType);
       System.out.println("INSTANTMIDworkflowStage : "+INSTANTMIDworkflowStage);
       System.out.println("INSTANTMIDworkflowSubStage : "+INSTANTMIDworkflowSubStage);
       System.out.println("InstantMIDpaymentdoneFlag : "+InstantMIDpaymentdone);
       System.out.println("InstantMIDleadstatus : "+InstantMIDleadstatus);
       System.out.println("instantMIDLeadcreationdate : "+instantMIDLeadcreationdate);
	} 
	

	@Test(priority = 0, description = "TC_3_getexistingParentLeadStatus", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_3_getexistingParentLeadStatus() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> leadsdata=getMCOLead();
		String MCOSolutiontype=leadsdata.get(0);
		String MCOleadID=leadsdata.get(1);
		String MCOworkflowStage=leadsdata.get(2);
		String MCOworkflowSUBStage=leadsdata.get(3);
		Object paymentDone=leadsdata.get(4);
		String MCOleadstatus=leadsdata.get(5);
		String MCOLeadcreationdate=leadsdata.get(6);

       System.out.println("MCO solutionType : "+MCOSolutiontype);
       System.out.println("MCO leadID : "+MCOleadID);
       System.out.println("MCOworkflowStage : "+MCOworkflowStage);
       System.out.println("MCOworkflowSUBStage : "+MCOworkflowSUBStage);
       System.out.println("MCOPAYMENTDONE : "+paymentDone);
       System.out.println("MCOleadstatus : "+MCOleadstatus);
       System.out.println("MCOLeadcreationdate : "+MCOLeadcreationdate);

	} 
	@Test(priority = 0, description = "TC_4_getLeadStatusWithoutLeadID", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_4_getLeadStatusWithoutLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);


		getLeadStatus getLeadStatus=new getLeadStatus(P.TESTDATA.get("GetLeadStatus"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("fetchChildLead", "true");

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getLeadStatusPlanMethod(getLeadStatus, queryParam, headers);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 500, "Testcase Failed");
    String Message=respObj.jsonPath().get("displayMessage").toString();
    System.out.println(Message);
	} 
	
	
	public List<String> getMCOChildLeads()
	{
getLeadStatus getLeadStatus=new getLeadStatus(P.TESTDATA.get("GetLeadStatus"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("fetchChildLead", "true");
	queryParam.put("leadId", existingLead);

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getLeadStatusPlanMethod(getLeadStatus, queryParam, headers);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");
    String childsolutionType=respObj.jsonPath().get("solutionType[0]").toString();
    String leadID=respObj.jsonPath().get("leadId[0]").toString();
    String solutionTypeLevel2=respObj.jsonPath().get("solutionTypeLevel2[0]").toString();
    String workflowStage=respObj.jsonPath().get("workflowStage[0]").toString();
    String workflowSubStage=respObj.jsonPath().get("workflowSubStage[0]").toString();
    String paymentDone=respObj.jsonPath().get("paymentDone[0]").toString();
    String status=respObj.jsonPath().get("status[0]").toString();
    String createdat=respObj.jsonPath().get("createdAt[0]").toString();

    String childsolutionType2=respObj.jsonPath().get("solutionType[1]").toString();
    String leadID2=respObj.jsonPath().get("leadId[1]").toString();
    String InstantsolutionTypeLevel2=respObj.jsonPath().get("solutionTypeLevel2[1]").toString();
    String workflowStage2=respObj.jsonPath().get("workflowStage[1]").toString();
    String workflowSubStage2=respObj.jsonPath().get("workflowSubStage[1]").toString();
    String paymentDone2=respObj.jsonPath().get("paymentDone[1]").toString();
    String status2=respObj.jsonPath().get("status[1]").toString();
    String createdat2=respObj.jsonPath().get("createdAt[1]").toString();


ArrayList<String> lt=new ArrayList<String>();
lt.add(childsolutionType);lt.add(leadID);
lt.add(childsolutionType2);lt.add(leadID2);
lt.add(solutionTypeLevel2);lt.add(InstantsolutionTypeLevel2);
lt.add(workflowStage);lt.add(workflowStage2);
lt.add(workflowSubStage);lt.add(workflowSubStage2);
lt.add(paymentDone);lt.add(paymentDone2);
lt.add(status);lt.add(status2);
lt.add(createdat);lt.add(createdat2);

return lt;




	}
	
	
	@Test(priority = 0, description = "TC_005_GetMCOLeadstatusWithoutVersion", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_5_GetMCOLeadstatusWithoutVersion() throws Exception {
		establishConnectiontoServer(sToken,5);

		getLeadStatus getLeadStatus=new getLeadStatus(P.TESTDATA.get("GetLeadStatus"));

		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("leadId", existingLead);
	queryParam.put("fetchChildLead", "true");


	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getLeadStatusPlanMethod(getLeadStatus, queryParam, headers);
    int httpcode = respObj.getStatusCode();
//
//	  Assert.assertTrue(httpcode == 200, "Testcase Failed");
//
//Assert.assertEquals(respObj.jsonPath().get("displayMessage"),displayMessage);
//		     Assert.assertEquals(respObj.jsonPath().get("message"),message);
// Assert.assertEquals(respObj.jsonPath().get("errorCode"),errorCode);


	}
	
	@Test(priority = 0, description = "TC_006_GetMCOLeadStatusWithoutdeviceIdentifier", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_6_GetMCOLeadStatusWithoutdeviceIdentifier() throws Exception {
		establishConnectiontoServer(sToken,5);

		getLeadStatus getLeadStatus=new getLeadStatus(P.TESTDATA.get("GetLeadStatus"));

		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("leadId", existingLead);
	queryParam.put("fetchChildLead", "true");


	headers.put("Content-Type", "application/json");
	headers.put("version", version);

	headers.put("session_token", sToken);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getLeadStatusPlanMethod(getLeadStatus, queryParam, headers);
    int httpcode = respObj.getStatusCode();

//	        Assert.assertTrue(httpcode == 410, "Testcase Failed");
			
	
	}
	
	

	public List<String> getMCOLead() throws Exception {
		establishConnectiontoServer(sToken,5);

		getLeadStatus getLeadStatus=new getLeadStatus(P.TESTDATA.get("GetLeadStatus"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("leadId", existingLead);

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getLeadStatusPlanMethod(getLeadStatus, queryParam, headers);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");
    String solutionType=respObj.jsonPath().get("solutionType").toString();
    String leadID=respObj.jsonPath().get("leadId").toString();
    String workflowStage=respObj.jsonPath().get("workflowStage").toString();
    String workflowSubStage=respObj.jsonPath().get("workflowSubStage").toString();
    String paymentDone=respObj.jsonPath().get("paymentDone").toString();
    String status=respObj.jsonPath().get("status").toString();
    String createdat=respObj.jsonPath().get("createdAt").toString();


ArrayList<String> lt=new ArrayList<String>();
lt.add(solutionType);
lt.add(leadID);
lt.add(workflowStage);
lt.add(workflowSubStage);
lt.add(paymentDone);
lt.add(status);
lt.add(createdat);

return lt;




	}
	
	



}
