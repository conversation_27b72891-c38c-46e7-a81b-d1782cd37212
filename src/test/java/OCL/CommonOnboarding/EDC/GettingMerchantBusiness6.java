package OCL.CommonOnboarding.EDC;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.goldengate.common.RetryAnalyzer;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.getBusiness;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class GettingMerchantBusiness6 extends BaseMethod {
	
	MiddlewareServices middlewareServicesObject = new MiddlewareServices();
	CreateMCOLead2 mcoLead=new  CreateMCOLead2();
	sendingOTPLead4 otplead=new  sendingOTPLead4();

	private static final Logger LOGGER = LogManager.getLogger(GettingMerchantBusiness6.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String NewMerchantmobileNo = "5555549829";
//    public static String ExistingMerchantmobileNo = "5555595699";
    public static String ExistingMerchantmobileNo = "5555631843";

    public static String Individualentity = "INDIVIDUAL";
    public static String user_Type = "common_merchant";
    boolean skipOTPTRUE=true;    boolean skipOTPFalse=false;
boolean onlySaveTncFalse=false;  boolean onlySaveTncTrue=true;
public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";
public static String STATUS="SUCCESS";
public static String SUCCESSMESSAGE="Otp sent to phone";


    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";
    public static String displayMessageWithoutsolutionType="Failed to validate OTP (Ref: G-JoWQ-101-500)";
    public static String displayMessageWithoutentity="Failed to validate OTP (Ref: G-JoV_-101-500)";
    public static String displayMessageInvaliduserType="Invalid action (Ref: G-JoWY-101-400)";
    public static String displayMessageInvalidMobileNumber="Please provide a Paytm registered mobile number. (Ref: O-Jobi-101-200)";
    public static String displayMessageWithoutLeadID="Please provide a Paytm registered mobile number. (Ref: O-Jobi-101-200)";


    String sToken = AgentSessionToken("8010630022","paytm@123");
//    String sToken = AgentSessionToken("7771216290","paytm@123");

    String existingLead="6527b8d3-c576-4dc8-b386-0d2141ac0704";

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("8010630022", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
		waitForLoad(3000);
	}

	@Test(priority = 0, description = "TC_1_getExistingMerchantBusiness", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_getExistingMerchantBusiness() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=mcoLead.TC_17_validateOTPConfirm("5554631843");
		String custId=data.get(0).toString();
		String leadID=data.get(1).toString();
		String entityType=data.get(2).toString();
		String hasEdcContext=data.get(3).toString();
		String hasEdcContextDropped=data.get(4).toString();

		
		getBusiness getLeadStatus=new getBusiness(P.TESTDATA.get("GetBusiness"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("merchantCustId", custId);
	queryParam.put("solutionType", "merchant_common_onboard");

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getBusinessPlanMethod(getLeadStatus, queryParam, headers);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");
	System.out.println("KYB BusinessId :"+respObj.jsonPath().get("businesses[0].kybBusinessId"));
	System.out.println("businessName :"+respObj.jsonPath().get("businesses[0].businessName"));
	
	} 
	
	
	@Test(priority = 0, description = "getMultipleExistingMerchantBusiness", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_2_getMultipleExistingMerchantBusiness() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=mcoLead.TC_17_validateOTPConfirm("5531907499");
		String custId=data.get(0).toString();
		String leadID=data.get(1).toString();
		String entityType=data.get(2).toString();
		String hasEdcContext=data.get(3).toString();
		String hasEdcContextDropped=data.get(4).toString();

		
		getBusiness getLeadStatus=new getBusiness(P.TESTDATA.get("GetBusiness"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("merchantCustId", custId);
	queryParam.put("solutionType", "merchant_common_onboard");

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getBusinessPlanMethod(getLeadStatus, queryParam, headers);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");
	System.out.println("KYB BusinessId :"+respObj.jsonPath().get("businesses[0].kybBusinessId"));
	System.out.println("businessName :"+respObj.jsonPath().get("businesses[0].businessName"));
	System.out.println("KYB BusinessId :"+respObj.jsonPath().get("businesses[1].kybBusinessId"));
	System.out.println("KYB BusinessId :"+respObj.jsonPath().get("businesses[1].businessName"));

	} 
	
	@Test(priority = 0, description = "MerchantNobusiness", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_3_MerchantNobusiness() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=mcoLead.TC_17_validateOTPConfirm("8551236928");
		String custId=data.get(0).toString();
		String leadID=data.get(1).toString();
		String entityType=data.get(2).toString();
		String hasEdcContext=data.get(3).toString();
		String hasEdcContextDropped=data.get(4).toString();

		
		getBusiness getLeadStatus=new getBusiness(P.TESTDATA.get("GetBusiness"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("merchantCustId", custId);
	queryParam.put("solutionType", "merchant_common_onboard");

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getBusinessPlanMethod(getLeadStatus, queryParam, headers);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");

	} 
	
	
	@Test(priority = 0, description = "ErrorWithoutcustidGetBusiness", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_4_ErrorWithoutcustidGetBusiness() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=mcoLead.TC_17_validateOTPConfirm("5553407499");
		String custId=data.get(0).toString();


		
		getBusiness getLeadStatus=new getBusiness(P.TESTDATA.get("GetBusiness"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
//	queryParam.put("merchantCustId", custId);
	queryParam.put("solutionType", "merchant_common_onboard");

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getBusinessPlanMethod(getLeadStatus, queryParam, headers);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 400, "Testcase Failed");
	} 
	
	@Test(priority = 0, description = "TC_5_ErrorWithoutSessionToken", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_5_ErrorWithoutSessionToken() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=mcoLead.TC_17_validateOTPConfirm("5523107499");
		String custId=data.get(0).toString();


		
		getBusiness getLeadStatus=new getBusiness(P.TESTDATA.get("GetBusiness"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("merchantCustId", custId);
	queryParam.put("solutionType", "merchant_common_onboard");

	headers.put("Content-Type", "application/json");
//	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getBusinessPlanMethod(getLeadStatus, queryParam, headers);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 401, "Testcase Failed");
	} 
	
	@Test(priority = 0, description = "TC_6_ErrorWithoutVersion", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_6_ErrorWithoutVersion() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=mcoLead.TC_17_validateOTPConfirm("5551897499");
		String custId=data.get(0).toString();


		
		getBusiness getLeadStatus=new getBusiness(P.TESTDATA.get("GetBusiness"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("merchantCustId", custId);
	queryParam.put("solutionType", "merchant_common_onboard");

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
//	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getBusinessPlanMethod(getLeadStatus, queryParam, headers);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");
    String actualdisplaymessage=respObj.jsonPath().get("displayMessage");
//    Assert.assertEquals(actualdisplaymessage,displayMessage);
    String actualmessage=respObj.jsonPath().get("message");
//    Assert.assertEquals(actualmessage,message);
    String actualerrorCode=respObj.jsonPath().get("errorCode");
//    Assert.assertEquals(actualerrorCode,errorCode);
    
	} 
	

	@Test(priority = 0, description = "TC_7_ErrorWithoutDeviceIdentifer", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_7_ErrorWithoutDeviceIdentifer() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=mcoLead.TC_17_validateOTPConfirm("5522419299");
		String custId=data.get(0).toString();


		
		getBusiness getLeadStatus=new getBusiness(P.TESTDATA.get("GetBusiness"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("merchantCustId", custId);
	queryParam.put("solutionType", "merchant_common_onboard");

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
//	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getBusinessPlanMethod(getLeadStatus, queryParam, headers);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 410, "Testcase Failed");
    
	} 
	
	
	public String getSinglebusinessKYBID(String mobileno) throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=mcoLead.TC_17_validateOTPConfirm(mobileno);
		String custId=data.get(0).toString();
		String leadID=data.get(1).toString();
		String entityType=data.get(2).toString();
		String hasEdcContext=data.get(3).toString();
		String hasEdcContextDropped=data.get(4).toString();

		
		getBusiness getLeadStatus=new getBusiness(P.TESTDATA.get("GetBusiness"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("merchantCustId", custId);
	queryParam.put("solutionType", "merchant_common_onboard");

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getBusinessPlanMethod(getLeadStatus, queryParam, headers);
    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");
	System.out.println("KYB BusinessId :"+respObj.jsonPath().get("businesses[0].kybBusinessId"));
	System.out.println("businessName :"+respObj.jsonPath().get("businesses[0].businessName"));
	return respObj.jsonPath().get("businesses[0].kybBusinessId");
	}
	
    
	public List<String> getMultiplebusinessKYBID(String mobileno) throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=mcoLead.TC_17_validateOTPConfirm(mobileno);
		String custId=data.get(0).toString();
		String leadID=data.get(1).toString();
		String entityType=data.get(2).toString();
		String hasEdcContext=data.get(3).toString();
		String hasEdcContextDropped=data.get(4).toString();

		
		getBusiness getLeadStatus=new getBusiness(P.TESTDATA.get("GetBusiness"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("merchantCustId", custId);
	queryParam.put("solutionType", "merchant_common_onboard");

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getBusinessPlanMethod(getLeadStatus, queryParam, headers);
	List<String> ltnew=new ArrayList<String>();
	ltnew.add(respObj.jsonPath().get("businesses[0].kybBusinessId").toString());
	ltnew.add(respObj.jsonPath().get("businesses[1].kybBusinessId").toString());

	return ltnew;
	}
	

}
