package OCL.CommonOnboarding.EDC;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.goldengate.common.RetryAnalyzer;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.fetchmerchantdevicedetails;
import Request.CommonOnboardingEDC.fetchqnadevices;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class fetchMerchantBasicDeviceDetails12 extends BaseMethod {
	
	AddBank8 addBank=new AddBank8();
	
	MiddlewareServices middlewareServicesObject = new MiddlewareServices();
	CreateMCOLead2 mcoLead=new CreateMCOLead2();
	private static final Logger LOGGER = LogManager.getLogger(fetchMerchantBasicDeviceDetails12.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String NewMerchantmobileNo = "**********";
    public static String ExistingMerchantmobileNo = "**********";
public static String bankIfsc="ICIC0001070";
public static String bankName="ICICI BANK LIMITED";

    public static String Individualentity = "INDIVIDUAL";
    public static String user_Type = "common_merchant";
    boolean skipOTPTRUE=true;    boolean skipOTPFalse=false;
boolean onlySaveTncFalse=false;  boolean onlySaveTncTrue=true;
public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";

public static String expectedifscmessage="IFSC cannot be empty (Ref: G-J2MM-155-400)";
public static String expectedACCOUNTmessage="Failed to update merchant lead (Ref: G-J2MZ-155-500))";
public static String sameBankDetails="This bank account is already registered with us. Please enter different bank details. (Ref: G-J2Mb-155-400))";

    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";
    public static String displayMessageWithoutsolutionType="Failed to validate OTP (Ref: G-JoWQ-101-500)";
    public static String displayMessageWithoutentity="Failed to validate OTP (Ref: G-JoV_-101-500)";
    public static String displayMessageInvaliduserType="Invalid action (Ref: G-JoWY-101-400)";
    public static String displayMessageInvalidMobileNumber="Mobile number not associated to your account. Kindly update through 'Need Help section on Login screen of Paytm App/Web' to proceed further (Ref: E-JoWe-101-400)";


    String sToken = AgentSessionToken("**********","paytm@123");
//    String sToken = AgentSessionToken("**********","paytm@123");

//    String sToken = "4d97b959-9746-4633-8136-a39586555600";
    //test

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("**********", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
		waitForLoad(3000);
	}


	@Test(priority = 0, description = "TC_1_fetchMerchantBasicDeviceDetails", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_fetchMerchantBasicDeviceDetails() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchmerchantdevicedetails fetchmerchantdevicedetailsobj = new fetchmerchantdevicedetails(P.TESTDATA.get("Fetchdevicedetails"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

		queryParam.put("parentLeadId", leadId);
		
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchmerchantbasicdetails(fetchmerchantdevicedetailsobj,headers,queryParam);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        String actualhasEDCContext=respObj.jsonPath().get("hasEDCContext").toString();
        String expectedhasEDCContext="hasEDCContext";
       

        
	} 
	

	@Test(priority = 0, description = "TC_2_fetchMerchantBasicDeviceDetailsExistingBigMerchant", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_2_fetchMerchantBasicDeviceDetailsExistingBigMerchant() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchmerchantdevicedetails fetchmerchantdevicedetailsobj = new fetchmerchantdevicedetails(P.TESTDATA.get("Fetchdevicedetails"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

		queryParam.put("parentLeadId", leadId);
		
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchmerchantbasicdetails(fetchmerchantdevicedetailsobj,headers,queryParam);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        String actualhasEDCContext=respObj.jsonPath().get("hasEDCContext").toString();
//        String actualdeclartion=respObj.jsonPath().get("existingDeclarationStatus").toString();
//        String expecteddeclartion="BIG";
//        Assert.assertEquals(actualdeclartion, expecteddeclartion);
       

        
	} 

	
	
	@Test(priority = 0, description = "TC_3_fetchdevicedetailsAWithoutSessiontoken", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_3_fetchdevicedetailsAWithoutSessiontoken() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchmerchantdevicedetails fetchmerchantdevicedetailsobj = new fetchmerchantdevicedetails(P.TESTDATA.get("Fetchdevicedetails"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

		queryParam.put("parentLeadId", leadId);
		
		headers.put("Content-Type", "application/json");
//		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchmerchantbasicdetails(fetchmerchantdevicedetailsobj,headers,queryParam);
        int httpcode = respObj.getStatusCode();
       
//        Assert.assertTrue(httpcode == 401, "Testcase Failed");
		
		
	} 
	


    

	@Test(priority = 0, description = "TC_4_fetchdevicesdetailsWithoutDeviceIdentifier", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_4_fetchdevicesdetailsWithoutDeviceIdentifier() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchmerchantdevicedetails fetchmerchantdevicedetailsobj = new fetchmerchantdevicedetails(P.TESTDATA.get("Fetchdevicedetails"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

		queryParam.put("parentLeadId", leadId);
		
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
//		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchmerchantbasicdetails(fetchmerchantdevicedetailsobj,headers,queryParam);
        int httpcode = respObj.getStatusCode();
       
//        Assert.assertTrue(httpcode == 410, "Testcase Failed");
		
		
		
	} 

	@Test(priority = 0, description = "TC_5_fetchdevicedetailsWithoutVersion", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_5_fetchdevicedetailsWithoutVersion() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchmerchantdevicedetails fetchmerchantdevicedetailsobj = new fetchmerchantdevicedetails(P.TESTDATA.get("Fetchdevicedetails"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

		queryParam.put("parentLeadId", leadId);
		
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
//		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchmerchantbasicdetails(fetchmerchantdevicedetailsobj,headers,queryParam);
        int httpcode = respObj.getStatusCode();
       
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
		
		
		
	} 




	@Test(priority = 0, description = "TC_6_fetchDEVICEDETAILSWithoutParentLeadID", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_6_fetchDEVICEDETAILSWithoutParentLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchmerchantdevicedetails fetchmerchantdevicedetailsobj = new fetchmerchantdevicedetails(P.TESTDATA.get("Fetchdevicedetails"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

//		queryParam.put("parentLeadId", leadId);
		
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchmerchantbasicdetails(fetchmerchantdevicedetailsobj,headers,queryParam);
        int httpcode = respObj.getStatusCode();
       
//        Assert.assertTrue(httpcode == 400, "Testcase Failed");
		
		
		
	} 
	
	


	@Test(priority = 0, description = "TC_6_fetchdevicedetailsWithInvalidleadID", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_7_fetchdevicedetailsWithInvalidleadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> data=addBank.TC_5_addBank("**********");
		String custId=data.get(0);		
		String leadId=data.get(1);
		String entityType=data.get(2);

		
		fetchmerchantdevicedetails fetchmerchantdevicedetailsobj = new fetchmerchantdevicedetails(P.TESTDATA.get("Fetchdevicedetails"));
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();

		queryParam.put("parentLeadId", "leadId");
		
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);

		Response respObj = middlewareServicesObject.fetchmerchantbasicdetails(fetchmerchantdevicedetailsobj,headers,queryParam);
        int httpcode = respObj.getStatusCode();
       
//        Assert.assertTrue(httpcode == 400, "Testcase Failed");
		
		
		
	} 
	


}

