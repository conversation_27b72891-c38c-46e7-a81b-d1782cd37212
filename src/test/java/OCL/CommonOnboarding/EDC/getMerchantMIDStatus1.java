package OCL.CommonOnboarding.EDC;

import java.util.HashMap;
import java.util.Map;

//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.getMerchantMIDStatus;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class getMerchantMIDStatus1  extends BaseMethod {
	
	MiddlewareServices middlewareServicesObject = new MiddlewareServices();

	private static final Logger LOGGER = LogManager.getLogger(getMerchantMIDStatus1.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String mobileNo = "5555545629";
    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";
    public static String displayMessageWithoutsolutionType="Failed to fetch mids for custId (Ref: G-JoQz-101-500)";


     String sToken = AgentSessionToken("8010630022","paytm@123");
//   String sToken = AgentSessionToken("7771216290","paytm@123");

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLogin() throws Exception {
        sToken = AgentSessionToken("8010630022", "paytm@123");
        LOGGER.info("Agent Token  : " + sToken);
//        establishConnectiontoServer(sToken,5);
        waitForLoad(3000);
    }
	
	@Test(priority = 0, description = "GetMerchantMIDStatus", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_001_GetMerchantMID() throws Exception {
        establishConnectiontoServer(sToken,5);

        getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("merchantMobileNumber", mobileNo);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);


		Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");

		LOGGER.info("getMIDStatusapi : - " + respObj.statusCode());
		boolean agentTNCStatus=respObj.jsonPath().get("agentTncStatus").equals(true);
		boolean agentKYCStatus=respObj.jsonPath().get("agentKycStatus").equals(true);

//		Assert.assertTrue(agentTNCStatus,"AgentTNCStatus TRUE");
//		Assert.assertTrue(agentKYCStatus,"AgentKYCStatus TRUE");

        


	}



	@Test(priority = 1, description = "GetMerchantMIDStatus with invalid session token", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_GetMerchantMIDWithInvalidSessionToken() throws Exception {
        establishConnectiontoServer(sToken,5);

        getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("solutionType", "merchant_common_onboard");
        queryParam.put("merchantMobileNumber", mobileNo);
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", "invalid_token");
        headers.put("deviceidentifier", deviceIdentifer);
        headers.put("version", version);


        Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        int httpcode = respObj.getStatusCode();
        // Assert.assertTrue(httpcode == 401, "Testcase Failed");
//		Assert.assertTrue(httpcode == 410, "Testcase Failed");


        LOGGER.info("getMIDStatusapi : - " + respObj.statusCode());
    }




	
	@Test(priority = 0, description = "GetMerchantMIDStatusWithoutVersion", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_002_GetMerchantMIDWithoutVersion() throws Exception {
        establishConnectiontoServer(sToken,5);

        getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("merchantMobileNumber", mobileNo);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);

		Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");

		LOGGER.info("getMIDStatusapi : - " + respObj.statusCode());
		boolean agentTNCStatus=respObj.jsonPath().get("agentTncStatus").equals(true);
		boolean agentKYCStatus=respObj.jsonPath().get("agentKycStatus").equals(true);

//		Assert.assertTrue(agentTNCStatus,"AgentTNCStatus TRUE");
//		Assert.assertTrue(agentKYCStatus,"AgentKYCStatus TRUE");

//        Assert.assertEquals(respObj.jsonPath().get("displayMessage"),displayMessage);
//
//        Assert.assertEquals(respObj.jsonPath().get("message"),message);
//        Assert.assertEquals(respObj.jsonPath().get("errorCode"),errorCode);


	}
	
	@Test(priority = 0, description = "GetMerchantMIDStatusWithoutDeviceIdentifer", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_003_GetMerchantMIDWithoutDeviceIdentifer() throws Exception {
        establishConnectiontoServer(sToken,5);

        getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("merchantMobileNumber", mobileNo);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("version", version);

		Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 410, "Testcase Failed");

		LOGGER.info("getMIDStatusapi : - " + respObj.statusCode());
		boolean agentTNCStatus=respObj.jsonPath().get("agentTncStatus").equals(true);
		boolean agentKYCStatus=respObj.jsonPath().get("agentKycStatus").equals(true);
//
//		Assert.assertTrue(agentTNCStatus,"AgentTNCStatus TRUE");
//		Assert.assertTrue(agentKYCStatus,"AgentKYCStatus TRUE");

        


	}
	
	@Test(priority = 0, description = "GetMerchantMIDStatusWithoutSolutionType", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_004_GetMerchantMIDWithoutSolutionType() throws Exception {
        establishConnectiontoServer(sToken,5);

        getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("merchantMobileNumber", mobileNo);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);


		Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 500, "Testcase Failed");

		LOGGER.info("getMIDStatusapi : - " + respObj.statusCode());
		boolean agentTNCStatus=respObj.jsonPath().get("agentTncStatus").equals(true);
		boolean agentKYCStatus=respObj.jsonPath().get("agentKycStatus").equals(true);
//
//		Assert.assertTrue(agentTNCStatus,"AgentTNCStatus TRUE");
//		Assert.assertTrue(agentKYCStatus,"AgentKYCStatus TRUE");

		boolean disMessage=displayMessageWithoutsolutionType.contains("Failed to fetch mids");
//		Assert.assertTrue(disMessage,"Failed to fetch mids for custId");


	}
	
	@Test(priority = 0, description = "GetMerchantMIDStatusWithInvalidSolutionType", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_009_GetMerchantMIDWithInvalidSolutionType() throws Exception {
        establishConnectiontoServer(sToken,5);

        getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("merchantMobileNumber", mobileNo);
		queryParam.put("solutionType", "ommon_onboard");

		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);


		Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 500, "Testcase Failed");

		LOGGER.info("getMIDStatusapi : - " + respObj.statusCode());
		boolean agentTNCStatus=respObj.jsonPath().get("agentTncStatus").equals(true);
		boolean agentKYCStatus=respObj.jsonPath().get("agentKycStatus").equals(true);

//		Assert.assertTrue(agentTNCStatus,"AgentTNCStatus TRUE");
//		Assert.assertTrue(agentKYCStatus,"AgentKYCStatus TRUE");

		boolean disMessage=displayMessageWithoutsolutionType.contains("Failed to fetch mids");
//		Assert.assertTrue(disMessage,"Failed to fetch mids for custId");


	}

	@Test(priority = 0, description = "GetMerchantMIDWithoutMobilenumber", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_006_GetMerchantMIDWithoutMobilenumber() throws Exception {
        establishConnectiontoServer(sToken,5);

        getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");

		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);


		Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 400, "Testcase Failed");

	}
	
	@Test(priority = 0, description = "GetMerchantMIDWithoutSessionToken", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_006_GetMerchantMIDWithoutSessionToken() throws Exception {
        establishConnectiontoServer(sToken,5);

        getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("merchantMobileNumber", mobileNo);

		queryParam.put("solutionType", "merchant_common_onboard");

		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);


		Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 401, "Testcase Failed");

	}


	// Copilot

	@Test(priority = 2, description = "GetMerchantMIDStatus with invalid solution type", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_GetMerchantMIDWithInvalidSolutionType() throws Exception {
        establishConnectiontoServer(sToken,5);

        getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("solutionType", "invalid_solution_type");
        queryParam.put("merchantMobileNumber", mobileNo);
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", sToken);
        headers.put("deviceidentifier", deviceIdentifer);
        headers.put("version", version);


        Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        int httpcode = respObj.getStatusCode();
        // Assert.assertTrue(httpcode == 400, "Testcase Failed");
//		Assert.assertTrue(httpcode == 500, "Testcase Failed");

		

        LOGGER.info("getMIDStatusapi : - " + respObj.statusCode());
    }

@Test(priority = 3, description = "GetMerchantMIDStatus with missing solution type", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_GetMerchantMIDWithMissingSolutionType() throws Exception {
    establishConnectiontoServer(sToken,5);

    getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("merchantMobileNumber", mobileNo);
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", sToken);
        headers.put("deviceidentifier", deviceIdentifer);
        headers.put("version", version);


        Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 500, "Testcase Failed");

        LOGGER.info("getMIDStatusapi : - " + respObj.statusCode());
    }

@Test(priority = 4, description = "GetMerchantMIDStatus with invalid solution type", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_GetMerchantMIDWithInvalidSolutionType() throws Exception {
    establishConnectiontoServer(sToken,5);

    getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("solutionType", "invalid_solution_type");
        queryParam.put("merchantMobileNumber", mobileNo);
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", sToken);
        headers.put("deviceidentifier", deviceIdentifer);
        headers.put("version", version);


        Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 500, "Testcase Failed");

        LOGGER.info("getMIDStatusapi : - " + respObj.statusCode());
    }

@Test(priority = 5, description = "GetMerchantMIDStatus with missing mobile number", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_GetMerchantMIDWithMissingMobileNumber() throws Exception {
    establishConnectiontoServer(sToken,5);

    getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("solutionType", "merchant_common_onboard");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", sToken);
        headers.put("deviceidentifier", deviceIdentifer);
        headers.put("version", version);


        Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 400, "Testcase Failed");

        LOGGER.info("getMIDStatusapi : - " + respObj.statusCode());
    }

@Test(priority = 6, description = "GetMerchantMIDStatus with missing session token", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_GetMerchantMIDWithMissingSessionToken() throws Exception {
    establishConnectiontoServer(sToken,5);

    getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("solutionType", "merchant_common_onboard");
        queryParam.put("merchantMobileNumber", mobileNo);
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("deviceidentifier", deviceIdentifer);
        headers.put("version", version);


        Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 401, "Testcase Failed");

        LOGGER.info("getMIDStatusapi : - " + respObj.statusCode());
    }

@Test(priority = 7, description = "GetMerchantMIDStatus with valid query parameters and headers", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_GetMerchantMIDWithValidParams() throws Exception {
    establishConnectiontoServer(sToken,5);

    getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("solutionType", "merchant_common_onboard");
        queryParam.put("merchantMobileNumber", mobileNo);
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", sToken);
        headers.put("deviceidentifier", deviceIdentifer);
        headers.put("version", version);


        Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");

        LOGGER.info("getMIDStatusapi : - " + respObj.statusCode());
        boolean agentTNCStatus=respObj.jsonPath().get("agentTncStatus").equals(true);
        boolean agentKYCStatus=respObj.jsonPath().get("agentKycStatus").equals(true);
//
//        Assert.assertTrue(agentTNCStatus,"AgentTNCStatus TRUE");
//        Assert.assertTrue(agentKYCStatus,"AgentKYCStatus TRUE");
    }

@Test(priority = 8, description = "GetMerchantMIDStatus response format", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_GetMerchantMIDResponseFormat() throws Exception {
    establishConnectiontoServer(sToken,5);

    getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("solutionType", "merchant_common_onboard");
        queryParam.put("merchantMobileNumber", mobileNo);
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", sToken);
        headers.put("deviceidentifier", deviceIdentifer);
        headers.put("version", version);


        Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        String contentType = respObj.getContentType();
//        Assert.assertTrue(contentType.contains("application/json"), "Testcase Failed");

        LOGGER.info("getMIDStatusapi : - " + respObj.statusCode());
    }

@Test(priority = 9, description = "GetMerchantMIDStatus response time", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_GetMerchantMIDResponseTime() throws Exception {
    establishConnectiontoServer(sToken,5);

    getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("solutionType", "merchant_common_onboard");
        queryParam.put("merchantMobileNumber", mobileNo);
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", sToken);
        headers.put("deviceidentifier", deviceIdentifer);
        headers.put("version", version);


        Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        // int responseTime = respObj.getTimeIn(TimeUnit.MILLISECONDS);
        // Assert.assertTrue(responseTime < 5000, "Testcase Failed");

        LOGGER.info("getMIDStatusapi : - " + respObj.statusCode());
    }

@Test(priority = 10, description = "GetMerchantMIDStatus with valid query parameters and headers", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_GetMerchantMIDWithValidParams() throws Exception {
    establishConnectiontoServer(sToken,5);

    getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("solutionType", "merchant_common_onboard");
        queryParam.put("merchantMobileNumber", mobileNo);
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", sToken);
        headers.put("deviceidentifier", deviceIdentifer);
        headers.put("version", version);


        Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        String merchantMID = respObj.jsonPath().get("merchantMID");
        // Assert.assertNotNull(merchantMID, "Testcase Failed");

        LOGGER.info("getMIDStatusapi : - " + respObj.statusCode());
    }

@Test(priority = 11, description = "GetMerchantMIDStatus with valid query parameters and headers", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_GetMerchantMIDWithValidParams() throws Exception {
    establishConnectiontoServer(sToken,5);

    getMerchantMIDStatus getMerchantMIDStatus = new getMerchantMIDStatus(P.TESTDATA.get("GetMerchantMID"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("solutionType", "merchant_common_onboard");
        queryParam.put("merchantMobileNumber", mobileNo);
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", sToken);
        headers.put("deviceidentifier", deviceIdentifer);
        headers.put("version", version);


        Response respObj = middlewareServicesObject.getMIDStatusPlanMethod(getMerchantMIDStatus, queryParam, headers);
        boolean agentTNCStatus = respObj.jsonPath().get("agentTncStatus");
//        Assert.assertNotNull(agentTNCStatus, "Testcase Failed");

        LOGGER.info("getMIDStatusapi : - " + respObj.statusCode());
    }


	


}
