package OCL.CommonOnboarding.EDC;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.goldengate.common.RetryAnalyzer;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.fetchBeauruStatus;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class FetchBeauruDetails10 extends BaseMethod {
	
	MiddlewareServices middlewareServicesObject = new MiddlewareServices();
	CreateMCOLead2 mcoLead=new  CreateMCOLead2();
	sendingOTPLead4 otplead=new  sendingOTPLead4();

	private static final Logger LOGGER = LogManager.getLogger(FetchBeauruDetails10.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String NewMerchantmobileNo = "5555549829";
//    public static String ExistingMerchantmobileNo = "5555595699";
    public static String ExistingMerchantmobileNo = "5555631843";

    public static String Individualentity = "INDIVIDUAL";
    public static String user_Type = "common_merchant";
    boolean skipOTPTRUE=true;    boolean skipOTPFalse=false;
boolean onlySaveTncFalse=false;  boolean onlySaveTncTrue=true;
public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";
public static String STATUS="SUCCESS";
public static String SUCCESSMESSAGE="Otp sent to phone";


    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";
    public static String displayMessageWithoutsolutionType="Failed to validate OTP (Ref: G-JoWQ-101-500)";
    public static String displayMessageWithoutentity="Failed to validate OTP (Ref: G-JoV_-101-500)";
    public static String displayMessageInvaliduserType="Invalid action (Ref: G-JoWY-101-400)";
    public static String displayMessageInvalidMobileNumber="Please provide a Paytm registered mobile number. (Ref: O-Jobi-101-200)";
    public static String displayMessageWithoutLeadID="Please provide a Paytm registered mobile number. (Ref: O-Jobi-101-200)";


    String sToken = AgentSessionToken("8010630022","paytm@123");
//    String sToken = AgentSessionToken("7771216290","paytm@123");

    String existingLead="6527b8d3-c576-4dc8-b386-0d2141ac0704";

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("8010630022", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
		waitForLoad(3000);
	}

	@Test(priority = 0, description = "TC_1_getExistingLeadbeauru", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_getExistingLeadbeauru() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> beauru=fetchbeauruStatus("a1c38e9f-883b-4984-8334-6f216a05a066");
	    String name=beauru.get(0);
		String dob=beauru.get(1);
		String gender=beauru.get(2);
		String serviceResponse=beauru.get(3);
		String ckycDone=beauru.get(4);
		String kycDone=beauru.get(5);
		String panValidated=beauru.get(6);
//		String identification_type=beauru.get(7);
//		String identification_id=beauru.get(8);

	

		System.out.println("name :"+name);
		System.out.println("dob :"+dob);
		System.out.println("gender :"+gender);
		System.out.println("serviceResponse :"+serviceResponse);
		System.out.println("ckycDone :"+ckycDone);
		System.out.println("kycDone :"+kycDone);
		System.out.println("panValidated :"+panValidated);
//		System.out.println("identification_type :"+identification_type);
//		System.out.println("identification_id :"+identification_id);

		
		
	} 
	

	
	
	
	
	@Test(priority = 0, description = "TC_2_ErrorWithoutSessionTokengetExistingLeadbeauru", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_2_ErrorWithoutSessionTokengetExistingLeadbeauru() throws Exception {
		establishConnectiontoServer(sToken,5);

		fetchBeauruStatus fetchBeauruStatus=new fetchBeauruStatus(P.TESTDATA.get("FetchBeauru"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("leadId", "a1c38e9f-883b-4984-8334-6f216a05a066");
	queryParam.put("isGstReq", "true");
	

	headers.put("Content-Type", "application/json");
//	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getbeauruPlanMethod(fetchBeauruStatus, queryParam, headers);


int httpcode = respObj.getStatusCode();


//    Assert.assertTrue(httpcode == 401, "Testcase Failed");
	} 
	
	@Test(priority = 0, description = "TC_3_ErrorWithoutVersiongetExistingLeadbeauru",invocationCount = 16, groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_3_ErrorWithoutVersiongetExistingLeadbeauru() throws Exception {
		establishConnectiontoServer(sToken,5);

		fetchBeauruStatus fetchBeauruStatus=new fetchBeauruStatus(P.TESTDATA.get("FetchBeauru"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("leadId", "a1c38e9f-883b-4984-8334-6f216a05a066");
	queryParam.put("isGstReq", "true");
	

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
//	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getbeauruPlanMethod(fetchBeauruStatus, queryParam, headers);

	int httpcode = respObj.getStatusCode();

		
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");
    String actualdisplaymessage=respObj.jsonPath().get("displayMessage");
//    Assert.assertEquals(actualdisplaymessage,displayMessage);
    String actualmessage=respObj.jsonPath().get("message");
//    Assert.assertEquals(actualmessage,message);
    String actualerrorCode=respObj.jsonPath().get("errorCode");
//    Assert.assertEquals(actualerrorCode,errorCode);
    
	} 
	

	@Test(priority = 0, description = "TC_4_GetBeauruErrorWithoutDeviceIdentifer", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_4_GetBeauruErrorWithoutDeviceIdentifer() throws Exception {
		establishConnectiontoServer(sToken,5);

		fetchBeauruStatus fetchBeauruStatus=new fetchBeauruStatus(P.TESTDATA.get("FetchBeauru"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("leadId", "a1c38e9f-883b-4984-8334-6f216a05a066");
	queryParam.put("isGstReq", "true");
	

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
//	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getbeauruPlanMethod(fetchBeauruStatus, queryParam, headers);

	int httpcode = respObj.getStatusCode();

//    Assert.assertTrue(httpcode == 410, "Testcase Failed");
    
	} 
	
	
    
	public List<String> fetchbeauruStatus(String LeadID) throws Exception {
		establishConnectiontoServer(sToken,5);

		fetchBeauruStatus fetchBeauruStatus=new fetchBeauruStatus(P.TESTDATA.get("FetchBeauru"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("leadId", LeadID);
	queryParam.put("isGstReq", "true");
	

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getbeauruPlanMethod(fetchBeauruStatus, queryParam, headers);
	List<String> ltnew=new ArrayList<String>();
	ltnew.add(respObj.jsonPath().get("name").toString());
	ltnew.add(respObj.jsonPath().get("dob").toString());
	ltnew.add(respObj.jsonPath().get("gender").toString());
    ltnew.add(respObj.jsonPath().get("serviceResponse").toString());
	ltnew.add(respObj.jsonPath().get("ckycDone").toString());
	ltnew.add(respObj.jsonPath().get("kycDone").toString());
	ltnew.add(respObj.jsonPath().get("panValidated").toString());
//	ltnew.add(respObj.jsonPath().get("serviceResponse.user_basic_info.identification_type").toString());
//	ltnew.add(respObj.jsonPath().get("serviceResponseuser_basic_info.identification_id").toString());
//	ltnew.add(respObj.jsonPath().get("serviceResponse.user_bureau_info[0].credit_score").toString());

//	String serviceResp=respObj.jsonPath().get("serviceResponse").toString();
//	ObjectMapper objMapper=new ObjectMapper();
	
//		JsonNode jsontree=objMapper.readTree(serviceResp);
//		String identification=jsontree.get("user_basic_info.identification_type").toString();
//		ltnew.add(identification);
	
	//http://makeseleniumeasy.com/2020/09/17/rest-assured-tutorial-47-fetch-value-from-nested-json-array-using-jsonnode-jackson-at-method/
	
	

int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");

	return ltnew;
	}

	

}



