package OCL.CommonOnboarding.EDC;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.goldengate.common.RetryAnalyzer;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.deviceAddons;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class DeviceAddOns16 extends BaseMethod {
	
	MiddlewareServices middlewareServicesObject = new MiddlewareServices();
	ConfirmPlan15 planConfirm=new ConfirmPlan15();

	CreateMCOLead2 mcoLead=new CreateMCOLead2();
	CreateDeviceOnboardingLead13 leadObj=new CreateDeviceOnboardingLead13();
	AddBank8 objbank=new AddBank8();
	PartialSave18 parobj=new PartialSave18();
	private static final Logger LOGGER = LogManager.getLogger(ConfirmPlan15.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String NewMerchantmobileNo = "**********";
  public static String planID="11608";
  public static String deviceID="10001";

    public static String Individualentity = "INDIVIDUAL";
    public static String user_Type = "common_merchant";
    boolean skipOTPTRUE=true;    boolean skipOTPFalse=false;
boolean onlySaveTncFalse=false;  boolean onlySaveTncTrue=true;
public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";


    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";


    String sToken = AgentSessionToken("**********","paytm@123");
//    String sToken = AgentSessionToken("7771216290","paytm@123");

@BeforeTest
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void AgentLogin() throws Exception {
	sToken = AgentSessionToken("**********", "paytm@123");
	LOGGER.info("Agent Token  : " + sToken);
//	establishConnectiontoServer(sToken,5);
	waitForLoad(3000);
}

	@Test(priority = 0, description = "TC_1_DeviceAddons", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_DeviceAddons() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);	        
	

		deviceAddons deviceAddonsobj = new deviceAddons(P.TESTDATA.get("DeviceAddOnsreq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceAddOns(deviceAddonsobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
//		        Assert.assertTrue(httpcode == 200, "Testcase Failed");
		        

		
		
	} 
	
	

	
	@Test(priority = 0, description = "TC_2_DeviceAddOnsWithoutSessiontoken", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_2_DeviceAddOnsWithoutSessiontoken() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);	        
	

		deviceAddons deviceAddonsobj = new deviceAddons(P.TESTDATA.get("DeviceAddOnsreq"));
				Map<String, Object> queryParam = new HashMap<String, Object>();
				Map<String, Object> headers = new HashMap<String, Object>();
				queryParam.put("planId", PlanId);
				queryParam.put("leadId", deviceOnboardingLead);
				headers.put("Content-Type", "application/json");
//				headers.put("session_token", sToken);
			headers.put("deviceidentifier", deviceIdentifer);
			headers.put("version", version);
			headers.put("UncleScrooge", xmwChecksumBypassValue);
			Response respObjnew = middlewareServicesObject.fetchDeviceAddOns(deviceAddonsobj,queryParam,headers);

		        int httpcode = respObjnew.getStatusCode();
//        Assert.assertTrue(httpcode == 401, "Testcase Failed");
	
		
	} 
	
	
	@Test(priority = 0, description = "TC_3_DeviceAddOnsWithoutDeviceIdentifer", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_3_DeviceAddOnsWithoutDeviceIdentifer() throws Exception {
		establishConnectiontoServer(sToken,5);


		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);	        
	

		deviceAddons deviceAddonsobj = new deviceAddons(P.TESTDATA.get("DeviceAddOnsreq"));
		Map<String, Object> queryParam = new HashMap<String, Object>();
		Map<String, Object> headers = new HashMap<String, Object>();
		queryParam.put("planId", PlanId);
		queryParam.put("leadId", deviceOnboardingLead);
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
//	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", xmwChecksumBypassValue);
	Response respObjnew = middlewareServicesObject.fetchDeviceAddOns(deviceAddonsobj,queryParam,headers);

        int httpcode = respObjnew.getStatusCode();
	   	     
		
//	        Assert.assertTrue(httpcode == 410, "Testcase Failed");
	
		
	} 





	@Test(priority = 0, description = "TC_4_DeviceAddOnsInvalidLeadID", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_4_DeviceAddOnsInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);	        
		deviceAddons deviceAddonsobj = new deviceAddons(P.TESTDATA.get("DeviceAddOnsreq"));
		Map<String, Object> queryParam = new HashMap<String, Object>();
		Map<String, Object> headers = new HashMap<String, Object>();
		queryParam.put("planId", PlanId);
		queryParam.put("leadId", "deviceOnboardingLead");
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", xmwChecksumBypassValue);
	Response respObjnew = middlewareServicesObject.fetchDeviceAddOns(deviceAddonsobj,queryParam,headers);

        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 500, "Testcase Failed");
		
	} 	
	 
	
	@Test(priority = 0, description = "TC_5_DeviceAddOnsWithoutLeadID", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_5_DeviceAddOnsWithoutLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);	        
	
		deviceAddons deviceAddonsobj = new deviceAddons(P.TESTDATA.get("DeviceAddOnsreq"));
		Map<String, Object> queryParam = new HashMap<String, Object>();
		Map<String, Object> headers = new HashMap<String, Object>();
		queryParam.put("planId", PlanId);
//		queryParam.put("leadId", "deviceOnboardingLead");
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", xmwChecksumBypassValue);
	Response respObjnew = middlewareServicesObject.fetchDeviceAddOns(deviceAddonsobj,queryParam,headers);

        int httpcode = respObjnew.getStatusCode();
	
		
//	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
		
	} 	
	
	@Test(priority = 0, description = "TC_6_DeviceAddOnswithInvalidPlanID", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_6_DeviceAddOnswithInvalidPlanID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=planConfirm.TC_ConfirmDevicePlanEDC("5555632145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();
		String deviceOnboardingLead=planDetails.get(6).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);	        
	
		deviceAddons deviceAddonsobj = new deviceAddons(P.TESTDATA.get("DeviceAddOnsreq"));
		Map<String, Object> queryParam = new HashMap<String, Object>();
		Map<String, Object> headers = new HashMap<String, Object>();
		queryParam.put("planId", "PlanId");
		queryParam.put("leadId", deviceOnboardingLead);
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", xmwChecksumBypassValue);
	Response respObjnew = middlewareServicesObject.fetchDeviceAddOns(deviceAddonsobj,queryParam,headers);

        int httpcode = respObjnew.getStatusCode();
	
		
		
	} 	
	

	
}



