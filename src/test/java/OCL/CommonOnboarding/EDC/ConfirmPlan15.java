package OCL.CommonOnboarding.EDC;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.goldengate.common.RetryAnalyzer;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.confirmdevicePlanEDC;
import Request.CommonOnboardingEDC.devicedetailsfetch;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class ConfirmPlan15 extends BaseMethod {
	
	MiddlewareServices middlewareServicesObject = new MiddlewareServices();
	CreateMCOLead2 mcoLead=new CreateMCOLead2();
	CreateDeviceOnboardingLead13 leadObj=new CreateDeviceOnboardingLead13();
	AddBank8 objbank=new AddBank8();
	PartialSave18 parobj=new PartialSave18();
	private static final Logger LOGGER = LogManager.getLogger(ConfirmPlan15.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String NewMerchantmobileNo = "**********";
  public static String planID="11608";
  public static String deviceID="10001";

    public static String Individualentity = "INDIVIDUAL";
    public static String user_Type = "common_merchant";
    boolean skipOTPTRUE=true;    boolean skipOTPFalse=false;
boolean onlySaveTncFalse=false;  boolean onlySaveTncTrue=true;
public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";


    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";


    String sToken = AgentSessionToken("**********","paytm@123");
//    String sToken = AgentSessionToken("7771216290","paytm@123");

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("**********", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
		waitForLoad(3000);
	}

	@Test(priority = 0, description = "TC_1_ConfirmDevicePlan", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_ConfirmDevicePlan() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> planDetails=TC_ConfirmDevicePlanEDC("5555492145");
		String PlanName=planDetails.get(0).toString();
		String PlanId=planDetails.get(1).toString();
		String UsageDepositAmount=planDetails.get(2).toString();
		String AdavnceRentAmount=planDetails.get(3).toString();
		String InstallationCharge=planDetails.get(4).toString();
		String TotalupfrontAmount=planDetails.get(5).toString();

		System.out.println("Plan Name = "+PlanName+" Plan Id = "+PlanId+" UsageDeposit ="+UsageDepositAmount+" AdvanceRent ="+AdavnceRentAmount+" Installation Amount = "+InstallationCharge+" Upfront Amount = "+TotalupfrontAmount);	        
	
	} 
	


	
	@Test(priority = 0, description = "TC_ConfirmDevicePlanEDC", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public List<Object> TC_ConfirmDevicePlanEDC(String mobileno) throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=leadObj.CreateDeviceOnboardingLead(mobileno);

		Object deviceOnboardingLeadID=data.get(0);
		Object mcoLead=data.get(1);
		Object custId=data.get(2);
		Object hasEDCContextDropped=data.get(6);
		
		System.out.println("DeviceOnboardingLead :"+deviceOnboardingLeadID+" MCO Lead :"+mcoLead+
				" Customer Id :"+custId+" hasEDCContextDropped :"+hasEDCContextDropped
				);
		
     devicedetailsfetch devicedetailsfetchobj = new devicedetailsfetch(P.TESTDATA.get("FetchdevicedetailsReq"));
			Map<String, Object> queryParam = new HashMap<String, Object>();
			Map<String, Object> headers = new HashMap<String, Object>();
			
			queryParam.put("leadId", deviceOnboardingLeadID);
			headers.put("Content-Type", "application/json");
			headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceLeadGet(devicedetailsfetchobj,queryParam,headers);

	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        


	        confirmdevicePlanEDC confirmdevicePlanEDCobj = new confirmdevicePlanEDC(P.TESTDATA.get("ConfirmDeviceReq"));
	   			Map<String, Object> queryParamObj = new HashMap<String, Object>();
	   			Map<String, Object> headersObj = new HashMap<String, Object>();
	   			
	   			queryParamObj.put("leadId", deviceOnboardingLeadID);
	   			queryParamObj.put("deviceId", deviceID);
	   			queryParamObj.put("planId", planID);
	   			headersObj.put("Content-Type", "application/json");
	   			headersObj.put("session_token", sToken);
	   			headersObj.put("deviceidentifier", deviceIdentifer);
	   			headersObj.put("version", version);
	   			headersObj.put("UncleScrooge", xmwChecksumBypassValue);
	   		Response confirmPlanrespObj = middlewareServicesObject.ConfirmPlanGet(confirmdevicePlanEDCobj,queryParamObj,headersObj);

	   	        int httpcodeObj = confirmPlanrespObj.getStatusCode();
//	   	        Assert.assertTrue(httpcodeObj == 200, "Testcase Failed");
	   	        
	   	        String planName=confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].name");
	   	        System.out.println(planName);
	   	        
	   	        String planId=confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].id");
	   	        System.out.println(planId);

//	   	        Float usageDepositAmount=confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.usageDeposit.amount");
	   	     Float usageDepositAmount=confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.componentMap.usageDeposit.amount");

	   	        System.out.println(usageDepositAmount);
		Float adavnceRentAmount=confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.componentMap.advanceRentalAmount.amount");

//	   	     Float adavnceRentAmount=confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.componentMap.advanceRentForFirstMonth.amount");
//	   	        Float adavnceRentAmount=confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.advanceRentForFirstMonth.amount");
	   	        System.out.println(adavnceRentAmount);

	   	     Float installationCharge=confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.componentMap.installationCharge.amount");	   	        
//	   	        Float installationCharge=confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.installationCharge.amount");
	   	        System.out.println(installationCharge);

	   	     Float totalUpfrontAmount=confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.componentMap.totalUpfrontCharge.amount");
//	   	        Float totalUpfrontAmount=confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.totalUpfrontCharge.amount");
	   	        System.out.println(totalUpfrontAmount);

	   	        
	   	        ArrayList<Object> lt=new ArrayList<Object>();
	            lt.add(planName);
	            lt.add(planId);
	            lt.add(usageDepositAmount);
	            lt.add(adavnceRentAmount);
	            lt.add(installationCharge);
	            lt.add(totalUpfrontAmount);
	            lt.add(deviceOnboardingLeadID);

//	   	        
//		        System.out.println("Plan Chosen :"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].name"));
//		        System.out.println("Plan ID Selected :"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].id"));
//		        System.out.println("Pricing component :"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.lifeTimeFee.label"));
//		        System.out.println("Lifetime fee Amount :"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.lifeTimeFee.amount"));
//		        System.out.println("Lifetime fee Optional :"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.lifeTimeFee.optional"));
//		        System.out.println("Pricing component :"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.usageDeposit.label"));
//		        System.out.println("Usage deposit :"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.usageDeposit.amount"));
//		        System.out.println("Usage deposit Optional :"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.usageDeposit.optional"));        
//		       
//		        System.out.println("Pricing Component :"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.advanceRentForFirstMonth.label"));
//		        System.out.println("Advance Rent charge :"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.advanceRentForFirstMonth.amount"));
//		        System.out.println("Advance Rent charge Optional"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.advanceRentForFirstMonth.optional"));
//
//
//		        System.out.println("Pricing Component :"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.installationCharge.label"));
//		        System.out.println("Installation charge :"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.installationCharge.amount"));
//		        System.out.println("Installation charge Optional"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.installationCharge.optional"));
//
//
//		        System.out.println("Pricing component :"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.totalUpfrontCharge.label"));
//		        System.out.println("Total Upfront charge :"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.totalUpfrontCharge.amount"));
//		        System.out.println("Total Upfront charge Optional :"+confirmPlanrespObj.jsonPath().get("deviceInfo[0].plans[0].pricingComponent.oneTimeComponent.totalUpfrontCharge.optional"));
//		        
		        return lt;


	        
	
	} 
	

	

	
	@Test(priority = 0, description = "TC_2_ConfirmDevicePlanWithoutSessiontoken", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_2_ConfirmDevicePlanWithoutSessiontoken() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=leadObj.CreateDeviceOnboardingLead("5556234167");

		Object deviceOnboardingLeadID=data.get(0);
		Object mcoLead=data.get(1);
		Object custId=data.get(2);
		Object hasEDCContextDropped=data.get(6);
		
		System.out.println("DeviceOnboardingLead :"+deviceOnboardingLeadID+" MCO Lead :"+mcoLead+
				" Customer Id :"+custId+" hasEDCContextDropped :"+hasEDCContextDropped
				);
		
     devicedetailsfetch devicedetailsfetchobj = new devicedetailsfetch(P.TESTDATA.get("FetchdevicedetailsReq"));
			Map<String, Object> queryParam = new HashMap<String, Object>();
			Map<String, Object> headers = new HashMap<String, Object>();
			
			queryParam.put("leadId", deviceOnboardingLeadID);
			headers.put("Content-Type", "application/json");
			headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceLeadGet(devicedetailsfetchobj,queryParam,headers);

	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        


	        confirmdevicePlanEDC confirmdevicePlanEDCobj = new confirmdevicePlanEDC(P.TESTDATA.get("ConfirmDeviceReq"));
	   			Map<String, Object> queryParamObj = new HashMap<String, Object>();
	   			Map<String, Object> headersObj = new HashMap<String, Object>();
	   			
	   			queryParamObj.put("leadId", deviceOnboardingLeadID);
	   			queryParamObj.put("deviceId", deviceID);
	   			queryParamObj.put("planId", planID);
	   			headersObj.put("Content-Type", "application/json");
//	   			headersObj.put("session_token", sToken);
	   			headersObj.put("deviceidentifier", deviceIdentifer);
	   			headersObj.put("version", version);
	   			headersObj.put("UncleScrooge", xmwChecksumBypassValue);
	   		Response confirmPlanrespObj = middlewareServicesObject.ConfirmPlanGet(confirmdevicePlanEDCobj,queryParamObj,headersObj);

	   	        int httpcodeObj = confirmPlanrespObj.getStatusCode();
	   	     


//	        Assert.assertTrue(httpcodeObj == 401, "Testcase Failed");
	
		
	} 
	
	
	@Test(priority = 0, description = "TC_3_ConfirmDevicePlanWithoutDeviceIdentifer", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_3_ConfirmDevicePlanWithoutDeviceIdentifer() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=leadObj.CreateDeviceOnboardingLead("5556234167");

		Object deviceOnboardingLeadID=data.get(0);
		Object mcoLead=data.get(1);
		Object custId=data.get(2);
		Object hasEDCContextDropped=data.get(6);
		
		System.out.println("DeviceOnboardingLead :"+deviceOnboardingLeadID+" MCO Lead :"+mcoLead+
				" Customer Id :"+custId+" hasEDCContextDropped :"+hasEDCContextDropped
				);
		
     devicedetailsfetch devicedetailsfetchobj = new devicedetailsfetch(P.TESTDATA.get("FetchdevicedetailsReq"));
			Map<String, Object> queryParam = new HashMap<String, Object>();
			Map<String, Object> headers = new HashMap<String, Object>();
			
			queryParam.put("leadId", deviceOnboardingLeadID);
			headers.put("Content-Type", "application/json");
			headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceLeadGet(devicedetailsfetchobj,queryParam,headers);

	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        


	        confirmdevicePlanEDC confirmdevicePlanEDCobj = new confirmdevicePlanEDC(P.TESTDATA.get("ConfirmDeviceReq"));
	   			Map<String, Object> queryParamObj = new HashMap<String, Object>();
	   			Map<String, Object> headersObj = new HashMap<String, Object>();
	   			
	   			queryParamObj.put("leadId", deviceOnboardingLeadID);
	   			queryParamObj.put("deviceId", deviceID);
	   			queryParamObj.put("planId", planID);
	   			headersObj.put("Content-Type", "application/json");
	   			headersObj.put("session_token", sToken);
//	   			headersObj.put("deviceidentifier", deviceIdentifer);
	   			headersObj.put("version", version);
	   			headersObj.put("UncleScrooge", xmwChecksumBypassValue);
	   		Response confirmPlanrespObj = middlewareServicesObject.ConfirmPlanGet(confirmdevicePlanEDCobj,queryParamObj,headersObj);

	   	        int httpcodeObj = confirmPlanrespObj.getStatusCode();
	   	     
		
//	        Assert.assertTrue(httpcodeObj == 410, "Testcase Failed");
	
		
	} 


	@Test(priority = 0, description = "TC_4_ConfirmDevicePlanWithoutVersion", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_4_ConfirmDevicePlanWithoutVersion() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=leadObj.CreateDeviceOnboardingLead("5556234167");

		Object deviceOnboardingLeadID=data.get(0);
		Object mcoLead=data.get(1);
		Object custId=data.get(2);
		Object hasEDCContextDropped=data.get(6);
		
		System.out.println("DeviceOnboardingLead :"+deviceOnboardingLeadID+" MCO Lead :"+mcoLead+
				" Customer Id :"+custId+" hasEDCContextDropped :"+hasEDCContextDropped
				);
		
     devicedetailsfetch devicedetailsfetchobj = new devicedetailsfetch(P.TESTDATA.get("FetchdevicedetailsReq"));
			Map<String, Object> queryParam = new HashMap<String, Object>();
			Map<String, Object> headers = new HashMap<String, Object>();
			
			queryParam.put("leadId", deviceOnboardingLeadID);
			headers.put("Content-Type", "application/json");
			headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceLeadGet(devicedetailsfetchobj,queryParam,headers);

	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        


	        confirmdevicePlanEDC confirmdevicePlanEDCobj = new confirmdevicePlanEDC(P.TESTDATA.get("ConfirmDeviceReq"));
	   			Map<String, Object> queryParamObj = new HashMap<String, Object>();
	   			Map<String, Object> headersObj = new HashMap<String, Object>();
	   			
	   			queryParamObj.put("leadId", deviceOnboardingLeadID);
	   			queryParamObj.put("deviceId", deviceID);
	   			queryParamObj.put("planId", planID);
	   			headersObj.put("Content-Type", "application/json");
	   			headersObj.put("session_token", sToken);
	   			headersObj.put("deviceidentifier", deviceIdentifer);
//	   			headersObj.put("version", version);
	   			headersObj.put("UncleScrooge", xmwChecksumBypassValue);
	   		Response confirmPlanrespObj = middlewareServicesObject.ConfirmPlanGet(confirmdevicePlanEDCobj,queryParamObj,headersObj);

	   	        int httpcodeObj = confirmPlanrespObj.getStatusCode();
//	        Assert.assertTrue(httpcodeObj == 200, "Testcase Failed");
	
		
	} 	



	@Test(priority = 0, description = "TC_5_ConfirmDevicePlanWithInvalidLeadID", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_5_ConfirmDevicePlanWithInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=leadObj.CreateDeviceOnboardingLead("5556234167");

		Object deviceOnboardingLeadID=data.get(0);
		Object mcoLead=data.get(1);
		Object custId=data.get(2);
		Object hasEDCContextDropped=data.get(6);
		
		System.out.println("DeviceOnboardingLead :"+deviceOnboardingLeadID+" MCO Lead :"+mcoLead+
				" Customer Id :"+custId+" hasEDCContextDropped :"+hasEDCContextDropped
				);
		
     devicedetailsfetch devicedetailsfetchobj = new devicedetailsfetch(P.TESTDATA.get("FetchdevicedetailsReq"));
			Map<String, Object> queryParam = new HashMap<String, Object>();
			Map<String, Object> headers = new HashMap<String, Object>();
			
			queryParam.put("leadId", deviceOnboardingLeadID);
			headers.put("Content-Type", "application/json");
			headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceLeadGet(devicedetailsfetchobj,queryParam,headers);

	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        


	        confirmdevicePlanEDC confirmdevicePlanEDCobj = new confirmdevicePlanEDC(P.TESTDATA.get("ConfirmDeviceReq"));
	   			Map<String, Object> queryParamObj = new HashMap<String, Object>();
	   			Map<String, Object> headersObj = new HashMap<String, Object>();
	   			
	   			queryParamObj.put("leadId", "1234");
	   			queryParamObj.put("deviceId", deviceID);
	   			queryParamObj.put("planId", planID);
	   			headersObj.put("Content-Type", "application/json");
	   			headersObj.put("session_token", sToken);
	   			headersObj.put("deviceidentifier", deviceIdentifer);
	   			headersObj.put("version", version);
	   			headersObj.put("UncleScrooge", xmwChecksumBypassValue);
	   		Response confirmPlanrespObj = middlewareServicesObject.ConfirmPlanGet(confirmdevicePlanEDCobj,queryParamObj,headersObj);

	   	        int httpcodeObj = confirmPlanrespObj.getStatusCode();
	
//	        Assert.assertTrue(httpcodeObj == 500, "Testcase Failed");
		
	} 	
	 
	
	@Test(priority = 0, description = "TC_6_ConfirmDevicePlanWithoutLeadID", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_6_ConfirmDevicePlanWithoutLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=leadObj.CreateDeviceOnboardingLead("5556234167");

		Object deviceOnboardingLeadID=data.get(0);
		Object mcoLead=data.get(1);
		Object custId=data.get(2);
		Object hasEDCContextDropped=data.get(6);
		
		System.out.println("DeviceOnboardingLead :"+deviceOnboardingLeadID+" MCO Lead :"+mcoLead+
				" Customer Id :"+custId+" hasEDCContextDropped :"+hasEDCContextDropped
				);
		
     devicedetailsfetch devicedetailsfetchobj = new devicedetailsfetch(P.TESTDATA.get("FetchdevicedetailsReq"));
			Map<String, Object> queryParam = new HashMap<String, Object>();
			Map<String, Object> headers = new HashMap<String, Object>();
			
			queryParam.put("leadId", deviceOnboardingLeadID);
			headers.put("Content-Type", "application/json");
			headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceLeadGet(devicedetailsfetchobj,queryParam,headers);

	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        


	        confirmdevicePlanEDC confirmdevicePlanEDCobj = new confirmdevicePlanEDC(P.TESTDATA.get("ConfirmDeviceReq"));
	   			Map<String, Object> queryParamObj = new HashMap<String, Object>();
	   			Map<String, Object> headersObj = new HashMap<String, Object>();
	   			
//	   			queryParamObj.put("leadId", "1234");
	   			queryParamObj.put("deviceId", deviceID);
	   			queryParamObj.put("planId", planID);
	   			headersObj.put("Content-Type", "application/json");
	   			headersObj.put("session_token", sToken);
	   			headersObj.put("deviceidentifier", deviceIdentifer);
	   			headersObj.put("version", version);
	   			headersObj.put("UncleScrooge", xmwChecksumBypassValue);
	   		Response confirmPlanrespObj = middlewareServicesObject.ConfirmPlanGet(confirmdevicePlanEDCobj,queryParamObj,headersObj);

	   	        int httpcodeObj = confirmPlanrespObj.getStatusCode();
		
//	        Assert.assertTrue(httpcodeObj == 400, "Testcase Failed");
		
	} 	
	
	@Test(priority = 0, description = "TC_7_ConfirmDevicePlanWithInvalidLeadID", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_7_ConfirmDevicePlanWithInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=leadObj.CreateDeviceOnboardingLead("5556234167");

		Object deviceOnboardingLeadID=data.get(0);
		Object mcoLead=data.get(1);
		Object custId=data.get(2);
		Object hasEDCContextDropped=data.get(6);
		
		System.out.println("DeviceOnboardingLead :"+deviceOnboardingLeadID+" MCO Lead :"+mcoLead+
				" Customer Id :"+custId+" hasEDCContextDropped :"+hasEDCContextDropped
				);
		
     devicedetailsfetch devicedetailsfetchobj = new devicedetailsfetch(P.TESTDATA.get("FetchdevicedetailsReq"));
			Map<String, Object> queryParam = new HashMap<String, Object>();
			Map<String, Object> headers = new HashMap<String, Object>();
			
			queryParam.put("leadId", deviceOnboardingLeadID);
			headers.put("Content-Type", "application/json");
			headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceLeadGet(devicedetailsfetchobj,queryParam,headers);

	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        


	        confirmdevicePlanEDC confirmdevicePlanEDCobj = new confirmdevicePlanEDC(P.TESTDATA.get("ConfirmDeviceReq"));
	   			Map<String, Object> queryParamObj = new HashMap<String, Object>();
	   			Map<String, Object> headersObj = new HashMap<String, Object>();
	   			
	   			queryParamObj.put("leadId", "1234");
	   			queryParamObj.put("deviceId", deviceID);
	   			queryParamObj.put("planId", planID);
	   			headersObj.put("Content-Type", "application/json");
	   			headersObj.put("session_token", sToken);
	   			headersObj.put("deviceidentifier", deviceIdentifer);
	   			headersObj.put("version", version);
	   			headersObj.put("UncleScrooge", xmwChecksumBypassValue);
	   		Response confirmPlanrespObj = middlewareServicesObject.ConfirmPlanGet(confirmdevicePlanEDCobj,queryParamObj,headersObj);

	   	        int httpcodeObj = confirmPlanrespObj.getStatusCode();
		
//	        Assert.assertTrue(httpcodeObj == 500, "Testcase Failed");
		
	} 	
	

	
}


