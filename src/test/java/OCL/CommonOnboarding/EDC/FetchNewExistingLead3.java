package OCL.CommonOnboarding.EDC;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.goldengate.common.RetryAnalyzer;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.fetchMCOLead;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class FetchNewExistingLead3 extends BaseMethod {
	
	MiddlewareServices middlewareServicesObject = new MiddlewareServices();
	CreateMCOLead2 newMCOLead=new CreateMCOLead2();


	private static final Logger LOGGER = LogManager.getLogger(FetchNewExistingLead3.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String NewMerchantmobileNo = "5555549829";
    public static String ExistingMerchantmobileNo = "5555631843";

    public static String Individualentity = "INDIVIDUAL";
    public static String user_Type = "common_merchant";
    boolean skipOTPTRUE=true;    boolean skipOTPFalse=false;
boolean onlySaveTncFalse=false;  boolean onlySaveTncTrue=true;
public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";


    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";
    public static String displayMessageWithoutsolutionType="Failed to validate OTP (Ref: G-JoWQ-101-500)";
    public static String displayMessageWithoutentity="Failed to validate OTP (Ref: G-JoV_-101-500)";
    public static String displayMessageInvaliduserType="Invalid action (Ref: G-JoWY-101-400)";
    public static String displayMessageInvalidMobileNumber="Mobile number not associated to your account. Kindly update through 'Need Help section on Login screen of Paytm App/Web' to proceed further (Ref: E-JoWe-101-400)";


    String sToken = AgentSessionToken("**********","paytm@123");
//    String sToken = AgentSessionToken("**********","paytm@123");

    String existingLead="6527b8d3-c576-4dc8-b386-0d2141ac0704";

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("**********", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
		waitForLoad(3000);
	}

	@Test(priority = 0, description = "FetchExistingLead", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_FetchExistingLead() throws Exception {
		establishConnectiontoServer(sToken,5);

		fetchMCOLead fetchMCOLead = new fetchMCOLead(P.TESTDATA.get("FetchMCOLead"),existingLead);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", Individualentity);
		
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);



		Response respObj = middlewareServicesObject.fetchMCOLeadPlanMethod(fetchMCOLead, queryParam, headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        String MobileNumber=respObj.jsonPath().get("merchantDetails.mobileNumberOfCustomer");
		String leadID=respObj.jsonPath().get("merchantDetails.Id");
		String substage=respObj.jsonPath().get("merchantDetails.substage");
		String custId=respObj.jsonPath().get("merchantDetails.merchantCustId");
		String shopAddress=respObj.jsonPath().get("merchantDetails.shopAddress");
		String latitide=respObj.jsonPath().get("merchantDetails.latitudeOfShopVehicle");
		String longitude=respObj.jsonPath().get("merchantDetails.longitudeOfShopVehicle");
		String area=respObj.jsonPath().get("merchantDetails.areaOfEnrollment");
		String state=respObj.jsonPath().get("merchantDetails.state");
		String pincode=respObj.jsonPath().get("merchantDetails.pincode");
		String shopName=respObj.jsonPath().get("merchantDetails.shopName");
		String landmark=respObj.jsonPath().get("merchantDetails.landmark");
		String formattedAddress=respObj.jsonPath().get("merchantDetails.formattedAddress");

		String docProvided=respObj.jsonPath().get("merchantDetails.suggestedRelatedBusinesses[0].address.documents[0].docProvided");
		String relatedBusinessUuid=respObj.jsonPath().get("merchantDetails.relatedBusinessUuid");
		String addressUuid=respObj.jsonPath().get("merchantDetails.addressUuid");
		String entityType=respObj.jsonPath().get("merchantDetails.entityType");
		String segment=respObj.jsonPath().get("merchantDetails.segment");
		String subSegment=respObj.jsonPath().get("merchantDetails.subSegment");
		String bankAccountHolderName=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.VALIDATED_BANK_ACCOUNT_HOLDER_NAME");
		String bankAccountHolderType=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.VALIDATED_BANK_ACCOUNT_TYPE");
		String MERCHANTDOB=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.MERCHANT_DOB");
		String bankAccountNo=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.VALIDATED_BANK_ACC_NO");
		String SETTLEMENTTYPE=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.SETTLEMENT_TYPE");

		String PAN=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.BUREAU_PAN");
		String BUSINESSNAME=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.BUSINESS_NAME");
		String IFSC=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.VALIDATED_BANK_IFSC");
		String DISPLAYNAME=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.DISPLAY_NAME");
		
		String BUREAUSUCCESS=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.BUREAU_SUCCESS");
		String SMALLMERCHANTDECLARATION=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.SMALL_MERCHANT_DECLARATION");
		String RISKREVIEWSTATUS=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.RISK_REVIEW_STATUS");
		String MAQ_RISKY_LOC=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.MAQ_RISKY_LOC");
		String GSTPRESENT=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.GST_PRESENT");
		String MAQUETTE_SEGMENT=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.MAQUETTE_SEGMENT");
		String IS_AGREEMENT_ACCEPTED=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.IS_AGREEMENT_ACCEPTED");
		String CITY_TIER=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.CITY_TIER");
		String PG_FRAMEWORK_CONSIDERED=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.PG_FRAMEWORK_CONSIDERED");
		String BOMCRS_SCORE=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.BOMCRS_SCORE");
		String MERCHANT_PRODUCT_SERVICE=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.MERCHANT_PRODUCT_SERVICE");
		String PRODUCTS_CONTEXT=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.PRODUCTS_CONTEXT");
		String NUMBER_OF_EDC_MACHINES_REQUIRED=respObj.jsonPath().get("merchantDetails.solutionAdditionalInfo.NUMBER_OF_EDC_MACHINES_REQUIRED");

		



//		Assert.assertNotNull(MobileNumber,"MobileNumber Found !!!");
		System.out.println("MobileNumber :"+MobileNumber);
//        Assert.assertNotNull(leadID,"leadID Found !!!");
		System.out.println("leadID :"+leadID);
//        Assert.assertNotNull(substage,"substage Found !!!");
		System.out.println("Substage of lead is :"+substage);
//        Assert.assertNotNull(custId,"custId Found !!!");
		System.out.println("CustId :"+custId);
//		Assert.assertNotNull(custId,"shopAddress Found !!!");
		System.out.println("ShopAddress :"+shopAddress);
//		Assert.assertNotNull(latitide,"latitide Found !!!");
		System.out.println("latitide :"+latitide);
//		Assert.assertNotNull(longitude,"longitude Found !!!");
		System.out.println("longitude :"+longitude);
//		Assert.assertNotNull(area,"Area Found !!!");
		System.out.println("area :"+area);
//		Assert.assertNotNull(state,"state Found !!!");
		System.out.println("state :"+state);
//		Assert.assertNotNull(pincode,"pincode Found !!!");
		System.out.println("pincode :"+pincode);
		System.out.println("shopName :"+shopName);
		System.out.println("landmark :"+landmark);
		System.out.println("formattedAddress :"+formattedAddress);
		System.out.println("docProvided :"+docProvided);

//		Assert.assertNotNull(relatedBusinessUuid,"relatedBusinessUuid Found !!!");
		System.out.println("relatedBusinessUuid :"+relatedBusinessUuid);
//		Assert.assertNotNull(addressUuid,"addressUuid Found !!!");
		System.out.println("addressUuid :"+addressUuid);
//		Assert.assertNotNull(entityType,"entityType Found !!!");
		System.out.println("entityType :"+entityType);

//		Assert.assertNotNull(segment,"segment Found !!!");
		System.out.println("segment :"+segment);
//		Assert.assertNotNull(subSegment,"subSegment Found !!!");
		System.out.println("subSegment :"+subSegment);
//		Assert.assertNotNull(bankAccountHolderName,"bankAccountHolderName Found !!!");
		System.out.println("bankAccountHolderName :"+bankAccountHolderName);
//		Assert.assertNotNull(bankAccountHolderType,"bankAccountHolderType Found !!!");
		System.out.println("bankAccountHolderType :"+bankAccountHolderType);
//		Assert.assertNotNull(MERCHANTDOB,"MERCHANTDOB Found !!!");
		System.out.println("MERCHANTDOB :"+MERCHANTDOB);
//		Assert.assertNotNull(bankAccountNo,"bankAccountNo Found !!!");
		System.out.println("bankAccountNo :"+bankAccountNo);
//		Assert.assertNotNull(SETTLEMENTTYPE,"SETTLEMENTTYPE Found !!!");
		System.out.println("SETTLEMENTTYPE :"+SETTLEMENTTYPE);
//		Assert.assertNotNull(PAN,"PAN Found !!!");
		System.out.println("PAN :"+PAN);
//		Assert.assertNotNull(BUSINESSNAME,"BUSINESSNAME Found !!!");
		System.out.println("BUSINESSNAME :"+BUSINESSNAME);
//		Assert.assertNotNull(IFSC,"IFSC Found !!!");
		System.out.println("IFSC :"+IFSC);
//		Assert.assertNotNull(DISPLAYNAME,"DISPLAYNAME Found !!!");
		System.out.println("DISPLAYNAME :"+DISPLAYNAME);
		System.out.println("BUREAUSUCCESS :"+BUREAUSUCCESS);
		System.out.println("RISKREVIEWSTATUS :"+RISKREVIEWSTATUS);
		System.out.println("MAQ_RISKY_LOC :"+MAQ_RISKY_LOC);
		System.out.println("MAQUETTE_SEGMENT :"+MAQUETTE_SEGMENT);
		System.out.println("IS_AGREEMENT_ACCEPTED :"+IS_AGREEMENT_ACCEPTED);
		System.out.println("CITY_TIER :"+CITY_TIER);
		System.out.println("PG_FRAMEWORK_CONSIDERED :"+PG_FRAMEWORK_CONSIDERED);
		System.out.println("BOMCRS_SCORE :"+BOMCRS_SCORE);
		System.out.println("MERCHANT_PRODUCT_SERVICE :"+MERCHANT_PRODUCT_SERVICE);
		System.out.println("PRODUCTS_CONTEXT :"+PRODUCTS_CONTEXT);
		System.out.println("SMALLMERCHANTDECLARATION :"+SMALLMERCHANTDECLARATION);
		System.out.println("NUMBER_OF_EDC_MACHINES_REQUIRED :"+NUMBER_OF_EDC_MACHINES_REQUIRED);



		
		
	} 
	
	

	@Test(priority = 0, description = "FetchExistingLeadWithInvalidentityType", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_2_FetchExistingLeadWithInvalidentityType() throws Exception {
		establishConnectiontoServer(sToken,5);

		fetchMCOLead fetchMCOLead = new fetchMCOLead(P.TESTDATA.get("FetchMCOLead"),existingLead);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", "Individualentity");
		
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);



		Response respObj = middlewareServicesObject.fetchMCOLeadPlanMethod(fetchMCOLead, queryParam, headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 500, "Testcase Failed");
        
		
		
	} 
	
	
	
	@Test(priority = 0, description = "FetchNewLead", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public List<String> TC_3_FetchNewLead() throws Exception {
		establishConnectiontoServer(sToken,5);

		String MCOLead=newMCOLead.TC_1_createNewMCOLeadValidateOTP("6955432189").get(1);
         System.out.println("New Lead :"+MCOLead);
		fetchMCOLead fetchMCOLead = new fetchMCOLead(P.TESTDATA.get("FetchMCOLead"),MCOLead);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", Individualentity);
		
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);



		Response respObj = middlewareServicesObject.fetchMCOLeadPlanMethod(fetchMCOLead, queryParam, headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        
        String MobileNumber=respObj.jsonPath().get("merchantDetails.mobileNumberOfCustomer");
		String leadID=respObj.jsonPath().get("merchantDetails.Id");
		String substage=respObj.jsonPath().get("merchantDetails.substage");
		String custId=respObj.jsonPath().get("merchantDetails.merchantCustId");

		System.out.println("MobileNumber :"+MobileNumber);
		System.out.println("leadID :"+leadID);
		System.out.println("Substage of lead is :"+substage);
		System.out.println("CustId :"+custId);
		ArrayList<String> lt=new ArrayList<String>();
		lt.add(MobileNumber);
		lt.add(leadID);
		lt.add(substage);
		lt.add(custId);

		return lt;


		
	} 
	
	@Test(priority = 0, description = "FetchNewLeadAndMobileNumberLeadCreatedsubstage", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_4_FetchNewLeadAndMobileNumberLeadCreatedsubstage() throws Exception {
		establishConnectiontoServer(sToken,5);

		String MobileNumber=TC_3_FetchNewLead().get(0);    
		String leadID=TC_3_FetchNewLead().get(1);    
		String substage=TC_3_FetchNewLead().get(2);    
		String custId=TC_3_FetchNewLead().get(3);    

		System.out.println("MobileNumber of Merchant :"+MobileNumber);
		System.out.println("LeadID MCO :"+leadID);
		System.out.println("substage of Lead :"+substage);
		System.out.println("CustId of Merchant :"+custId);
		
	} 
	

	@Test(priority = 0, description = "FetchExistingLeadWithInvalidentityType", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_5_FetchExistingLeadWithInvalidSolutionType() throws Exception {
		establishConnectiontoServer(sToken,5);

		fetchMCOLead fetchMCOLead = new fetchMCOLead(P.TESTDATA.get("FetchMCOLead"),existingLead);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
		queryParam.put("solutionType", "merc_common_onboard");
		queryParam.put("entityType", Individualentity);
		
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);



		Response respObj = middlewareServicesObject.fetchMCOLeadPlanMethod(fetchMCOLead, queryParam, headers);
        int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 500, "Testcase Failed");
        
		
		
	} 
	

	@Test(priority = 0, description = "FetchExistingLeadWithoutSessionToken", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_6_FetchExistingLeadWithoutSessionToken() throws Exception {
		establishConnectiontoServer(sToken,5);

		fetchMCOLead fetchMCOLead = new fetchMCOLead(P.TESTDATA.get("FetchMCOLead"),existingLead);
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("solutionType", "merc_common_onboard");
	queryParam.put("entityType", Individualentity);
	
	headers.put("Content-Type", "application/json");
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);



	Response respObj = middlewareServicesObject.fetchMCOLeadPlanMethod(fetchMCOLead, queryParam, headers);
    int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 401, "Testcase Failed");

	}



	@Test(priority = 0, description = "FetchExistingLeadWithoutVersion", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_7_FetchExistingLeadWithoutVersion() throws Exception {
		establishConnectiontoServer(sToken,5);

		fetchMCOLead fetchMCOLead = new fetchMCOLead(P.TESTDATA.get("FetchMCOLead"),existingLead);

		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("solutionType", "merc_common_onboard");
	queryParam.put("entityType", Individualentity);
	
	headers.put("Content-Type", "application/json");
	headers.put("deviceidentifier", deviceIdentifer);



	Response respObj = middlewareServicesObject.fetchMCOLeadPlanMethod(fetchMCOLead, queryParam, headers);
    int httpcode = respObj.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");

		boolean agentTNCStatus=respObj.jsonPath().get("agentTncStatus").equals(true);
		boolean agentKYCStatus=respObj.jsonPath().get("agentKycStatus").equals(true);

//		Assert.assertTrue(agentTNCStatus,"AgentTNCStatus TRUE");
//		Assert.assertTrue(agentKYCStatus,"AgentKYCStatus TRUE");
//
//        Assert.assertEquals(respObj.jsonPath().get("displayMessage"),displayMessage);
//
//        Assert.assertEquals(respObj.jsonPath().get("message"),message);
//        Assert.assertEquals(respObj.jsonPath().get("errorCode"),errorCode);


	}
	
	
	
	
	
}
