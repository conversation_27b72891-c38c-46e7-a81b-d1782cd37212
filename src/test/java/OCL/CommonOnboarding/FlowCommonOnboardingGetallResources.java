package OCL.CommonOnboarding;

import Request.MerchantService.v1.Resources.GetallResources;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FlowCommonOnboardingGetallResources extends BaseMethod {
    // public static final Logger LOGGER = Logger.getLogger(FlowUnifiedPaymentWithoutPan.class);

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    public FlowCommonOnboardingGetallResources() throws Exception {
    }

    public int stagingServer = getCurrentStagingServer();

    public int getCurrentStagingServer() {


        String apiUrl = P.API.get("api_url");
        if (apiUrl.contains("https://goldengate-staging7.paytm.com/MerchantService")) {
            return 7;  // staging PAN
        } else if (apiUrl.contains("https://goldengate-staging6.paytm.com/MerchantService")) {
            return 6;  // preprod PAN
        }
        return 6; // default
    }
    public static String mobileNo = "8010630022";

    public  String session_token = AgentSessionToken(mobileNo, "paytm@123");
    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLogin() throws Exception {

        establishConnectiontoServer(session_token, stagingServer);
    }
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities)", priority = 0)

    public void TC0001_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
        String entities = fetchAllResources.jsonPath().getString("dataMap.COEntities");
        entities = entities.replace("[", "").replace("]", "");
        String[] entityList = entities.split(" ");
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) with empty authority in headers", priority = 1)

    public void TC0002_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) with empty pragma in headers", priority = 2)

    public void TC0003_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) with empty cache-control in headers", priority = 3)

    public void TC0004_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) with empty sec-ch-ua in headers", priority = 4)

    public void TC0005_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty devicemac in headers ", priority = 5)

    public void TC0006_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty deviceidentifier in headers", priority = 6)

    public void TC0007_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty client in headers", priority = 7)

    public void TC0008_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty x-mw-url-checksum in headers", priority = 8)

    public void TC0009_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty androidid in headers", priority = 9)

    public void TC0010_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty osversion in headers", priority = 10)

    public void TC0011_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty x-src in headers", priority = 11)

    public void TC0012_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty devicemanufacturer in headers", priority = 12)

    public void TC0013_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty sec-ch-ua-platform in headers", priority = 13)

    public void TC0014_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty session_token in headers", priority = 14)

    public void TC0015_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", "");
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty isdevicerooted in headers", priority = 15)

    public void TC0016_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty isbusyboxfound in headers", priority = 16)

    public void TC0017_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty sec-ch-ua-mobile in headers", priority = 17)

    public void TC0018_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty ipaddress in headers", priority = 18)

    public void TC0019_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty content-type in headers", priority = 19)

    public void TC0020_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty accept in headers", priority = 20)

    public void TC0021_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty x-mw-checksum in headers", priority = 21)

    public void TC0022_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty user-agent in headers", priority = 22)

    public void TC0023_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty imei in headers", priority = 23)

    public void TC0024_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty devicename in headers", priority = 24)

    public void TC0025_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty origin in headers", priority = 25)

    public void TC0026_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty sec-fetch-site in headers", priority = 26)

    public void TC0027_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty sec-fetch-mode in headers", priority = 27)

    public void TC0028_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty sec-fetch-dest in headers", priority = 28)

    public void TC0029_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty referer in headers", priority = 29)

    public void TC0030_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty accept-language in headers", priority = 30)

    public void TC0031_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Positive_FetchAllTheResources(Entities) with empty cookie in headers", priority = 31)

    public void TC0032_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Naegative_FetchAllTheResources(Entities) without authority in headers", priority = 32)

    public void TC0033_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without pragma in headers", priority = 33)

    public void TC0034_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without cache-control in headers", priority = 34)

    public void TC0035_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without sec-ch-ua in headers", priority = 35)

    public void TC0036_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without devicemac in headers", priority = 36)

    public void TC0037_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without deviceidentifier in headers", priority = 37)

    public void TC0038_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without client in headers", priority = 38)

    public void TC0039_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without x-mw-url-checksum in headers", priority = 39)

    public void TC0040_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without androidid in headers", priority = 40)

    public void TC0041_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without osversion in headers", priority = 41)

    public void TC0042_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without x-src in headers", priority = 42)

    public void TC0043_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without devicemanufacturer in headers", priority = 43)

    public void TC0044_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without sec-ch-ua-platform in headers", priority = 44)

    public void TC0045_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without session_token in headers", priority = 45)

    public void TC0046_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without isdevicerooted in headers", priority = 46)

    public void TC0047_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without isbusyboxfound in headers", priority = 47)

    public void TC0048_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without sec-ch-ua-mobile in headers", priority = 48)

    public void TC0049_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without ipaddress in headers", priority = 49)

    public void TC0050_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without content-type in headers", priority = 50)

    public void TC0051_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without accept in headers", priority = 51)

    public void TC0052_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without x-mw-checksum in headers", priority = 52)

    public void TC0053_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", session_token);
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without user-agent in headers", priority = 53)

    public void TC0054_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", "79e3fbb0-11e8-407f-9ac4-b7e6af0d0500");
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without imei in headers", priority = 54)

    public void TC0055_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", "79e3fbb0-11e8-407f-9ac4-b7e6af0d0500");
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without devicename in headers", priority = 55)

    public void TC0056_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", "79e3fbb0-11e8-407f-9ac4-b7e6af0d0500");
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without origin in headers", priority = 56)

    public void TC0057_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", "79e3fbb0-11e8-407f-9ac4-b7e6af0d0500");
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without sec-fetch-site in headers", priority = 57)

    public void TC0058_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", "79e3fbb0-11e8-407f-9ac4-b7e6af0d0500");
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without sec-fetch-mode in headers", priority = 58)

    public void TC0059_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", "79e3fbb0-11e8-407f-9ac4-b7e6af0d0500");
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without sec-fetch-dest in headers", priority = 59)

    public void TC0060_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", "79e3fbb0-11e8-407f-9ac4-b7e6af0d0500");
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without referer in headers", priority = 60)

    public void TC0061_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", "79e3fbb0-11e8-407f-9ac4-b7e6af0d0500");
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("accept-language", "en-US,en;q=0.9");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without accept-language in headers", priority = 61)

    public void TC0062_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", "79e3fbb0-11e8-407f-9ac4-b7e6af0d0500");
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("cookie", "JSESSIONID=B8B54621841F2E2F65B4732EC95C5A4E; _ga=GA1.2.1915496843.1640351889; _gcl_au=1.1.1743746630.1640351892; _fbp=fb.1.1640351894373.513076290; X-MW-TOKEN=93d04cf5-bbe3-45ba-b5f9-e79a0ae460fc; X-MW-TOKEN-EX=g%2FyzQMVO%2F%2BJhtUh3ZS8qFysCPTpyjsz00Xj1BPScRk8nUFtHhpxBgsc%2B9B9gHl%2FNkjfnkE7nODzlwlTdB3%2Flvw%3D%3D; X-MW-TOKEN-EXTERNAL=EnprbPm4gE%2FN3glIwYmUGxxnN7m3pinR5CZmrtbwTuym7q2PtSoIAwWDZhzMajE2ntL2U%2FwoGpY2R53J2RwW8g%3D%3D");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without cookie in headers", priority = 62)

    public void TC0063_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("pragma", "no-cache");
        headers.put("cache-control", "no-cache");
        headers.put("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"");
        headers.put("devicemac", "58:85:A2:2A:D4:5B");
        headers.put("deviceidentifier", "Realme-RMX1851-c030dd4e4bec5b91");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "Lalt7TeYEbHgAZtE4195jPYIaKM20Mctwh3mqAgp9dB9ZbQeaUci3juSqwufWYv2QNJ051Vq/ClsF714OteQ253yOApEvmFh8SmN5OdDxtAcx/eMcyGZiU/TMT2Dh+eg");
        headers.put("androidid", "eddd61575c65b950");
        headers.put("osversion", "9");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Realme");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("session_token", "79e3fbb0-11e8-407f-9ac4-b7e6af0d0500");
        headers.put("isdevicerooted", "false");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("ipaddress", "************");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("x-mw-checksum", "XoFBLyALlaho/NKY/KAhigwxs2dWhJg0RcUsB4G44jjks76dXrDcoJFkQQ4/vIT+oTqBTmvzUrTidFke5GhgQPfK8C4zRzs23qmFsGq9HHTxTSmj6d0KWzAjXU045Z8q");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36");
        headers.put("imei", "862162044194517");
        headers.put("devicename", "RMX1851");
        headers.put("origin", "https://local.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://local.paytm.com/");
        headers.put("accept-language", "en-US,en;q=0.9");

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Negative_FetchAllTheResources(Entities) without headers", priority = 63)

    public void TC0064_GetAllResources() throws Exception {
        GetallResources getallResources = new GetallResources();

        Map<String, String> headers = new HashMap<>();

        Response fetchAllResources = middlewareServicesObject.getAllResources(getallResources, headers);

        Assert.assertEquals(fetchAllResources.getStatusCode(), 400);
    }

   }