package OCL.CommonOnboarding;

import Request.MerchantService.v1.MerchantCommonOnboard.AdditionalDetailsFetchScreen;
import Request.MerchantService.v1.MerchantCommonOnboard.AudioDetails;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class AdditionalDetailsCoAPI extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(OCL.CommonOnboarding.AdditionalDetailsCoAPI.class);
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    public int stagingServer = getCurrentStagingServer();

    public int getCurrentStagingServer() {


        String apiUrl = P.API.get("api_url");
        if (apiUrl.contains("https://goldengate-staging7.paytm.com/MerchantService")) {
            return 7;  // staging PAN
        } else if (apiUrl.contains("https://goldengate-staging6.paytm.com/MerchantService")) {
            return 6;  // preprod PAN
        }
        return 6; // default
    }


    public static String version = "7.1.9";
    public static String deviceIdentifier = "samsung-SM-M426B-fc32eda53ff125fb";
    public static String content_type="application/json; charset=UTF-8";
    public  String session_token = AgentSessionToken(mobileNo, "paytm@123");
    //public static String session_token = session_token;
    public static String fetchStrategy = "ADDITIONAL_DETAILS";
    public static String mobileNo = "8010630022";
    public static String deviceName = "SM-M426B";

    public Map<String,String> setCommonParams(){
        Map<String,String> queryParams = new HashMap<>();
        queryParams.put("fetchStrategy","ADDITIONAL_DETAILS");
        return  queryParams;
    }

    public Map<String,String> setCommonHeaders(){
        Map<String,String> headers= new HashMap<>();
        //session_token = ApplicantToken(mobileNo, "paytm@123");
        //headers.put("session_token","session_token");
        headers.put("session_token",session_token);
        headers.put("Content-Type","application/json");
        headers.put("version","7.1.9");
        headers.put("deviceName","SM-M426B");
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        return headers;
    }

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLogin() throws Exception {

        establishConnectiontoServer(session_token, stagingServer);
    }

    // channel
    @Test(groups= "Regression")
    public void T0001_AdditionalDetailApi(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        AdditionalDetailsFetchScreen AdditionalDetailsFetchScreenObj = new AdditionalDetailsFetchScreen();
        Response responseObj = middlewareServicesObject.AdditionalDetails(AdditionalDetailsFetchScreenObj, queryParams,headers);
        int statusCode = responseObj.getStatusCode();
        Assert.assertEquals(statusCode,200);

    }
    @Test(groups= "Regression")
    public void TC002_AdditionalDetailApiWithoutToken() {
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        headers.remove("session_token");
        AudioDetails audioDetails = new AudioDetails();
        Response responseObj = middlewareServicesObject.AudioDetails(audioDetails, queryParams,headers);
        Assert.assertEquals(responseObj.getStatusCode(), 401);


    }
    @Test(groups= "Regression")
    public void TC003_AudioDetailApiWithoutVersion() {
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        headers.remove("version");
        AudioDetails audioDetails = new AudioDetails();
        Response responseObj = middlewareServicesObject.AudioDetails(audioDetails, queryParams,headers);
        String responsemessage = responseObj.jsonPath().getString("message");
        String expectedresponsemessage = "version is empty in header";
        Assert.assertEquals(responsemessage,expectedresponsemessage);


    }

    @Test(groups= "Regression")
    public void TC004_AudioDetailApiWithoutQueryparam() {
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        queryParams.remove("fetchStrategy");
        AudioDetails audioDetails = new AudioDetails();
        Response responseObj = middlewareServicesObject.AudioDetails(audioDetails, queryParams,headers);
        Assert.assertEquals(responseObj.getStatusCode(), 400);


    }
    @Test(groups= "Regression")
    public void TC005_AudioDetailApiWithoutdeviceIdentifier() {
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        headers.remove("deviceIdentifier");
        AudioDetails audioDetails = new AudioDetails();
        Response responseObj = middlewareServicesObject.AudioDetails(audioDetails, queryParams,headers);
        Assert.assertEquals(responseObj.getStatusCode(), 410);


    }

}
