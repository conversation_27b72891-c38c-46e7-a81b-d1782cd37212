package OCL.CommonOnboarding;

//import Request.MerchantService.v1.sfcrm.fetchLeadTnC;

import Request.MerchantService.v2.profile.update.commissiontncs.GetMerchantAgreementTnC;
import Services.MechantService.MiddlewareServices;
import Services.UAD.UADServices;
import Services.Utilities.Utilities;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FlowCommonOnboardingMerchantAgreementTnC extends BaseMethod {
   // private static final Logger LOGGER = Logger.getLogger(OCL.Business.AssistedMerchantOnboard.FlowAssistedMerchantOnboardFixed.class);
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    UADServices uadServicesObject = new UADServices();
    Faker GenerateFake = new Faker();
    Utilities utilities = new Utilities();
    String AuthorizationToken = "";

    public String stagingServer = getCurrentStagingServer();

    public String getCurrentStagingServer() {


        String apiUrl = P.API.get("api_url");
        if (apiUrl.contains("https://goldengate-staging7.paytm.com/MerchantService")) {
            return "7";  // staging PAN
        } else if (apiUrl.contains("https://goldengate-staging6.paytm.com/MerchantService")) {
            return "6";  // preprod PAN
        }
        return "null"; // default
    }


    public static String AgentToken = "6d21d26e-3e1a-4465-a101-7ee9c9697500";
    public static String session_token = "ef0f3ebb-db71-4217-89f2-c1c85dbb0600";
    public static String CustId = "";
    public static String mobileNo = "5997904359";
    public static String newState = "";
    public static String version = "4.7.3";
    public static String OTP = "";
    public static String PAN = "";
    public static String leadId = "b9fcac73-02dc-4ed6-a85e-c45d4622e21a";
    public static String RRBUUID = "";
    public static String nameMatchStatus = "";
    public String lineOne = GenerateFake.address().streetAddress();
    public String lineTwo = GenerateFake.address().cityName();
    public String lineThree = GenerateFake.address().streetName();
    public static String OePanelDocStatus = "REJECTED";
    public static String RejectionReason = "Wrong Photo";
    public static String DocumetRequestDeserialised = "";
    public static String LeadStagePanel = "";
    public static String WorkFlowId = "";
    public static String XMWToken = "";
    public static String MID = "";
    public static String content_type="application/json; charset=UTF-8";

    //public static Map<String,String> headers;
    //public static Map<String,String> queryParams;

    public Map<String,String> setCommonParams(){
        Map<String,String> queryParams=new HashMap<>();;
        queryParams.put("solution","merchant_common_onboard");
        //queryParams.put("leadId","f58a0800-c4b3-4127-8bfd-24cce82cb3eb");
        queryParams.put("entityType","INDIVIDUAL");

//        queryParams.put("solutionLeadId","f58a0800-c4b3-4127-8bfd-24cce82cb3eb");
        queryParams.put("channel","UMP_WEB");
        return queryParams;

    }

    public Map<String,String> setCommonHeaders(){
        Map<String,String> headers=new HashMap<>();;
//        headers.put("Content-Type","application/json");
//        headers.put("version","4.7.3");
//        headers.put("custId",CustId);
//        headers.put("Authorization",AuthorizationToken);
        session_token = ApplicantToken(mobileNo, "paytm@123");
        headers.put("session_token",session_token);
        return headers;
    }
//    @BeforeTest
//    public void SetHeadersAndQueryParams() {
//        setCommonHeaders();
//        setCommonParams();
//
//    }



    @Test(priority = 1)
    public void T0001_GetTnC_Positive(){
//        SetHeadersAndQueryParams();
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);
        //System.out.println("session toke=="+session_token);
        Assert.assertEquals(200,responseObj.getStatusCode());

    }
    //    // solution
    @Test(priority = 2)
    public void T0002001_GetTnC_NoSolution(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        queryParams.remove("solution");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        Assert.assertEquals(400,responseObj.getStatusCode());

    }

    @Test()
    public void T0002002_GetTnC_EmptySolution(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        queryParams.put("solution","");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        Assert.assertEquals(500,responseObj.getStatusCode());

    }

    @Test()
    public void T0002003_GetTnC_DifferentSolution(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        queryParams.put("solution","qr_merchant");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        Boolean rightStatusCode = (500 == responseObj.getStatusCode())? true : false;
        Boolean rightResponseBody= (responseObj.path("displayMessage")!=null);

        Assert.assertEquals(true, rightStatusCode&&rightResponseBody);
        //(Assert.assertTrue(responseObj.path("displayMessage") != null),Assert.assertEquals(500,responseObj.getStatusCode()));
        //Assert.assertEquals(500,responseObj.getStatusCode());
        //Assert.

    }

    @Test()
    public void T0002004_GetTnC_WrongSolution(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        queryParams.put("solution","wrong_solution");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        //Boolean rightStatusCode = (500 == responseObj.getStatusCode())? true : false;
//        String responseLeadId = responseObj.path("displayMessage");
//        System.out.println("displayMessage ="+responseLeadId);
        //Boolean rightResponseBody= (responseObj.path("displayMessage")!=null);
        //System.out.println(rightResponseBody);
        //Assert.assertEquals(true, rightStatusCode &&rightResponseBody);
        Assert.assertEquals(500,responseObj.getStatusCode());
        //here since the response is HTML we cannot use path that is why it was showing error

    }
//    // entityType

    @Test()
    public void T0003001_GetTnC_NoEntityType(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        queryParams.remove("entityType");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        Assert.assertEquals(400,responseObj.getStatusCode());

    }

    @Test()
    public void T0003002_GetTnC_DifferentEntityType(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        queryParams.put("entityType","PUBLIC_LIMITED");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        Assert.assertEquals(200,responseObj.getStatusCode());

    }

    @Test()
    public void T0003003_GetTnC_WrongEntityType(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        queryParams.put("entityType","wrong_entitytype");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        Assert.assertEquals(200,responseObj.getStatusCode());

    }

    @Test()
    public void T0003004_GetTnC_EmptyEntityType(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        queryParams.put("entityType","");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        Assert.assertEquals(200,responseObj.getStatusCode());

    }

//    // channel

    @Test()
    public void T0004001_GetTnC_NoChannel(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        queryParams.remove("channel");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        Assert.assertEquals(400,responseObj.getStatusCode());
    }

    @Test()
    public void T0004002_GetTnC_EmptyChannel(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        queryParams.put("channel","");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        Assert.assertEquals(200,responseObj.getStatusCode());
    }
    @Test()
    public void T0004003_GetTnC_WrongChannel(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        queryParams.put("channel","wrong_channel");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        Assert.assertEquals(200,responseObj.getStatusCode());
    }

    @Test()
    public void T0004004_GetTnC_DifferentChannel(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        queryParams.put("channel","GG_APP");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        Assert.assertEquals(200,responseObj.getStatusCode());
    }

    // solution X enitytype

    @Test()
    public void T0005001_GetTnC_NoEntityType_and_WrongSolution(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        queryParams.put("solution","wrong solution");
        queryParams.remove("entityType");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        Assert.assertEquals(400,responseObj.getStatusCode());
    }

    @Test()
    public void T0005002_GetTnC_NoEntityType_and_DifferentSolution(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        queryParams.put("solution","qr_merchant");
        queryParams.remove("entityType");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        Assert.assertEquals(400,responseObj.getStatusCode());
    }

    //    // session token
    @Test()
    public void T0006001_GetTnC_NoSessionToken(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        headers.remove("session_token");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        Assert.assertEquals(401,responseObj.getStatusCode());
    }

    @Test()
    public void T0006002_GetTnC_EmptySessionToken(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        headers.put("session_token","");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        Assert.assertEquals(401,responseObj.getStatusCode());
    }

    @Test()
    public void T0006003_GetTnC_WrongSessionToken(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        headers.put("session_token","wrong_session_toke");
        GetMerchantAgreementTnC LeadObj = new GetMerchantAgreementTnC();
        Response responseObj= middlewareServicesObject.getMerchantAgreementTnC(LeadObj,queryParams,headers);

        Assert.assertEquals(401,responseObj.getStatusCode());
    }

}
