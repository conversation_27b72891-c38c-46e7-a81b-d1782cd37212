package OCL.CommonOnboarding;

import Request.CommonOnboardingEDC.sendOTPLead;
import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.Revisit.LoanIntentfetchDocumentDetails;
import Request.MerchantService.v3.SubmitDocs;
import Request.MerchantService.v3.SubmitMerchant;
import Request.MerchantService.v3.ValidateOtp;
import Request.MerchantService.v3.merchant.fetch.FetchV3Merchant;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeSuite;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FlowCommonOnboardingPrivateLimitedRejection extends BaseMethod {


    public String MCONonIndividualWorkflowStatusId = "";

    public String sToken = "";
    public String QCAgentsToken = "";

    String BusinessOwnerPhotoDMSID = "";
    String AadharPhotoDMSID1 = "";
    String AadharPhotoDMSID2 = "";
    String PanphotoDMSID = "";
    String CancelledChequePhotoDMSID = "";
    String ShopphotoDMSID = "";

    String AuthSignatoryDeclarationDMSID = "";

    //POAWTN
    String POAWTN = "";
    //coi
    String COI = "";

    //POIAWB
    String POIAWB = "";

    //businessProof
    String businessProof = "";

    public int stagingServer = getCurrentStagingServer();

    public int getCurrentStagingServer() {


        String apiUrl = P.API.get("api_url");
        if (apiUrl.contains("https://goldengate-staging7.paytm.com/MerchantService")) {
            return 7;  // staging PAN
        } else if (apiUrl.contains("https://goldengate-staging6.paytm.com/MerchantService")) {
            return 6;  // preprod PAN
        }
        return 6; // default
    }





    public String AgreementOTPState = "";
    //   String DocPath = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";
    String requestPath = "";
    String endPoint = P.API.get("SubmitDocs");

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    oAuthServices oAuthServicesObj = new oAuthServices();
    Utilities utilities = new Utilities();
    String Mobile = "";
    boolean oauthuser = utilities.createNewAuthUser(Mobile, "Paytm@123");

    public static String version = "7.2.8";

    String custId = "";

    String state = "";
    String leadId = "";
    String AddressUUID = "";

    String relatedBusinessUuid = "";
    String aadharNumber = utilities.generateRandomAadhaar();
    String PAN_Private = utilities.generateRandomPAN("C");



    Map<String, String> headers = new HashMap<>();

    public FlowCommonOnboardingPrivateLimitedRejection() throws Exception {
    }

    @BeforeSuite
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void BeforeSuiteLogin() throws Exception {
        sToken = CommonAgentToken;
        QCAgentsToken = XMWCookie;


        // sToken = AgentSessionToken("8010630022", "paytm@123");
        // QCAgentsToken = AgentSessionToken("7771216290", "paytm@123");
        // XMWCookie = findXMWTokenforPanel("7771216290", "paytm@123");

      //  establishConnectiontoServer(sToken, stagingServer);
        waitForLoad(3000);
        Mobile = utilities.randomMobileNumberGenerator();

        boolean oauthuser = utilities.createNewAuthUser(Mobile, "Paytm@123");

        custId = GetResourceOwnerId(Mobile, "Paytm@123");
    }

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLogin() throws Exception {


        establishConnectiontoServer(sToken,stagingServer);

        headers.put("session_token", sToken); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("version", "7.2.8"); // No change
        headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged

    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 0)

    public void TC01_Create_Lead() throws Exception {

        int maxRetries = 20;
        int retryCount = 0;
        int statusCode = 0;
        Response createMCOLead = null;

        while (retryCount < maxRetries) {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");
            createMCOLead = middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
            statusCode = createMCOLead.getStatusCode();
            if (statusCode == 200) {
                break;
            }
            retryCount++;
        }

        if (statusCode != 200) {
            throw new Exception("Failed to create lead after " + maxRetries + " retries");
        }

        leadId = createMCOLead.path("leadId");
        Assert.assertEquals(statusCode, 200);
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 1, dependsOnMethods = "TC01_Create_Lead")
    public void TC02_SelectProductContext() throws Exception {
        int maxRetries = 20;
        int retryCount = 0;
        int statusCode = 0;
        Response createMCOLead = null;

        while (retryCount < maxRetries) {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPSelectProductContext.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            createMCOLead = middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
            statusCode = createMCOLead.getStatusCode();
            if (statusCode == 200) {
                break;
            }
            retryCount++;
        }

        if (statusCode != 200) {
            throw new Exception("Failed to select product context after " + maxRetries + " retries");
        }

        Assert.assertEquals(statusCode, 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 2, dependsOnMethods = "TC02_SelectProductContext")
    public void TC03_SendOtp() throws Exception {

        requestPath = "MerchantService/V3/SendOtp/SendOtpMco.json";
        sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
        sendOTPobj.getProperties().setProperty("mobile", Mobile);
        sendOTPobj.getProperties().setProperty("custId", custId);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);

        Response createMCOLead = middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        state = createMCOLead.path("state");
        System.out.println("state is " + state);

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 3, dependsOnMethods = "TC03_SendOtp")
    public void TC04_ValidateOTP() throws Exception {

        requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMco.json";
        ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
        ValidateOtp.getProperties().setProperty("state", state);
        ValidateOtp.getProperties().setProperty("otp", "888888");
        ValidateOtp.getProperties().setProperty("mobile", Mobile);
        ValidateOtp.getProperties().setProperty("leadId", leadId);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");

        Response createMCOLead = middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 4, dependsOnMethods = "TC04_ValidateOTP")
    public void TC05_AddBankDetail() throws Exception {

        requestPath = "MerchantService/V3/SubmitMerchant/SubmitMCOAddBankDeatil.json";
        SubmitMerchant AddBankDetail = new SubmitMerchant(custId, requestPath);

        AddBankDetail.getProperties().setProperty("bankAccountNumber", Mobile);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);


        Response createMCOLead = middlewareServicesObject.AddBankZDetail(AddBankDetail, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        relatedBusinessUuid = createMCOLead.path("relatedBusinessUuid");


        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 5, dependsOnMethods = "TC05_AddBankDetail")
    public void TC06_ConfirmBerau() throws Exception {

        requestPath = "MerchantService/V3/SubmitMerchant/ConfirmBearau.json";
        SubmitMerchant confirmbearu = new SubmitMerchant(custId, requestPath);

        confirmbearu.getProperties().setProperty("bankAccountNumber", Mobile);
        confirmbearu.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);

        Response createMCOLead = middlewareServicesObject.AddBankZDetail(confirmbearu, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 6, dependsOnMethods = "TC06_ConfirmBerau")
    public void TC07_AADHAR_OCR_INITIATED() throws Exception {

        requestPath = "MerchantService/V3/SubmitMerchant/AadharOCRInitiate.json";
        SubmitMerchant AADHAR_OCR_INITIATED_obj = new SubmitMerchant(custId, requestPath);

        AADHAR_OCR_INITIATED_obj.getProperties().setProperty("bankAccountNumber", Mobile);
        AADHAR_OCR_INITIATED_obj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);

        Response createMCOLead = middlewareServicesObject.AddBankZDetail(AADHAR_OCR_INITIATED_obj, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 7, dependsOnMethods = "TC07_AADHAR_OCR_INITIATED")
    public void TC08_SubmitAadharOCRDetails() throws Exception {

        requestPath = "MerchantService/V3/SubmitMerchant/SubmitAadharOCRDetails.json";
        SubmitMerchant SubmitAadharOCRDetailsObj = new SubmitMerchant(custId, requestPath);

        SubmitAadharOCRDetailsObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitAadharOCRDetailsObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitAadharOCRDetailsObj.getProperties().setProperty("aadharNumber", aadharNumber);


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);
        Response createMCOLead = middlewareServicesObject.AddBankZDetail(SubmitAadharOCRDetailsObj, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 8, dependsOnMethods = "TC08_SubmitAadharOCRDetails")
    public void TC09_SubmitPanDetail() throws Exception {

        requestPath = "MerchantService/V3/SubmitMerchant/SubmitPanDetailMcoNonIndividual.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);

        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN_Private);
        SubmitPanDetailMcoObj.getProperties().setProperty("BUSINESS_ENTITY", "PRIVATE_LIMITED");
        SubmitPanDetailMcoObj.getProperties().setProperty("BUSINESS_TYPE", "PRIVATE_LIMITED");





        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "PRIVATE_LIMITED");
        queryParams.put("leadId", leadId);

        Response createMCOLead = middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 9, dependsOnMethods = "TC09_SubmitPanDetail")
    public void TC10_SubmitGSTNSkippedMCO() throws Exception {

        requestPath = "MerchantService/V3/SubmitMerchant/SubmitGSTNSkippedMCONonIndividual.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);

        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN_Private);
        SubmitPanDetailMcoObj.getProperties().setProperty("BUSINESS_ENTITY", "PRIVATE_LIMITED");
        SubmitPanDetailMcoObj.getProperties().setProperty("BUSINESS_TYPE", "PRIVATE_LIMITED");


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "PRIVATE_LIMITED");
        queryParams.put("leadId", leadId);

        Response createMCOLead = middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }

    @Test(priority = 10, description = "To check that upload document is working", groups = {"Regression"}, dependsOnMethods = "TC10_SubmitGSTNSkippedMCO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC11_uploadBankProofDocument() throws JSONException, Exception {

        // String endPoint = P.API.get("SubmitDocs");


        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "merchant_common_onboard");
        //  params.put("channel", channel);
        params.put("entityType", "PRIVATE_LIMITED");
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "0");
        params.put("docCount", "0");
        params.put("docType", "bankProof");
        params.put("docProvided", "cancelledChequePhoto");
        params.put("merchantCustId", custId);
        params.put("type", "jpg");

        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);


        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);






      /*  File uploadFile = new File("src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png");
        Response resp2 = utilities.UploadDocInAPI(uploadFile, params, headers, endPoint);

        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200); */

    }


   

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 11, dependsOnMethods = "TC11_uploadBankProofDocument")

    public void TC14_qna_submit() throws Exception {

        requestPath = "MerchantService/V3/SubmitMerchant/QNANonIndividual.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);

        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN_Private);
        SubmitPanDetailMcoObj.getProperties().setProperty("BUSINESS_ENTITY", "PRIVATE_LIMITED");
        SubmitPanDetailMcoObj.getProperties().setProperty("BUSINESS_TYPE", "PRIVATE_LIMITED");


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "PRIVATE_LIMITED");
        queryParams.put("leadId", leadId);
        Response createMCOLead = middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 14, dependsOnMethods = "TC14_qna_submit")
    public void TC15_SegmentSubSegmentSubmit() throws Exception {

        requestPath = "MerchantService/V3/SubmitMerchant/SegmentSubSegmentSubmitNonIndividual.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);

        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN_Private);
        SubmitPanDetailMcoObj.getProperties().setProperty("BUSINESS_ENTITY", "PRIVATE_LIMITED");
        SubmitPanDetailMcoObj.getProperties().setProperty("BUSINESS_TYPE", "PRIVATE_LIMITED");


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "PRIVATE_LIMITED");
        queryParams.put("leadId", leadId);

        Response createMCOLead = middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 15, dependsOnMethods = "TC15_SegmentSubSegmentSubmit")
    public void TC16_UAC_Submit() throws Exception {

        requestPath = "MerchantService/V3/SubmitMerchant/UACSubmitNonIndividual.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);

        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN_Private);
        SubmitPanDetailMcoObj.getProperties().setProperty("BUSINESS_ENTITY", "PRIVATE_LIMITED");
        SubmitPanDetailMcoObj.getProperties().setProperty("BUSINESS_TYPE", "PRIVATE_LIMITED");


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "PRIVATE_LIMITED");
        queryParams.put("leadId", leadId);

        Response createMCOLead = middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 16, dependsOnMethods = "TC16_UAC_Submit")
    public void TC17_FetchLeadDetail() throws Exception {

        FetchV3Merchant v3Merch = new FetchV3Merchant(leadId);

        Map<String, String> query = new HashMap<>();
        query.put("entityType", "PRIVATE_LIMITED");
        query.put("solutionType", "merchant_common_onboard");
        query.put("custId", custId);


        Response RespV3Fetch = middlewareServicesObject.FetchV3MerchantLead(v3Merch, query, headers);
        // AddressUUID = RespV3Fetch.path("addressUuid");

        AddressUUID = RespV3Fetch.jsonPath().getJsonObject("merchantDetails.addressUuid");
        System.out.println("ADDress UIID IS" + AddressUUID);


    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 17, dependsOnMethods = "TC17_FetchLeadDetail")
    public void TC18_RBDScreen_Submit() throws Exception {

        requestPath = "MerchantService/V3/SubmitMerchant/RBDSubmitNonIndividual.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);

        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN_Private);
        SubmitPanDetailMcoObj.getProperties().setProperty("AddressUUID", AddressUUID);
        SubmitPanDetailMcoObj.getProperties().setProperty("aadharNumber", aadharNumber);


        SubmitPanDetailMcoObj.getProperties().setProperty("BUSINESS_ENTITY", "PRIVATE_LIMITED");
        SubmitPanDetailMcoObj.getProperties().setProperty("BUSINESS_TYPE", "PRIVATE_LIMITED");


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "PRIVATE_LIMITED");
        queryParams.put("leadId", leadId);


        Response createMCOLead = middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 18, dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC19_SendOtpforAgreement() throws Exception {

        requestPath = "MerchantService/V3/SendOtp/SendOtpMcoForAgreement.json";
        sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
        sendOTPobj.getProperties().setProperty("mobile", Mobile);
        sendOTPobj.getProperties().setProperty("custId", custId);
        sendOTPobj.getProperties().setProperty("lead_id", leadId);
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "PRIVATE_LIMITED");

        Response createMCOLead = middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        AgreementOTPState = createMCOLead.path("state");
        System.out.println("state is " + state);

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 19, dependsOnMethods = "TC19_SendOtpforAgreement")
    public void TC20_ValidateOtpforAgreement() throws Exception {

        requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMcoForAgreement.json";
        ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
        ValidateOtp.getProperties().setProperty("state", AgreementOTPState);
        ValidateOtp.getProperties().setProperty("otp", "888888");
        ValidateOtp.getProperties().setProperty("mobile", Mobile);
        ValidateOtp.getProperties().setProperty("leadId", leadId);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "PRIVATE_LIMITED");

        Response createMCOLead = middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }

    @Test(priority = 20, description = "To check that upload document is working", groups = {"Regression"}, dependsOnMethods = "TC20_ValidateOtpforAgreement")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC21_UploadShopPhoto() throws JSONException, Exception {

        // String endPoint = P.API.get("SubmitDocs");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "merchant_common_onboard");
        //  params.put("channel", channel);
        params.put("entityType", "PRIVATE_LIMITED");
        params.put("leadId", leadId);
        params.put("pageNo", "0");
        params.put("docCount", "0");
        params.put("docType", "shopFrontPhoto");
        params.put("docProvided", "shopFrontPhoto");
        params.put("merchantCustId", custId);
        params.put("docLat", "28.5682852");
        params.put("docLong", "77.3956855");
        params.put("docDistance", "0.3397966438531691");

        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);


        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }

    @Test(priority = 21, description = "To check that upload document is working", groups = {"Regression"}, dependsOnMethods = "TC21_UploadShopPhoto")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC22_UploadAadharPhoto1() throws JSONException, Exception {

        // String endPoint = P.API.get("SubmitDocs");


        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "merchant_common_onboard");
        //  params.put("channel", channel);
        params.put("entityType", "PRIVATE_LIMITED");
        params.put("leadId", leadId);
        params.put("pageNo", "1");
        params.put("docCount", "2");
        params.put("docType", "poi");
        params.put("docProvided", "aadhaar");
        params.put("merchantCustId", custId);
        params.put("docLat", "28.5682852");
        params.put("docLong", "77.3956855");
        params.put("docDistance", "0.3397966438531691");

        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);


        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }

    @Test(priority = 22, description = "To check that upload document is working", groups = {"Regression"}, dependsOnMethods = "TC22_UploadAadharPhoto1")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC22_UploadAadharPhoto2() throws JSONException, Exception {

        // String endPoint = P.API.get("SubmitDocs");


        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "merchant_common_onboard");
        //  params.put("channel", channel);
        params.put("entityType", "PRIVATE_LIMITED");
        params.put("leadId", leadId);
        params.put("pageNo", "2");
        params.put("docCount", "2");
        params.put("docType", "poi");
        params.put("docProvided", "aadhaar");
        params.put("merchantCustId", custId);
        params.put("docLat", "28.5682852");
        params.put("docLong", "77.3956855");
        params.put("docDistance", "0.3397966438531691");

        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);


        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }



    @Test(priority = 23, description = "To check that upload document is working", groups = {"Regression"}, dependsOnMethods = "TC22_UploadAadharPhoto1")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC23_UploadPanDoc() throws JSONException, Exception {

        // String endPoint = P.API.get("SubmitDocs");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "merchant_common_onboard");
        //  params.put("channel", channel);
        params.put("entityType", "PRIVATE_LIMITED");
        params.put("leadId", leadId);
        params.put("pageNo", "0");
        params.put("docCount", "0");
        params.put("docType", "pan");
        params.put("docProvided", "pan");
        params.put("merchantCustId", custId);
        params.put("docLat", "28.5682852");
        params.put("docLong", "77.3956855");
        params.put("docDistance", "0.3397966438531691");

        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);


        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }




    @Test(priority = 23, description = "To check that upload document is working", groups = {"Regression"}, dependsOnMethods = "TC23_UploadPanDoc")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC24_authSignatoryDeclaration_Doc() throws JSONException, Exception {

        // String endPoint = P.API.get("SubmitDocs");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "merchant_common_onboard");
        //  params.put("channel", channel);
        params.put("entityType", "PRIVATE_LIMITED");
        params.put("leadId", leadId);
        params.put("pageNo", "0");
        params.put("docCount", "0");
        params.put("docType", "authSignatoryDeclaration");
        params.put("docProvided", "authSignatoryDeclaration");
        params.put("merchantCustId", custId);
        params.put("docLat", "28.5682852");
        params.put("docLong", "77.3956855");
        params.put("docDistance", "0.3397966438531691");

        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);


        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }





    @Test(priority = 23, description = "To check that upload document is working", groups = {"Regression"}, dependsOnMethods = "TC24_authSignatoryDeclaration_Doc")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC25_franchiseeAgreement_poawtn_Doc() throws JSONException, Exception {

        // String endPoint = P.API.get("SubmitDocs");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "merchant_common_onboard");
        //  params.put("channel", channel);
        params.put("entityType", "PRIVATE_LIMITED");
        params.put("leadId", leadId);
        params.put("pageNo", "0");
        params.put("docCount", "0");
        params.put("docType", "franchiseeAgreement_poawtn");
        params.put("docProvided", "franchiseeAgreement_poawtn");
        params.put("merchantCustId", custId);
        params.put("docLat", "28.5682852");
        params.put("docLong", "77.3956855");
        params.put("docDistance", "0.3397966438531691");

        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);


        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }


    @Test(priority = 23, description = "To check that upload document is working", groups = {"Regression"}, dependsOnMethods = "TC25_franchiseeAgreement_poawtn_Doc")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC26_coi_Doc() throws JSONException, Exception {

        // String endPoint = P.API.get("SubmitDocs");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "merchant_common_onboard");
        //  params.put("channel", channel);
        params.put("entityType", "PRIVATE_LIMITED");
        params.put("leadId", leadId);
        params.put("pageNo", "0");
        params.put("docCount", "0");
        params.put("docType", "coi");
        params.put("docProvided", "COI");
        params.put("merchantCustId", custId);
        params.put("docLat", "28.5682852");
        params.put("docLong", "77.3956855");
        params.put("docDistance", "0.3397966438531691");

        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);


        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }



    @Test(priority = 23, description = "To check that upload document is working", groups = {"Regression"}, dependsOnMethods = "TC26_coi_Doc")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC27_POIAWB_Doc() throws JSONException, Exception {

        // String endPoint = P.API.get("SubmitDocs");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "merchant_common_onboard");
        //  params.put("channel", channel);
        params.put("entityType", "PRIVATE_LIMITED");
        params.put("leadId", leadId);
        params.put("pageNo", "0");
        params.put("docCount", "0");
        params.put("docType", "POIAWB");
        params.put("docProvided", "registrationCertificate_poiawb");
        params.put("merchantCustId", custId);
        params.put("docLat", "28.5682852");
        params.put("docLong", "77.3956855");
        params.put("docDistance", "0.3397966438531691");

        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);


        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }




    @Test(priority = 23, description = "To check that upload document is working", groups = {"Regression"}, dependsOnMethods = "TC27_POIAWB_Doc")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC28_businessProof_Doc() throws JSONException, Exception {

        // String endPoint = P.API.get("SubmitDocs");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "merchant_common_onboard");
        //  params.put("channel", channel);
        params.put("entityType", "PRIVATE_LIMITED");
        params.put("leadId", leadId);
        params.put("pageNo", "0");
        params.put("docCount", "0");
        params.put("docType", "businessProof");
        params.put("docProvided", "foodLicense");
        params.put("merchantCustId", custId);
        params.put("docLat", "28.5682852");
        params.put("docLong", "77.3956855");
        params.put("docDistance", "0.3397966438531691");

        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);


        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }












    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 24, dependsOnMethods = "TC28_businessProof_Doc")
    public void TC29_McoLeadSubmitAfterUploadingAllDocs() throws Exception {

        requestPath = "MerchantService/V3/SubmitMerchant/FinalMcoLeadSubmitAfterUploadingAllDocs.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("AddressUUID", AddressUUID);


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "PRIVATE_LIMITED");
        queryParams.put("leadId", leadId);

        Response createMCOLead = middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 24, dependsOnMethods = "TC29_McoLeadSubmitAfterUploadingAllDocs")
    public void TC30_McoLeadFetchDocStatus() throws Exception {

        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solution", "merchant_common_onboard");
        queryParams.put("entityType", "PRIVATE_LIMITED");
        queryParams.put("leadId", leadId);
        queryParams.put("merchantCustId", custId);
        queryParams.put("channel", "GG_APP");


        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj, headers, queryParams);

        Assert.assertEquals(respObj.getStatusCode(), 200);
        ShopphotoDMSID = respObj.path("uploadedDocDetailsSet[0].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['0']");
        AadharPhotoDMSID1 = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
        AadharPhotoDMSID2 = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['2']");
        PanphotoDMSID = respObj.path("uploadedDocDetailsSet[8].uploadedDocs[0].uuid");
        CancelledChequePhotoDMSID = respObj.path("uploadedDocDetailsSet[3].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['0']");
        AuthSignatoryDeclarationDMSID = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['0']");
        POAWTN = respObj.path("uploadedDocDetailsSet[4].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['0']");
        COI = respObj.path("uploadedDocDetailsSet[5].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['0']");
        POIAWB = respObj.path("uploadedDocDetailsSet[6].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['0']");
        businessProof = respObj.path("uploadedDocDetailsSet[7].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['0']");

        System.out.println(COI + " " + POIAWB + " " + businessProof + " " + POAWTN + " " + AuthSignatoryDeclarationDMSID + " " + CancelledChequePhotoDMSID + " " + PanphotoDMSID + " " + AadharPhotoDMSID1 + " " + AadharPhotoDMSID2 + " " + ShopphotoDMSID);

    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 24, dependsOnMethods = "TC30_McoLeadFetchDocStatus")
    public void TC31_McoNonIndividualLeadQC() throws Exception {

        DBConnection dbConnectionObj = new DBConnection();


        int Ubmid = dbConnectionObj.getUserBusinessMappingId(Mobile, "merchant_common_onboard");

        dbConnectionObj.assignAgentViaDB("1152", Ubmid);


        System.out.println("Ubmid is " + Ubmid);

        Long MCONonIndividualWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);

        System.out.println("MCONonIndividualWorkflowStatusId is " + MCONonIndividualWorkflowStatusId);

        //Save value of MCOIndividualWorkflowStatusId in string
        String wfsid = String.valueOf(MCONonIndividualWorkflowStatusId);
        System.out.println("wfsid is " + wfsid);

        requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadMcoPrivateRejection.json";
        EditLead EditLeadObj = new EditLead(leadId, requestPath);
        EditLeadObj.getProperties().setProperty("uuidAadharPhoto1", AadharPhotoDMSID1);
        EditLeadObj.getProperties().setProperty("uuidAadharPhoto2", AadharPhotoDMSID2);
        EditLeadObj.getProperties().setProperty("uuidShopPhoto", ShopphotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidBankPhoto", CancelledChequePhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidPanPhoto", PanphotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidAuthSignatoryDeclaration", AuthSignatoryDeclarationDMSID);
        EditLeadObj.getProperties().setProperty("uuidPOIAWB", POIAWB);
        EditLeadObj.getProperties().setProperty("uuidCOI", COI);
        EditLeadObj.getProperties().setProperty("uuidBusinessProof", businessProof);
       // EditLeadObj.getProperties().setProperty("uuidPOAWTN", POAWTN);

        EditLeadObj.getProperties().setProperty("workflowStatusId", wfsid);
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("action", "SUBMIT");
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        // headers.put("session_token", QCAgentsToken);
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("Cookie", XMWCookie);
        Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);

    }
}
