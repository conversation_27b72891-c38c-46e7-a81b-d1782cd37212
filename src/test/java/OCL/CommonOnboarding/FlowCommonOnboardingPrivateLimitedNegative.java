package OCL.CommonOnboarding;

import org.testng.annotations.BeforeSuite;
import org.testng.annotations.Test;

import Request.CommonOnboardingEDC.sendOTPLead;
import Request.MerchantService.v3.SendOtp;
import Request.MerchantService.v3.SubmitDocs;
import Request.MerchantService.v3.SubmitMerchant;
import Request.MerchantService.v3.ValidateOtp;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;

import java.io.FileWriter;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

public class FlowCommonOnboardingPrivateLimitedNegative extends BaseMethod {

    public int stagingServer = getCurrentStagingServer();
    public String sToken = "";
    public String QCAgentsToken = "";
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    oAuthServices oAuthServicesObj = new oAuthServices();
    Utilities utilities = new Utilities();
    String Mobile = "";
    String custId = "";
    String state = "";
    String leadId = "";
    Map<String, String> headers = new HashMap<>();
    private static final String LOG_FILE = "src/test/resources/logs/api_responses_negative.log";

    public int getCurrentStagingServer() {
        String apiUrl = P.API.get("api_url");
        if (apiUrl.contains("https://goldengate-staging7.paytm.com/MerchantService")) {
            return 7;
        } else if (apiUrl.contains("https://goldengate-staging6.paytm.com/MerchantService")) {
            return 6;
        }
        return 6;
    }

    private void logResponse(String testName, Response response) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(LOG_FILE, true))) {
            LocalDateTime now = LocalDateTime.now();
            writer.println("Test: " + testName);
            writer.println("Time: " + now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            writer.println("Status Code: " + response.getStatusCode());
            writer.println("Response Body: " + response.getBody().asString());
            writer.println("----------------------------------------");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Response executeWithRetry(String testName, Supplier<Response> apiCall) throws Exception {
        int maxRetries = 3;
        int retryCount = 0;
        Response response = null;

        while (retryCount < maxRetries) {
            response = apiCall.get();
            logResponse(testName, response);

            if (response.getStatusCode() != 500) {
                break;
            }
            retryCount++;
            if (retryCount < maxRetries) {
                Thread.sleep(1000);
            }
        }

        return response;
    }

    @BeforeSuite
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void BeforeSuiteLogin() throws Exception {
        sToken = CommonAgentToken;
        QCAgentsToken = XMWCookie;
        Mobile = utilities.randomMobileNumberGenerator();
        custId = GetResourceOwnerId(Mobile, "Paytm@123");
    }

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLogin() throws Exception {
        establishConnectiontoServer(sToken, stagingServer);
        headers.put("session_token", sToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("version", "7.2.8");
        headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
    }

    @Test(description = "Verify lead creation fails with invalid mobile number")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC01_CreateLead_InvalidMobile() throws Exception {
        Response createMCOLead = executeWithRetry("TC01_CreateLead_InvalidMobile", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", "123"); // Invalid mobile

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
       // Assert.assertTrue(createMCOLead.path("message").toString().contains("Invalid mobile number"));
    }

    @Test(description = "Verify lead creation fails with empty mobile number")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC02_CreateLead_EmptyMobile() throws Exception {
        Response createMCOLead = executeWithRetry("TC02_CreateLead_EmptyMobile", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", ""); // Empty mobile

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
       // Assert.assertTrue(createMCOLead.path("message").toString().contains("Mobile number is required"));
    }

    @Test(description = "Verify OTP validation fails with incorrect OTP")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC03_ValidateOTP_Incorrect() throws Exception {
        // First create a valid lead
        Response validLead = createValidLead();
        leadId = validLead.path("leadId");
        state = validLead.path("state");

        Response validateOTP = executeWithRetry("TC03_ValidateOTP_Incorrect", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMco.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("state", state);
            ValidateOtp.getProperties().setProperty("otp", "111111"); // Incorrect OTP
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("leadId", leadId);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(validateOTP.getStatusCode(), 412);
      //  Assert.assertTrue(validateOTP.path("message").toString().contains("Invalid OTP"));
    }

    @Test(description = "Verify bank details submission fails with invalid account number")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC04_AddBankDetail_InvalidAccount() throws Exception {
        Response createMCOLead = executeWithRetry("TC04_AddBankDetail_InvalidAccount", () -> {
            String requestPath = "MerchantService/V3/SubmitMerchant/SubmitMCOAddBankDeatil.json";
            SubmitMerchant AddBankDetail = new SubmitMerchant(custId, requestPath);
            AddBankDetail.getProperties().setProperty("bankAccountNumber", "123"); // Invalid account

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(AddBankDetail, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
      //  Assert.assertTrue(createMCOLead.path("message").toString().contains("Invalid bank account number"));
    }

    @Test(description = "Verify document upload fails with invalid document type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC05_UploadDoc_InvalidDocType() throws Exception {
        Response submitDocs = executeWithRetry("TC05_UploadDoc_InvalidDocType", () -> {
            Map<String, String> params = new HashMap<>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "PRIVATE_LIMITED");
            params.put("leadId", leadId);
            params.put("docType", "invalidDocType"); // Invalid document type
            params.put("docProvided", "pan");
            params.put("merchantCustId", custId);

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });

        Assert.assertEquals(submitDocs.getStatusCode(), 400);
      //  Assert.assertTrue(submitDocs.path("message").toString().contains("Invalid document type"));
    }

    @Test(description = "Verify lead creation fails with invalid email format")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC06_CreateLead_InvalidEmail() throws Exception {
        Response createMCOLead = executeWithRetry("TC06_CreateLead_InvalidEmail", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("email", "invalid.email@"); // Invalid email format

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify bank details submission fails with invalid IFSC code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC07_AddBankDetail_InvalidIFSC() throws Exception {
        Response createMCOLead = executeWithRetry("TC07_AddBankDetail_InvalidIFSC", () -> {
            String requestPath = "MerchantService/V3/SubmitMerchant/SubmitMCOAddBankDeatil.json";
            SubmitMerchant AddBankDetail = new SubmitMerchant(custId, requestPath);
            AddBankDetail.getProperties().setProperty("ifscCode", "INVALID123"); // Invalid IFSC

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(AddBankDetail, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify document upload fails with empty file")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC08_UploadDoc_EmptyFile() throws Exception {
        Response submitDocs = executeWithRetry("TC08_UploadDoc_EmptyFile", () -> {
            Map<String, String> params = new HashMap<>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "PRIVATE_LIMITED");
            params.put("leadId", leadId);
            params.put("docType", "PAN");
            params.put("docProvided", "pan");
            params.put("merchantCustId", custId);
            params.put("fileContent", ""); // Empty file content

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });

        Assert.assertEquals(submitDocs.getStatusCode(), 400);
    }

    @Test(description = "Verify lead creation fails with invalid pincode")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC09_CreateLead_InvalidPincode() throws Exception {
        Response createMCOLead = executeWithRetry("TC09_CreateLead_InvalidPincode", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("pincode", "12"); // Invalid pincode

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid business name")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC10_CreateLead_InvalidBusinessName() throws Exception {
        Response createMCOLead = executeWithRetry("TC10_CreateLead_InvalidBusinessName", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("businessName", ""); // Empty business name

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid GST number")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC11_CreateLead_InvalidGST() throws Exception {
        Response createMCOLead = executeWithRetry("TC11_CreateLead_InvalidGST", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("gstNumber", "INVALID123"); // Invalid GST number

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid PAN number")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC12_CreateLead_InvalidPAN() throws Exception {
        Response createMCOLead = executeWithRetry("TC12_CreateLead_InvalidPAN", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("panNumber", "INVALID12"); // Invalid PAN number

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify bank details submission fails with mismatched account holder name")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC13_AddBankDetail_MismatchedName() throws Exception {
        Response createMCOLead = executeWithRetry("TC13_AddBankDetail_MismatchedName", () -> {
            String requestPath = "MerchantService/V3/SubmitMerchant/SubmitMCOAddBankDeatil.json";
            SubmitMerchant AddBankDetail = new SubmitMerchant(custId, requestPath);
            AddBankDetail.getProperties().setProperty("accountHolderName", "Different Name"); // Mismatched name

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(AddBankDetail, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid address")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC14_CreateLead_InvalidAddress() throws Exception {
        Response createMCOLead = executeWithRetry("TC14_CreateLead_InvalidAddress", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("address", ""); // Empty address

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify document upload fails with invalid file format")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC15_UploadDoc_InvalidFormat() throws Exception {
        Response submitDocs = executeWithRetry("TC15_UploadDoc_InvalidFormat", () -> {
            Map<String, String> params = new HashMap<>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "PRIVATE_LIMITED");
            params.put("leadId", leadId);
            params.put("docType", "PAN");
            params.put("docProvided", "pan");
            params.put("merchantCustId", custId);
            params.put("fileFormat", "invalid"); // Invalid file format

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });

        Assert.assertEquals(submitDocs.getStatusCode(), 400);
    }

    @Test(description = "Verify lead creation fails with invalid state code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC16_CreateLead_InvalidState() throws Exception {
        Response createMCOLead = executeWithRetry("TC16_CreateLead_InvalidState", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("stateCode", "XX"); // Invalid state code

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with expired session token")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC17_CreateLead_ExpiredToken() throws Exception {
        Map<String, String> expiredHeaders = new HashMap<>(headers);
        expiredHeaders.put("session_token", "expired_token");

        Response createMCOLead = executeWithRetry("TC17_CreateLead_ExpiredToken", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, expiredHeaders);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid solution type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC18_CreateLead_InvalidSolutionType() throws Exception {
        Response createMCOLead = executeWithRetry("TC18_CreateLead_InvalidSolutionType", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "invalid_solution_type");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid city name")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC19_CreateLead_InvalidCity() throws Exception {
        Response createMCOLead = executeWithRetry("TC19_CreateLead_InvalidCity", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("city", "123"); // Invalid city name with numbers

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid contact person name")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC20_CreateLead_InvalidContactPerson() throws Exception {
        Response createMCOLead = executeWithRetry("TC20_CreateLead_InvalidContactPerson", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("contactPersonName", "123@#$"); // Invalid name with special characters

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify bank details submission fails with invalid bank branch")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC21_AddBankDetail_InvalidBranch() throws Exception {
        Response createMCOLead = executeWithRetry("TC21_AddBankDetail_InvalidBranch", () -> {
            String requestPath = "MerchantService/V3/SubmitMerchant/SubmitMCOAddBankDeatil.json";
            SubmitMerchant AddBankDetail = new SubmitMerchant(custId, requestPath);
            AddBankDetail.getProperties().setProperty("branchName", "!@#$%"); // Invalid branch name

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(AddBankDetail, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify document upload fails with oversized file")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC22_UploadDoc_OversizedFile() throws Exception {
        Response submitDocs = executeWithRetry("TC22_UploadDoc_OversizedFile", () -> {
            Map<String, String> params = new HashMap<>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "PRIVATE_LIMITED");
            params.put("leadId", leadId);
            params.put("docType", "PAN");
            params.put("docProvided", "pan");
            params.put("merchantCustId", custId);
            params.put("fileSize", "********"); // Oversized file (15MB)

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });

        Assert.assertEquals(submitDocs.getStatusCode(), 400);
    }

    @Test(description = "Verify lead creation fails with invalid alternate mobile number")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC23_CreateLead_InvalidAlternateMobile() throws Exception {
        Response createMCOLead = executeWithRetry("TC23_CreateLead_InvalidAlternateMobile", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("alternateMobile", "12345"); // Invalid alternate mobile

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid date format")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC24_CreateLead_InvalidDateFormat() throws Exception {
        Response createMCOLead = executeWithRetry("TC24_CreateLead_InvalidDateFormat", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("dateOfBirth", "31-13-2023"); // Invalid date format

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid business category")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC25_CreateLead_InvalidBusinessCategory() throws Exception {
        Response createMCOLead = executeWithRetry("TC25_CreateLead_InvalidBusinessCategory", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("businessCategory", "INVALID_CAT_999"); // Invalid category

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid website URL")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC26_CreateLead_InvalidWebsite() throws Exception {
        Response createMCOLead = executeWithRetry("TC26_CreateLead_InvalidWebsite", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("website", "not-a-valid-url"); // Invalid URL

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid location coordinates")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC27_CreateLead_InvalidCoordinates() throws Exception {
        Response createMCOLead = executeWithRetry("TC27_CreateLead_InvalidCoordinates", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("latitude", "999.999"); // Invalid latitude
            ValidateOtp.getProperties().setProperty("longitude", "-999.999"); // Invalid longitude

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify document upload fails with invalid document expiry date")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC28_UploadDoc_InvalidExpiryDate() throws Exception {
        Response submitDocs = executeWithRetry("TC28_UploadDoc_InvalidExpiryDate", () -> {
            Map<String, String> params = new HashMap<>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "PRIVATE_LIMITED");
            params.put("leadId", leadId);
            params.put("docType", "PAN");
            params.put("docProvided", "pan");
            params.put("merchantCustId", custId);
            params.put("expiryDate", "2022-13-45"); // Invalid expiry date

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });

        Assert.assertEquals(submitDocs.getStatusCode(), 400);
    }

    @Test(description = "Verify lead creation fails with special characters in required fields")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC29_CreateLead_SpecialCharacters() throws Exception {
        Response createMCOLead = executeWithRetry("TC29_CreateLead_SpecialCharacters", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("businessName", "Test@#$%^&*()"); // Special characters in business name

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid merchant category code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC30_CreateLead_InvalidMCC() throws Exception {
        Response createMCOLead = executeWithRetry("TC30_CreateLead_InvalidMCC", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("mcc", "99999"); // Invalid MCC code

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid tax registration number")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC31_CreateLead_InvalidTaxNumber() throws Exception {
        Response createMCOLead = executeWithRetry("TC31_CreateLead_InvalidTaxNumber", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("taxRegistrationNumber", "123ABC"); // Invalid tax number

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid annual turnover value")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC32_CreateLead_InvalidTurnover() throws Exception {
        Response createMCOLead = executeWithRetry("TC32_CreateLead_InvalidTurnover", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("annualTurnover", "-50000"); // Negative turnover value

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid currency code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC33_CreateLead_InvalidCurrency() throws Exception {
        Response createMCOLead = executeWithRetry("TC33_CreateLead_InvalidCurrency", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("currencyCode", "XXX"); // Invalid currency code

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify document upload fails with invalid file signature")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC34_UploadDoc_InvalidFileSignature() throws Exception {
        Response submitDocs = executeWithRetry("TC34_UploadDoc_InvalidFileSignature", () -> {
            Map<String, String> params = new HashMap<>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "PRIVATE_LIMITED");
            params.put("leadId", leadId);
            params.put("docType", "PAN");
            params.put("docProvided", "pan");
            params.put("merchantCustId", custId);
            params.put("fileContent", "InvalidFileSignature123"); // Invalid file signature

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });

        Assert.assertEquals(submitDocs.getStatusCode(), 400);
    }

    @Test(description = "Verify lead creation fails with mismatched business owner details")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC35_CreateLead_MismatchedOwnerDetails() throws Exception {
        Response createMCOLead = executeWithRetry("TC35_CreateLead_MismatchedOwnerDetails", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("ownerName", "John Doe");
            ValidateOtp.getProperties().setProperty("panName", "Jane Doe"); // Mismatched name with PAN

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid postal code format")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC36_CreateLead_InvalidPostalCode() throws Exception {
        Response createMCOLead = executeWithRetry("TC36_CreateLead_InvalidPostalCode", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("postalCode", "1234"); // Invalid postal code format

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with future incorporation date")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC37_CreateLead_FutureIncorporationDate() throws Exception {
        Response createMCOLead = executeWithRetry("TC37_CreateLead_FutureIncorporationDate", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("incorporationDate", "2025-12-31"); // Future date

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid business registration number")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC38_CreateLead_InvalidRegistrationNumber() throws Exception {
        Response createMCOLead = executeWithRetry("TC38_CreateLead_InvalidRegistrationNumber", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("registrationNumber", "REG123"); // Invalid format

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid director details")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC39_CreateLead_InvalidDirectorDetails() throws Exception {
        Response createMCOLead = executeWithRetry("TC39_CreateLead_InvalidDirectorDetails", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("directorDIN", "12345"); // Invalid DIN number
            ValidateOtp.getProperties().setProperty("directorName", "123"); // Invalid director name

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid business address format")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC40_CreateLead_InvalidBusinessAddress() throws Exception {
        Response createMCOLead = executeWithRetry("TC40_CreateLead_InvalidBusinessAddress", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("addressLine1", "@#$%"); // Invalid address characters
            ValidateOtp.getProperties().setProperty("addressLine2", ""); // Empty required field

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify bank details submission fails with invalid branch code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC41_AddBankDetail_InvalidBranchCode() throws Exception {
        Response createMCOLead = executeWithRetry("TC41_AddBankDetail_InvalidBranchCode", () -> {
            String requestPath = "MerchantService/V3/SubmitMerchant/SubmitMCOAddBankDeatil.json";
            SubmitMerchant AddBankDetail = new SubmitMerchant(custId, requestPath);
            AddBankDetail.getProperties().setProperty("branchCode", "ABC"); // Invalid branch code format
            AddBankDetail.getProperties().setProperty("bankCode", "XYZ"); // Invalid bank code

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(AddBankDetail, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify document upload fails with mismatched document details")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC42_UploadDoc_MismatchedDetails() throws Exception {
        Response submitDocs = executeWithRetry("TC42_UploadDoc_MismatchedDetails", () -> {
            Map<String, String> params = new HashMap<>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "PRIVATE_LIMITED");
            params.put("leadId", leadId);
            params.put("docType", "PAN");
            params.put("docProvided", "gst"); // Mismatched document type
            params.put("merchantCustId", custId);
            params.put("documentNumber", "**********"); // Mismatched document number

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });

        Assert.assertEquals(submitDocs.getStatusCode(), 400);
    }

    @Test(description = "Verify lead creation fails with invalid business sector code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC43_CreateLead_InvalidSectorCode() throws Exception {
        Response createMCOLead = executeWithRetry("TC43_CreateLead_InvalidSectorCode", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("businessSectorCode", "999999"); // Invalid sector code
            ValidateOtp.getProperties().setProperty("subSectorCode", "INVALID"); // Invalid sub-sector

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid contact details format")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC44_CreateLead_InvalidContactFormat() throws Exception {
        Response createMCOLead = executeWithRetry("TC44_CreateLead_InvalidContactFormat", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("contactEmail", "invalid.email@.com"); // Invalid email format
            ValidateOtp.getProperties().setProperty("alternateContact", "+1234"); // Invalid phone format

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with duplicate business registration")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC45_CreateLead_DuplicateRegistration() throws Exception {
        // First create a valid lead
        Response validLead = createValidLead();
        String existingBusinessId = validLead.path("businessId").toString();

        Response createMCOLead = executeWithRetry("TC45_CreateLead_DuplicateRegistration", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("businessId", existingBusinessId); // Duplicate business ID

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    @Test(description = "Verify lead creation fails with invalid shareholder information")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC46_CreateLead_InvalidShareholderInfo() throws Exception {
        Response createMCOLead = executeWithRetry("TC46_CreateLead_InvalidShareholderInfo", () -> {
            String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("shareholderName", "123"); // Invalid name format
            ValidateOtp.getProperties().setProperty("shareholderPercentage", "150"); // Invalid percentage

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "PRIVATE_LIMITED");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 412);
    }

    // Helper method to create a valid lead for dependent tests
    private Response createValidLead() throws Exception {
        String requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
        ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
        ValidateOtp.getProperties().setProperty("mobile", Mobile);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "PRIVATE_LIMITED");

        return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
    }
} 