package OCL.CommonOnboarding;

import Request.CommonOnboardingEDC.sendOTPLead;
import Request.MerchantService.v1.upgradeMid.tnc.MerchantAgreementTnCAccept;
import Request.MerchantService.v3.SubmitDocs;
import Request.MerchantService.v3.SubmitMerchant;
import Request.MerchantService.v3.ValidateOtp;
import Request.MerchantService.v3.merchant.fetch.FetchV3Merchant;
import Services.MechantService.MiddlewareServices;
import Services.UAD.UADServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeSuite;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.io.FileWriter;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

public class FlowCommonOnboardingTnCAccept extends BaseMethod {



    UADServices uadServicesObject = new UADServices();
    Faker GenerateFake = new Faker();
    String AuthorizationToken = "";

    public static String AgentToken = "6d21d26e-3e1a-4465-a101-7ee9c9697500";

    public static String CustId = "";
    public static String mobileNo = "5234633335";
    //public static String session_token = ApplicantToken(mobileNo, "paytm@123");
    public static String session_token = "";


    // Reset password to paytm@123 while generating session_token
    // In case of accepting TnC again it throws 400 which are handled in cases with priority 2. We used variable to handle those cases

    public Boolean revertCustomHandling = false;

    public Map<String,String> setCommonParams(){
        Map<String,String> queryParams = new HashMap<>();
        queryParams.put("solution","merchant_common_onboard");
        queryParams.put("entityType","INDIVIDUAL");

        queryParams.put("solutionLeadId",leadId);
        queryParams.put("channel","UMP_WEB");
        return  queryParams;
    }

    public Map<String,String> setCommonHeaders(){
        Map<String,String> headers= new HashMap<>();
        session_token = ApplicantToken(mobileNo, "paytm@123");
        headers.put("session_token",session_token);
        return headers;
    }


    // channel

    public String MCOIndividualWorkflowStatusId = "";

    public String sToken = "";
    public String QCAgentsToken = "";

    public int getCurrentStagingServer() {


        String apiUrl = P.API.get("api_url");
        if (apiUrl.contains("https://goldengate-staging7.paytm.com/MerchantService")) {
            return 7;  // staging PAN
        } else if (apiUrl.contains("https://goldengate-staging6.paytm.com/MerchantService")) {
            return 6;  // preprod PAN
        }
        return 6; // default
    }

    public int stagingServer = getCurrentStagingServer();

    String BusinessOwnerPhotoDMSID = "";
    String AadharPhotoDMSID1 = "";
    String AadharPhotoDMSID2 = "";
    String PanphotoDMSID = "";
    String CancelledChequePhotoDMSID = "";
    String ShopphotoDMSID = "";
    public String AgreementOTPState = "";
    //   String DocPath = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";
    String requestPath = "";
    String endPoint = P.API.get("SubmitDocs");

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    oAuthServices oAuthServicesObj = new oAuthServices();
    Utilities utilities = new Utilities();
    String Mobile = "";
    boolean oauthuser = utilities.createNewAuthUser(Mobile, "Paytm@123");

    public static String version = "7.2.8";

    String custId = "";

    String state = "";
    String leadId = "";
    String AddressUUID = "";

    String relatedBusinessUuid = "";
    String aadharNumber = utilities.generateRandomAadhaar();
    String PAN = utilities.generateRandomPAN("P");

    Map<String, String> headers = new HashMap<>();

    private static final String LOG_FILE = "src/test/resources/logs/api_responses.log";

    private void logResponse(String testName, Response response) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(LOG_FILE, true))) {
            LocalDateTime now = LocalDateTime.now();
            writer.println("Test: " + testName);
            writer.println("Time: " + now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            writer.println("Status Code: " + response.getStatusCode());
            writer.println("Response Body: " + response.getBody().asString());
            writer.println("----------------------------------------");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Response executeWithRetry(String testName, Supplier<Response> apiCall) throws Exception {
        int maxRetries = 5;
        int retryCount = 0;
        Response response = null;

        while (retryCount < maxRetries) {
            response = apiCall.get();
            logResponse(testName, response);

            if (response.getStatusCode() != 500) {
                break;
            }
            retryCount++;
            if (retryCount < maxRetries) {
                Thread.sleep(1000); // Wait 1 second before retry
            }
        }

        if (response.getStatusCode() == 307 && retryCount == maxRetries) {
            throw new Exception(testName + " failed after " + maxRetries + " retries");
        }

        return response;
    }

    public FlowCommonOnboardingTnCAccept() throws Exception {
    }

    @BeforeSuite
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void BeforeSuiteLogin() throws Exception {
        // CommonAgentToken = AgentSessionToken
        sToken = CommonAgentToken;
        // sToken = AgentSessionToken("8010630022", "paytm@123");

        //QCAgentsToken = AgentSessionToken("7771216290", "paytm@123");

        QCAgentsToken = XMWCookie;
        // XMWCookie = findXMWTokenforPanel("7771216290", "paytm@123");


        waitForLoad(3000);
        Mobile = utilities.randomMobileNumberGenerator();

        boolean oauthuser = utilities.createNewAuthUser(Mobile, "Paytm@123");

        System.out.println("oauthuser created: " + oauthuser);

        custId = GetResourceOwnerId(Mobile, "Paytm@123");

        System.out. println("merchant custId: " + custId);

    }



    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLogin() throws Exception {

        establishConnectiontoServer(sToken,stagingServer);


        headers.put("session_token", sToken); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("version", "7.3.0"); // No change
        headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged


    }
    @Test(retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC01_Create_Lead() throws Exception {
        Response createMCOLead = executeWithRetry("TC01_Create_Lead", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        leadId = createMCOLead.path("leadId");
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC01_Create_Lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC02_SelectProductContext() throws Exception {
        Response createMCOLead = executeWithRetry("TC02_SelectProductContext", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPSelectProductContext.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC02_SelectProductContext")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC03_SendOtp() throws Exception {
        Response createMCOLead = executeWithRetry("TC03_SendOtp", () -> {
            requestPath = "MerchantService/V3/SendOtp/SendOtpMco.json";
            sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
            sendOTPobj.getProperties().setProperty("mobile", Mobile);
            sendOTPobj.getProperties().setProperty("custId", custId);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            return middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        state = createMCOLead.path("state");
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 3, dependsOnMethods = "TC03_SendOtp")
    public void TC04_ValidateOTP() throws Exception {
        Response createMCOLead = executeWithRetry("TC04_ValidateOTP", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMco.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("state", state);
            ValidateOtp.getProperties().setProperty("otp", "888888");
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("leadId", leadId);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 4, dependsOnMethods = "TC04_ValidateOTP")
    public void TC05_AddBankDetail() throws Exception {
        Response createMCOLead = executeWithRetry("TC05_AddBankDetail", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitMCOAddBankDeatil.json";
            SubmitMerchant AddBankDetail = new SubmitMerchant(custId, requestPath);
            AddBankDetail.getProperties().setProperty("bankAccountNumber", Mobile);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(AddBankDetail, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        relatedBusinessUuid = createMCOLead.path("relatedBusinessUuid");
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 5, dependsOnMethods = "TC05_AddBankDetail")
    public void TC06_ConfirmBerau() throws Exception {
        Response createMCOLead = executeWithRetry("TC06_ConfirmBerau", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/ConfirmBearau.json";
            SubmitMerchant confirmbearu = new SubmitMerchant(custId, requestPath);
            confirmbearu.getProperties().setProperty("bankAccountNumber", Mobile);
            confirmbearu.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(confirmbearu, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC06_ConfirmBerau")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC07_AADHAR_OCR_INITIATED() throws Exception {
        Response createMCOLead = executeWithRetry("TC07_AADHAR_OCR_INITIATED", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/AadharOCRInitiate.json";
            SubmitMerchant AADHAR_OCR_INITIATED_obj = new SubmitMerchant(custId, requestPath);
            AADHAR_OCR_INITIATED_obj.getProperties().setProperty("bankAccountNumber", Mobile);
            AADHAR_OCR_INITIATED_obj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(AADHAR_OCR_INITIATED_obj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC07_AADHAR_OCR_INITIATED")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC08_SubmitAadharOCRDetails() throws Exception {
        Response createMCOLead = executeWithRetry("TC08_SubmitAadharOCRDetails", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitAadharOCRDetails.json";
            SubmitMerchant SubmitAadharOCRDetailsObj = new SubmitMerchant(custId, requestPath);
            SubmitAadharOCRDetailsObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitAadharOCRDetailsObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitAadharOCRDetailsObj.getProperties().setProperty("aadharNumber", aadharNumber);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitAadharOCRDetailsObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC08_SubmitAadharOCRDetails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC09_SubmitPanDetail() throws Exception {
        Response createMCOLead = executeWithRetry("TC09_SubmitPanDetail", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitPanDetailMco.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC09_SubmitPanDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC10_SubmitGSTNSkippedMCO() throws Exception {
        Response createMCOLead = executeWithRetry("TC10_SubmitGSTNSkippedMCO", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitGSTNSkippedMCO.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC10_SubmitGSTNSkippedMCO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC11_uploadBankProofDocument() throws Exception {
        Response submitDocs = executeWithRetry("TC11_uploadBankProofDocument", () -> {
            Map<String, String> params = new HashMap<String, String>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "INDIVIDUAL");
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "0");
            params.put("docCount", "0");
            params.put("docType", "bankProof");
            params.put("docProvided", "cancelledChequePhoto");
            params.put("merchantCustId", custId);
            params.put("type", "jpg");
            params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC11_uploadBankProofDocument")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC12_UploadBusinessOwnerPhoto() throws Exception {
        Response submitDocs = executeWithRetry("TC12_UploadBusinessOwnerPhoto", () -> {
            Map<String, String> params = new HashMap<String, String>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "INDIVIDUAL");
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "0");
            params.put("docCount", "0");
            params.put("docType", "bankProof");
            params.put("docProvided", "businessOwnerPhoto");
            params.put("merchantCustId", custId);
            params.put("type", "jpg");
            params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC12_UploadBusinessOwnerPhoto")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC13_MERCHANT_PHOTO_SKIPPED_AFTER_EXHAUSTING_ATTEMPTS() throws Exception {
        Response createMCOLead = executeWithRetry("TC13_MERCHANT_PHOTO_SKIPPED_AFTER_EXHAUSTING_ATTEMPTS", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/MerchantPhotoSkippedAfterAllAtempts.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC13_MERCHANT_PHOTO_SKIPPED_AFTER_EXHAUSTING_ATTEMPTS")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC14_qna_submit() throws Exception {
        Response createMCOLead = executeWithRetry("TC14_qna_submit", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/QNA.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC14_qna_submit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC15_SegmentSubSegmentSubmit() throws Exception {
        Response createMCOLead = executeWithRetry("TC15_SegmentSubSegmentSubmit", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SegmentSubSegmentSubmit.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC15_SegmentSubSegmentSubmit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC16_UAC_Submit() throws Exception {
        Response createMCOLead = executeWithRetry("TC16_UAC_Submit", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/UACSubmit.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC16_UAC_Submit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC17_FetchLeadDetail() throws Exception {
        Response RespV3Fetch = executeWithRetry("TC17_FetchLeadDetail", () -> {
            FetchV3Merchant v3Merch = new FetchV3Merchant(leadId);
            Map<String, String> query = new HashMap<>();
            query.put("entityType", "INDIVIDUAL");
            query.put("solutionType", "merchant_common_onboard");
            query.put("custId", custId);

            return middlewareServicesObject.FetchV3MerchantLead(v3Merch, query, headers);
        });
        AddressUUID = RespV3Fetch.jsonPath().getJsonObject("merchantDetails.addressUuid");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC17_FetchLeadDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC18_RBDScreen_Submit() throws Exception {

        requestPath = "MerchantService/V3/SubmitMerchant/RBDSubmit.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);

        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);
        SubmitPanDetailMcoObj.getProperties().setProperty("AddressUUID", AddressUUID);


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);


        Response createMCOLead = middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }

    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC01_TnCAccept_NoChannel(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        queryParams.remove("channel");
        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);

        Assert.assertEquals(400,responseObj.getStatusCode());

    }

    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC01_TnCAccept_WrongChannel(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        queryParams.put("channel","wrong_channel");
        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);

        String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage= responseDisplayMessage.equals("Tnc save is not allowed at this point.");
        Boolean correctResponseCode = 200 == responseObj.getStatusCode();
        if(revertCustomHandling){
            correctResponseMessage = false;
        }
        Assert.assertEquals(true,correctResponseCode || correctResponseMessage);



    }

    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC01_TnCAccept_DifferentChannel(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        queryParams.put("channel","GG_APP");
        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);

        String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage= responseDisplayMessage.equals("Tnc save is not allowed at this point.");
        Boolean correctResponseCode = 200 == responseObj.getStatusCode();
        if(revertCustomHandling){
            correctResponseMessage = false;
        }
        Assert.assertEquals(true,correctResponseCode || correctResponseMessage);



    }

    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC01_TnCAccept_EmptyChannel(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        queryParams.put("channel","");
        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);

        String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage= responseDisplayMessage.equals("Tnc save is not allowed at this point.");
        Boolean correctResponseCode = 200 == responseObj.getStatusCode();
        if(revertCustomHandling){
            correctResponseMessage = false;
        }
        Assert.assertEquals(true,correctResponseCode || correctResponseMessage);



    }

    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC01_TnCAccept_AgainAccepting(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();

        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);

        String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage= responseDisplayMessage.equals("Tnc save is not allowed at this point.");
        Boolean correctResponseCode= 400 == responseObj.getStatusCode();

        Assert.assertEquals(true,correctResponseMessage && correctResponseCode);

    }


//solution lead id


    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC02_TnCAccept_NoLeadId(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        queryParams.remove("solutionLeadId");
        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);
        String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage= responseDisplayMessage.equals("Invalid request parameters.");
        Boolean correctResponseCode= 400 == responseObj.getStatusCode();

        Assert.assertEquals(true,correctResponseMessage && correctResponseCode);


    }



    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC02_TnCAccept_WrongLeadId(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        queryParams.put("solutionLeadId","wrong_lead_id");
        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);
        String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage= responseDisplayMessage.equals("We are unable to process your request. Please try again after sometime to continue.");
        Boolean correctResponseCode= 404 == responseObj.getStatusCode();

        Assert.assertEquals(true,correctResponseMessage && correctResponseCode);


    }


    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC02_TnCAccept_DifferentLeadId(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        queryParams.put("solutionLeadId","b531d1c9-6b6b-4abf-800c-e2c5082ca92f");
        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);
        String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage= responseDisplayMessage.equals("We are unable to process your request. Please try again after sometime to continue.");
        Boolean correctResponseCode= 404 == responseObj.getStatusCode();

        Assert.assertEquals(true,correctResponseMessage && correctResponseCode);


    }



    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC02_TnCAccept_EmptytLeadId(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        queryParams.put("solutionLeadId","");
        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);
        String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage= responseDisplayMessage.equals("Invalid request parameters.");
        Boolean correctResponseCode= 400 == responseObj.getStatusCode();

        Assert.assertEquals(true,correctResponseMessage && correctResponseCode);


    }


//entityType


    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC03_TnCAccept_NoEntityType(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        queryParams.remove("entityType");
        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);
        String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage= responseDisplayMessage.equals("Invalid request parameters.");
        Boolean correctResponseCode= 400 == responseObj.getStatusCode();

        Assert.assertEquals(true,correctResponseMessage && correctResponseCode);

    }

    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC03_TnCAccept_WrongEntityType(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        queryParams.put("entityType","wrong_type");
        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);

        String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage= responseDisplayMessage.equals("Tnc save is not allowed at this point.");
        Boolean correctResponseCode = 500 == responseObj.getStatusCode();
        if(revertCustomHandling){
            correctResponseMessage = false;
        }
        Assert.assertEquals(true,correctResponseCode || correctResponseMessage);

    }


    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC03_TnCAccept_DifferentEntityType(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        queryParams.put("entityType","PUBLIC_LIMITED");
        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);

        String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage= responseDisplayMessage.equals("Tnc save is not allowed at this point.");
        Boolean correctResponseCode = 200 == responseObj.getStatusCode();
        if(revertCustomHandling){
            correctResponseMessage = false;
        }
        Assert.assertEquals(true,correctResponseCode || correctResponseMessage);

    }

    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC03_TnCAccept_EmptyEntityType() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, String> queryParams = setCommonParams();
        queryParams.put("entityType", "");
        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj = middlewareServicesObject.merchantAgreementTnCAccept(LeadObj, queryParams, headers);
        String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage = responseDisplayMessage.equals("Invalid request parameters.");
        Boolean correctResponseCode= 400 == responseObj.getStatusCode();

        Assert.assertEquals(true,correctResponseMessage && correctResponseCode);
    }
//solution type

    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
        public void TC03_TnCAccept_NoSolutionType(){
            Map<String,String> headers=setCommonHeaders();
            Map<String,String> queryParams=setCommonParams();
            queryParams.remove("solution");
            MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
            Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);

            Assert.assertEquals(400,responseObj.getStatusCode());
        }

    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
        public void TC03_TnCAccept_EmptySolutionType(){
            Map<String,String> headers=setCommonHeaders();
            Map<String,String> queryParams=setCommonParams();
            queryParams.put("solution","");
            MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
            Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);

        String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage= responseDisplayMessage.equals("Tnc save is not allowed at this point.");
        Boolean correctResponseCode = 200 == responseObj.getStatusCode();
        if(revertCustomHandling){
            correctResponseMessage = false;
        }
        Assert.assertEquals(true,correctResponseCode || correctResponseMessage);



        }


    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
        public void TC04_TnCAccept_differentSolutionType(){
            Map<String,String> headers=setCommonHeaders();
            Map<String,String> queryParams=setCommonParams();
            queryParams.put("solution","qr_merchant");
            MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
            Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);

        String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage= responseDisplayMessage.equals("Tnc save is not allowed at this point.");
        Boolean correctResponseCode = 200 == responseObj.getStatusCode();
        if(revertCustomHandling){
            correctResponseMessage = false;
        }
        Assert.assertEquals(true,correctResponseCode || correctResponseMessage);
        }


    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
        public void TC04_TnCAccept_WrongSolutionType(){
            Map<String,String> headers=setCommonHeaders();
            Map<String,String> queryParams=setCommonParams();
            queryParams.put("solution","wrong_solutiont");
            MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
            Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);

            String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage= responseDisplayMessage.equals("Tnc save is not allowed at this point.");
        Boolean correctResponseCode = 200 == responseObj.getStatusCode();
        if(revertCustomHandling){
            correctResponseMessage = false;
        }
        Assert.assertEquals(true,correctResponseCode || correctResponseMessage);


        }


// session token

    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
        public void TC05_TnCAccept_NoSessionToken(){
            Map<String,String> headers=setCommonHeaders();
            Map<String,String> queryParams=setCommonParams();
            headers.remove("session_token");
            MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
            Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);
            Assert.assertEquals(401,responseObj.getStatusCode());

        }



    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
        public void TC05_TnCAccept_EmptySessionToken(){
            Map<String,String> headers=setCommonHeaders();
            Map<String,String> queryParams=setCommonParams();
            headers.put("session_token","");
            MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
            Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);
            Assert.assertEquals(401,responseObj.getStatusCode());
        }

    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
        public void TC05_TnCAccept_WrongSessionToken(){
            Map<String,String> headers=setCommonHeaders();
            Map<String,String> queryParams=setCommonParams();
            headers.put("session_token", "wrong_token");
            MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
            Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);
            Assert.assertEquals(401,responseObj.getStatusCode());
        }


    @Test(priority = 2, dependsOnMethods = "TC18_RBDScreen_Submit")
        public void TC06_TnCAccept_Positive(){
            Map<String,String> headers=setCommonHeaders();
            Map<String,String> queryParams=setCommonParams();
            MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
            Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);
        String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage= responseDisplayMessage.equals("Tnc save is not allowed at this point.");
        Boolean correctResponseCode = 200 == responseObj.getStatusCode();
        if(revertCustomHandling){
            correctResponseMessage = false;
        }

        Assert.assertEquals(true,correctResponseCode || correctResponseMessage);
//        Boolean flag = (responseObj.getStatusCode() == 400) || (responseObj.getStatusCode() == 200);
//        Assert.assertTrue( flag);
        }


    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC05N_TnCAccept_NoSessionToken(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        headers.remove("session_token");
        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);
        Assert.assertEquals(401,responseObj.getStatusCode());

    }



    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC05N_TnCAccept_EmptySessionToken(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        headers.put("session_token","");
        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);
        Assert.assertEquals(401,responseObj.getStatusCode());
    }

    @Test(dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC05N_TnCAccept_WrongSessionToken(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        headers.put("session_token", "wrong_token");
        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);
        Assert.assertEquals(401,responseObj.getStatusCode());
    }


    @Test(priority = 2, dependsOnMethods = "TC18_RBDScreen_Submit")
    public void TC06N_TnCAccept_Positive(){
        Map<String,String> headers=setCommonHeaders();
        Map<String,String> queryParams=setCommonParams();
        MerchantAgreementTnCAccept LeadObj = new MerchantAgreementTnCAccept();
        Response responseObj= middlewareServicesObject.merchantAgreementTnCAccept(LeadObj,queryParams,headers);
        String responseDisplayMessage = responseObj.jsonPath().getString("displayMessage");
        Boolean correctResponseMessage= responseDisplayMessage.equals("Tnc save is not allowed at this point.");
        Boolean correctResponseCode = 200 == responseObj.getStatusCode();
        if(revertCustomHandling){
            correctResponseMessage = false;
        }

        Assert.assertEquals(true,correctResponseCode || correctResponseMessage);
//        Boolean flag = (responseObj.getStatusCode() == 400) || (responseObj.getStatusCode() == 200);
//        Assert.assertTrue( flag);
    }


}
