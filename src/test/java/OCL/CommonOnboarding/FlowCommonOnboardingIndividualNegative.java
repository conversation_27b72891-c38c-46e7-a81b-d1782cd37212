package OCL.CommonOnboarding;

import org.testng.annotations.BeforeSuite;
import org.testng.annotations.Test;

import Request.CommonOnboardingEDC.sendOTPLead;
import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.Resources.GetallResources;
import Request.MerchantService.v1.Revisit.LoanIntentfetchDocumentDetails;
import Request.MerchantService.v3.SendOtp;
import Request.MerchantService.v3.SubmitDocs;
import Request.MerchantService.v3.SubmitMerchant;
import Request.MerchantService.v3.ValidateOtp;
import Request.MerchantService.v3.merchant.fetch.FetchV3Merchant;
import Request.SoundBox.SendOTPV1;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;

import com.github.javafaker.File;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import io.restassured.specification.RequestSpecification;
import org.apache.commons.lang.ObjectUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.IRetryAnalyzer;
import org.testng.ITestResult;
import org.testng.annotations.BeforeTest;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;


public class FlowCommonOnboardingIndividualNegative extends BaseMethod {



    public String MCOIndividualWorkflowStatusId = "";

    public String sToken = "";
    public String QCAgentsToken = "";

    public int stagingServer = getCurrentStagingServer();

    public int getCurrentStagingServer() {


        String apiUrl = P.API.get("api_url");
        if (apiUrl.contains("https://goldengate-staging7.paytm.com/MerchantService")) {
            return 7;  // staging PAN
        } else if (apiUrl.contains("https://goldengate-staging6.paytm.com/MerchantService")) {
            return 6;  // preprod PAN
        }
        return 6; // default
    }

    String BusinessOwnerPhotoDMSID = "";
    String AadharPhotoDMSID1 = "";
    String AadharPhotoDMSID2 = "";
    String PanphotoDMSID = "";
    String CancelledChequePhotoDMSID = "";
    String ShopphotoDMSID = "";
    public String AgreementOTPState = "";
    //   String DocPath = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";
    String requestPath = "";
    String endPoint = P.API.get("SubmitDocs");

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    oAuthServices oAuthServicesObj = new oAuthServices();
    Utilities utilities = new Utilities();
    String Mobile = "";
    boolean oauthuser = utilities.createNewAuthUser(Mobile, "Paytm@123");

    public static String version = "7.2.8";

    String custId = "";

    String state = "";
    String leadId = "";
    String AddressUUID = "";

    String relatedBusinessUuid = "";
    String aadharNumber = utilities.generateRandomAadhaar();
    String PAN = utilities.generateRandomPAN("P");

    Map<String, String> headers = new HashMap<>();

    private static final String LOG_FILE = "src/test/resources/logs/api_responses.log";

    private void logResponse(String testName, Response response) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(LOG_FILE, true))) {
            LocalDateTime now = LocalDateTime.now();
            writer.println("Test: " + testName);
            writer.println("Time: " + now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            writer.println("Status Code: " + response.getStatusCode());
            writer.println("Response Body: " + response.getBody().asString());
            writer.println("----------------------------------------");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Response executeWithRetry(String testName, Supplier<Response> apiCall) throws Exception {
        int maxRetries = 5;
        int retryCount = 0;
        Response response = null;

        while (retryCount < maxRetries) {
            response = apiCall.get();
            logResponse(testName, response);

            if (response.getStatusCode() != 500) {
                break;
            }
            retryCount++;
            if (retryCount < maxRetries) {
                Thread.sleep(1000); // Wait 1 second before retry
            }
        }

        if (response.getStatusCode() == 307 && retryCount == maxRetries) {
            throw new Exception(testName + " failed after " + maxRetries + " retries");
        }

        return response;
    }

    public FlowCommonOnboardingIndividualNegative() throws Exception {
    }

    @BeforeSuite
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void BeforeSuiteLogin() throws Exception {
        // CommonAgentToken = AgentSessionToken
        sToken = CommonAgentToken;
        // sToken = AgentSessionToken("8010630022", "paytm@123");

        //QCAgentsToken = AgentSessionToken("7771216290", "paytm@123");

        QCAgentsToken = XMWCookie;
        // XMWCookie = findXMWTokenforPanel("7771216290", "paytm@123");


        waitForLoad(3000);
        Mobile = utilities.randomMobileNumberGenerator();

        boolean oauthuser = utilities.createNewAuthUser(Mobile, "Paytm@123");

        custId = GetResourceOwnerId(Mobile, "Paytm@123");
    }



    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLogin() throws Exception {

        establishConnectiontoServer(sToken,stagingServer);


        headers.put("session_token", sToken); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("version", "7.3.0"); // No change
        headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged


    }


    @Test(retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC01_Create_Lead_Invalid_Mobile() throws Exception {
        Response createMCOLead = executeWithRetry("TC01_Create_Lead_Invalid_Mobile", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", "123"); // Invalid mobile number
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

            Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid mobile number format");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC01_Create_Lead_Invalid_EntityType() throws Exception {
        Response createMCOLead = executeWithRetry("TC01_Create_Lead_Invalid_EntityType", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INVALID_TYPE"); // Invalid entity type
            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
      //  Assert.assertEquals(createMCOLead.path("message"), "Invalid entity type");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC01_Create_Lead_Invalid_SolutionType() throws Exception {
        Response createMCOLead = executeWithRetry("TC01_Create_Lead_Invalid_SolutionType", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "invalid_solution"); // Invalid solution type
            queryParams.put("entityType", "INDIVIDUAL");
            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
      //  Assert.assertEquals(createMCOLead.path("message"), "Invalid solution type");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC01_Create_Lead_Missing_QueryParams() throws Exception {
        Response createMCOLead = executeWithRetry("TC01_Create_Lead_Missing_QueryParams", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            Map<String, String> queryParams = new HashMap<>();
            // Missing required query parameters
            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
      //  Assert.assertEquals(createMCOLead.path("message"), "Missing required query parameters");
    }


    @Test(retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC01_Create_Lead() throws Exception {
        Response createMCOLead = executeWithRetry("TC01_Create_Lead", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        leadId = createMCOLead.path("leadId");
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC01_Create_Lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC02_SelectProductContext() throws Exception {
        Response createMCOLead = executeWithRetry("TC02_SelectProductContext", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPSelectProductContext.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    }


    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC01_Create_Lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC02_SelectProductContext_Invalid_Mobile() throws Exception {
        Response createMCOLead = executeWithRetry("TC02_SelectProductContext_Invalid_Mobile", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPSelectProductContext.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", "invalid_mobile"); // Invalid mobile
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
       // Assert.assertEquals(createMCOLead.path("message"), "Invalid mobile number");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC01_Create_Lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC02_SelectProductContext_Unregistered_Mobile() throws Exception {
        Response createMCOLead = executeWithRetry("TC02_SelectProductContext_Unregistered_Mobile", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPSelectProductContext.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", "**********"); // Unregistered mobile
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Mobile number not registered");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC01_Create_Lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC02_SelectProductContext_Invalid_Solution_Type() throws Exception {
        Response createMCOLead = executeWithRetry("TC02_SelectProductContext_Invalid_Solution_Type", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPSelectProductContext.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "invalid_solution_type"); // Invalid solution type
            queryParams.put("entityType", "INDIVIDUAL");
            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
       // Assert.assertEquals(createMCOLead.path("message"), "Invalid solution type");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC01_Create_Lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC02_SelectProductContext_Missing_Parameters() throws Exception {
        Response createMCOLead = executeWithRetry("TC02_SelectProductContext_Missing_Parameters", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPSelectProductContext.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            // Not setting any properties
            Map<String, String> queryParams = new HashMap<>();
            // Not adding any query parameters
            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
      //  Assert.assertEquals(createMCOLead.path("message"), "Required parameters missing");
    }


    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC02_SelectProductContext")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC03_SendOtp() throws Exception {
        Response createMCOLead = executeWithRetry("TC03_SendOtp", () -> {
            requestPath = "MerchantService/V3/SendOtp/SendOtpMco.json";
            sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
            sendOTPobj.getProperties().setProperty("mobile", Mobile);
            sendOTPobj.getProperties().setProperty("custId", custId);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            return middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        state = createMCOLead.path("state");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC02_SelectProductContext")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC03_SendOtp_Invalid_Mobile() throws Exception {
        Response createMCOLead = executeWithRetry("TC03_SendOtp_Invalid_Mobile", () -> {
            requestPath = "MerchantService/V3/SendOtp/SendOtpMco.json";
            sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
            sendOTPobj.getProperties().setProperty("mobile", "123"); // Invalid mobile
            sendOTPobj.getProperties().setProperty("custId", custId);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            return middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 400);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid mobile number format");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC02_SelectProductContext")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC03_SendOtp_Invalid_CustId() throws Exception {
        Response createMCOLead = executeWithRetry("TC03_SendOtp_Invalid_CustId", () -> {
            requestPath = "MerchantService/V3/SendOtp/SendOtpMco.json";
            sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
            sendOTPobj.getProperties().setProperty("mobile", Mobile);
            sendOTPobj.getProperties().setProperty("custId", "invalid_cust_id");

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            return middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid customer ID");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC02_SelectProductContext")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC03_SendOtp_Missing_Required_Fields() throws Exception {
        Response createMCOLead = executeWithRetry("TC03_SendOtp_Missing_Required_Fields", () -> {
            requestPath = "MerchantService/V3/SendOtp/SendOtpMco.json";
            sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
            // Not setting any properties

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            return middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 400);
        //Assert.assertEquals(createMCOLead.path("message"), "Required fields missing");
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 3, dependsOnMethods = "TC03_SendOtp")
    public void TC04_ValidateOTP() throws Exception {
        Response createMCOLead = executeWithRetry("TC04_ValidateOTP", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMco.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("state", state);
            ValidateOtp.getProperties().setProperty("otp", "888888");
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("leadId", leadId);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    }

    @Test(description = "Validate OTP with incorrect OTP", priority = 3, dependsOnMethods = "TC03_SendOtp")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC04_ValidateOTP_Incorrect_OTP() throws Exception {
        Response createMCOLead = executeWithRetry("TC04_ValidateOTP_Incorrect_OTP", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMco.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("state", state);
            ValidateOtp.getProperties().setProperty("otp", "111111"); // Incorrect OTP
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("leadId", leadId);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid OTP");
    }

    @Test(description = "Validate OTP with expired OTP", priority = 3, dependsOnMethods = "TC03_SendOtp")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC04_ValidateOTP_Expired_OTP() throws Exception {
        // Wait for OTP to expire (assuming 5 minutes expiry)
        Thread.sleep(300000); // 5 minutes

        Response createMCOLead = executeWithRetry("TC04_ValidateOTP_Expired_OTP", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMco.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("state", state);
            ValidateOtp.getProperties().setProperty("otp", "888888");
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("leadId", leadId);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "OTP has expired");
    }

    @Test(description = "Validate OTP with invalid state", priority = 3, dependsOnMethods = "TC03_SendOtp")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC04_ValidateOTP_Invalid_State() throws Exception {
        Response createMCOLead = executeWithRetry("TC04_ValidateOTP_Invalid_State", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMco.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("state", "invalid_state");
            ValidateOtp.getProperties().setProperty("otp", "888888");
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("leadId", leadId);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid state");
    }

    @Test(description = "Validate OTP with invalid leadId", priority = 3, dependsOnMethods = "TC03_SendOtp")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC04_ValidateOTP_Invalid_LeadId() throws Exception {
        Response createMCOLead = executeWithRetry("TC04_ValidateOTP_Invalid_LeadId", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMco.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("state", state);
            ValidateOtp.getProperties().setProperty("otp", "888888");
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("leadId", "invalid_lead_id");

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid lead ID");
    }

    @Test(description = "Validate OTP with missing required fields", priority = 3, dependsOnMethods = "TC03_SendOtp")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC04_ValidateOTP_Missing_Fields() throws Exception {
        Response createMCOLead = executeWithRetry("TC04_ValidateOTP_Missing_Fields", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMco.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            // Not setting any properties

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Required fields missing");
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 4, dependsOnMethods = "TC04_ValidateOTP")
    public void TC05_AddBankDetail() throws Exception {
        Response createMCOLead = executeWithRetry("TC05_AddBankDetail", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitMCOAddBankDeatil.json";
            SubmitMerchant AddBankDetail = new SubmitMerchant(custId, requestPath);
            AddBankDetail.getProperties().setProperty("bankAccountNumber", Mobile);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(AddBankDetail, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        relatedBusinessUuid = createMCOLead.path("relatedBusinessUuid");
    }

    @Test(description = "Add Bank Detail with Invalid Account Number", priority = 4, dependsOnMethods = "TC04_ValidateOTP")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC05_AddBankDetail_Invalid_Account() throws Exception {
        Response createMCOLead = executeWithRetry("TC05_AddBankDetail_Invalid_Account", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitMCOAddBankDeatil.json";
            SubmitMerchant AddBankDetail = new SubmitMerchant(custId, requestPath);
            AddBankDetail.getProperties().setProperty("bankAccountNumber", "123"); // Invalid account number

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(AddBankDetail, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid bank account number");
    }

    @Test(description = "Add Bank Detail with Invalid Customer ID", priority = 4, dependsOnMethods = "TC04_ValidateOTP")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC05_AddBankDetail_Invalid_CustId() throws Exception {
        Response createMCOLead = executeWithRetry("TC05_AddBankDetail_Invalid_CustId", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitMCOAddBankDeatil.json";
            SubmitMerchant AddBankDetail = new SubmitMerchant("invalid_cust_id", requestPath);
            AddBankDetail.getProperties().setProperty("bankAccountNumber", Mobile);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(AddBankDetail, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid customer ID");
    }

    @Test(description = "Add Bank Detail with Invalid Lead ID", priority = 4, dependsOnMethods = "TC04_ValidateOTP")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC05_AddBankDetail_Invalid_LeadId() throws Exception {
        Response createMCOLead = executeWithRetry("TC05_AddBankDetail_Invalid_LeadId", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitMCOAddBankDeatil.json";
            SubmitMerchant AddBankDetail = new SubmitMerchant(custId, requestPath);
            AddBankDetail.getProperties().setProperty("bankAccountNumber", Mobile);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", "invalid_lead_id");

            return middlewareServicesObject.AddBankZDetail(AddBankDetail, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 400);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid lead ID");
    }

    @Test(description = "Add Bank Detail with Missing Required Fields", priority = 4, dependsOnMethods = "TC04_ValidateOTP")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC05_AddBankDetail_Missing_Fields() throws Exception {
        Response createMCOLead = executeWithRetry("TC05_AddBankDetail_Missing_Fields", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitMCOAddBankDeatil.json";
            SubmitMerchant AddBankDetail = new SubmitMerchant(custId, requestPath);
            // Not setting any properties

            Map<String, String> queryParams = new HashMap<>();
            // Not setting any query parameters

            return middlewareServicesObject.AddBankZDetail(AddBankDetail, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 400);
        //Assert.assertEquals(createMCOLead.path("message"), "Required fields missing");
    }

    @Test(description = "Add Bank Detail with Invalid Solution Type", priority = 4, dependsOnMethods = "TC04_ValidateOTP")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC05_AddBankDetail_Invalid_SolutionType() throws Exception {
        Response createMCOLead = executeWithRetry("TC05_AddBankDetail_Invalid_SolutionType", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitMCOAddBankDeatil.json";
            SubmitMerchant AddBankDetail = new SubmitMerchant(custId, requestPath);
            AddBankDetail.getProperties().setProperty("bankAccountNumber", Mobile);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "invalid_solution_type");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(AddBankDetail, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid solution type");
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 5, dependsOnMethods = "TC05_AddBankDetail")
    public void TC06_ConfirmBerau() throws Exception {
        Response createMCOLead = executeWithRetry("TC06_ConfirmBerau", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/ConfirmBearau.json";
            SubmitMerchant confirmbearu = new SubmitMerchant(custId, requestPath);
            confirmbearu.getProperties().setProperty("bankAccountNumber", Mobile);
            confirmbearu.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(confirmbearu, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }

    @Test(description = "Confirm Bureau with Invalid Business UUID", priority = 5, dependsOnMethods = "TC05_AddBankDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC06_ConfirmBerau_Invalid_BusinessUUID() throws Exception {
        Response createMCOLead = executeWithRetry("TC06_ConfirmBerau_Invalid_BusinessUUID", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/ConfirmBearau.json";
            SubmitMerchant confirmbearu = new SubmitMerchant(custId, requestPath);
            confirmbearu.getProperties().setProperty("bankAccountNumber", Mobile);
            confirmbearu.getProperties().setProperty("relatedBusinessUuid", "invalid_uuid");

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(confirmbearu, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid business UUID");
    }

    @Test(description = "Confirm Bureau with Mismatched Bank Account", priority = 5, dependsOnMethods = "TC05_AddBankDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC06_ConfirmBerau_Mismatched_BankAccount() throws Exception {
        Response createMCOLead = executeWithRetry("TC06_ConfirmBerau_Mismatched_BankAccount", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/ConfirmBearau.json";
            SubmitMerchant confirmbearu = new SubmitMerchant(custId, requestPath);
            confirmbearu.getProperties().setProperty("bankAccountNumber", "**********"); // Different from original
            confirmbearu.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(confirmbearu, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Bank account number does not match previous submission");
    }

    @Test(description = "Confirm Bureau with Missing Required Fields", priority = 5, dependsOnMethods = "TC05_AddBankDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC06_ConfirmBerau_Missing_Fields() throws Exception {
        Response createMCOLead = executeWithRetry("TC06_ConfirmBerau_Missing_Fields", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/ConfirmBearau.json";
            SubmitMerchant confirmbearu = new SubmitMerchant(custId, requestPath);
            // Not setting any properties

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(confirmbearu, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 400);
        //Assert.assertEquals(createMCOLead.path("message"), "Required fields missing");
    }

    @Test(description = "Confirm Bureau with Invalid Customer ID", priority = 5, dependsOnMethods = "TC05_AddBankDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC06_ConfirmBerau_Invalid_CustomerId() throws Exception {
        Response createMCOLead = executeWithRetry("TC06_ConfirmBerau_Invalid_CustomerId", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/ConfirmBearau.json";
            SubmitMerchant confirmbearu = new SubmitMerchant("invalid_cust_id", requestPath);
            confirmbearu.getProperties().setProperty("bankAccountNumber", Mobile);
            confirmbearu.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(confirmbearu, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid customer ID");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC06_ConfirmBerau")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC07_AADHAR_OCR_INITIATED() throws Exception {
        Response createMCOLead = executeWithRetry("TC07_AADHAR_OCR_INITIATED", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/AadharOCRInitiate.json";
            SubmitMerchant AADHAR_OCR_INITIATED_obj = new SubmitMerchant(custId, requestPath);
            AADHAR_OCR_INITIATED_obj.getProperties().setProperty("bankAccountNumber", Mobile);
            AADHAR_OCR_INITIATED_obj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(AADHAR_OCR_INITIATED_obj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }


    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC06_ConfirmBerau")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC07_AADHAR_OCR_INITIATED_Invalid_UUID() throws Exception {
        Response createMCOLead = executeWithRetry("TC07_AADHAR_OCR_INITIATED_Invalid_UUID", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/AadharOCRInitiate.json";
            SubmitMerchant AADHAR_OCR_INITIATED_obj = new SubmitMerchant(custId, requestPath);
            AADHAR_OCR_INITIATED_obj.getProperties().setProperty("bankAccountNumber", Mobile);
            AADHAR_OCR_INITIATED_obj.getProperties().setProperty("relatedBusinessUuid", "invalid_uuid");

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(AADHAR_OCR_INITIATED_obj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid business UUID provided");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC06_ConfirmBerau")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC07_AADHAR_OCR_INITIATED_Invalid_LeadId() throws Exception {
        Response createMCOLead = executeWithRetry("TC07_AADHAR_OCR_INITIATED_Invalid_LeadId", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/AadharOCRInitiate.json";
            SubmitMerchant AADHAR_OCR_INITIATED_obj = new SubmitMerchant(custId, requestPath);
            AADHAR_OCR_INITIATED_obj.getProperties().setProperty("bankAccountNumber", Mobile);
            AADHAR_OCR_INITIATED_obj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", "invalid_lead_id");

            return middlewareServicesObject.AddBankZDetail(AADHAR_OCR_INITIATED_obj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 400);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid lead ID provided");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC06_ConfirmBerau")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC07_AADHAR_OCR_INITIATED_Invalid_CustId() throws Exception {
        Response createMCOLead = executeWithRetry("TC07_AADHAR_OCR_INITIATED_Invalid_CustId", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/AadharOCRInitiate.json";
            SubmitMerchant AADHAR_OCR_INITIATED_obj = new SubmitMerchant("invalid_cust_id", requestPath);
            AADHAR_OCR_INITIATED_obj.getProperties().setProperty("bankAccountNumber", Mobile);
            AADHAR_OCR_INITIATED_obj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(AADHAR_OCR_INITIATED_obj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid customer ID provided");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC06_ConfirmBerau")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC07_AADHAR_OCR_INITIATED_Missing_Fields() throws Exception {
        Response createMCOLead = executeWithRetry("TC07_AADHAR_OCR_INITIATED_Missing_Fields", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/AadharOCRInitiate.json";
            SubmitMerchant AADHAR_OCR_INITIATED_obj = new SubmitMerchant(custId, requestPath);
            // Not setting any properties

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(AADHAR_OCR_INITIATED_obj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 400);
        //Assert.assertEquals(createMCOLead.path("message"), "Required fields are missing");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC07_AADHAR_OCR_INITIATED")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC08_SubmitAadharOCRDetails() throws Exception {
        Response createMCOLead = executeWithRetry("TC08_SubmitAadharOCRDetails", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitAadharOCRDetails.json";
            SubmitMerchant SubmitAadharOCRDetailsObj = new SubmitMerchant(custId, requestPath);
            SubmitAadharOCRDetailsObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitAadharOCRDetailsObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitAadharOCRDetailsObj.getProperties().setProperty("aadharNumber", aadharNumber);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitAadharOCRDetailsObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC07_AADHAR_OCR_INITIATED")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC08_SubmitAadharOCRDetails_Invalid_Aadhar() throws Exception {
        Response createMCOLead = executeWithRetry("TC08_SubmitAadharOCRDetails_Invalid_Aadhar", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitAadharOCRDetails.json";
            SubmitMerchant SubmitAadharOCRDetailsObj = new SubmitMerchant(custId, requestPath);
            SubmitAadharOCRDetailsObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitAadharOCRDetailsObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitAadharOCRDetailsObj.getProperties().setProperty("aadharNumber", "123456"); // Invalid Aadhar

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitAadharOCRDetailsObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid Aadhar number format");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC07_AADHAR_OCR_INITIATED")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC08_SubmitAadharOCRDetails_Missing_Aadhar() throws Exception {
        Response createMCOLead = executeWithRetry("TC08_SubmitAadharOCRDetails_Missing_Aadhar", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitAadharOCRDetails.json";
            SubmitMerchant SubmitAadharOCRDetailsObj = new SubmitMerchant(custId, requestPath);
            SubmitAadharOCRDetailsObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitAadharOCRDetailsObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            // Not setting aadharNumber

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitAadharOCRDetailsObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Aadhar number is required");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC07_AADHAR_OCR_INITIATED")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC08_SubmitAadharOCRDetails_OCR_Not_Initiated() throws Exception {
        // First, reset the OCR initiation status (if possible)
        // Then try to submit Aadhar details
        Response createMCOLead = executeWithRetry("TC08_SubmitAadharOCRDetails_OCR_Not_Initiated", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitAadharOCRDetails.json";
            SubmitMerchant SubmitAadharOCRDetailsObj = new SubmitMerchant(custId, requestPath);
            SubmitAadharOCRDetailsObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitAadharOCRDetailsObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitAadharOCRDetailsObj.getProperties().setProperty("aadharNumber", aadharNumber);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitAadharOCRDetailsObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Aadhar OCR not initiated");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC07_AADHAR_OCR_INITIATED")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC08_SubmitAadharOCRDetails_Mismatch_BusinessUUID() throws Exception {
        Response createMCOLead = executeWithRetry("TC08_SubmitAadharOCRDetails_Mismatch_BusinessUUID", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitAadharOCRDetails.json";
            SubmitMerchant SubmitAadharOCRDetailsObj = new SubmitMerchant(custId, requestPath);
            SubmitAadharOCRDetailsObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitAadharOCRDetailsObj.getProperties().setProperty("relatedBusinessUuid", "different_uuid");
            SubmitAadharOCRDetailsObj.getProperties().setProperty("aadharNumber", aadharNumber);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitAadharOCRDetailsObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Business UUID mismatch");
    }



    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC08_SubmitAadharOCRDetails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC09_SubmitPanDetail() throws Exception {
        Response createMCOLead = executeWithRetry("TC09_SubmitPanDetail", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitPanDetailMco.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC08_SubmitAadharOCRDetails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC09_SubmitPanDetail_Invalid_PAN() throws Exception {
        Response createMCOLead = executeWithRetry("TC09_SubmitPanDetail_Invalid_PAN", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitPanDetailMco.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", "INVALID12"); // Invalid PAN format

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 400);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid PAN format");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC08_SubmitAadharOCRDetails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC09_SubmitPanDetail_PAN_Aadhar_Mismatch() throws Exception {
        Response createMCOLead = executeWithRetry("TC09_SubmitPanDetail_PAN_Aadhar_Mismatch", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitPanDetailMco.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", "**********"); // PAN that doesn't match Aadhar

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 400);
        //Assert.assertEquals(createMCOLead.path("message"), "PAN details do not match with Aadhar details");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC08_SubmitAadharOCRDetails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC09_SubmitPanDetail_Already_Used_PAN() throws Exception {
        Response createMCOLead = executeWithRetry("TC09_SubmitPanDetail_Already_Used_PAN", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitPanDetailMco.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", "XYZA12345A"); // Already registered PAN

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 400);
        //Assert.assertEquals(createMCOLead.path("message"), "PAN already registered with another merchant");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC08_SubmitAadharOCRDetails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC09_SubmitPanDetail_Missing_PAN() throws Exception {
        Response createMCOLead = executeWithRetry("TC09_SubmitPanDetail_Missing_PAN", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitPanDetailMco.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            // Not setting PAN

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "PAN is required");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC08_SubmitAadharOCRDetails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC09_SubmitPanDetail_Blacklisted_PAN() throws Exception {
        Response createMCOLead = executeWithRetry("TC09_SubmitPanDetail_Blacklisted_PAN", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitPanDetailMco.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", "BLCK12345P"); // Blacklisted PAN

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 400);
        //Assert.assertEquals(createMCOLead.path("message"), "PAN is blacklisted");
    }


    
    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC09_SubmitPanDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC10_SubmitGSTNSkippedMCO() throws Exception {
        Response createMCOLead = executeWithRetry("TC10_SubmitGSTNSkippedMCO", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitGSTNSkippedMCO.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }




    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC09_SubmitPanDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC10_SubmitGSTNSkippedMCO_Invalid_PAN() throws Exception {
        Response createMCOLead = executeWithRetry("TC10_SubmitGSTNSkippedMCO_Invalid_PAN", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitGSTNSkippedMCO.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", "INVALID123"); // Invalid PAN

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 400);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid PAN format provided");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC09_SubmitPanDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC10_SubmitGSTNSkippedMCO_PAN_Mismatch() throws Exception {
        Response createMCOLead = executeWithRetry("TC10_SubmitGSTNSkippedMCO_PAN_Mismatch", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitGSTNSkippedMCO.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", "**********"); // Different from previously submitted PAN

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "PAN does not match with previously submitted details");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC09_SubmitPanDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC10_SubmitGSTNSkippedMCO_Invalid_BusinessUUID() throws Exception {
        Response createMCOLead = executeWithRetry("TC10_SubmitGSTNSkippedMCO_Invalid_BusinessUUID", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitGSTNSkippedMCO.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", "invalid_uuid");
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid business UUID");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC09_SubmitPanDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC10_SubmitGSTNSkippedMCO_GST_Required() throws Exception {
        // First set business category that requires GST
        // Then try to skip GST submission
        Response createMCOLead = executeWithRetry("TC10_SubmitGSTNSkippedMCO_GST_Required", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitGSTNSkippedMCO.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "GST registration is mandatory for this business category");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC09_SubmitPanDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC10_SubmitGSTNSkippedMCO_Invalid_EntityType() throws Exception {
        Response createMCOLead = executeWithRetry("TC10_SubmitGSTNSkippedMCO_Invalid_EntityType", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitGSTNSkippedMCO.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INVALID_TYPE"); // Invalid entity type
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid entity type");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC10_SubmitGSTNSkippedMCO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC11_uploadBankProofDocument() throws Exception {
        Response submitDocs = executeWithRetry("TC11_uploadBankProofDocument", () -> {
            Map<String, String> params = new HashMap<String, String>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "INDIVIDUAL");
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "0");
            params.put("docCount", "0");
            params.put("docType", "bankProof");
            params.put("docProvided", "cancelledChequePhoto");
            params.put("merchantCustId", custId);
            params.put("type", "jpg");
            params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);
    }


    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC10_SubmitGSTNSkippedMCO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC11_uploadBankProofDocument_Invalid_DocType() throws Exception {
        Response submitDocs = executeWithRetry("TC11_uploadBankProofDocument_Invalid_DocType", () -> {
            Map<String, String> params = new HashMap<String, String>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "INDIVIDUAL");
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "0");
            params.put("docCount", "0");
            params.put("docType", "invalidDocType"); // Invalid document type
            params.put("docProvided", "cancelledChequePhoto");
            params.put("merchantCustId", custId);
            params.put("type", "jpg");
            params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });
        Assert.assertEquals(submitDocs.getStatusCode(), 200);
        //Assert.assertEquals(submitDocs.jsonPath().getString("message"), "Invalid document type");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC10_SubmitGSTNSkippedMCO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC11_uploadBankProofDocument_Invalid_FileType() throws Exception {
        Response submitDocs = executeWithRetry("TC11_uploadBankProofDocument_Invalid_FileType", () -> {
            Map<String, String> params = new HashMap<String, String>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "INDIVIDUAL");
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "0");
            params.put("docCount", "0");
            params.put("docType", "bankProof");
            params.put("docProvided", "cancelledChequePhoto");
            params.put("merchantCustId", custId);
            params.put("type", "exe"); // Invalid file type
            params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });
        Assert.assertEquals(submitDocs.getStatusCode(), 200);
        //Assert.assertEquals(submitDocs.jsonPath().getString("message"), "Invalid file type");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC10_SubmitGSTNSkippedMCO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC11_uploadBankProofDocument_Missing_Required_Params() throws Exception {
        Response submitDocs = executeWithRetry("TC11_uploadBankProofDocument_Missing_Required_Params", () -> {
            Map<String, String> params = new HashMap<String, String>();
            // Missing required parameters
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "INDIVIDUAL");
            // Not setting other required parameters

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });
        Assert.assertEquals(submitDocs.getStatusCode(), 400);
        //Assert.assertEquals(submitDocs.jsonPath().getString("message"), "Required parameters missing");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC10_SubmitGSTNSkippedMCO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC11_uploadBankProofDocument_Invalid_LeadId() throws Exception {
        Response submitDocs = executeWithRetry("TC11_uploadBankProofDocument_Invalid_LeadId", () -> {
            Map<String, String> params = new HashMap<String, String>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "INDIVIDUAL");
            params.put("solutionLeadId", "invalid_lead_id"); // Invalid lead ID
            params.put("pageNo", "0");
            params.put("docCount", "0");
            params.put("docType", "bankProof");
            params.put("docProvided", "cancelledChequePhoto");
            params.put("merchantCustId", custId);
            params.put("type", "jpg");
            params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });
        Assert.assertEquals(submitDocs.getStatusCode(), 200);
        //Assert.assertEquals(submitDocs.jsonPath().getString("message"), "Invalid lead ID");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC10_SubmitGSTNSkippedMCO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC11_uploadBankProofDocument_Invalid_DocFormat() throws Exception {
        Response submitDocs = executeWithRetry("TC11_uploadBankProofDocument_Invalid_DocFormat", () -> {
            Map<String, String> params = new HashMap<String, String>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "INDIVIDUAL");
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "0");
            params.put("docCount", "0");
            params.put("docType", "bankProof");
            params.put("docProvided", "invalidFormat"); // Invalid document format
            params.put("merchantCustId", custId);
            params.put("type", "jpg");
            params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });
        Assert.assertEquals(submitDocs.getStatusCode(), 400);
        //Assert.assertEquals(submitDocs.jsonPath().getString("message"), "Invalid document format");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC11_uploadBankProofDocument")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC12_UploadBusinessOwnerPhoto() throws Exception {
        Response submitDocs = executeWithRetry("TC12_UploadBusinessOwnerPhoto", () -> {
            Map<String, String> params = new HashMap<String, String>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "INDIVIDUAL");
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "0");
            params.put("docCount", "0");
            params.put("docType", "bankProof");
            params.put("docProvided", "businessOwnerPhoto");
            params.put("merchantCustId", custId);
            params.put("type", "jpg");
            params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);
    }



    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC11_uploadBankProofDocument")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC12_UploadBusinessOwnerPhoto_Invalid_Image() throws Exception {
        Response submitDocs = executeWithRetry("TC12_UploadBusinessOwnerPhoto_Invalid_Image", () -> {
            Map<String, String> params = new HashMap<String, String>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "INDIVIDUAL");
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "0");
            params.put("docCount", "0");
            params.put("docType", "bankProof");
            params.put("docProvided", "businessOwnerPhoto");
            params.put("merchantCustId", custId);
            params.put("type", "jpg");
            params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));
            // Assume invalid image data is provided

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });
        Assert.assertEquals(submitDocs.getStatusCode(), 200);
        //Assert.assertEquals(submitDocs.jsonPath().getString("message"), "Invalid image format or corrupted image");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC11_uploadBankProofDocument")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC12_UploadBusinessOwnerPhoto_Large_File() throws Exception {
        Response submitDocs = executeWithRetry("TC12_UploadBusinessOwnerPhoto_Large_File", () -> {
            Map<String, String> params = new HashMap<String, String>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "INDIVIDUAL");
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "0");
            params.put("docCount", "0");
            params.put("docType", "bankProof");
            params.put("docProvided", "businessOwnerPhoto");
            params.put("merchantCustId", custId);
            params.put("type", "jpg");
            params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));
            // Assume large file size is provided

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });
        Assert.assertEquals(submitDocs.getStatusCode(), 200);
        //Assert.assertEquals(submitDocs.jsonPath().getString("message"), "File size exceeds maximum limit");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC11_uploadBankProofDocument")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC12_UploadBusinessOwnerPhoto_Invalid_Resolution() throws Exception {
        Response submitDocs = executeWithRetry("TC12_UploadBusinessOwnerPhoto_Invalid_Resolution", () -> {
            Map<String, String> params = new HashMap<String, String>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "INDIVIDUAL");
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "0");
            params.put("docCount", "0");
            params.put("docType", "bankProof");
            params.put("docProvided", "businessOwnerPhoto");
            params.put("merchantCustId", custId);
            params.put("type", "jpg");
            params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));
            // Assume low resolution image is provided

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });
        Assert.assertEquals(submitDocs.getStatusCode(), 200);
        //Assert.assertEquals(submitDocs.jsonPath().getString("message"), "Image resolution below minimum requirement");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC11_uploadBankProofDocument")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC12_UploadBusinessOwnerPhoto_No_Face_Detected() throws Exception {
        Response submitDocs = executeWithRetry("TC12_UploadBusinessOwnerPhoto_No_Face_Detected", () -> {
            Map<String, String> params = new HashMap<String, String>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "INDIVIDUAL");
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "0");
            params.put("docCount", "0");
            params.put("docType", "bankProof");
            params.put("docProvided", "businessOwnerPhoto");
            params.put("merchantCustId", custId);
            params.put("type", "jpg");
            params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));
            // Assume image without face is provided

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });
        Assert.assertEquals(submitDocs.getStatusCode(), 200);
        //Assert.assertEquals(submitDocs.jsonPath().getString("message"), "No face detected in the image");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC11_uploadBankProofDocument")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC12_UploadBusinessOwnerPhoto_Multiple_Faces() throws Exception {
        Response submitDocs = executeWithRetry("TC12_UploadBusinessOwnerPhoto_Multiple_Faces", () -> {
            Map<String, String> params = new HashMap<String, String>();
            params.put("solutionType", "merchant_common_onboard");
            params.put("entityType", "INDIVIDUAL");
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "0");
            params.put("docCount", "0");
            params.put("docType", "bankProof");
            params.put("docProvided", "businessOwnerPhoto");
            params.put("merchantCustId", custId);
            params.put("type", "jpg");
            params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));
            // Assume image with multiple faces is provided

            SubmitDocs v3DocSubmit = new SubmitDocs();
            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        });
        Assert.assertEquals(submitDocs.getStatusCode(), 200);
        //Assert.assertEquals(submitDocs.jsonPath().getString("message"), "Multiple faces detected in the image");
    }
    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC12_UploadBusinessOwnerPhoto")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC13_MERCHANT_PHOTO_SKIPPED_AFTER_EXHAUSTING_ATTEMPTS() throws Exception {
        Response createMCOLead = executeWithRetry("TC13_MERCHANT_PHOTO_SKIPPED_AFTER_EXHAUSTING_ATTEMPTS", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/MerchantPhotoSkippedAfterAllAtempts.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }



    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC12_UploadBusinessOwnerPhoto")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC13_MERCHANT_PHOTO_SKIPPED_Invalid_CustId() throws Exception {
        Response createMCOLead = executeWithRetry("TC13_MERCHANT_PHOTO_SKIPPED_Invalid_CustId", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/MerchantPhotoSkippedAfterAllAtempts.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant("invalid_cust_id", requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid customer ID");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC12_UploadBusinessOwnerPhoto")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC13_MERCHANT_PHOTO_SKIPPED_Invalid_BusinessUUID() throws Exception {
        Response createMCOLead = executeWithRetry("TC13_MERCHANT_PHOTO_SKIPPED_Invalid_BusinessUUID", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/MerchantPhotoSkippedAfterAllAtempts.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", "invalid_uuid");
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("relatedBusinessUuid"), "invalid_uuid");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC12_UploadBusinessOwnerPhoto")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC13_MERCHANT_PHOTO_SKIPPED_Invalid_LeadId() throws Exception {
        Response createMCOLead = executeWithRetry("TC13_MERCHANT_PHOTO_SKIPPED_Invalid_LeadId", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/MerchantPhotoSkippedAfterAllAtempts.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", "invalid_lead_id");

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 400);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid lead ID");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC12_UploadBusinessOwnerPhoto")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC13_MERCHANT_PHOTO_SKIPPED_Missing_Fields() throws Exception {
        Response createMCOLead = executeWithRetry("TC13_MERCHANT_PHOTO_SKIPPED_Missing_Fields", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/MerchantPhotoSkippedAfterAllAtempts.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            // Not setting any properties

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Required fields missing");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC12_UploadBusinessOwnerPhoto")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC13_MERCHANT_PHOTO_SKIPPED_Invalid_EntityType() throws Exception {
        Response createMCOLead = executeWithRetry("TC13_MERCHANT_PHOTO_SKIPPED_Invalid_EntityType", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/MerchantPhotoSkippedAfterAllAtempts.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INVALID_TYPE");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid entity type");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC13_MERCHANT_PHOTO_SKIPPED_AFTER_EXHAUSTING_ATTEMPTS")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC14_qna_submit() throws Exception {
        Response createMCOLead = executeWithRetry("TC14_qna_submit", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/QNA.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC13_MERCHANT_PHOTO_SKIPPED_AFTER_EXHAUSTING_ATTEMPTS")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC14_qna_submit_Invalid_CustId() throws Exception {
        Response createMCOLead = executeWithRetry("TC14_qna_submit_Invalid_CustId", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/QNA.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant("invalid_cust_id", requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid customer ID");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC13_MERCHANT_PHOTO_SKIPPED_AFTER_EXHAUSTING_ATTEMPTS")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC14_qna_submit_Invalid_LeadId() throws Exception {
        Response createMCOLead = executeWithRetry("TC14_qna_submit_Invalid_LeadId", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/QNA.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", "invalid_lead_id");

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 400);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid lead ID");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC13_MERCHANT_PHOTO_SKIPPED_AFTER_EXHAUSTING_ATTEMPTS")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC14_qna_submit_Invalid_BusinessUUID() throws Exception {
        Response createMCOLead = executeWithRetry("TC14_qna_submit_Invalid_BusinessUUID", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/QNA.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", "invalid_uuid");
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("relatedBusinessUuid"), "invalid_uuid");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC13_MERCHANT_PHOTO_SKIPPED_AFTER_EXHAUSTING_ATTEMPTS")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC14_qna_submit_Missing_Required_Fields() throws Exception {
        Response createMCOLead = executeWithRetry("TC14_qna_submit_Missing_Required_Fields", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/QNA.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            // Not setting any properties

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Required fields missing");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC13_MERCHANT_PHOTO_SKIPPED_AFTER_EXHAUSTING_ATTEMPTS")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC14_qna_submit_Invalid_Solution_Type() throws Exception {
        Response createMCOLead = executeWithRetry("TC14_qna_submit_Invalid_Solution_Type", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/QNA.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "invalid_solution_type");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid solution type");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC13_MERCHANT_PHOTO_SKIPPED_AFTER_EXHAUSTING_ATTEMPTS")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC14_qna_submit_Invalid_Entity_Type() throws Exception {
        Response createMCOLead = executeWithRetry("TC14_qna_submit_Invalid_Entity_Type", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/QNA.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INVALID_TYPE");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid entity type");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC14_qna_submit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC15_SegmentSubSegmentSubmit() throws Exception {
        Response createMCOLead = executeWithRetry("TC15_SegmentSubSegmentSubmit", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SegmentSubSegmentSubmit.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }


    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC14_qna_submit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC15_SegmentSubSegmentSubmit_Invalid_CustId() throws Exception {
        Response createMCOLead = executeWithRetry("TC15_SegmentSubSegmentSubmit_Invalid_CustId", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SegmentSubSegmentSubmit.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant("invalid_cust_id", requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid customer ID");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC14_qna_submit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC15_SegmentSubSegmentSubmit_Invalid_BusinessUUID() throws Exception {
        Response createMCOLead = executeWithRetry("TC15_SegmentSubSegmentSubmit_Invalid_BusinessUUID", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SegmentSubSegmentSubmit.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", "invalid_uuid");
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid business UUID");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC14_qna_submit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC15_SegmentSubSegmentSubmit_Invalid_LeadId() throws Exception {
        Response createMCOLead = executeWithRetry("TC15_SegmentSubSegmentSubmit_Invalid_LeadId", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SegmentSubSegmentSubmit.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", "invalid_lead_id");

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 400);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid lead ID");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC14_qna_submit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC15_SegmentSubSegmentSubmit_Invalid_SolutionType() throws Exception {
        Response createMCOLead = executeWithRetry("TC15_SegmentSubSegmentSubmit_Invalid_SolutionType", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SegmentSubSegmentSubmit.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "invalid_solution_type");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid solution type");
    }

    
    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC14_qna_submit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC15_SegmentSubSegmentSubmit_Invalid_EntityType() throws Exception {
        Response createMCOLead = executeWithRetry("TC15_SegmentSubSegmentSubmit_Invalid_EntityType", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SegmentSubSegmentSubmit.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INVALID_TYPE");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 500);
//        Assert.assertEquals(createMCOLead.path("message"), "Invalid entity type");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC14_qna_submit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC15_SegmentSubSegmentSubmit_Missing_Required_Fields() throws Exception {
        Response createMCOLead = executeWithRetry("TC15_SegmentSubSegmentSubmit_Missing_Required_Fields", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SegmentSubSegmentSubmit.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            // Not setting any properties

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
       // Assert.assertEquals(createMCOLead.path("message"), "Required fields missing");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC14_qna_submit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC15_SegmentSubSegmentSubmit_Invalid_BankAccount() throws Exception {
        Response createMCOLead = executeWithRetry("TC15_SegmentSubSegmentSubmit_Invalid_BankAccount", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SegmentSubSegmentSubmit.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", "invalid_account");
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        //Assert.assertEquals(createMCOLead.path("message"), "Invalid bank account number");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC15_SegmentSubSegmentSubmit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC16_UAC_Submit() throws Exception {
        Response createMCOLead = executeWithRetry("TC16_UAC_Submit", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/UACSubmit.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }
    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC15_SegmentSubSegmentSubmit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC16_UAC_Submit_Invalid_CustId() throws Exception {
    Response createMCOLead = executeWithRetry("TC16_UAC_Submit_Invalid_CustId", () -> {
        requestPath = "MerchantService/V3/SubmitMerchant/UACSubmit.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant("invalid_cust_id", requestPath);
        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);

        return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 500);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid customer ID");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC15_SegmentSubSegmentSubmit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC16_UAC_Submit_Invalid_BusinessUUID() throws Exception {
    Response createMCOLead = executeWithRetry("TC16_UAC_Submit_Invalid_BusinessUUID", () -> {
        requestPath = "MerchantService/V3/SubmitMerchant/UACSubmit.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", "invalid_uuid");
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);

        return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid business UUID");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC15_SegmentSubSegmentSubmit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC16_UAC_Submit_Invalid_LeadId() throws Exception {
    Response createMCOLead = executeWithRetry("TC16_UAC_Submit_Invalid_LeadId", () -> {
        requestPath = "MerchantService/V3/SubmitMerchant/UACSubmit.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", "invalid_lead_id");

        return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 400);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid lead ID");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC15_SegmentSubSegmentSubmit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC16_UAC_Submit_Invalid_SolutionType() throws Exception {
    Response createMCOLead = executeWithRetry("TC16_UAC_Submit_Invalid_SolutionType", () -> {
        requestPath = "MerchantService/V3/SubmitMerchant/UACSubmit.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "invalid_solution_type");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);

        return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 500);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid solution type");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC15_SegmentSubSegmentSubmit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC16_UAC_Submit_Invalid_EntityType() throws Exception {
    Response createMCOLead = executeWithRetry("TC16_UAC_Submit_Invalid_EntityType", () -> {
        requestPath = "MerchantService/V3/SubmitMerchant/UACSubmit.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INVALID_TYPE");
        queryParams.put("leadId", leadId);

        return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 500);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid entity type");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC15_SegmentSubSegmentSubmit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC16_UAC_Submit_Missing_Required_Fields() throws Exception {
    Response createMCOLead = executeWithRetry("TC16_UAC_Submit_Missing_Required_Fields", () -> {
        requestPath = "MerchantService/V3/SubmitMerchant/UACSubmit.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
        // Not setting any properties

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);

        return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    //Assert.assertEquals(createMCOLead.path("message"), "Required fields missing");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC15_SegmentSubSegmentSubmit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC16_UAC_Submit_Invalid_PAN() throws Exception {
    Response createMCOLead = executeWithRetry("TC16_UAC_Submit_Invalid_PAN", () -> {
        requestPath = "MerchantService/V3/SubmitMerchant/UACSubmit.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", "INVALID12345");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);

        return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 400);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid PAN format");
}

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC16_UAC_Submit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC17_FetchLeadDetail() throws Exception {
        Response RespV3Fetch = executeWithRetry("TC17_FetchLeadDetail", () -> {
            FetchV3Merchant v3Merch = new FetchV3Merchant(leadId);
            Map<String, String> query = new HashMap<>();
            query.put("entityType", "INDIVIDUAL");
            query.put("solutionType", "merchant_common_onboard");
            query.put("custId", custId);

            return middlewareServicesObject.FetchV3MerchantLead(v3Merch, query, headers);
        });
        AddressUUID = RespV3Fetch.jsonPath().getJsonObject("merchantDetails.addressUuid");
    }
    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC16_UAC_Submit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC17_FetchLeadDetail_Invalid_LeadId() throws Exception {
    Response RespV3Fetch = executeWithRetry("TC17_FetchLeadDetail_Invalid_LeadId", () -> {
        FetchV3Merchant v3Merch = new FetchV3Merchant("invalid_lead_id");
        Map<String, String> query = new HashMap<>();
        query.put("entityType", "INDIVIDUAL");
        query.put("solutionType", "merchant_common_onboard");
        query.put("custId", custId);

        return middlewareServicesObject.FetchV3MerchantLead(v3Merch, query, headers);
    });
    Assert.assertEquals(RespV3Fetch.getStatusCode(), 200);
    //Assert.assertEquals(RespV3Fetch.path("message"), "Invalid lead ID");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC16_UAC_Submit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC17_FetchLeadDetail_Invalid_CustId() throws Exception {
    Response RespV3Fetch = executeWithRetry("TC17_FetchLeadDetail_Invalid_CustId", () -> {
        FetchV3Merchant v3Merch = new FetchV3Merchant(leadId);
        Map<String, String> query = new HashMap<>();
        query.put("entityType", "INDIVIDUAL");
        query.put("solutionType", "merchant_common_onboard");
        query.put("custId", "invalid_cust_id");

        return middlewareServicesObject.FetchV3MerchantLead(v3Merch, query, headers);
    });
    Assert.assertEquals(RespV3Fetch.getStatusCode(), 500);
    //Assert.assertEquals(RespV3Fetch.path("message"), "Invalid customer ID");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC16_UAC_Submit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC17_FetchLeadDetail_Invalid_EntityType() throws Exception {
    Response RespV3Fetch = executeWithRetry("TC17_FetchLeadDetail_Invalid_EntityType", () -> {
        FetchV3Merchant v3Merch = new FetchV3Merchant(leadId);
        Map<String, String> query = new HashMap<>();
        query.put("entityType", "INVALID_TYPE");
        query.put("solutionType", "merchant_common_onboard");
        query.put("custId", custId);

        return middlewareServicesObject.FetchV3MerchantLead(v3Merch, query, headers);
    });
    Assert.assertEquals(RespV3Fetch.getStatusCode(), 500);
    //Assert.assertEquals(RespV3Fetch.path("message"), "Invalid entity type");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC16_UAC_Submit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC17_FetchLeadDetail_Invalid_SolutionType() throws Exception {
    Response RespV3Fetch = executeWithRetry("TC17_FetchLeadDetail_Invalid_SolutionType", () -> {
        FetchV3Merchant v3Merch = new FetchV3Merchant(leadId);
        Map<String, String> query = new HashMap<>();
        query.put("entityType", "INDIVIDUAL");
        query.put("solutionType", "invalid_solution_type");
        query.put("custId", custId);

        return middlewareServicesObject.FetchV3MerchantLead(v3Merch, query, headers);
    });
    Assert.assertEquals(RespV3Fetch.getStatusCode(), 500);
    //Assert.assertEquals(RespV3Fetch.path("message"), "Invalid solution type");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC16_UAC_Submit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC17_FetchLeadDetail_Missing_Required_Params() throws Exception {
    Response RespV3Fetch = executeWithRetry("TC17_FetchLeadDetail_Missing_Required_Params", () -> {
        FetchV3Merchant v3Merch = new FetchV3Merchant(leadId);
        Map<String, String> query = new HashMap<>();
        // Not setting any query parameters

        return middlewareServicesObject.FetchV3MerchantLead(v3Merch, query, headers);
    });
    Assert.assertEquals(RespV3Fetch.getStatusCode(), 400);
    //Assert.assertEquals(RespV3Fetch.path("message"), "Required parameters missing");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC16_UAC_Submit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC17_FetchLeadDetail_Lead_Not_Found() throws Exception {
    Response RespV3Fetch = executeWithRetry("TC17_FetchLeadDetail_Lead_Not_Found", () -> {
        FetchV3Merchant v3Merch = new FetchV3Merchant("non_existent_lead_id");
        Map<String, String> query = new HashMap<>();
        query.put("entityType", "INDIVIDUAL");
        query.put("solutionType", "merchant_common_onboard");
        query.put("custId", custId);

        return middlewareServicesObject.FetchV3MerchantLead(v3Merch, query, headers);
    });
    Assert.assertEquals(RespV3Fetch.getStatusCode(), 200);
    //Assert.assertEquals(RespV3Fetch.path("message"), "Lead not found");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC16_UAC_Submit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC17_FetchLeadDetail_Invalid_Headers() throws Exception {
    Response RespV3Fetch = executeWithRetry("TC17_FetchLeadDetail_Invalid_Headers", () -> {
        FetchV3Merchant v3Merch = new FetchV3Merchant(leadId);
        Map<String, String> query = new HashMap<>();
        query.put("entityType", "INDIVIDUAL");
        query.put("solutionType", "merchant_common_onboard");
        query.put("custId", custId);

        Map<String, String> invalidHeaders = new HashMap<>();
        // Using invalid headers

        return middlewareServicesObject.FetchV3MerchantLead(v3Merch, query, invalidHeaders);
    });
    Assert.assertEquals(RespV3Fetch.getStatusCode(), 200);
    //Assert.assertEquals(RespV3Fetch.path("message"), "Unauthorized access");
}

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC17_FetchLeadDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC18_RBDScreen_Submit() throws Exception {

        requestPath = "MerchantService/V3/SubmitMerchant/RBDSubmit.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);

        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);
        SubmitPanDetailMcoObj.getProperties().setProperty("AddressUUID", AddressUUID);


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);


        Response createMCOLead = middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }
    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC17_FetchLeadDetail")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC18_RBDScreen_Submit_Invalid_CustId() throws Exception {
    Response createMCOLead = executeWithRetry("TC18_RBDScreen_Submit_Invalid_CustId", () -> {
        requestPath = "MerchantService/V3/SubmitMerchant/RBDSubmit.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant("invalid_cust_id", requestPath);

        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);
        SubmitPanDetailMcoObj.getProperties().setProperty("AddressUUID", AddressUUID);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);

        return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 500);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid customer ID");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC17_FetchLeadDetail")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC18_RBDScreen_Submit_Invalid_BusinessUUID() throws Exception {
    Response createMCOLead = executeWithRetry("TC18_RBDScreen_Submit_Invalid_BusinessUUID", () -> {
        requestPath = "MerchantService/V3/SubmitMerchant/RBDSubmit.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);

        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", "invalid_uuid");
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);
        SubmitPanDetailMcoObj.getProperties().setProperty("AddressUUID", AddressUUID);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);

        return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid business UUID");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC17_FetchLeadDetail")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC18_RBDScreen_Submit_Invalid_AddressUUID() throws Exception {
    Response createMCOLead = executeWithRetry("TC18_RBDScreen_Submit_Invalid_AddressUUID", () -> {
        requestPath = "MerchantService/V3/SubmitMerchant/RBDSubmit.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);

        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);
        SubmitPanDetailMcoObj.getProperties().setProperty("AddressUUID", "invalid_address_uuid");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);

        return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid address UUID");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC17_FetchLeadDetail")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC18_RBDScreen_Submit_Invalid_LeadId() throws Exception {
    Response createMCOLead = executeWithRetry("TC18_RBDScreen_Submit_Invalid_LeadId", () -> {
        requestPath = "MerchantService/V3/SubmitMerchant/RBDSubmit.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);

        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);
        SubmitPanDetailMcoObj.getProperties().setProperty("AddressUUID", AddressUUID);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", "invalid_lead_id");

        return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 400);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid lead ID");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC17_FetchLeadDetail")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC18_RBDScreen_Submit_Invalid_SolutionType() throws Exception {
    Response createMCOLead = executeWithRetry("TC18_RBDScreen_Submit_Invalid_SolutionType", () -> {
        requestPath = "MerchantService/V3/SubmitMerchant/RBDSubmit.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);

        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);
        SubmitPanDetailMcoObj.getProperties().setProperty("AddressUUID", AddressUUID);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "invalid_solution_type");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);

        return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 500);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid solution type");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC17_FetchLeadDetail")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC18_RBDScreen_Submit_Invalid_EntityType() throws Exception {
    Response createMCOLead = executeWithRetry("TC18_RBDScreen_Submit_Invalid_EntityType", () -> {
        requestPath = "MerchantService/V3/SubmitMerchant/RBDSubmit.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);

        SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);
        SubmitPanDetailMcoObj.getProperties().setProperty("AddressUUID", AddressUUID);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INVALID_TYPE");
        queryParams.put("leadId", leadId);

        return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 500);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid entity type");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC17_FetchLeadDetail")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC18_RBDScreen_Submit_Missing_Required_Fields() throws Exception {
    Response createMCOLead = executeWithRetry("TC18_RBDScreen_Submit_Missing_Required_Fields", () -> {
        requestPath = "MerchantService/V3/SubmitMerchant/RBDSubmit.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
        // Not setting any properties

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);

        return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 400);
    //Assert.assertEquals(createMCOLead.path("message"), "Required fields missing");
}

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC18_RBDScreen_Submit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC19_SendOtpforAgreement() throws Exception {
        Response createMCOLead = executeWithRetry("TC19_SendOtpforAgreement", () -> {
            requestPath = "MerchantService/V3/SendOtp/SendOtpMcoForAgreement.json";
            sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
            sendOTPobj.getProperties().setProperty("mobile", Mobile);
            sendOTPobj.getProperties().setProperty("custId", custId);
            sendOTPobj.getProperties().setProperty("lead_id", leadId);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");

            return middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        AgreementOTPState = createMCOLead.path("state");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC18_RBDScreen_Submit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC19_SendOtpforAgreement_Invalid_Mobile() throws Exception {
    Response createMCOLead = executeWithRetry("TC19_SendOtpforAgreement_Invalid_Mobile", () -> {
        requestPath = "MerchantService/V3/SendOtp/SendOtpMcoForAgreement.json";
        sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
        sendOTPobj.getProperties().setProperty("mobile", "123"); // Invalid mobile number
        sendOTPobj.getProperties().setProperty("custId", custId);
        sendOTPobj.getProperties().setProperty("lead_id", leadId);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");

        return middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 400);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid mobile number format");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC18_RBDScreen_Submit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC19_SendOtpforAgreement_Invalid_CustId() throws Exception {
    Response createMCOLead = executeWithRetry("TC19_SendOtpforAgreement_Invalid_CustId", () -> {
        requestPath = "MerchantService/V3/SendOtp/SendOtpMcoForAgreement.json";
        sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
        sendOTPobj.getProperties().setProperty("mobile", Mobile);
        sendOTPobj.getProperties().setProperty("custId", "invalid_cust_id");
        sendOTPobj.getProperties().setProperty("lead_id", leadId);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");

        return middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid customer ID");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC18_RBDScreen_Submit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC19_SendOtpforAgreement_Invalid_LeadId() throws Exception {
    Response createMCOLead = executeWithRetry("TC19_SendOtpforAgreement_Invalid_LeadId", () -> {
        requestPath = "MerchantService/V3/SendOtp/SendOtpMcoForAgreement.json";
        sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
        sendOTPobj.getProperties().setProperty("mobile", Mobile);
        sendOTPobj.getProperties().setProperty("custId", custId);
        sendOTPobj.getProperties().setProperty("lead_id", "invalid_lead_id");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");

        return middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid lead ID");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC18_RBDScreen_Submit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC19_SendOtpforAgreement_Invalid_SolutionType() throws Exception {
    Response createMCOLead = executeWithRetry("TC19_SendOtpforAgreement_Invalid_SolutionType", () -> {
        requestPath = "MerchantService/V3/SendOtp/SendOtpMcoForAgreement.json";
        sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
        sendOTPobj.getProperties().setProperty("mobile", Mobile);
        sendOTPobj.getProperties().setProperty("custId", custId);
        sendOTPobj.getProperties().setProperty("lead_id", leadId);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "invalid_solution_type");
        queryParams.put("entityType", "INDIVIDUAL");

        return middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 500);
   // Assert.assertEquals(createMCOLead.path("message"), "Invalid solution type");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC18_RBDScreen_Submit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC19_SendOtpforAgreement_Invalid_EntityType() throws Exception {
    Response createMCOLead = executeWithRetry("TC19_SendOtpforAgreement_Invalid_EntityType", () -> {
        requestPath = "MerchantService/V3/SendOtp/SendOtpMcoForAgreement.json";
        sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
        sendOTPobj.getProperties().setProperty("mobile", Mobile);
        sendOTPobj.getProperties().setProperty("custId", custId);
        sendOTPobj.getProperties().setProperty("lead_id", leadId);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INVALID_TYPE");

        return middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid entity type");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC18_RBDScreen_Submit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC19_SendOtpforAgreement_Missing_Required_Fields() throws Exception {
    Response createMCOLead = executeWithRetry("TC19_SendOtpforAgreement_Missing_Required_Fields", () -> {
        requestPath = "MerchantService/V3/SendOtp/SendOtpMcoForAgreement.json";
        sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
        // Not setting any properties

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");

        return middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 400);
    //Assert.assertEquals(createMCOLead.path("message"), "Required fields missing");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC18_RBDScreen_Submit")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC19_SendOtpforAgreement_OTP_Generation_Limit_Exceeded() throws Exception {
    // First, send multiple OTP requests to exceed the limit
    for(int i = 0; i < 100; i++) {
        middlewareServicesObject.SendOtpMco(new sendOTPLead(requestPath), new HashMap<>(), headers);
    }
    
    Response createMCOLead = executeWithRetry("TC19_SendOtpforAgreement_OTP_Generation_Limit_Exceeded", () -> {
        requestPath = "MerchantService/V3/SendOtp/SendOtpMcoForAgreement.json";
        sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
        sendOTPobj.getProperties().setProperty("mobile", Mobile);
        sendOTPobj.getProperties().setProperty("custId", custId);
        sendOTPobj.getProperties().setProperty("lead_id", leadId);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");

        return middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    //Assert.assertEquals(createMCOLead.path("message"), "OTP generation limit exceeded. Please try after some time");
}


    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC19_SendOtpforAgreement")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC20_ValidateOtpforAgreement() throws Exception {
        Response createMCOLead = executeWithRetry("TC20_ValidateOtpforAgreement", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMcoForAgreement.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("state", AgreementOTPState);
            ValidateOtp.getProperties().setProperty("otp", "888888");
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("leadId", leadId);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC19_SendOtpforAgreement")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC20_ValidateOtpforAgreement_Invalid_OTP() throws Exception {
    Response createMCOLead = executeWithRetry("TC20_ValidateOtpforAgreement_Invalid_OTP", () -> {
        requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMcoForAgreement.json";
        ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
        ValidateOtp.getProperties().setProperty("state", AgreementOTPState);
        ValidateOtp.getProperties().setProperty("otp", "123456"); // Wrong OTP
        ValidateOtp.getProperties().setProperty("mobile", Mobile);
        ValidateOtp.getProperties().setProperty("leadId", leadId);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");

        return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid OTP");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC19_SendOtpforAgreement")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC20_ValidateOtpforAgreement_Expired_OTP() throws Exception {
    // Wait for OTP to expire
    Thread.sleep(300000); // 5 minutes wait

    Response createMCOLead = executeWithRetry("TC20_ValidateOtpforAgreement_Expired_OTP", () -> {
        requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMcoForAgreement.json";
        ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
        ValidateOtp.getProperties().setProperty("state", AgreementOTPState);
        ValidateOtp.getProperties().setProperty("otp", "888888");
        ValidateOtp.getProperties().setProperty("mobile", Mobile);
        ValidateOtp.getProperties().setProperty("leadId", leadId);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");

        return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    //Assert.assertEquals(createMCOLead.path("message"), "OTP has expired");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC19_SendOtpforAgreement")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC20_ValidateOtpforAgreement_Invalid_State() throws Exception {
    Response createMCOLead = executeWithRetry("TC20_ValidateOtpforAgreement_Invalid_State", () -> {
        requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMcoForAgreement.json";
        ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
        ValidateOtp.getProperties().setProperty("state", "invalid_state");
        ValidateOtp.getProperties().setProperty("otp", "888888");
        ValidateOtp.getProperties().setProperty("mobile", Mobile);
        ValidateOtp.getProperties().setProperty("leadId", leadId);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");

        return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid state");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC19_SendOtpforAgreement")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC20_ValidateOtpforAgreement_Invalid_Mobile() throws Exception {
    Response createMCOLead = executeWithRetry("TC20_ValidateOtpforAgreement_Invalid_Mobile", () -> {
        requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMcoForAgreement.json";
        ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
        ValidateOtp.getProperties().setProperty("state", AgreementOTPState);
        ValidateOtp.getProperties().setProperty("otp", "888888");
        ValidateOtp.getProperties().setProperty("mobile", "123"); // Invalid mobile
        ValidateOtp.getProperties().setProperty("leadId", leadId);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");

        return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    //Assert.assertEquals(createMCOLead.path("message"), "Invalid mobile number format");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC19_SendOtpforAgreement")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC20_ValidateOtpforAgreement_Max_Attempts_Exceeded() throws Exception {
    // Try validating OTP multiple times with wrong OTP to exceed max attempts
    for(int i = 0; i < 3; i++) {
        middlewareServicesObject.CreateLead(new ValidateOtp(requestPath), new HashMap<>(), headers);
    }

    Response createMCOLead = executeWithRetry("TC20_ValidateOtpforAgreement_Max_Attempts_Exceeded", () -> {
        requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMcoForAgreement.json";
        ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
        ValidateOtp.getProperties().setProperty("state", AgreementOTPState);
        ValidateOtp.getProperties().setProperty("otp", "888888");
        ValidateOtp.getProperties().setProperty("mobile", Mobile);
        ValidateOtp.getProperties().setProperty("leadId", leadId);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");

        return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    //Assert.assertEquals(createMCOLead.path("message"), "Maximum validation attempts exceeded");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC19_SendOtpforAgreement")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC20_ValidateOtpforAgreement_Missing_Required_Fields() throws Exception {
    Response createMCOLead = executeWithRetry("TC20_ValidateOtpforAgreement_Missing_Required_Fields", () -> {
        requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMcoForAgreement.json";
        ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
        // Not setting any properties

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");

        return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
    });
    Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    //Assert.assertEquals(createMCOLead.path("message"), "Required fields missing");
}

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC20_ValidateOtpforAgreement")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC21_UploadShopPhoto() throws Exception {

        // String endPoint = P.API.get("SubmitDocs");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "merchant_common_onboard");
        //  params.put("channel", channel);
        params.put("entityType", "INDIVIDUAL");
        params.put("leadId", leadId);
        params.put("pageNo", "0");
        params.put("docCount", "0");
        params.put("docType", "shopFrontPhoto");
        params.put("docProvided", "shopFrontPhoto");
        params.put("merchantCustId", custId);
        params.put("docLat", "28.5682852");
        params.put("docLong", "77.3956855");
        params.put("docDistance", "0.3397966438531691");

        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);


        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC20_ValidateOtpforAgreement")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC21_UploadShopPhoto_Invalid_LeadId() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "merchant_common_onboard");
    params.put("entityType", "INDIVIDUAL");
    params.put("leadId", "invalid_lead_id");
    params.put("pageNo", "0");
    params.put("docCount", "0");
    params.put("docType", "shopFrontPhoto");
    params.put("docProvided", "shopFrontPhoto");
    params.put("merchantCustId", custId);
    params.put("docLat", "28.5682852");
    params.put("docLong", "77.3956855");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 400);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid lead ID");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC20_ValidateOtpforAgreement")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC21_UploadShopPhoto_Invalid_Coordinates() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "merchant_common_onboard");
    params.put("entityType", "INDIVIDUAL");
    params.put("leadId", leadId);
    params.put("pageNo", "0");
    params.put("docCount", "0");
    params.put("docType", "shopFrontPhoto");
    params.put("docProvided", "shopFrontPhoto");
    params.put("merchantCustId", custId);
    params.put("docLat", "invalid_lat");
    params.put("docLong", "invalid_long");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 200);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid coordinates format");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC20_ValidateOtpforAgreement")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC21_UploadShopPhoto_Invalid_DocType() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "merchant_common_onboard");
    params.put("entityType", "INDIVIDUAL");
    params.put("leadId", leadId);
    params.put("pageNo", "0");
    params.put("docCount", "0");
    params.put("docType", "invalid_doc_type");
    params.put("docProvided", "shopFrontPhoto");
    params.put("merchantCustId", custId);
    params.put("docLat", "28.5682852");
    params.put("docLong", "77.3956855");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 200);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid document type");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC20_ValidateOtpforAgreement")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC21_UploadShopPhoto_Missing_Required_Fields() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    // Not setting any parameters
    
    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 400);
    //Assert.assertEquals(submitDocs.path("message"), "Required fields missing");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC20_ValidateOtpforAgreement")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC21_UploadShopPhoto_Invalid_Distance() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "merchant_common_onboard");
    params.put("entityType", "INDIVIDUAL");
    params.put("leadId", leadId);
    params.put("pageNo", "0");
    params.put("docCount", "0");
    params.put("docType", "shopFrontPhoto");
    params.put("docProvided", "shopFrontPhoto");
    params.put("merchantCustId", custId);
    params.put("docLat", "28.5682852");
    params.put("docLong", "77.3956855");
    params.put("docDistance", "invalid_distance");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 200);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid distance format");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC20_ValidateOtpforAgreement")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC21_UploadShopPhoto_Invalid_SolutionType() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "invalid_solution_type");
    params.put("entityType", "INDIVIDUAL");
    params.put("leadId", leadId);
    params.put("pageNo", "0");
    params.put("docCount", "0");
    params.put("docType", "shopFrontPhoto");
    params.put("docProvided", "shopFrontPhoto");
    params.put("merchantCustId", custId);
    params.put("docLat", "28.5682852");
    params.put("docLong", "77.3956855");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 400);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid solution type");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC20_ValidateOtpforAgreement")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC21_UploadShopPhoto_Invalid_EntityType() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "merchant_common_onboard");
    params.put("entityType", "INVALID_TYPE");
    params.put("leadId", leadId);
    params.put("pageNo", "0");
    params.put("docCount", "0");
    params.put("docType", "shopFrontPhoto");
    params.put("docProvided", "shopFrontPhoto");
    params.put("merchantCustId", custId);
    params.put("docLat", "28.5682852");
    params.put("docLong", "77.3956855");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 200);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid entity type");
}

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC21_UploadShopPhoto")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC22_UploadAadharPhoto1() throws Exception {

        // String endPoint = P.API.get("SubmitDocs");


        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "merchant_common_onboard");
        //  params.put("channel", channel);
        params.put("entityType", "INDIVIDUAL");
        params.put("leadId", leadId);
        params.put("pageNo", "1");
        params.put("docCount", "2");
        params.put("docType", "poi");
        params.put("docProvided", "aadhaar");
        params.put("merchantCustId", custId);
        params.put("docLat", "28.5682852");
        params.put("docLong", "77.3956855");
        params.put("docDistance", "0.3397966438531691");

        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);


        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC21_UploadShopPhoto")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC22_UploadAadharPhoto1_Invalid_LeadId() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "merchant_common_onboard");
    params.put("entityType", "INDIVIDUAL");
    params.put("leadId", "invalid_lead_id");
    params.put("pageNo", "1");
    params.put("docCount", "2");
    params.put("docType", "poi");
    params.put("docProvided", "aadhaar");
    params.put("merchantCustId", custId);
    params.put("docLat", "28.5682852");
    params.put("docLong", "77.3956855");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 400);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid lead ID");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC21_UploadShopPhoto")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC22_UploadAadharPhoto1_Invalid_PageNo() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "merchant_common_onboard");
    params.put("entityType", "INDIVIDUAL");
    params.put("leadId", leadId);
    params.put("pageNo", "3"); // Invalid page number for Aadhaar
    params.put("docCount", "2");
    params.put("docType", "poi");
    params.put("docProvided", "aadhaar");
    params.put("merchantCustId", custId);
    params.put("docLat", "28.5682852");
    params.put("docLong", "77.3956855");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 400);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid page number for Aadhaar document");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC21_UploadShopPhoto")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC22_UploadAadharPhoto1_Invalid_DocCount() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "merchant_common_onboard");
    params.put("entityType", "INDIVIDUAL");
    params.put("leadId", leadId);
    params.put("pageNo", "1");
    params.put("docCount", "5"); // Invalid doc count for Aadhaar
    params.put("docType", "poi");
    params.put("docProvided", "aadhaar");
    params.put("merchantCustId", custId);
    params.put("docLat", "28.5682852");
    params.put("docLong", "77.3956855");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 200);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid document count for Aadhaar");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC21_UploadShopPhoto")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC22_UploadAadharPhoto1_Invalid_DocType() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "merchant_common_onboard");
    params.put("entityType", "INDIVIDUAL");
    params.put("leadId", leadId);
    params.put("pageNo", "1");
    params.put("docCount", "2");
    params.put("docType", "invalid_doc_type");
    params.put("docProvided", "aadhaar");
    params.put("merchantCustId", custId);
    params.put("docLat", "28.5682852");
    params.put("docLong", "77.3956855");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 200);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid document type");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC21_UploadShopPhoto")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC22_UploadAadharPhoto1_Missing_Required_Fields() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    // Not setting any parameters
    
    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 400);
    //Assert.assertEquals(submitDocs.path("message"), "Required fields missing");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC21_UploadShopPhoto")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC22_UploadAadharPhoto1_Invalid_Coordinates() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "merchant_common_onboard");
    params.put("entityType", "INDIVIDUAL");
    params.put("leadId", leadId);
    params.put("pageNo", "1");
    params.put("docCount", "2");
    params.put("docType", "poi");
    params.put("docProvided", "aadhaar");
    params.put("merchantCustId", custId);
    params.put("docLat", "invalid_lat");
    params.put("docLong", "invalid_long");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 200);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid coordinates format");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC21_UploadShopPhoto")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC22_UploadAadharPhoto1_Invalid_DocProvided() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "merchant_common_onboard");
    params.put("entityType", "INDIVIDUAL");
    params.put("leadId", leadId);
    params.put("pageNo", "1");
    params.put("docCount", "2");
    params.put("docType", "poi");
    params.put("docProvided", "invalid_doc");
    params.put("merchantCustId", custId);
    params.put("docLat", "28.5682852");
    params.put("docLong", "77.3956855");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 400);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid document provided");
}

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC22_UploadAadharPhoto1")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC22_UploadAadharPhoto2() throws Exception {

        // String endPoint = P.API.get("SubmitDocs");


        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "merchant_common_onboard");
        //  params.put("channel", channel);
        params.put("entityType", "INDIVIDUAL");
        params.put("leadId", leadId);
        params.put("pageNo", "2");
        params.put("docCount", "2");
        params.put("docType", "poi");
        params.put("docProvided", "aadhaar");
        params.put("merchantCustId", custId);
        params.put("docLat", "28.5682852");
        params.put("docLong", "77.3956855");
        params.put("docDistance", "0.3397966438531691");

        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);


        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
       // int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(submitDocs.getStatusCode(), 400);


    }


    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC22_UploadAadharPhoto2")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC23_UploadPanDoc() throws Exception {

        // String endPoint = P.API.get("SubmitDocs");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "merchant_common_onboard");
        //  params.put("channel", channel);
        params.put("entityType", "INDIVIDUAL");
        params.put("leadId", leadId);
        params.put("pageNo", "0");
        params.put("docCount", "0");
        params.put("docType", "pan");
        params.put("docProvided", "pan");
        params.put("merchantCustId", custId);
        params.put("docLat", "28.5682852");
        params.put("docLong", "77.3956855");
        params.put("docDistance", "0.3397966438531691");

        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);


        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC22_UploadAadharPhoto2")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC23_UploadPanDoc_Invalid_LeadId() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "merchant_common_onboard");
    params.put("entityType", "INDIVIDUAL");
    params.put("leadId", "invalid_lead_id");
    params.put("pageNo", "0");
    params.put("docCount", "0");
    params.put("docType", "pan");
    params.put("docProvided", "pan");
    params.put("merchantCustId", custId);
    params.put("docLat", "28.5682852");
    params.put("docLong", "77.3956855");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 400);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid lead ID");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC22_UploadAadharPhoto2")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC23_UploadPanDoc_Invalid_MerchantCustId() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "merchant_common_onboard");
    params.put("entityType", "INDIVIDUAL");
    params.put("leadId", leadId);
    params.put("pageNo", "0");
    params.put("docCount", "0");
    params.put("docType", "pan");
    params.put("docProvided", "pan");
    params.put("merchantCustId", "invalid_cust_id");
    params.put("docLat", "28.5682852");
    params.put("docLong", "77.3956855");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 500);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid merchant customer ID");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC22_UploadAadharPhoto2")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC23_UploadPanDoc_Invalid_Coordinates() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "merchant_common_onboard");
    params.put("entityType", "INDIVIDUAL");
    params.put("leadId", leadId);
    params.put("pageNo", "0");
    params.put("docCount", "0");
    params.put("docType", "pan");
    params.put("docProvided", "pan");
    params.put("merchantCustId", custId);
    params.put("docLat", "invalid_lat");
    params.put("docLong", "invalid_long");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 200);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid coordinates format");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC22_UploadAadharPhoto2")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC23_UploadPanDoc_Invalid_DocType() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "merchant_common_onboard");
    params.put("entityType", "INDIVIDUAL");
    params.put("leadId", leadId);
    params.put("pageNo", "0");
    params.put("docCount", "0");
    params.put("docType", "invalid_doc_type");
    params.put("docProvided", "pan");
    params.put("merchantCustId", custId);
    params.put("docLat", "28.5682852");
    params.put("docLong", "77.3956855");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 200);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid document type");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC22_UploadAadharPhoto2")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC23_UploadPanDoc_Missing_Required_Fields() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    // Not setting any parameters
    
    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 400);
    //Assert.assertEquals(submitDocs.path("message"), "Required fields missing");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC22_UploadAadharPhoto2")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC23_UploadPanDoc_Invalid_SolutionType() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "invalid_solution_type");
    params.put("entityType", "INDIVIDUAL");
    params.put("leadId", leadId);
    params.put("pageNo", "0");
    params.put("docCount", "0");
    params.put("docType", "pan");
    params.put("docProvided", "pan");
    params.put("merchantCustId", custId);
    params.put("docLat", "28.5682852");
    params.put("docLong", "77.3956855");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 400);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid solution type");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC22_UploadAadharPhoto2")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC23_UploadPanDoc_Invalid_EntityType() throws Exception {
    Map<String, String> params = new HashMap<String, String>();
    params.put("solutionType", "merchant_common_onboard");
    params.put("entityType", "INVALID_TYPE");
    params.put("leadId", leadId);
    params.put("pageNo", "0");
    params.put("docCount", "0");
    params.put("docType", "pan");
    params.put("docProvided", "pan");
    params.put("merchantCustId", custId);
    params.put("docLat", "28.5682852");
    params.put("docLong", "77.3956855");
    params.put("docDistance", "0.3397966438531691");
    params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));

    SubmitDocs v3DocSubmit = new SubmitDocs();
    Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
    
    Assert.assertEquals(submitDocs.getStatusCode(), 200);
    //Assert.assertEquals(submitDocs.path("message"), "Invalid entity type");
}


    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC23_UploadPanDoc")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC24_McoLeadSubmitAfterUploadingAllDocs() throws Exception {

        requestPath = "MerchantService/V3/SubmitMerchant/FinalMcoLeadSubmitAfterUploadingAllDocs.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("AddressUUID", AddressUUID);


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);

        Response createMCOLead = middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }


    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC24_McoLeadSubmitAfterUploadingAllDocs")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC25_McoLeadFetchDocStatus() throws Exception {

        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solution", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);
        queryParams.put("merchantCustId", custId);
        queryParams.put("channel", "GG_APP");


        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj, headers, queryParams);

        Assert.assertEquals(respObj.getStatusCode(), 200);
        ShopphotoDMSID = respObj.path("uploadedDocDetailsSet[0].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['0']");
        BusinessOwnerPhotoDMSID = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].uuid");
        AadharPhotoDMSID1 = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
        AadharPhotoDMSID2 = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['2']");
        PanphotoDMSID = respObj.path("uploadedDocDetailsSet[3].uploadedDocs[0].uuid");
        CancelledChequePhotoDMSID = respObj.path("uploadedDocDetailsSet[4].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['0']");


        System.out.println(ShopphotoDMSID + BusinessOwnerPhotoDMSID + AadharPhotoDMSID1 + AadharPhotoDMSID2 + PanphotoDMSID + CancelledChequePhotoDMSID);
        // Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC25_McoLeadFetchDocStatus")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC26_McoIndividualLeadQC() throws Exception {

        DBConnection dbConnectionObj = new DBConnection();


        int Ubmid = dbConnectionObj.getUserBusinessMappingId(Mobile, "merchant_common_onboard");

        dbConnectionObj.assignAgentViaDB("1152", Ubmid);


        System.out.println("Ubmid is " + Ubmid);

        Long MCOIndividualWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);

        System.out.println("MCOIndividualWorkflowStatusId is " + MCOIndividualWorkflowStatusId);

        //Save value of MCOIndividualWorkflowStatusId in string
        String wfsid = String.valueOf(MCOIndividualWorkflowStatusId);
        System.out.println("wfsid is " + wfsid);

        requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadMcoIndividualWithPan.json";
        EditLead EditLeadObj = new EditLead(leadId, requestPath);
        EditLeadObj.getProperties().setProperty("uuidAadharPhoto1", AadharPhotoDMSID1);
        EditLeadObj.getProperties().setProperty("uuidAadharPhoto2", AadharPhotoDMSID2);
        EditLeadObj.getProperties().setProperty("uuidShopPhoto", ShopphotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidBankPhoto", CancelledChequePhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto", BusinessOwnerPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto1", BusinessOwnerPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidPanPhoto", PanphotoDMSID);
        EditLeadObj.getProperties().setProperty("workflowStatusId", wfsid);
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("action", "SUBMIT");
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging7.paytm.com");
        // headers.put("session_token", QCAgentsToken);
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("Cookie", XMWCookie);
        Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);

    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC25_McoLeadFetchDocStatus")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC26_McoIndividualLeadQC_Invalid_Mobile() throws Exception {
    DBConnection dbConnectionObj = new DBConnection();
    
    try {
        int Ubmid = dbConnectionObj.getUserBusinessMappingId("invalid_mobile", "merchant_common_onboard");
        Assert.fail("Should have thrown exception for invalid mobile");
    } catch (Exception e) {
        Assert.assertTrue(e.getMessage().contains("Invalid mobile number"));
    }
}

/*@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC25_McoLeadFetchDocStatus")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC26_McoIndividualLeadQC_Invalid_Agent_Id() throws Exception {
    DBConnection dbConnectionObj = new DBConnection();
    int Ubmid = dbConnectionObj.getUserBusinessMappingId(Mobile, "merchant_common_onboard");
    
    try {
        dbConnectionObj.assignAgentViaDB("invalid_agent_id", Ubmid);
        Assert.fail("Should have thrown exception for invalid agent ID");
    } catch (Exception e) {
        Assert.assertTrue(e.getMessage().contains("Invalid agent ID"));
    }
}*/

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC25_McoLeadFetchDocStatus")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC26_McoIndividualLeadQC_Invalid_UUID_Photos() throws Exception {
    DBConnection dbConnectionObj = new DBConnection();
    int Ubmid = dbConnectionObj.getUserBusinessMappingId(Mobile, "merchant_common_onboard");
    dbConnectionObj.assignAgentViaDB("1152", Ubmid);
    Long MCOIndividualWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);
    String wfsid = String.valueOf(MCOIndividualWorkflowStatusId);

    requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadMcoIndividualWithPan.json";
    EditLead EditLeadObj = new EditLead(leadId, requestPath);
    EditLeadObj.getProperties().setProperty("uuidAadharPhoto1", "invalid_uuid");
    EditLeadObj.getProperties().setProperty("uuidAadharPhoto2", "invalid_uuid");
    EditLeadObj.getProperties().setProperty("workflowStatusId", wfsid);

    Map<String, String> queryParams = new HashMap<>();
    queryParams.put("action", "SUBMIT");
    
    Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);
    Assert.assertEquals(QCMCOLead.getStatusCode(), 400);
  //  Assert.assertEquals(QCMCOLead.path("message"), "Invalid document UUIDs");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC25_McoLeadFetchDocStatus")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC26_McoIndividualLeadQC_Missing_Required_Photos() throws Exception {
    DBConnection dbConnectionObj = new DBConnection();
    int Ubmid = dbConnectionObj.getUserBusinessMappingId(Mobile, "merchant_common_onboard");
    dbConnectionObj.assignAgentViaDB("1152", Ubmid);
    Long MCOIndividualWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);
    String wfsid = String.valueOf(MCOIndividualWorkflowStatusId);

    requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadMcoIndividualWithPan.json";
    EditLead EditLeadObj = new EditLead(leadId, requestPath);
    // Not setting any photo UUIDs
    EditLeadObj.getProperties().setProperty("workflowStatusId", wfsid);

    Map<String, String> queryParams = new HashMap<>();
    queryParams.put("action", "SUBMIT");
    
    Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);
    Assert.assertEquals(QCMCOLead.getStatusCode(), 401);
   // Assert.assertEquals(QCMCOLead.path("message"), "Required documents missing");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC25_McoLeadFetchDocStatus")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC26_McoIndividualLeadQC_Invalid_WorkflowStatusId() throws Exception {
    DBConnection dbConnectionObj = new DBConnection();
    int Ubmid = dbConnectionObj.getUserBusinessMappingId(Mobile, "merchant_common_onboard");
    dbConnectionObj.assignAgentViaDB("1152", Ubmid);

    requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadMcoIndividualWithPan.json";
    EditLead EditLeadObj = new EditLead(leadId, requestPath);
    EditLeadObj.getProperties().setProperty("uuidAadharPhoto1", AadharPhotoDMSID1);
    EditLeadObj.getProperties().setProperty("uuidAadharPhoto2", AadharPhotoDMSID2);
    EditLeadObj.getProperties().setProperty("workflowStatusId", "invalid_workflow_id");

    Map<String, String> queryParams = new HashMap<>();
    queryParams.put("action", "SUBMIT");
    
    Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);
    Assert.assertEquals(QCMCOLead.getStatusCode(), 500);
    Assert.assertEquals(QCMCOLead.path("message"), "Invalid workflow status ID");
}

@Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC25_McoLeadFetchDocStatus")
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC26_McoIndividualLeadQC_Invalid_Headers() throws Exception {
    DBConnection dbConnectionObj = new DBConnection();
    int Ubmid = dbConnectionObj.getUserBusinessMappingId(Mobile, "merchant_common_onboard");
    dbConnectionObj.assignAgentViaDB("1152", Ubmid);
    Long MCOIndividualWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);
    String wfsid = String.valueOf(MCOIndividualWorkflowStatusId);

    requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadMcoIndividualWithPan.json";
    EditLead EditLeadObj = new EditLead(leadId, requestPath);
    EditLeadObj.getProperties().setProperty("uuidAadharPhoto1", AadharPhotoDMSID1);
    EditLeadObj.getProperties().setProperty("workflowStatusId", wfsid);

    Map<String, String> queryParams = new HashMap<>();
    queryParams.put("action", "SUBMIT");
    
    Map<String, String> invalidHeaders = new HashMap<>();
    invalidHeaders.put("Host", "invalid-host");
    
    Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, invalidHeaders);
    Assert.assertEquals(QCMCOLead.getStatusCode(), 500);
    Assert.assertEquals(QCMCOLead.path("message"), "Unauthorized access");
}


}

