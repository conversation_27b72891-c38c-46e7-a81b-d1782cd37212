package OCL.SFtoOE2024;

import OCL.Individual.SoundBox.FlowSoundBox;
import Request.MerchantService.v1.Enterprise.*;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.google.gson.JsonObject;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.json.JSONObject;
import java.util.ArrayList;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static OCL.SFtoOE2024.SFtoOEUtils.*;

public class BusinessLeadCreationB2B extends BaseMethod {
    // Middleware Services
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final org.apache.logging.log4j.Logger LOGGER = org.apache.logging.log4j.LogManager.getLogger(FlowSoundBox.class);

    public String AgentNumber = "7771216290";
    public String LoginPassword = "paytm@123";
    public String model = "B2B";
    public String subModel = "Aggregator";
    public String nameAsPerNSDL = "TOUCH WOOD LIMITED";
    public String dateOfIncorporation = "2024-11-21";
    public String gstExempted = "No";
    public String legalName = "RONIT ARYA";
    public String tradeName = "ARYA ENTERPRISES";
    public String segment = "Government";
    public String subSegment = "Central Department";
    public String isInvolvedInInternationalTransaction = "No";
    public String isInternationalMerchant = "false";
    public String onboardingEntity = "OCL";
    public String email = "<EMAIL>";
    public String flowType = "offline";
    public String entityType = "PROPRIETORSHIP";//"PROPRIETORSHIP" "GOVERNMENT" ;
    public String pan = randomPanNumber("PROPRIETORSHIP");
    public String onboardingDocumentType = "PAN";
    public String onboardingDocumentValue = pan;
    public String gstin = randomGSTNumber(pan);
    public final String solutionType = "enterprise_merchant_business";
    public String leadId = "";
    public String host = getHost();
    public String cookie = OEpanelCookie(AgentNumber , LoginPassword , host);
    public String isValidated = "";
    public String firstName = "";
    public String middleName = "";
    public String lastName = "";
    public String channel = "OE_PANEL";
    public String leadType = "BUSINESS_LEAD";
    public String category = "Food";
    public String CategoryCode = "";
    public String pincode = "814149";
    public HashMap<String, String> solutionadditioninformetadata;
    public HashMap<String, String> registeredaddress;
    public String retailrelatedbusinessuuid = "";
    public String kycverifygstsuccessresponse = "";
    public String nameAsPerTan = "TOUCH WOOD LIMITED";
    public String state = "";
    public String city = "";
    public String country = "";
    public BusinessLeadCreationB2B() throws Exception {
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch the Business Lead Details", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchEntityType() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Cookie", cookie);
        Map<String, String> queryParams = new HashMap<>();
        if(onboardingDocumentType.equals("TAN")){
            queryParams.put("typeList", "entityType_tan_gpid");
            EnterprisefetchAllResources obj = new EnterprisefetchAllResources();
            Response respobj = middlewareServicesObject.fetchAllResourcesEnterprise(obj, headers, queryParams);
            int StatusCode = respobj.getStatusCode();
            Assert.assertEquals(StatusCode, 200);
            if (StatusCode == 200) {
                HashMap<Object, Object> dataMapfetchallresourcesmodel = respobj.jsonPath().get("dataMap");
                LOGGER.info("Data Map is : " + dataMapfetchallresourcesmodel);
            }
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch model from resources")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchAllResourcesmodel() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Cookie", cookie);       // "X-MW-TOKEN-EX=7ljjrn6Nnqw%2F%2BZ1Axr%2BfJD49Juouv%2FhjePWnY%2Bk%2FzeYd64gzAOOwcZLNHDm4Tu0vH41LoQ3Yc3tdfnQ8QI90qg%3D%3D; X-MW-TOKEN-EXTERNAL=aiuGKDS8e3qQatX4GISnLNvlOqcv15RtxdFNgA%2FW1OueEVT1KdrCYc45%2BnpqxEyOfrEmEwRoA%2BdH%2BYR7ki23Vg%3D%3D; X-MW-TOKEN-EX-V2=71fFZhKTpxR8KMr5S3xzZ836iNi%2F7h%2F3utDcucjCYcMjg0tr417HYRBGWH6uiO3xD8PEVgtrpIMqo3lnDXArRXJeumAw; X-MW-TOKEN-EXTERNAL-V2=71fFZhKTpxR8KMr5S3xzZ836iNi%2F7h%2F3utDcucjCYcMjg0tr417HYRBGWH6uiO3xD8PEVgtrpIMqo3lnDXArRXJeumAw; X-MW-TOKEN=9b91c4e8-bb77-4a56-92dd-f4baeafb43cc");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("typeList", "model_offline");
        EnterprisefetchAllResources obj = new EnterprisefetchAllResources();
        Response respobj = middlewareServicesObject.fetchAllResourcesEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        if (StatusCode == 200) {
            HashMap<Object, Object> dataMapfetchallresourcesmodel = respobj.jsonPath().get("dataMap");
            LOGGER.info("Data Map is : " + dataMapfetchallresourcesmodel);
        }
    }

    @Test(priority = 2, groups = {"Regression"}, description = "Fetch the Submodel for the Business Lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchAllResourcesSubmodel() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Cookie", cookie);       // "X-MW-TOKEN-EX=7ljjrn6Nnqw%2F%2BZ1Axr%2BfJD49Juouv%2FhjePWnY%2Bk%2FzeYd64gzAOOwcZLNHDm4Tu0vH41LoQ3Yc3tdfnQ8QI90qg%3D%3D; X-MW-TOKEN-EXTERNAL=aiuGKDS8e3qQatX4GISnLNvlOqcv15RtxdFNgA%2FW1OueEVT1KdrCYc45%2BnpqxEyOfrEmEwRoA%2BdH%2BYR7ki23Vg%3D%3D; X-MW-TOKEN-EX-V2=71fFZhKTpxR8KMr5S3xzZ836iNi%2F7h%2F3utDcucjCYcMjg0tr417HYRBGWH6uiO3xD8PEVgtrpIMqo3lnDXArRXJeumAw; X-MW-TOKEN-EXTERNAL-V2=71fFZhKTpxR8KMr5S3xzZ836iNi%2F7h%2F3utDcucjCYcMjg0tr417HYRBGWH6uiO3xD8PEVgtrpIMqo3lnDXArRXJeumAw; X-MW-TOKEN=9b91c4e8-bb77-4a56-92dd-f4baeafb43cc");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("typeList", "subModel_B2C");
        EnterprisefetchAllResources obj = new EnterprisefetchAllResources();
        Response respobj = middlewareServicesObject.fetchAllResourcesEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        if (StatusCode == 200) {
            HashMap<Object, Object> dataMapfetchallresourcesSubmodel = respobj.jsonPath().get("dataMap");
            LOGGER.info("Data Map is : " + dataMapfetchallresourcesSubmodel);
        }
    }

    @Test(priority = 3, groups = {"Regression"}, description = "Fetch the lead details from the given pan", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddata() {
        Map<String, String> queryParams = new HashMap<>();
        if(onboardingDocumentType.equals("pan")){
            queryParams.put("pan", onboardingDocumentValue);
        }
        else{
            queryParams.put("pan", "undefined");
        }
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", leadType);

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Cookie", cookie); //, cookie);


        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 4, groups = {"Regression"}, description = "Verify the given pan is valid or not", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void validatePan() {
        if (onboardingDocumentType.equals("Pan")) {
            Map<String, String> queryParams = new HashMap<>();
            Map<String, String> headers = new HashMap<>();
            headers.put("Host", host);
            headers.put("Cookie", cookie);
            Map<String, String> body = new HashMap<>();
            body.put("pan", pan);
            body.put("solutionType", solutionType);

            EnterpriseValidatePan obj = new EnterpriseValidatePan();
            Response respobj = middlewareServicesObject.enterpriseValidatePan(obj, headers, body);
            int StatusCode = respobj.statusCode();
            Assert.assertEquals(StatusCode, 200);
            if (StatusCode == 200) {
                pan = respobj.jsonPath().getString("pan").toString();
                isValidated = respobj.jsonPath().getString("isValidated").toString();
                firstName = respobj.jsonPath().getString("firstName").toString();
                middleName = respobj.jsonPath().getString("middleName").toString();
                lastName = respobj.jsonPath().getString("lastName").toString();
            }


        }
    }

    @Test(priority = 5, groups = {"Regression"}, description = "Fetch Category", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchCategory() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Cookie", cookie);
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("solTypeLevel2", flowType);
        EnterpriseGetCategorySubCategory obj = new EnterpriseGetCategorySubCategory();
        Response respobj = middlewareServicesObject.getCategorySubCategoryEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        if (StatusCode == 200) {
            ArrayList<Map<String, Object>> dataList = respobj.jsonPath().get("catSubList");
            List<String> CategoriesFetched = new ArrayList<>();
            for (Map<String, Object> data : dataList) {
                if (data.values().toArray()[0].toString().equals(category)) {
                    CategoryCode = data.values().toArray()[1].toString();
                }
                CategoriesFetched.add(data.values().toArray()[0].toString());
            }
            LOGGER.info("Categories Fetched are : " + CategoriesFetched);
            LOGGER.info("Categories Fetched are : " + CategoryCode);
            if (CategoriesFetched.contains(category)) {
                LOGGER.info("Category found in the list");
            } else {
                LOGGER.error("Category not found in the list");
            }
        }
    }

    @Test(priority = 6, groups = {"Regression"}, description = "Fetch Sub Category", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchSubCategory() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Cookie", cookie); //"X-MW-TOKEN-EX=7ljjrn6Nnqw%2F%2BZ1Axr%2BfJD49Juouv%2FhjePWnY%2Bk%2FzeYd64gzAOOwcZLNHDm4Tu0vH41LoQ3Yc3tdfnQ8QI90qg%3D%3D; X-MW-TOKEN-EXTERNAL=aiuGKDS8e3qQatX4GISnLNvlOqcv15RtxdFNgA%2FW1OueEVT1KdrCYc45%2BnpqxEyOfrEmEwRoA%2BdH%2BYR7ki23Vg%3D%3D; X-MW-TOKEN-EX-V2=71fFZhKTpxR8KMr5S3xzZ836iNi%2F7h%2F3utDcucjCYcMjg0tr417HYRBGWH6uiO3xD8PEVgtrpIMqo3lnDXArRXJeumAw; X-MW-TOKEN-EXTERNAL-V2=71fFZhKTpxR8KMr5S3xzZ836iNi%2F7h%2F3utDcucjCYcMjg0tr417HYRBGWH6uiO3xD8PEVgtrpIMqo3lnDXArRXJeumAw; X-MW-TOKEN=9b91c4e8-bb77-4a56-92dd-f4baeafb43cc");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("solTypeLevel2", flowType);
        EnterpriseGetSubcategory obj = new EnterpriseGetSubcategory(CategoryCode);
        Response respobj = middlewareServicesObject.getSubCategoryEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.getStatusCode();
    }

    @Test(priority = 7, groups = {"Regression"}, description = "Fetch GST from Pan", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchGstFromPan() {
        if (onboardingDocumentType.equals("Pan")) {
            Map<String, String> headers = new HashMap<>();
            headers.put("Host", host);
            headers.put("Cookie", cookie);

            Map<String, String> body = new HashMap<>();
            body.put("pan", pan);
            FetchGstFromPan obj = new FetchGstFromPan();
            Response respobj = middlewareServicesObject.fetchGstFromPan(obj, headers, body);
            int StatusCode = respobj.getStatusCode();
            // This api fails since staging environment is not able to fetch the GST from the given PAN
            Assert.assertEquals(StatusCode, 400);

        }
    }

    @Test(priority = 8, groups = {"Regression"}, description = "Create Business Lead", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyGst() throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Host",host);
        headers.put("Cookie", cookie);
        Map<String, String> body = new HashMap<>();
        body.put("gstin", gstin);
        VerifyGST obj = new VerifyGST();
        Response respobj = middlewareServicesObject.verifyGST(obj, headers, body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        if (StatusCode == 200) {
            kycverifygstsuccessresponse = respobj.getBody().asString();
            JsonEscaper jsonEscaper = new JsonEscaper(kycverifygstsuccessresponse);
            String escapedJson = jsonEscaper.getEscapedJson();
            kycverifygstsuccessresponse = escapedJson;
            LOGGER.info("KYC Verify GST Success Response is : " + kycverifygstsuccessresponse);
        }
    }

    @Test(priority = 9, groups = {"Regression"}, description = "Fetch PinCode Details", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchPinCodeDetails() throws InterruptedException {
        Map<String, String> headers = new HashMap<>();
        headers.put("Host",host);
        headers.put("Cookie", cookie);

        EnterpriseFetchPinCodeDetails obj = new EnterpriseFetchPinCodeDetails(pincode);
        Response respobj = middlewareServicesObject.fetchPinCodeDetailsEnterprise(obj, headers);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Thread.sleep(2000);
        if (StatusCode == 200) {
            state = respobj.jsonPath().getString("pincodeDetailsList[0].state").toString();
            city = respobj.jsonPath().getString("pincodeDetailsList[0].city").toString();
            country = respobj.jsonPath().getString("pincodeDetailsList[0].country").toString();
            LOGGER.info("State is : " + state);
            LOGGER.info("City is : " + city);
            LOGGER.info("Country is : " + country);
        }

    }

    @Test(priority = 10, groups = {"Regression"}, description = "Create Business Lead", retryAnalyzer = RetryAnalyzer.class , dependsOnMethods = "fetchPinCodeDetails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createBusinessLead() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("solutionTypeLevel2", flowType);
        queryParams.put("partialSave", "true");

        Map<String, String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie", cookie);
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36");

        Map<String, String> body = new HashMap<>();
        if (onboardingDocumentType.equals("PAN")) {
            body.put("pan", pan);
        } else {
            if (onboardingDocumentType.equals("TAN")){
                body.put("NAME_AS_PER_TAN" , nameAsPerTan);
            }
            body.put("ONBOARDING_DOCUMENT_TYPE" , onboardingDocumentType);
            body.put("ONBOARDING_DOCUMENT_VALUE" , onboardingDocumentValue);
        }
        body.put("gstin", gstin);
        body.put("entityType", entityType);
        body.put("MODEL", model);
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION", dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL", nameAsPerNSDL);
        body.put("GST_EXEMPTED", gstExempted);
        body.put("LEGAL_NAME", legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION", isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT", isInternationalMerchant);
        body.put("ONBOARDING_ENTITY", onboardingEntity);
        body.put("FLOW_TYPE", flowType);
        body.put("pincode" , pincode);
        body.put("state" , state);
        body.put("city" , city);
        body.put("country" , country);
        body.put("kycverifygstsuccessresponse" , kycverifygstsuccessresponse);


        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj, headers, queryParams, body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        leadId = respobj.jsonPath().getString("leadId").toString();
        LOGGER.info("Lead Id is : " + leadId);
        Thread.sleep(5000);
    }

    @Test(priority = 11, groups = {"Regression"}, description = "Fetch Business Lead Information", retryAnalyzer = RetryAnalyzer.class , dependsOnMethods = "createBusinessLead" )

    public void fetchBusinessLeadInformation() throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("leadId",leadId);

        FetchBusinessLead obj = new FetchBusinessLead();
        Response respobj = middlewareServicesObject.fetchBusinessLeadInformation(obj, headers, queryParams);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        if (StatusCode == 200) {
            solutionadditioninformetadata = respobj.jsonPath().get("solution.solutionAdditionalInfo");
            retailrelatedbusinessuuid = respobj.jsonPath().get("retailRelatedBusiness.relatedBusinessUuid").toString();
            registeredaddress = respobj.jsonPath().get("business.addresses[0]");
            kycverifygstsuccessresponse = respobj.jsonPath().get("solution.solutionAdditionalInfo.KYC_VERIFY_GST_SUCCESS_RESPONSE").toString();
            // Create an instance of JsonEscaper
            JsonEscaper jsonEscaper = new JsonEscaper(kycverifygstsuccessresponse);

            // Get the escaped JSON
            String escapedJson = jsonEscaper.getEscapedJson();
            kycverifygstsuccessresponse = escapedJson;
            LOGGER.info(escapedJson);
            LOGGER.info("Data Map is : " + solutionadditioninformetadata);
        }
    }


    @Test(priority = 12, groups = {"Regression"}, description = "Update Business Lead", retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "fetchBusinessLeadInformation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateBusinessLead() {
        Map<String, String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("cookie", cookie); // Use the same cookie from the previous method
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36");

        // Query Parameters HashMap
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("partialSave", "false");
        queryParams.put("solutionTypeLevel2", flowType);
        queryParams.put("leadId", leadId);

        Map<String, String> body = new HashMap<>();
        if (onboardingDocumentType.equals("PAN")) {
            body.put("pan", pan);
        } else {
            body.put("ONBOARDING_DOCUMENT_TYPE" , onboardingDocumentType);
            body.put("ONBOARDING_DOCUMENT_VALUE" , onboardingDocumentValue);
        }
        body.put("entityType", entityType);
        body.put("relatedBusinessUuid" , retailrelatedbusinessuuid);
        body.put("kycverifygstsuccessresponse" , kycverifygstsuccessresponse);
        body.putAll(solutionadditioninformetadata);
        EnterpriseUpdateBusinessLead obj = new EnterpriseUpdateBusinessLead();
        Response respobj = middlewareServicesObject.updateBusinessLeadEnterprise(obj, headers, body, queryParams);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
}