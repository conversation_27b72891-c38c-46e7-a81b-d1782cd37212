package OCL.SFtoOE2024;

import OCL.Individual.SoundBox.FlowSoundBox;
import Request.MerchantService.v1.Enterprise.*;
import Request.MerchantService.v2.Banks;
import Services.MechantService.MiddlewareServices;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.io.File;
import org.json.JSONObject;
import org.json.JSONArray;

import static OCL.SFtoOE2024.SFtoOEUtils.*;

public class SolutionLeadCreation_OnUs extends BaseMethod {

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final org.apache.logging.log4j.Logger LOGGER = org.apache.logging.log4j.LogManager.getLogger(FlowSoundBox.class);

    public String AgentNumber = "**********";
    public String LoginPassword = "paytm@123";
    public String model = "OnUs";
    //public String subModel = "Aggregator";
    public String nameAsPerNSDL = "TOUCH WOOD LIMITED";
    public String dateOfIncorporation = "2024-11-21";
    public String gstExempted = "No";
    public String legalName = "RONIT ARYA";
    public String tradeName = "ARYA ENTERPRISES";
    //public String segment = "Government";
    //public String subSegment = "Central Department";
    //public String isInvolvedInInternationalTransaction = "No";
    //public String isInternationalMerchant = "false";
    public String onboardingEntity = "OCL";
    public String email = "<EMAIL>";
    public String flowType = "OnUs";
    public String entityType = "PROPRIETORSHIP";
    public String pan = "**********";
    public String gstin = "11**********S64";
    public final String solutionType = "enterprise_merchant_business";
    public String leadId = "673d71f9-5d59-4180-894b-7a449d620d65";
    public String host = getHost();
    public String cookie = "BOSS_SESSION=de32d948-c3bd-488f-9b6e-53026168b0ba; X-MW-TOKEN-EX=tkheaDsoSFpgg3v0Jh%2BHKwQeKbMcNzAQN79s6NNqZZzET0BC4jVpnl8m%2B51KwDrWYTQEbrSQUFxDgVln9mZY7A%3D%3D; X-MW-TOKEN-EXTERNAL=ZSBLzB0JSu8uXIxwbE9gdt2G72feLk%2FnN4LqE2KkX3LVYoKZbcg7Y0Lgymq9w92keVDQdIb%2BPtTMg%2B1%2BLCYAug%3D%3D; X-MW-TOKEN-EX-V2=71fFZhTT8UE4KdW3E3smMZ7pjt6%2F60v6%2FZDBotnCecwhxEtr41vHYRBGX3qoiOjyAcDOVAExTAcMT06fiDlVQ5gUPsDf; X-MW-TOKEN-EXTERNAL-V2=71fFZhTT8UE4KdW3E3smMZ7pjt6%2F60v6%2FZDBotnCecwhxEtr41vHYRBGX3qoiOjyAcDOVAExTAcMT06fiDlVQ5gUPsDf; X-MW-TOKEN=2f9ff9fb-7e81-49ce-84b4-69058e50c79e";
    //public String cookie = "BOSS_SESSION=9cdea8e3-c103-4412-9053-e5477993597f; X-MW-TOKEN-EX=%2BhyC1o%2FYQjBTu3nUSqqzz8Ok9AJKp0onUxKeAXHdzhsiqytChm0KkaSosUlGBvRRUx3V7wg85zpDuFNKQYJQPQ%3D%3D; X-MW-TOKEN-EXTERNAL=iHCNyGTSPyK8uXAcLPwNKh%2Fg1hiUn%2F3vV2WTWQxPvXCG8GvgB2ad0NJhxVQ1hZEZSW6t9EK2OfBEUEUV%2FA7adg%3D%3D; X-MW-TOKEN-EX-V2=71fFZhbF%2FUF4IMP5QHhxZZP%2F0dS6oEijpsLQs8LCeINt2xVr41zHYRBGX3uujOHwDczFVA0mTIhFHo3SJXHgiHz%2F0JvN; X-MW-TOKEN-EXTERNAL-V2=71fFZhbF%2FUF4IMP5QHhxZZP%2F0dS6oEijpsLQs8LCeINt2xVr41zHYRBGX3uujOHwDczFVA0mTIhFHo3SJXHgiHz%2F0JvN; X-MW-TOKEN=b12ebe2f-20f7-43f2-9ea8-5b22c3342009";
    //public String cookie = "BOSS_SESSION=5b9db1d3-cce4-4f9a-b94c-475436d684bf; X-MW-TOKEN-EX=SbuFrDNGRJoXRsmH8vPcS32krCyA6LubrFKrfjhBJte6q0ND9HoKZOZrTwgP9rFAqAvsdDBo9TkvngWR9pKGLw%3D%3D; X-MW-TOKEN-EXTERNAL=yJv2uq%2By4J9i5X%2Bjxta5iF5Zkw5BpM9Bi6ZDtT4HARcFjX%2F4yjM3u1YiRUv0tpIQ%2F1FC%2FEEzGRCbiShF4B8Pig%3D%3D; X-MW-TOKEN-EX-V2=71fFZhXQ9l0hL86jHTtoeZOq0Nqpqkz6r8rQuM%2FCeIN90hBr417HYRBGX3uvhOz7DMPDVgAKhhq8oFXWGwHiMcxtUikL; X-MW-TOKEN-EXTERNAL-V2=71fFZhXQ9l0hL86jHTtoeZOq0Nqpqkz6r8rQuM%2FCeIN90hBr417HYRBGX3uvhOz7DMPDVgAKhhq8oFXWGwHiMcxtUikL; X-MW-TOKEN=e7e84fad-c0e0-4fc7-92e8-6d621fa7d524";
    public String isValidated = "";
    public String firstName = "";
    public String middleName = "";
    public String lastName = "";
    public String channel = "OE_PANEL";
    public String leadType = "BUSINESS_LEAD";
    public String category = "Food";
    public String CategoryCode = "";
    public String pincode = "226020";
    public HashMap<String, String> solutionadditioninformetadata;
    public HashMap<String, String> registeredaddress;
    public String retailrelatedbusinessuuid = "";
    public String kycverifygstsuccessresponse = "";
    public String ifscCode = "HDFC0000001";
    public String bankAccountNumber = "**************";
    public String solutionLeadId = "";
    public String owner_uuid = "";
    public String onboardingDocumentType = "PAN";
    public String onboardingDocumentValue = "**********";
    public String nameAsPerTan = "TOUCH WOOD LIMITED";
    public String workflowStatusId = "";
    public String commercialworkflowStatusId = "";
    public String bankLobworkflowStatusId = "";
    public String aggrementworkflow = "";
    HashMap<String, Object> aggrementdoc = new HashMap<>();
    HashMap<String, List<String>> docDetails = new HashMap<>();
    List<HashMap<String, Object>> documentList = new ArrayList<>();
    List<HashMap<String, Object>> ownerDocument = new ArrayList<>();
    List<HashMap<String, Object>>paymodes = new ArrayList<>();
    HashMap<String, Object> bankLobBenchMarkSRO = new HashMap<>();
    List<List<String>> documents = new ArrayList<>();
    List<HashMap<String, Object>>timelinedetails=new ArrayList<>();

    public SolutionLeadCreation_OnUs() throws Exception {
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch basic details of enterprise lead", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchBasicInfo() {
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, String> queryParams = new HashMap<>();
        headers.put("Cookie", cookie);


        queryParams.put("leadType", "BUSINESS_LEAD,SOLUTION_LEAD");
        queryParams.put("flowType", flowType);
        queryParams.put("entityType", entityType);
        queryParams.put("model", model);
        //queryParams.put("subModel", subModel);
        queryParams.put("onboardingDocumentValue", pan);
        queryParams.put("onboardingDocumentType", onboardingDocumentType);
        queryParams.put("pan", pan);
        EnterpriseFetchBasicInfo obj = new EnterpriseFetchBasicInfo();
        Response respobj = middlewareServicesObject.enterpriseFetchBasicInfo(obj, headers, queryParams);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        if (statusCode == 200) {
            try {
                HashMap<Object, Object> businessLeads = respobj.jsonPath().get("businessLeads[0]");
                HashMap<Object, Object> solutionLeads = respobj.jsonPath().get("solutionLeads[0]");
                LOGGER.info("Business Lead: " + businessLeads);
                LOGGER.info("Business Lead: " + solutionLeads);
            } catch (Exception e) {
                LOGGER.info("Exception: " + e);
            }
        }
    }

    @Test(priority = 2, groups = {"Regression"}, description = "Get All Resources", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getAllResources() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("typeList", "flowType,onboardingEntity_online,onboardingEntity_offline,isPPBLContext," +
                "onboardingEntity_OnUs,onboardingEntity_corporate,searchType,onboardingDocumentType," +
                "model_offline,subModel_B2B,subModel_B2C");

        EnterprisefetchAllResources obj = new EnterprisefetchAllResources();
        Response respobj = middlewareServicesObject.fetchAllResourcesEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        if (StatusCode == 200) {
            HashMap<Object, Object> dataMap = respobj.jsonPath().get("dataMap");
            LOGGER.info("Resources Data Map: " + dataMap);
        }
    }

    @Test(priority = 3, groups = {"Regression"}, description = "Create solution lead", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchBusinessLeadData() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", "enterprise_merchant_parent");
        queryParams.put("channel", channel);
        queryParams.put("businessLeadId", leadId); // Using the leadId from previous test
        queryParams.put("leadType", "SOLUTION_LEAD");
        FetchBusinessLead obj = new FetchBusinessLead();
        Response respobj = middlewareServicesObject.fetchBusinessLeadInformation(obj, headers, queryParams);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);


    }

    @Test(priority = 4, groups = {"Regression"}, description = "Get Solution Type Resources", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getSolutionTypeResources() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("typeList", "solutionType_offline,productType_offline,agreementType_offline," +
                "settlementStrategy_offline_B2C,platform_offline,accountSourceTeam_offline,ncmcMerchant");

        EnterprisefetchAllResources obj = new EnterprisefetchAllResources();
        Response respobj = middlewareServicesObject.fetchAllResourcesEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        if (StatusCode == 200) {
            HashMap<Object, Object> dataMap = respobj.jsonPath().get("dataMap");
            LOGGER.info("Solution Type Resources Data Map: " + dataMap);
        }
    }

    @Test(priority = 5, groups = {"Regression"}, description = "Fetch Merchant Banks", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchMerchantBanks() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);
        headers.put("client_id", "oe-panel-staging");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "**********");
        headers.put("encryption_enabled", "true");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("leadId", leadId);
        queryParams.put("upiFetchOnly", "false");

        EnterpriseFetchMerchantBanks obj = new EnterpriseFetchMerchantBanks();
        Response respobj = middlewareServicesObject.fetchMerchantBanksEnterprise(obj, headers, queryParams);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        if (statusCode == 200) {
            // Extract and log bank details from response
            HashMap<Object, Object> bankDetails = respobj.jsonPath().get(".");
            LOGGER.info("Merchant Bank Details: " + bankDetails);
        }
    }

    @Test(priority = 6, groups = {"Regression"}, description = "Get Bank Details", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getBankDetails() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);


        FetchBankDetailsEnterprise obj = new FetchBankDetailsEnterprise(ifscCode);
        Response respobj = middlewareServicesObject.fetchBankDetailsEnterprise(obj, headers);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        if (statusCode == 200) {
            HashMap<Object, Object> bankDetails = respobj.jsonPath().get(".");
            LOGGER.info("Bank Details: " + bankDetails);
        }
    }

    @Test(priority = 7, groups = {"Regression"}, description = "Penny Drop Verification", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void pennyDropVerification() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);
        headers.put("Host", host);


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "enterprise_merchant_parent");
        queryParams.put("bankName", "HDFC BANK");

        Map<String, String> body = new HashMap<>();
        body.put("bankAccountNumber", bankAccountNumber);
        body.put("ifsc", "HDFC0000001");
        body.put("mobile", "9" + getrandomNumbers(9));

        EnterprisePennyDrop obj = new EnterprisePennyDrop();
        Response respobj = middlewareServicesObject.pennyDropVerification(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        if (statusCode == 200) {
            // Extract and log penny drop verification details
            HashMap<Object, Object> verificationDetails = respobj.jsonPath().get(".");
            LOGGER.info("Penny Drop Verification Details: " + verificationDetails);
        }
    }

}
