package OCL.SFtoOE2024;

import com.fasterxml.jackson.databind.ObjectMapper;

public class JsonEscaper {
    public String kycverifygstsuccessresponse;

    // Constructor to initialize the instance variable
    public JsonEscaper(String kycverifygstsuccessresponse) {
        this.kycverifygstsuccessresponse = kycverifygstsuccessresponse;
    }

    public String getEscapedJson() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(this.kycverifygstsuccessresponse);
    }

    public void main(String[] args) throws Exception {
        // Example input JSON string
        String jsonInput = this.kycverifygstsuccessresponse;

        // Create an instance of JsonEscaper
        JsonEscaper jsonEscaper = new JsonEscaper(jsonInput);

        // Get the escaped JSON
        String escapedJson = jsonEscaper.getEscapedJson();

        // Print the escaped JSON
        System.out.println(escapedJson);
    }
}
