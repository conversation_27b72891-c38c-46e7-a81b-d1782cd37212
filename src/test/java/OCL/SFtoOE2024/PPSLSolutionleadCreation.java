package OCL.SFtoOE2024;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.json.JSONException;
import org.json.JSONObject;
import org.testng.Assert;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;

import OCL.Individual.SoundBox.FlowSoundBox;
import static OCL.SFtoOE2024.SFtoOEUtils.getHost;
import Request.MerchantService.v1.Enterprise.EnterpriseCreateSolutionLead;
import Request.MerchantService.v1.Enterprise.EnterpriseDocumentStatus;
import Request.MerchantService.v1.Enterprise.EnterpriseFetchBasicInfo;
import Request.MerchantService.v1.Enterprise.EnterpriseFetchMerchantBanks;
import Request.MerchantService.v1.Enterprise.EnterprisePennyDrop;
import Request.MerchantService.v1.Enterprise.EnterpriseSolutionInstruments;
import Request.MerchantService.v1.Enterprise.EnterpriseUpdateSolutionLead;
import Request.MerchantService.v1.Enterprise.EnterprisefetchAllResources;
import Request.MerchantService.v1.Enterprise.FetchBankDetailsEnterprise;
import Request.MerchantService.v1.Enterprise.FetchBusinessLead;
import Request.MerchantService.v1.Enterprise.FetchQCDetails;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class PPSLSolutionleadCreation extends BaseMethod {

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final org.apache.logging.log4j.Logger LOGGER = org.apache.logging.log4j.LogManager.getLogger(FlowSoundBox.class);

    public String AgentNumber = "**********";
    public String LoginPassword = "paytm@123";
    public String model = "B2C";
    public String subModel = "Aggregator";
    public String nameAsPerNSDL = "TOUCH WOOD LIMITED";
    public String dateOfIncorporation = "2024-11-21";
    public String gstExempted = "No";
    public String legalName = "RONIT ARYA";
    public String tradeName = "ARYA ENTERPRISES";
    public String segment = "Government";
    public String subSegment = "Central Department";
    public String isInvolvedInInternationalTransaction = "No";
    public String isInternationalMerchant = "false";
    public String onboardingEntity = "PPSL";
    public String email = "<EMAIL>";
    public String flowType = "online";
    public String entityType = "PROPRIETORSHIP";
    public String pan = "**********";
    public String gstin = "11**********S94";
    public final String solutionType = "enterprise_merchant_business";
    public String leadId = "4d012f19-c600-447a-a0fe-fdc9428755ea";
    public String host = getHost();
    public String cookie ="TNC_SESSION=wtflcUwU61NtcI6ZhzRLowvc5G5u47QCnUqElarZx/4A4Lxjl8EhT+DtbgGmoOh5; X-MW-TOKEN-EX=q017zJWyrRwX6MNZfwSJTZnphZvjYQeYLrXKyBx7jAY7nhotisnyCs6qBoNfGbT8ZeEXvJTjJ5FwRLMTT%2BelQA%3D%3D; X-MW-TOKEN-EXTERNAL=ULYpLBam8VpMg58asOrZE5DNLWWCh%2FyyOfmlPmXz3qAmS3yFtoUdZxRpFsIdHtf4LVvlBXHFdSWoqOuWoTzs9w%3D%3D; X-MW-TOKEN-EX-V2=71fFZhfT4UshPpWsWjt2atb9jIm4tVn8r97Ip4nCecwl3hJr41vHYRBGX3qoj%2Bj1DMbBVwBIleA6cBRMAC6N6EyD3NPh; X-MW-TOKEN-EXTERNAL-V2=71fFZhfT4UshPpWsWjt2atb9jIm4tVn8r97Ip4nCecwl3hJr41vHYRBGX3qoj%2Bj1DMbBVwBIleA6cBRMAC6N6EyD3NPh; X-MW-TOKEN=24ec169e-15af-48b7-bf5c-497674c6971a";
    //public String cookie = "_abck=39925C140B4E46936731A606929B08B1~-1~YAAQZ5UvF9f4FnyWAQAA3midjw1+6LVrYh+Oe93IubVo7J/WEB0OB56XAkbcl8tCoP0bUtsKQd7iQ+ZnMbPi5OosZJ0VOmDyd1TQHhcVi9uVyFW5Lf2LfjnWREIPNUORbLhpL5J/U4J/ib0a/G7MB0OAcnGbTphaS+tWHQL5AYOrwxYj/4JH2hzqO5IO4DEruMrtzf6cQy4g0HLjxzL4IiyESydSik8Klq8XlWqCATcPbhjipa+cECCT2ZHm7JH9TfN0NOEuU3GWCWQQ2d9oAk2a/oyERJJhv6pHoOht6KfC7ueUxHWCfuuGwgzhPRSO0SvIJkm5b7duUR6dSeqXFRENLUbF85z8SHflD/x9GCbJxfi0DXFbFaDqTfcI10A6+V4zZqDFs7cexat7XfCcPGhYLMGxV+W0cKpRfQ==~-1~-1~-1; X-MW-TOKEN-EX=GdKvlc%2FCBEx%2BOOAKacyfd2JSqWE4OKaVr88F7BZyJ9C2KZS3sbRBSLZgatIGTan4U8brMSHIbtRmZaPToFa6Vw%3D%3D; X-MW-TOKEN-EXTERNAL=GoD8CSEFiBYeKFcqHw2DikU%2FlRSg9u5T2vzOcXLRUHyQ9XbuS21trdPkK8%2FLWJBsq3UcgOjhDH9Re7ggJCNS1A%3D%3D; X-MW-TOKEN-EX-V2=71fFZhHF7lE8LsO3WH9kaMzvmdHrolKsrNPC%2BI%2FCeIlu0EVr417HYRBGX3utju7yD8TDVwBASUDeCHIG%2Fwn48zTH7vpr; X-MW-TOKEN-EXTERNAL-V2=71fFZhHF7lE8LsO3WH9kaMzvmdHrolKsrNPC%2BI%2FCeIlu0EVr417HYRBGX3utju7yD8TDWAlU0gNe3JhCgO8xhXAzsta1; X-MW-TOKEN=ebec1e6d-c04b-4d01-96ac-eba8fa92eecf";
    public String isValidated = "";
    public String firstName = "";
    public String middleName = "";
    public String lastName = "";
    public String channel = "OE_PANEL";
    public String leadType = "SOLUTION_LEAD";
    public String category = "Food";
    public String CategoryCode = "";
    public String pincode = "518134";
    public HashMap<String, String> solutionadditioninformetadata;
    public HashMap<String, String> registeredaddress;
    public String retailrelatedbusinessuuid = "";
    public String kycverifygstsuccessresponse = "";
    public String ifscCode = "UBIN0808059";
    public String bankAccountNumber = "*************";
    public String solutionLeadId = "";
    public String owner_uuid = "";
    public String onboardingDocumentType = "PAN";
    public String onboardingDocumentValue = "**********";
    public String nameAsPerTan = "TOUCH WOOD LIMITED";
    public String workflowStatusId = "";
    public String commercialworkflowStatusId = "";
    public String bankLobworkflowStatusId = "";
    public String aggrementworkflow = "";
    HashMap<String, Object> aggrementdoc = new HashMap<>();
    HashMap<String, List<String>> docDetails = new HashMap<>();
    List<HashMap<String, Object>> documentList = new ArrayList<>();
    List<HashMap<String, Object>> ownerDocument = new ArrayList<>();
    List<HashMap<String, Object>>paymodes = new ArrayList<>();
    HashMap<String, Object> bankLobBenchMarkSRO = new HashMap<>();
    List<List<String>> documents = new ArrayList<>();
    List<HashMap<String, Object>>timelinedetails=new ArrayList<>();

    public PPSLSolutionleadCreation() throws Exception {
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch basic details of enterprise lead", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchBasicInfo() {
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, String> queryParams = new HashMap<>();
        headers.put("Cookie", cookie);

        queryParams.put("leadType", "BUSINESS_LEAD,SOLUTION_LEAD");
        queryParams.put("flowType", flowType);
        queryParams.put("entityType", entityType);
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("onboardingDocumentValue", pan);
        queryParams.put("onboardingDocumentType", onboardingDocumentType);
        queryParams.put("pan", pan);
        EnterpriseFetchBasicInfo obj = new EnterpriseFetchBasicInfo();
        Response respobj = middlewareServicesObject.enterpriseFetchBasicInfo(obj, headers, queryParams);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        if (statusCode == 200) {
            try {
                HashMap<Object, Object> businessLeads = respobj.jsonPath().get("businessLeads[0]");
                HashMap<Object, Object> solutionLeads = respobj.jsonPath().get("solutionLeads[0]");
                LOGGER.info("Business Lead: " + businessLeads);
                LOGGER.info("Business Lead: " + solutionLeads);
            } catch (Exception e) {
                LOGGER.info("Exception: " + e);
            }
        }
    }

    @Test(priority = 2, groups = {"Regression"}, description = "Get All Resources", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getAllResources() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("typeList", "flowType,onboardingEntity_online,onboardingEntity_offline,isPPBLContext," +
                "onboardingEntity_OnUs,onboardingEntity_corporate,searchType,onboardingDocumentType," +
                "model_online,subModel_B2B,subModel_B2C");

        EnterprisefetchAllResources obj = new EnterprisefetchAllResources();
        Response respobj = middlewareServicesObject.fetchAllResourcesEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        if (StatusCode == 200) {
            HashMap<Object, Object> dataMap = respobj.jsonPath().get("dataMap");
            LOGGER.info("Resources Data Map: " + dataMap);
        }
    }

    @Test(priority = 3, groups = {"Regression"}, description = "Create solution lead", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchBusinessLeadData() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", "enterprise_merchant_parent");
        queryParams.put("channel", channel);
        queryParams.put("businessLeadId", leadId);
        queryParams.put("leadType", "SOLUTION_LEAD");
        FetchBusinessLead obj = new FetchBusinessLead();
        Response respobj = middlewareServicesObject.fetchBusinessLeadInformation(obj, headers, queryParams);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        
        if (statusCode == 200) {
            solutionadditioninformetadata = respobj.jsonPath().get("solution.solutionAdditionalInfo");
            retailrelatedbusinessuuid = respobj.jsonPath().get("retailRelatedBusiness.relatedBusinessUuid");
            registeredaddress = respobj.jsonPath().get("business.addresses[0]");
            LOGGER.info("Solution Additional Info: " + solutionadditioninformetadata);
            LOGGER.info("Retail Related Business UUID: " + retailrelatedbusinessuuid);
            LOGGER.info("Registered Address: " + registeredaddress);
        }
    }

    @Test(priority = 4, groups = {"Regression"}, description = "Get Solution Type Resources", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getSolutionTypeResources() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("typeList", "solutionType_online,productType_online,agreementType_online," +
                "settlementStrategy_online_B2C,platform_online,accountSourceTeam_online");

        EnterprisefetchAllResources obj = new EnterprisefetchAllResources();
        Response respobj = middlewareServicesObject.fetchAllResourcesEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        if (StatusCode == 200) {
            HashMap<Object, Object> dataMap = respobj.jsonPath().get("dataMap");
            LOGGER.info("Solution Type Resources Data Map: " + dataMap);
        }
    }

    @Test(priority = 5, groups = {"Regression"}, description = "Fetch Merchant Banks", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchMerchantBanks() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);
        headers.put("client_id", "oe-panel-staging");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "**********");
        headers.put("encryption_enabled", "true");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("leadId", leadId);
        queryParams.put("upiFetchOnly", "false");

        EnterpriseFetchMerchantBanks obj = new EnterpriseFetchMerchantBanks();
        Response respobj = middlewareServicesObject.fetchMerchantBanksEnterprise(obj, headers, queryParams);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        if (statusCode == 200) {
            // Extract and log bank details from response
            HashMap<Object, Object> bankDetails = respobj.jsonPath().get(".");
            LOGGER.info("Merchant Bank Details: " + bankDetails);
        }
    }

    @Test(priority = 6, groups = {"Regression"}, description = "Get Bank Details", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getBankDetails() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);

        FetchBankDetailsEnterprise obj = new FetchBankDetailsEnterprise(ifscCode);
        Response respobj = middlewareServicesObject.fetchBankDetailsEnterprise(obj, headers);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        if (statusCode == 200) {
            HashMap<Object, Object> bankDetails = respobj.jsonPath().get(".");
            LOGGER.info("Bank Details: " + bankDetails);
        }
    }

    @Test(priority = 7, groups = {"Regression"}, description = "Penny Drop Verification", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void pennyDropVerification() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);
        headers.put("Host", host);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "enterprise_merchant_parent");
        queryParams.put("bankName", "UNION BANK OF INDIA");

        Map<String, String> body = new HashMap<>();
        body.put("bankAccountNumber", bankAccountNumber);
        body.put("ifsc", "UBIN0808059");
        body.put("mobile", "9" + getrandomNumbers(9));

        EnterprisePennyDrop obj = new EnterprisePennyDrop();
        Response respobj = middlewareServicesObject.pennyDropVerification(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        if (statusCode == 200) {
            HashMap<Object, Object> verificationDetails = respobj.jsonPath().get(".");
            LOGGER.info("Penny Drop Verification Details: " + verificationDetails);
        }
    }

    private String getrandomNumbers(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }

    @Test(priority = 8, groups = {"Regression"}, description = "Update Solution Instruments", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateSolutionInstruments() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Cookie", cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("ssoid", "**********");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("solutionTypeLevel2", flowType);
        queryParams.put("businessLeadId", leadId);
        queryParams.put("productType", "Payment_Gateway");

        Map<String, String> body = new HashMap<>();
        body.put("PRODUCT_TYPE", "Payment Gateway");
        body.put("CONVENIENCE_FEE_TYPE", "Default");
        body.put("AGREEMENT_TYPE", "Vetted");
        body.put("SETTLEMENT_STRATEGY", "Online Settlement");
        body.put("PLATFORM", "Panel");
        body.put("IS_INTEGRATED", "false");
        body.put("VALUE_ADDED_SERVICE_FEE", "");
        body.put("INVOICE_SAP_TYPE", "INDIVIDUAL");
        body.put("SETTLEMENT_TYPE", "Individual");
        body.put("INVOICE_COMMISSION_TYPE", "Default");
        body.put("FLOW_TYPE", "online");
        body.put("MERCHANT_GMV", "9000000");
        body.put("MERCHANT_INDUSTRY_TYPE", "Big");
        body.put("ECODE", "007");
        body.put("ACCOUNT_SOURCE_TEAM","ENT1-Portfolio");
        body.put("NCMC_MERCHANT", "No");
        body.put("PENNY_DROP_STATUS", "true");
        body.put("IMPS_SUPPORTED", "true");

        EnterpriseSolutionInstruments obj = new EnterpriseSolutionInstruments();
        Response respobj = middlewareServicesObject.updateSolutionInstruments(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        if (statusCode == 200) {
            HashMap<Object, Object> response = respobj.jsonPath().get(".");
            LOGGER.info("Solution Instruments Update Response: " + response);
        }
    }

    @Test(priority = 9, groups = {"Regression"}, description = "Create Solution Lead", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createSolutionLead() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Cookie", cookie);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", "enterprise_merchant_parent");
        queryParams.put("channel", channel);
        queryParams.put("partialSave", "true");
        queryParams.put("businessLeadId", leadId);

        Map<String, String> body = new HashMap<>();
        if (onboardingDocumentType.equals("PAN")) {
            body.put("pan", pan);
        } else if (onboardingDocumentType.equals("TAN")) {
            body.put("ONBOARDING_DOCUMENT_VALUE", onboardingDocumentValue);
            body.put("ONBOARDING_DOCUMENT_TYPE", onboardingDocumentType);
        }

        body.put("entityType", entityType);

        body.put("PRODUCT_TYPE", "Payment Gateway");
        body.put("CONVENIENCE_FEE_TYPE", "Default");
        body.put("AGREEMENT_TYPE", "Vetted");
        body.put("SETTLEMENT_STRATEGY", "Online Settlement");
        body.put("PLATFORM", "Panel");
        body.put("IS_INTEGRATED", "false");
        body.put("VALUE_ADDED_SERVICE_FEE", "");
        body.put("INVOICE_SAP_TYPE", "INDIVIDUAL");
        body.put("SETTLEMENT_TYPE", "Individual");
        body.put("INVOICE_COMMISSION_TYPE", "Default");
        body.put("FLOW_TYPE", "online");
        body.put("MERCHANT_GMV", "9000000");
        body.put("MERCHANT_INDUSTRY_TYPE", "Big");
        body.put("ECODE", "007");
        body.put("ACCOUNT_SOURCE_TEAM", "ENT1-Portfolio");
        body.put("DEAL_APPROVAL_ID", "007");
        body.put("DEAL_APPROVAL_COMMENT", "None");
        body.put("NCMC_MERCHANT", "No");
        body.put("PENNY_DROP_STATUS", "true");
        body.put("IMPS_SUPPORTED", "true");
        body.put("MDR_LINE_ITEMS", "{\"payModeDetails\":[{\"txnType\":\"Payments\",\"paymodes\":{\"DC\":[{\"bank\":\"DEFAULT\",\"bankDisplayName\":\"DEFAULT\",\"paymode\":\"DC\",\"feeType\":\"SIMPLE\",\"commissionType\":\"PERCENTAGE\",\"fee\":\"0.9\",\"cardType\":null,\"cardScheme\":null,\"convenienceModel\":null,\"subscriptionAction\":null,\"feeFactor\":null,\"startRange\":null,\"endRange\":null}],\"UPI\":[{\"bank\":\"DEFAULT\",\"bankDisplayName\":\"DEFAULT\",\"paymode\":\"UPI\",\"feeType\":\"SIMPLE\",\"commissionType\":\"PERCENTAGE\",\"fee\":\"0\",\"approvedFee\":\"0\",\"cardType\":null,\"cardScheme\":null,\"convenienceModel\":null,\"subscriptionAction\":null,\"feeFactor\":null,\"startRange\":null,\"endRange\":null}]}}],\"txnFlows\":[\"Payment Links\"]}");
        body.put("INTEGRATION_CHARGES", "{\"integrationCharges\":[{\"txnType\":\"Payments\",\"paymentService\":{\"AMC\":[{\"paymentService\":\"AMC\",\"feeType\":\"SIMPLE\",\"commissionType\":\"FLAT\",\"startRange\":null,\"endRange\":null,\"fee\":\"0\"}],\"Setup Fee\":[{\"paymentService\":\"Setup Fee\",\"feeType\":\"SIMPLE\",\"commissionType\":\"FLAT\",\"startRange\":null,\"endRange\":null,\"fee\":\"0\"}]}}]}");
        body.put("MODEL", model);
        body.put("SUB_MODEL", subModel);
        body.put("bankName", "UNION BANK OF INDIA");
        body.put("ifsc", "UBIN0808059");
        body.put("bankAccountNumber", bankAccountNumber);
        body.put("bankAccountHolderNameByPennyDrop", "ANMOL JAIN");
        body.put("pennyDropStatus", "true");

        EnterpriseCreateSolutionLead obj = new EnterpriseCreateSolutionLead();
        Response respobj = middlewareServicesObject.createSolutionLeadEnterprise(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        if (statusCode == 200) {
            solutionLeadId = respobj.jsonPath().get("leadId");
            HashMap<Object, Object> response = respobj.jsonPath().get(".");
            LOGGER.info("Create Solution Lead Response: " + response);
        }
    }

    @Test(priority = 10, groups = {"Regression"}, description = "Fetch Solution Lead Information", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchSolutionLeadInformation() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("leadId", solutionLeadId);

        FetchBusinessLead obj = new FetchBusinessLead();
        Response respobj = middlewareServicesObject.fetchBusinessLeadInformation(obj, headers, queryParams);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        if (statusCode == 200) {
            solutionadditioninformetadata = respobj.jsonPath().get("solution.solutionAdditionalInfo");
            retailrelatedbusinessuuid = respobj.jsonPath().get("retailRelatedBusiness.relatedBusinessUuid");
            registeredaddress = respobj.jsonPath().get("business.addresses[0]");
            kycverifygstsuccessresponse = respobj.jsonPath().get("solution.solutionAdditionalInfo.KYC_VERIFY_GST_SUCCESS_RESPONSE").toString();
            List<String> businessOwners = respobj.jsonPath().get("businessOwners");
            LOGGER.info(businessOwners);
            if (businessOwners != null) {
                for (int i = 0; i < businessOwners.size(); i++) {
                    HashMap<String, String> owners = respobj.jsonPath().get("businessOwners[" + i + "]");
                    if (owners.get("ownershipType").equals("AUTHORIZED_SIGNATORY")) {
                        owner_uuid = owners.get("uuid");
                        LOGGER.info("Owner UUID: " + owner_uuid);
                    }
                }
            }
            LOGGER.info("Solution Additional Info: " + solutionadditioninformetadata);
        }
    }

    @Test(priority = 11, groups = {"Regression"}, description = "Update Solution Lead", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateSolutionLead() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Cookie", cookie);
        headers.put("client_id", "oe-panel-staging");
        headers.put("secret_version", "v1");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("ssoid", "**********");
        headers.put("encryption_enabled", "true");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", "enterprise_merchant_parent");
        queryParams.put("channel", channel);
        queryParams.put("partialSave", "false");
        queryParams.put("leadId", solutionLeadId);

        Map<String, String> body = new HashMap<>();
        if (onboardingDocumentType.equals("PAN")) {
            body.put("pan", pan);
        } else {
            body.put("ONBOARDING_DOCUMENT_VALUE", onboardingDocumentValue);
            body.put("ONBOARDING_DOCUMENT_TYPE", onboardingDocumentType);
        }
        body.put("entityType", entityType);
        body.put("currentFormState", "RENTAL_PLAN_DETAILS");

        EnterpriseUpdateSolutionLead obj = new EnterpriseUpdateSolutionLead();
        Response respobj = middlewareServicesObject.updateSolutionLeadEnterprise(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        if (statusCode == 200) {
            HashMap<Object, Object> response = respobj.jsonPath().get(".");
            LOGGER.info("Update Solution Lead Response: " + response);
        }
    }

    @Test(priority = 12, groups = {"Regression"}, description = "Fetch Document Status", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchDocumentStatus() throws JSONException {
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Cookie", cookie);
        headers.put("client_id", "oe-panel-staging");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "**********");
        headers.put("encryption_enabled", "true");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("channel", channel);
        queryParams.put("solution", "enterprise_merchant_parent");
        queryParams.put("leadId", solutionLeadId);

        EnterpriseDocumentStatus obj = new EnterpriseDocumentStatus();
        Response respobj = middlewareServicesObject.fetchDocumentStatusEnterprise(obj, headers, queryParams);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        docDetails = new HashMap<>();

        for (int i = 0; i < respobj.jsonPath().getList("docDetailsSet").size(); i++) {
            if (respobj.jsonPath().get("docDetailsSet[" + i + "].docType").toString().equals("authSignatoryAddressProof")) {
                String additionalInfo = respobj.jsonPath().get("docDetailsSet[" + i + "].possibleDocuments[0].additionalInfo").toString();
                // Remove the escaped quotes and parse as JSON
                additionalInfo = additionalInfo.replace("\\", "");
                JSONObject additionalInfoJson = new JSONObject(additionalInfo);
                owner_uuid = additionalInfoJson.getString("ownerUuid");
                LOGGER.info("Owner UUID: " + owner_uuid);
            }
        }

        for (int i = 0; i < respobj.jsonPath().getList("docDetailsSet").size(); i++) {
            LOGGER.info(respobj.jsonPath().get("docDetailsSet[" + i + "].docType").toString());
            LOGGER.info(respobj.jsonPath().get("docDetailsSet[" + i + "].possibleDocs").toString());
            docDetails.put(respobj.jsonPath().get("docDetailsSet[" + i + "].docType").toString(), respobj.jsonPath().get("docDetailsSet[" + i + "].possibleDocs"));
            LOGGER.info(docDetails);
        }
    }

    @Test(priority = 13, groups = {"Regression"}, description = "Upload Documents", retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = {"fetchDocumentStatus"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void uploadDocuments() throws InterruptedException {
        Map<String, String> headers = new HashMap<>();

        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("cookie", cookie);
        headers.put("email", "<EMAIL>");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "**********");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("Content-Type", "multipart/form-data");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solutionType", "enterprise_merchant_parent");
        queryParams.put("leadId", solutionLeadId);
        for (Map.Entry<String, List<String>> entry : docDetails.entrySet()) {
            List<String> list1 = new ArrayList<>();
            String docType = entry.getKey();
            List<String> possibleDocs = entry.getValue();
            queryParams.put("docType", docType);
            list1.add(docType);
            queryParams.put("pageNo", "1");
            queryParams.put("docProvided", possibleDocs.get(0));
            queryParams.put("merchantCustId", "");
            if (docType.equals("authSignatoryAddressProof")) {
                queryParams.put("ownerUuid", owner_uuid);
            }
            LOGGER.info(queryParams);
            File file1 = new File("/Users/<USER>/Documents/oe-api-automation/src/test/java/OCL/SFtoOE2024/resources/sampledoc.png");
            Response respobj = middlewareServicesObject.uploadDocumentsEnterprise(headers, queryParams, file1);
            String uuid = respobj.jsonPath().get("uuid").toString();
            list1.add(uuid);
            Assert.assertEquals(respobj.getStatusCode(), 200);
            documents.add(list1);
        }
        Thread.sleep(10000);
        LOGGER.info(documents);
    }

    @Test(priority = 14, groups = {"Regression"}, description = "Fetch QC Details", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchingQCDeatails() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("cookie", cookie);
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "**********");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        FetchQCDetails obj = new FetchQCDetails(solutionLeadId);
        Response respobj = middlewareServicesObject.fetchQCDetailsEnterprise(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        documentList = respobj.jsonPath().get("leadDetails.documents");
        ownerDocument = respobj.jsonPath().get("leadDetails.ownerDocuments");
        timelinedetails = respobj.jsonPath().get("leadDetails.timelineDetail");
        for(int i=0;i<timelinedetails.size();i++){
            if (respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].stage").toString().equals("QC") && respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].subStage").toString().equals("QC_ACTION_PENDING")) {
                workflowStatusId = respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].workflowStatusId").toString();
                LOGGER.info("workflowStatusId: " + workflowStatusId);
            }
        }
        if (statusCode == 200) {
            LOGGER.info(documentList);
            LOGGER.info(ownerDocument);
        }
    }
//    @Test(priority = 15, groups = {"Regression"}, description = "Doing QC", retryAnalyzer = RetryAnalyzer.class , dependsOnMethods = "fetchingQCDeatails")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void doingQC() throws InterruptedException {
//        try {
//            Map<String, String> headers = new HashMap<>();
//            Map<String, String> queryParams = new HashMap<>();
//
//            // Set up headers
//            headers.put("accept", "application/json, text/plain, */*");
//            headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
//            headers.put("client_id", "oe-panel-staging");
//            headers.put("content-type", "application/json;charset=UTF-8");
//            headers.put("cookie", cookie);
//            headers.put("encryption_enabled", "true");
//            headers.put("origin", "https://oe-staging20.paytm.com");
//            headers.put("priority", "u=1, i");
//            headers.put("referer", "https://oe-staging20.paytm.com/");
//            headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"");
//            headers.put("sec-ch-ua-mobile", "?0");
//            headers.put("sec-ch-ua-platform", "\"macOS\"");
//            headers.put("sec-fetch-dest", "empty");
//            headers.put("sec-fetch-mode", "cors");
//            headers.put("sec-fetch-site", "same-site");
//            headers.put("secret_version", "v1");
//            headers.put("ssoid", "**********");
//            headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
//
//            // Set up query params
//            queryParams.put("action", "SUBMIT");
//
//            // Convert documents to JSON format
//            JSONArray ownerDocsJson = new JSONArray();
//            for (HashMap<String, Object> doc : ownerDocument) {
//                doc.put("status", "APPROVED");
//                doc.put("containsNonRedactedAadhaar", "No");
//                doc.put("noExpiryDate" , true);
//                JSONObject docJson = new JSONObject(doc);
//                ownerDocsJson.put(docJson);
//            }
//
//
//            JSONArray docsJson = new JSONArray();
//            for (HashMap<String, Object> doc : documentList) {
//                doc.put("status", "APPROVED");
//                doc.put("containsNonRedactedAadhaar", "No");
//                doc.put("noExpiryDate" , true);
//                JSONObject docJson = new JSONObject(doc);
//                docsJson.put(docJson);
//            }
//
//
//            // Create properties map
//            Map<String, Object> properties = new HashMap<>();
//            properties.put("ownerDocuments", ownerDocsJson.toString());
//            properties.put("documentList", docsJson.toString());
//            properties.put("reEnterIfscCode", ifscCode);
//            properties.put("reEnterAccountNumber", bankAccountNumber);
//            properties.put("workflowStatusId" , workflowStatusId);
//
//            LOGGER.info("Request Payload: " + new JSONObject(properties).toString(4));
//
//            // Perform API call
//            EnterpriseQC obj = new EnterpriseQC(solutionLeadId);
//            Response respobj = middlewareServicesObject.doingQCEnterprise(obj, headers, queryParams, properties);
//
//            // Validate response
//            int statusCode = respobj.getStatusCode();
//            Assert.assertEquals(statusCode, 200, "Expected status code 200 but got " + statusCode);
//
//            String responseBody = respobj.getBody().asString();
//            LOGGER.info("Response Body: " + responseBody);
//            Assert.assertTrue(responseBody.contains("success"), "Response does not indicate success!");
//
//            LOGGER.info("QC Done Successfully");
//
//        } catch (Exception e) {
//            LOGGER.error("Error in doingQC test: ", e);
//            Assert.fail("Test failed due to exception: " + e.getMessage());
//        }
//        Thread.sleep(20000);
//    }
} 