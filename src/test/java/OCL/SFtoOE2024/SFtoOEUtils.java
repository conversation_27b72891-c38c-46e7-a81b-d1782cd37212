package OCL.SFtoOE2024;
import java.util.Random;

import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.http.Header;
import io.restassured.http.Headers;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class SFtoOEUtils {

    @Owner(emailId = "<EMAIL>")
    public static  String authorizeToken (String username,String password) throws Exception {
        String baseUrl = "https://accounts-staging.paytm.in/oauth2/authorize";
        String clientId = "GG-OE-staging";

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Basic R0ctT0Utc3RhZ2luZzpkOG9iOTU0eDBzRWVBM0pZYTVoUXRqTldac2FUTm9SMg==");
        headers.put("Content-Type", "application/x-www-form-urlencoded");
        headers.put("Host", "accounts-staging.paytm.in");

        // Set up form parameters
        Map<String, String> formParams = new HashMap<>();
        formParams.put("client_id", clientId);
        formParams.put("do_not_redirect", "true");
        formParams.put("scope", "paytm");
        formParams.put("response_type", "code");
        formParams.put("username", username);
        formParams.put("password", password);

        // Send POST request
        RequestSpecification request = RestAssured.given().headers(headers).formParams(formParams);
        Response response = request.post(baseUrl);
        String code = response.jsonPath().getString("code");
        System.out.println("Authorization code: " + code);
        return code;

    }

    public static  String agentSessionToken (String username,String password) throws Exception {
        String baseUrl = "https://accounts-staging.paytm.in/oauth2/token";
        String clientId = "GG-OE-staging";

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Basic R0ctT0Utc3RhZ2luZzpkOG9iOTU0eDBzRWVBM0pZYTVoUXRqTldac2FUTm9SMg==");
        headers.put("Content-Type", "application/x-www-form-urlencoded");
        headers.put("Host", "accounts-staging.paytm.in");

        // Set up form parameters
        Map<String, String> formParams = new HashMap<>();
        formParams.put("client_id", clientId);
        formParams.put("grant_type", "authorization_code");
        formParams.put("scope", "paytm");
        String code=authorizeToken(username,password);
        formParams.put("code", code);


        // Send POST request
        RequestSpecification request = RestAssured.given().headers(headers).formParams(formParams);
        Response response = request.post(baseUrl);
        String access_token = response.jsonPath().getString("access_token");
        return access_token;

    }

    public static  Map<String, String> v1Token (String oAuthToken , String host) throws Exception {
        //String baseUrl = "https://goldengate-staging6.paytm.com/MerchantService/v1/token";
        String baseUrl = "https://" + host + "/MerchantService/v1/token";
        String clientId = "GG-OE-staging";

        Map<String, String> headers = new HashMap<>();
//        headers.put("Cookie", "X-MW-TOKEN=5a24ebb8-4a10-4bd7-b5fc-18cfe7595efa; X-MW-TOKEN-EX=prMSN2mSz8JbxonUAzbJiIxc0O36M8UBdmhWTqvxAugfP979AOpwbAl/+5u6a1txYM6oDTf+mJ6lYx5Go4sELw==; X-MW-TOKEN-EX-V2=71fFZhfAo0R5KsW9SSFhcNLojNzkvki8usjLus7CaMx8wFFr41rHYRBGWnWgi+/xAcTAUg/j6rdrELQ96vrZyUyMFeti; X-MW-TOKEN-EXTERNAL=IStY9o87UNa4mVG8mDPUTNe2ek7KAsPHCHYcRto6USaurJELeMfvNTX/9MhnqzZ8XhE0kghlNxVCaaDmg8T1yw==; X-MW-TOKEN-EXTERNAL-V2=71fFZhfAo0R5KsW9SSFhcNLojNzkvki8usjLus7CaMx8wFFr41rHYRBGWnWgi+/xAcTAUg/j6rdrELQ96vrZyUyMFeti; X-MW-TOKEN=8e2de7f1-4d32-42fa-b954-778a21c18c48; X-MW-TOKEN-EX=prMSN2mSz8JbxonUAzbJiIxc0O36M8UBdmhWTqvxAuhntSHisfgVsCCwMVulendr9eH+eQcxeIFUREoNQyZgsg==; X-MW-TOKEN-EX-V2=71fFZhfAo0R5KsW9SSFhcNLojNzkvki8usjLus7CaMx8wFFr41rHYRBGWX2shOH0CcPPUg6giPywsKByDxXrM3zhph2z; X-MW-TOKEN-EXTERNAL=IStY9o87UNa4mVG8mDPUTNe2ek7KAsPHCHYcRto6USb/gvW3pGbPx4GAWo0NsK01RQeZyT8D+uFAwzAaOcDdww==; X-MW-TOKEN-EXTERNAL-V2=71fFZhfAo0R5KsW9SSFhcNLojNzkvki8usjLus7CaMx8wFFr41rHYRBGWX2shOH0CcPPUg6giPywsKByDxXrM3zhph2z");
        headers.put("Content-Type", "application/json");
        headers.put("Host", host);

        // Set up form parameters
        Map<String, String> reqBody = new HashMap<>();
        reqBody.put("oAuthToken", oAuthToken);
        reqBody.put("ipAddress", "::ffff:127.0.0.1");


        // Send POST request
        RequestSpecification request = RestAssured.given().headers(headers).body(reqBody);
        Response response = request.post(baseUrl);
        Headers responseHeaders = response.getHeaders();
//        for (Header header : responseHeaders) {
//            System.out.println(header.getName() + ": " + header.getValue());
//        }
        Map<String, String> cookies = new HashMap<>();
        for (Header header : responseHeaders) {
            if ("Set-Cookie".equalsIgnoreCase(header.getName())) {
                String[] cookieParts = header.getValue().split(";", 2);
                if (cookieParts.length > 0) {
                    String[] keyValue = cookieParts[0].split("=", 2);
                    if (keyValue.length == 2) {
                        cookies.put(keyValue[0], keyValue[1]);
                    }
                }
            }
        }

        return cookies;
    }



    public static String OEpanelCookie(String userName,String password , String host) throws Exception {
        String token=agentSessionToken(userName,password);
        System.out.println(token);
        Map<String, String> cookies = v1Token(token , host);
        cookies.forEach((key, value) -> System.out.println(key + ": " + value));
        cookies.get("X-MW-TOKEN");
        cookies.get("X-MW-TOKEN-EXTERNAL");
        cookies.get("X-MW-TOKEN-EX");
        System.out.println("X-MW-TOKEN= " + cookies.get("X-MW-TOKEN")+"; X-MW-TOKEN-EXTERNAL= " + cookies.get("X-MW-TOKEN-EXTERNAL")+"; X-MW-TOKEN-EX= " + cookies.get("X-MW-TOKEN-EX"));
        return "X-MW-TOKEN= " + cookies.get("X-MW-TOKEN")+"; X-MW-TOKEN-EXTERNAL= " + cookies.get("X-MW-TOKEN-EXTERNAL")+"; X-MW-TOKEN-EX= " + cookies.get("X-MW-TOKEN-EX");
    }
    public static String getrandomString(int length) {
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        StringBuilder result = new StringBuilder();
        Random random = new Random();

        for (int i = 0; i < length; i++) {
            int index = random.nextInt(characters.length()); // Get a random index
            result.append(characters.charAt(index)); // Append the random character
        }

        return result.toString();
    }
    public static String getrandomNumbers(int length){
        String characters = "1234567890";
        StringBuilder result = new StringBuilder();
        Random random = new Random();

        for (int i = 0; i < length; i++) {
            int index = random.nextInt(characters.length()); // Get a random index
            result.append(characters.charAt(index)); // Append the random digit
        }

        return result.toString();
    }

    public static String randomPanNumber(String entityType) {
        entityType = entityType.toLowerCase();
        switch (entityType) {
            case "proprietorship":
                return getrandomString(3) + 'P' + getrandomString(1) + getrandomNumbers(4) + getrandomString(1);
            case "company":
                return getrandomString(3) + 'C' + getrandomString(1) + getrandomNumbers(4) + getrandomString(1);
            case "partnership":
                return getrandomString(3) + 'F' + getrandomString(1) + getrandomNumbers(4) + getrandomString(1);
            case "trust":
                return getrandomString(3) + 'T' + getrandomString(1) + getrandomNumbers(4) + getrandomString(1);
            case "llp":
                return getrandomString(3) + 'L' + getrandomString(1) + getrandomNumbers(4) + getrandomString(1);
            case "government":
                return getrandomString(3) + 'G' + getrandomString(1) + getrandomNumbers(4) + getrandomString(1);
            case "huf":
                return getrandomString(3) + 'H' + getrandomString(1) + getrandomNumbers(4) + getrandomString(1);
            case "aop":
                return getrandomString(3) + 'A' + getrandomString(1) + getrandomNumbers(4) + getrandomString(1);
            case "boi":
                return getrandomString(3) + 'B' + getrandomString(1) + getrandomNumbers(4) + getrandomString(1);
            case "artificial juridical person":
                return getrandomString(3) + 'J' + getrandomString(1) + getrandomNumbers(4) + getrandomString(1);
            case "local authority":
                return getrandomString(3) + 'L' + getrandomString(1) + getrandomNumbers(4) + getrandomString(1);
            default:
                return "Please give a valid entity from the list: proprietorship , company, partnership, trust, aop, llp, local authority, government, huf, aop, boi, artificial juridical person, local authority";
        }
    }

    public static String randomTanNumber() {
        return getrandomString(4) + getrandomNumbers(5) + getrandomString(1);
    }
    public static String randomGSTNumber(String pan) {
        return "11"+pan+"S"+getrandomNumbers(2);
    }

    public static String getHost(){
        return P.API.get("goldengate_api_url").replaceFirst("^https://", "");
    }

    @Test
    public void test(){
        String pan = randomPanNumber("PROPRIETORSHIP");
        String gstin = randomGSTNumber(pan);
        System.out.println("PROPRIETORSHIP".toString().toLowerCase());
        System.out.println(getHost());
        System.out.println(pan);
        System.out.println(gstin);
        System.out.println(getrandomNumbers(5));
        System.out.println(randomTanNumber());
    }

}

