package OCL.SFtoOE2024;

import OCL.Individual.SoundBox.FlowSoundBox;
import Request.MerchantService.v1.Enterprise.*;
import Request.MerchantService.v2.Banks;
import Services.MechantService.MiddlewareServices;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.io.File;
import org.json.JSONObject;
import org.json.JSONArray;

import static OCL.SFtoOE2024.SFtoOEUtils.*;

public class SolutionLeadCreationB2B extends BaseMethod {

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final org.apache.logging.log4j.Logger LOGGER = org.apache.logging.log4j.LogManager.getLogger(FlowSoundBox.class);

    public String AgentNumber = "**********";
    public String LoginPassword = "paytm@123";
    public String model = "B2B";
    public String subModel = "Aggregator";
    public String nameAsPerNSDL = "TOUCH WOOD LIMITED";
    public String dateOfIncorporation = "2024-11-21";
    public String gstExempted = "No";
    public String legalName = "RONIT ARYA";
    public String tradeName = "ARYA ENTERPRISES";
    public String segment = "Government";
    public String subSegment = "Central Department";
    public String isInvolvedInInternationalTransaction = "No";
    public String isInternationalMerchant = "false";
    public String onboardingEntity = "OCL";
    public String email = "<EMAIL>";
    public String flowType = "offline";
    public String entityType = "PROPRIETORSHIP";
    public String pan = "**********";
    public String gstin = "11**********S24";
    public final String solutionType = "enterprise_merchant_business";
    public String leadId = "77583289-783a-4334-b70e-b87eaed3c676";
    public String host = getHost();
    public String cookie = "X-MW-TOKEN-EX=w%2FfFWfxKyuEpd0bT9LhQXtGbCMTNLm1AbUbdakrY8aCDqyWbfxFetaL%2B3VlyBVbqbXvAXVJbCTAMi9D7XVYKRQ%3D%3D; X-MW-TOKEN-EXTERNAL=7dmL9cNnRWcLEwGK%2BTlsKEYDjE%2BnRHNX7YryTkdOxLWkDDx%2BGpKJDgaVWp0r5Jb0aFbVa7GuhXsJZy2qIf5Dhw%3D%3D; X-MW-TOKEN-EX-V2=71fFZhbX81IkI8%2F5HT8kNNPrjoq9sUGhvMmVvN7CeZIigFRr41zHYRBGX3qviev0DcHDUQ9ynBcpMcgEo4S37EXWEZbB; X-MW-TOKEN-EXTERNAL-V2=71fFZhbX81IkI8%2F5HT8kNNPrjoq9sUGhvMmVvN7CeZIigFRr41zHYRBGX3qviev0DcHDUQ9ynBcpMcgEo4S37EXWEZbB; X-MW-TOKEN=cec5e40c-46bc-46bb-8fec-bfe1497b64e6";
    public String isValidated = "";
    public String firstName = "";
    public String middleName = "";
    public String lastName = "";
    public String channel = "OE_PANEL";
    public String leadType = "BUSINESS_LEAD";
    public String category = "Food";
    public String CategoryCode = "";
    public String pincode = "226020";
    public HashMap<String, String> solutionadditioninformetadata;
    public HashMap<String, String> registeredaddress;
    public String retailrelatedbusinessuuid = "";
    public String kycverifygstsuccessresponse = "";
    public String ifscCode = "HDFC0000001";
    public String bankAccountNumber = "*************";
    public String solutionLeadId = "";
    public String owner_uuid = "";
    public String onboardingDocumentType = "PAN";
    public String onboardingDocumentValue = "**********";
    public String nameAsPerTan = "TOUCH WOOD LIMITED";
    public String workflowStatusId = "";
    public String commercialworkflowStatusId = "";
    public String bankLobworkflowStatusId = "";
    public String aggrementworkflow = "";
    HashMap<String, Object> aggrementdoc = new HashMap<>();
    HashMap<String, List<String>> docDetails = new HashMap<>();
    List<HashMap<String, Object>> documentList = new ArrayList<>();
    List<HashMap<String, Object>> ownerDocument = new ArrayList<>();
    List<HashMap<String, Object>>paymodes = new ArrayList<>();
    HashMap<String, Object> bankLobBenchMarkSRO = new HashMap<>();
    List<List<String>> documents = new ArrayList<>();
    List<HashMap<String, Object>>timelinedetails=new ArrayList<>();

    public SolutionLeadCreationB2B() throws Exception {
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch basic details of enterprise lead", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchBasicInfo() {
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, String> queryParams = new HashMap<>();
        headers.put("Cookie", cookie);


        queryParams.put("leadType", "BUSINESS_LEAD,SOLUTION_LEAD");
        queryParams.put("flowType", flowType);
        queryParams.put("entityType", entityType);
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("onboardingDocumentValue", pan);
        queryParams.put("onboardingDocumentType", onboardingDocumentType);
        queryParams.put("pan", pan);
        EnterpriseFetchBasicInfo obj = new EnterpriseFetchBasicInfo();
        Response respobj = middlewareServicesObject.enterpriseFetchBasicInfo(obj, headers, queryParams);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        if (statusCode == 200) {
            try {
                HashMap<Object, Object> businessLeads = respobj.jsonPath().get("businessLeads[0]");
                HashMap<Object, Object> solutionLeads = respobj.jsonPath().get("solutionLeads[0]");
                LOGGER.info("Business Lead: " + businessLeads);
                LOGGER.info("Business Lead: " + solutionLeads);
            } catch (Exception e) {
                LOGGER.info("Exception: " + e);
            }
        }
    }

    @Test(priority = 2, groups = {"Regression"}, description = "Get All Resources", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getAllResources() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("typeList", "flowType,onboardingEntity_online,onboardingEntity_offline,isPPBLContext," +
                "onboardingEntity_OnUs,onboardingEntity_corporate,searchType,onboardingDocumentType," +
                "model_offline,subModel_B2B,subModel_B2C");

        EnterprisefetchAllResources obj = new EnterprisefetchAllResources();
        Response respobj = middlewareServicesObject.fetchAllResourcesEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        if (StatusCode == 200) {
            HashMap<Object, Object> dataMap = respobj.jsonPath().get("dataMap");
            LOGGER.info("Resources Data Map: " + dataMap);
        }
    }

    @Test(priority = 3, groups = {"Regression"}, description = "Create solution lead", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchBusinessLeadData() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", "enterprise_merchant_parent");
        queryParams.put("channel", channel);
        queryParams.put("businessLeadId", leadId); // Using the leadId from previous test
        queryParams.put("leadType", "SOLUTION_LEAD");
        FetchBusinessLead obj = new FetchBusinessLead();
        Response respobj = middlewareServicesObject.fetchBusinessLeadInformation(obj, headers, queryParams);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);


    }

    @Test(priority = 4, groups = {"Regression"}, description = "Get Solution Type Resources", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getSolutionTypeResources() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("typeList", "solutionType_offline,productType_offline,agreementType_offline," +
                "settlementStrategy_offline_B2C,platform_offline,accountSourceTeam_offline,ncmcMerchant");

        EnterprisefetchAllResources obj = new EnterprisefetchAllResources();
        Response respobj = middlewareServicesObject.fetchAllResourcesEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        if (StatusCode == 200) {
            HashMap<Object, Object> dataMap = respobj.jsonPath().get("dataMap");
            LOGGER.info("Solution Type Resources Data Map: " + dataMap);
        }
    }

    @Test(priority = 5, groups = {"Regression"}, description = "Fetch Merchant Banks", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchMerchantBanks() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);
        headers.put("client_id", "oe-panel-staging");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "**********");
        headers.put("encryption_enabled", "true");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("leadId", leadId);
        queryParams.put("upiFetchOnly", "false");

        EnterpriseFetchMerchantBanks obj = new EnterpriseFetchMerchantBanks();
        Response respobj = middlewareServicesObject.fetchMerchantBanksEnterprise(obj, headers, queryParams);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        if (statusCode == 200) {
            // Extract and log bank details from response
            HashMap<Object, Object> bankDetails = respobj.jsonPath().get(".");
            LOGGER.info("Merchant Bank Details: " + bankDetails);
        }
    }

    @Test(priority = 6, groups = {"Regression"}, description = "Get Bank Details", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getBankDetails() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);


        FetchBankDetailsEnterprise obj = new FetchBankDetailsEnterprise(ifscCode);
        Response respobj = middlewareServicesObject.fetchBankDetailsEnterprise(obj, headers);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        if (statusCode == 200) {
            HashMap<Object, Object> bankDetails = respobj.jsonPath().get(".");
            LOGGER.info("Bank Details: " + bankDetails);
        }
    }

    @Test(priority = 7, groups = {"Regression"}, description = "Penny Drop Verification", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void pennyDropVerification() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);
        headers.put("Host", host);


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "enterprise_merchant_parent");
        queryParams.put("bankName", "HDFC BANK");

        Map<String, String> body = new HashMap<>();
        body.put("bankAccountNumber", bankAccountNumber);
        body.put("ifsc", "HDFC0000001");
        body.put("mobile", "9" + getrandomNumbers(9));

        EnterprisePennyDrop obj = new EnterprisePennyDrop();
        Response respobj = middlewareServicesObject.pennyDropVerification(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        if (statusCode == 200) {
            // Extract and log penny drop verification details
            HashMap<Object, Object> verificationDetails = respobj.jsonPath().get(".");
            LOGGER.info("Penny Drop Verification Details: " + verificationDetails);
        }
    }

    @Test(priority = 8, groups = {"Regression"}, description = "Fetch Solution Instruments", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateSolutionInstruments() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Cookie", cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("ssoid", "**********");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", "enterprise_merchant_business");
        queryParams.put("channel", channel);
        queryParams.put("solutionTypeLevel2", flowType);
        queryParams.put("businessLeadId", leadId);
        queryParams.put("productType", "Offline_Gateway");


        Map<String, String> body = new HashMap<>();
        body.put("PRODUCT_TYPE", "Offline Gateway");
        body.put("CONVENIENCE_FEE_TYPE", "Default");
        body.put("AGREEMENT_TYPE", "Vetted");
        body.put("SETTLEMENT_STRATEGY", "Online Settlement");
        body.put("PLATFORM", "API");
        body.put("IS_INTEGRATED", "true");
        body.put("VALUE_ADDED_SERVICE_FEE", "Withdrawal");
        body.put("INVOICE_SAP_TYPE", "INDIVIDUAL");
        body.put("SETTLEMENT_TYPE", "Aggregator");
        body.put("INVOICE_COMMISSION_TYPE", "Monthly");
        body.put("FLOW_TYPE", "offline");
        body.put("ECODE", "9934934");
        body.put("ACCOUNT_SOURCE_TEAM", "Government");
        body.put("DEAL_APPROVAL_ID", "91235");
        body.put("DEAL_APPROVAL_COMMENT", "None");
        body.put("NCMC_MERCHANT", "No");
        body.put("PENNY_DROP_STATUS", "true");
        body.put("IMPS_SUPPORTED", "true");


        EnterpriseSolutionInstruments obj = new EnterpriseSolutionInstruments();
        Response respobj = middlewareServicesObject.updateSolutionInstruments(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        if (statusCode == 200) {
            HashMap<Object, Object> response = respobj.jsonPath().get(".");
            LOGGER.info("Solution Instruments Update Response: " + response);
        }
    }


    @Test(priority = 9, groups = {"Regression"}, description = "Create Solution Lead", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createSolutionLead() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Cookie", cookie);


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", "enterprise_merchant_parent");
        queryParams.put("channel", channel);
        queryParams.put("partialSave", "true");
        queryParams.put("businessLeadId", leadId);

        Map<String, String> body = new HashMap<>();
        if (onboardingDocumentType.equals("PAN")) {
            body.put("pan", pan);
        } else if (onboardingDocumentType.equals("TAN")) {
            body.put("ONBOARDING_DOCUMENT_VALUE", onboardingDocumentValue);
            body.put("ONBOARDING_DOCUMENT_TYPE", onboardingDocumentType);
        }

        body.put("entityType", entityType);

        body.put("PRODUCT_TYPE", "Offline Gateway");
        body.put("CONVENIENCE_FEE_TYPE", "Default");
        body.put("AGREEMENT_TYPE", "Vetted");
        body.put("SETTLEMENT_STRATEGY", "Online Settlement");
        body.put("PLATFORM", "API");
        body.put("IS_INTEGRATED", "true");
        body.put("VALUE_ADDED_SERVICE_FEE", "Withdrawal");
        body.put("INVOICE_SAP_TYPE", "INDIVIDUAL");
        body.put("SETTLEMENT_TYPE", "Aggregator");
        body.put("INVOICE_COMMISSION_TYPE", "Monthly");
        body.put("FLOW_TYPE", "offline");
        body.put("ECODE", "9934934");
        body.put("ACCOUNT_SOURCE_TEAM", "Government");
        body.put("DEAL_APPROVAL_ID", "91235");
        body.put("DEAL_APPROVAL_COMMENT", "None");
        body.put("NCMC_MERCHANT", "No");
        body.put("PENNY_DROP_STATUS", "true");
        body.put("IMPS_SUPPORTED", "true");
        body.put("MDR_LINE_ITEMS", "{\"payModeDetails\":[{\"txnType\":\"Payments\",\"paymodes\":{\"UPI\":[{\"bank\":\"DEFAULT\",\"bankDisplayName\":\"DEFAULT\",\"paymode\":\"UPI\",\"feeType\":\"SIMPLE\",\"commissionType\":\"PERCENTAGE\",\"fee\":\"0\",\"approvedFee\":\"0\",\"cardType\":null,\"cardScheme\":null,\"convenienceModel\":null,\"subscriptionAction\":null,\"feeFactor\":null,\"startRange\":null,\"endRange\":null}]}},{\"txnType\":\"WITHDRAWAL\",\"paymodes\":{\"MANUAL_WITHDRAWAL\":[{\"bank\":\"DEFAULT\",\"bankDisplayName\":\"DEFAULT\",\"paymode\":\"MANUAL_WITHDRAWAL\",\"convenienceModel\":\"PCF\",\"feeType\":\"SIMPLE\",\"commissionType\":\"FLAT\",\"fee\":\"1\",\"cardType\":null,\"cardScheme\":null,\"subscriptionAction\":null,\"feeFactor\":null,\"startRange\":null,\"endRange\":null}]}}],\"txnFlows\":[\"Barcode\"]}");
        body.put("INTEGRATION_CHARGES", "{\"integrationCharges\":[{\"txnType\":\"Payments\",\"paymentService\":{\"AMC\":[{\"paymentService\":\"AMC\",\"feeType\":\"SIMPLE\",\"commissionType\":\"FLAT\",\"startRange\":null,\"endRange\":null,\"fee\":\"1\"}],\"Setup Fee\":[{\"paymentService\":\"Setup Fee\",\"feeType\":\"SIMPLE\",\"commissionType\":\"FLAT\",\"startRange\":null,\"endRange\":null,\"fee\":\"1\"}]}}]}");
        body.put("MODEL", model);
        body.put("SUB_MODEL", subModel);
        body.put("bankName", "HDFC BANK");
        body.put("ifsc", "HDFC0000001");
        body.put("bankAccountNumber", bankAccountNumber);
        body.put("bankAccountHolderNameByPennyDrop", "ANMOL JAIN");
        body.put("pennyDropStatus", "true");


        EnterpriseCreateSolutionLead obj = new EnterpriseCreateSolutionLead();
        Response respobj = middlewareServicesObject.createSolutionLeadEnterprise(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        if (statusCode == 200) {
            solutionLeadId = respobj.jsonPath().get("leadId");
            HashMap<Object, Object> response = respobj.jsonPath().get(".");
            LOGGER.info("Create Solution Lead Response: " + response);
        }
    }


    @Test(priority = 10, groups = {"Regression"}, description = "Fetch Business Lead Information", retryAnalyzer = RetryAnalyzer.class)

    public void fetchSolutionLeadInformation() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("leadId", solutionLeadId);

        FetchBusinessLead obj = new FetchBusinessLead();
        Response respobj = middlewareServicesObject.fetchBusinessLeadInformation(obj, headers, queryParams);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        if (StatusCode == 200) {
            solutionadditioninformetadata = respobj.jsonPath().get("solution.solutionAdditionalInfo");
            retailrelatedbusinessuuid = respobj.jsonPath().get("retailRelatedBusiness.relatedBusinessUuid");
            registeredaddress = respobj.jsonPath().get("business.addresses[0]");
            kycverifygstsuccessresponse = respobj.jsonPath().get("solution.solutionAdditionalInfo.KYC_VERIFY_GST_SUCCESS_RESPONSE").toString();
            List<String> businessOwners = respobj.jsonPath().get("businessOwners");
            LOGGER.info(businessOwners);
            if (businessOwners != null) {
                for (int i = 0; i < businessOwners.size(); i++) {
                    HashMap<String, String> owners = respobj.jsonPath().get("businessOwners[" + i + "]");
                    if (owners.get("ownershipType").equals("AUTHORIZED_SIGNATORY")) {
                        owner_uuid = owners.get("uuid");
                        LOGGER.info("Owner UUID: " + owner_uuid);

                    }
                }
            }

            LOGGER.info("Data Map is : " + solutionadditioninformetadata);
        }
    }

    @Test(priority = 11, groups = {"Regression"}, description = "Update Solution Lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateSolutionLead() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Cookie", cookie);
        headers.put("client_id", "oe-panel-staging");
        headers.put("secret_version", "v1");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("ssoid", "**********");
        headers.put("encryption_enabled", "true");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", "enterprise_merchant_parent");
        queryParams.put("channel", channel);
        queryParams.put("partialSave", "false");
        queryParams.put("leadId", solutionLeadId);

        Map<String, String> body = new HashMap<>();
        if (onboardingDocumentType.equals("PAN")) {
            body.put("pan", pan);
        } else {
            body.put("ONBOARDING_DOCUMENT_VALUE", onboardingDocumentValue);
            body.put("ONBOARDING_DOCUMENT_TYPE", onboardingDocumentType);
        }
        body.put("entityType", entityType);
        body.put("currentFormState", "RENTAL_PLAN_DETAILS");


        EnterpriseUpdateSolutionLead obj = new EnterpriseUpdateSolutionLead();
        Response respobj = middlewareServicesObject.updateSolutionLeadEnterprise(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        if (statusCode == 200) {
            HashMap<Object, Object> response = respobj.jsonPath().get(".");
            LOGGER.info("Update Solution Lead Response: " + response);
        }
    }

    @Test(priority = 12, groups = {"Regression"}, description = "Fetch Document Status", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchDocumentStatus() throws JSONException {
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Cookie", cookie);
        headers.put("client_id", "oe-panel-staging");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "**********");
        headers.put("encryption_enabled", "true");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("channel", channel);
        queryParams.put("solution", "enterprise_merchant_parent");
        queryParams.put("leadId", solutionLeadId);

        EnterpriseDocumentStatus obj = new EnterpriseDocumentStatus();
        Response respobj = middlewareServicesObject.fetchDocumentStatusEnterprise(obj, headers, queryParams);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        docDetails = new HashMap<>();

        for (int i = 0; i < respobj.jsonPath().getList("docDetailsSet").size(); i++) {
            if (respobj.jsonPath().get("docDetailsSet[" + i + "].docType").toString().equals("authSignatoryAddressProof")) {
                String additionalInfo = respobj.jsonPath().get("docDetailsSet[" + i + "].possibleDocuments[0].additionalInfo").toString();
                // Remove the escaped quotes and parse as JSON
                additionalInfo = additionalInfo.replace("\\", "");
                JSONObject additionalInfoJson = new JSONObject(additionalInfo);
                owner_uuid = additionalInfoJson.getString("ownerUuid");
                LOGGER.info("Owner UUID: " + owner_uuid);

            }
        }

        for (int i = 0; i < respobj.jsonPath().getList("docDetailsSet").size(); i++) {
            LOGGER.info(respobj.jsonPath().get("docDetailsSet[" + i + "].docType").toString());
            LOGGER.info(respobj.jsonPath().get("docDetailsSet[" + i + "].possibleDocs").toString());
            docDetails.put(respobj.jsonPath().get("docDetailsSet[" + i + "].docType").toString(), respobj.jsonPath().get("docDetailsSet[" + i + "].possibleDocs"));
            LOGGER.info(docDetails);
        }

    }


    @Test(priority = 13, groups = {"Regression"}, description = "Upload Documents", retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = {"fetchDocumentStatus"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void uploadDocuments() throws InterruptedException {
        Map<String, String> headers = new HashMap<>();

        //headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("cookie", cookie);//"X-MW-TOKEN= bab6761a-53ed-4097-9785-0a12e4fddd40; X-MW-TOKEN-EXTERNAL= OgfyMr4Ctiq6yFXBmMC+ODemrJv37Bf4906E02DQAUTUbn0i4ZOzOU3HBq7jOCyMziCYre0IYhFQEq+WnqZDrw==; X-MW-TOKEN-EX= JnU1hAfKHn6EKIoiI71rm6a74sLRVdch0DX9tzUvQkqyPPGW7Lud5H4ZP8AQqe/1F/9R7oqniDz4TRhmB07b8g==; JSESSIONID=6F1F63851BAC3C11920CA0EC6F1ABA66");
        headers.put("email", "<EMAIL>");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "**********");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("Content-Type", "multipart/form-data");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solutionType", "enterprise_merchant_parent");
        queryParams.put("leadId", solutionLeadId);
        for (Map.Entry<String, List<String>> entry : docDetails.entrySet()) {
            List<String> list1 = new ArrayList<>();
            String docType = entry.getKey();
            List<String> possibleDocs = entry.getValue();
            queryParams.put("docType", docType);
            list1.add(docType);
            queryParams.put("pageNo", "1");
            queryParams.put("docProvided", possibleDocs.get(0));
            queryParams.put("merchantCustId", "");
            if (docType.equals("authSignatoryAddressProof")) {
                queryParams.put("ownerUuid", owner_uuid);
            }
            LOGGER.info(queryParams);
            //File file1 = new File("/Users/<USER>/OE_QA_Api_Automation/oe-api-automation/src/test/java/OCL/SFtoOE2024/resources/sampledoc.png");
            File file1 = new File("/Users/<USER>/Documents/paytm_oe_api_automation/oe-api-automation/src/test/java/OCL/SFtoOE2024/resources/sampledoc.png");
            Response respobj = middlewareServicesObject.uploadDocumentsEnterprise(headers, queryParams, file1);
            String uuid = respobj.jsonPath().get("uuid").toString();
            list1.add(uuid);
            Assert.assertEquals(respobj.getStatusCode(), 200);
            documents.add(list1);
        }
        Thread.sleep(10000);
        LOGGER.info(documents);
    }

    @Test(priority = 14, groups = {"Regression"}, description = "Fetch QC Details", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchingQCDeatails() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("cookie", cookie);
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "**********");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        FetchQCDetails obj = new FetchQCDetails(solutionLeadId);
        Response respobj = middlewareServicesObject.fetchQCDetailsEnterprise(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        documentList = respobj.jsonPath().get("leadDetails.documents");
        ownerDocument = respobj.jsonPath().get("leadDetails.ownerDocuments");
        timelinedetails = respobj.jsonPath().get("leadDetails.timelineDetail");
        for(int i=0;i<timelinedetails.size();i++){
            if (respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].stage").toString().equals("QC") && respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].subStage").toString().equals("QC_ACTION_PENDING")) {
                workflowStatusId = respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].workflowStatusId").toString();
                LOGGER.info("workflowStatusId: " + workflowStatusId);
            }
        }
        if (statusCode == 200) {
            LOGGER.info(documentList);
            LOGGER.info(ownerDocument);
        }

    }

    public List<HashMap<String, String>> updateStatusToApproved(List<HashMap<String, String>> documents) {
        for (HashMap<String, String> document : documents) {
            document.put("status", "APPROVED"); // Set status to "APPROVED"
        }
        return documents;
    }

    @Test(priority = 15, groups = {"Regression"}, description = "Doing QC", retryAnalyzer = RetryAnalyzer.class , dependsOnMethods = "fetchingQCDeatails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void doingQC() throws InterruptedException {
        try {
            Map<String, String> headers = new HashMap<>();
            Map<String, String> queryParams = new HashMap<>();

            // Set up headers
            headers.put("accept", "application/json, text/plain, */*");
            headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
            headers.put("client_id", "oe-panel-staging");
            headers.put("content-type", "application/json;charset=UTF-8");
            headers.put("cookie", cookie);
            headers.put("encryption_enabled", "true");
            headers.put("origin", "https://oe-staging20.paytm.com");
            headers.put("priority", "u=1, i");
            headers.put("referer", "https://oe-staging20.paytm.com/");
            headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"");
            headers.put("sec-ch-ua-mobile", "?0");
            headers.put("sec-ch-ua-platform", "\"macOS\"");
            headers.put("sec-fetch-dest", "empty");
            headers.put("sec-fetch-mode", "cors");
            headers.put("sec-fetch-site", "same-site");
            headers.put("secret_version", "v1");
            headers.put("ssoid", "**********");
            headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            // Set up query params
            queryParams.put("action", "SUBMIT");

            // Convert documents to JSON format
            JSONArray ownerDocsJson = new JSONArray();
            for (HashMap<String, Object> doc : ownerDocument) {
                doc.put("status", "APPROVED");
                doc.put("containsNonRedactedAadhaar", "No");
                doc.put("noExpiryDate" , true);
                JSONObject docJson = new JSONObject(doc);
                ownerDocsJson.put(docJson);
            }


            JSONArray docsJson = new JSONArray();
            for (HashMap<String, Object> doc : documentList) {
                doc.put("status", "APPROVED");
                doc.put("containsNonRedactedAadhaar", "No");
                doc.put("noExpiryDate" , true);
                JSONObject docJson = new JSONObject(doc);
                docsJson.put(docJson);
            }


            // Create properties map
            Map<String, Object> properties = new HashMap<>();
            properties.put("ownerDocuments", ownerDocsJson.toString());
            properties.put("documentList", docsJson.toString());
            properties.put("reEnterIfscCode", ifscCode);
            properties.put("reEnterAccountNumber", bankAccountNumber);
            properties.put("workflowStatusId" , workflowStatusId);

            LOGGER.info("Request Payload: " + new JSONObject(properties).toString(4));

            // Perform API call
            EnterpriseQC obj = new EnterpriseQC(solutionLeadId);
            Response respobj = middlewareServicesObject.doingQCEnterprise(obj, headers, queryParams, properties);

            // Validate response
            int statusCode = respobj.getStatusCode();
            Assert.assertEquals(statusCode, 200, "Expected status code 200 but got " + statusCode);

            String responseBody = respobj.getBody().asString();
            LOGGER.info("Response Body: " + responseBody);
            Assert.assertTrue(responseBody.contains("success"), "Response does not indicate success!");

            LOGGER.info("QC Done Successfully");

        } catch (Exception e) {
            LOGGER.error("Error in doingQC test: ", e);
            Assert.fail("Test failed due to exception: " + e.getMessage());
        }
        Thread.sleep(20000);
    }
    @Test(priority = 16, groups = {"Regression"}, description = "Fetch QC Details", retryAnalyzer = RetryAnalyzer.class , dependsOnMethods = "doingQC")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchingCommercialDetails() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("cookie", cookie);
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "**********");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        FetchQCDetails obj = new FetchQCDetails(solutionLeadId);
        Response respobj = middlewareServicesObject.fetchQCDetailsEnterprise(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        if (statusCode == 200) {
            timelinedetails = respobj.jsonPath().get("leadDetails.timelineDetail");
            paymodes = respobj.jsonPath().get("leadDetails.payModeDetails");
            LOGGER.info(paymodes);
            for(int i=0;i<timelinedetails.size();i++){
                if (respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].stage").toString().equals("COMMERCIAL_APPROVAL") && respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].subStage").toString().equals("COMMERCIAL_APPROVAL_ACTION_PENDING")) {
                    commercialworkflowStatusId = respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].workflowStatusId").toString();
                    LOGGER.info("workflowStatusId: " + commercialworkflowStatusId);
                }
            }
        }
    }

    @Test(priority = 17, groups = {"Regression"}, description = "Reallocate Agent", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void reAllocateAgent() {
        try {
            Map<String, String> headers = new HashMap<>();
            Map<String, String> queryParams = new HashMap<>();
            Map<String, Object> properties = new HashMap<>();

            // Set up headers
            headers.put("accept", "application/json, text/plain, */*");
            headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
            headers.put("client_id", "oe-panel-staging");
            headers.put("content-type", "application/json;charset=UTF-8");
            headers.put("cookie", cookie);
            headers.put("encryption_enabled", "true");
            headers.put("origin", "https://oe-staging20.paytm.com");
            headers.put("priority", "u=1, i");
            headers.put("referer", "https://oe-staging20.paytm.com/");
            headers.put("sec-ch-ua", "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"");
            headers.put("sec-ch-ua-mobile", "?0");
            headers.put("sec-ch-ua-platform", "\"macOS\"");
            headers.put("sec-fetch-dest", "empty");
            headers.put("sec-fetch-mode", "cors");
            headers.put("sec-fetch-site", "same-site");
            headers.put("secret_version", "v1");
            headers.put("ssoid", "**********");
            headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36");

            // Set up query params
            queryParams.put("reallocationType", "ALLOCATED_AGENT");

            // Set up properties
            properties.put("leadId", solutionLeadId);
            properties.put("agentId", 1152);

            // Make API call
            ReAllocateAgent obj = new ReAllocateAgent();
            Response respobj = middlewareServicesObject.reAllocateAgent(obj, headers, queryParams, properties);

            // Validate response
            int statusCode = respobj.getStatusCode();
            Assert.assertEquals(statusCode, 200);


        } catch (Exception e) {
            LOGGER.error("Error in reAllocateAgent test: ", e);
            Assert.fail("Test failed due to exception: " + e.getMessage());
        }
    }

    @Test(priority = 18, groups = {"Regression"}, description = "Commercials Getting Approved", retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "fetchingCommercialDetails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void commercialStage() throws JSONException {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String , Object> body = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("cookie", cookie);
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "**********");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        queryParams.put("action", "SUBMIT");

        // Transform paymodes data
        JSONArray transformedPaymodes = new JSONArray();

        for (Map<String, Object> item : paymodes) {
            JSONObject paymentObj = new JSONObject();
            paymentObj.put("txnType", item.get("txnType"));

            Map<String, List<Map<String, Object>>> paymodeMap = (Map<String, List<Map<String, Object>>>) item.get("paymodes");
            JSONObject transformedPaymodeObj = new JSONObject();

            for (Map.Entry<String, List<Map<String, Object>>> entry : paymodeMap.entrySet()) {
                String paymodeName = entry.getKey();
                List<Map<String, Object>> paymodeList = entry.getValue();
                JSONArray transformedPaymodeArray = new JSONArray();

                for (Map<String, Object> paymode : paymodeList) {
                    JSONObject transformedPaymode = new JSONObject(paymode);
                    transformedPaymode.put("paymode", paymodeName);
                    transformedPaymode.put("approvedFee", paymode.get("fee"));
                    if (paymode.get("status").toString().equals("Rejected")) {
                        transformedPaymode.put("status", "Approved By Business Approval");
                    }
                    transformedPaymodeArray.put(transformedPaymode);
                }

                transformedPaymodeObj.put(paymodeName, transformedPaymodeArray);
            }
            paymentObj.put("paymodes", transformedPaymodeObj);
            transformedPaymodes.put(paymentObj);
        }

        // Create the final request structure
        JSONObject payModeDetails = new JSONObject();
        payModeDetails.put("payModeDetails", transformedPaymodes);
        LOGGER.info(payModeDetails.getString("payModeDetails"));
        // Update the body with transformed paymodes as string
        body.put("payModeDetails", payModeDetails.getString("payModeDetails").toString());
        body.put("workflowStatusId", commercialworkflowStatusId);
        EnterpriseCommercial obj = new EnterpriseCommercial(solutionLeadId);
        Response respobj = middlewareServicesObject.doingEnterpriseCommercial(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200, "Expected status code 200 but got " + statusCode);

    }
    @Test(priority = 19, groups = {"Regression"}, description = "Get the BankLob Data", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchingBankLobDetails() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("cookie", cookie);
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "**********");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        FetchQCDetails obj = new FetchQCDetails(solutionLeadId);
        Response respobj = middlewareServicesObject.fetchQCDetailsEnterprise(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        LOGGER.info("statusCode" + statusCode);
        Assert.assertEquals(statusCode, 200);
        if (statusCode == 200) {
            timelinedetails = respobj.jsonPath().get("leadDetails.timelineDetail");
            bankLobBenchMarkSRO = respobj.jsonPath().get("leadDetails.bankLobBenchMarkSRO");
            for(int i=0;i<timelinedetails.size();i++){
                if (respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].stage").toString().equals("LOB") && respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].subStage").toString().equals("BANK_LOB_ACTION_PENDING")) {
                    bankLobworkflowStatusId = respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].workflowStatusId").toString();
                }
            }
            LOGGER.info("workflowStatusId: " + bankLobworkflowStatusId);
            LOGGER.info("workflowStatusId: " + bankLobBenchMarkSRO);
            List<Map<String, Object>> upiList = new ArrayList<>();
            Map<String, Object> defaultBank = new HashMap<>();
            defaultBank.put("bank", "DEFAULT");
            defaultBank.put("disabled", false);
            upiList.add(defaultBank);
            for(Map.Entry<String,Object> entry : bankLobBenchMarkSRO.entrySet()){
                LOGGER.info(entry.getKey() + " : " + entry.getValue());
                for(Map.Entry<String,Object> entry1 : ((Map<String,Object>)entry.getValue()).entrySet()){
                    LOGGER.info(entry1.getKey() + " : " + entry1.getValue());
                    if(entry.getKey().equals("benchMark") && entry1.getKey().equals("UPI")){
                        entry1.setValue(upiList);
                    }
                }
            }
            LOGGER.info(bankLobBenchMarkSRO);
        }
    }


    @Test(priority = 19, groups = {"Regression"}, description = "Get the BankLob Data", retryAnalyzer = RetryAnalyzer.class , dependsOnMethods = "fetchingBankLobDetails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void bankLob() throws InterruptedException {
        try {
            Map<String, String> headers = new HashMap<>();
            Map<String, String> queryParams = new HashMap<>();
            Map<String, Object> body = new HashMap<>();
            headers.put("accept", "application/json, text/plain, */*");
            headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
            headers.put("client_id", "oe-panel-staging");
            headers.put("content-type", "application/json;charset=UTF-8");
            headers.put("cookie", cookie);
            headers.put("encryption_enabled", "true");
            headers.put("origin", "https://oe-staging20.paytm.com");
            headers.put("priority", "u=1, i");
            headers.put("referer", "https://oe-staging20.paytm.com/");
            headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"");
            headers.put("sec-ch-ua-mobile", "?0");
            headers.put("sec-ch-ua-platform", "\"macOS\"");
            headers.put("sec-fetch-dest", "empty");
            headers.put("sec-fetch-mode", "cors");
            headers.put("sec-fetch-site", "same-site");
            headers.put("secret_version", "v1");
            headers.put("ssoid", "**********");
            headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            queryParams.put("action", "SUBMIT");
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonString = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(bankLobBenchMarkSRO);
            body.put("bankLobBenchMarkSRO", jsonString);
            body.put("workflowStatusId", bankLobworkflowStatusId);
            EnterpriseCommercial obj = new EnterpriseCommercial(solutionLeadId);
            Response respobj = middlewareServicesObject.doingEnterpriseCommercial(obj, headers, queryParams, body);
            int statusCode = respobj.getStatusCode();
            Assert.assertEquals(statusCode, 200, "Expected status code 200 but got " + statusCode);

        }
        catch (Exception e){
            LOGGER.error("Error in bankLob test: ", e);
            Assert.fail("Test failed due to exception: " + e.getMessage());
        }
        Thread.sleep(5000);
    }
    @Test(priority = 20, groups = {"Regression"}, description = "Get the aggrement Data", retryAnalyzer = RetryAnalyzer.class )
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchingAgreementDetails() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("cookie", cookie);
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "**********");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        FetchQCDetails obj = new FetchQCDetails(solutionLeadId);
        Response respobj = middlewareServicesObject.fetchQCDetailsEnterprise(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        LOGGER.info("statusCode" + statusCode);
        Assert.assertEquals(statusCode, 200);
        if (statusCode == 200) {
            timelinedetails = respobj.jsonPath().get("leadDetails.timelineDetail");
            for(int i=0;i<timelinedetails.size();i++){
                if (respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].stage").toString().equals("AGREEMENT_UPLOAD") && respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].subStage").toString().equals("AGREEMENT_ACTION_PENDING")) {
                    aggrementworkflow = respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].workflowStatusId").toString();
                }
            }
            LOGGER.info("workflowStatusId: " + aggrementworkflow);
        }
    }

    @Test(priority = 21, groups = {"Regression"}, description = "Doing the aggrement", retryAnalyzer = RetryAnalyzer.class  , dependsOnMethods = "fetchingAgreementDetails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void doingAgreement() throws InterruptedException {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, Object> body = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("cookie", cookie);
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "**********");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        queryParams.put("action", "SUBMIT");
        body.put("workflowStatusId", aggrementworkflow);
        body.put("aggrement_stage" , "True");
        EnterpriseCommercial obj = new EnterpriseCommercial(solutionLeadId);
        Response respobj = middlewareServicesObject.doingEnterpriseCommercial(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode, 200, "Expected status code 200 but got " + statusCode);
        Thread.sleep(5000);
    }
    @Test(priority = 22, groups = {"Regression"}, description = "Get the aggrement Upload Data", retryAnalyzer = RetryAnalyzer.class )
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchingAgreementUploadDetails() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("cookie", cookie);
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "**********");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        FetchQCDetails obj = new FetchQCDetails(solutionLeadId);
        Response respobj = middlewareServicesObject.fetchQCDetailsEnterprise(obj, headers, queryParams, body);
        int statusCode = respobj.getStatusCode();
        LOGGER.info("statusCode" + statusCode);
        Assert.assertEquals(statusCode, 200);
        if (statusCode == 200) {
            timelinedetails = respobj.jsonPath().get("leadDetails.timelineDetail");
            for(int i=0;i<timelinedetails.size();i++){
                if (respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].stage").toString().equals("AGREEMENT_UPLOAD") && respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].subStage").toString().equals("AGREEMENT_UPLOAD_PENDING")) {
                    aggrementworkflow = respobj.jsonPath().get("leadDetails.timelineDetail[" + i + "].workflowStatusId").toString();
                }
            }
            LOGGER.info("workflowStatusId: " + aggrementworkflow);
        }
    }

}