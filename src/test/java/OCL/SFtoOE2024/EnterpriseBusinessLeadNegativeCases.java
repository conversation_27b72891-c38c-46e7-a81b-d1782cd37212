package OCL.SFtoOE2024;


import OCL.Individual.SoundBox.FlowSoundBox;
import Request.MerchantService.v1.Enterprise.*;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static OCL.SFtoOE2024.SFtoOEUtils.*;
import static OCL.SFtoOE2024.SFtoOEUtils.OEpanelCookie;

public class EnterpriseBusinessLeadNegativeCases extends BaseMethod {
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final org.apache.logging.log4j.Logger LOGGER = org.apache.logging.log4j.LogManager.getLogger(FlowSoundBox.class);

    // Variables
    public String AgentNumber = "7771216290";
    public String LoginPassword = "paytm@123";
    public String model = "B2C";
    public String subModel = "Aggregator";
    public String nameAsPerNSDL = "TOUCH WOOD LIMITED";
    public String dateOfIncorporation = "2024-11-21";
    public String gstExempted = "No";
    public String legalName = "RONIT ARYA";
    public String tradeName = "ARYA ENTERPRISES";
    public String segment = "Government";
    public String subSegment = "Central Department";
    public String isInvolvedInInternationalTransaction = "No";
    public String isInternationalMerchant = "false";
    public String onboardingEntity = "OCL";
    public String email = "<EMAIL>";
    public String flowType = "offline";
    public String entityType = "PROPRIETORSHIP";//"PROPRIETORSHIP" "GOVERNMENT" ;
    public String pan = randomPanNumber("PROPRIETORSHIP");
    public String onboardingDocumentType = "PAN";
    public String onboardingDocumentValue = pan;
    public String gstin = randomGSTNumber(pan);
    public final String solutionType = "enterprise_merchant_business";
    public String leadId = "";
    public String host = getHost();
    public String cookie = OEpanelCookie(AgentNumber , LoginPassword , host);
    public String isValidated = "";
    public String firstName = "";
    public String middleName = "";
    public String lastName = "";
    public String channel = "OE_PANEL";
    public String leadType = "BUSINESS_LEAD";
    public String category = "Food";
    public String CategoryCode = "";
    public String pincode = "814149";
    public HashMap<String, String> solutionadditioninformetadata;
    public HashMap<String, String> registeredaddress;
    public String retailrelatedbusinessuuid = "";
    public String kycverifygstsuccessresponse = "";
    public String nameAsPerTan = "TOUCH WOOD LIMITED";
    public String state = "";
    public String city = "";
    public String country = "";

    public EnterpriseBusinessLeadNegativeCases() throws Exception {
    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddata(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("pan", pan);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataentitytypemissing(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", "");
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("pan", pan);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 500);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataSolutiontypemissing(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", "");
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("pan", pan);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 500);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataChannelmissing(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "");
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("pan", pan);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataModelmissing(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", "");
        queryParams.put("subModel", subModel);
        queryParams.put("pan", pan);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataSubmodelMissing(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", model);
        queryParams.put("subModel", "");
        queryParams.put("pan", pan);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataPanmissing(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("pan", "");
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 400);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataFlowTypemissing(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("pan", pan);
        queryParams.put("flowType", "");
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 400);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataleadtypemissing(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("pan", pan);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 500);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataCookiemissing(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("pan", pan);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , ""); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 401);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataSsoidmissing(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("pan", pan);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataAllheadersmissing(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("pan", pan);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 401);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataEntitytyperandom(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", "random");
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("pan", pan);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 500);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataSolutiontyperandom(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", "random");
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("pan", pan);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 500);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataChannelRandom(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "random");
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("pan", pan);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 500);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataModelrandom(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", "random");
        queryParams.put("subModel", subModel);
        queryParams.put("pan", pan);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataSubmodelrandom(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", model);
        queryParams.put("subModel", "random");
        queryParams.put("pan", pan);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataRandompan(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("pan", "random");
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataFlowtypeRandom(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("pan", pan);
        queryParams.put("flowType", "random");
        queryParams.put("leadType", "BUSINESS_LEAD");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Fetch the lead details from the given pan" , retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getleaddataleadtyperandom(){
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("model", model);
        queryParams.put("subModel", subModel);
        queryParams.put("pan", pan);
        queryParams.put("flowType", flowType);
        queryParams.put("leadType", "random");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); //, cookie);
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        GetLeadDataEnterprise obj = new GetLeadDataEnterprise();
        Response respobj = middlewareServicesObject.getLeadDataEnterprise(obj, headers, queryParams);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 500);

        // We have to implement the case that if my response have a lead we will stop our test and say that the data already exists
    }

    @Test(priority = 2 , groups = {"Regression"}, description = "Enterprise Validate Pan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void enterpriseValidatePan(){
        // Query Parameters (none in this case)
        Map<String, String> queryParams = new HashMap<>();

        // Headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("Cookie" , cookie); // , cookie); // , "_hjSessionUser_2137993=eyJpZCI6IjYyOWYyOGE0LWE1ZDYtNTYyMy1iNTk2LWI2ZjllMTQzMzk2NSIsImNyZWF0ZWQiOjE3MzI3MDIxMjI3NDEsImV4aXN0aW5nIjp0cnVlfQ==; _gcl_au=1.1.1397050866.1732702172; _fbp=fb.1.1732702177743.337441985933055387; _ga=GA1.2.917964499.1732702123; _ga_Z4F7L25N51=GS1.2.1732789240.5.1.1732789286.14.0.0; _ga_LSKTYTR270=GS1.1.1732797914.6.0.1732797914.0.0.0; X-MW-TOKEN-EX=%2FxY1SEUKPE%2FP6RyryTGl0eYb3qnfXt5h9kFguEHoFRhOsT8jhRHdeJiTcZbWGfjVAY7K9%2F5bERJC4y9D3n8UvQ%3D%3D; X-MW-TOKEN-EXTERNAL=%2BX47T26Suug33%2BqHWqw1JM90Ydh5eQN6EaVOw4sXIhutGn1z189Qm3783YeqZ7uTWBvJEXITsCLWnZx8LbJCcw%3D%3D; X-MW-TOKEN-EX-V2=71fFZkDQ4hx8atP7WS8iOZ%2F50My2q1q2ucDKqNvCYI0mxlBr413HYRBGWH6oj%2B7yD8DAUAC%2FbfJyPt5BIsb9cPfR3L%2B9; X-MW-TOKEN-EXTERNAL-V2=71fFZkDQ4hx8atP7WS8iOZ%2F50My2q1q2ucDKqNvCYI0mxlBr413HYRBGWH6oj%2B7yD8DAUAC%2FbfJyPt5BIsb9cPfR3L%2B9; X-MW-TOKEN=523c0599-a359-41ed-8f9a-460d43a60c1e");
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        // Body Parameters
        Map<String, String> body = new HashMap<>();
        body.put("pan", pan);
        body.put("solutionType", solutionType);

        EnterpriseValidatePan obj = new EnterpriseValidatePan();
        Response respobj = middlewareServicesObject.enterpriseValidatePan( obj, headers, body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);
        if(StatusCode == 200){
            pan = respobj.jsonPath().getString("pan").toString();
            isValidated = respobj.jsonPath().getString("isValidated").toString();
            firstName = respobj.jsonPath().getString("firstName").toString();
            middleName = respobj.jsonPath().getString("middleName").toString();
            lastName = respobj.jsonPath().getString("lastName").toString();
        }
        

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead")
    @Owner( emailId =  "<EMAIL>", isAutomated = true)
    public void fetchAllResources(){
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", cookie);       // "X-MW-TOKEN-EX=7ljjrn6Nnqw%2F%2BZ1Axr%2BfJD49Juouv%2FhjePWnY%2Bk%2FzeYd64gzAOOwcZLNHDm4Tu0vH41LoQ3Yc3tdfnQ8QI90qg%3D%3D; X-MW-TOKEN-EXTERNAL=aiuGKDS8e3qQatX4GISnLNvlOqcv15RtxdFNgA%2FW1OueEVT1KdrCYc45%2BnpqxEyOfrEmEwRoA%2BdH%2BYR7ki23Vg%3D%3D; X-MW-TOKEN-EX-V2=71fFZhKTpxR8KMr5S3xzZ836iNi%2F7h%2F3utDcucjCYcMjg0tr417HYRBGWH6uiO3xD8PEVgtrpIMqo3lnDXArRXJeumAw; X-MW-TOKEN-EXTERNAL-V2=71fFZhKTpxR8KMr5S3xzZ836iNi%2F7h%2F3utDcucjCYcMjg0tr417HYRBGWH6uiO3xD8PEVgtrpIMqo3lnDXArRXJeumAw; X-MW-TOKEN=9b91c4e8-bb77-4a56-92dd-f4baeafb43cc");
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("typeList", "model_offline,subModel_B2C");
        EnterprisefetchAllResources obj = new EnterprisefetchAllResources();
        Response respobj = middlewareServicesObject.fetchAllResourcesEnterprise(obj , headers , queryParams);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
        if(StatusCode == 200){
            HashMap<Object , Object> dataMap = respobj.jsonPath().get("dataMap");
            LOGGER.info("Data Map is : " + dataMap);
        }
    }

    @Test(priority = 2 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead")
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void getCategorySubCategory(){
        Map<String , String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", cookie ); //"X-MW-TOKEN-EX=7ljjrn6Nnqw%2F%2BZ1Axr%2BfJD49Juouv%2FhjePWnY%2Bk%2FzeYd64gzAOOwcZLNHDm4Tu0vH41LoQ3Yc3tdfnQ8QI90qg%3D%3D; X-MW-TOKEN-EXTERNAL=aiuGKDS8e3qQatX4GISnLNvlOqcv15RtxdFNgA%2FW1OueEVT1KdrCYc45%2BnpqxEyOfrEmEwRoA%2BdH%2BYR7ki23Vg%3D%3D; X-MW-TOKEN-EX-V2=71fFZhKTpxR8KMr5S3xzZ836iNi%2F7h%2F3utDcucjCYcMjg0tr417HYRBGWH6uiO3xD8PEVgtrpIMqo3lnDXArRXJeumAw; X-MW-TOKEN-EXTERNAL-V2=71fFZhKTpxR8KMr5S3xzZ836iNi%2F7h%2F3utDcucjCYcMjg0tr417HYRBGWH6uiO3xD8PEVgtrpIMqo3lnDXArRXJeumAw; X-MW-TOKEN=9b91c4e8-bb77-4a56-92dd-f4baeafb43cc");
        headers.put("sec-ch-ua-platform", "macOS");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("client_id", "oe-panel-staging");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("secret_version", "v1");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("ssoid", "1001647902");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("solTypeLevel2", flowType );
        EnterpriseGetCategorySubCategory obj = new EnterpriseGetCategorySubCategory();
        Response respobj = middlewareServicesObject.getCategorySubCategoryEnterprise(obj , headers , queryParams);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
        if(StatusCode == 200){
            ArrayList<Object> dataList = respobj.jsonPath().get("catSubList");
            LOGGER.info("Data  is : " + dataList);
        }

    }


    @Test(priority = 2 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead")
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("solutionTypeLevel2", flowType);
        queryParams.put("partialSave", "true");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , cookie); //, "_ga=GA1.2.1283265820.1732089129; _gid=GA1.2.1818765987.1732089129; X-MW-TOKEN-EX=ZZmFbOeRmq1Bc%2FUbZ%2BHJJPSylXTHLlfWvdhWqe%2BPW8zHwZgQIJLqrt80Bmkh%2F3MGysJfmELZOFmNHkb04Pbg9Q%3D%3D; X-MW-TOKEN-EXTERNAL=oYR7lWF2r7DYcoyY%2FeYUUdaPMIbnsiJZVXEjX%2Blg%2FvRLQ%2Bt5m0mmVUY90CvKn60%2FizN7G01qIsCp7%2FKSCe2QiQ%3D%3D; X-MW-TOKEN-EX-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQi%2BXOvw5n8CFxjvgh8nbDch; X-MW-TOKEN-EXTERNAL-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQBmJYanXqQN1EtnqMeJZOUU; X-MW-TOKEN=9c84f7b4-4cd2-44ec-a6e8-f4f1dd51dd88; JSESSIONID=CEFCA54AE5EAAC40B2DF831C903FCAD4");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        Map<String , String> body = new HashMap<>();
        body.put("pan" , pan );
        body.put("gstin" , gstin);
        body.put("entityType" , entityType );
        body.put("MODEL" , model );
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);



        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
        leadId = respobj.jsonPath().getString("leadId").toString();

    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead")
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate_EntityType_missing(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", "");
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("solutionTypeLevel2", "offline");
        queryParams.put("partialSave", "true");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , cookie); //, "_ga=GA1.2.1283265820.1732089129; _gid=GA1.2.1818765987.1732089129; X-MW-TOKEN-EX=ZZmFbOeRmq1Bc%2FUbZ%2BHJJPSylXTHLlfWvdhWqe%2BPW8zHwZgQIJLqrt80Bmkh%2F3MGysJfmELZOFmNHkb04Pbg9Q%3D%3D; X-MW-TOKEN-EXTERNAL=oYR7lWF2r7DYcoyY%2FeYUUdaPMIbnsiJZVXEjX%2Blg%2FvRLQ%2Bt5m0mmVUY90CvKn60%2FizN7G01qIsCp7%2FKSCe2QiQ%3D%3D; X-MW-TOKEN-EX-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQi%2BXOvw5n8CFxjvgh8nbDch; X-MW-TOKEN-EXTERNAL-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQBmJYanXqQN1EtnqMeJZOUU; X-MW-TOKEN=9c84f7b4-4cd2-44ec-a6e8-f4f1dd51dd88; JSESSIONID=CEFCA54AE5EAAC40B2DF831C903FCAD4");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        
        Map<String , String> body = new HashMap<>();
        body.put("pan" , pan );
        body.put("entityType" , entityType );
        body.put("MODEL" , model );
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);

        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 400);
        String displayMessage = respobj.jsonPath().getString("displayMessage").toString();
        Assert.assertEquals(displayMessage , "Invalid entityType/solutionType");

    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead")
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate_solution_missing(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", "");
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("solutionTypeLevel2", "offline");
        queryParams.put("partialSave", "true");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , cookie); //, "_ga=GA1.2.1283265820.1732089129; _gid=GA1.2.1818765987.1732089129; X-MW-TOKEN-EX=ZZmFbOeRmq1Bc%2FUbZ%2BHJJPSylXTHLlfWvdhWqe%2BPW8zHwZgQIJLqrt80Bmkh%2F3MGysJfmELZOFmNHkb04Pbg9Q%3D%3D; X-MW-TOKEN-EXTERNAL=oYR7lWF2r7DYcoyY%2FeYUUdaPMIbnsiJZVXEjX%2Blg%2FvRLQ%2Bt5m0mmVUY90CvKn60%2FizN7G01qIsCp7%2FKSCe2QiQ%3D%3D; X-MW-TOKEN-EX-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQi%2BXOvw5n8CFxjvgh8nbDch; X-MW-TOKEN-EXTERNAL-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQBmJYanXqQN1EtnqMeJZOUU; X-MW-TOKEN=9c84f7b4-4cd2-44ec-a6e8-f4f1dd51dd88; JSESSIONID=CEFCA54AE5EAAC40B2DF831C903FCAD4");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        Map<String , String> body = new HashMap<>();
        body.put("pan" , pan );
        body.put("entityType" , entityType );
        body.put("MODEL" , model );
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);

        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 400);

        String errorMessage = respobj.jsonPath().getString("displayMessage").toString();
        Assert.assertEquals(errorMessage , "Solution Type is not valid");

    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead" , retryAnalyzer = RetryAnalyzer.class)
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate_channel_missing(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "");
        queryParams.put("solutionTypeLevel2", "offline");
        queryParams.put("partialSave", "true");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , cookie); //, "_ga=GA1.2.1283265820.1732089129; _gid=GA1.2.1818765987.1732089129; X-MW-TOKEN-EX=ZZmFbOeRmq1Bc%2FUbZ%2BHJJPSylXTHLlfWvdhWqe%2BPW8zHwZgQIJLqrt80Bmkh%2F3MGysJfmELZOFmNHkb04Pbg9Q%3D%3D; X-MW-TOKEN-EXTERNAL=oYR7lWF2r7DYcoyY%2FeYUUdaPMIbnsiJZVXEjX%2Blg%2FvRLQ%2Bt5m0mmVUY90CvKn60%2FizN7G01qIsCp7%2FKSCe2QiQ%3D%3D; X-MW-TOKEN-EX-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQi%2BXOvw5n8CFxjvgh8nbDch; X-MW-TOKEN-EXTERNAL-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQBmJYanXqQN1EtnqMeJZOUU; X-MW-TOKEN=9c84f7b4-4cd2-44ec-a6e8-f4f1dd51dd88; JSESSIONID=CEFCA54AE5EAAC40B2DF831C903FCAD4");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        Map<String , String> body = new HashMap<>();
        body.put("pan" , pan );
        body.put("entityType" , entityType );
        body.put("MODEL" , model );
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);

        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 500);

    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead" , retryAnalyzer = RetryAnalyzer.class)
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate_solutiontype_level2_missing(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("solutionTypeLevel2", "");
        queryParams.put("partialSave", "true");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , cookie); //, "_ga=GA1.2.1283265820.1732089129; _gid=GA1.2.1818765987.1732089129; X-MW-TOKEN-EX=ZZmFbOeRmq1Bc%2FUbZ%2BHJJPSylXTHLlfWvdhWqe%2BPW8zHwZgQIJLqrt80Bmkh%2F3MGysJfmELZOFmNHkb04Pbg9Q%3D%3D; X-MW-TOKEN-EXTERNAL=oYR7lWF2r7DYcoyY%2FeYUUdaPMIbnsiJZVXEjX%2Blg%2FvRLQ%2Bt5m0mmVUY90CvKn60%2FizN7G01qIsCp7%2FKSCe2QiQ%3D%3D; X-MW-TOKEN-EX-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQi%2BXOvw5n8CFxjvgh8nbDch; X-MW-TOKEN-EXTERNAL-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQBmJYanXqQN1EtnqMeJZOUU; X-MW-TOKEN=9c84f7b4-4cd2-44ec-a6e8-f4f1dd51dd88; JSESSIONID=CEFCA54AE5EAAC40B2DF831C903FCAD4");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        Map<String , String> body = new HashMap<>();
        body.put("pan" , pan );
        body.put("entityType" , entityType );
        body.put("MODEL" , model );
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);

        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead")
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate_partialSaveMissing(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("solutionTypeLevel2", "offline");
        queryParams.put("partialSave", "");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , cookie); //, "_ga=GA1.2.1283265820.1732089129; _gid=GA1.2.1818765987.1732089129; X-MW-TOKEN-EX=ZZmFbOeRmq1Bc%2FUbZ%2BHJJPSylXTHLlfWvdhWqe%2BPW8zHwZgQIJLqrt80Bmkh%2F3MGysJfmELZOFmNHkb04Pbg9Q%3D%3D; X-MW-TOKEN-EXTERNAL=oYR7lWF2r7DYcoyY%2FeYUUdaPMIbnsiJZVXEjX%2Blg%2FvRLQ%2Bt5m0mmVUY90CvKn60%2FizN7G01qIsCp7%2FKSCe2QiQ%3D%3D; X-MW-TOKEN-EX-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQi%2BXOvw5n8CFxjvgh8nbDch; X-MW-TOKEN-EXTERNAL-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQBmJYanXqQN1EtnqMeJZOUU; X-MW-TOKEN=9c84f7b4-4cd2-44ec-a6e8-f4f1dd51dd88; JSESSIONID=CEFCA54AE5EAAC40B2DF831C903FCAD4");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        Map<String , String> body = new HashMap<>();
        body.put("pan" , pan );
        body.put("entityType" , entityType );
        body.put("MODEL" , model );
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);

        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead" , retryAnalyzer = RetryAnalyzer.class)
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate_cookie_missing(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("solutionTypeLevel2", "offline");
        queryParams.put("partialSave", "true");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , ""); //, "");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        Map<String , String> body = new HashMap<>();
        body.put("pan" , pan );
        body.put("entityType" , entityType );
        body.put("MODEL" , model );
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);

        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 401);

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead" , retryAnalyzer = RetryAnalyzer.class)
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate_wrong_entity_type(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", "random");
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("solutionTypeLevel2", "offline");
        queryParams.put("partialSave", "true");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , cookie); //, "_ga=GA1.2.1283265820.1732089129; _gid=GA1.2.1818765987.1732089129; X-MW-TOKEN-EX=ZZmFbOeRmq1Bc%2FUbZ%2BHJJPSylXTHLlfWvdhWqe%2BPW8zHwZgQIJLqrt80Bmkh%2F3MGysJfmELZOFmNHkb04Pbg9Q%3D%3D; X-MW-TOKEN-EXTERNAL=oYR7lWF2r7DYcoyY%2FeYUUdaPMIbnsiJZVXEjX%2Blg%2FvRLQ%2Bt5m0mmVUY90CvKn60%2FizN7G01qIsCp7%2FKSCe2QiQ%3D%3D; X-MW-TOKEN-EX-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQi%2BXOvw5n8CFxjvgh8nbDch; X-MW-TOKEN-EXTERNAL-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQBmJYanXqQN1EtnqMeJZOUU; X-MW-TOKEN=9c84f7b4-4cd2-44ec-a6e8-f4f1dd51dd88; JSESSIONID=CEFCA54AE5EAAC40B2DF831C903FCAD4");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        Map<String , String> body = new HashMap<>();
        body.put("pan" , pan );
        body.put("entityType" , entityType );
        body.put("MODEL" , model );
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);

        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 400);

        String errorMessage = respobj.jsonPath().getString("displayMessage").toString();
        Assert.assertEquals(errorMessage , "Invalid entityType/solutionType");

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead")
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate_wrong_solution_type(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", "random");
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("solutionTypeLevel2", "offline");
        queryParams.put("partialSave", "true");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , cookie); //, "_ga=GA1.2.1283265820.1732089129; _gid=GA1.2.1818765987.1732089129; X-MW-TOKEN-EX=ZZmFbOeRmq1Bc%2FUbZ%2BHJJPSylXTHLlfWvdhWqe%2BPW8zHwZgQIJLqrt80Bmkh%2F3MGysJfmELZOFmNHkb04Pbg9Q%3D%3D; X-MW-TOKEN-EXTERNAL=oYR7lWF2r7DYcoyY%2FeYUUdaPMIbnsiJZVXEjX%2Blg%2FvRLQ%2Bt5m0mmVUY90CvKn60%2FizN7G01qIsCp7%2FKSCe2QiQ%3D%3D; X-MW-TOKEN-EX-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQi%2BXOvw5n8CFxjvgh8nbDch; X-MW-TOKEN-EXTERNAL-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQBmJYanXqQN1EtnqMeJZOUU; X-MW-TOKEN=9c84f7b4-4cd2-44ec-a6e8-f4f1dd51dd88; JSESSIONID=CEFCA54AE5EAAC40B2DF831C903FCAD4");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        Map<String , String> body = new HashMap<>();
        body.put("pan" , pan );
        body.put("entityType" , entityType );
        body.put("MODEL" , model );
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);

        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 400);

        String errorMessage = respobj.jsonPath().getString("displayMessage").toString();
        Assert.assertEquals(errorMessage , "Solution Type is not valid");

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead" , retryAnalyzer = RetryAnalyzer.class)
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate_random_channel(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "Random");
        queryParams.put("solutionTypeLevel2", "offline");
        queryParams.put("partialSave", "true");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , cookie); //, "_ga=GA1.2.1283265820.1732089129; _gid=GA1.2.1818765987.1732089129; X-MW-TOKEN-EX=ZZmFbOeRmq1Bc%2FUbZ%2BHJJPSylXTHLlfWvdhWqe%2BPW8zHwZgQIJLqrt80Bmkh%2F3MGysJfmELZOFmNHkb04Pbg9Q%3D%3D; X-MW-TOKEN-EXTERNAL=oYR7lWF2r7DYcoyY%2FeYUUdaPMIbnsiJZVXEjX%2Blg%2FvRLQ%2Bt5m0mmVUY90CvKn60%2FizN7G01qIsCp7%2FKSCe2QiQ%3D%3D; X-MW-TOKEN-EX-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQi%2BXOvw5n8CFxjvgh8nbDch; X-MW-TOKEN-EXTERNAL-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQBmJYanXqQN1EtnqMeJZOUU; X-MW-TOKEN=9c84f7b4-4cd2-44ec-a6e8-f4f1dd51dd88; JSESSIONID=CEFCA54AE5EAAC40B2DF831C903FCAD4");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        Map<String , String> body = new HashMap<>();
        body.put("pan" , pan );
        body.put("entityType" , entityType );
        body.put("MODEL" , model );
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);

        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 500);

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead")
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate_ssoid_missing(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("solutionTypeLevel2", "offline");
        queryParams.put("partialSave", "true");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , cookie); //, "_ga=GA1.2.1283265820.1732089129; _gid=GA1.2.1818765987.1732089129; X-MW-TOKEN-EX=ZZmFbOeRmq1Bc%2FUbZ%2BHJJPSylXTHLlfWvdhWqe%2BPW8zHwZgQIJLqrt80Bmkh%2F3MGysJfmELZOFmNHkb04Pbg9Q%3D%3D; X-MW-TOKEN-EXTERNAL=oYR7lWF2r7DYcoyY%2FeYUUdaPMIbnsiJZVXEjX%2Blg%2FvRLQ%2Bt5m0mmVUY90CvKn60%2FizN7G01qIsCp7%2FKSCe2QiQ%3D%3D; X-MW-TOKEN-EX-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQi%2BXOvw5n8CFxjvgh8nbDch; X-MW-TOKEN-EXTERNAL-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQBmJYanXqQN1EtnqMeJZOUU; X-MW-TOKEN=9c84f7b4-4cd2-44ec-a6e8-f4f1dd51dd88; JSESSIONID=CEFCA54AE5EAAC40B2DF831C903FCAD4");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        Map<String , String> body = new HashMap<>();
        body.put("pan" , pan );
        body.put("entityType" , entityType );
        body.put("MODEL" , model );
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);

        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead" , retryAnalyzer = RetryAnalyzer.class)
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate_random_solutiontype_level2(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("solutionTypeLevel2", "random");
        queryParams.put("partialSave", "true");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , cookie); //, "_ga=GA1.2.1283265820.1732089129; _gid=GA1.2.1818765987.1732089129; X-MW-TOKEN-EX=ZZmFbOeRmq1Bc%2FUbZ%2BHJJPSylXTHLlfWvdhWqe%2BPW8zHwZgQIJLqrt80Bmkh%2F3MGysJfmELZOFmNHkb04Pbg9Q%3D%3D; X-MW-TOKEN-EXTERNAL=oYR7lWF2r7DYcoyY%2FeYUUdaPMIbnsiJZVXEjX%2Blg%2FvRLQ%2Bt5m0mmVUY90CvKn60%2FizN7G01qIsCp7%2FKSCe2QiQ%3D%3D; X-MW-TOKEN-EX-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQi%2BXOvw5n8CFxjvgh8nbDch; X-MW-TOKEN-EXTERNAL-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQBmJYanXqQN1EtnqMeJZOUU; X-MW-TOKEN=9c84f7b4-4cd2-44ec-a6e8-f4f1dd51dd88; JSESSIONID=CEFCA54AE5EAAC40B2DF831C903FCAD4");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        Map<String , String> body = new HashMap<>();
        body.put("pan" , pan );
        body.put("entityType" , entityType );
        body.put("MODEL" , model );
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);

        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead" , retryAnalyzer = RetryAnalyzer.class)
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate_partialsave_false(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("solutionTypeLevel2", "offline");
        queryParams.put("partialSave", "false");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , cookie); //, "_ga=GA1.2.1283265820.1732089129; _gid=GA1.2.1818765987.1732089129; X-MW-TOKEN-EX=ZZmFbOeRmq1Bc%2FUbZ%2BHJJPSylXTHLlfWvdhWqe%2BPW8zHwZgQIJLqrt80Bmkh%2F3MGysJfmELZOFmNHkb04Pbg9Q%3D%3D; X-MW-TOKEN-EXTERNAL=oYR7lWF2r7DYcoyY%2FeYUUdaPMIbnsiJZVXEjX%2Blg%2FvRLQ%2Bt5m0mmVUY90CvKn60%2FizN7G01qIsCp7%2FKSCe2QiQ%3D%3D; X-MW-TOKEN-EX-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQi%2BXOvw5n8CFxjvgh8nbDch; X-MW-TOKEN-EXTERNAL-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQBmJYanXqQN1EtnqMeJZOUU; X-MW-TOKEN=9c84f7b4-4cd2-44ec-a6e8-f4f1dd51dd88; JSESSIONID=CEFCA54AE5EAAC40B2DF831C903FCAD4");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        Map<String , String> body = new HashMap<>();
        body.put("pan" , pan );
        body.put("entityType" , entityType );
        body.put("MODEL" , model );
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);

        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead")
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate_emptypan(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("solutionTypeLevel2", "offline");
        queryParams.put("partialSave", "true");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , cookie); //, "_ga=GA1.2.1283265820.1732089129; _gid=GA1.2.1818765987.1732089129; X-MW-TOKEN-EX=ZZmFbOeRmq1Bc%2FUbZ%2BHJJPSylXTHLlfWvdhWqe%2BPW8zHwZgQIJLqrt80Bmkh%2F3MGysJfmELZOFmNHkb04Pbg9Q%3D%3D; X-MW-TOKEN-EXTERNAL=oYR7lWF2r7DYcoyY%2FeYUUdaPMIbnsiJZVXEjX%2Blg%2FvRLQ%2Bt5m0mmVUY90CvKn60%2FizN7G01qIsCp7%2FKSCe2QiQ%3D%3D; X-MW-TOKEN-EX-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQi%2BXOvw5n8CFxjvgh8nbDch; X-MW-TOKEN-EXTERNAL-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQBmJYanXqQN1EtnqMeJZOUU; X-MW-TOKEN=9c84f7b4-4cd2-44ec-a6e8-f4f1dd51dd88; JSESSIONID=CEFCA54AE5EAAC40B2DF831C903FCAD4");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        Map<String , String> body = new HashMap<>();
        body.put("pan" , "" );
        body.put("entityType" , entityType );
        body.put("MODEL" , model );
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);



        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 400);
        String errorMessage = respobj.jsonPath().getString("displayMessage");
        Assert.assertEquals(errorMessage , "Pan is not present");

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead" , retryAnalyzer = RetryAnalyzer.class)
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate_empty_entity_body(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("solutionTypeLevel2", "offline");
        queryParams.put("partialSave", "true");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , cookie); //, "_ga=GA1.2.1283265820.1732089129; _gid=GA1.2.1818765987.1732089129; X-MW-TOKEN-EX=ZZmFbOeRmq1Bc%2FUbZ%2BHJJPSylXTHLlfWvdhWqe%2BPW8zHwZgQIJLqrt80Bmkh%2F3MGysJfmELZOFmNHkb04Pbg9Q%3D%3D; X-MW-TOKEN-EXTERNAL=oYR7lWF2r7DYcoyY%2FeYUUdaPMIbnsiJZVXEjX%2Blg%2FvRLQ%2Bt5m0mmVUY90CvKn60%2FizN7G01qIsCp7%2FKSCe2QiQ%3D%3D; X-MW-TOKEN-EX-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQi%2BXOvw5n8CFxjvgh8nbDch; X-MW-TOKEN-EXTERNAL-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQBmJYanXqQN1EtnqMeJZOUU; X-MW-TOKEN=9c84f7b4-4cd2-44ec-a6e8-f4f1dd51dd88; JSESSIONID=CEFCA54AE5EAAC40B2DF831C903FCAD4");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        Map<String , String> body = new HashMap<>();
        body.put("pan" , pan );
        body.put("entityType" , "" );
        body.put("MODEL" , model );
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);



        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead" , retryAnalyzer = RetryAnalyzer.class)
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate_empty_model(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("solutionTypeLevel2", "offline");
        queryParams.put("partialSave", "true");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , cookie); //, "_ga=GA1.2.1283265820.1732089129; _gid=GA1.2.1818765987.1732089129; X-MW-TOKEN-EX=ZZmFbOeRmq1Bc%2FUbZ%2BHJJPSylXTHLlfWvdhWqe%2BPW8zHwZgQIJLqrt80Bmkh%2F3MGysJfmELZOFmNHkb04Pbg9Q%3D%3D; X-MW-TOKEN-EXTERNAL=oYR7lWF2r7DYcoyY%2FeYUUdaPMIbnsiJZVXEjX%2Blg%2FvRLQ%2Bt5m0mmVUY90CvKn60%2FizN7G01qIsCp7%2FKSCe2QiQ%3D%3D; X-MW-TOKEN-EX-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQi%2BXOvw5n8CFxjvgh8nbDch; X-MW-TOKEN-EXTERNAL-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQBmJYanXqQN1EtnqMeJZOUU; X-MW-TOKEN=9c84f7b4-4cd2-44ec-a6e8-f4f1dd51dd88; JSESSIONID=CEFCA54AE5EAAC40B2DF831C903FCAD4");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        Map<String , String> body = new HashMap<>();
        body.put("pan" , pan );
        body.put("entityType" , entityType );
        body.put("MODEL" , "" );
        body.put("SUB_MODEL", subModel);
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);



        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Create the Lead Id for the Business Lead" , retryAnalyzer = RetryAnalyzer.class)
    @Owner( emailId = "<EMAIL>", isAutomated = true)
    public void BusinessLeadCreate_empty_submodel(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("solutionTypeLevel2", "offline");
        queryParams.put("partialSave", "true");

        Map<String , String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("Cookie" , cookie); //, "_ga=GA1.2.1283265820.1732089129; _gid=GA1.2.1818765987.1732089129; X-MW-TOKEN-EX=ZZmFbOeRmq1Bc%2FUbZ%2BHJJPSylXTHLlfWvdhWqe%2BPW8zHwZgQIJLqrt80Bmkh%2F3MGysJfmELZOFmNHkb04Pbg9Q%3D%3D; X-MW-TOKEN-EXTERNAL=oYR7lWF2r7DYcoyY%2FeYUUdaPMIbnsiJZVXEjX%2Blg%2FvRLQ%2Bt5m0mmVUY90CvKn60%2FizN7G01qIsCp7%2FKSCe2QiQ%3D%3D; X-MW-TOKEN-EX-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQi%2BXOvw5n8CFxjvgh8nbDch; X-MW-TOKEN-EXTERNAL-V2=71fFZhqb5FY%2BbJCrWngmN5fzn46w7B6s%2BpHO8MPCYJp%2FhEhr413HYRBGWH%2Boi%2Bv7DcfFVQBmJYanXqQN1EtnqMeJZOUU; X-MW-TOKEN=9c84f7b4-4cd2-44ec-a6e8-f4f1dd51dd88; JSESSIONID=CEFCA54AE5EAAC40B2DF831C903FCAD4");
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging20.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging20.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        Map<String , String> body = new HashMap<>();
        body.put("pan" , pan );
        body.put("entityType" , entityType );
        body.put("MODEL" , model );
        body.put("SUB_MODEL", "");
        body.put("DATE_OF_INCORPORATION" , dateOfIncorporation);
        body.put("NAME_AS_PER_NSDL" , nameAsPerNSDL);
        body.put("GST_EXEMPTED" , gstExempted);
        body.put("LEGAL_NAME" , legalName);
        body.put("TRADE_NAME", tradeName);
        body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION" , isInvolvedInInternationalTransaction);
        body.put("IS_INTERNATIONAL_MERCHANT" , isInternationalMerchant);
        body.put("ONBOARDING_ENTITY" , onboardingEntity);
        body.put("FLOW_TYPE" , flowType);



        CreateLeadEnterprise obj = new CreateLeadEnterprise();
        Response respobj = middlewareServicesObject.createLeadEnterprise(obj , headers , queryParams , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 12, groups = {"Regression"}, description = "Update Business Lead", retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "fetchBusinessLeadInformation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateBusinessLead_entity_empty() {
        Map<String, String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("cookie", cookie); // Use the same cookie from the previous method
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        // Query Parameters HashMap
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", "");
        queryParams.put("solution", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("partialSave", "false");
        queryParams.put("solutionTypeLevel2", flowType);
        queryParams.put("leadId", leadId);

        Map<String, String> body = new HashMap<>();
        if (onboardingDocumentType.equals("PAN")) {
            body.put("pan", pan);
        } else {
            body.put("ONBOARDING_DOCUMENT_TYPE" , onboardingDocumentType);
            body.put("ONBOARDING_DOCUMENT_VALUE" , onboardingDocumentValue);
        }
        body.put("entityType", entityType);
        body.put("relatedBusinessUuid" , retailrelatedbusinessuuid);
        body.put("kycverifygstsuccessresponse" , kycverifygstsuccessresponse);
        body.putAll(solutionadditioninformetadata);
        EnterpriseUpdateBusinessLead obj = new EnterpriseUpdateBusinessLead();
        Response respobj = middlewareServicesObject.updateBusinessLeadEnterprise(obj, headers, body, queryParams);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 13, groups = {"Regression"}, description = "Update Business Lead", retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "fetchBusinessLeadInformation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateBusinessLead_entity_empty_1() {
        Map<String, String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("client_id", "oe-panel-staging");
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("cookie", cookie); // Use the same cookie from the previous method
        headers.put("encryption_enabled", "true");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"macOS\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-site");
        headers.put("secret_version", "v1");
        headers.put("ssoid", "1001647902");
        headers.put("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        // Query Parameters HashMap
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", "");
        queryParams.put("solution", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("partialSave", "false");
        queryParams.put("solutionTypeLevel2", flowType);
        queryParams.put("leadId", leadId);

        Map<String, String> body = new HashMap<>();
        if (onboardingDocumentType.equals("PAN")) {
            body.put("pan", pan);
        } else {
            body.put("ONBOARDING_DOCUMENT_TYPE" , onboardingDocumentType);
            body.put("ONBOARDING_DOCUMENT_VALUE" , onboardingDocumentValue);
        }
        body.put("entityType", entityType);
        body.put("relatedBusinessUuid" , retailrelatedbusinessuuid);
        body.put("kycverifygstsuccessresponse" , kycverifygstsuccessresponse);
        body.putAll(solutionadditioninformetadata);
        EnterpriseUpdateBusinessLead obj = new EnterpriseUpdateBusinessLead();
        Response respobj = middlewareServicesObject.updateBusinessLeadEnterprise(obj, headers, body, queryParams);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
}
