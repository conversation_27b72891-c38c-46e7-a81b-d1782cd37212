package OCL.CRM;

import Request.CRM.v1.create.CancelEvent;
import Request.CRM.v1.create.CreateEvent;
import Request.CRM.v1.create.UpdateEvent;
import Services.oAuth.oAuthServices;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.ssh.SSHConnection;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;

import java.sql.Connection;
import java.util.Date;
import java.util.Map;

public class CRMBaseApi extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(CRMBaseApi.class);

    oAuthServices oAuthServicesObject = new oAuthServices();
    static Connection connection = null;
    static SSHConnection sshConnection = new SSHConnection();
    public static String DbName = "sprint23_2";
    
    /*
     * Method to create Event
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEvent(Map<String, String> headers, Map<String, String> body, String RequestPath) {

        CreateEvent createEventObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
      	  createEventObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
      	  createEventObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventObjectResponse = createEventObject.callAPI();

        return createEventObjectResponse;
    }
    
    /*
     * Method to create Event Without CustId
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithoutCustId(Map<String, String> headers, Map<String, String> body, String RequestPath) {

        CreateEvent createEventWithoutCustIdObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
        	createEventWithoutCustIdObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
        	createEventWithoutCustIdObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventWithoutCustIdObjectResponse = createEventWithoutCustIdObject.callAPI();

        return createEventWithoutCustIdObjectResponse;
    }
    
    
    /*
     * Method to create Event Without Email
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithoutEmail(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        CreateEvent createEventWithoutEmailObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
        	createEventWithoutEmailObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
        	createEventWithoutEmailObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventWithoutEmailObjectResponse = createEventWithoutEmailObject.callAPI();

        return createEventWithoutEmailObjectResponse;
    }
    
    /*
     * Method to create Event Without Mobile Number
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithoutMobileNumber(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        CreateEvent createEventWithoutMobileNumberObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
        	createEventWithoutMobileNumberObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
        	createEventWithoutMobileNumberObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventWithoutMobileNumberObjectResponse = createEventWithoutMobileNumberObject.callAPI();

        return createEventWithoutMobileNumberObjectResponse;
    }
    
    /*
     * Method to create Event Without Solution Sub Type
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithoutSolutionSubType(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        CreateEvent createEventWithoutSolutionSubTypeObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
        	createEventWithoutSolutionSubTypeObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
        	createEventWithoutSolutionSubTypeObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventWithoutSolutionSubTypeObjectResponse = createEventWithoutSolutionSubTypeObject.callAPI();

        return createEventWithoutSolutionSubTypeObjectResponse;
    }
    
    /*
     * Method to create Event Without Solution Type
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithoutSolutionType(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        CreateEvent createEventWithoutSolutionTypeObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
        	createEventWithoutSolutionTypeObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
        	createEventWithoutSolutionTypeObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventWithoutSolutionTypeObjectResponse = createEventWithoutSolutionTypeObject.callAPI();

        return createEventWithoutSolutionTypeObjectResponse;
    }
    
    
    
    /*
     * Method to update Event
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response UpdateEvent(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        UpdateEvent updateEventObject = new UpdateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
      	  updateEventObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
      	  updateEventObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response updateEventObjectResponse = updateEventObject.callAPI();

        return updateEventObjectResponse;
    }
    
    /*
     * Method to update Event when one role is empty
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response UpdateEventWhenOneRoleEmpty(Map<String, String> headers, Map<String, String> body,String RequestPath) {

    	UpdateEvent updateEventWhenOneRoleEmptyObject = new UpdateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
        	updateEventWhenOneRoleEmptyObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
        	updateEventWhenOneRoleEmptyObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response updateEventWhenOneRoleEmptyObjectResponse = updateEventWhenOneRoleEmptyObject.callAPI();

        return updateEventWhenOneRoleEmptyObjectResponse;
    }
    
    /*
     * Method to update Event without EventId
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response UpdateEventWithoutEventId(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        UpdateEvent updateEventWithoutEventIdObject = new UpdateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
      	  updateEventWithoutEventIdObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
      	  updateEventWithoutEventIdObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response updateEventWithoutEventIdObjectResponse = updateEventWithoutEventIdObject.callAPI();

        return updateEventWithoutEventIdObjectResponse;
    }
    
    /*
     * Method to update Event with Multiple EventId
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response UpdateEventWithMultipleEventId(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        UpdateEvent updateEventWithMultipleEventIdObject = new UpdateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
      	  updateEventWithMultipleEventIdObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
      	  updateEventWithMultipleEventIdObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response updateEventWithMultipleEventIdObjectResponse = updateEventWithMultipleEventIdObject.callAPI();

        return updateEventWithMultipleEventIdObjectResponse;
    }
    
    
    /*
     * Method to update Event with Invalid JWT Token
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response UpdateEventWithInvalidJWTToken(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        UpdateEvent updateEventWithInvalidJWTTokenObject = new UpdateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
      	  updateEventWithInvalidJWTTokenObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
      	  updateEventWithInvalidJWTTokenObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response updateEventWithInvalidJWTTokenObjectResponse = updateEventWithInvalidJWTTokenObject.callAPI();

        return updateEventWithInvalidJWTTokenObjectResponse;
    }
    
    /*
     * Method to create Event with multiple roles
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithMultipleRoles(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        CreateEvent createEventObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
      	  createEventObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
      	  createEventObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventObjectResponse = createEventObject.callAPI();

        return createEventObjectResponse;
    }
    
    /*
     * Method to create Event with three stakeholders
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithThreeStakeHolders(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        CreateEvent createEventWithThreeStakeHoldersObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
      	  createEventWithThreeStakeHoldersObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
      	  createEventWithThreeStakeHoldersObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventWithThreeStakeHoldersObjectResponse = createEventWithThreeStakeHoldersObject.callAPI();

        return createEventWithThreeStakeHoldersObjectResponse;
    }
    
    /**
     * Method to cancel event
     * @param queryParams
     * @param headers
     * @return
     */

    public Response CancelEvent(Map<String, String> queryParams, Map<String, String> headers,String RequestPath) {

        CancelEvent cancelEventObject = new CancelEvent(RequestPath);

        for (Map.Entry m : queryParams.entrySet()) {
            cancelEventObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            cancelEventObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response cancelEventObjectResponse = cancelEventObject.callAPI();


        return cancelEventObjectResponse;
    }
    
    
    
    
    /*
     * Method to create Event without Event type
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithoutEventType(Map<String, String> headers, Map<String, String> body, String RequestPath) {

        CreateEvent createEventWithoutEventTypeObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
        	createEventWithoutEventTypeObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
        	createEventWithoutEventTypeObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventWithoutEventTypeObjectResponse = createEventWithoutEventTypeObject.callAPI();

        return createEventWithoutEventTypeObjectResponse;
    }
    
    /*
     * Method to create Event with invalid Event type
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithInvalidEventType(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        CreateEvent createEventWithInvalidEventTypeObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
        	createEventWithInvalidEventTypeObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
        	createEventWithInvalidEventTypeObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventWithInvalidEventTypeObjectResponse = createEventWithInvalidEventTypeObject.callAPI();

        return createEventWithInvalidEventTypeObjectResponse;
    }
    
    /*
     * Method to create Event with invalid JWT Token
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithInvalidJWTToken(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        CreateEvent createEventWithInvalidJWTTokenObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
        	createEventWithInvalidJWTTokenObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
        	createEventWithInvalidJWTTokenObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventWithInvalidJWTTokenObjectResponse = createEventWithInvalidJWTTokenObject.callAPI();

        return createEventWithInvalidJWTTokenObjectResponse;
    }
    
    /*
     * Method to create Event without Context Value
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithoutContextValue(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        CreateEvent createEventWithoutContextValueObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
        	createEventWithoutContextValueObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
        	createEventWithoutContextValueObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventWithoutContextValueObjectResponse = createEventWithoutContextValueObject.callAPI();

        return createEventWithoutContextValueObjectResponse;
    }
    
    /*
     * Method to create Event without Context Key
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithoutContextKey(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        CreateEvent createEventWithoutContextKeyObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
        	createEventWithoutContextKeyObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
        	createEventWithoutContextKeyObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventWithoutContextKeyObjectResponse = createEventWithoutContextKeyObject.callAPI();

        return createEventWithoutContextKeyObjectResponse;
    }
    
    /*
     * Method to create Event without title
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithoutTitle(Map<String, String> headers, Map<String, String> body, String RequestPath) {

        CreateEvent createEventWithoutTitleObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
        	createEventWithoutTitleObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
        	createEventWithoutTitleObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventWithoutTitleObjectResponse = createEventWithoutTitleObject.callAPI();

        return createEventWithoutTitleObjectResponse;
    }
    
    /*
     * Method to create Event without description
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithoutDescription(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        CreateEvent createEventWithoutDescriptionObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
        	createEventWithoutDescriptionObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
        	createEventWithoutDescriptionObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventWithoutDescriptionObjectResponse = createEventWithoutDescriptionObject.callAPI();

        return createEventWithoutDescriptionObjectResponse;
    }
    
    /*
     * Method to create Event without StartTime
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithoutStartTime(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        CreateEvent createEventWithoutStartTimeObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
        	createEventWithoutStartTimeObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
        	createEventWithoutStartTimeObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventWithoutStartTimeObjectResponse = createEventWithoutStartTimeObject.callAPI();

        return createEventWithoutStartTimeObjectResponse;
    }
    
    /*
     * Method to create Event without Roles
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithoutRoles(Map<String, String> headers, Map<String, String> body,String RequestPath) {

        CreateEvent createEventWithoutRolesObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
        	createEventWithoutRolesObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
        	createEventWithoutRolesObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventWithoutRolesObjectResponse = createEventWithoutRolesObject.callAPI();

        return createEventWithoutRolesObjectResponse;
    }
    
    /*
     * Method to create Event without EndTime
     * @param headers
     * @param body
     * @return
     */
    
    
    public Response CreateEventWithoutEndTime(Map<String, String> headers, Map<String, String> body, String RequestPath) {

        CreateEvent createEventWithoutEndTimeObject = new CreateEvent(RequestPath);

        for (Map.Entry m : headers.entrySet()) {
        	createEventWithoutEndTimeObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
        	createEventWithoutEndTimeObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createEventWithoutEndTimeObjectResponse = createEventWithoutEndTimeObject.callAPI();

        return createEventWithoutEndTimeObjectResponse;
    }
    
    
    
    
    /**
     * Method to generate JWT token using epoch time
     * @param custId
     * @return
     */
    public String generateJwtTokenUsingEpochTime(String clientId,String custId) {
        String token = "";
        
        Date date = new Date();
        long unixTime = date.getTime();
        System.out.println(unixTime);

        Algorithm buildAlgorithm = Algorithm.HMAC256("29589d4d-9967-4851-8ba0-5984ab09a9ed");
        token = JWT.create().withClaim("custId", custId)
                .withClaim("clientId", clientId)
                .withIssuedAt(date).sign(buildAlgorithm);
           
               
        return token;
    }
    
    /**
     * Verify  Response Code as 200 OK
     * @param responseObject
     */
    public void verifyResponseCodeAs200OK(Response responseObject) {

        LOGGER.info("Status Code : " +responseObject.getStatusCode());

        Assert.assertEquals(responseObject.getStatusCode(),200);

    }
    
    /**
     * Verify  Response Code as 400 Bad Request
     * @param responseObject
     */
    public void verifyResponseCodeAs400BadRequest(Response responseObject) {

        LOGGER.info("Status Code : " +responseObject.getStatusCode());

        Assert.assertEquals(responseObject.getStatusCode(),400);

    }
    
    
    /**
     * Verify  Response Code as 401 Unauthorized
     * @param responseObject
     */
    public void verifyResponseCodeAs401Unauthorized(Response responseObject) {

    	
        LOGGER.info("Status Code : " +responseObject.getStatusCode());
        Assert.assertEquals(responseObject.getStatusCode(),401);

    }
    
  
    
    
    
    
    
    
    
    
    
    
}
