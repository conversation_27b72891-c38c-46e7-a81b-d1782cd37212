package OCL.CRM;

import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

public class TestCRM extends CRMBaseApi {
	
	private static final Logger LOGGER = LogManager.getLogger(TestCRM.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	CRMBaseApi CRMBaseClassObject = new CRMBaseApi();
	

	//generates a unique context-key
	public String GenerateUniqueContextKey() {
	int n = 9;
	Random randGen = new Random();

	int startNum = (int) Math.pow(10, n-1);
	int range = (int) (Math.pow(10, n) - startNum + 1);

	int randomNum = randGen.nextInt(range) + startNum;
	String contextKey="OE_FOLLOW"+randomNum;
	return contextKey;
	}
	
	//generate Start Time
	public String GenerateStartTime() {
		
		
		 SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
		    Date date = new Date(new Date().getTime() + 86400000);  
		    System.out.println(formatter.format(date));
		    String StartTime=formatter.format(date);
			return StartTime; 
		
		
	}
	
	//generate End Time
		public String GenerateEndTime() {
			
			
			 SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
			    Date date = new Date(new Date().getTime() + 172800000);  
			    System.out.println(formatter.format(date));
			    String StartTime=formatter.format(date);
				return StartTime; 
			
			
		}

	String sessionToken = "";
	String leadId = "";
	String clientId="OE";
	String custId = "1106992015";
	String agentNumber = "9717017397";
	String agentPassword = "paytm@123";
	String eventType="oe_lead_followup";
	String contextKey="";
	String contextValue="3f42053f-e00b-4f1d-b954-fcb30hj88";
	String title="Follow up Event";
	String description="Event for following up with agent";
	String startTime=GenerateStartTime();
	String endTime=GenerateEndTime();
	String email="<EMAIL>";
	String mobileNumber="9198457848";
	String role_1="ASSIGNEE";
	String role_2="MANAGER";
	String SOLUTION_SUB_TYPE="EDC";
	String SOLUTION_TYPE="Register_lead";
	String TL_NAME="Neeraj";
	String AGENT_NAME="Harsh";
	String MERCHANT_NAME="Hello";
	String SOLUTION_REQUESTED="My Solution";
	String token = "";
	String eventId="";
	String RequestPath="";
	
	
	Map<String, String> commonHeaders;
	

	@BeforeClass()
	public void intitializeInputData() throws IOException {

		LOGGER.info(" Before Suite Method for Event Creation ");
		//commonHeaders = setcommonHeaders();

	}
	
	
	@Test(description = "Create Event", groups = {"Regression" },enabled=true,priority=1)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC01_CreateEvent() {
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
		headers.put("jwt", token);
		
		RequestPath="CRM/v1/CreateEventRequest.json";
		
		contextKey=GenerateUniqueContextKey();
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("eventType", eventType);
		body.put("contextKey", contextKey);
		body.put("contextValue", contextValue);
		body.put("title", title);
	
		body.put("description", description);
		body.put("startTime", startTime);
	
		body.put("endTime", endTime);
		body.put("custId", custId);
		body.put("email", email);
		body.put("mobileNumber", mobileNumber);
		body.put("role_1", role_1);
		body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
		body.put("AGENT_NAME", AGENT_NAME);
		body.put("MERCHANT_NAME", MERCHANT_NAME);
		body.put("TL_NAME", TL_NAME);
		body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
		
		
		body.put("SOLUTION_TYPE", SOLUTION_TYPE);
		
		
	
		Response responseObject = CRMBaseClassObject.CreateEvent(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
			verifyResponseCodeAs200OK(responseObject);
			Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully created");
			
			 eventId=responseObject.jsonPath().getString("eventId");
		
	
	}   
	
	
	@Test(description = "Update Event",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=2)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC02_UpdateEvent() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			
			RequestPath="CRM/v1/UpdateEventRequest.json";
			
			String email="<EMAIL>";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventId", eventId);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.UpdateEvent(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
				verifyResponseCodeAs200OK(responseObject);
				Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully updated");
				
			
		
		}   
		
	
	
	 @Test(description = "Create Event with multiple roles", groups = {"Regression" },enabled=true,priority=18)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC03_CreateEventWithMultipleRoles() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			
			RequestPath="CRM/v1/CreateEventWithMultipleRoles.json";
			
			contextKey=GenerateUniqueContextKey();
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			body.put("role_2", role_2);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithMultipleRoles(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
				verifyResponseCodeAs200OK(responseObject);
				Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully created");
				
			 eventId=responseObject.jsonPath().getString("eventId");
		
		}  
	
	@Test(description = "Cancel Event",dependsOnMethods = {"TC03_CreateEventWithMultipleRoles"}, groups = {"Regression" },enabled=true,priority=20)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC04_CancelEvent() {
			
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("eventId", eventId);
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
      
			RequestPath="CRM/v1/CancelEventRequest.json";
			
			Response responseObject = CRMBaseClassObject.CancelEvent(queryParams, headers,RequestPath);
			

			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
				verifyResponseCodeAs200OK(responseObject);
				Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully cancelled");
				
				
	} 
	
	@Test(description = "Create Event without Event Type", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC05_CreateEventWithoutEventType() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			RequestPath="CRM/v1/CreateEventRequestWithoutEventType.json";
			
			contextKey=GenerateUniqueContextKey();
		    String eventType="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithoutEventType(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs400BadRequest(responseObject);
				Assert.assertNotEquals(responseObject.jsonPath().getString("resultInfo.resultMsg"), "Event has been successfully created");
				Assert.assertEquals(responseObject.jsonPath().getString("resultInfo.resultCode"), "BUSINESS_EXCEPTION");
		
		}   
		
	@Test(description = "Create Event without Context value", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC06_CreateEventWithoutContextValue() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			RequestPath="CRM/v1/CreateEventWithoutContextValue.json";
			
			contextKey=GenerateUniqueContextKey();
			
			String contextValue="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithoutContextValue(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs400BadRequest(responseObject);
				Assert.assertNotEquals(responseObject.jsonPath().getString("resultInfo.resultMsg"), "Event has been successfully created");
				Assert.assertEquals(responseObject.jsonPath().getString("resultInfo.resultCode"), "BUSINESS_EXCEPTION");
		
		}   
		
	
	@Test(description = "Create Event without Context value", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC07_CreateEventWithoutContextKey() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			RequestPath="CRM/v1/CreateEventRequestWithoutContextKey.json";
			
			String contextKey="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithoutContextKey(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs400BadRequest(responseObject);
				Assert.assertNotEquals(responseObject.jsonPath().getString("resultInfo.resultMsg"), "Event has been successfully created");
				Assert.assertEquals(responseObject.jsonPath().getString("resultInfo.resultCode"), "BUSINESS_EXCEPTION");
		
		}  
	
	@Test(description = "Create Event without Title", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC08_CreateEventWithoutTitle() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			RequestPath="CRM/v1/CreateEventRequestWithoutTitle.json";
			
			contextKey=GenerateUniqueContextKey();
			String title="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithoutTitle(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs400BadRequest(responseObject);
				Assert.assertNotEquals(responseObject.jsonPath().getString("resultInfo.resultMsg"), "Event has been successfully created");
				Assert.assertEquals(responseObject.jsonPath().getString("resultInfo.resultCode"), "BUSINESS_EXCEPTION");
		
		}    
	
	@Test(description = "Create Event without Description", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC09_CreateEventWithoutDescription() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			RequestPath="CRM/v1/CreateEventRequestWithoutDescription.json";
			
			contextKey=GenerateUniqueContextKey();
		    String description="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithoutDescription(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
			verifyResponseCodeAs200OK(responseObject);
			Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully created");
			
		
		}   
	
	@Test(description = "Create Event without StartTime", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC10_CreateEventWithoutStartTime() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			RequestPath="CRM/v1/CreateEventRequestWithoutStartTime.json";
			
			contextKey=GenerateUniqueContextKey();
			String startTime="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithoutStartTime(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			verifyResponseCodeAs400BadRequest(responseObject);
			Assert.assertNotEquals(responseObject.jsonPath().getString("resultInfo.resultMsg"), "Event has been successfully created");
			Assert.assertEquals(responseObject.jsonPath().getString("resultInfo.resultCode"), "BUSINESS_EXCEPTION");
		
		} 
	
	@Test(description = "Create Event without EndTime", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC11_CreateEventWithoutEndTime() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			RequestPath="CRM/v1/CreateEventRequestWithoutEndTime.json";
			
			contextKey=GenerateUniqueContextKey();
			String endTime="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithoutEndTime(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
			verifyResponseCodeAs200OK(responseObject);
			Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully created");
			
		
		}   
	
	@Test(description = "Create Event Without CustId", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC12_CreateEventWithoutCustId() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			RequestPath="CRM/v1/CreateEventRequest.json";
			
			contextKey=GenerateUniqueContextKey();
			String custId="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithoutCustId(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
				verifyResponseCodeAs200OK(responseObject);
				Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully created");
				
			
		
		}  
		
	
			
	@Test(description = "Create Event Without Email", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC13_CreateEventWithoutEmail() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			RequestPath="CRM/v1/CreateEventRequest.json";
			
			contextKey=GenerateUniqueContextKey();
			String email="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithoutEmail(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
				verifyResponseCodeAs200OK(responseObject);
				Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully created");
				
			
		
		}  
			
	
@Test(description = "Create Event Without Mobile Number", groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC14_CreateEventWithoutMobileNumber() {
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
		headers.put("jwt", token);
		RequestPath="CRM/v1/CreateEventRequest.json";
		
		contextKey=GenerateUniqueContextKey();
		String mobileNumber="";
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("eventType", eventType);
		body.put("contextKey", contextKey);
		body.put("contextValue", contextValue);
		body.put("title", title);
	
		body.put("description", description);
		body.put("startTime", startTime);
	
		body.put("endTime", endTime);
		body.put("custId", custId);
		body.put("email", email);
		body.put("mobileNumber", mobileNumber);
		body.put("role_1", role_1);
		body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
		body.put("AGENT_NAME", AGENT_NAME);
		body.put("MERCHANT_NAME", MERCHANT_NAME);
		body.put("TL_NAME", TL_NAME);
		body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
		body.put("SOLUTION_TYPE", SOLUTION_TYPE);
		
		
	
		Response responseObject = CRMBaseClassObject.CreateEventWithoutMobileNumber(headers, body,RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
			verifyResponseCodeAs200OK(responseObject);
			Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully created");
			
		
	
	}  
	
	@Test(description = "Create Event without Roles", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC15_CreateEventWithoutRoles() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			RequestPath="CRM/v1/CreateEventRequest.json";
			
			contextKey=GenerateUniqueContextKey();
			String role_1="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithoutRoles(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			verifyResponseCodeAs400BadRequest(responseObject);
			Assert.assertNotEquals(responseObject.jsonPath().getString("resultInfo.resultMsg"), "Event has been successfully created");
			Assert.assertEquals(responseObject.jsonPath().getString("resultInfo.resultCode"), "BUSINESS_EXCEPTION");
		
		} 


	@Test(description = "Create Event Without Solution Sub Type", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC16_CreateEventWithoutSolutionSubType() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			RequestPath="CRM/v1/CreateEventRequest.json";
			
			contextKey=GenerateUniqueContextKey();
			String SOLUTION_SUB_TYPE="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithoutSolutionSubType(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
				verifyResponseCodeAs200OK(responseObject);
				Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully created");
				
			
		
		}  
	
	@Test(description = "Create Event Without Solution Type", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC17_CreateEventWithoutSolutionType() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			RequestPath="CRM/v1/CreateEventRequest.json";
			
			contextKey=GenerateUniqueContextKey();
			String SOLUTION_TYPE="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithoutSolutionType(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
				verifyResponseCodeAs200OK(responseObject);
				Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully created");
				
			
		
		} 
	
	@Test(description = "Create Event with Invalid Event Type", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC18_CreateEventWithInvalidEventType() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			RequestPath="CRM/v1/CreateEventRequestWithoutEventType.json";
			
			contextKey=GenerateUniqueContextKey();
		    String eventType="SAMPLE_LEADDDJNVHJV";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithInvalidEventType(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs400BadRequest(responseObject);
				Assert.assertNotEquals(responseObject.jsonPath().getString("resultInfo.resultMsg"), "Event has been successfully created");
				Assert.assertEquals(responseObject.jsonPath().getString("resultInfo.resultCode"), "BUSINESS_EXCEPTION");
		
		}   
	
	@Test(description = "Create Event With correct Lead Id", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC19_CreateEventWithCorrectLeadId() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			RequestPath="CRM/v1/CreateEventRequest.json";
			
			contextKey=GenerateUniqueContextKey();
			String contextValue="3f42053f-e00b-4f1d-b954-fcb30e52f897";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEvent(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
				verifyResponseCodeAs200OK(responseObject);
				Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully created");
				
			
		
		}   
	
	@Test(description = "Create Event with Invalid Role which is not present in DB", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC20_CreateEventWithDifferentRole() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			RequestPath="CRM/v1/CreateEventRequest.json";
			
			contextKey=GenerateUniqueContextKey();
		    String role_1="Test";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithInvalidEventType(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs400BadRequest(responseObject);
				Assert.assertNotEquals(responseObject.jsonPath().getString("resultInfo.resultMsg"), "Event has been successfully created");
				Assert.assertEquals(responseObject.jsonPath().getString("resultInfo.resultCode"), "BUSINESS_EXCEPTION");
		
		}   
	
	@Test(description = "Create Event with Invalid JWT Token", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC21_CreateEventWithInvalidJWTToken() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
		//	token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			String InvalidToken="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjdXN0SWQiOiIxMTA2OTkyMDE1IiwiY2xpZW50SWQiOiJPRSIsImlhdCI6MTUxNTI3MzE5NH0.y17L7pvHqEvpW5F1l8lVqRIfWVMxfxr4RSf2Fi_nZ0E";
			headers.put("jwt", InvalidToken);
			RequestPath="CRM/v1/CreateEventRequest.json";
			
			
			contextKey=GenerateUniqueContextKey();
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithInvalidJWTToken(headers, body,RequestPath);
		
			
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			
				verifyResponseCodeAs401Unauthorized(responseObject);
				
		}   
	
	@Test(description = "Create Event when start_time is less than end_time", groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC22_CreateEventWhenStartTimeLessThanEndTime() {
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
		headers.put("jwt", token);
		RequestPath="CRM/v1/CreateEventRequest.json";
		
		contextKey=GenerateUniqueContextKey();
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("eventType", eventType);
		body.put("contextKey", contextKey);
		body.put("contextValue", contextValue);
		body.put("title", title);
	
		body.put("description", description);
		body.put("startTime", startTime);
	
		body.put("endTime", endTime);
		body.put("custId", custId);
		body.put("email", email);
		body.put("mobileNumber", mobileNumber);
		body.put("role_1", role_1);
		body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
		body.put("AGENT_NAME", AGENT_NAME);
		body.put("MERCHANT_NAME", MERCHANT_NAME);
		body.put("TL_NAME", TL_NAME);
		body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
		
		
		body.put("SOLUTION_TYPE", SOLUTION_TYPE);
		
		
	
		Response responseObject = CRMBaseClassObject.CreateEvent(headers, body,RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
			verifyResponseCodeAs200OK(responseObject);
			Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully created");
			
		
	
	}   
	
	@Test(description = "Create Event when Start_time is greater than End_time", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC23_CreateEventWhenStartTimeIsGreaterThanEndTime() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
			headers.put("jwt", token);
			RequestPath="CRM/v1/CreateEventRequest.json";
			
			contextKey=GenerateUniqueContextKey();
			String startTime=GenerateEndTime();
			String endTime=GenerateStartTime();
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("eventType", eventType);
			body.put("contextKey", contextKey);
			body.put("contextValue", contextValue);
			body.put("title", title);
		
			body.put("description", description);
			body.put("startTime", startTime);
		
			body.put("endTime", endTime);
			body.put("custId", custId);
			body.put("email", email);
			body.put("mobileNumber", mobileNumber);
			body.put("role_1", role_1);
			body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
			body.put("AGENT_NAME", AGENT_NAME);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("TL_NAME", TL_NAME);
			body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
			
			
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			
			
		
			Response responseObject = CRMBaseClassObject.CreateEventWithInvalidEventType(headers, body,RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs400BadRequest(responseObject);
				Assert.assertNotEquals(responseObject.jsonPath().getString("resultInfo.resultMsg"), "Event has been successfully created");
				Assert.assertEquals(responseObject.jsonPath().getString("resultInfo.resultCode"), "BUSINESS_EXCEPTION");
		
		}   
	
	 @Test(description = "Create Event with Role ASSIGNEE and MANAGER", groups = {"Regression" },enabled=true)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC24_CreateEventWithCustomizeRole() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/CreateEventRequest.json";
				
				
				contextKey=GenerateUniqueContextKey();
				String role_1="ASSIGNEE";
				String role_2="MANAGER";
			
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventType", eventType);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				body.put("role_2", role_2);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.CreateEventWithMultipleRoles(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
					verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully created");
					
			
			
			}  
	 
	 @Test(description = "Create Event with Three Stakeholders (ASSIGNEE,MANAGER,MERCHANT)", groups = {"Regression" },enabled=true)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC25_CreateEventWithThreeStakeHolders() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/CreateEventWithThreeStakeHolders.json";
				
				contextKey=GenerateUniqueContextKey();
				String role_1="ASSIGNEE";
				String role_2="MANAGER";
				String role_3="MERCHANT";
				String custId_2="1106992016";
				String custId_3="1106992017";
			
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventType", eventType);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("custId_2", custId_2);
				body.put("custId_3", custId_3);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				body.put("role_2", role_2);
				body.put("role_3", role_3);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.CreateEventWithThreeStakeHolders(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
					verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully created");
					
			
			
			}  
	 
	 @Test(description = "Update Event Without Event_id",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=24)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC26_UpdateEventWithoutEventId() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventRequest.json";
				
			
				String email="<EMAIL>";
				String eventId="";
			
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEventWithoutEventId(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs400BadRequest(responseObject);
				Assert.assertNotEquals(responseObject.jsonPath().getString("resultInfo.resultMsg"), "Event has been successfully updated");
				Assert.assertEquals(responseObject.jsonPath().getString("resultInfo.resultCode"), "BUSINESS_EXCEPTION");
			
				
			
			}   
	 
	 @Test(description = "Update Event Without Context Value",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=25)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC27_UpdateEventWithoutContextValue() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventRequest.json";
				
			
				String email="<EMAIL>";
				String contextValue="";
			
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEventWithoutEventId(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs400BadRequest(responseObject);
				Assert.assertNotEquals(responseObject.jsonPath().getString("resultInfo.resultMsg"), "Event has been successfully updated");
				Assert.assertEquals(responseObject.jsonPath().getString("resultInfo.resultCode"), "BUSINESS_EXCEPTION");
			
				
			
			}   
	 
	 @Test(description = "Update Event With Invalid JWT Token",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=3)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC28_UpdateEventWithInvalidJWTToken() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
			//	token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				String invalidToken="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjdXN0SWQiOiIxMTA2OTkyMDE1IiwiY2xpZW50SWQiOiJPRSIsImlhdCI6MTUxNTI3MzE5NH0.y17L7pvHqEvpW5F1l8lVqRIfWVMxfxr4RSf2Fi_nZ0E";
				headers.put("jwt", invalidToken);
				RequestPath="CRM/v1/UpdateEventRequest.json";
				
			
				String email="<EMAIL>";
			
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEventWithInvalidJWTToken(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs401Unauthorized(responseObject);
				
			
			}   
	 
	 @Test(description = "Update Event With Multiple Event_id",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=4)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC29_UpdateEventWithMultipleEventId() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventWithMultipleEventRequest.json";
				
			
				String email="<EMAIL>";
				String eventId_2="250";
			
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("eventId_2", eventId_2);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEventWithMultipleEventId(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs400BadRequest(responseObject);
			
				
			
			}   
	 
	 @Test(description = "Update Event Without Changing CustID",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=5)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC30_UpdateEventWithoutChangingCustId() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventRequest.json";
				
			
				String email="<EMAIL>";
				String mobileNumber="9999955555";
				String role_1="MERCHANT";
				
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEvent(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
					verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully updated");
					
				
			
			}   
	 
	 @Test(description = "Update Event Without Changing Mobile Number",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=6)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC31_UpdateEventWithoutChangingMobile() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventRequest.json";
				
			
				String email="<EMAIL>";
				String custId="1106992020";
				String role_1="MERCHANT";
				
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEvent(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
					verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully updated");
					
				
			
			}   
			
	 @Test(description = "Update Event Without Changing Email",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=7)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC32_UpdateEventWithoutChangingEmail() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventRequest.json";
				
				String custId="1106992020";
				String role_1="MERCHANT";
			    String mobileNumber="8888888888";
				
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEvent(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
					verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully updated");
					
				
			
			}   
			
	 @Test(description = "Update Event Without Title",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=8)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC33_UpdateEventWithoutTitle() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventRequest.json";
				
				String title="";
			    String mobileNumber="8888888888";
				
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEvent(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
					verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully updated");
					
				
			
			}   
	 
	 @Test(description = "Update Event Without Description",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=9)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC34_UpdateEventWithoutDescription() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventRequest.json";
				
				String description="";
			    String mobileNumber="8888888888";
				
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEvent(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
					verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully updated");
					
				
			
			}   
	 
	 @Test(description = "Update Event Without Start Time",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=10)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC35_UpdateEventWithoutStartTime() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventRequest.json";
				
				String startTime="";
			    String mobileNumber="8888888888";
				
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEvent(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
					verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully updated");
					
				
			
			}   
	 
	 @Test(description = "Update Event Without End Time",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=11)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC36_UpdateEventWithoutTime() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventRequest.json";
				
				String endTime="";
			    String mobileNumber="8888888888";
				
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEvent(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
					verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully updated");
					
				
			
			}   
	 
	 @Test(description = "Update Event Without Event Metadata",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=12)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC37_UpdateEventWithoutEventMetadata() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventRequest.json";
				
			    String mobileNumber="8888888888";
			    String SOLUTION_SUB_TYPE="";
			    String AGENT_NAME="";
			    String MERCHANT_NAME="";
			    String TL_NAME="";
			    String SOLUTION_REQUESTED="";
			    String SOLUTION_TYPE="";
				
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEvent(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
					verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully updated");
					
				
			
			}   
	 
	 @Test(description = "Update Event Without SOLUTION_TYPE",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=13)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC38_UpdateEventWithoutSolutionType() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventRequest.json";
				
			    String mobileNumber="8888888888";
			    String SOLUTION_TYPE="";
				
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEvent(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
					verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully updated");
					
				
			
			}   
	 
	 
	 @Test(description = "Update Event Without SOLUTION_SUB_TYPE",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=14)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC39_UpdateEventWithoutEventMetadata() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventRequest.json";
				
			    String mobileNumber="8888888888";
			    String SOLUTION_SUB_TYPE="";
	
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEvent(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
					verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully updated");
					
				
			
			}   
	 
	 @Test(description = "Update Event Without Role",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=15)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC40_UpdateEventWithoutRole() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventRequest.json";
				
			    String mobileNumber="8888888888";
			    String role_1="";
				
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEvent(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs400BadRequest(responseObject);
				Assert.assertNotEquals(responseObject.jsonPath().getString("resultInfo.resultMsg"), "Event has been successfully updated");
				Assert.assertEquals(responseObject.jsonPath().getString("resultInfo.resultCode"), "BUSINESS_EXCEPTION");
			
				
			
			}   
	 
	 @Test(description = "Update Event Happy Case",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=16)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC41_UpdateEventHappyCase() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventRequest.json";
				
				String title="New";
			    String mobileNumber="8888888888";
			    String email="<EMAIL>";
			    String role_1="ASSIGNEE";
			    String custId="1106992034";
				
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEvent(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
					verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully updated");
					
				
			
			}   
	 
	 
	 @Test(description = "Update Event with Different SOLUTION_SUB_TYPE",dependsOnMethods = {"TC01_CreateEvent"}, groups = {"Regression" },enabled=true,priority=17)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC42_UpdateEventWithDifferentSoltionSubType() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventRequest.json";
				
				
			    String mobileNumber="8888888888";
			   
			    String SOLUTION_SUB_TYPE="Fastag";
				
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEvent(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "Success");
					verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("message"), "Event has been successfully updated");
					
				
			
			}   
	 
	 @Test(description = "Cancel Event Without EventId", groups = {"Regression" },enabled=true,priority=23)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC43_CancelEventWithoutEventId() {
				
			Map<String, String> queryParams = new HashMap<String, String>();
			String eventId="";
			queryParams.put("eventId", eventId);
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/CancelEventRequest.json";
				
				
				Response responseObject = CRMBaseClassObject.CancelEvent(queryParams, headers,RequestPath);
				

				LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs400BadRequest(responseObject);
					
		} 
	 
	 @Test(description = "Cancel Event which is already cancelled",dependsOnMethods = {"TC04_CancelEvent"}, groups = {"Regression" },enabled=true,priority=21)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC44_CancelAlreadyCancelledEvent() {
				
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("eventId", eventId);
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/CancelEventRequest.json";
				
				
				Response responseObject = CRMBaseClassObject.CancelEvent(queryParams, headers,RequestPath);
				

				LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs400BadRequest(responseObject);
				Assert.assertNotEquals(responseObject.jsonPath().getString("resultInfo.resultMsg"), "Event has been successfully cancelled");
				Assert.assertEquals(responseObject.jsonPath().getString("resultInfo.resultCode"), "BUSINESS_EXCEPTION");
		
					
		} 
	 
	 @Test(description = "Cancel Event which is not created",dependsOnMethods = {"TC04_CancelEvent"}, groups = {"Regression" },enabled=true,priority=22)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC45_CancelNotExistEvent() {
				
			Map<String, String> queryParams = new HashMap<String, String>();
			int num= getRndNumber();
			String eventId= String.valueOf(num);
			queryParams.put("eventId", eventId);
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/CancelEventRequest.json";
				
				
				Response responseObject = CRMBaseClassObject.CancelEvent(queryParams, headers,RequestPath);
				

				LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs400BadRequest(responseObject);
				Assert.assertNotEquals(responseObject.jsonPath().getString("resultInfo.resultMsg"), "Event has been successfully cancelled");
				Assert.assertEquals(responseObject.jsonPath().getString("resultInfo.resultCode"), "BUSINESS_EXCEPTION");
		
					
		} 
	 
	 @Test(description = "Create Event with multiple roles when one role is empty", groups = {"Regression" },enabled=true)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC46_CreateEventWithOneRoleEmpty() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/CreateEventWithMultipleRoles.json";
				
				contextKey=GenerateUniqueContextKey();
				String role_2="";
			
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventType", eventType);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				body.put("role_2", role_2);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.CreateEventWithMultipleRoles(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs400BadRequest(responseObject);
				Assert.assertNotEquals(responseObject.jsonPath().getString("resultInfo.resultMsg"), "Event has been successfully created");
				Assert.assertEquals(responseObject.jsonPath().getString("resultInfo.resultCode"), "BUSINESS_EXCEPTION");
		
			
			} 
	 
	 @Test(description = "Update Event when one role is empty",dependsOnMethods = {"TC03_CreateEventWithMultipleRoles"}, groups = {"Regression" },enabled=true,priority=19)
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC47_UpdateEventWhenOneRoleEmpty() {
				
				Map<String, String> headers = new HashMap<String, String>();
				
				headers.put("Content-Type", "application/json");
				token = CRMBaseClassObject.generateJwtTokenUsingEpochTime(clientId, custId);
				headers.put("jwt", token);
				RequestPath="CRM/v1/UpdateEventWhenOneRoleEmptyRequest.json";
				
				
			    String mobileNumber="8888888888";
			    String role_2="";
			    String custId_2="1106992029";
			   
				
				Map<String, String> body = new HashMap<String, String>();
				body.put("eventId", eventId);
				body.put("contextKey", contextKey);
				body.put("contextValue", contextValue);
				body.put("title", title);
			
				body.put("description", description);
				body.put("startTime", startTime);
			
				body.put("endTime", endTime);
				body.put("custId", custId);
				body.put("custId_2", custId_2);
				body.put("email", email);
				body.put("mobileNumber", mobileNumber);
				body.put("role_1", role_1);
				body.put("SOLUTION_SUB_TYPE", SOLUTION_SUB_TYPE);
				body.put("AGENT_NAME", AGENT_NAME);
				body.put("MERCHANT_NAME", MERCHANT_NAME);
				body.put("TL_NAME", TL_NAME);
				body.put("SOLUTION_REQUESTED", SOLUTION_REQUESTED);
				body.put("role_2", role_2);
				
				
				body.put("SOLUTION_TYPE", SOLUTION_TYPE);
				
				
			
				Response responseObject = CRMBaseClassObject.UpdateEventWhenOneRoleEmpty(headers, body,RequestPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
				verifyResponseCodeAs400BadRequest(responseObject);
				Assert.assertNotEquals(responseObject.jsonPath().getString("resultInfo.resultMsg"), "Event has been successfully updated");
				Assert.assertEquals(responseObject.jsonPath().getString("resultInfo.resultCode"), "BUSINESS_EXCEPTION");
			
				
			
			}   
		
	 
	 
		
	 
	/**
	 * Method to set headers which are used in event creation request
	 * 
	 * @return
	 */
	public Map<String, String> setcommonHeaders() {

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json;charset=utf-8");

		return headers;
	}
	
	/**
	 * Method to generate 10 digit unique number for cancel event
	 * 
	 * @return
	 */
	private static int getRndNumber() {
	    Random random=new Random();
	    int randomNumber=0;
	    boolean loop=true;
	    while(loop) {
	        randomNumber=random.nextInt();
	        if(Integer.toString(randomNumber).length()==10 && !Integer.toString(randomNumber).startsWith("-")) {
	            loop=false;
	        }
	        }
	    return randomNumber;
	}

}