package OCL.IOT;

import Services.DBConnection.DBConnection;
import Services.IOT.*;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.parser.ParseException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class IOTNotification extends BaseMethod
{
    IOTMiddlewareServices iotMiddlewareServicesObject = new IOTMiddlewareServices();

    private static final Logger LOGGER = LogManager.getLogger(IOTNotification.class);
    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";

    String agentvar = "9891497839";
   String sessiontoken = ApplicantToken("7771116065" , "paytm@123");
 // String sessiontoken = AgentSessionToken("8010630022" , "paytm@123");

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLogin() throws Exception {
        sessiontoken = AgentSessionToken("7771116065", "paytm@123");
        LOGGER.info("Agent Token  : " + sessiontoken);
        //establishConnectiontoServer(sessiontoken,5);
        waitForLoad(3000);
       /* TestBase testBase =new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '"+mobileNo+"' and status = '0' and solution_type='update_device_configuration';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes);*/
//        DBConnection.UpdateQueryToCloseLead(mobileNo,solution_type);

    }

   @Test(priority = 0,description = "Checking the Response")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_IOTstatuscode() throws Exception {
        establishConnectiontoServer(sessiontoken,5);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessiontoken);
        headers.put("deviceidentifier", "OnePlus-CPH2487-b0239246dc8dc4ee");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("leadId", "b889ccb1-faae-4d5e-90de-d744ad81be40");
        //Additional edcQrDetails
        body.put("deviceType", "ANDROID");
        body.put("modelName", "A910");
        body.put("oem", "PAX");
        body.put("osType", "ANDROID");
        body.put("serialNo", "JHIU3E23FUTRY");
        body.put("tid", "94053192");
        body.put("httpStatusCode", 0);

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        LOGGER.info("IOT notification with status  : " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);
        Assert.assertEquals(respObj.jsonPath().getString("bindStatus"),"true");

    }

    @Test(priority = 0,description = "Checking if the sound is enabled")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_IOTsound() throws Exception {

        establishConnectiontoServer(sessiontoken,5);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessiontoken);
        headers.put("deviceidentifier", "OnePlus-CPH2487-b0239246dc8dc4ee");


        Map<String,Object> body = new HashMap<String,Object>();
        body.put("leadId","b889ccb1-faae-4d5e-90de-d744ad81be40");
        //Additional edcQrDetails
        body.put("deviceType","ANDROID");
        body.put("modelName","A910");
        body.put("oem","PAX");
        body.put("osType","ANDROID");
        body.put("serialNo","JHIU3E23FUTRY");
        body.put("tid","94053192");
        body.put("httpStatusCode",0);

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.jsonPath().getString("iotFeatureDetails[0].enabled"),"true");
    }


    @Test(priority = 0,description = "Checking if the print is enabled")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_IOTprint() throws Exception {
        establishConnectiontoServer(sessiontoken,5);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessiontoken);
        headers.put("deviceidentifier", "OnePlus-CPH2487-b0239246dc8dc4ee");

        Map<String,Object> body = new HashMap<String,Object>();
        body.put("leadId","b889ccb1-faae-4d5e-90de-d744ad81be40");
        //Additional edcQrDetails
        body.put("deviceType","ANDROID");
        body.put("modelName","A910");
        body.put("oem","PAX");
        body.put("osType","ANDROID");
        body.put("serialNo","JHIU3E23FUTRY");
        body.put("tid","94053192");
        body.put("httpStatusCode",0);

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.jsonPath().getString("iotFeatureDetails[1].enabled"),"true");
    }


    @Test(priority = 0,description = "Checking the POS ID ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_IOTPOS() throws Exception {
        establishConnectiontoServer(sessiontoken,5);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessiontoken);
        headers.put("deviceidentifier", "OnePlus-CPH2487-b0239246dc8dc4ee");

        Map<String,Object> body = new HashMap<String,Object>();
        body.put("leadId","b889ccb1-faae-4d5e-90de-d744ad81be40");
        //Additional edcQrDetails
        body.put("deviceType","ANDROID");
        body.put("modelName","A910");
        body.put("oem","PAX");
        body.put("osType","ANDROID");
        body.put("serialNo","JHIU3E23FUTRY");
        body.put("tid","94053192");
        body.put("httpStatusCode",0);

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.jsonPath().getString("qrMappingDetails.qrCodeName"), "76584");
        LOGGER.info("qrCodeSubText : " + respObj.jsonPath().getString("qrMappingDetails.qrCodeSubText"));

    }

    @Test(priority = 0,description = "Wrong lead ID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_invalidLead() throws Exception {
        establishConnectiontoServer(sessiontoken,5);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessiontoken);
        headers.put("deviceidentifier", "OnePlus-CPH2487-b0239246dc8dc4ee");

        Map<String,Object> body = new HashMap<String,Object>();
        body.put("leadId","abcd123");
        //Additional edcQrDetails
        body.put("deviceType","ANDROID");
        body.put("modelName","A910");
        body.put("oem","PAX");
        body.put("osType","ANDROID");
        body.put("serialNo","JHIU3E23FUTRY");
        body.put("tid","94053192");
        body.put("httpStatusCode",0);

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);
        LOGGER.info("display Message: " + respObj.jsonPath().getString("displayMessage"));

    }

    @Test(priority = 0,description = "Missing lead ID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_MissingLead() throws Exception {
        establishConnectiontoServer(sessiontoken,5);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessiontoken);
        headers.put("deviceidentifier", "OnePlus-CPH2487-b0239246dc8dc4ee");

        Map<String,Object> body = new HashMap<String,Object>();
        body.put("leadId","");
        //Additional edcQrDetails
        body.put("deviceType","ANDROID");
        body.put("modelName","A910");
        body.put("oem","PAX");
        body.put("osType","ANDROID");
        body.put("serialNo","JHIU3E23FUTRY");
        body.put("tid","94053192");
        body.put("httpStatusCode",0);

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);
        LOGGER.info("display Message: " + respObj.jsonPath().getString("displayMessage"));

    }



    @Test(priority = 0,description = "Invalid Device Model name")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_InvalidModelName() throws Exception {
        establishConnectiontoServer(sessiontoken,5);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessiontoken);
        headers.put("deviceidentifier", "OnePlus-CPH2487-b0239246dc8dc4ee");

        Map<String,Object> body = new HashMap<String,Object>();
        body.put("leadId","b889ccb1-faae-4d5e-90de-d744ad81be40");
        //Additional edcQrDetails
        body.put("deviceType","ANDROID");
        body.put("modelName","ABC123");
        body.put("oem","PAX");
        body.put("osType","ANDROID");
        body.put("serialNo","JHIU3E23FUTRY");
        body.put("tid","94053192");
        body.put("httpStatusCode",0);

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);//Bad Request
        LOGGER.info("display Message: " + respObj.jsonPath().getString("displayMessage"));

    }

    @Test(priority = 0,description = "Wrong Device Model name")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_wrongmodelname() throws Exception {
        establishConnectiontoServer(sessiontoken,5);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessiontoken);
        headers.put("deviceidentifier", "OnePlus-CPH2487-b0239246dc8dc4ee");

        Map<String,Object> body = new HashMap<String,Object>();
        body.put("leadId","b889ccb1-faae-4d5e-90de-d744ad81be40");
        //Additional edcQrDetails
        body.put("deviceType","ANDROID");
        body.put("modelName","A50");
        body.put("oem","PAX");
        body.put("osType","ANDROID");
        body.put("serialNo","JHIU3E23FUTRY");
        body.put("tid","94053192");
        body.put("httpStatusCode",0);

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);//Bad Request
        LOGGER.info("display Message: " + respObj.jsonPath().getString("bindConfirmationMessag"));

    }

    @Test(priority = 0,description = "Missing Headers")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_Missingdeviceidentifier() throws Exception {

        establishConnectiontoServer(sessiontoken,5);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessiontoken);
     //   headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");


        Map<String,Object> body = new HashMap<String,Object>();
        body.put("leadId","b889ccb1-faae-4d5e-90de-d744ad81be40");
        //Additional edcQrDetails
        body.put("deviceType","ANDROID");
        body.put("modelName","A910");
        body.put("oem","PAX");
        body.put("osType","ANDROID");
        body.put("serialNo","JHIU3E23FUTRY");
        body.put("tid","94053192");
        body.put("httpStatusCode",0);

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);//Gone
        LOGGER.info("display Message: " + respObj.jsonPath().getString("displayMessage"));
    }

    @Test(priority = 0,description = "Missing session token")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_Missingsessiontoken() throws Exception {
        establishConnectiontoServer(sessiontoken,5);
        Map<String, String> headers = new HashMap<String, String>();
       // headers.put("session_token", sessiontoken);
        headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");


        Map<String,Object> body = new HashMap<String,Object>();
        body.put("leadId","b889ccb1-faae-4d5e-90de-d744ad81be40");
        //Additional edcQrDetails
        body.put("deviceType","ANDROID");
        body.put("modelName","A910");
        body.put("oem","PAX");
        body.put("osType","ANDROID");
        body.put("serialNo","JHIU3E23FUTRY");
        body.put("tid","94053192");
        body.put("httpStatusCode",0);

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.statusCode(), 401);

    }

    @Test(priority = 0,description = "Invalid session token")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_Invalidsessiontoken() throws Exception {
        establishConnectiontoServer(sessiontoken,5);
        Map<String, String> headers = new HashMap<String, String>();
         headers.put("session_token", "2c4a58af-101a-4087-ad37-11a6ef3e1200");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b0239246dc8dc4ee");


        Map<String,Object> body = new HashMap<String,Object>();
        body.put("leadId","b889ccb1-faae-4d5e-90de-d744ad81be40");
        //Additional edcQrDetails
        body.put("deviceType","ANDROID");
        body.put("modelName","A910");
        body.put("oem","PAX");
        body.put("osType","ANDROID");
        body.put("serialNo","JHIU3E23FUTRY");
        body.put("tid","94053192");
        body.put("httpStatusCode",0);

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.statusCode(), 410);

    }

    @Test(priority = 0,description = "Wrong Device Identifier")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_Wrongdeviceidentifier() throws Exception {
        establishConnectiontoServer(sessiontoken,5);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessiontoken);
        headers.put("deviceidentifier", "IVI00-HOM1859-8611");


        Map<String,Object> body = new HashMap<String,Object>();
        body.put("leadId","b889ccb1-faae-4d5e-90de-d744ad81be40");
        //Additional edcQrDetails
        body.put("deviceType","ANDROID");
        body.put("modelName","A910");
        body.put("oem","PAX");
        body.put("osType","ANDROID");
        body.put("serialNo","JHIU3E23FUTRY");
        body.put("tid","94053192");
        body.put("httpStatusCode",0);

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);//Gone
        LOGGER.info("display Message: " + respObj.jsonPath().getString("displayMessage"));
    }

    @Test(priority = 0,description = "The serial number is an invalid one")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_Invalidserialnumber() throws Exception {
        establishConnectiontoServer(sessiontoken,5);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessiontoken);
        headers.put("deviceidentifier", "OnePlus-CPH2487-b0239246dc8dc4ee");


        Map<String,Object> body = new HashMap<String,Object>();
        body.put("leadId","b889ccb1-faae-4d5e-90de-d744ad81be40");
        //Additional edcQrDetails
        body.put("deviceType","ANDROID");
        body.put("modelName","A910");
        body.put("oem","PAX");
        body.put("osType","ANDROID");
        body.put("serialNo","abc");
        body.put("tid","94053192");
        body.put("httpStatusCode",0);

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);
        Assert.assertEquals(respObj.jsonPath().getString("bindStatus"),"false");
        Assert.assertEquals(respObj.jsonPath().getString("iotFeatureDetails[0].enabled"),"false");//sound
        Assert.assertEquals(respObj.jsonPath().getString("iotFeatureDetails[1].enabled"),"false");//print
        LOGGER.info("bind Confirmation Message: "+respObj.jsonPath().getString("bindConfirmationMessage"));
        LOGGER.info("QR status: " + respObj.jsonPath().getString("qrMappingDetails.qrCodeName"));
    }
    @Test(priority = 0,description = "The lead ID is not mentioned")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014_NoleadID() throws Exception {
        establishConnectiontoServer(sessiontoken,5);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessiontoken);
        headers.put("deviceidentifier", "OnePlus-CPH2487-b0239246dc8dc4ee");

        Map<String,Object> body = new HashMap<String,Object>();
        //Additional edcQrDetails
        body.put("deviceType","ANDROID");
        body.put("modelName","A910");
        body.put("oem","PAX");
        body.put("osType","ANDROID");
        body.put("serialNo","JHIU3E23FUTRY");
        body.put("tid","94053192");
        body.put("httpStatusCode",0);

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);
        LOGGER.info("Display Message: "+respObj.jsonPath().getString("displayMessage"));

    }

    @Test(priority = 0,description = "no aditional QR details is provided")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015_NoQRdetails() throws Exception {
        establishConnectiontoServer(sessiontoken,5);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessiontoken);
        headers.put("deviceidentifier", "OnePlus-CPH2487-b0239246dc8dc4ee");

        Map<String,Object> body = new HashMap<String,Object>();
        body.put("leadId","b889ccb1-faae-4d5e-90de-d744ad81be40");

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);
        LOGGER.info("Display message: " + respObj.jsonPath().getString("displayMessage"));
    }

    @Test(priority = 0,description = "Request body is empty")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016_EmptyRequest() throws Exception {

        establishConnectiontoServer(sessiontoken,5);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessiontoken);
        headers.put("deviceidentifier", "OnePlus-CPH2487-b0239246dc8dc4ee");

        Map<String,Object> body = new HashMap<String,Object>();
        body.put("leadId", "");
        //Additional edcQrDetails
        body.put("deviceType", "");
        body.put("modelName", "");
        body.put("oem", "");
        body.put("osType", "");
        body.put("serialNo", "");
        body.put("tid", "");
        body.put("httpStatusCode", 0);

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);
        LOGGER.info("display message: " + respObj.jsonPath().getString("displayMessage"));
    }

    @Test(priority = 0,description = "Request body is missing")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017_missingRequestBody() throws Exception {
        establishConnectiontoServer(sessiontoken,5);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessiontoken);
        headers.put("deviceidentifier", "OnePlus-CPH2487-b0239246dc8dc4ee");

        Map<String,Object> body = new HashMap<String,Object>();

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);
        LOGGER.info("display message: " + respObj.jsonPath().getString("displayMessage"));
    }

    @Test(priority = 0,description = "Request headers is empty")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_018_missingRequestHeaders() throws Exception {
        establishConnectiontoServer(sessiontoken,5);

        Map<String, String> headers = new HashMap<String, String>();

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("leadId", "b889ccb1-faae-4d5e-90de-d744ad81be40");
        //Additional edcQrDetails
        body.put("deviceType", "ANDROID");
        body.put("modelName", "A910");
        body.put("oem", "PAX");
        body.put("osType", "ANDROID");
        body.put("serialNo", "JHIU3E23FUTRY");
        body.put("tid", "94053192");
        body.put("httpStatusCode", 0);

        Response respObj = iotMiddlewareServicesObject.iotnotificationMethod(headers, body);
        Assert.assertEquals(respObj.statusCode(), 401);


    }

}