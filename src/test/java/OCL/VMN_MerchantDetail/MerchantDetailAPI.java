package OCL.VMN_MerchantDetail;

import Request.KYB.GetAddressInKyb;
import Request.MerchantService.v1.MerchantCommonOnboard.AudioDetails;
import Services.MechantService.MiddlewareServices;
import Services.VMN.GetMerchantDetails;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class MerchantDetailAPI extends BaseMethod
{
    private static final Logger LOGGER = LogManager.getLogger(OCL.VMN_MerchantDetail.MerchantDetailAPI.class);
    GetMerchantDetails GetMerchantDetailsPathobj = new GetMerchantDetails();
    Response response;

    public static String content_type="application/json; charset=UTF-8";




    @Test(priority = 1, description = "Fetch Merchant details Using PgMid", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_FetchMerchantDetails() {

        Map<String, String> body = new HashMap<String, String>();
        body.put("pg_mid", "fBYeIx47798245244793");

        Map<String,String> headers= new HashMap<>();
        headers.put("Content-Type","application/json");

        Map<String,String> params= new HashMap<>();

        String requestpath = "VMN/fact_vmn_srs/search/_searchRequest.json";

        response = GetMerchantDetailsPathobj.MerchantDetails(requestpath,headers, body, params);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }



}
