package OCL.BrandEMI;

import Request.BrandEMI.*;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.ssh.SSHConnection;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;

import java.sql.Connection;
import java.util.Map;

public class BrandEMIBaseAPi extends BaseMethod {
	
	
	 private static final Logger LOGGER = LogManager.getLogger(BrandEMIBaseAPi.class);

	    oAuthServices oAuthServicesObject = new oAuthServices();
	    static Connection connection = null;
	    static SSHConnection sshConnection = new SSHConnection();
	    public static String DbName = "sprint33_2";
	    
	    
	    
	    /*
	     * Method to create User
	     * @param headers
	     * @param body
	     * @return
	     */
	    
	    
	    public Response CreateUser(Map<String, String> headers, Map<String, String> body,String RequestPath) {

	        CreateUser createUserObject = new CreateUser(RequestPath);

	        for (Map.Entry m : headers.entrySet()) {
	        	createUserObject.setHeader(m.getKey().toString(), m.getValue().toString());
	        }

	        for (Map.Entry m : body.entrySet()) {
	        	createUserObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
	        }

	        Response createUserObjectResponse = createUserObject.callAPI();

	        return createUserObjectResponse;
	    }
	    
	    public Response FetchCustId(Map<String, String> headers, Map<String, String> body,String RequestPath) {

	        UserDetails createUserObject = new UserDetails(RequestPath);

	        for (Map.Entry m : headers.entrySet()) {
	        	createUserObject.setHeader(m.getKey().toString(), m.getValue().toString());
	        }

	        for (Map.Entry m : body.entrySet()) {
	        	createUserObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
	        }

	        Response createUserObjectResponse = createUserObject.callAPI();

	        return createUserObjectResponse;
	    }
	    
	    public Response KybCompanyOnboard(Map<String, String> headers, Map<String, String> body,String RequestPath) {

	        KybCompanyOnboard KybCompanyOnboardObject = new KybCompanyOnboard(RequestPath);

	        for (Map.Entry m : headers.entrySet()) {
	        	KybCompanyOnboardObject.setHeader(m.getKey().toString(), m.getValue().toString());
	        }

	        for (Map.Entry m : body.entrySet()) {
	        	KybCompanyOnboardObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
	        }

	        Response KybCompanyOnboardObjectResponse = KybCompanyOnboardObject.callAPI();

	        return KybCompanyOnboardObjectResponse;
	    }
	    
	    public Response MerchantCreationonPG(Map<String, String> headers, Map<String, String> body,String RequestPath) {

	    	CreateMerchantOnPG MerchantCreationonPGObject = new CreateMerchantOnPG(RequestPath);

	        for (Map.Entry m : headers.entrySet()) {
	        	MerchantCreationonPGObject.setHeader(m.getKey().toString(), m.getValue().toString());
	        }

	        for (Map.Entry m : body.entrySet()) {
	        	MerchantCreationonPGObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
	        }

	        Response MerchantCreationonPGObjectResponse = MerchantCreationonPGObject.callAPI();

	        return MerchantCreationonPGObjectResponse;
	    }
	    
	    public Response EditMerchantOnPG(Map<String, String> headers, Map<String, String> body,String RequestPath) {

	    	EditMerchantOnPG EditMerchantOnPGObject = new EditMerchantOnPG(RequestPath);

	        for (Map.Entry m : headers.entrySet()) {
	        	EditMerchantOnPGObject.setHeader(m.getKey().toString(), m.getValue().toString());
	        }

	        for (Map.Entry m : body.entrySet()) {
	        	EditMerchantOnPGObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
	        }

	        Response EditMerchantOnPGObjectResponse = EditMerchantOnPGObject.callAPI();

	        return EditMerchantOnPGObjectResponse;
	    }
	    
	    
	    public Response CheckCraetedMerchant(Map<String, String> headers,String RequestPath, String Cust_id) {

	    	GetMIDUsingCustID CheckCraetedMerchantObject = new GetMIDUsingCustID(RequestPath,Cust_id);

	        for (Map.Entry m : headers.entrySet()) {
	        	CheckCraetedMerchantObject.setHeader(m.getKey().toString(), m.getValue().toString());
	        }
	        


	        Response CheckCraetedMerchantObjectResponse = CheckCraetedMerchantObject.callAPI();

	        return CheckCraetedMerchantObjectResponse;
	    }
	    
	    
	    /**
	     * Verify  Response Code as 200 OK
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs200OK(Response responseObject) {

	        LOGGER.info("Status Code : " +responseObject.getStatusCode());

	        Assert.assertEquals(responseObject.getStatusCode(),200);

	    }
	    
	    /**
	     * Verify  Response Code as 400 Bad Request
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs400BadRequest(Response responseObject) {

	        LOGGER.info("Status Code : " +responseObject.getStatusCode());

	        Assert.assertEquals(responseObject.getStatusCode(),400);

	    }
	    /**
	     * Verify  Response Code as 403 Bad Request
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs403BadRequest(Response responseObject) {

	        LOGGER.info("Status Code : " +responseObject.getStatusCode());

	        Assert.assertEquals(responseObject.getStatusCode(),403);

	    }
	    
	    
	    /**
	     * Verify  Response Code as 401 Unauthorized
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs401Unauthorized(Response responseObject) {

	    	
	        LOGGER.info("Status Code : " +responseObject.getStatusCode());
	        Assert.assertEquals(responseObject.getStatusCode(),401);

	    }
	    
	    /**
	     * Verify  Response Code as 415 Unsupported Media Type
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs415UnsupportedMediaType(Response responseObject) {

	    	
	        LOGGER.info("Status Code : " +responseObject.getStatusCode());
	        Assert.assertEquals(responseObject.getStatusCode(),415);

	    }
	    

}
