package OCL.BrandEMI;

import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.openqa.selenium.WebDriver;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;



public class BrandEMITest extends BrandEMIBaseAPi{
	
	
	private static final Logger LOGGER = LogManager.getLogger(BrandEMITest.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	
	protected WebDriver driver;
	
	
	BrandEMIBaseAPi BrandEMIBaseClassObject= new BrandEMIBaseAPi();
	BaseMethod BaseMethodObject=new BaseMethod();
	
	
	//Generate Unique 10 Digit Mobile Number
public String GenerateMobileNumber()
{
	   String str=GenerateRandomDigit9();
	   System.out.println(str);
	   String MobileNumber= "5"+str;
	   System.out.println(MobileNumber);
	   return MobileNumber;

}	

   //Generate Random 9 Digit Number
static String GenerateRandomDigit9() { 
    String numbers = "**********"; 
    // create a super set of all characters 
    String allCharacters = numbers; 
    // initialize a string to hold result 
    StringBuffer randomString = new StringBuffer(); 
    // loop for 10 times 
    for (int i = 0; i < 9; i++) { 
      // generate a random number between 0 and 9
      int randomIndex = (int)(Math.random() * allCharacters.length()); 
      // retrieve character at index and add it to result 
      randomString.append(allCharacters.charAt(randomIndex)); 
    } 
    return randomString.toString(); 
  }

//Generate Random 10 Digit RequestID for Create Merchant
static String GenerateRandomDigit10() { 
   String numbers = "**********"; 
   // create a super set of all characters 
   String allCharacters = numbers; 
   // initialize a string to hold result 
   StringBuffer randomString = new StringBuffer(); 
   // loop for 10 times 
   for (int i = 0; i < 10; i++) { 
     // generate a random number between 0 and 9
     int randomIndex = (int)(Math.random() * allCharacters.length()); 
     // retrieve character at index and add it to result 
     randomString.append(allCharacters.charAt(randomIndex)); 
   } 
   return randomString.toString(); 
 } 

//Generate Random 13 Digit Account No. for Create Merchant
static String GenerateAccountNo() { 
 String numbers = "**********"; 
 // create a super set of all characters 
 String allCharacters = numbers; 
 // initialize a string to hold result 
 StringBuffer randomString = new StringBuffer(); 
 // loop for 10 times 
 for (int i = 0; i < 13; i++) { 
   // generate a random number between 0 and 9
   int randomIndex = (int)(Math.random() * allCharacters.length()); 
   // retrieve character at index and add it to result 
   randomString.append(allCharacters.charAt(randomIndex)); 
 } 
 return randomString.toString(); 
} 

  //Generate Random 4 Digit number for Pan Card
static String GenerateRandomDigit4() { 
    String numbers = "**********"; 
    // create a super set of all characters 
    String allCharacters = numbers; 
    // initialize a string to hold result 
    StringBuffer randomString = new StringBuffer(); 
    // loop for 10 times 
    for (int i = 0; i < 4; i++) { 
      // generate a random number between 0 and 9  
      int randomIndex = (int)(Math.random() * allCharacters.length()); 
      // retrieve character at index and add it to result 
      randomString.append(allCharacters.charAt(randomIndex)); 
    } 
    return randomString.toString(); 
  } 
//Generate Unique 4 Digit Random Number
public String GenerateRandomNumber()
{
	   String str = GenerateRandomDigit4();
	   String RandomNumber4= str;
	   System.out.println(RandomNumber4);
	   return RandomNumber4;

}	

    //Generate 3 Character Random String
static String usingMath() { 
    String alphabetsInUpperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"; 
    
   
    // create a super set of all characters 
    String allCharacters = alphabetsInUpperCase; 
    // initialize a string to hold result 
    StringBuffer randomString = new StringBuffer(); 
    // loop for 3 times 
    for (int i = 0; i < 3; i++) { 
      // generate a random number of all characters 
      int randomIndex = (int)(Math.random() * allCharacters.length()); 
      // retrieve character at index and add it to result 
      randomString.append(allCharacters.charAt(randomIndex)); 
    } 
    return randomString.toString(); 
  } 

//Generate Unique Pan Number
public String GeneratePanNumber() {
	
	String First3Letter= usingMath();
	String RandomNumberforPan=GenerateRandomNumber();
	String UniquePanNumber= First3Letter+"PW"+RandomNumberforPan+"H";
	return UniquePanNumber;
	
	
	
}

//Generate Current Date Time Stamp
public String GenerateCurrentDateTime() {
	String timeStamp = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(new Date());
    return timeStamp;
}




String mobile=GenerateMobileNumber();
String loginPassword= "Paytm@123";
String RequestPath="";
String code="";
String fetch_strategy="phone";
String Cust_id="";
String KybJWTtoken="Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE1MTgwMDA4MTAwMDAiLCJjdXN0X2lkIjoiMTEwNzE5OTQwNyIsImNsaWVudF9pZCI6InRlc3RjbGllbnQifQ.J69gTrP1Ct6-C0rBuBUKl0Gsvo/4lDXBojYOzwhJN2o";
String companyFlags="APPLICANT";
String companyType="PROPRIETORSHIP";
String name="TOUCH WOOD LIMITED";
String entityType="Company";
String nameOnPan="TOUCH WOOD LIMITED";
String docCode="pan";
String docValue=GeneratePanNumber();
String submittedAs="Poi";
String placeOfIssue="00";
String docIssueDate=GenerateCurrentDateTime();
String role="Company";
String businessId="";
String CREATED_BY="sobeer_sales";
String ACTION="Submit for Approval";
String REQUEST_ID=GenerateRandomDigit10();
String USER_NAME=REQUEST_ID;
String ACCOUNT_FOR="unifiedMerchantPanel";
String SOURCE_ID="OE";
String MERCHANT_TYPE="UNLIMITED_SD";
String OFFLINE_ENABLED="FALSE";
String PPI_LIMITED_MERCHANT="4";
String KYB_ID="";
String BUSINESS_NAME="Rohan";
String BUSINESS_TYPE="PROPRIETORSHIP";
String CALLBACK_URL_ENABLED="TRUE";
String CUSTOM="SYSTEM GENERATED";
String MERCHANT_NAME="PRATEEK SRIVASTAVA";
String CURRENCY="INR";
String REFUND_TO_BANK_ENABLED="TRUE";
String STORE_CARD_DETAILS="NO";
String ADD_MONEY_ENABLE="TRUE";
String CHECKSUM_ENABLED="TRUE";
String NUMBER_OF_RETRY="1";
String CATEGORY="BFSI";
String SUB_CATEGORY="Insurance";
String INDUSTRY_TYPE="Retail";
String WALLET_RECHARGE_OPT="MANUAL_RECHARGE";
String PROFILE_ID="1";
String EMAIL_ALERT="TRUE";
String CONVENIENCE_FEE_TYPE="1";
String VALID_FROM="12/20/2021";
String VALID_TO="07/25/2024";
String MULTI_SUPPORT="YES";
String HOW_MANY="3";
String OCP="TRUE";
String REQUEST_NAME="DP2Web";
String FIRST_NAME="Rohan";
String LAST_NAME="Saxena";
String MOBILE_NUMBER="";
String PHONE_NUMBER="";
String MerchUniqRef="X";
String ACCOUNT_PRIMARY="FALSE";
String CAN_EDIT_PMOBILE="TRUE";
String IS_SUB_USER="FALSE";
String ADDRESS1="B-265";
String ADDRESS2="Brij Vihar";
String ADDRESS3="Ghaziabad";
String COUNTRY="India";
String STATE="Uttar Pradesh";
String CITY="Noida";
String PIN="201011";
String SAME_AS_BUSINESS_ADDR="FALSE";
String COMMUNICATION_ADDRESS1="B-265";
String COMMUNICATION_ADDRESS2= "Brij Vihar";
String COMMUNICATION_ADDRESS3= "Ghaziabad";
String COMMUNICATION_COUNTRY= "India";
String COMMUNICATION_STATE= "Uttar Pradesh";
String COMMUNICATION_CITY= "Noida";
String COMMUNICATION_PIN="201011";
String COMMUNICATION_LATITUDE= "28.6740548";
String COMMUNICATION_LONGITUDE= "77.3340577";
String KYC_BANK_NAME= "ALLAHABAD";
String KYC_BANK_ACCOUNT_HOLDER_NAME= "Adsd";
String KYC_BANK_ACCOUNT_NO= GenerateAccountNo();
String KYC_BUSINESS_PAN_NO= "";
String KYC_AUTHORIZED_SIGNATORY_PAN_NO= "";
String KYC_BUSINESS_IFSC_NO= "ALLA0123456";
String KYC_AUTHORIZED_SIGNATORY_NAME= "Rohan G1627034886877";
String KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO= "**********";
String COMM_STAT_SELECT= "1";
String EMAIL_MERCHANT= "TRUE";
String EMAIL_CONSUMER= "FALSE";
String REQUEST_TYPE_NAME= "QR_ORDER,SELF_DECLARED_MERCHANT,EDC";
String WEBSITE_NAME= "paytm";
String SIZE_OF_KEY="16";
String SMS_MERCHANT="TRUE";
String PAYOUT_DAYS="1";
String ONLINE_SETTLEMENT= "FALSE";
String FLAG_MERCHANT= "FALSE";
String BW_ENABLED= "TRUE";

String TRANSFER_MODE= "M2B";
String AUTO = "TRUE";
String TRIGGER_MODE = "TIME_INTERVAL";
String TRIGGER_VALUE="1";

String API_DISABLED= "TRUE";
String MERCHANT_INDUSTRY_TYPE= "BIG";
String P2M_ENABLED= "TRUE";
String OB_CHANNEL= "GG_APP";
String SOLUTION_TYPE= "OFFLINE";

String  WEBSITE_NAME1= "dp2wen";
String  REQUEST_URL1= "https://secure.paytm.in/MerchantSite/bankResponse";
String  RESPONSE_URL1= "https://secure.paytm.in/oltp-web/smsInvoiceAddMoney/displayPaymentStatus?ORDER_ID=m1";
String  PEON_URL1= "http://dp2web.com";
String  IMAGE_NAME1= "paytm_log";


String  WEBSITE_NAME2="paytm";
String  REQUEST_URL2= "https://www.paytm.com";
String RESPONSE_URL2= "https://cart-beta.paytm.com/payment/status";
String IMAGE_NAME2= "paytm_log";
 
String  WEBSITE_NAME3="retail";
String  REQUEST_URL3= "https://www.paytm.com";
String RESPONSE_URL3= "https://cart-beta.paytm.com/payment/status";
String IMAGE_NAME3= "paytm_log";

String DETAILED_LIST="CARD";
String VELOCITY_TYPE="PER_MID";
String MAX_AMT_PER_DAY="100";
String MAX_AMT_PER_MONTH="100";
String ACTION_ConfigureMerchnatComm="EDIT";
String FEE_TYPE ="simple";
String PERCENT_COMMISSION="1";
String COMMISSION_TYPE_BOTH="FALSE";

String TXN_TYPE="Payments";
String PAY_MODE="NB";

String CookieEditMerchantOnPG="JSESSIONID=314BDC407AA0A84DDB37963ABB45EC01.adminjvm1; JSESSIONID=D739C98D9B0B5EF5F82B944DF4DBFAF2.adminjvm1; SESSION=d6f5d8e3-0f63-4997-9874-f33a35dd9cac";
String TYPE="BILLING";
String MID="";

@SuppressWarnings("static-access")
String xssotoken= BaseMethodObject.ApplicantToken("9891497839", "paytm@123");

@BeforeClass()
public void intitializeInputData() throws IOException {

	LOGGER.info(" Before Suite Method for User Creation ");
	//commonHeaders = setcommonHeaders();

}


@Test(description = "Create User", groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC01_CreateUser() {
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		
		RequestPath="oAuth/oAuthWormhole/CreateUser/CreateUserRequest.json";
	
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("mobile", mobile);
		body.put("loginPassword", loginPassword);
		
	
		Response responseObject = BrandEMIBaseClassObject.CreateUser(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
			verifyResponseCodeAs200OK(responseObject);
			code=responseObject.jsonPath().getString("code");
			
			
		
	
	}   
	
	
@Test(description = "Fetch User_id",dependsOnMethods = {"TC01_CreateUser"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC02_FetchUserId() {
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		
		RequestPath="oAuth/oAuthWormhole/UserDetails/UserDetailsRequest.json";
		
		
	    String userData= mobile;
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("userData", userData);
		body.put("fetch_strategy", fetch_strategy);
		
	
		Response responseObject = BrandEMIBaseClassObject.FetchCustId(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
			verifyResponseCodeAs200OK(responseObject);
			Assert.assertEquals(responseObject.jsonPath().getString("phone"), mobile);
			Cust_id=responseObject.jsonPath().getString("userId");
	
	}   





@Test(description = "Company Onboard Without CustId", groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC03_CompanyOnboardWithoutCustId() {

	String KYBToken = null;
	try {
		KYBToken = generateTokenKYB();
		System.out.println("KYBTOKEN: " +KYBToken);
	} catch (Exception e) {
		// Handle the exception here
		e.printStackTrace();
	}
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("x-jwt-token",KYBToken);
		
		RequestPath="BrandEMI/CompanyOnboardRequest.json";
	
	    String parentCustId="";
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("parentCustId", parentCustId);
		body.put("companyFlags", companyFlags);
		body.put("companyType", companyType);
		body.put("name", name);
		body.put("entityType", entityType);
		body.put("nameOnPan", nameOnPan);
		body.put("docCode", docCode);
		body.put("docValue", docValue);
		body.put("submittedAs", submittedAs);
		body.put("placeOfIssue", placeOfIssue);
		body.put("docIssueDate", docIssueDate);
		body.put("role", role);
		
	
		Response responseObject = BrandEMIBaseClassObject.KybCompanyOnboard(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("error.errorMsg"), "Bad Request");
			verifyResponseCodeAs200OK(responseObject);

	
	} 

@Test(description = "Company Onboard Without PAN Card",dependsOnMethods = {"TC02_FetchUserId"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC04_CompanyOnboardWithoutPan() {

	String KYBToken = null;
	try {
		KYBToken = generateTokenKYB();
		System.out.println("KYBTOKEN: " +KYBToken);
	} catch (Exception e) {
		// Handle the exception here
		e.printStackTrace();
	}
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("x-jwt-token",KYBToken);
		
		RequestPath="BrandEMI/CompanyOnboardRequest.json";
	
	    String parentCustId=Cust_id;
	    String docValue="";
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("parentCustId", parentCustId);
		body.put("companyFlags", companyFlags);
		body.put("companyType", companyType);
		body.put("name", name);
		body.put("entityType", entityType);
		body.put("nameOnPan", nameOnPan);
		body.put("docCode", docCode);
		body.put("docValue", docValue);
		body.put("submittedAs", submittedAs);
		body.put("placeOfIssue", placeOfIssue);
		body.put("docIssueDate", docIssueDate);
		body.put("role", role);
		
	
		Response responseObject = BrandEMIBaseClassObject.KybCompanyOnboard(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("error.errorMsg"), "Document value is missing");
			verifyResponseCodeAs200OK(responseObject);

	
	} 


@Test(description = "Company Onboard Without Doc Code",dependsOnMethods = {"TC02_FetchUserId"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC05_CompanyOnboardWithoutDoc() {

	String KYBToken = null;
	try {
		KYBToken = generateTokenKYB();
		System.out.println("KYBTOKEN: " +KYBToken);
	} catch (Exception e) {
		// Handle the exception here
		e.printStackTrace();
	}
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("x-jwt-token",KYBToken);
		
		RequestPath="BrandEMI/CompanyOnboardRequest.json";
	
	    String parentCustId=Cust_id;
	    String docCode="";
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("parentCustId", parentCustId);
		body.put("companyFlags", companyFlags);
		body.put("companyType", companyType);
		body.put("name", name);
		body.put("entityType", entityType);
		body.put("nameOnPan", nameOnPan);
		body.put("docCode", docCode);
		body.put("docValue", docValue);
		body.put("submittedAs", submittedAs);
		body.put("placeOfIssue", placeOfIssue);
		body.put("docIssueDate", docIssueDate);
		body.put("role", role);
		
	
		Response responseObject = BrandEMIBaseClassObject.KybCompanyOnboard(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("error.errorMsg"), "Mandatory data missing from company onboarding request");
			verifyResponseCodeAs200OK(responseObject);

	
	} 

@Test(description = "Company Onboard Without Role",dependsOnMethods = {"TC02_FetchUserId"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC06_CompanyOnboardWithoutRole() {

	String KYBToken = null;
	try {
		KYBToken = generateTokenKYB();
		System.out.println("KYBTOKEN: " +KYBToken);
	} catch (Exception e) {
		// Handle the exception here
		e.printStackTrace();
	}
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("x-jwt-token",KYBToken);
		
		RequestPath="BrandEMI/CompanyOnboardRequest.json";
	
	    String parentCustId=Cust_id;
	    String role="";
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("parentCustId", parentCustId);
		body.put("companyFlags", companyFlags);
		body.put("companyType", companyType);
		body.put("name", name);
		body.put("entityType", entityType);
		body.put("nameOnPan", nameOnPan);
		body.put("docCode", docCode);
		body.put("docValue", docValue);
		body.put("submittedAs", submittedAs);
		body.put("placeOfIssue", placeOfIssue);
		body.put("docIssueDate", docIssueDate);
		body.put("role", role);
		
	
		Response responseObject = BrandEMIBaseClassObject.KybCompanyOnboard(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("error.errorMsg"), "Requested Role does not exist");
			verifyResponseCodeAs200OK(responseObject);

	
	} 

@Test(description = "Company Onboard With Invalid Pan",dependsOnMethods = {"TC02_FetchUserId"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC07_CompanyOnboardWithInvalidPan() {

	String KYBToken = null;
	try {
		KYBToken = generateTokenKYB();
		System.out.println("KYBTOKEN: " +KYBToken);
	} catch (Exception e) {
		// Handle the exception here
		e.printStackTrace();
	}
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("x-jwt-token",KYBToken);
		
		RequestPath="BrandEMI/CompanyOnboardRequest.json";
	
	    String parentCustId=Cust_id;
	    String docValue="**********";
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("parentCustId", parentCustId);
		body.put("companyFlags", companyFlags);
		body.put("companyType", companyType);
		body.put("name", name);
		body.put("entityType", entityType);
		body.put("nameOnPan", nameOnPan);
		body.put("docCode", docCode);
		body.put("docValue", docValue);
		body.put("submittedAs", submittedAs);
		body.put("placeOfIssue", placeOfIssue);
		body.put("docIssueDate", docIssueDate);
		body.put("role", role);
		
	
		Response responseObject = BrandEMIBaseClassObject.KybCompanyOnboard(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("error.errorMsg"), "Document value is missing or Invalid docCode: PAN");
			verifyResponseCodeAs200OK(responseObject);

	
	} 

@Test(description = "Company Onboard Without Company Type",dependsOnMethods = {"TC02_FetchUserId"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC08_CompanyOnboardWithoutCompanyType() {

	String KYBToken = null;
	try {
		KYBToken = generateTokenKYB();
		System.out.println("KYBTOKEN: " +KYBToken);
	} catch (Exception e) {
		// Handle the exception here
		e.printStackTrace();
	}
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("x-jwt-token",KYBToken);
		
		RequestPath="BrandEMI/CompanyOnboardRequest.json";
	
	    String parentCustId=Cust_id;
	    String companyType="";
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("parentCustId", parentCustId);
		body.put("companyFlags", companyFlags);
		body.put("companyType", companyType);
		body.put("name", name);
		body.put("entityType", entityType);
		body.put("nameOnPan", nameOnPan);
		body.put("docCode", docCode);
		body.put("docValue", docValue);
		body.put("submittedAs", submittedAs);
		body.put("placeOfIssue", placeOfIssue);
		body.put("docIssueDate", docIssueDate);
		body.put("role", role);
		
	
		Response responseObject = BrandEMIBaseClassObject.KybCompanyOnboard(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("error.errorMsg"), "Invalid value for company type");
			verifyResponseCodeAs200OK(responseObject);

	
	} 

@Test(description = "Company Onboard Without Company Flag",dependsOnMethods = {"TC02_FetchUserId"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC09_CompanyOnboardWithoutCompanyFlag() {

	String KYBToken = null;
	try {
		KYBToken = generateTokenKYB();
		System.out.println("KYBTOKEN: " +KYBToken);
	} catch (Exception e) {
		// Handle the exception here
		e.printStackTrace();
	}
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("x-jwt-token",KYBToken);
		
		RequestPath="BrandEMI/CompanyOnboardRequest.json";
	
	    String parentCustId=Cust_id;
	    String companyFlags="";
	    String docValue="";
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("parentCustId", parentCustId);
		body.put("companyFlags", companyFlags);
		body.put("companyType", companyType);
		body.put("name", name);
		body.put("entityType", entityType);
		body.put("nameOnPan", nameOnPan);
		body.put("docCode", docCode);
		body.put("docValue", docValue);
		body.put("submittedAs", submittedAs);
		body.put("placeOfIssue", placeOfIssue);
		body.put("docIssueDate", docIssueDate);
		body.put("role", role);
		
	
		Response responseObject = BrandEMIBaseClassObject.KybCompanyOnboard(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("error.errorMsg"), "Document value is missing");
			verifyResponseCodeAs200OK(responseObject);

	
	} 

@Test(description = "Company Onboard Without Entity Type",dependsOnMethods = {"TC02_FetchUserId"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC10_CompanyOnboardWithoutEntityType() {

	String KYBToken = null;
	try {
		KYBToken = generateTokenKYB();
		System.out.println("KYBTOKEN: " +KYBToken);
	} catch (Exception e) {
		// Handle the exception here
		e.printStackTrace();
	}
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("x-jwt-token",KYBToken);
		
		RequestPath="BrandEMI/CompanyOnboardRequest.json";
	
	    String parentCustId=Cust_id;
	    String entityType="";
	    String docValue="";
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("parentCustId", parentCustId);
		body.put("companyFlags", companyFlags);
		body.put("companyType", companyType);
		body.put("name", name);
		body.put("entityType", entityType);
		body.put("nameOnPan", nameOnPan);
		body.put("docCode", docCode);
		body.put("docValue", docValue);
		body.put("submittedAs", submittedAs);
		body.put("placeOfIssue", placeOfIssue);
		body.put("docIssueDate", docIssueDate);
		body.put("role", role);
		
	
		Response responseObject = BrandEMIBaseClassObject.KybCompanyOnboard(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("error.errorMsg"), "Document value is missing");
			verifyResponseCodeAs200OK(responseObject);

	
	} 

@Test(description = "Company Onboard Without PAN Name",dependsOnMethods = {"TC02_FetchUserId"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC11_CompanyOnboardWithoutPANName() {


	String KYBToken = null;
	try {
		KYBToken = generateTokenKYB();
		System.out.println("KYBTOKEN: " +KYBToken);
	} catch (Exception e) {
		// Handle the exception here
		e.printStackTrace();
	}
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("x-jwt-token",KYBToken);
		
		RequestPath="BrandEMI/CompanyOnboardRequest.json";
	
	    String parentCustId=Cust_id;
	    String nameOnPan="";
	    String docValue="";
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("parentCustId", parentCustId);
		body.put("companyFlags", companyFlags);
		body.put("companyType", companyType);
		body.put("name", name);
		body.put("entityType", entityType);
		body.put("nameOnPan", nameOnPan);
		body.put("docCode", docCode);
		body.put("docValue", docValue);
		body.put("submittedAs", submittedAs);
		body.put("placeOfIssue", placeOfIssue);
		body.put("docIssueDate", docIssueDate);
		body.put("role", role);
		
	
		Response responseObject = BrandEMIBaseClassObject.KybCompanyOnboard(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("error.errorMsg"), "Document value is missing");
			verifyResponseCodeAs200OK(responseObject);

	
	} 



@Test(description = "Company Onboard",dependsOnMethods = {"TC02_FetchUserId"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC12_CompanyOnboard() {

	String KYBToken = null;
	try {
		KYBToken = generateTokenKYB();
		System.out.println("KYBTOKEN: " +KYBToken);
	} catch (Exception e) {
		// Handle the exception here
		e.printStackTrace();
	}
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("x-jwt-token",KYBToken);
		
		RequestPath="BrandEMI/CompanyOnboardRequest.json";
	
	    String parentCustId=Cust_id;
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("parentCustId", parentCustId);
		body.put("companyFlags", companyFlags);
		body.put("companyType", companyType);
		body.put("name", name);
		body.put("entityType", entityType);
		body.put("nameOnPan", nameOnPan);
		body.put("docCode", docCode);
		body.put("docValue", docValue);
		body.put("submittedAs", submittedAs);
		body.put("placeOfIssue", placeOfIssue);
		body.put("docIssueDate", docIssueDate);
		body.put("role", role);
		
	
		Response responseObject = BrandEMIBaseClassObject.KybCompanyOnboard(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("statusMessage"), "SUCCESS");
			verifyResponseCodeAs200OK(responseObject);
			businessId=responseObject.jsonPath().getString("businessId");
			
	
	} 


@Test(description = "Create Merchant on PG without RequestID",dependsOnMethods = {"TC02_FetchUserId","TC12_CompanyOnboard"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC13_CreateMerchantonPGWithoutRequestID() {
	String KYBToken = null;
	try {
		KYBToken = generateTokenKYB();
		System.out.println("KYBTOKEN: " +KYBToken);
	} catch (Exception e) {
		// Handle the exception here
		e.printStackTrace();
	}


	Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("x-jwt-token",KYBToken);
		
		RequestPath="BrandEMI/MerchantCreationRequest.json";
	
	    String CUST_ID=Cust_id;
	    String KYB_ID=businessId;
	    String MOBILE_NUMBER=mobile;
	    String PHONE_NUMBER=mobile;
	    String KYC_BUSINESS_PAN_NO=docValue;
	    String KYC_AUTHORIZED_SIGNATORY_PAN_NO=	docValue;
	    String REQUEST_ID="";
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("CREATED_BY", CREATED_BY);
		body.put("ACTION", ACTION);
		body.put("REQUEST_ID", REQUEST_ID);
		body.put("USER_NAME", USER_NAME);
		body.put("ACCOUNT_FOR", ACCOUNT_FOR);
		body.put("SOURCE_ID", SOURCE_ID);
		body.put("MERCHANT_TYPE", MERCHANT_TYPE);
		body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
		body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
		body.put("KYB_ID", KYB_ID);
		body.put("BUSINESS_NAME", BUSINESS_NAME);
		body.put("BUSINESS_TYPE", BUSINESS_TYPE);
		body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
		body.put("CUSTOM", CUSTOM);
		body.put("MERCHANT_NAME", MERCHANT_NAME);
		body.put("CURRENCY", CURRENCY);
		body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
		body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
		body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
		body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
		body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
		body.put("CATEGORY", CATEGORY);
		body.put("SUB_CATEGORY", SUB_CATEGORY);
		body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
		body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
		body.put("PROFILE_ID", PROFILE_ID);
		body.put("EMAIL_ALERT", EMAIL_ALERT);
		body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
		body.put("VALID_FROM", VALID_FROM);
		body.put("VALID_TO", VALID_TO);
		body.put("MULTI_SUPPORT", MULTI_SUPPORT);
		body.put("HOW_MANY", HOW_MANY);
		body.put("OCP", OCP);
		body.put("REQUEST_NAME", REQUEST_NAME);
		body.put("FIRST_NAME", FIRST_NAME);
		body.put("LAST_NAME", LAST_NAME);
		body.put("MOBILE_NUMBER", MOBILE_NUMBER);
		body.put("PHONE_NUMBER", PHONE_NUMBER);
		body.put("MerchUniqRef", MerchUniqRef);
		body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
		body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
		body.put("IS_SUB_USER", IS_SUB_USER);
		body.put("ADDRESS1", ADDRESS1);
		body.put("ADDRESS2", ADDRESS2);
		body.put("ADDRESS3", ADDRESS3);
		body.put("COUNTRY", COUNTRY);
		body.put("STATE", STATE);
		body.put("CITY", CITY);
		body.put("PIN", PIN);
		body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
		body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
		body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
		body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
		body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
		body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
		body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
		body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
		body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
		body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
		body.put("KYC_BANK_NAME", KYC_BANK_NAME);
		body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
		body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
		body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
		body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
		body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
		body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
		body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
		body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
		body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
		body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
		body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
		body.put("WEBSITE_NAME", WEBSITE_NAME);
		body.put("SIZE_OF_KEY", SIZE_OF_KEY);
		body.put("SMS_MERCHANT", SMS_MERCHANT);
		body.put("PAYOUT_DAYS", PAYOUT_DAYS);
		body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
		body.put("FLAG_MERCHANT", FLAG_MERCHANT);
		body.put("BW_ENABLED", BW_ENABLED);
		body.put("TRANSFER_MODE", TRANSFER_MODE);
		body.put("AUTO", AUTO);
		body.put("TRIGGER_MODE", TRIGGER_MODE);
		body.put("TRIGGER_VALUE", TRIGGER_VALUE);
		body.put("API_DISABLED", API_DISABLED);
		body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
		body.put("P2M_ENABLED", P2M_ENABLED);
		body.put("OB_CHANNEL", OB_CHANNEL);
		body.put("SOLUTION_TYPE", SOLUTION_TYPE);
		body.put("WEBSITE_NAME1", WEBSITE_NAME1);
		//body.put("REQUEST_URL1", REQUEST_URL1);
	//	body.put("RESPONSE_URL1", RESPONSE_URL1);
	//	body.put("PEON_URL1", PEON_URL1);
	//	body.put("IMAGE_NAME1", IMAGE_NAME1);
		
		body.put("WEBSITE_NAME2", WEBSITE_NAME2);
		//body.put("REQUEST_URL2", REQUEST_URL2);
		//body.put("RESPONSE_URL2", RESPONSE_URL2);
		//body.put("IMAGE_NAME2", IMAGE_NAME2);
		
		body.put("WEBSITE_NAME3", WEBSITE_NAME3);
	//	body.put("REQUEST_URL3", REQUEST_URL3);
	//	body.put("RESPONSE_URL3", RESPONSE_URL3);
	//	body.put("IMAGE_NAME3", IMAGE_NAME3);
		
	//	body.put("DETAILED_LIST", DETAILED_LIST);
		body.put("VELOCITY_TYPE", VELOCITY_TYPE);
		body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
		body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
	//	body.put("ACTION_ConfigureMerchnatComm", ACTION_ConfigureMerchnatComm);
		body.put("FEE_TYPE", FEE_TYPE);
		body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
		body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
		body.put("TXN_TYPE", TXN_TYPE);
		body.put("PAY_MODE", PAY_MODE);
		body.put("CUST_ID", CUST_ID);
		
		
		
		
		
		
	
		Response responseObject = BrandEMIBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
			
			
	
	} 

@Test(description = "Create Merchant on PG without SourceID",dependsOnMethods = {"TC02_FetchUserId","TC12_CompanyOnboard"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC14_CreateMerchantonPGWithoutRequestID() {

	String KYBToken = null;
	try {
		KYBToken = generateTokenKYB();
		System.out.println("KYBTOKEN: " +KYBToken);
	} catch (Exception e) {
		// Handle the exception here
		e.printStackTrace();
	}
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("x-jwt-token",KYBToken);
		
		RequestPath="BrandEMI/MerchantCreationRequest.json";
	
	    String CUST_ID=Cust_id;
	    String KYB_ID=businessId;
	    String MOBILE_NUMBER=mobile;
	    String PHONE_NUMBER=mobile;
	    String KYC_BUSINESS_PAN_NO=docValue;
	    String KYC_AUTHORIZED_SIGNATORY_PAN_NO=	docValue;
	    String SOURCE_ID="";
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("CREATED_BY", CREATED_BY);
		body.put("ACTION", ACTION);
		body.put("REQUEST_ID", REQUEST_ID);
		body.put("USER_NAME", USER_NAME);
		body.put("ACCOUNT_FOR", ACCOUNT_FOR);
		body.put("SOURCE_ID", SOURCE_ID);
		body.put("MERCHANT_TYPE", MERCHANT_TYPE);
		body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
		body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
		body.put("KYB_ID", KYB_ID);
		body.put("BUSINESS_NAME", BUSINESS_NAME);
		body.put("BUSINESS_TYPE", BUSINESS_TYPE);
		body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
		body.put("CUSTOM", CUSTOM);
		body.put("MERCHANT_NAME", MERCHANT_NAME);
		body.put("CURRENCY", CURRENCY);
		body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
		body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
		body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
		body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
		body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
		body.put("CATEGORY", CATEGORY);
		body.put("SUB_CATEGORY", SUB_CATEGORY);
		body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
		body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
		body.put("PROFILE_ID", PROFILE_ID);
		body.put("EMAIL_ALERT", EMAIL_ALERT);
		body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
		body.put("VALID_FROM", VALID_FROM);
		body.put("VALID_TO", VALID_TO);
		body.put("MULTI_SUPPORT", MULTI_SUPPORT);
		body.put("HOW_MANY", HOW_MANY);
		body.put("OCP", OCP);
		body.put("REQUEST_NAME", REQUEST_NAME);
		body.put("FIRST_NAME", FIRST_NAME);
		body.put("LAST_NAME", LAST_NAME);
		body.put("MOBILE_NUMBER", MOBILE_NUMBER);
		body.put("PHONE_NUMBER", PHONE_NUMBER);
		body.put("MerchUniqRef", MerchUniqRef);
		body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
		body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
		body.put("IS_SUB_USER", IS_SUB_USER);
		body.put("ADDRESS1", ADDRESS1);
		body.put("ADDRESS2", ADDRESS2);
		body.put("ADDRESS3", ADDRESS3);
		body.put("COUNTRY", COUNTRY);
		body.put("STATE", STATE);
		body.put("CITY", CITY);
		body.put("PIN", PIN);
		body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
		body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
		body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
		body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
		body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
		body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
		body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
		body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
		body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
		body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
		body.put("KYC_BANK_NAME", KYC_BANK_NAME);
		body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
		body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
		body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
		body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
		body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
		body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
		body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
		body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
		body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
		body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
		body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
		body.put("WEBSITE_NAME", WEBSITE_NAME);
		body.put("SIZE_OF_KEY", SIZE_OF_KEY);
		body.put("SMS_MERCHANT", SMS_MERCHANT);
		body.put("PAYOUT_DAYS", PAYOUT_DAYS);
		body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
		body.put("FLAG_MERCHANT", FLAG_MERCHANT);
		body.put("BW_ENABLED", BW_ENABLED);
		body.put("TRANSFER_MODE", TRANSFER_MODE);
		body.put("AUTO", AUTO);
		body.put("TRIGGER_MODE", TRIGGER_MODE);
		body.put("TRIGGER_VALUE", TRIGGER_VALUE);
		body.put("API_DISABLED", API_DISABLED);
		body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
		body.put("P2M_ENABLED", P2M_ENABLED);
		body.put("OB_CHANNEL", OB_CHANNEL);
		body.put("SOLUTION_TYPE", SOLUTION_TYPE);
		body.put("WEBSITE_NAME1", WEBSITE_NAME1);
		body.put("WEBSITE_NAME2", WEBSITE_NAME2);
		body.put("WEBSITE_NAME3", WEBSITE_NAME3);
		body.put("VELOCITY_TYPE", VELOCITY_TYPE);
		body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
		body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
		body.put("FEE_TYPE", FEE_TYPE);
		body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
		body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
		body.put("TXN_TYPE", TXN_TYPE);
		body.put("PAY_MODE", PAY_MODE);
		body.put("CUST_ID", CUST_ID);
		
		
		
		
		
		
	
		Response responseObject = BrandEMIBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
			
			
	
	} 


@Test(description = "Create Merchant on PG without SourceID and RequestID",dependsOnMethods = {"TC02_FetchUserId","TC12_CompanyOnboard"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC15_CreateMerchantonPGWithoutRequestIDSourceID() {

	String KYBToken = null;
	try {
		KYBToken = generateTokenKYB();
		System.out.println("KYBTOKEN: " +KYBToken);
	} catch (Exception e) {
		// Handle the exception here
		e.printStackTrace();
	}
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("x-jwt-token",KYBToken);
		
		RequestPath="BrandEMI/MerchantCreationRequest.json";
	
	    String CUST_ID=Cust_id;
	    String KYB_ID=businessId;
	    String MOBILE_NUMBER=mobile;
	    String PHONE_NUMBER=mobile;
	    String KYC_BUSINESS_PAN_NO=docValue;
	    String KYC_AUTHORIZED_SIGNATORY_PAN_NO=	docValue;
	    String SOURCE_ID="";
	    String REQUEST_ID="";
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("CREATED_BY", CREATED_BY);
		body.put("ACTION", ACTION);
		body.put("REQUEST_ID", REQUEST_ID);
		body.put("USER_NAME", USER_NAME);
		body.put("ACCOUNT_FOR", ACCOUNT_FOR);
		body.put("SOURCE_ID", SOURCE_ID);
		body.put("MERCHANT_TYPE", MERCHANT_TYPE);
		body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
		body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
		body.put("KYB_ID", KYB_ID);
		body.put("BUSINESS_NAME", BUSINESS_NAME);
		body.put("BUSINESS_TYPE", BUSINESS_TYPE);
		body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
		body.put("CUSTOM", CUSTOM);
		body.put("MERCHANT_NAME", MERCHANT_NAME);
		body.put("CURRENCY", CURRENCY);
		body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
		body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
		body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
		body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
		body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
		body.put("CATEGORY", CATEGORY);
		body.put("SUB_CATEGORY", SUB_CATEGORY);
		body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
		body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
		body.put("PROFILE_ID", PROFILE_ID);
		body.put("EMAIL_ALERT", EMAIL_ALERT);
		body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
		body.put("VALID_FROM", VALID_FROM);
		body.put("VALID_TO", VALID_TO);
		body.put("MULTI_SUPPORT", MULTI_SUPPORT);
		body.put("HOW_MANY", HOW_MANY);
		body.put("OCP", OCP);
		body.put("REQUEST_NAME", REQUEST_NAME);
		body.put("FIRST_NAME", FIRST_NAME);
		body.put("LAST_NAME", LAST_NAME);
		body.put("MOBILE_NUMBER", MOBILE_NUMBER);
		body.put("PHONE_NUMBER", PHONE_NUMBER);
		body.put("MerchUniqRef", MerchUniqRef);
		body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
		body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
		body.put("IS_SUB_USER", IS_SUB_USER);
		body.put("ADDRESS1", ADDRESS1);
		body.put("ADDRESS2", ADDRESS2);
		body.put("ADDRESS3", ADDRESS3);
		body.put("COUNTRY", COUNTRY);
		body.put("STATE", STATE);
		body.put("CITY", CITY);
		body.put("PIN", PIN);
		body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
		body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
		body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
		body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
		body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
		body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
		body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
		body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
		body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
		body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
		body.put("KYC_BANK_NAME", KYC_BANK_NAME);
		body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
		body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
		body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
		body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
		body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
		body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
		body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
		body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
		body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
		body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
		body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
		body.put("WEBSITE_NAME", WEBSITE_NAME);
		body.put("SIZE_OF_KEY", SIZE_OF_KEY);
		body.put("SMS_MERCHANT", SMS_MERCHANT);
		body.put("PAYOUT_DAYS", PAYOUT_DAYS);
		body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
		body.put("FLAG_MERCHANT", FLAG_MERCHANT);
		body.put("BW_ENABLED", BW_ENABLED);
		body.put("TRANSFER_MODE", TRANSFER_MODE);
		body.put("AUTO", AUTO);
		body.put("TRIGGER_MODE", TRIGGER_MODE);
		body.put("TRIGGER_VALUE", TRIGGER_VALUE);
		body.put("API_DISABLED", API_DISABLED);
		body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
		body.put("P2M_ENABLED", P2M_ENABLED);
		body.put("OB_CHANNEL", OB_CHANNEL);
		body.put("SOLUTION_TYPE", SOLUTION_TYPE);
		body.put("WEBSITE_NAME1", WEBSITE_NAME1);
		body.put("WEBSITE_NAME2", WEBSITE_NAME2);
		body.put("WEBSITE_NAME3", WEBSITE_NAME3);
		body.put("VELOCITY_TYPE", VELOCITY_TYPE);
		body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
		body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
		body.put("FEE_TYPE", FEE_TYPE);
		body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
		body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
		body.put("TXN_TYPE", TXN_TYPE);
		body.put("PAY_MODE", PAY_MODE);
		body.put("CUST_ID", CUST_ID);
		
		
		
		
		
		
	
		Response responseObject = BrandEMIBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
			
			
	
	} 

 



@Test(description = "Create Merchant on PG",dependsOnMethods = {"TC02_FetchUserId","TC12_CompanyOnboard"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC16_CreateMerchantonPG() throws InterruptedException {

	String KYBToken = null;
	try {
		KYBToken = generateTokenKYB();
		System.out.println("KYBTOKEN: " +KYBToken);
	} catch (Exception e) {
		// Handle the exception here
		e.printStackTrace();
	}
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("x-jwt-token",KYBToken);
		
		RequestPath="BrandEMI/MerchantCreationRequest.json";
	
	    String CUST_ID=Cust_id;
	    String KYB_ID=businessId;
	    String MOBILE_NUMBER=mobile;
	    String PHONE_NUMBER=mobile;
	    String KYC_BUSINESS_PAN_NO=docValue;
	    String KYC_AUTHORIZED_SIGNATORY_PAN_NO=	docValue;
	    
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("CREATED_BY", CREATED_BY);
		body.put("ACTION", ACTION);
		body.put("REQUEST_ID", REQUEST_ID);
		body.put("USER_NAME", USER_NAME);
		body.put("ACCOUNT_FOR", ACCOUNT_FOR);
		body.put("SOURCE_ID", SOURCE_ID);
		body.put("MERCHANT_TYPE", MERCHANT_TYPE);
		body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
		body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
		body.put("KYB_ID", KYB_ID);
		body.put("BUSINESS_NAME", BUSINESS_NAME);
		body.put("BUSINESS_TYPE", BUSINESS_TYPE);
		body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
		body.put("CUSTOM", CUSTOM);
		body.put("MERCHANT_NAME", MERCHANT_NAME);
		body.put("CURRENCY", CURRENCY);
		body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
		body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
		body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
		body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
		body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
		body.put("CATEGORY", "Government");
		body.put("SUB_CATEGORY", "Central Department");
		body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
		body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
		body.put("PROFILE_ID", PROFILE_ID);
		body.put("EMAIL_ALERT", EMAIL_ALERT);
		body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
		body.put("VALID_FROM", VALID_FROM);
		body.put("VALID_TO", VALID_TO);
		body.put("MULTI_SUPPORT", MULTI_SUPPORT);
		body.put("HOW_MANY", HOW_MANY);
		body.put("OCP", OCP);
		body.put("REQUEST_NAME", REQUEST_NAME);
		body.put("FIRST_NAME", FIRST_NAME);
		body.put("LAST_NAME", LAST_NAME);
		body.put("MOBILE_NUMBER", MOBILE_NUMBER);
		body.put("PHONE_NUMBER", PHONE_NUMBER);
		body.put("MerchUniqRef", MerchUniqRef);
		body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
		body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
		body.put("IS_SUB_USER", IS_SUB_USER);
		body.put("ADDRESS1", ADDRESS1);
		body.put("ADDRESS2", ADDRESS2);
		body.put("ADDRESS3", ADDRESS3);
		body.put("COUNTRY", COUNTRY);
		body.put("STATE", STATE);
		body.put("CITY", CITY);
		body.put("PIN", PIN);
		body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
		body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
		body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
		body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
		body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
		body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
		body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
		body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
		body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
		body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
		body.put("KYC_BANK_NAME", KYC_BANK_NAME);
		body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
		body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
		body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
		body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
		body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
		body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
		body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
		body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
		body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
		body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
		body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
		body.put("WEBSITE_NAME", WEBSITE_NAME);
		body.put("SIZE_OF_KEY", SIZE_OF_KEY);
		body.put("SMS_MERCHANT", SMS_MERCHANT);
		body.put("PAYOUT_DAYS", PAYOUT_DAYS);
		body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
		body.put("FLAG_MERCHANT", FLAG_MERCHANT);
		body.put("BW_ENABLED", BW_ENABLED);
		body.put("TRANSFER_MODE", TRANSFER_MODE);
		body.put("AUTO", AUTO);
		body.put("TRIGGER_MODE", TRIGGER_MODE);
		body.put("TRIGGER_VALUE", TRIGGER_VALUE);
		body.put("API_DISABLED", API_DISABLED);
		body.put("MERCHANT_INDUSTRY_TYPE", "SMALL");
		body.put("P2M_ENABLED", P2M_ENABLED);
		body.put("OB_CHANNEL", OB_CHANNEL);
		body.put("SOLUTION_TYPE", SOLUTION_TYPE);
		body.put("WEBSITE_NAME1", WEBSITE_NAME1);
		body.put("WEBSITE_NAME2", WEBSITE_NAME2);
		body.put("WEBSITE_NAME3", WEBSITE_NAME3);
		body.put("VELOCITY_TYPE", VELOCITY_TYPE);
		body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
		body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
		body.put("FEE_TYPE", FEE_TYPE);
		body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
		body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
		body.put("TXN_TYPE", TXN_TYPE);
		body.put("PAY_MODE", PAY_MODE);
		body.put("CUST_ID", CUST_ID);
		
		
		
		
	
		Response responseObject = BrandEMIBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "MID generation is in progress");
			verifyResponseCodeAs200OK(responseObject);
			Thread.sleep(15000);  //wait for MID Generation
			
	
	} 

@Test(description = "Create Merchant on PG without KYBID",dependsOnMethods = {"TC02_FetchUserId","TC12_CompanyOnboard"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC17_CreateMerchantonPGWithoutKYB() throws InterruptedException {

	String KYBToken = null;
	try {
		KYBToken = generateTokenKYB();
		System.out.println("KYBTOKEN: " +KYBToken);
	} catch (Exception e) {
		// Handle the exception here
		e.printStackTrace();
	}
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("x-jwt-token",KYBToken);
		
		RequestPath="BrandEMI/MerchantCreationRequest.json";
	
	    String CUST_ID=Cust_id;
	    String MOBILE_NUMBER=mobile;
	    String PHONE_NUMBER=mobile;
	    String KYC_BUSINESS_PAN_NO=docValue;
	    String KYC_AUTHORIZED_SIGNATORY_PAN_NO=	docValue;
	    String KYB_NEWID="";
	
		Map<String, String> body = new HashMap<String, String>();
		body.put("CREATED_BY", CREATED_BY);
		body.put("ACTION", ACTION);
		body.put("REQUEST_ID", REQUEST_ID);
		body.put("USER_NAME", USER_NAME);
		body.put("ACCOUNT_FOR", ACCOUNT_FOR);
		body.put("SOURCE_ID", SOURCE_ID);
		body.put("MERCHANT_TYPE", MERCHANT_TYPE);
		body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
		body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
		body.put("KYB_ID", KYB_NEWID);
		body.put("BUSINESS_NAME", BUSINESS_NAME);
		body.put("BUSINESS_TYPE", BUSINESS_TYPE);
		body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
		body.put("CUSTOM", CUSTOM);
		body.put("MERCHANT_NAME", MERCHANT_NAME);
		body.put("CURRENCY", CURRENCY);
		body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
		body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
		body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
		body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
		body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
		body.put("CATEGORY", CATEGORY);
		body.put("SUB_CATEGORY", SUB_CATEGORY);
		body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
		body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
		body.put("PROFILE_ID", PROFILE_ID);
		body.put("EMAIL_ALERT", EMAIL_ALERT);
		body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
		body.put("VALID_FROM", VALID_FROM);
		body.put("VALID_TO", VALID_TO);
		body.put("MULTI_SUPPORT", MULTI_SUPPORT);
		body.put("HOW_MANY", HOW_MANY);
		body.put("OCP", OCP);
		body.put("REQUEST_NAME", REQUEST_NAME);
		body.put("FIRST_NAME", FIRST_NAME);
		body.put("LAST_NAME", LAST_NAME);
		body.put("MOBILE_NUMBER", MOBILE_NUMBER);
		body.put("PHONE_NUMBER", PHONE_NUMBER);
		body.put("MerchUniqRef", MerchUniqRef);
		body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
		body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
		body.put("IS_SUB_USER", IS_SUB_USER);
		body.put("ADDRESS1", ADDRESS1);
		body.put("ADDRESS2", ADDRESS2);
		body.put("ADDRESS3", ADDRESS3);
		body.put("COUNTRY", COUNTRY);
		body.put("STATE", STATE);
		body.put("CITY", CITY);
		body.put("PIN", PIN);
		body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
		body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
		body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
		body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
		body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
		body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
		body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
		body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
		body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
		body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
		body.put("KYC_BANK_NAME", KYC_BANK_NAME);
		body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
		body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
		body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
		body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
		body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
		body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
		body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
		body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
		body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
		body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
		body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
		body.put("WEBSITE_NAME", WEBSITE_NAME);
		body.put("SIZE_OF_KEY", SIZE_OF_KEY);
		body.put("SMS_MERCHANT", SMS_MERCHANT);
		body.put("PAYOUT_DAYS", PAYOUT_DAYS);
		body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
		body.put("FLAG_MERCHANT", FLAG_MERCHANT);
		body.put("BW_ENABLED", BW_ENABLED);
		body.put("TRANSFER_MODE", TRANSFER_MODE);
		body.put("AUTO", AUTO);
		body.put("TRIGGER_MODE", TRIGGER_MODE);
		body.put("TRIGGER_VALUE", TRIGGER_VALUE);
		body.put("API_DISABLED", API_DISABLED);
		body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
		body.put("P2M_ENABLED", P2M_ENABLED);
		body.put("OB_CHANNEL", OB_CHANNEL);
		body.put("SOLUTION_TYPE", SOLUTION_TYPE);
		body.put("WEBSITE_NAME1", WEBSITE_NAME1);
		body.put("WEBSITE_NAME2", WEBSITE_NAME2);
		body.put("WEBSITE_NAME3", WEBSITE_NAME3);
		body.put("VELOCITY_TYPE", VELOCITY_TYPE);
		body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
		body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
		body.put("FEE_TYPE", FEE_TYPE);
		body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
		body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
		body.put("TXN_TYPE", TXN_TYPE);
		body.put("PAY_MODE", PAY_MODE);
		body.put("CUST_ID", CUST_ID);
		
		
		
		
	
		Response responseObject = BrandEMIBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "MID generation is in progress");
			verifyResponseCodeAs200OK(responseObject);
			
	
	}

@Test(description = "Check Created merchant on PG without Token",dependsOnMethods = {"TC02_FetchUserId","TC12_CompanyOnboard"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC18_CheckCreatedMerchantWithoutToken() {
		
		Map<String, String> headers = new HashMap<String, String>();
		
		
		
		String xssotoken="";
		headers.put("x-sso-token", xssotoken);
		headers.put("Cookie", "SESSION=b3882129-cfd1-4582-a3c7-1f3fdc971b1b");
		
		
		RequestPath="BrandEMI/CheckCreatedMerchantRequest.json";
	
		Response responseObject = BrandEMIBaseClassObject.CheckCraetedMerchant(headers, RequestPath , Cust_id);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			//Assert.assertEquals(responseObject.jsonPath().getString("state"), "null");
		verifyResponseCodeAs403BadRequest(responseObject);
			
			
			
		
	
	}   

@Test(description = "Check Created merchant on PG ",dependsOnMethods = {"TC02_FetchUserId","TC12_CompanyOnboard"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC19_CheckCreatedMerchantonPG() {
		
		Map<String, String> headers = new HashMap<String, String>();
		
		
	
		headers.put("x-sso-token", xssotoken);
		headers.put("Cookie", "BOSS_SESSION=ab9d25bc-f8aa-4be5-bd5a-b0e25d8bbbb5");
		
		
		
		System.out.println(xssotoken);
		RequestPath="BrandEMI/CheckCreatedMerchant.json";
	
		Response responseObject = BrandEMIBaseClassObject.CheckCraetedMerchant(headers, RequestPath , Cust_id);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
			//Assert.assertEquals(responseObject.jsonPath().getString("state"), "null");
		verifyResponseCodeAs200OK(responseObject);
		MID=responseObject.jsonPath().getString("MID");
		
			
	
	}   


@Test(description = "Edit Merchant on PG without Headers",dependsOnMethods = {"TC02_FetchUserId","TC12_CompanyOnboard","TC16_CreateMerchantonPG","TC19_CheckCreatedMerchantonPG"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC20_EditMerchantonPGWithoutHeaders() throws InterruptedException {
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("cache-control", "no-cache");
		headers.put("Cookie",CookieEditMerchantOnPG);
		
		RequestPath="BrandEMI/EditMerchantOnPGRequest.json";
	
	    String CUST_ID=Cust_id;
	    String GSTIN= "09"+docValue+"1ZU";
	    String MID="lErKgg68277133000129";
	    String REQUEST_ID="";
	    System.out.println(KYB_ID);	  
	    
		Map<String, String> body = new HashMap<String, String>();
		body.put("CREATED_BY", CREATED_BY);
		body.put("MID", MID);
		body.put("TYPE", TYPE);
		body.put("REQUEST_ID", REQUEST_ID);
		body.put("ACCOUNT_FOR", ACCOUNT_FOR);
		body.put("SOURCE_ID", SOURCE_ID);
		body.put("KYB_ID", KYB_ID);
		body.put("BUSINESS_NAME", BUSINESS_NAME);
		body.put("ADDRESS1", ADDRESS1);
		body.put("ADDRESS2", ADDRESS2);
		body.put("ADDRESS3", ADDRESS3);
		body.put("COUNTRY", COUNTRY);
		body.put("STATE", STATE);
		body.put("CITY", CITY);
		body.put("PIN", PIN);
		body.put("GSTIN", GSTIN);
		body.put("KYC_BUSINESS_GSTIN", GSTIN);
		body.put("CUST_ID", CUST_ID);
		
		
		
		
	
		Response responseObject = BrandEMIBaseClassObject.EditMerchantOnPG(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
		Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
		verifyResponseCodeAs200OK(responseObject);
			
	
	}

@Test(description = "Edit Merchant on PG without SourceID",dependsOnMethods = {"TC02_FetchUserId","TC12_CompanyOnboard","TC16_CreateMerchantonPG","TC19_CheckCreatedMerchantonPG"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC22_EditMerchantonPGWithourSourceID() throws InterruptedException {
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("Cookie",CookieEditMerchantOnPG);
		
		RequestPath="BrandEMI/EditMerchantOnPGRequest.json";
	
	    String CUST_ID=Cust_id;
	    String GSTIN= "09"+docValue+"1ZU";
	    String MID="lErKgg68277133000129";
	    System.out.println(KYB_ID);
	    String SOURCE_ID="";
	    
		Map<String, String> body = new HashMap<String, String>();
		body.put("CREATED_BY", CREATED_BY);
		body.put("MID", MID);
		body.put("TYPE", TYPE);
		body.put("REQUEST_ID", REQUEST_ID);
		body.put("ACCOUNT_FOR", ACCOUNT_FOR);
		body.put("SOURCE_ID", SOURCE_ID);
		body.put("KYB_ID", KYB_ID);
		body.put("BUSINESS_NAME", BUSINESS_NAME);
		body.put("ADDRESS1", ADDRESS1);
		body.put("ADDRESS2", ADDRESS2);
		body.put("ADDRESS3", ADDRESS3);
		body.put("COUNTRY", COUNTRY);
		body.put("STATE", STATE);
		body.put("CITY", CITY);
		body.put("PIN", PIN);
		body.put("GSTIN", GSTIN);
		body.put("KYC_BUSINESS_GSTIN", GSTIN);
		body.put("CUST_ID", CUST_ID);
		
		
		
		
	
		Response responseObject = BrandEMIBaseClassObject.EditMerchantOnPG(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
		Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
		verifyResponseCodeAs200OK(responseObject);
			
	
	}

@Test(description = "Edit Merchant on PG without RequestID",dependsOnMethods = {"TC02_FetchUserId","TC12_CompanyOnboard","TC16_CreateMerchantonPG","TC19_CheckCreatedMerchantonPG"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC23_EditMerchantonPGWithoutRequestID() throws InterruptedException {
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("Cookie",CookieEditMerchantOnPG);
		
		RequestPath="BrandEMI/EditMerchantOnPGRequest.json";
	
	    String CUST_ID=Cust_id;
	    String GSTIN= "09"+docValue+"1ZU";
	    String MID="lErKgg68277133000129";
	    System.out.println(KYB_ID);
	    String REQUEST_ID="";
	    
		Map<String, String> body = new HashMap<String, String>();
		body.put("CREATED_BY", CREATED_BY);
		body.put("MID", MID);
		body.put("TYPE", TYPE);
		body.put("REQUEST_ID", REQUEST_ID);
		body.put("ACCOUNT_FOR", ACCOUNT_FOR);
		body.put("SOURCE_ID", SOURCE_ID);
		body.put("KYB_ID", KYB_ID);
		body.put("BUSINESS_NAME", BUSINESS_NAME);
		body.put("ADDRESS1", ADDRESS1);
		body.put("ADDRESS2", ADDRESS2);
		body.put("ADDRESS3", ADDRESS3);
		body.put("COUNTRY", COUNTRY);
		body.put("STATE", STATE);
		body.put("CITY", CITY);
		body.put("PIN", PIN);
		body.put("GSTIN", GSTIN);
		body.put("KYC_BUSINESS_GSTIN", GSTIN);
		body.put("CUST_ID", CUST_ID);
		
		
		
		
	
		Response responseObject = BrandEMIBaseClassObject.EditMerchantOnPG(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
		Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
		verifyResponseCodeAs200OK(responseObject);
			
	
	}

@Test(description = "Edit Merchant on PG without RequestID and SourceID",dependsOnMethods = {"TC02_FetchUserId","TC12_CompanyOnboard","TC16_CreateMerchantonPG","TC19_CheckCreatedMerchantonPG"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC24_EditMerchantonPGWithoutRequestSourceID() throws InterruptedException {
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("Cookie",CookieEditMerchantOnPG);
		
		RequestPath="BrandEMI/EditMerchantOnPGRequest.json";
	
	    String CUST_ID=Cust_id;
	    String GSTIN= "09"+docValue+"1ZU";
	    String MID="lErKgg68277133000129";
	    System.out.println(KYB_ID);
	    String REQUEST_ID="";
	    String SOURCE_ID="";
	    
		Map<String, String> body = new HashMap<String, String>();
		body.put("CREATED_BY", CREATED_BY);
		body.put("MID", MID);
		body.put("TYPE", TYPE);
		body.put("REQUEST_ID", REQUEST_ID);
		body.put("ACCOUNT_FOR", ACCOUNT_FOR);
		body.put("SOURCE_ID", SOURCE_ID);
		body.put("KYB_ID", KYB_ID);
		body.put("BUSINESS_NAME", BUSINESS_NAME);
		body.put("ADDRESS1", ADDRESS1);
		body.put("ADDRESS2", ADDRESS2);
		body.put("ADDRESS3", ADDRESS3);
		body.put("COUNTRY", COUNTRY);
		body.put("STATE", STATE);
		body.put("CITY", CITY);
		body.put("PIN", PIN);
		body.put("GSTIN", GSTIN);
		body.put("KYC_BUSINESS_GSTIN", GSTIN);
		body.put("CUST_ID", CUST_ID);
		
		
		
		
	
		Response responseObject = BrandEMIBaseClassObject.EditMerchantOnPG(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
		Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
		verifyResponseCodeAs200OK(responseObject);
			
	
	}

@Test(description = "Edit Merchant on PG without CustID",dependsOnMethods = {"TC02_FetchUserId","TC12_CompanyOnboard","TC16_CreateMerchantonPG","TC19_CheckCreatedMerchantonPG"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC25_EditMerchantonPGWithoutCustID() throws InterruptedException {
		
	/*	Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("Cookie",CookieEditMerchantOnPG);
		
		RequestPath="BrandEMI/EditMerchantOnPGRequest.json";
	
	    String CUST_ID="";
	    String GSTIN= "09"+docValue+"1ZU";
	    System.out.println(KYB_ID);
	    
		Map<String, String> body = new HashMap<String, String>();
		body.put("CREATED_BY", CREATED_BY);
		body.put("MID", MID);
		body.put("TYPE", TYPE);
		body.put("REQUEST_ID", REQUEST_ID);
		body.put("ACCOUNT_FOR", ACCOUNT_FOR);
		body.put("SOURCE_ID", SOURCE_ID);
		body.put("KYB_ID", KYB_ID);
		body.put("BUSINESS_NAME", BUSINESS_NAME);
		body.put("ADDRESS1", ADDRESS1);
		body.put("ADDRESS2", ADDRESS2);
		body.put("ADDRESS3", ADDRESS3);
		body.put("COUNTRY", COUNTRY);
		body.put("STATE", STATE);
		body.put("CITY", CITY);
		body.put("PIN", PIN);
		body.put("GSTIN", GSTIN);
		body.put("KYC_BUSINESS_GSTIN", GSTIN);
		body.put("CUST_ID", CUST_ID);
		
		
		
		
	
		Response responseObject = BrandEMIBaseClassObject.EditMerchantOnPG(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
		Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "MID generation is in progress");
		verifyResponseCodeAs200OK(responseObject); */

	Map<String, String> headers = new HashMap<String, String>();

	headers.put("Content-Type", "application/json");
	headers.put("cache-control", "no-cache");
	headers.put("Cookie",CookieEditMerchantOnPG);

	RequestPath="BrandEMI/EditMerchantOnPGRequest.json";

	String CUST_ID=Cust_id;
	String GSTIN= "09"+docValue+"1ZU";
	String MID="lErKgg68277133000129";
	System.out.println(KYB_ID);
	String REQUEST_ID="";
	String SOURCE_ID="";

	Map<String, String> body = new HashMap<String, String>();
	body.put("CREATED_BY", "");
	body.put("MID", MID);
	body.put("TYPE", TYPE);
	body.put("REQUEST_ID", REQUEST_ID);
	body.put("ACCOUNT_FOR", ACCOUNT_FOR);
	body.put("SOURCE_ID", SOURCE_ID);
	body.put("KYB_ID", KYB_ID);
	body.put("BUSINESS_NAME", BUSINESS_NAME);
	body.put("ADDRESS1", ADDRESS1);
	body.put("ADDRESS2", ADDRESS2);
	body.put("ADDRESS3", ADDRESS3);
	body.put("COUNTRY", COUNTRY);
	body.put("STATE", STATE);
	body.put("CITY", CITY);
	body.put("PIN", PIN);
	body.put("GSTIN", GSTIN);
	body.put("KYC_BUSINESS_GSTIN", GSTIN);
	body.put("CUST_ID", "");





	Response responseObject = BrandEMIBaseClassObject.EditMerchantOnPG(headers, body, RequestPath);

	LOGGER.info("Status Code : " + responseObject.getStatusCode());
	Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
	verifyResponseCodeAs200OK(responseObject);
			
	
	}


@Test(description = "Edit Merchant on PG without MID",dependsOnMethods = {"TC02_FetchUserId","TC12_CompanyOnboard","TC16_CreateMerchantonPG","TC19_CheckCreatedMerchantonPG"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC26_EditMerchantonPGWithoutMID() throws InterruptedException {
		
		Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("Cookie",CookieEditMerchantOnPG);
		
		RequestPath="BrandEMI/EditMerchantOnPGRequest.json";
	
	    String CUST_ID=Cust_id;
	    String GSTIN= "09"+docValue+"1ZU";
	    String MID="lErKgg68277133000129";
	    System.out.println(KYB_ID);
	    
		Map<String, String> body = new HashMap<String, String>();
		body.put("CREATED_BY", CREATED_BY);
		body.put("MID", MID);
		body.put("TYPE", TYPE);
		body.put("REQUEST_ID", REQUEST_ID);
		body.put("ACCOUNT_FOR", ACCOUNT_FOR);
		body.put("SOURCE_ID", SOURCE_ID);
		body.put("KYB_ID", KYB_ID);
		body.put("BUSINESS_NAME", BUSINESS_NAME);
		body.put("ADDRESS1", ADDRESS1);
		body.put("ADDRESS2", ADDRESS2);
		body.put("ADDRESS3", ADDRESS3);
		body.put("COUNTRY", COUNTRY);
		body.put("STATE", STATE);
		body.put("CITY", CITY);
		body.put("PIN", PIN);
		body.put("GSTIN", GSTIN);
		body.put("KYC_BUSINESS_GSTIN", GSTIN);
		body.put("CUST_ID", CUST_ID);
		
		
		
		
	
		Response responseObject = BrandEMIBaseClassObject.EditMerchantOnPG(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
		Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "MID generation is in progress");
		verifyResponseCodeAs200OK(responseObject);
			
	
	}

@Test(description = "Edit Merchant on PG without Business Name",dependsOnMethods = {"TC02_FetchUserId","TC12_CompanyOnboard","TC16_CreateMerchantonPG","TC19_CheckCreatedMerchantonPG"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC27_EditMerchantonPGWithoutBusinessName() throws InterruptedException {
		
		/*Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("Cookie",CookieEditMerchantOnPG);
		
		RequestPath="BrandEMI/EditMerchantOnPGRequest.json";
	
	    String CUST_ID=Cust_id;
	    String GSTIN= "09"+docValue+"1ZU";
	    String BUSINESS_NAME="";
	    System.out.println(KYB_ID);
	    
		Map<String, String> body = new HashMap<String, String>();
		body.put("CREATED_BY", CREATED_BY);
		body.put("MID", MID);
		body.put("TYPE", TYPE);
		body.put("REQUEST_ID", REQUEST_ID);
		body.put("ACCOUNT_FOR", ACCOUNT_FOR);
		body.put("SOURCE_ID", SOURCE_ID);
		body.put("KYB_ID", KYB_ID);
		body.put("BUSINESS_NAME", BUSINESS_NAME);
		body.put("ADDRESS1", ADDRESS1);
		body.put("ADDRESS2", ADDRESS2);
		body.put("ADDRESS3", ADDRESS3);
		body.put("COUNTRY", COUNTRY);
		body.put("STATE", STATE);
		body.put("CITY", CITY);
		body.put("PIN", PIN);
		body.put("GSTIN", GSTIN);
		body.put("KYC_BUSINESS_GSTIN", GSTIN);
		body.put("CUST_ID", CUST_ID);
		
		
		
		
	
		Response responseObject = BrandEMIBaseClassObject.EditMerchantOnPG(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
		Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "MID generation is in progress");
		verifyResponseCodeAs200OK(responseObject); */

	Map<String, String> headers = new HashMap<String, String>();

	headers.put("Content-Type", "application/json");
	headers.put("cache-control", "no-cache");
	headers.put("Cookie",CookieEditMerchantOnPG);

	RequestPath="BrandEMI/EditMerchantOnPGRequest.json";

	String CUST_ID=Cust_id;
	String GSTIN= "09"+docValue+"1ZU";
	String MID="lErKgg68277133000129";
	System.out.println(KYB_ID);
	String REQUEST_ID="";
	String SOURCE_ID="";

	Map<String, String> body = new HashMap<String, String>();
	body.put("CREATED_BY", "");
	body.put("MID", MID);
	body.put("TYPE", TYPE);
	body.put("REQUEST_ID", REQUEST_ID);
	body.put("ACCOUNT_FOR", ACCOUNT_FOR);
	body.put("SOURCE_ID", SOURCE_ID);
	body.put("KYB_ID", KYB_ID);
	body.put("BUSINESS_NAME", BUSINESS_NAME);
	body.put("ADDRESS1", ADDRESS1);
	body.put("ADDRESS2", ADDRESS2);
	body.put("ADDRESS3", ADDRESS3);
	body.put("COUNTRY", COUNTRY);
	body.put("STATE", STATE);
	body.put("CITY", CITY);
	body.put("PIN", PIN);
	body.put("GSTIN", GSTIN);
	body.put("KYC_BUSINESS_GSTIN", GSTIN);
	body.put("CUST_ID", CUST_ID);





	Response responseObject = BrandEMIBaseClassObject.EditMerchantOnPG(headers, body, RequestPath);

	LOGGER.info("Status Code : " + responseObject.getStatusCode());
	Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
	verifyResponseCodeAs200OK(responseObject);
			
	
	}


@Test(description = "Edit Merchant on PG without CraetedBY",dependsOnMethods = {"TC02_FetchUserId","TC12_CompanyOnboard","TC16_CreateMerchantonPG","TC19_CheckCreatedMerchantonPG"}, groups = {"Regression" },enabled=true)
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC28_EditMerchantonPGWithoutCreatedBY() throws InterruptedException {
		
	/*	Map<String, String> headers = new HashMap<String, String>();
		
		headers.put("Content-Type", "application/json");
		headers.put("cache-control", "no-cache");
		headers.put("Cookie",CookieEditMerchantOnPG);
		
		RequestPath="BrandEMI/EditMerchantOnPGRequest.json";
	
	    String CUST_ID=Cust_id;
	    String GSTIN= "09"+docValue+"1ZU";
	    String CREATED_BY="";
	    System.out.println(KYB_ID);
	    
		Map<String, String> body = new HashMap<String, String>();
		body.put("CREATED_BY", CREATED_BY);
		body.put("MID", MID);
		body.put("TYPE", TYPE);
		body.put("REQUEST_ID", REQUEST_ID);
		body.put("ACCOUNT_FOR", ACCOUNT_FOR);
		body.put("SOURCE_ID", SOURCE_ID);
		body.put("KYB_ID", KYB_ID);
		body.put("BUSINESS_NAME", BUSINESS_NAME);
		body.put("ADDRESS1", ADDRESS1);
		body.put("ADDRESS2", ADDRESS2);
		body.put("ADDRESS3", ADDRESS3);
		body.put("COUNTRY", COUNTRY);
		body.put("STATE", STATE);
		body.put("CITY", CITY);
		body.put("PIN", PIN);
		body.put("GSTIN", GSTIN);
		body.put("KYC_BUSINESS_GSTIN", GSTIN);
		body.put("CUST_ID", CUST_ID);
		
		
		
		
	
		Response responseObject = BrandEMIBaseClassObject.EditMerchantOnPG(headers, body, RequestPath);
	
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
		//Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "MID generation is in progress");
		verifyResponseCodeAs200OK(responseObject); */

	Map<String, String> headers = new HashMap<String, String>();

	headers.put("Content-Type", "application/json");
	headers.put("cache-control", "no-cache");
	headers.put("Cookie",CookieEditMerchantOnPG);

	RequestPath="BrandEMI/EditMerchantOnPGRequest.json";

	String CUST_ID=Cust_id;
	String GSTIN= "09"+docValue+"1ZU";
	String MID="lErKgg68277133000129";
	System.out.println(KYB_ID);
	String REQUEST_ID="";
	String SOURCE_ID="";

	Map<String, String> body = new HashMap<String, String>();
	body.put("CREATED_BY", "");
	body.put("MID", MID);
	body.put("TYPE", TYPE);
	body.put("REQUEST_ID", REQUEST_ID);
	body.put("ACCOUNT_FOR", ACCOUNT_FOR);
	body.put("SOURCE_ID", SOURCE_ID);
	body.put("KYB_ID", KYB_ID);
	body.put("BUSINESS_NAME", BUSINESS_NAME);
	body.put("ADDRESS1", ADDRESS1);
	body.put("ADDRESS2", ADDRESS2);
	body.put("ADDRESS3", ADDRESS3);
	body.put("COUNTRY", COUNTRY);
	body.put("STATE", STATE);
	body.put("CITY", CITY);
	body.put("PIN", PIN);
	body.put("GSTIN", GSTIN);
	body.put("KYC_BUSINESS_GSTIN", GSTIN);
	body.put("CUST_ID", CUST_ID);





	Response responseObject = BrandEMIBaseClassObject.EditMerchantOnPG(headers, body, RequestPath);

	LOGGER.info("Status Code : " + responseObject.getStatusCode());
	Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
	verifyResponseCodeAs200OK(responseObject);
			
	
	}






}
