package OCL.SFtoOE;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;

import Request.oAuth.oAuthWormhole.CreateUser;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import io.restassured.response.Response;

public class BusinessLeadCreation extends BaseMethod {

	String sessionToken = "";
	String leadId = "";
	String custId = "";
	String agentNumber = "";
	String agentPassword = "paytm@123";
	String token = "";
	String uuid = "";
	String ckycStage = "";
	String loanOffered = "";
	String maxLoanAmount = "";
	String authorisedMonthlyLimit = "";
	String stage = "";
	String code = "";
	String tncName = "";
	String url = "";
	String uniqueIdentifier = "";
	String md5 = "";
	String codeSanctionLetter = "";
	String tncNameSanctionLetter = "";
	String urlSanctionLetter = "";
	String uniqueIdentifierSanctionLetter = "";
	String md5SanctionLetter = "";
	
	public static String bankaccountholderName;
	public static String bankdetailsUuid;
	public static boolean namematchStatus = true;
	public static String leadID;
	public static String solID;
	
	public static final String SOLUTION = "upgrade_mid";
	public static final String SOLUTION_TYPE_LEVEL_2 = "CLIX";
	public static final String SOLUTION_TYPE_LEVEL_3 = "Unsecured_Short_term_Loan_Simplified";
	public static final String ENTITY_TYPE = "INDIVIDUAL";
	public static final String CHANNEL = "UMP_WEB";
	public static String PAN = "";
	public static final String DOB = "1989-04-21";
	public static final String EMAIL = "";
	public static final String ISSUER = "OE";
	public static final String CLIENT_ID = "LMS";
	public static final String WORKFLOW_VERSION = "V2";
	public static  String MOBILE = "";
	public static  String BUSINESSPAN="";
	public static  String EntityTypeUL = "PROPRIETORSHIP";
	public static final String BUSINESSNAME="TestBeneficiary";
	public static final String CATEGORY="BFSI";
	public static final String SUBCATEGORY="Loans";
	public static final String BANKNAME="ICICI Bank";
	public static String BANKACCOUNTNUMBER="************";
	public static final String IFSC="Icic0006622";
	private static final Logger LOGGER = LogManager.getLogger(BusinessLeadCreation.class);
	
	Map<String, String> commonHeaders;
	
	@BeforeClass()
	public void intitializeInputData() throws IOException {
		sessionToken=findXMWTokenforPanel("**********","paytm@123");

		Utilities accObj = new Utilities();
		MOBILE = accObj.randomMobileNumberGenerator();
		LOGGER.info("New Number is : " + MOBILE);
		//LOGGER.info(" Before Suite Method for Agent Login ");
		CreateUser OauthObj = new CreateUser();
		OauthObj.setHeader("Content-Type","application/json");
		OauthObj.getProperties().setProperty("mobile",MOBILE);
		OauthObj.getProperties().setProperty("loginPassword","paytm@123");
		Response OauthResp =  OauthObj.callAPI();

		
		//LOGGER.info("Applicant Token for Lending : " + sessionToken);
		commonHeaders = setcommonHeaders();

		switch (EntityTypeUL) {
			case "PUBLIC_LIMITED":
			case "PRIVATE_LIMITED": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomPublicPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
			case "PROPRIETORSHIP": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomIndividualPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
			case "TRUST": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomTrustPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
			case "SOCIETY_ASSOCIATION_CLUB": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomSocietyPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
			case "PARTNERSHIP": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomPartnershipPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
			case "HINDU_UNDIVIDED_FAMILY": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomHUFPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
		}

	}

	private Map<String, String> setcommonHeaders() {
	

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("cookie", sessionToken);
		headers.put("Content-Type", "application/json");


		return headers;
	}

	MiddlewareServices middlewareServiceObject =new MiddlewareServices();
	 

	
	
	@Test(priority = 0,groups = {"Regression"},description = "Create sf-oe entrprise business  lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0001_postCreateLead() {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		
		queryParams.put("solution", "enterprise_merchant_business");
		queryParams.put("entityType", EntityTypeUL);
	
		//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("channel", "OE_PANEL");
		//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("pan", "**********");
		body.put("MODEL", "B2C");
		body.put("SUB_MODEL", "Aggregator");
		body.put("NAME_AS_PER_NSDL", "TOUCH WOOD LIMITED");
		body.put("DATE_OF_INCORPORATION", "2023-05-11");
		body.put("GST_EXEMPTED", "Yes");
		body.put("LEGAL_NAME", "Ashish");
		body.put("SEGMENT", "BFSI");
		body.put("SUB_SEGMENT", "Credit Card");
		body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION", "No");
		
		body.put("IS_INTERNATIONAL_MERCHANT", "false");
		body.put("ONBOARDING_ENTITY", "OCL");
		body.put("EMAIL", "<EMAIL>");
		body.put("IS_GSTIN_FETCHED_FROM_PAN", "false");
		body.put("FLOW_TYPE", "offline");
		body.put("gstin", " null");
		body.put("displayName", "Ashish");
		body.put("addressSubType", "CORRESPONDENCE");
		body.put("addressType", "BUSINESS");
	    body.put("city", "Noida");
		body.put("country", "India");
		body.put("state", "Uttar Pradesh");
		body.put("pincode", "282004");
		body.put("line1", "F 9201");
		body.put("line2", "Sector 26");
		body.put("line3", "NAveen Okhla");
		body.put("copyAddressCheckbox_correspondenceAddressDetails", "true");
	
		
		Response responseObject = middlewareServiceObject.sfoecreateLead(queryParams, headers, body);
		
	// Status Code Validation	
		//middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);
		
		String responseBody  =responseObject.getBody().asString();
		System.out.println("Response Body is:" +responseBody);
		
		leadID =responseObject.jsonPath().getString("leadId");
		solID =responseObject.jsonPath().getString("solutionLeadId");
		
		// Status Code Validation
		//int statusCode=responseObject.getStatusCode();
		//Assert.assertEquals(statusCode, 200);
		
	// Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
      // Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead is already present.");

		
		}
	
	@Test(priority = 0,groups = {"Regression"},description = "Create sf-oe entrprise business  lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0002_postCreateLeadInvalidPinCode() {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		
		queryParams.put("solution", "enterprise_merchant_business");
		queryParams.put("entityType", EntityTypeUL);
	
		//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("channel", "OE_PANEL");
		//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("pan", "**********");
		body.put("MODEL", "B2C");
		body.put("SUB_MODEL", "Aggregator");
		body.put("NAME_AS_PER_NSDL", "TOUCH WOOD LIMITED");
		body.put("DATE_OF_INCORPORATION", "2023-05-11");
		body.put("GST_EXEMPTED", "Yes");
		body.put("LEGAL_NAME", "Ashish");
		body.put("SEGMENT", "BFSI");
		body.put("SUB_SEGMENT", "Credit Card");
		body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION", "No");
		
		body.put("IS_INTERNATIONAL_MERCHANT", "false");
		body.put("ONBOARDING_ENTITY", "OCL");
		body.put("EMAIL", "<EMAIL>");
		body.put("IS_GSTIN_FETCHED_FROM_PAN", "false");
		body.put("FLOW_TYPE", "offline");
		body.put("gstin", " null");
		body.put("displayName", "Ashish");
		body.put("addressSubType", "CORRESPONDENCE");
		body.put("addressType", "BUSINESS");
	    body.put("city", "Noida");
		body.put("country", "India");
		body.put("state", "Uttar Pradesh");
		body.put("pincode", "282004234");
		body.put("line1", "F 9201");
		body.put("line2", "Sector 26");
		body.put("line3", "NAveen Okhla");
		body.put("copyAddressCheckbox_correspondenceAddressDetails", "true");
	
		
		Response responseObject = middlewareServiceObject.sfoecreateLead(queryParams, headers, body);
		
	// Status Code Validation	
		//middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);
		
		String responseBody  =responseObject.getBody().asString();
		System.out.println("Response Body is:" +responseBody);
		
		leadID =responseObject.jsonPath().getString("leadId");
		solID =responseObject.jsonPath().getString("solutionLeadId");
		
		// Status Code Validation
		//int statusCode=responseObject.getStatusCode();
		//Assert.assertEquals(statusCode, 200);
		
	// Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
      // Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead is already present.");

		
		}
	@Test(priority = 0,groups = {"Regression"},description = "Create sf-oe entrprise business  lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0003_postCreateLeadInvalidDisplayName() {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		
		queryParams.put("solution", "enterprise_merchant_business");
		queryParams.put("entityType", EntityTypeUL);
	
		//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("channel", "OE_PANEL");
		//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("pan", "**********");
		body.put("MODEL", "B2C");
		body.put("SUB_MODEL", "Aggregator");
		body.put("NAME_AS_PER_NSDL", "TOUCH WOOD LIMITED");
		body.put("DATE_OF_INCORPORATION", "2023-05-11");
		body.put("GST_EXEMPTED", "Yes");
		body.put("LEGAL_NAME", "Ashish");
		body.put("SEGMENT", "BFSI");
		body.put("SUB_SEGMENT", "Credit Card");
		body.put("IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION", "No");
		
		body.put("IS_INTERNATIONAL_MERCHANT", "false");
		body.put("ONBOARDING_ENTITY", "OCL");
		body.put("EMAIL", "<EMAIL>");
		body.put("IS_GSTIN_FETCHED_FROM_PAN", "false");
		body.put("FLOW_TYPE", "offline");
		body.put("gstin", " null");
		body.put("displayName", "A");
		body.put("addressSubType", "CORRESPONDENCE");
		body.put("addressType", "BUSINESS");
	    body.put("city", "Noida");
		body.put("country", "India");
		body.put("state", "Uttar Pradesh");
		body.put("pincode", "282004");
		body.put("line1", "F 9201");
		body.put("line2", "Sector 26");
		body.put("line3", "NAveen Okhla");
		body.put("copyAddressCheckbox_correspondenceAddressDetails", "true");
	
		
		Response responseObject = middlewareServiceObject.sfoecreateLead(queryParams, headers, body);
		
	// Status Code Validation	
		//middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);
		
		String responseBody  =responseObject.getBody().asString();
		System.out.println("Response Body is:" +responseBody);
		
		leadID =responseObject.jsonPath().getString("leadId");
		solID =responseObject.jsonPath().getString("solutionLeadId");
		
		// Status Code Validation
		//int statusCode=responseObject.getStatusCode();
		//Assert.assertEquals(statusCode, 200);
		
	// Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
      // Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead is already present.");

		
		}
	
	

	
			
	
	}
	
	

