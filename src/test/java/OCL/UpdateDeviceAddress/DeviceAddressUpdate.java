package OCL.UpdateDeviceAddress;
import Request.UpdateDevideAddress.CreateLeadUpdateDeviceAddress;
import Services.DBConnection.DBConnection;
import Services.UpdateDeviceAddress.UpdateDeviceAddressServices;
import Services.Utilities.TestBase;
import com.goldengate.common.BaseMethod;
import com.goldengate.common.RetryAnalyzer;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class DeviceAddressUpdate extends BaseMethod {

    UpdateDeviceAddressServices updateDeviceAddressServices = new UpdateDeviceAddressServices();

    private static final Logger LOGGER = LogManager.getLogger(DeviceAddressUpdate.class);

    String AgentToken ="";

    String mobileNo= "7722127717";
    String solution_type="update_device_address";

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLogin() throws Exception {
        AgentToken = AgentSessionToken("7771116065", "paytm@123");
        LOGGER.info("Agent Token  : " + AgentToken);
        waitForLoad(3000);
       /* TestBase testBase =new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '"+mobileNo+"' and status = '0' and solution_type='update_device_address';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes); */
        DBConnection.UpdateQueryToCloseLead(mobileNo,solution_type);


    }

    @Test(priority = 0, description = "Create a new lead with all valid details", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        CreateLeadUpdateDeviceAddress createLeadUpdateDeviceAddress = new CreateLeadUpdateDeviceAddress(P.TESTDATA.get("CreateLeadUpdateDeviceAddressRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",AgentToken);
        headers.put("deviceIdentifier","OnePlus-CPH2487-b0239246dc8dc4ee");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","5555890134");
        body.put("mid","YMDdbW80309188492036");
        body.put("kybId","A02t97mz0v9qb288");

        Response respObj = updateDeviceAddressServices.CreateLeadUpdateAddress(createLeadUpdateDeviceAddress,body, headers);

        LOGGER.info(" Create a new lead with all valid details" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        String leadId = respObj.jsonPath().getJsonObject("leadId");
        LOGGER.info(" LeadId : " +leadId);
    }

    @Test(priority = 0, description = "Rehit same request to check no new lead should be created", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        CreateLeadUpdateDeviceAddress createLeadUpdateDeviceAddress = new CreateLeadUpdateDeviceAddress(P.TESTDATA.get("CreateLeadUpdateDeviceAddressRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",AgentToken);
        headers.put("deviceIdentifier","OnePlus-CPH2487-b0239246dc8dc4ee");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","5555890134");
        body.put("mid","YMDdbW80309188492036");
        body.put("kybId","A02t97mz0v9qb288");

        Response respObj = updateDeviceAddressServices.CreateLeadUpdateAddress(createLeadUpdateDeviceAddress,body, headers);

        LOGGER.info(" Create a new lead with all valid details" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        String leadId = respObj.jsonPath().getJsonObject("leadId");
        LOGGER.info(" LeadId : " +leadId);
    }

    @Test(priority = 0, description = "API Response without token", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        CreateLeadUpdateDeviceAddress createLeadUpdateDeviceAddress = new CreateLeadUpdateDeviceAddress(P.TESTDATA.get("CreateLeadUpdateDeviceAddressRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token","");
        headers.put("deviceIdentifier","OnePlus-CPH2487-b0239246dc8dc4ee");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","5555890134");
        body.put("mid","YMDdbW80309188492036");
        body.put("kybId","A02t97mz0v9qb288");

        Response respObj = updateDeviceAddressServices.CreateLeadUpdateAddress(createLeadUpdateDeviceAddress,body, headers);

        LOGGER.info(" Create a new lead with all valid details" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 401);

    }

    @Test(priority = 0, description = "API Response without device Identifier", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        CreateLeadUpdateDeviceAddress createLeadUpdateDeviceAddress = new CreateLeadUpdateDeviceAddress(P.TESTDATA.get("CreateLeadUpdateDeviceAddressRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",AgentToken);
        headers.put("deviceIdentifier","");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","5555890134");
        body.put("mid","YMDdbW80309188492036");
        body.put("kybId","A02t97mz0v9qb288");

        Response respObj = updateDeviceAddressServices.CreateLeadUpdateAddress(createLeadUpdateDeviceAddress,body, headers);

        LOGGER.info(" Create a new lead with all valid details" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);

    }
    @Test(priority = 0, description = "API Response without ENTITY TYPE", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        CreateLeadUpdateDeviceAddress createLeadUpdateDeviceAddress = new CreateLeadUpdateDeviceAddress(P.TESTDATA.get("CreateLeadUpdateDeviceAddressRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",AgentToken);
        headers.put("deviceIdentifier","OnePlus-CPH2487-b0239246dc8dc4ee");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","5555890134");
        body.put("mid","YMDdbW80309188492036");
        body.put("kybId","A02t97mz0v9qb288");

        Response respObj = updateDeviceAddressServices.CreateLeadUpdateAddress(createLeadUpdateDeviceAddress,body, headers);

        LOGGER.info(" Create a new lead with all valid details" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 500);

    }
    @Test(priority = 0, description = "Hit API again with different entity to check error message", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        CreateLeadUpdateDeviceAddress createLeadUpdateDeviceAddress = new CreateLeadUpdateDeviceAddress(P.TESTDATA.get("CreateLeadUpdateDeviceAddressRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",AgentToken);
        headers.put("deviceIdentifier","OnePlus-CPH2487-b0239246dc8dc4ee");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PRIVATE_LIMITED");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","5555890134");
        body.put("mid","YMDdbW80309188492036");
        body.put("kybId","A02t97mz0v9qb288");

        Response respObj = updateDeviceAddressServices.CreateLeadUpdateAddress(createLeadUpdateDeviceAddress,body, headers);

        LOGGER.info(" Create a new lead with all valid details" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);

        String ActualMsg = respObj.jsonPath().getString("internalMessage");
        String ExpectedMsg = "Your details could not be saved. Lead already exist with different EntityType, please continue with same.";
        Assert.assertTrue(ActualMsg.contains(ExpectedMsg));

    }
    @Test(priority = 0, description = "API Response without agent custID", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        CreateLeadUpdateDeviceAddress createLeadUpdateDeviceAddress = new CreateLeadUpdateDeviceAddress(P.TESTDATA.get("CreateLeadUpdateDeviceAddressRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",AgentToken);
        headers.put("deviceIdentifier","OnePlus-CPH2487-b0239246dc8dc4ee");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PRIVATE_LIMITED");
        body.put("userCustId","1700547833");
        body.put("agentCustId","");
        body.put("userMobile","5555890134");
        body.put("mid","YMDdbW80309188492036");
        body.put("kybId","A02t97mz0v9qb288");

        Response respObj = updateDeviceAddressServices.CreateLeadUpdateAddress(createLeadUpdateDeviceAddress,body, headers);

        LOGGER.info(" Create a new lead with all valid details" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 500);

    }

    @Test(priority = 0, description = "API Response without mobile number", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        CreateLeadUpdateDeviceAddress createLeadUpdateDeviceAddress = new CreateLeadUpdateDeviceAddress(P.TESTDATA.get("CreateLeadUpdateDeviceAddressRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",AgentToken);
        headers.put("deviceIdentifier","OnePlus-CPH2487-b0239246dc8dc4ee");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PRIVATE_LIMITED");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","");
        body.put("mid","YMDdbW80309188492036");
        body.put("kybId","A02t97mz0v9qb288");

        Response respObj = updateDeviceAddressServices.CreateLeadUpdateAddress(createLeadUpdateDeviceAddress,body, headers);

        LOGGER.info(" Create a new lead with all valid details" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);

    }
    @Test(priority = 0, description = "API Response without MID", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        CreateLeadUpdateDeviceAddress createLeadUpdateDeviceAddress = new CreateLeadUpdateDeviceAddress(P.TESTDATA.get("CreateLeadUpdateDeviceAddressRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",AgentToken);
        headers.put("deviceIdentifier","OnePlus-CPH2487-b0239246dc8dc4ee");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PRIVATE_LIMITED");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","5555890134");
        body.put("mid","");
        body.put("kybId","A02t97mz0v9qb288");

        Response respObj = updateDeviceAddressServices.CreateLeadUpdateAddress(createLeadUpdateDeviceAddress,body, headers);

        LOGGER.info(" Create a new lead with all valid details" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }

    @Test(priority = 0, description = "API Response without KYB ID", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        CreateLeadUpdateDeviceAddress createLeadUpdateDeviceAddress = new CreateLeadUpdateDeviceAddress(P.TESTDATA.get("CreateLeadUpdateDeviceAddressRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",AgentToken);
        headers.put("deviceIdentifier","OnePlus-CPH2487-b0239246dc8dc4ee");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PRIVATE_LIMITED");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","5555890134");
        body.put("mid","YMDdbW80309188492036");
        body.put("kybId","");

        Response respObj = updateDeviceAddressServices.CreateLeadUpdateAddress(createLeadUpdateDeviceAddress,body, headers);

        LOGGER.info(" Create a new lead with all valid details" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "API Response with invalid Entity", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        CreateLeadUpdateDeviceAddress createLeadUpdateDeviceAddress = new CreateLeadUpdateDeviceAddress(P.TESTDATA.get("CreateLeadUpdateDeviceAddressRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",AgentToken);
        headers.put("deviceIdentifier","OnePlus-CPH2487-b0239246dc8dc4ee");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIPuii");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","5555890134");
        body.put("mid","YMDdbW80309188492036");
        body.put("kybId","A02t97mz0v9qb288");

        Response respObj = updateDeviceAddressServices.CreateLeadUpdateAddress(createLeadUpdateDeviceAddress,body, headers);


        LOGGER.info(" Create a new lead with all valid details" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 500);

    }
    @Test(priority = 0, description = "API Response with invalid MID", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        CreateLeadUpdateDeviceAddress createLeadUpdateDeviceAddress = new CreateLeadUpdateDeviceAddress(P.TESTDATA.get("CreateLeadUpdateDeviceAddressRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",AgentToken);
        headers.put("deviceIdentifier","OnePlus-CPH2487-b0239246dc8dc4ee");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","5555890134");
        body.put("mid","YMDdbW80309188492036juu");
        body.put("kybId","A02t97mz0v9qb288");

        Response respObj = updateDeviceAddressServices.CreateLeadUpdateAddress(createLeadUpdateDeviceAddress,body, headers);

        LOGGER.info(" Create a new lead with all valid details" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "API Response with invalid token", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        CreateLeadUpdateDeviceAddress createLeadUpdateDeviceAddress = new CreateLeadUpdateDeviceAddress(P.TESTDATA.get("CreateLeadUpdateDeviceAddressRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", "abcd");
        headers.put("deviceIdentifier", "OnePlus-CPH2487-b0239246dc8dc4ee");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId", "1700547833");
        body.put("agentCustId", "1107195733");
        body.put("userMobile", "5555890134");
        body.put("mid", "YMDdbW80309188492036");
        body.put("kybId", "A02t97mz0v9qb288JHH");


        Response respObj = null;
        try {
            respObj = updateDeviceAddressServices.CreateLeadUpdateAddress(createLeadUpdateDeviceAddress, body, headers);
        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {
            LOGGER.info("API Response with invalid token" + respObj.statusCode());
            Assert.assertEquals(respObj.statusCode(), 410);
        }


    }
}
