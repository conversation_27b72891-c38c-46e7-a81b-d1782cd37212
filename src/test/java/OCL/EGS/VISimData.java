package OCL.EGS;

import Request.EGS.SimDetails;
import Request.EGS.VISIMDetails;
import Services.EGS.EGSMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class VISimData extends BaseMethod
{
	EGSMiddlewareServices egsMiddlewareServicesObject = new EGSMiddlewareServices();
	private static final Logger LOGGER = LogManager.getLogger(VISimData.class);
	Map<String, String> jwtParams = new HashMap<>();
	String Token="";


	@Test(priority = 0, description = "Sim Status will all correct params")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_001_FetchSimStatus() throws Exception {

		VISIMDetails VISIMDetails = new VISIMDetails(P.TESTDATA.get("VIsimdetailsRequest"));

		Map<String, String> jwtParams = new HashMap<>();
		Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("MSISDN", "915796328779845");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("JWT",Token);
		headers.put("Cookie", "JSESSIONID=1A9F34D484F695A62F29B709B1B39A90");


		Response respObj = egsMiddlewareServicesObject.VISIMDetailsmethod(VISIMDetails, queryParams, headers);
		LOGGER.info("Sim Status will all correct params" + respObj.statusCode());
		Assert.assertEquals(respObj.statusCode(), 400);
	}



	@Test(priority = 0, description = "Sim Status for Active sim")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_003_FetchSimStatusforActivedSIM() throws Exception {

		VISIMDetails VISIMDetails = new VISIMDetails(P.TESTDATA.get("VIsimdetailsRequest"));

		Map<String, String> jwtParams = new HashMap<>();
		Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("MSISDN", "915790263528256");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("JWT",Token);
		headers.put("Cookie", "JSESSIONID=1A9F34D484F695A62F29B709B1B39A90");


		Response respObj = egsMiddlewareServicesObject.VISIMDetailsmethod(VISIMDetails, queryParams, headers);
		LOGGER.info("Sim Status : " +  respObj.jsonPath().getString("service.basicInfo[33].value"));
		Assert.assertEquals(respObj.statusCode(), 400);

	}

	@Test(priority = 0, description = "Sim Status for Safe Custody sim")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_004_FetchSimStatusforSCSIM() throws Exception {

		VISIMDetails VISIMDetails = new VISIMDetails(P.TESTDATA.get("VIsimdetailsRequest"));

		Map<String, String> jwtParams = new HashMap<>();
		Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("MSISDN", "915796328779854");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("JWT",Token);
		headers.put("Cookie", "JSESSIONID=1A9F34D484F695A62F29B709B1B39A90");


		Response respObj = egsMiddlewareServicesObject.VISIMDetailsmethod(VISIMDetails, queryParams, headers);
		LOGGER.info("Sim Status : " +  respObj.jsonPath().getString("service.basicInfo[33].value"));
		Assert.assertEquals(respObj.statusCode(), 400);

	}


	@Test(priority = 0, description = "Sim Status for Ready state sim")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_005_FetchSimStatusforRPSIM() throws Exception {

		VISIMDetails VISIMDetails = new VISIMDetails(P.TESTDATA.get("VIsimdetailsRequest"));

		Map<String, String> jwtParams = new HashMap<>();
		Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("MSISDN", "915796328779835");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("JWT",Token);
		headers.put("Cookie", "JSESSIONID=1A9F34D484F695A62F29B709B1B39A90");


		Response respObj = egsMiddlewareServicesObject.VISIMDetailsmethod(VISIMDetails, queryParams, headers);
		LOGGER.info("Sim Status : " +  respObj.jsonPath().getString("service.basicInfo[33].value"));
		Assert.assertEquals(respObj.statusCode(), 400);

	}



	@Test(priority = 0, description = "empty cookies")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_006_emptyCookie() throws Exception {

		VISIMDetails VISIMDetails = new VISIMDetails(P.TESTDATA.get("VIsimdetailsRequest"));

//		JSESSIONID=1A9F34D484F695A62F29B709B1B39A90

		Map<String, String> jwtParams = new HashMap<>();
		Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("MSISDN", "915796328779835");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("JWT",Token);
		headers.put("Cookie", "");


		Response respObj = egsMiddlewareServicesObject.VISIMDetailsmethod(VISIMDetails, queryParams, headers);
		LOGGER.info("Sim Status : " +  respObj.jsonPath().getString("service.basicInfo[33].value"));
		Assert.assertEquals(respObj.statusCode(), 400);

	}


	@Test(priority = 0, description = "empty cookies")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_007_missingCookie() throws Exception {

		VISIMDetails VISIMDetails = new VISIMDetails(P.TESTDATA.get("VIsimdetailsRequest"));



		Map<String, String> jwtParams = new HashMap<>();
		Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("MSISDN", "915796328779835");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("JWT",Token);
		//headers.put("Cookie", "JSESSIONID=1A9F34D484F695A62F29B709B1B39A90");


		Response respObj = egsMiddlewareServicesObject.VISIMDetailsmethod(VISIMDetails, queryParams, headers);
		LOGGER.info("Sim Status : " +  respObj.jsonPath().getString("service.basicInfo[33].value"));
		Assert.assertEquals(respObj.statusCode(), 400);

	}



	@Test(priority = 0, description = "Token is invalid")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_008_invalidToken() throws Exception {

		VISIMDetails VISIMDetails = new VISIMDetails(P.TESTDATA.get("VIsimdetailsRequest"));



		Map<String, String> jwtParams = new HashMap<>();
		Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("MSISDN", "915796328779835");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("JWT","JWT Token");
		headers.put("Cookie", "JSESSIONID=1A9F34D484F695A62F29B709B1B39A90");



		Response respObj = null;
		try {
			respObj = egsMiddlewareServicesObject.VISIMDetailsmethod(VISIMDetails, queryParams, headers);
		} catch (PatternSyntaxException e) {

		}
		if (respObj != null) {

			int statusCode = respObj.getStatusCode();
			Assert.assertEquals(statusCode, 400);
		}


	}


}
