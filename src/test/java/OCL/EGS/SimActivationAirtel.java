package OCL.EGS;

import Request.EGS.AirtelSimActivation;
import Services.EGS.EGSMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class SimActivationAirtel extends BaseMethod {

    EGSMiddlewareServices egsMiddlewareServicesObject = new EGSMiddlewareServices();

    private static final Logger LOGGER = LogManager.getLogger(FetchSimStatus.class);

    String Token ="";


    @Test(priority = 0, description = "Activate Sim with valid request", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_ActivateSim() throws Exception {

        AirtelSimActivation airtelSimActivation = new AirtelSimActivation(P.TESTDATA.get("AirtelSimActivationRequest"));

        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> body = new HashMap<String, String>();
        body.put("planName", "M2ML 4G 50MB 200 SMS - INR25 D20P PM AR");
        body.put("mobileNumber", "*************");
        body.put("accountNumber", "1-*************");
        body.put("pageNo", "0");
        body.put("pageSize", "0");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.AirtelSimActivationMiddleware(airtelSimActivation, body, headers);
        LOGGER.info("Activate Sim with valid request" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
    }


    @Test(priority = 0, description = "Activate Sim with valid token", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_ActivateSim() throws Exception {

        AirtelSimActivation airtelSimActivation = new AirtelSimActivation(P.TESTDATA.get("AirtelSimActivationRequest"));

        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> body = new HashMap<String, String>();
        body.put("planName", "M2ML 4G 50MB 200 SMS - INR25 D20P PM AR");
        body.put("mobileNumber", "*************");
        body.put("accountNumber", "1-*************");
        body.put("pageNo", "0");
        body.put("pageSize", "0");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT","shshsh");


        Response respObj = null;
        try {
            respObj = egsMiddlewareServicesObject.AirtelSimActivationMiddleware(airtelSimActivation, body, headers);
        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {
            LOGGER.info("Sim Status without token" + respObj.statusCode());
            Assert.assertEquals(respObj.statusCode(), 401);
        }

    }

    @Test(priority = 0, description = "Activate Sim without token", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_ActivateSim() throws Exception {

        AirtelSimActivation airtelSimActivation = new AirtelSimActivation(P.TESTDATA.get("AirtelSimActivationRequest"));

        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> body = new HashMap<String, String>();
        body.put("planName", "M2ML 4G 50MB 200 SMS - INR25 D20P PM AR");
        body.put("mobileNumber", "*************");
        body.put("accountNumber", "1-*************");
        body.put("pageNo", "0");
        body.put("pageSize", "0");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT","");


        Response respObj = null;
        try {
            respObj = egsMiddlewareServicesObject.AirtelSimActivationMiddleware(airtelSimActivation, body, headers);
        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {
            LOGGER.info("Activate Sim without token" + respObj.statusCode());
            Assert.assertEquals(respObj.statusCode(), 401);
        }

    }

    @Test(priority = 0, description = "Activate Sim without plan name", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_ActivateSim() throws Exception {

        AirtelSimActivation airtelSimActivation = new AirtelSimActivation(P.TESTDATA.get("AirtelSimActivationRequest"));

        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> body = new HashMap<String, String>();
        body.put("planName", "");
        body.put("mobileNumber", "*************");
        body.put("accountNumber", "1-*************");
        body.put("pageNo", "0");
        body.put("pageSize", "0");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.AirtelSimActivationMiddleware(airtelSimActivation, body, headers);
        LOGGER.info("Activate Sim without plan name" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        respObj.getBody();

        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2=(JSONObject) jsObj1.get("resultInfo");

        Assert.assertEquals(jsObj2.get("resultMsg"),"Plan name can not be blank");
        Assert.assertEquals(jsObj2.get("resultCode"),"BUSINESS_EXCEPTION");
        Assert.assertEquals(jsObj2.get("resultStatus"),"F");
    }

    @Test(priority = 0, description = "Activate Sim with invalid plan name", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_ActivateSim() throws Exception {

        AirtelSimActivation airtelSimActivation = new AirtelSimActivation(P.TESTDATA.get("AirtelSimActivationRequest"));

        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> body = new HashMap<String, String>();
        body.put("planName", "M2ML 4G 50MB 200 SMS - INR25 D20P PM AR22");
        body.put("mobileNumber", "*************");
        body.put("accountNumber", "1-*************");
        body.put("pageNo", "0");
        body.put("pageSize", "0");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.AirtelSimActivationMiddleware(airtelSimActivation, body, headers);
        LOGGER.info("Activate Sim with invalid plan name" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        respObj.getBody();

        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2=(JSONObject) jsObj1.get("resultInfo");

        Assert.assertEquals(jsObj2.get("resultMsg"),"Provided Plan name is not valid");
        Assert.assertEquals(jsObj2.get("resultCode"),"BUSINESS_EXCEPTION");
        Assert.assertEquals(jsObj2.get("resultStatus"),"F");
    }

    @Test(priority = 0, description = "Activate Sim with invalid mobile number", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_ActivateSim() throws Exception {

        AirtelSimActivation airtelSimActivation = new AirtelSimActivation(P.TESTDATA.get("AirtelSimActivationRequest"));

        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> body = new HashMap<String, String>();
        body.put("planName", "M2ML 4G 50MB 200 SMS - INR25 D20P PM AR");
        body.put("mobileNumber", "*************2");
        body.put("accountNumber", "1-*************");
        body.put("pageNo", "0");
        body.put("pageSize", "0");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.AirtelSimActivationMiddleware(airtelSimActivation, body, headers);
        LOGGER.info("Activate Sim with invalid mobile number" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        respObj.getBody();

        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2=(JSONObject) jsObj1.get("resultInfo");

        Assert.assertEquals(jsObj2.get("resultMsg"),"Request Message Not Correct");
        Assert.assertEquals(jsObj2.get("resultCode"),"BUSINESS_EXCEPTION");
        Assert.assertEquals(jsObj2.get("resultStatus"),"F");
        Assert.assertEquals(jsObj2.get("resultCodeId"),"400");
    }


    @Test(priority = 0, description = "Activate Sim without mobile number", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_ActivateSim() throws Exception {

        AirtelSimActivation airtelSimActivation = new AirtelSimActivation(P.TESTDATA.get("AirtelSimActivationRequest"));

        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> body = new HashMap<String, String>();
        body.put("planName", "M2ML 4G 50MB 200 SMS - INR25 D20P PM AR");
        body.put("mobileNumber", "");
        body.put("accountNumber", "1-*************");
        body.put("pageNo", "0");
        body.put("pageSize", "0");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.AirtelSimActivationMiddleware(airtelSimActivation, body, headers);
        LOGGER.info("Activate Sim without mobile number" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        respObj.getBody();

        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2=(JSONObject) jsObj1.get("resultInfo");

        Assert.assertEquals(jsObj2.get("resultMsg"),"Mobile number can not be blank");
        Assert.assertEquals(jsObj2.get("resultCode"),"BUSINESS_EXCEPTION");
        Assert.assertEquals(jsObj2.get("resultStatus"),"F");
    }


    @Test(priority = 0, description = "Activate Sim without account number", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_ActivateSim() throws Exception {

        AirtelSimActivation airtelSimActivation = new AirtelSimActivation(P.TESTDATA.get("AirtelSimActivationRequest"));

        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> body = new HashMap<String, String>();
        body.put("planName", "M2ML 4G 50MB 200 SMS - INR25 D20P PM AR");
        body.put("mobileNumber", "*************");
        body.put("accountNumber", "");
        body.put("pageNo", "0");
        body.put("pageSize", "0");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.AirtelSimActivationMiddleware(airtelSimActivation, body, headers);
        LOGGER.info("Activate Sim without account number" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        respObj.getBody();

        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2=(JSONObject) jsObj1.get("resultInfo");

       // Assert.assertEquals(jsObj2.get("resultMsg"),"Account number can not be blank");
        Assert.assertEquals(jsObj2.get("resultCode"),"BUSINESS_EXCEPTION");
        Assert.assertEquals(jsObj2.get("resultStatus"),"F");
    }


    @Test(priority = 0, description = "Activate Sim with invalid account number", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_ActivateSim() throws Exception {

        AirtelSimActivation airtelSimActivation = new AirtelSimActivation(P.TESTDATA.get("AirtelSimActivationRequest"));

        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> body = new HashMap<String, String>();
        body.put("planName", "M2ML 4G 50MB 200 SMS - INR25 D20P PM AR");
        body.put("mobileNumber", "*************");
        body.put("accountNumber", "1-*************3");
        body.put("pageNo", "0");
        body.put("pageSize", "0");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.AirtelSimActivationMiddleware(airtelSimActivation, body, headers);
        LOGGER.info("Activate Sim with invalid account number" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        respObj.getBody();

        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2=(JSONObject) jsObj1.get("resultInfo");

        Assert.assertEquals(jsObj2.get("resultMsg"),"Account details not found for provided account number");
        Assert.assertEquals(jsObj2.get("resultCode"),"BUSINESS_EXCEPTION");
        Assert.assertEquals(jsObj2.get("resultStatus"),"F");
    }

    @Test(priority = 0, description = "Activate Sim without page size", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_ActivateSim() throws Exception {

        AirtelSimActivation airtelSimActivation = new AirtelSimActivation(P.TESTDATA.get("AirtelSimActivationRequest"));

        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> body = new HashMap<String, String>();
        body.put("planName", "M2ML 4G 50MB 200 SMS - INR25 D20P PM AR");
        body.put("mobileNumber", "*************");
        body.put("accountNumber", "1-*************");
        body.put("pageNo", "0");
        body.put("pageSize", "");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.AirtelSimActivationMiddleware(airtelSimActivation, body, headers);
        LOGGER.info("Activate Sim without page size" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "Activate Sim without page number", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_ActivateSim() throws Exception {

        AirtelSimActivation airtelSimActivation = new AirtelSimActivation(P.TESTDATA.get("AirtelSimActivationRequest"));

        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> body = new HashMap<String, String>();
        body.put("planName", "M2ML 4G 50MB 200 SMS - INR25 D20P PM AR");
        body.put("mobileNumber", "*************");
        body.put("accountNumber", "1-*************");
        body.put("pageNo", "");
        body.put("pageSize", "0");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.AirtelSimActivationMiddleware(airtelSimActivation, body, headers);
        LOGGER.info("Activate Sim without page number" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

    }





}
