package OCL.EGS;

import Request.EGS.SafeCustody;

import Services.EGS.EGSMiddlewareServices;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class SafeCustodyMovement extends BaseMethod {
    EGSMiddlewareServices egsMiddlewareServiceSafeCustodysObject = new EGSMiddlewareServices();

    private static final Logger LOGGER = LogManager.getLogger(SafeCustodyMovement.class);

    String Token ="";

    @Test(priority = 0, description = "Hit Airtel to move sim in Safe custody with all mandatory fields ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_MoveSimInSafeCustody() throws Exception {
        SafeCustody safecustody = new SafeCustody(P.TESTDATA.get("SafeCustodyRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Map<String, String> body = new HashMap<String, String>();
        body.put("accountNumber", "1-*************");
        body.put("mobileNumber", "*************");
        body.put("reason", "NETWORK_COVERAGE_ISSUE");


        Response respObj = egsMiddlewareServiceSafeCustodysObject.egssafecustodyMiddlewareServices(safecustody, body, headers);
        LOGGER.info("Sim Status will all correct params" + respObj.statusCode());

        int StatusCode=respObj.statusCode();

        if (StatusCode == 200)
        {
            Assert.assertEquals(StatusCode, 200);
        }
        if (StatusCode == 400) {
            Assert.assertEquals(StatusCode, 400);
        }

    }
    @Test(priority = 0, description = "Hit Airtel to move sim in Safe custody without Token ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_MoveSimInSafeCustody() throws Exception {
        SafeCustody safecustody = new SafeCustody(P.TESTDATA.get("SafeCustodyRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT","");

        Map<String, String> body = new HashMap<String, String>();
        body.put("accountNumber", "1-*************");
        body.put("mobileNumber", "*************");
        body.put("reason", "NETWORK_COVERAGE_ISSUE");

        Response respObj = null;

        try {
            respObj = egsMiddlewareServiceSafeCustodysObject.egssafecustodyMiddlewareServices(safecustody, body, headers);
        }

        catch (PatternSyntaxException e) {

        }

        if (respObj != null) {
            LOGGER.info("Sim Status will all correct params" + respObj.statusCode());
            Assert.assertEquals(respObj.statusCode(), 401);
        }
    }
    @Test(priority = 0, description = "Hit Airtel to move sim in Safe custody without Account Number ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_MoveSimInSafeCustody() throws Exception {
        SafeCustody safecustody = new SafeCustody(P.TESTDATA.get("SafeCustodyRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Map<String, String> body = new HashMap<String, String>();
        body.put("accountNumber", "");
        body.put("mobileNumber", "*************");
        body.put("reason", "NETWORK_COVERAGE_ISSUE");


        Response respObj = egsMiddlewareServiceSafeCustodysObject.egssafecustodyMiddlewareServices(safecustody, body, headers);
        LOGGER.info("Sim Status will all correct params" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");
        Assert.assertEquals(jsObj2.get("resultStatus"), "F");
        Assert.assertEquals(jsObj2.get("resultCode"), "BUSINESS_EXCEPTION");
       // Assert.assertEquals(jsObj2.get("resultMsg"), "Account number can not be blank");

    }
    @Test(priority = 0, description = "Hit Airtel to move sim in Safe custody with incorrect value of account number ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_MoveSimInSafeCustody() throws Exception {
        SafeCustody safecustody = new SafeCustody(P.TESTDATA.get("SafeCustodyRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Map<String, String> body = new HashMap<String, String>();
        body.put("accountNumber", "45677");
        body.put("mobileNumber", "*************");
        body.put("reason", "NETWORK_COVERAGE_ISSUE");


        Response respObj = egsMiddlewareServiceSafeCustodysObject.egssafecustodyMiddlewareServices(safecustody, body, headers);
        LOGGER.info("Sim Status will all correct params" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");
        Assert.assertEquals(jsObj2.get("resultStatus"), "F");
        Assert.assertEquals(jsObj2.get("resultCode"), "BUSINESS_EXCEPTION");
        Assert.assertEquals(jsObj2.get("resultMsg"), "Account details not found for provided account number");

    }
    @Test(priority = 0, description = "Hit Airtel to move sim in Safe custody without mobile number ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_MoveSimInSafeCustody() throws Exception {
        SafeCustody safecustody = new SafeCustody(P.TESTDATA.get("SafeCustodyRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Map<String, String> body = new HashMap<String, String>();
        body.put("accountNumber", "1-*************");
        body.put("mobileNumber", "");
        body.put("reason", "NETWORK_COVERAGE_ISSUE");


        Response respObj = egsMiddlewareServiceSafeCustodysObject.egssafecustodyMiddlewareServices(safecustody, body, headers);
        LOGGER.info("Sim Status will all correct params" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");
        Assert.assertEquals(jsObj2.get("resultStatus"), "F");
        Assert.assertEquals(jsObj2.get("resultCode"), "BUSINESS_EXCEPTION");
        Assert.assertEquals(jsObj2.get("resultMsg"), "Mobile number can not be blank");

    }

    @Test(priority = 0, description = "Hit Airtel to move sim in Safe custody with incorrect  mobile number ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_MoveSimInSafeCustody() throws Exception {
        SafeCustody safecustody = new SafeCustody(P.TESTDATA.get("SafeCustodyRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Map<String, String> body = new HashMap<String, String>();
        body.put("accountNumber", "1-*************");
        body.put("mobileNumber", "78990");
        body.put("reason", "NETWORK_COVERAGE_ISSUE");


        Response respObj = egsMiddlewareServiceSafeCustodysObject.egssafecustodyMiddlewareServices(safecustody, body, headers);
        LOGGER.info("Sim Status will all correct params" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");
        Assert.assertEquals(jsObj2.get("resultStatus"), "F");
        Assert.assertEquals(jsObj2.get("resultCode"), "BUSINESS_EXCEPTION");
        Assert.assertEquals(jsObj2.get("resultCodeId"), "400");
        Assert.assertEquals(jsObj2.get("resultMsg"), "Request Message Not Correct");

    }
    @Test(priority = 0, description = "Hit Airtel to move sim in Safe custody without reason ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_MoveSimInSafeCustody() throws Exception {
        SafeCustody safecustody = new SafeCustody(P.TESTDATA.get("SafeCustodyRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Map<String, String> body = new HashMap<String, String>();
        body.put("accountNumber", "1-*************");
        body.put("mobileNumber", "*************");
        body.put("reason", "");


        Response respObj = egsMiddlewareServiceSafeCustodysObject.egssafecustodyMiddlewareServices(safecustody, body, headers);
        LOGGER.info("Sim Status will all correct params" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");
        Assert.assertEquals(jsObj2.get("resultStatus"), "F");
        Assert.assertEquals(jsObj2.get("resultCode"), "BUSINESS_EXCEPTION");
        Assert.assertEquals(jsObj2.get("resultCodeId"), "400");
        Assert.assertEquals(jsObj2.get("resultMsg"), "Request Message Not Correct");

    }

    @Test(priority = 0, description = "Hit Airtel to move sim in Safe custody with incorrect reason ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_MoveSimInSafeCustody() throws Exception {
        SafeCustody safecustody = new SafeCustody(P.TESTDATA.get("SafeCustodyRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Map<String, String> body = new HashMap<String, String>();
        body.put("accountNumber", "1-*************");
        body.put("mobileNumber", "*************");
        body.put("reason", "disconnected");


        Response respObj = egsMiddlewareServiceSafeCustodysObject.egssafecustodyMiddlewareServices(safecustody, body, headers);
        LOGGER.info("Sim Status will all correct params" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");
        Assert.assertEquals(jsObj2.get("resultStatus"), "F");
        Assert.assertEquals(jsObj2.get("resultCode"), "BUSINESS_EXCEPTION");
        Assert.assertEquals(jsObj2.get("resultCodeId"), "400");
        Assert.assertEquals(jsObj2.get("resultMsg"), "Request Message Not Correct");

    }

}
