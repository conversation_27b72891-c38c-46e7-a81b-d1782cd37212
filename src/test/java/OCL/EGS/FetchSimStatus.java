package OCL.EGS;

import Request.EGS.SimDetails;
import Services.EGS.EGSMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class FetchSimStatus extends BaseMethod {
    EGSMiddlewareServices egsMiddlewareServicesObject = new EGSMiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(FetchSimStatus.class);
    String Token ="";


    @Test(priority = 0, description = "Sim Status will all correct params", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_FetchSimStatus() throws Exception {
        SimDetails simdetails = new SimDetails(P.TESTDATA.get("SimDetailsRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mobileNumber", "*************");
        queryParam.put("accountNumber", "1-*************");
        queryParam.put("filterValue", "*************");
        queryParam.put("pageNo", "0");
        queryParam.put("pageSize", "0");
        queryParam.put("simFilterType", "MSISDN");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.egsMiddlewareServices(simdetails, queryParam, headers);
        LOGGER.info("Sim Status will all correct params" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);
    }
    @Test(priority = 0, description = "Sim Status without token", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_FetchSimStatus() throws Exception {
        SimDetails simdetails = new SimDetails(P.TESTDATA.get("SimDetailsRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mobileNumber", "*************");
        queryParam.put("accountNumber", "1-*************");
        queryParam.put("filterValue", "*************");
        queryParam.put("pageNo", "0");
        queryParam.put("pageSize", "0");
        queryParam.put("simFilterType", "MSISDN");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT","");

        Response respObj = null;
        try {
            respObj = egsMiddlewareServicesObject.egsMiddlewareServices(simdetails, queryParam, headers);

        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {
            LOGGER.info("Sim Status without token" + respObj.statusCode());
            Assert.assertEquals(respObj.statusCode(), 401);
        }
    }
    @Test(priority = 0, description = "Sim Status without mobileNumber", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_FetchSimStatus() throws Exception {
        SimDetails simdetails = new SimDetails(P.TESTDATA.get("SimDetailsRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mobileNumber", "");
        queryParam.put("accountNumber", "1-*************");
        queryParam.put("filterValue", "*************");
        queryParam.put("pageNo", "0");
        queryParam.put("pageSize", "0");
        queryParam.put("simFilterType", "MSISDN");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.egsMiddlewareServices(simdetails, queryParam, headers);
        LOGGER.info("Sim Status without mobileNumber" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
        respObj.getBody();

        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");
        Assert.assertEquals(jsObj2.get("resultStatus"), "F");
        Assert.assertEquals(jsObj2.get("resultCode"), "BUSINESS_EXCEPTION");
        Assert.assertEquals(jsObj2.get("resultMsg"), "Mobile number can not be blank");
    }
    @Test(priority = 0, description = "Sim Status without Accountnumber", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_FetchSimStatus() throws Exception {
        SimDetails simdetails = new SimDetails(P.TESTDATA.get("SimDetailsRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mobileNumber", "*************");
        queryParam.put("accountNumber", "");
        queryParam.put("filterValue", "*************");
        queryParam.put("pageNo", "0");
        queryParam.put("pageSize", "0");
        queryParam.put("simFilterType", "MSISDN");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.egsMiddlewareServices(simdetails, queryParam, headers);
        LOGGER.info("Sim Status without Accountnumber" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
        respObj.getBody();

        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");
        Assert.assertEquals(jsObj2.get("resultStatus"), "F");
        Assert.assertEquals(jsObj2.get("resultCode"), "BUSINESS_EXCEPTION");
       // Assert.assertEquals(jsObj2.get("resultMsg"), "Account number can not be blank");
    }
    @Test(priority = 0, description = "Sim Status without filtervalue", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_FetchSimStatus() throws Exception {
        SimDetails simdetails = new SimDetails(P.TESTDATA.get("SimDetailsRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mobileNumber", "*************");
        queryParam.put("accountNumber", "1-*************");
        queryParam.put("filterValue", "");
        queryParam.put("pageNo", "0");
        queryParam.put("pageSize", "0");
        queryParam.put("simFilterType", "MSISDN");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.egsMiddlewareServices(simdetails, queryParam, headers);
        LOGGER.info("Sim Status without filtervalue" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);
    }

    @Test(priority = 0, description = "Sim Status without simFilterType", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_FetchSimStatus() throws Exception {
        SimDetails simdetails = new SimDetails(P.TESTDATA.get("SimDetailsRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mobileNumber", "*************");
        queryParam.put("accountNumber", "1-*************");
        queryParam.put("filterValue", "*************");
        queryParam.put("pageNo", "0");
        queryParam.put("pageSize", "0");
        queryParam.put("simFilterType", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.egsMiddlewareServices(simdetails, queryParam, headers);
        LOGGER.info("Sim Status without simFilterType" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
    }
    @Test(priority = 0, description = "Sim Status with incorrect mobile number", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_FetchSimStatus() throws Exception {
        SimDetails simdetails = new SimDetails(P.TESTDATA.get("SimDetailsRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mobileNumber", "*************");
        queryParam.put("accountNumber", "1-*************23");
        queryParam.put("filterValue", "*************");
        queryParam.put("pageNo", "0");
        queryParam.put("pageSize", "0");
        queryParam.put("simFilterType", "MSISDN");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.egsMiddlewareServices(simdetails, queryParam, headers);
        LOGGER.info("Sim Status without simFilterType" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");
        Assert.assertEquals(jsObj2.get("resultStatus"), "F");
        Assert.assertEquals(jsObj2.get("resultCode"), "BUSINESS_EXCEPTION");
        Assert.assertEquals(jsObj2.get("resultMsg"), "Account details not found for provided account number");
    }

    @Test(priority = 0, description = "Sim Status with incorrect filtervalue", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_FetchSimStatus() throws Exception {
        SimDetails simdetails = new SimDetails(P.TESTDATA.get("SimDetailsRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mobileNumber", "*************");
        queryParam.put("accountNumber", "1-*************");
        queryParam.put("filterValue", "***************");
        queryParam.put("pageNo", "0");
        queryParam.put("pageSize", "0");
        queryParam.put("simFilterType", "MSISDN");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.egsMiddlewareServices(simdetails, queryParam, headers);
        LOGGER.info("Sim Status with incorrect filtervalue" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);
    }

    @Test(priority = 0, description = "Sim Status with incorrect simFilterType", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_FetchSimStatus() throws Exception {
        SimDetails simdetails = new SimDetails(P.TESTDATA.get("SimDetailsRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mobileNumber", "*************");
        queryParam.put("accountNumber", "1-*************");
        queryParam.put("filterValue", "***************");
        queryParam.put("pageNo", "0");
        queryParam.put("pageSize", "0");
        queryParam.put("simFilterType", "MSISDNsw");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.egsMiddlewareServices(simdetails, queryParam, headers);
        LOGGER.info("Sim Status with incorrect simFilterType" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");
        Assert.assertEquals(jsObj2.get("resultStatus"), "F");
        Assert.assertEquals(jsObj2.get("resultCode"), "BUSINESS_EXCEPTION");
        Assert.assertEquals(jsObj2.get("resultCodeId"), "400");
        Assert.assertEquals(jsObj2.get("resultMsg"), "Request Message Not Correct");
    }
    @Test(priority = 0, description = "Sim Status without pageno. and size", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_FetchSimStatus() throws Exception {
        SimDetails simdetails = new SimDetails(P.TESTDATA.get("SimDetailsRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mobileNumber", "*************");
        queryParam.put("accountNumber", "1-*************");
        queryParam.put("filterValue", "*************");
        queryParam.put("pageNo", "");
        queryParam.put("pageSize", "");
        queryParam.put("simFilterType", "MSISDN");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Response respObj = egsMiddlewareServicesObject.egsMiddlewareServices(simdetails, queryParam, headers);
        LOGGER.info("Sim Status without pageno. and size" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
    }


}
