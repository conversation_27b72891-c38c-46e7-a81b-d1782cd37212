package OCL.EGS;

import Request.EGS.SafeCustody;
import Request.EGS.VISimSafeCustody;
import Services.EGS.EGSMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class SafeCustodyVI extends BaseMethod {

    EGSMiddlewareServices egsViMiddlewareServiceSafeCustodysObject = new EGSMiddlewareServices();

    private static final Logger LOGGER = LogManager.getLogger(SafeCustodyVI.class);

    String Token ="";

    @Test(priority = 0, description = "Hit Vi to move sim in Safe custody with all mandatory fields ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_MoveSimInSafeCustodyVI() throws Exception {
        VISimSafeCustody VISimSafeCustody = new VISimSafeCustody(P.TESTDATA.get("VISimSafeCustody"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);
        headers.put("Cookie" ,"JSESSIONID=1A9F34D484F695A62F29B709B1B39A90");

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "P0-12");
        body.put("serviceId", "915755044001060");



        Response respObj = egsViMiddlewareServiceSafeCustodysObject.egsvisafecustodyMiddlewareServices(VISimSafeCustody, body, headers);
        LOGGER.info("Hit Vi to move sim in Safe custody with all mandatory fields " + respObj.statusCode());

        int StatusCode=respObj.statusCode();

        if (StatusCode == 200)
        {
            Assert.assertEquals(StatusCode, 200);
        }
        if (StatusCode == 400) {
            Assert.assertEquals(StatusCode, 400);
        }

    }

    @Test(priority = 0, description = "Hit VI to move sim in Safe custody with invalid Token ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_MoveSimInSafeCustodyVI() throws Exception {
        VISimSafeCustody VISimSafeCustody = new VISimSafeCustody(P.TESTDATA.get("VISimSafeCustody"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT","agahha");
        headers.put("Cookie" ,"JSESSIONID=1A9F34D484F695A62F29B709B1B39A90");

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "P0-12");
        body.put("serviceId", "915755044001060");

        Response respObj = null;

        try {
            respObj =  egsViMiddlewareServiceSafeCustodysObject.egsvisafecustodyMiddlewareServices(VISimSafeCustody, body, headers);
        }

        catch (PatternSyntaxException e) {

        }

        if (respObj != null) {
            LOGGER.info("Hit VI to move sim in Safe custody with invalid Token" + respObj.statusCode());
            Assert.assertEquals(respObj.statusCode(), 401);
        }

    }

    @Test(priority = 0, description = "Hit VI to move sim in Safe custody without Token ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_MoveSimInSafeCustodyVI() throws Exception {
        VISimSafeCustody VISimSafeCustody = new VISimSafeCustody(P.TESTDATA.get("VISimSafeCustody"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT","");
        headers.put("Cookie" ,"JSESSIONID=1A9F34D484F695A62F29B709B1B39A90");

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "P0-12");
        body.put("serviceId", "915755044001060");

        Response respObj = null;

        try {
            respObj =  egsViMiddlewareServiceSafeCustodysObject.egsvisafecustodyMiddlewareServices(VISimSafeCustody, body, headers);
        }

        catch (PatternSyntaxException e) {

        }

        if (respObj != null) {
            LOGGER.info("Hit VI to move sim in Safe custody without Token " + respObj.statusCode());
            Assert.assertEquals(respObj.statusCode(), 401);
        }

    }

    @Test(priority = 0, description = "Hit Vi to move sim in Safe custody without cookie ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_MoveSimInSafeCustodyVI() throws Exception {
        VISimSafeCustody VISimSafeCustody = new VISimSafeCustody(P.TESTDATA.get("VISimSafeCustody"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);
        headers.put("Cookie" ,"");

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "P0-12");
        body.put("serviceId", "915755044001060");



        Response respObj = egsViMiddlewareServiceSafeCustodysObject.egsvisafecustodyMiddlewareServices(VISimSafeCustody, body, headers);
        LOGGER.info("Hit Vi to move sim in Safe custody without cookie " + respObj.statusCode());

        int StatusCode=respObj.statusCode();


            Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, description = "Hit Vi to move sim in Safe custody with invalid cookie ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_MoveSimInSafeCustodyVI() throws Exception {
        VISimSafeCustody VISimSafeCustody = new VISimSafeCustody(P.TESTDATA.get("VISimSafeCustody"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);
        headers.put("Cookie" ,"44444");

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "P0-12");
        body.put("serviceId", "915755044001060");



        Response respObj = egsViMiddlewareServiceSafeCustodysObject.egsvisafecustodyMiddlewareServices(VISimSafeCustody, body, headers);
        LOGGER.info("Hit Vi to move sim in Safe custody with invalid cookie " + respObj.statusCode());

        int StatusCode=respObj.statusCode();


        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, description = "Hit Vi to move sim in Safe custody with invalid lead id ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_MoveSimInSafeCustodyVI() throws Exception {
        VISimSafeCustody VISimSafeCustody = new VISimSafeCustody(P.TESTDATA.get("VISimSafeCustody"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);
        headers.put("Cookie" ,"JSESSIONID=1A9F34D484F695A62F29B709B1B39A90");

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "78999");
        body.put("serviceId", "915755044001060");



        Response respObj = egsViMiddlewareServiceSafeCustodysObject.egsvisafecustodyMiddlewareServices(VISimSafeCustody, body, headers);
        LOGGER.info("Hit Vi to move sim in Safe custody with invalid lead id " + respObj.statusCode());

        int StatusCode=respObj.statusCode();


        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, description = "Hit Vi to move sim in Safe custody without  lead id ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_MoveSimInSafeCustodyVI() throws Exception {
        VISimSafeCustody VISimSafeCustody = new VISimSafeCustody(P.TESTDATA.get("VISimSafeCustody"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);
        headers.put("Cookie" ,"JSESSIONID=1A9F34D484F695A62F29B709B1B39A90");

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "");
        body.put("serviceId", "915755044001060");



        Response respObj = egsViMiddlewareServiceSafeCustodysObject.egsvisafecustodyMiddlewareServices(VISimSafeCustody, body, headers);
        LOGGER.info("Hit Vi to move sim in Safe custody without lead id " + respObj.statusCode());

        int StatusCode=respObj.statusCode();


        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, description = "Hit Vi to move sim in Safe custody with invalid service id", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_MoveSimInSafeCustodyVI() throws Exception {
        VISimSafeCustody VISimSafeCustody = new VISimSafeCustody(P.TESTDATA.get("VISimSafeCustody"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);
        headers.put("Cookie" ,"JSESSIONID=1A9F34D484F695A62F29B709B1B39A90");

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "78999");
        body.put("serviceId", "91575504400106011");



        Response respObj = egsViMiddlewareServiceSafeCustodysObject.egsvisafecustodyMiddlewareServices(VISimSafeCustody, body, headers);
        LOGGER.info("Hit Vi to move sim in Safe custody with invalid service id " + respObj.statusCode());

        int StatusCode=respObj.statusCode();


        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, description = "Hit Vi to move sim in Safe custody without service id", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_MoveSimInSafeCustodyVI() throws Exception {
        VISimSafeCustody VISimSafeCustody = new VISimSafeCustody(P.TESTDATA.get("VISimSafeCustody"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);
        headers.put("Cookie" ,"JSESSIONID=1A9F34D484F695A62F29B709B1B39A90");

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "78999");
        body.put("serviceId", "");



        Response respObj = egsViMiddlewareServiceSafeCustodysObject.egsvisafecustodyMiddlewareServices(VISimSafeCustody, body, headers);
        LOGGER.info("Hit Vi to move sim in Safe custody without service id " + respObj.statusCode());

        int StatusCode=respObj.statusCode();


        Assert.assertEquals(StatusCode, 400);
    }
}


