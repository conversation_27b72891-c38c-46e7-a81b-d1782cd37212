package OCL.EGS;

import Request.EGS.VISimActivation;
import Services.EGS.EGSMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class SimActivationVI extends BaseMethod {

    EGSMiddlewareServices VISimActivationServicesObject = new EGSMiddlewareServices();

    private static final Logger LOGGER = LogManager.getLogger(SafeCustodyMovement.class);

    String Token ="";

    @Test(priority = 0, description = "Hit API with correct details ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_SimActivationVI() throws Exception {
        VISimActivation viSimActivation =new VISimActivation(P.TESTDATA.get("VISimActivationRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "P0-12");
        body.put("serviceId", "915796328779848");
        body.put("basePlanName", "ACTIVE_PLAN_B_LCC");


        Response respObj = VISimActivationServicesObject.VISimActivationServices(viSimActivation, body, headers);
        LOGGER.info("Hit API with correct details: " + respObj.statusCode());

        int StatusCode=respObj.statusCode();

        if (StatusCode == 200)
        {
            Assert.assertEquals(StatusCode, 200);
        }
        if (StatusCode == 400) {
            Assert.assertEquals(StatusCode, 400);
        }

    }

    @Test(priority = 0, description = "Hit API without Token ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_SimActivationVI() throws Exception {
        VISimActivation viSimActivation =new VISimActivation(P.TESTDATA.get("VISimActivationRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT","");

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "P0-12");
        body.put("serviceId", "915796328779848");
        body.put("basePlanName", "ACTIVE_PLAN_B_LCC");


        Response respObj = null;

        try {
            respObj = VISimActivationServicesObject.VISimActivationServices(viSimActivation, body, headers);
        }

        catch (PatternSyntaxException e) {

        }

        if (respObj != null) {
            LOGGER.info("Hit API without Token: " + respObj.statusCode());
            Assert.assertEquals(respObj.statusCode(), 401);
        }
    }

    @Test(priority = 0, description = "Hit API with invalid Token ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_SimActivationVI() throws Exception {
        VISimActivation viSimActivation =new VISimActivation(P.TESTDATA.get("VISimActivationRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT","fagaha");

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "P0-12");
        body.put("serviceId", "915796328779848");
        body.put("basePlanName", "ACTIVE_PLAN_B_LCC");


        Response respObj = null;

        try {
            respObj = VISimActivationServicesObject.VISimActivationServices(viSimActivation, body, headers);
        }

        catch (PatternSyntaxException e) {

        }

        if (respObj != null) {
            LOGGER.info("Hit API with invalid Token: " + respObj.statusCode());
            Assert.assertEquals(respObj.statusCode(), 401);
        }
    }

    @Test(priority = 0, description = "Hit API without LeadID", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_SimActivationVI() throws Exception {
        VISimActivation viSimActivation =new VISimActivation(P.TESTDATA.get("VISimActivationRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "");
        body.put("serviceId", "915796328779848");
        body.put("basePlanName", "ACTIVE_PLAN_B_LCC");

        Response respObj = VISimActivationServicesObject.VISimActivationServices(viSimActivation, body, headers);
        LOGGER.info("Hit API without LeadID: " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "Hit API with invalid LeadID", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_SimActivationVI() throws Exception {
        VISimActivation viSimActivation =new VISimActivation(P.TESTDATA.get("VISimActivationRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "derr");
        body.put("serviceId", "915796328779848");
        body.put("basePlanName", "ACTIVE_PLAN_B_LCC");

        Response respObj = VISimActivationServicesObject.VISimActivationServices(viSimActivation, body, headers);
        LOGGER.info("Hit API with invalid LeadID: " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "Hit API without serviceId", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_SimActivationVI() throws Exception {
        VISimActivation viSimActivation =new VISimActivation(P.TESTDATA.get("VISimActivationRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "P0-12");
        body.put("serviceId", "");
        body.put("basePlanName", "ACTIVE_PLAN_B_LCC");

        Response respObj = VISimActivationServicesObject.VISimActivationServices(viSimActivation, body, headers);
        LOGGER.info("Hit API without serviceId: " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "Hit API with invalid serviceId", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_SimActivationVI() throws Exception {
        VISimActivation viSimActivation =new VISimActivation(P.TESTDATA.get("VISimActivationRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "P0-12");
        body.put("serviceId", "34443");
        body.put("basePlanName", "ACTIVE_PLAN_B_LCC");

        Response respObj = VISimActivationServicesObject.VISimActivationServices(viSimActivation, body, headers);
        LOGGER.info("Hit API with invalid serviceId: " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "Hit API without base plan name", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_SimActivationVI() throws Exception {
        VISimActivation viSimActivation =new VISimActivation(P.TESTDATA.get("VISimActivationRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "P0-12");
        body.put("serviceId", "915796328779848");
        body.put("basePlanName", "");

        Response respObj = VISimActivationServicesObject.VISimActivationServices(viSimActivation, body, headers);
        LOGGER.info("Hit API without base plan name: " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "Hit API with invalid plan name", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_SimActivationVI() throws Exception {
        VISimActivation viSimActivation =new VISimActivation(P.TESTDATA.get("VISimActivationRequest"));
        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("JWT",Token);

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", "P0-12");
        body.put("serviceId", "915796328779848");
        body.put("basePlanName", "22333");

        Response respObj = VISimActivationServicesObject.VISimActivationServices(viSimActivation, body, headers);
        LOGGER.info("Hit API with invalid plan name: " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

    }



}
