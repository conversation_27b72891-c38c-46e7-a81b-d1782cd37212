package OCL.EGS;

import Request.EGS.VIOrderDetails;
import Request.EGS.VISIMDetails;
import Services.EGS.EGSMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class OrderDetailsVI extends BaseMethod {

    EGSMiddlewareServices egsViMiddlewareServiceOrderDetailsObject = new EGSMiddlewareServices();

    private static final Logger LOGGER = LogManager.getLogger(OrderDetailsVI.class);

    String Token="";

    @Test(priority = 0, description = "Sim Status will all correct params")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_FetchVISimOrderDetails() throws Exception {

        VIOrderDetails VIOrderDetails = new VIOrderDetails(P.TESTDATA.get("VIOrderDetails"));

        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("orderId", "1145643039504056320");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("JWT",Token);
       // headers.put("Cookie", "JSESSIONID=1A9F34D484F695A62F29B709B1B39A90");


        Response respObj = egsViMiddlewareServiceOrderDetailsObject.VIOrderDetailssmethod(VIOrderDetails, queryParams, headers);
        LOGGER.info("Sim Status will all correct params" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
    }

    @Test(priority = 0, description = "Sim order details without order id")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_FetchVISimOrderDetails() throws Exception {

        VIOrderDetails VIOrderDetails = new VIOrderDetails(P.TESTDATA.get("VIOrderDetails"));

        Map<String, String> jwtParams = new HashMap<>();
        Token=generateJwtTokenEGS("zZrq0sZK1yt9RJk51RTJ/jeU6WERbvr8nqKMWQJRX1E=", "OE",jwtParams,true );

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("orderId", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("JWT",Token);
        // headers.put("Cookie", "JSESSIONID=1A9F34D484F695A62F29B709B1B39A90");


        Response respObj = egsViMiddlewareServiceOrderDetailsObject.VIOrderDetailssmethod(VIOrderDetails, queryParams, headers);
        LOGGER.info("Sim order details without order id" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
    }
}
