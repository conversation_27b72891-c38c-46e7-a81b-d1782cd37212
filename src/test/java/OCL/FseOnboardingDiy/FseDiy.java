package OCL.FseOnboardingDiy;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.Cart.PinCode;
import Request.MerchantService.v1.Resources.LanguagePreference;
import Request.MerchantService.v1.fseDiy.*;
import Request.MerchantService.v2.Banks;
import Request.MerchantService.v2.TnC;
import Request.MerchantService.v3.*;
import Request.UAD.Category;
import Request.UAD.SubCategory;
import Request.Wallet.CreateUserWallet;
import Services.MechantService.MiddlewareServices;
import Services.UAD.UADServices;
import Services.Utilities.Utilities;
import Services.Wallet.WalletServices;
import Services.oAuth.oAuthServices;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSchException;
import com.opencsv.CSVWriter;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FseDiy extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(FseDiy.class);
    private static final String NAME_AS_PER_PAN = "Puneet kumar";
    public static  String merchant_dob = "07/24/1963";
    public static  String gender = "male";
    public static  String aadhar_not_readable = "true";
    public static String aadhar_ref_no = "************";
    public static String screen_Name = "aadhar_not_present_page";
    public static String partial_Save = "true";

    public static String requestPathAadhar = "MerchantService/V1/Fse/Diy/Request/SubmitLeadDetailRequest.json";
    public static String requestPathPan = "MerchantService/V1/Fse/Diy/Request/SubmitLeadDetailRequestWithPan.json";
    public static String requestPathHomeAddress = "MerchantService/V1/Fse/Diy/Request/AddHomeAddressRequest.json";

    public static String NAME_AS_PER_AADHAR ="Puneet Kumar" ;


    public static String AgentToken = "";
    public static  String AgentNo = "7011653794";
    public static  String version = "5.1.6";
    public static String mobileNo = "9650376491";
    public static String FSEMobileNumber = "9650376491";


    public static String newState = "";
    public static String OTP = "888888";
    public static String nameMatchStatus = "";
    public static String CustId = "";
    public static String emptyCustID = "";
    public static String noLeadCustID = "1000527769";
    public static String noOauthCustID = "10000001";
    public static String XMWToken = "";
    public static String leadId = "";
    public static String MID = "";

    Faker GenerateFake = new Faker();
    public String lineOne = GenerateFake.address().streetAddress();
    public String lineTwo = GenerateFake.address().cityName();
    public String lineThree = GenerateFake.address().streetName();

    public static String WorkFlowId = "";
    public static String addressUuid = "";
    public static String rrbUuid = "";
    public static String ownerAddressUUID = "";
    public static List<String> docsToUpload = new ArrayList<>();

    public static List<Object> DMS = new ArrayList<>();
    public static List<Object> DMSforUUID = new ArrayList<>();
    public static List<Object> DocType = new ArrayList<>();
    public static List<Object> DocProvided = new ArrayList<>();
    public static List<Object> DocumentRequest = new ArrayList<>();
    public static List<Object> NameOfDoc = new ArrayList<>();
    public static List<Object> TypeOfDoc = new ArrayList<>();
    List <Object> GetDocuments = new ArrayList<>();

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    UADServices uadServicesObject = new UADServices();
    WalletServices walletServices = new WalletServices();
    Services.oAuth.oAuthServices oAuthServices = new oAuthServices();

    TnC GetTnC = new TnC();
    GetDocStatus v3GetDocStat = new GetDocStatus();
    LanguagePreference getLanguage = new LanguagePreference();


    @BeforeClass
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void AgentFseDiyLogin()
    {
        //AgentToken=AgentSessionToken("9953828631","paytm@123");
        // AgentToken = CommonAgentToken;
        AgentToken = ApplicantToken(FSEMobileNumber, "paytm@123");
        // CA9277571643  9650376490
        LOGGER.info("FSE diy Agent Token is : " + AgentToken);

        //CustId = oAuthServices.getoAuthCustId(FSEMobileNumber);


    }


    //V3 Send OTP API

    @Test(description = "Send otp with Empty Mobile Number",priority = 0)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyMobileSendOTP()
    {
        SendOtpFseDiy SendOTPobj = new SendOtpFseDiy();


        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Response ResObject = middlewareServicesObject.v3SentOtpFseDiy(SendOTPobj,"INDIVIDUAL","fse_diy",AgentToken,version,"","fse_diy",  "1701277853");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,400);
    }

    @Test(description = "Mobile number is passed less then 10 digits")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectMobileSendOTP()
    {
        SendOtpFseDiy SendOTPobj = new SendOtpFseDiy();


        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Response ResObject = middlewareServicesObject.v3SentOtpFseDiy(SendOTPobj,"INDIVIDUAL","fse_diy",AgentToken,version,"671287129","fse_diy",  "1701277853");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,400);
    }

    @Test(description = "Where User Type is invalid",priority = 0)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectUserTypeSendOTP()
    {
        SendOtpFseDiy SendOTPobj = new SendOtpFseDiy();


        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Response ResObject = middlewareServicesObject.v3SentOtpFseDiy(SendOTPobj,"INDIVIDUAL","fse_diy",AgentToken,version,FSEMobileNumber,"fsiy",  "1701277853");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);
    }

    @Test(description = "Where User Type is of different solution type")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectSolutionUserTypeSendOTP()
    {
        SendOtpFseDiy SendOTPobj = new SendOtpFseDiy();
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Response ResObject = middlewareServicesObject.v3SentOtpFseDiy(SendOTPobj,"INDIVIDUAL","fse_diy",AgentToken,version,FSEMobileNumber,"company",  "1701277853");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,500);
    }
    @Test(description = "Where entity type is invalid")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidEntityTypeSendOTP()
    {
        SendOtpFseDiy SendOTPobj = new SendOtpFseDiy();


        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Response ResObject = middlewareServicesObject.v3SentOtpFseDiy(SendOTPobj,"IND","fse_diy",AgentToken,version,FSEMobileNumber,"fse_diy",  "1701277853");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);
    }

    @Test(description = "Where entity type is empty")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyEntityTypeSendOTP()
    {
        SendOtpFseDiy SendOTPobj = new SendOtpFseDiy();


        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Response ResObject = middlewareServicesObject.v3SentOtpFseDiy(SendOTPobj,"","fse_diy",AgentToken,version,FSEMobileNumber,"fse_diy",  "1701277853");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);
    }

    @Test(description = "Where entity type is different")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffEntityTypeSendOTP()
    {
        SendOtpFseDiy SendOTPobj = new SendOtpFseDiy();


        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Response ResObject = middlewareServicesObject.v3SentOtpFseDiy(SendOTPobj,"PUBLIC_LIMITED","fse_diy",AgentToken,version,FSEMobileNumber,"fse_diy",  "1701277853");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);
    }

    @Test(description = "Where solution type is invalid",priority = 0)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidSolutionTypeSendOTP()
    {
        SendOtpFseDiy SendOTPobj = new SendOtpFseDiy();


        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Response ResObject = middlewareServicesObject.v3SentOtpFseDiy(SendOTPobj,"INDIVIDUAL","fse_di",AgentToken,version,FSEMobileNumber,"fse_diy",  "1701277853");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,500);
    }


    @Test(description = "Where solution type is different",priority = 0)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffSolutionTypeSendOTP()
    {
        SendOtpFseDiy SendOTPobj = new SendOtpFseDiy();


        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Response ResObject = middlewareServicesObject.v3SentOtpFseDiy(SendOTPobj,"INDIVIDUAL","company_onboard",AgentToken,version,FSEMobileNumber,"fse_diy",  "1701277853");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);
    }

    @Test(description = "Send OTP on Mobile Number")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void fseDiyPositiveSendOTP()
    {
        SendOtpFseDiy SendOTPobj = new SendOtpFseDiy();
      //  String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Response ResObject = middlewareServicesObject.v3SentOtpFseDiy(SendOTPobj,"INDIVIDUAL","fse_diy",AgentToken,version,FSEMobileNumber,"fse_diy","1701277853");
        String actualMsg = ResObject.jsonPath().getString("message");
        newState = ResObject.jsonPath().getString("state");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);
    }


    @Test(description = "Positive ValidateOTP")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveValidateOtp() throws IOException, JSchException, InterruptedException {
     //   fseDiyPositiveSendOTP();

        SendOtpFseDiy SendOTPobj = new SendOtpFseDiy();
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Response ResObject = middlewareServicesObject.v3SentOtpFseDiy(SendOTPobj,"INDIVIDUAL","fse_diy",AgentToken,version,FSEMobileNumber,"fse_diy","1701277853");
        String actualMsg = ResObject.jsonPath().getString("message");
        newState = ResObject.jsonPath().getString("state");
      //  int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        ValidateOtpFseDiy ValidateOTPobj = new ValidateOtpFseDiy();
        //OTP = getOTP(mobileNo);
        // OTP="888888";
       // OTP = getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v1ValidateOtpFseDiy(ValidateOTPobj,"INDIVIDUAL","fse_diy",AgentToken,version,mobileNo,"fse_diy",newState,OTP);
        leadId = ResObj.jsonPath().getString("leadId");
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");
        Assert.assertEquals(StatusCode,200);


    }

    @Test(priority = 0,description = "Invalid ValidateOTP")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidOtpValidateOTP() throws IOException, JSchException, InterruptedException {
        fseDiyPositiveSendOTP();
        ValidateOtpFseDiy ValidateOTPobj = new ValidateOtpFseDiy();
        //OTP = getOTP(mobileNo);
        // OTP="888888";
        // OTP = getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v1ValidateOtpFseDiy(ValidateOTPobj,"INDIVIDUAL", "fse_diy", AgentToken, version, mobileNo, "fse_diy", newState,"123456");
        leadId = ResObj.jsonPath().getString("leadId");
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");

    }

    @Test(priority = 0,description = "4 Digit ValidateOTP")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void fourDigitOtpValidateOTP() throws IOException, JSchException, InterruptedException {
        fseDiyPositiveSendOTP();
        ValidateOtpFseDiy ValidateOTPobj = new ValidateOtpFseDiy();
        //OTP = getOTP(mobileNo);
        // OTP="888888";
        // OTP = getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v1ValidateOtpFseDiy(ValidateOTPobj,"INDIVIDUAL", "fse_diy", AgentToken, version, mobileNo, "fse_diy", newState,"1234");
        leadId = ResObj.jsonPath().getString("leadId");
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");

    }


    @Test(priority = 0,description = "Empty ValidateOTP")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyOtpValidateOTP() throws IOException, JSchException, InterruptedException {
        fseDiyPositiveSendOTP();
        ValidateOtpFseDiy ValidateOTPobj = new ValidateOtpFseDiy();
        //OTP = getOTP(mobileNo);
        // OTP="888888";
        // OTP = getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v1ValidateOtpFseDiy(ValidateOTPobj,"INDIVIDUAL", "fse_diy", AgentToken, version, mobileNo, "fse_diy", newState,"");
        leadId = ResObj.jsonPath().getString("leadId");
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");

    }

    @Test(priority = 0,description = "Empty Mobile ValidateOTP")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyMobileValidateOTP() throws IOException, JSchException, InterruptedException {
        fseDiyPositiveSendOTP();
        ValidateOtpFseDiy ValidateOTPobj = new ValidateOtpFseDiy();
        //OTP = getOTP(mobileNo);
        // OTP="888888";
        // OTP = getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v1ValidateOtpFseDiy(ValidateOTPobj,"INDIVIDUAL", "fse_diy", AgentToken, version, "", "fse_diy", newState,OTP);
        leadId = ResObj.jsonPath().getString("leadId");
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");

    }


    @Test(priority = 0,description = "invalidStateValidateOTP")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidStateValidateOTP() throws IOException, JSchException, InterruptedException {
        fseDiyPositiveSendOTP();
        ValidateOtpFseDiy ValidateOTPobj = new ValidateOtpFseDiy();
        //OTP = getOTP(mobileNo);
        // OTP="888888";
        // OTP = getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v1ValidateOtpFseDiy(ValidateOTPobj,"INDIVIDUAL", "fse_diy", AgentToken, version, mobileNo, "fse_diy", "7878787877",OTP);
        leadId = ResObj.jsonPath().getString("leadId");
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");

    }
    @Test(priority = 0,description = "emptyStateValidateOTP")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyStateValidateOTP() throws IOException, JSchException, InterruptedException {
        fseDiyPositiveSendOTP();
        ValidateOtpFseDiy ValidateOTPobj = new ValidateOtpFseDiy();
        //OTP = getOTP(mobileNo);
        // OTP="888888";
        // OTP = getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v1ValidateOtpFseDiy(ValidateOTPobj,"INDIVIDUAL", "fse_diy", AgentToken, version, mobileNo, "fse_diy", "",OTP);
        leadId = ResObj.jsonPath().getString("leadId");
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");

    }
    @Test(priority = 0,description = "invalidUserTypeValidateOTP")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidUserTypeValidateOTP() throws IOException, JSchException, InterruptedException {
        fseDiyPositiveSendOTP();
        ValidateOtpFseDiy ValidateOTPobj = new ValidateOtpFseDiy();
        //OTP = getOTP(mobileNo);
        // OTP="888888";
        // OTP = getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v1ValidateOtpFseDiy(ValidateOTPobj,"INDIVIDUAL", "fse_diy", AgentToken, version, mobileNo, "fsy", newState,OTP);
        leadId = ResObj.jsonPath().getString("leadId");
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");

    }

    @Test(priority = 0,description = "diffUserTypeValidateOTP")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffUserTypeValidateOTP() throws IOException, JSchException, InterruptedException {
        fseDiyPositiveSendOTP();
        ValidateOtpFseDiy ValidateOTPobj = new ValidateOtpFseDiy();
        //OTP = getOTP(mobileNo);
        // OTP="888888";
        // OTP = getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v1ValidateOtpFseDiy(ValidateOTPobj,"INDIVIDUAL", "fse_diy", AgentToken, version, mobileNo, "p2p_100k", newState,OTP);
        leadId = ResObj.jsonPath().getString("leadId");
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");

    }



    @Test(priority = 0,description = "emptyUserTypeValidateOTP")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyUserTypeValidateOTP() throws IOException, JSchException, InterruptedException {
        fseDiyPositiveSendOTP();
        ValidateOtpFseDiy ValidateOTPobj = new ValidateOtpFseDiy();
        //OTP = getOTP(mobileNo);
        // OTP="888888";
        // OTP = getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v1ValidateOtpFseDiy(ValidateOTPobj,"INDIVIDUAL", "fse_diy", AgentToken, version, mobileNo, "", newState,OTP);
        leadId = ResObj.jsonPath().getString("leadId");
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");

    }

    @Test(priority = 0,description = "diffEntityTypeValidateOTP")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffEntityTypeValidateOTP() throws IOException, JSchException, InterruptedException {
        fseDiyPositiveSendOTP();
        ValidateOtpFseDiy ValidateOTPobj = new ValidateOtpFseDiy();
        //OTP = getOTP(mobileNo);
        // OTP="888888";
        // OTP = getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v1ValidateOtpFseDiy(ValidateOTPobj,"PROPRIETORSHIP", "fse_diy", AgentToken, version, mobileNo, "fse_diy", newState,OTP);
        leadId = ResObj.jsonPath().getString("leadId");
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");

    }

    @Test(priority = 0,description = "invalidEntityTypeValidateOTP")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidEntityTypeValidateOTP() throws IOException, JSchException, InterruptedException {
        fseDiyPositiveSendOTP();
        ValidateOtpFseDiy ValidateOTPobj = new ValidateOtpFseDiy();
        //OTP = getOTP(mobileNo);
        // OTP="888888";
        // OTP = getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v1ValidateOtpFseDiy(ValidateOTPobj,"INDIVIDUL", "fse_diy", AgentToken, version, mobileNo, "fse_diy", newState,OTP);
        leadId = ResObj.jsonPath().getString("leadId");
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");

    }



    @Test(description = "Submit Aadhar Detail",priority = 0, dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void submitAadhar() throws JSchException, IOException, InterruptedException {

       // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "invalid Entity Type submit Aadhar",  dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidEntityTypesubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVI","fse_diy", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }





    @Test(description = "invalidEntityTypesubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyEntityTypesubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"","fse_diy", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }





    @Test(description = "different EntityType submit Aadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffEntityTypesubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"TRUST","fse_diy", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }





    @Test(description = "diff Solution Type submitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffSolutionTypesubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","Revisit", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }





    @Test(description = "Empty Solution Type submit Aadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptySolutionTypesubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }





    @Test(description = "incorrect Solution Type submit Aadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectSolutionTypesubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_y", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }


    @Test(description = "incorrectLeadID_SubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectLeadID_SubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", "54ed7934-68bc-4faa-8654-c7f4cdc7f9d8", AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }
    @Test(description = "empty LeadID_SubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyLeadID_SubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", "", AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,500);



    }


    @Test(description = "emptyAgentTokensubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyAgentTokensubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, "",version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "IncorrectAgentTokensubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void IncorrectAgentTokensubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, "ac5f8168-86eb-4634-83d8-0f1915255701",version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,410);



    }

    @Test(description = "emptyVersionsubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyVersionsubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,"", NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "incorrecttVersionsubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrecttVersionsubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,"9.5.0", NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }


    @Test(description = "emptyAadharNamesubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyAadharNamesubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "", merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }


    @Test(description = "incorrectAadharNamesubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectAadharNamesubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "123%^a", merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,500);



    }


    @Test(description = "emptyDOBsubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyDOBsubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Merchant's Date of Birth is invalid";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, NAME_AS_PER_AADHAR, "",gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "incorrectDOBsubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectDOBsubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Merchant's Date of Birth is invalid";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, NAME_AS_PER_AADHAR, "23/06/1988",gender,aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "emptyGendersubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyGendersubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,"",aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,500);



    }

    @Test(description = "incorrectGendersubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectGendersubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,"ma",aadhar_not_readable,aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,500);



    }

    @Test(description = "emptyAadharNotReadableSubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyAadharNotReadableSubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,"",aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,500);



    }

    @Test(description = "incorrectAadharNotReadableSubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectAadharNotReadableSubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,"trueee",aadhar_ref_no,screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }


    @Test(description = "emptyAadharRefsubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyAadharRefsubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,"",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }


    @Test(description = "incorrectAadharRefsubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectAadharRefsubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,"**********12",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "emptyPartialSaveSubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyPartialSaveSubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,"");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "incorrectPartialSaveSubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectPartialSaveSubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,screen_Name,"trueeee");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }


    @Test(description = "emptyScreenNameSubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyScreenNameSubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,"",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "incorrectScreenNameSubmitAadhar",dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectScreenNameSubmitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, NAME_AS_PER_AADHAR, merchant_dob,gender,aadhar_not_readable,aadhar_ref_no,"screengit ",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void addBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "***************","ABHY0065305","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }


    @Test(description = "Incorrect entity add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectaddBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"IND","fse_diy", leadId, AgentToken,version, "***************","ABHY0065305","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "empty entity type add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyEntityTypeaddBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"","fse_diy", leadId, AgentToken,version, "***************","ABHY0065305","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "different entity add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void differentEntityaddBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"Trust","fse_diy", leadId, AgentToken,version, "***************","ABHY0065305","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }


    @Test(description = "Incorrect solution type add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectSolutionaddBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","fse", leadId, AgentToken,version, "***************","ABHY0065305","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }


    @Test(description = "empty solution type add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptySolutionaddBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","", leadId, AgentToken,version, "***************","ABHY0065305","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }

    @Test(description = "different solution type add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffSolutionaddBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","Revisit", leadId, AgentToken,version, "***************","ABHY0065305","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }


    @Test(description = "empty lead id  add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyLeadIDBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","fse_diy", "", AgentToken,version, "***************","ABHY0065305","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }

    @Test(description = "incorrect lead id  add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectLeadIDBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","fse_diy", "178227-37837", AgentToken,version, "***************","ABHY0065305","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }

    @Test(description = "empty fse token add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptySessionTokenBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","fse_diy", leadId, "",version, "***************","ABHY0065305","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }

    @Test(description = "empty fse token add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectSessionTokenBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","fse_diy", leadId, "************",version, "***************","ABHY0065305","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }

    @Test(description = "empty version add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyVersionaddBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","Revisit", leadId, AgentToken,"", "***************","ABHY0065305","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }


    @Test(description = "incorrect version add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectVersionaddBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","Revisit", leadId, AgentToken,"9.2.1", "***************","ABHY0065305","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }


    @Test(description = "incorrect bank add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectBankaddBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","Revisit", leadId, AgentToken,"9.2.1", "12","ABHY0065305","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }

    @Test(description = "empty bank add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyBankaddBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","Revisit", leadId, AgentToken,"9.2.1", "","ABHY0065305","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }


    @Test(description = "empty Ifsc add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyIfscaddBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","Revisit", leadId, AgentToken,"9.2.1", "**********","","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }

    @Test(description = "incorrect Ifsc add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectIfscaddBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","Revisit", leadId, AgentToken,"9.2.1", "**********","AAAAAAAA","true","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }

    @Test(description = "incorrect Only validate add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectOnlyValidateBankaddBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","Revisit", leadId, AgentToken,"9.2.1", "**********","ICIC0006622","tre","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }

    @Test(description = "empty Only validate add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyOnlyValidateBankaddBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","Revisit", leadId, AgentToken,"9.2.1", "**********","ICIC0006622","","ABHYUDAYA COOPERATIVE BANK LIMITED",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }

    @Test(description = "incorrect add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectBank_addBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","Revisit", leadId, AgentToken,"9.2.1", "**********","ICIC0006622","true","ABHYUDAYA",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }

    @Test(description = "empty add bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyBank_addBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","Revisit", leadId, AgentToken,"9.2.1", "**********","ICIC0006622","true","",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }



    @Test(description = "empty partial save bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptypartialSaveBankaddBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","Revisit", leadId, AgentToken,"9.2.1", "**********","ICIC0006622","true","ABHYUDAYA COOPERATIVE BANK LIMITED","");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }

    @Test(description = "incorrect partial save bank Detail",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectpartialSaveBankaddBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail,"INDIVIDUAL","Revisit", leadId, AgentToken,"9.2.1", "**********","ICIC0006622","true","ABHYUDAYA COOPERATIVE BANK LIMITED","Tst");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }

    @Test(description = "Submit Pan Detail",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void submitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "INDIVIDUAL","INDIVIDUAL","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "emptyEntitysubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyEntitysubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"","fse_diy", leadId, AgentToken,version, "INDIVIDUAL","INDIVIDUAL","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "incorrectEntitysubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectEntitysubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"IND","fse_diy", leadId, AgentToken,version, "INDIVIDUAL","INDIVIDUAL","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "DiffEntitysubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void DiffEntitysubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"TRUST","fse_diy", leadId, AgentToken,version, "INDIVIDUAL","INDIVIDUAL","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }


    @Test(description = "emptySolutionTypesubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptySolutionTypesubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","", leadId, AgentToken,version, "INDIVIDUAL","INDIVIDUAL","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "incorrectSolutionTypesubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectSolutionTypesubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fs", leadId, AgentToken,version, "INDIVIDUAL","INDIVIDUAL","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "diffSolutionTypesubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffSolutionTypesubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","Revisit", leadId, AgentToken,version, "INDIVIDUAL","INDIVIDUAL","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "emptyLeadIDsubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyLeadIDsubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", "", AgentToken,version, "INDIVIDUAL","INDIVIDUAL","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "incorrectLeadIDsubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectLeadIDsubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", "****************", AgentToken,version, "INDIVIDUAL","INDIVIDUAL","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "emptyTokensubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyTokensubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, "",version, "INDIVIDUAL","INDIVIDUAL","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "incorrectTokensubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectTokensubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, "****************",version, "INDIVIDUAL","INDIVIDUAL","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }

    @Test(description = "emptyVersionsubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyVersionsubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,"", "INDIVIDUAL","INDIVIDUAL","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "IncorrectVersionsubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectVersionsubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,"1.2.3", "INDIVIDUAL","INDIVIDUAL","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }



    @Test(description = "Incorrect Business Type submit Pan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectBusinessTypesubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "Indi","INDIVIDUAL","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "different Business Type submit Pan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void differentBusinessTypesubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "TRUST","INDIVIDUAL","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "emptyBusinessEntitysubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyBusinessEntitysubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "INDIVIDUAL","","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "incorrectBusinessEntitysubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectBusinessEntitysubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "INDIVIDUAL","INDIVID","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "diffBusinessEntitysubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffBusinessEntitysubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "INDIVIDUAL","TRUST","**********","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "emptyPansubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyPansubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "INDIVIDUAL","INDIVIDUAL","","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "incorrectPansubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectPansubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "INDIVIDUAL","INDIVIDUAL","","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "diffentityPansubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffentityPansubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "INDIVIDUAL","INDIVIDUAL","crzck0897h","false",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }


    @Test(description = "emptyIspanSkippedsubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyIspanSkippedsubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "INDIVIDUAL","INDIVIDUAL","**********","",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "IncorrectIspanSkippedsubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void IncorrectIspanSkippedsubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "INDIVIDUAL","INDIVIDUAL","**********","rey",screen_Name,partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }


    @Test(description = "emptyscreensubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyScreensubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "INDIVIDUAL","INDIVIDUAL","**********","false","",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "incorrectScreensubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectScreensubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "INDIVIDUAL","INDIVIDUAL","**********","false","screen",partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "emptyPartialSavesubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyPartialSavesubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "INDIVIDUAL","INDIVIDUAL","**********","false",screen_Name,"");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }

    @Test(description = "incorrectPartialSavesubmitPan",dependsOnMethods = {"addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectPartialSavesubmitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPan,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "INDIVIDUAL","INDIVIDUAL","**********","false",screen_Name,"rew");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);



    }



    @Test(description = "Add home address",dependsOnMethods = {"submitPan","addBankDetail","submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void addHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,version, "Udyog Bhawan","Near India Gate","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }



    @Test(description = "Add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void EmptyEntityAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"","fse_diy", leadId, AgentToken,version, "Udyog Bhawan","Near India Gate","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);


    }

    @Test(description = "incorrect entity Add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectEntityAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"test","fse_diy", leadId, AgentToken,version, "Udyog Bhawan","Near India Gate","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "diff entity Add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffEntityAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"TRUST","fse_diy", leadId, AgentToken,version, "Udyog Bhawan","Near India Gate","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty solution type add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptySolutionAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"TRUST","", leadId, AgentToken,version, "Udyog Bhawan","Near India Gate","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "incorrect solution type add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectSolutionAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse", leadId, AgentToken,version, "Udyog Bhawan","Near India Gate","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "diff solution type add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffSolutionAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","Revisit", leadId, AgentToken,version, "Udyog Bhawan","Near India Gate","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty Lead id add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyLeadIDAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", "", AgentToken,version, "Udyog Bhawan","Near India Gate","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "wrong Lead id add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void wrongLeadIDAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", "123456677888", AgentToken,version, "Udyog Bhawan","Near India Gate","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "diff Lead id add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffLeadIDAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", "f7fb28ef-42fe-4ce2-abae-e364ba286c3f", AgentToken,version, "Udyog Bhawan","Near India Gate","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }


    @Test(description = "empty session token add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptySessionTokenAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, "",version, "Udyog Bhawan","Near India Gate","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }


    @Test(description = "empty version add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyVersionTokenAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"", "Udyog Bhawan","Near India Gate","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "incorrect version add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectVersionTokenAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.1", "Udyog Bhawan","Near India Gate","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "diff version add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffVersionTokenAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"1.1.0", "Udyog Bhawan","Near India Gate","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }


    @Test(description = "empty shop name add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyShopNameAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "","Near India Gate","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "incorrect shop name add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectShopNameAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "!#@%@%^%^","Near India Gate","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty landmark add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyLandMarkNameAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "incorrect Landmark add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectLandMarkNameAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","!#@%@%^%^","India","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }


    @Test(description = "incorrect Shop Address add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectShopAddressAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","!#@%@%^%^","!#@%@%^%^","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }


    @Test(description = "empty Shop Address add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyShopAddressAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","","CP Area","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty Shop Address add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyAreaOfEntrollmentAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty Shop Address add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectAreaOfEntrollmentAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","^^%$%$","Uttar Pradesh","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty Shop Address add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectStateAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","asd","Uttar","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty Shop Address add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyStateAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","asd","","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty Shop Address add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrect1StateAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","asd","%^%^$$$$^","Nopida","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty Shop Address add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectCityOfEntrollmentAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","huh","Uttar Pradesh","**^&","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty city of enrollment add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyCityOfEntrollmentAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","huh","Uttar Pradesh","","201301","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty city of enrollment add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptypincodeAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","huh","Uttar Pradesh","aa","","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty city of enrollment add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectpincodeAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","huh","Uttar Pradesh","aa","XYZ","28.5355","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty city of enrollment add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyLattitudeAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","huh","Uttar Pradesh","aa","XYZ","","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty city of enrollment add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectLattitudeAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","huh","Uttar Pradesh","aa","XYZ","xyas","77.391","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty city of enrollment add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorretLongitudeAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","huh","Uttar Pradesh","aa","XYZ","28.5355","xtz","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty city of enrollment add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyLongitudeAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","huh","Uttar Pradesh","aa","XYZ","28.5355","","Gautam Buddh Nagar","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty city of enrollment add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptydistrictAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","huh","Uttar Pradesh","aa","XYZ","28.5355","28.5355","","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty city of enrollment add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectdistrictAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","huh","Uttar Pradesh","aa","XYZ","28.5355","28.5355","&&*&*&","Gautam Buddh Nagar","Gautam Buddh Nagar","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);
    }

    @Test(description = "empty city of enrollment add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectVillageAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","huh","Uttar Pradesh","aa","XYZ","28.5355","28.5355","&&*&*&","Gautam Buddh Nagar","**&*&","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);
    }

    @Test(description = "empty city of enrollment add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyVillageAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","huh","Uttar Pradesh","aa","XYZ","28.5355","28.5355","Gautam Buddh Nagar","Gautam Buddh Nagar","","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }

    @Test(description = "empty city of enrollment add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptysubdistirctAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","huh","Uttar Pradesh","aa","XYZ","28.5355","28.5355","Gautam Buddh Nagar","","","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);
    }

    @Test(description = "empty city of enrollment add home address",dependsOnMethods = {"submitAadhar","PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectsubdistirctAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress,CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address,"INDIVIDUAL","fse_diy", leadId, AgentToken,"5.8.0", "abc","xyz","abc xyz","huh","Uttar Pradesh","aa","XYZ","28.5355","28.5355","Gautam Buddh Nagar","&**&","","Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)","0","","false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);

    }
    //incorrect formatted address
    @Test(description = "incorrect formatted address add home address", dependsOnMethods = {"submitAadhar", "PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void incorrectformattedAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "", "*&*&*&*", "0", "", "false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode, 200);

    }

    //empty formatted address
    @Test(description = "empty formatted address add home address", dependsOnMethods = {"submitAadhar", "PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyformattedAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "", "", "0", "", "false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains("Please provide a Paytm registered mobile number."));
        Assert.assertEquals(StatusCode, 200);}

    // create a test method with empty refreshLocationCount
    @Test(description = "empty refreshLocationCount add home address", dependsOnMethods = {"submitAadhar", "PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void emptyrefreshLocationCountAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "", "", "false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode, 200);


    }


    // create a method with incorrect refreshLocationCount
    @Test(description = "incorrect refreshLocationCount add home address", dependsOnMethods = {"submitAadhar", "PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void incorrectrefreshLocationCountAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "", "false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    // create a method with incorrect userAddressUUID
    @Test(description = "incorrect userAddressUUID add home address", dependsOnMethods = {"submitAadhar", "PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void incorrectuserAddressUUIDAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    //create a method with empty partialSave
    @Test(description = "empty partialSave add home address", dependsOnMethods = {"submitAadhar", "PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void emptypartialSaveAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    //create a method with incorrect partialSave
    @Test(description = "incorrect partialSave add home address", dependsOnMethods = {"submitAadhar", "PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void incorrectpartialSaveAddHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    // create a method to fetch questions
    @Test(description = "fetch questions", dependsOnMethods = {"PositiveValidateOtp","addBankDetail","submitAadhar"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void fetchQuestions() throws JSchException, IOException, InterruptedException {
        FetchQuestions fetchQuestions = new FetchQuestions();
        Response ResObject = middlewareServicesObject.v1FetchQuestions(fetchQuestions, "INDIVIDUAL", "fse_diy", AgentToken, "5.8.0");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    // create a method to fetch questions
    @Test(description = "fetch questions", dependsOnMethods = {"PositiveValidateOtp","addBankDetail","submitAadhar"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void InvalidEntityTypefetchQuestions() throws JSchException, IOException, InterruptedException {
        FetchQuestions fetchQuestions = new FetchQuestions();
        Response ResObject = middlewareServicesObject.v1FetchQuestions(fetchQuestions, "abc", "fse_diy", AgentToken, "5.8.0");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }


    // create a method to fetch questions with empty entityType
     @Test(description = "fetch questions", dependsOnMethods = {"PositiveValidateOtp","addBankDetail","submitAadhar"})

    public void emptyEntityTypefetchQuestions() throws JSchException, IOException, InterruptedException {
        FetchQuestions fetchQuestions = new FetchQuestions();
        Response ResObject = middlewareServicesObject.v1FetchQuestions(fetchQuestions, "", "fse_diy", AgentToken, "5.8.0");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }


    // create a method to fetch questions with empty solutionType
    @Test(description = "fetch questions", dependsOnMethods = {"PositiveValidateOtp","addBankDetail","submitAadhar"})

    public void emptySolutionTypefetchQuestions() throws JSchException, IOException, InterruptedException {
        FetchQuestions fetchQuestions = new FetchQuestions();
        Response ResObject = middlewareServicesObject.v1FetchQuestions(fetchQuestions, "INDIVIDUAL", "", AgentToken, "5.8.0");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    // create a method to fetch questions with different solutionType
    @Test(description = "fetch questions", dependsOnMethods = {"PositiveValidateOtp","addBankDetail","submitAadhar"})

    public void differentSolutionTypefetchQuestions() throws JSchException, IOException, InterruptedException {
        FetchQuestions fetchQuestions = new FetchQuestions();
        Response ResObject = middlewareServicesObject.v1FetchQuestions(fetchQuestions, "INDIVIDUAL", "diy_mco", AgentToken, "5.8.0");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    // create a method to fetch questions with incorrect solutionType
    @Test(description = "fetch questions", dependsOnMethods = {"PositiveValidateOtp","addBankDetail","submitAadhar"})

    public void incorrectSolutionTypefetchQuestions() throws JSchException, IOException, InterruptedException {
        FetchQuestions fetchQuestions = new FetchQuestions();
        Response ResObject = middlewareServicesObject.v1FetchQuestions(fetchQuestions, "INDIVIDUAL", "uuuu", AgentToken, "5.8.0");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    // create a method to fetch questions with empty agentToken
    @Test(description = "fetch questions", dependsOnMethods = {"PositiveValidateOtp","addBankDetail","submitAadhar"})

    public void emptyAgentTokenfetchQuestions() throws JSchException, IOException, InterruptedException {
        FetchQuestions fetchQuestions = new FetchQuestions();
        Response ResObject = middlewareServicesObject.v1FetchQuestions(fetchQuestions, "INDIVIDUAL", "fse_diy", "", "5.8.0");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    // create a method to fetch questions with different agentToken
    @Test(description = "fetch questions", dependsOnMethods = {"PositiveValidateOtp","addBankDetail","submitAadhar"})

    public void differentAgentTokenfetchQuestions() throws JSchException, IOException, InterruptedException {
        FetchQuestions fetchQuestions = new FetchQuestions();
        Response ResObject = middlewareServicesObject.v1FetchQuestions(fetchQuestions, "INDIVIDUAL", "fse_diy", "abc", "5.8.0");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    // create a method to fetch questions with empty appVersion
    @Test(description = "fetch questions", dependsOnMethods = {"PositiveValidateOtp","addBankDetail","submitAadhar"})

    public void emptyAppVersionfetchQuestions() throws JSchException, IOException, InterruptedException {
        FetchQuestions fetchQuestions = new FetchQuestions();
        Response ResObject = middlewareServicesObject.v1FetchQuestions(fetchQuestions, "INDIVIDUAL", "fse_diy", AgentToken, "");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    // create a method to fetch questions with different appVersion
    @Test(description = "fetch questions", dependsOnMethods = {"PositiveValidateOtp","addBankDetail","submitAadhar"})

    public void differentAppVersionfetchQuestions() throws JSchException, IOException, InterruptedException {
        FetchQuestions fetchQuestions = new FetchQuestions();
        Response ResObject = middlewareServicesObject.v1FetchQuestions(fetchQuestions, "INDIVIDUAL", "fse_diy", AgentToken, "*******");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    // create a method to fetch questions with incorrect appVersion
    @Test(description = "fetch questions", dependsOnMethods = {"PositiveValidateOtp","addBankDetail","submitAadhar"})

    public void incorrectAppVersionfetchQuestions() throws JSchException, IOException, InterruptedException {
        FetchQuestions fetchQuestions = new FetchQuestions();
        Response ResObject = middlewareServicesObject.v1FetchQuestions(fetchQuestions, "INDIVIDUAL", "fse_diy", AgentToken, "UHHH");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    // Create a method to fetch questions with empty leadId
    @Test(description = "fetch questions", dependsOnMethods = {"PositiveValidateOtp","addBankDetail","submitAadhar"})

    public void emptyaLeadIdfetchQuestions() throws JSchException, IOException, InterruptedException {
        FetchQuestions fetchQuestions = new FetchQuestions();
        Response ResObject = middlewareServicesObject.v1FetchQuestions(fetchQuestions,"INDIVIDUAL","fse_diy",AgentToken,"5.8.0");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }
    //Create a method to create a lead with empty leadId
    // Create a method to fetch questions with incorrect leadId


    // Create a method to fetch questions with empty leadId

    // Create a method to fetch questions with incorrect leadId


    @Test(description = "fetch questions", dependsOnMethods = {"PositiveValidateOtp","addBankDetail","submitAadhar"})

    public void emptyLeadIdfetchQuestions() throws JSchException, IOException, InterruptedException {
        FetchQuestions fetchQuestions = new FetchQuestions();
        Response ResObject = middlewareServicesObject.v1FetchQuestions(fetchQuestions,"INDIVIDUAL","fse_diy",AgentToken,"5.8.0");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }

// Create a method to fetch questions with incorrect leadId

    @Test(description = "incorrect partialSave add home address", dependsOnMethods = {"submitAadhar", "PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void aaddQuestionspage3() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void aQuestionspage4() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void aaddQuestionspage1() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void aQuestionspage21() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void aQuestionspage31() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void aQuestionspage41() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }



    @Test(description = "incorrect partialSave add home address", dependsOnMethods = {"submitAadhar", "PositiveValidateOtp"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addQuestionspage3() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void addQuestionspage1() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void Questionspage21() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void Questionspage31() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void Questionspage41() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void Questionspage5() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void addQuestionspage6() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void addQuestionspage7() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void addQuestionspage8() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void Questionspage9() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void Questionspage10() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void addQuestionspage11() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void Questionspage6_1() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }




    // New screens Questions & answers



    public void addQuestionspage4() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void Questionspage1() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void addQuestionspage21() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void addQuestionspage31() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void addQuestionspage41() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void addQuestionspage5() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void Questionspage6() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void Questionspage7() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void Questionspage8() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void addQuestaionspage9() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void AddaddQuestionspage21() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void AddaddQuestioanspage31() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void AddaddQuestionspage41() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void AddaddQuestionspagae5() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void AddQuestionspage6() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void AddQauestionspage7() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void AddQuestionspage8() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void addQuestionspage9() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void AddaddaQuestionspage21() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void AddaddQuestionspage31() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }
    public void AdaddQuestionspage41() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }


    public void AddaddQuestionspage5() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void AddQuestionaspage6() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void AddQuestionspage7() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    //To add test

    public void AddQuestionspag() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void AddQuestionsag() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }
    public void AddQuestionsg() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void AddQuetionsg() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    public void AdQuestionsg() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }
    public void AdQuetionsg() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, "5.8.0", "abc", "xyz", "abc xyz", "huh", "Uttar Pradesh", "aa", "XYZ", "28.5355", "28.5355", "Gautam Buddh Nagar", "&**&", "noida", "noida", "abc", "0", "abc");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }
    //fetch questions



}







