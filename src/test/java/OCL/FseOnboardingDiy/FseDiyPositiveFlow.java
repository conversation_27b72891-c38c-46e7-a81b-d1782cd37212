package OCL.FseOnboardingDiy;

import OCL.CommonOnboarding.RetryAnalyzer;
import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.Cart.PinCode;
import Request.MerchantService.v1.Resources.LanguagePreference;
import Request.MerchantService.v1.Revisit.LoanIntentfetchDocumentDetails;
import Request.MerchantService.v1.fseDiy.*;
import Request.MerchantService.v2.Banks;
import Request.MerchantService.v2.TnC;
import Request.MerchantService.v3.*;
import Request.UAD.Category;
import Request.UAD.SubCategory;
import Request.Wallet.CreateUserWallet;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.UAD.UADServices;
import Services.Utilities.Utilities;
import Services.Wallet.WalletServices;
import Services.oAuth.oAuthServices;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSchException;
import com.opencsv.CSVWriter;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FseDiyPositiveFlow extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(FseDiy.class);
    private static final String NAME_AS_PER_PAN = "Puneet kumar";
    public static String merchant_dob = "07/24/1963";
    public static String gender = "male";
    public static String aadhar_not_readable = "true";
    Utilities utilities = new Utilities();

    public String aadhar_ref_no = utilities.ValidAadhaar();
    public static String screen_Name = "aadhar_not_present_page";
    public static String partial_Save = "true";

    public static String SignatureDMSID = "";
    public static String BusinessOwnerPhotoDMSID = "";
    public static String AadharPhotoDMSID1 = "";
    public static String AadharPhotoDMSID2 = "";

    public static String requestPathAadhar = "MerchantService/V1/Fse/Diy/Request/SubmitLeadDetailRequest.json";
    public static String requestPathPan = "MerchantService/V1/Fse/Diy/Request/BankDetailRequest.json";

    public static String requestPathPanDetail = "MerchantService/V1/Fse/Diy/Request/PanDetailRequest.json";

    public static String requestPathbank = "MerchantService/V1/Fse/Diy/Request/SubmitBankDetailRequest.json";

    public static String requestPathHomeAddress = "MerchantService/V1/Fse/Diy/Request/AddHomeAddressRequest.json";

    public static String requestQuestions = "MerchantService/V1/Fse/Diy/Request/QuestionsRequest.json";
    public static String requestFamilyDetails = "MerchantService/V1/Fse/Diy/Request/FamilyDetailsRequest.json";
    public static String requestDependentDetails = "MerchantService/V1/Fse/Diy/Request/DependentDetailsRequest.json";

    public static String requestSaveTnc = "MerchantService/V1/Fse/Diy/Request/SaveTncRequest.json";

    public static String NAME_AS_PER_AADHAR = "ANMOL JAIN";

    public static final String FSEMobileNumber = "**********";

    public static String AgentToken = ApplicantToken(FSEMobileNumber, "Paytm@123");

    public static String AgentNo = "**********";
    public static String version = "5.1.6";
    public static String mobileNo = "**********";


    public static String newState = "";
    public static String OTP = "888888";
    public static String nameMatchStatus = "";
    public static String CustId = "";
    public static String emptyCustID = "";
    public static String noLeadCustID = "1000527769";
    public static String noOauthCustID = "10000001";
    public static String XMWToken = "";
    public static String leadId = "";
    public static String MID = "";

    Faker GenerateFake = new Faker();
    public String lineOne = GenerateFake.address().streetAddress();
    public String lineTwo = GenerateFake.address().cityName();
    public String lineThree = GenerateFake.address().streetName();

    public static String WorkFlowId = "";
    public static String addressUuid = "";
    public static String rrbUuid = "";
    public static String ownerAddressUUID = "";
    public static List<String> docsToUpload = new ArrayList<>();

    public static List<Object> DMS = new ArrayList<>();
    public static List<Object> DMSforUUID = new ArrayList<>();
    public static List<Object> DocType = new ArrayList<>();
    public static List<Object> DocProvided = new ArrayList<>();
    public static List<Object> DocumentRequest = new ArrayList<>();
    public static List<Object> NameOfDoc = new ArrayList<>();
    public static List<Object> TypeOfDoc = new ArrayList<>();
    List<Object> GetDocuments = new ArrayList<>();

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    UADServices uadServicesObject = new UADServices();
    WalletServices walletServices = new WalletServices();
    Services.oAuth.oAuthServices oAuthServices = new oAuthServices();

    TnC GetTnC = new TnC();
    GetDocStatus v3GetDocStat = new GetDocStatus();
    LanguagePreference getLanguage = new LanguagePreference();

    Map<String, String> headers = new HashMap<>();


    public static String requestReferredByEmp = "MerchantService/V1/Fse/Diy/Request/ReferredByEmpRequest.json";

    @BeforeClass
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentFseDiyLogin() throws Exception {
        //AgentToken=AgentSessionToken("9953828631","paytm@123");
        // AgentToken = CommonAgentToken;


        AgentToken = ApplicantToken(FSEMobileNumber, "Paytm@123");
        // CA9277571643  9650376490
        LOGGER.info("FSE diy Agent Token is : " + AgentToken);


        DBConnection dbConnection = new DBConnection();
        dbConnection.UpdateQueryToCloseLead(FSEMobileNumber, "fse_diy");

        CustId = GetResourceOwnerId(FSEMobileNumber, "Paytm@123");

        System.out.println("Agent CustId is " + CustId);


    }


   // @BeforeTest
   // @Owner(emailId = "<EMAIL>", isAutomated = true)
    //public void AgentLogin() throws Exception {
    //}


    @Test(description = "Send OTP on Mobile Number", priority = 1)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fseDiyPositiveSendOTP() {
        SendOtpFseDiy SendOTPobj = new SendOtpFseDiy();
        //  String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Response ResObject = middlewareServicesObject.v3SentOtpFseDiy(SendOTPobj, "INDIVIDUAL", "fse_diy", AgentToken, version, FSEMobileNumber, "fse_diy", CustId);
        String actualMsg = ResObject.jsonPath().getString("message");
        newState = ResObject.jsonPath().getString("state");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(description = "Positive ValidateOTP", priority = 2, dependsOnMethods = "fseDiyPositiveSendOTP")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void PositiveValidateOtp() throws IOException, JSchException, InterruptedException {
        //   fseDiyPositiveSendOTP();

    //    SendOtpFseDiy SendOTPobj = new SendOtpFseDiy();
     //   String expectedMsg1 = "Please provide a Paytm registered mobile number.";
    //    Response ResObject = middlewareServicesObject.v3SentOtpFseDiy(SendOTPobj, "INDIVIDUAL", "fse_diy", AgentToken, version, FSEMobileNumber, "fse_diy", CustId);
     //   String actualMsg = ResObject.jsonPath().getString("message");
      //  newState = ResObject.jsonPath().getString("state");
        //  int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        ValidateOtpFseDiy ValidateOTPobj = new ValidateOtpFseDiy();
        //OTP = getOTP(mobileNo);
        // OTP="888888";
        // OTP = getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v1ValidateOtpFseDiy(ValidateOTPobj, "INDIVIDUAL", "fse_diy", AgentToken, version, FSEMobileNumber, "fse_diy", newState, OTP);
        leadId = ResObj.jsonPath().getString("leadId");
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");
        Assert.assertEquals(StatusCode, 200);


    }


    @Test(description = "Send OTP on Mobile Number", priority = 1, dependsOnMethods = "PositiveValidateOtp")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fseDiyFetchInfo() {
        FetchFseInfo FetchFseInfo = new FetchFseInfo(CustId);
        //  String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Response ResObject = middlewareServicesObject.v1fetchFseInfo(FetchFseInfo, "INDIVIDUAL", "fse_diy", AgentToken, version, FSEMobileNumber, "fse_diy", CustId);
        String actualMsg = ResObject.jsonPath().getString("message");
        newState = ResObject.jsonPath().getString("state");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode, 200);
    }


    @Test(description = "Submit Aadhar Detail", priority = 3, dependsOnMethods = "fseDiyFetchInfo")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void submitAadhar() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitAadharDetail = new SubmitLeadDetail(requestPathAadhar, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitAadharDetail, "INDIVIDUAL", "fse_diy", leadId, AgentToken, version, NAME_AS_PER_AADHAR, merchant_dob, gender, aadhar_not_readable, aadhar_ref_no,screen_Name, partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(description = "add bank Detail", priority = 4, dependsOnMethods = "submitAadhar")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void validateBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathPan, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail, "INDIVIDUAL", "fse_diy", leadId, AgentToken, version, "***************", "ABHY0065305", "true", "ABHYUDAYA COOPERATIVE BANK LIMITED", partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode, 200);


    }


    @Test(description = "add bank Detail", priority = 4, dependsOnMethods = "validateBankDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addBankDetail() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddBankDetail AddBank_Detail = new AddBankDetail(requestPathbank, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1addBankFseDiy(AddBank_Detail, "INDIVIDUAL", "fse_diy", leadId, AgentToken, version, "***************", "ABHY0065305", "false", "ABHYUDAYA COOPERATIVE BANK LIMITED", partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(description = "Submit Pan Detail", priority = 5, dependsOnMethods = "addBankDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void submitPan() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitPanDetail = new SubmitLeadDetail(requestPathPanDetail, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy(SubmitPanDetail, "INDIVIDUAL", "fse_diy", leadId, AgentToken, version, "INDIVIDUAL", "INDIVIDUAL", "**********", "false", screen_Name, partial_Save);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(description = "Add home address", priority = 6, dependsOnMethods = "submitPan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addHomeAddress() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        AddHomeAddress add_Home_address = new AddHomeAddress(requestPathHomeAddress, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseHomeAddress(add_Home_address, "INDIVIDUAL", "fse_diy", leadId, AgentToken, version, "Udyog Bhawan", "Near India Gate", "India", "CP Area", "Uttar Pradesh", "Nopida", "201301", "28.5355", "77.391", "Gautam Buddh Nagar", "Gautam Buddh Nagar", "Gautam Buddh Nagar", "Noida, Gautam Buddh Nagar, Uttar Pradesh, Pin-201301 (India)", "0", "", "false");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode, 200);

    }

    // create a method to fetch questions
 /*   @Test(description = "fetch questions", priority = 7, dependsOnMethods = "addHomeAddress")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchQuestions() throws JSchException, IOException, InterruptedException {
        FetchQuestions fetchQuestions = new FetchQuestions();
        Response ResObject = middlewareServicesObject.v1FetchQuestions(fetchQuestions, "INDIVIDUAL", "fse_diy", AgentToken, "5.8.0");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
*/
    @Test(description = "incorrect partialSave add home address", priority = 8, dependsOnMethods = "addHomeAddress")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AddQuestions() throws JSchException, IOException, InterruptedException {

        // PositiveValidateOtp();
        SubmitLeadDetail SubmitLeadDetailQuestion = new SubmitLeadDetail(requestQuestions, CustId);
        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Object screenName;
        Object partialSave;
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy1(SubmitLeadDetailQuestion, "INDIVIDUAL", "fse_diy", leadId, AgentToken, version);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));

        Assert.assertEquals(StatusCode, 200);
    }

    @Test(description = "Add family details", priority = 9, dependsOnMethods = "AddQuestions")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AddNomineeDetails() throws JSchException, IOException, InterruptedException {
        SubmitLeadDetail submitFamilyDetails = new SubmitLeadDetail(requestFamilyDetails, CustId);
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy1(submitFamilyDetails, "INDIVIDUAL", "fse_diy", leadId, AgentToken, version);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(description = "Add dependent details", priority = 10, dependsOnMethods = {"AddNomineeDetails"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AddDependentDetails() throws JSchException, IOException, InterruptedException {
        SubmitLeadDetail submitDependentDetails = new SubmitLeadDetail(requestDependentDetails, CustId);
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy1(submitDependentDetails, "INDIVIDUAL", "fse_diy", leadId, AgentToken, version);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, priority = 11, dependsOnMethods = {"AddDependentDetails"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UploadDependentAadharFront() throws Exception {

        // String endPoint = P.API.get("SubmitDocs");
        headers.put("session_token", AgentToken); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("version", "7.3.0"); // No change
        headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged


        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "fse_diy");
        //  params.put("channel", channel);
        params.put("entityType", "INDIVIDUAL");
        params.put("leadId", leadId);
        params.put("docCount", "0");
        params.put("pageNo", "0");
        params.put("docType", "dependent1AadhaarImage");
        params.put("docValue", "dependent1AadhaarImage");
        params.put("type", "jpg");
        params.put("mimeType", "image/jpeg");

        params.put("merchantCustId", CustId);
        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);
        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }

    @Test(retryAnalyzer = RetryAnalyzer.class, priority = 12, dependsOnMethods = {"UploadDependentAadharFront"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UploadDependentAadharBack() throws Exception {

        // String endPoint = P.API.get("SubmitDocs");
        headers.put("session_token", AgentToken); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("version", "7.3.0"); // No change
        headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged


        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "fse_diy");
        //  params.put("channel", channel);
        params.put("entityType", "INDIVIDUAL");
        params.put("leadId", leadId);
        params.put("docCount", "0");
        params.put("pageNo", "1");
        params.put("docType", "dependent1AadhaarImage");
        params.put("docValue", "dependent1AadhaarImage");
        params.put("type", "jpg");
        params.put("mimeType", "image/jpeg");

        params.put("merchantCustId", CustId);
        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);
        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }

    @Test(description = "Add referred by employee details", priority = 13, dependsOnMethods = {"UploadDependentAadharBack"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void referedbyEmp() throws JSchException, IOException, InterruptedException {
        SubmitLeadDetail submitReferredByEmp = new SubmitLeadDetail(requestReferredByEmp, CustId);
        Response ResObject = middlewareServicesObject.v1SubmitFseDiy1(submitReferredByEmp, "INDIVIDUAL", "fse_diy", leadId, AgentToken, version);
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, priority = 14, dependsOnMethods = {"referedbyEmp"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UploadSignature() throws Exception {

        // String endPoint = P.API.get("SubmitDocs");

        headers.put("session_token", AgentToken); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("version", "7.3.0"); // No change
        headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged



        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "fse_diy");
        //  params.put("channel", channel);
        params.put("entityType", "INDIVIDUAL");
        params.put("leadId", leadId);
        params.put("docCount", "0");
        params.put("docType", "signature");
        params.put("type", "jpg");

        params.put("mimeType", "image/jpeg");

        params.put("pageNo", "0");
        params.put("merchantCustId", CustId);
        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);
        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }

    @Test(retryAnalyzer = RetryAnalyzer.class, priority = 15, dependsOnMethods = {"UploadSignature"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UploadAadharFront() throws Exception {

        // String endPoint = P.API.get("SubmitDocs");
        headers.put("session_token", AgentToken); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("version", "7.3.0"); // No change
        headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged


        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "fse_diy");
        //  params.put("channel", channel);
        params.put("entityType", "INDIVIDUAL");
        params.put("leadId", leadId);
        params.put("docCount", "0");
        params.put("pageNo", "0");
        params.put("docType", "aadhaar");
        params.put("merchantCustId", CustId);
        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);
        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }


    @Test(retryAnalyzer = RetryAnalyzer.class, priority = 16, dependsOnMethods = {"UploadAadharFront"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UploadAadharBack() throws Exception {

        // String endPoint = P.API.get("SubmitDocs");

        headers.put("session_token", AgentToken); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("version", "7.3.0"); // No change
        headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged

        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "fse_diy");
        //  params.put("channel", channel);
        params.put("entityType", "INDIVIDUAL");
        params.put("leadId", leadId);
        params.put("docCount", "0");
        params.put("pageNo", "1");
        params.put("docType", "aadhaar");
        params.put("merchantCustId", CustId);
        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);
        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }

    @Test(description = "save tnc", priority = 17, dependsOnMethods = {"UploadDependentAadharBack"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void SaveTnc() throws JSchException, IOException, InterruptedException {
        FseDiySaveTnc savTnc = new FseDiySaveTnc(requestSaveTnc);
        savTnc.getProperties().setProperty("leadId", leadId);
        Response ResObject = middlewareServicesObject.v1SaveTnc(savTnc, AgentToken, version);

        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, priority = 11, dependsOnMethods = {"submitPan"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UploadBusinessOwnerPhoto() throws Exception {

        // String endPoint = P.API.get("SubmitDocs");
        headers.put("session_token", AgentToken); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("version", "7.3.0"); // No change
        headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged


        Map<String, String> params = new HashMap<String, String>();
        params.put("solutionType", "fse_diy");
        //  params.put("channel", channel);
        params.put("entityType", "INDIVIDUAL");
        params.put("leadId", leadId);
        params.put("docCount", "0");
        params.put("type", "jpg");
        params.put("mimeType", "image/jpeg");


        params.put("pageNo", "0");
        params.put("docType", "businessOwnerPhoto");
        params.put("merchantCustId", CustId);
        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
        params.put("docId", randomID);
        SubmitDocs v3DocSubmit = new SubmitDocs();

        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);


    }


    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "SaveTnc")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void McoLeadFetchDocStatus() throws Exception {

        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solution", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);
        queryParams.put("merchantCustId", CustId);
        queryParams.put("channel", "GG_APP");


        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj, headers, queryParams);

        Assert.assertEquals(respObj.getStatusCode(), 200);
        SignatureDMSID = respObj.path("uploadedDocDetailsSet[0].uploadedDocs[0].uuid");
        BusinessOwnerPhotoDMSID = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].uuid");
        AadharPhotoDMSID1 = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['0']");
        AadharPhotoDMSID2 = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");


        System.out.println(SignatureDMSID + BusinessOwnerPhotoDMSID + AadharPhotoDMSID1 + AadharPhotoDMSID2);
        // Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "McoLeadFetchDocStatus")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC26_McoIndividualLeadQC() throws Exception {

        DBConnection dbConnectionObj = new DBConnection();


        int Ubmid = dbConnectionObj.getUserBusinessMappingId(FSEMobileNumber, "fse_diy");

        dbConnectionObj.assignAgentViaDB("1152", Ubmid);


        System.out.println("Ubmid is " + Ubmid);

        Long MCOIndividualWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);

        System.out.println("MCOIndividualWorkflowStatusId is " + MCOIndividualWorkflowStatusId);

        //Save value of MCOIndividualWorkflowStatusId in string
        String wfsid = String.valueOf(MCOIndividualWorkflowStatusId);
        System.out.println("wfsid is " + wfsid);

        String requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadFseDiy.json";
        EditLead EditLeadObj = new EditLead(leadId, requestPath);
        EditLeadObj.getProperties().setProperty("uuidAadharPhoto1", AadharPhotoDMSID1);
        EditLeadObj.getProperties().setProperty("uuidAadharPhoto2", AadharPhotoDMSID2);
        EditLeadObj.getProperties().setProperty("uuidSignature", SignatureDMSID);
        EditLeadObj.getProperties().setProperty("uuidSignature1", SignatureDMSID);
        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto", BusinessOwnerPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto1", BusinessOwnerPhotoDMSID);
        EditLeadObj.getProperties().setProperty("workflowStatusId", wfsid);
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("action", "SUBMIT");
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        // headers.put("session_token", QCAgentsToken);
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("Cookie", XMWCookie);
        Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);
        Assert.assertEquals(QCMCOLead.getStatusCode(), 200);

    }

}





