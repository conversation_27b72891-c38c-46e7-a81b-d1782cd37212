package OCL.ICICI;

import Request.MerchantService.v2.ICICI.CreateLeadWithGST;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class CreateLeadWithGSTIN extends BaseMethod
{
    public static final Logger LOGGER = LogManager.getLogger(CreateLeadWithoutGSTIN.class);

    public static String Additional = "**********";

    Map<String, String> CommonHeaders ;
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    Utilities utilities = new Utilities();
    String AuthorizationToken = utilities.generateJwtTokenForICICI();
    String mobilenumber = utilities.randomMobileNumberGenerator();
    String PAN = utilities.randomIndividualPANValueGenerator();
    Long bankAccNumber = utilities.generateRandom(10);
    String bankAccountNumber = bankAccNumber.toString();
    String bankAccountNum = "3" + bankAccountNumber;

    public CreateLeadWithGSTIN() throws Exception {
    }

    public Map<String,String> SetCommonHeader()
    {
        Map<String,String> SetUp = new HashMap<>();
        SetUp.put("Content-Type", "application/json");
        SetUp.put("Authorization", AuthorizationToken);
        SetUp.put("additional", Additional);

        return SetUp;
    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with empty gst", priority = 0)

    public void TC0001_CreateLead() throws Exception
    {
        CreateLeadWithGST createLeadWithGSTIN = new CreateLeadWithGST();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("gst"," ");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithGST(createLeadWithGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with invalid gst", priority = 0)

    public void TC0002_CreateLead() throws Exception
    {
        CreateLeadWithGST createLeadWithGSTIN = new CreateLeadWithGST();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("gst","01AWLPH00308D1ZJ");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithGST(createLeadWithGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Gstin and PAN does not matched");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with invalid gst and invalid PAN and correct entity", priority = 0)

    public void TC0003_CreateLead() throws Exception
    {
        CreateLeadWithGST createLeadWithGSTIN = new CreateLeadWithGST();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("gst","01AWLPH0038D1ZJ");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan","AWLPJ22338F");
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithGST(createLeadWithGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Invalid entity type entered");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with invalid gst and invalid PAN and invalid entity", priority = 0)

    public void TC0004_CreateLead() throws Exception
    {
        CreateLeadWithGST createLeadWithGSTIN = new CreateLeadWithGST();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("gst","01AWLPH0038D1ZJ");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIPS");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan","AWLPJ22338F");
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithGST(createLeadWithGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Invalid entity type entered");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with valid gst and invalid PAN and invalid entity", priority = 0)

    public void TC0005_CreateLead() throws Exception
    {
        CreateLeadWithGST createLeadWithGSTIN = new CreateLeadWithGST();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("gst","01"+PAN+"1ZL");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIPS");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan","AWLPJ22338F");
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithGST(createLeadWithGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Invalid entity type entered");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with valid gst and valid PAN and invalid entity", priority = 0)

    public void TC0006_CreateLead() throws Exception
    {
        CreateLeadWithGST createLeadWithGSTIN = new CreateLeadWithGST();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("gst","01"+PAN+"1ZL");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIPS");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithGST(createLeadWithGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Invalid entity type entered");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with valid gst and invalid PAN and valid entity", priority = 0)

    public void TC0007_CreateLead() throws Exception
    {
        CreateLeadWithGST createLeadWithGSTIN = new CreateLeadWithGST();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("gst","01"+PAN+"1ZL");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan","AWLPH99002F");
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithGST(createLeadWithGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Invalid entity type entered");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with invalid gst and valid PAN and invalid entity", priority = 1)

    public void TC0008_CreateLead() throws Exception
    {
        CreateLeadWithGST createLeadWithGSTIN = new CreateLeadWithGST();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("gst","01AWLPH990021ZL");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIPS");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithGST(createLeadWithGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Invalid entity type entered");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with valid gst and valid PAN and valid entity", priority = 0)

    public void TC0009_CreateLead() throws Exception
    {
        CreateLeadWithGST createLeadWithGSTIN = new CreateLeadWithGST();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("gst","01"+PAN+"1ZJ");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithGST(createLeadWithGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with valid gst and same mobile number ", priority = 1)

    public void TC0010_CreateLead() throws Exception
    {
        CreateLeadWithGST createLeadWithGSTIN = new CreateLeadWithGST();

        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("gst","01"+PAN+"1ZJ");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithGST(createLeadWithGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with valid gst and same mobile number (multiple mid case)", priority = 2,
            dependsOnMethods = "TC0010_CreateLead")

    public void TC0011_CreateLead() throws Exception
    {
        CreateLeadWithGST createLeadWithGSTIN = new CreateLeadWithGST();

        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("gst","01"+PAN+"1ZJ");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithGST(createLeadWithGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Positive_Create_Lead_ICICI with device count non-zero ", priority = 2,
            dependsOnMethods = "TC0011_CreateLead")

    public void TC0012_CreateLead() throws Exception
    {
        CreateLeadWithGST createLeadWithGSTIN = new CreateLeadWithGST();

        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","2");
        body.put("email","<EMAIL>");
        body.put("gst","01"+PAN+"1ZJ");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithGST(createLeadWithGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with new number and device count as non-zero ", priority = 2,
            dependsOnMethods = "TC0011_CreateLead")

    public void TC0013_CreateLead() throws Exception
    {
        CreateLeadWithGST createLeadWithGSTIN = new CreateLeadWithGST();

        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("gst","01"+PAN+"1ZJ");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithGST(createLeadWithGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with new number and device count as non-zero ", priority = 2,
            dependsOnMethods = "TC0011_CreateLead")

    public void TC0014_CreateLead() throws Exception
    {
        CreateLeadWithGST createLeadWithGSTIN = new CreateLeadWithGST();

        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("gst","01"+PAN+"1ZJ");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithGST(createLeadWithGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }
}
