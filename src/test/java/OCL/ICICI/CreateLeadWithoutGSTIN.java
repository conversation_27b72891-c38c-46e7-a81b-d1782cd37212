package OCL.ICICI;

import Request.MerchantService.v2.ICICI.CreateLeadWithOutGSTIN;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class CreateLeadWithoutGSTIN extends BaseMethod
{
    public static final Logger LOGGER = LogManager.getLogger(CreateLeadWithoutGSTIN.class);

    public static String Additional = "9810618965";

    Map<String, String> CommonHeaders ;
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    Utilities utilities = new Utilities();
    String AuthorizationToken = utilities.generateJwtTokenForICICI();

    public CreateLeadWithoutGSTIN() throws Exception {
    }

    public Map<String,String> SetCommonHeader()
    {
        Map<String,String> SetUp = new HashMap<>();
        SetUp.put("Content-Type", "application/json");
        SetUp.put("Authorization", AuthorizationToken);
        SetUp.put("additional", Additional);

        return SetUp;
    }
/*
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without solution type", priority = 1)

    public void TC0001_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType"," ");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),500);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"No enum constant com.paytm.oe.enums.SolutionType. ");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without mobile number", priority = 1)

    public void TC0002_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile"," ");
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Missing mobile number");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without device count", priority = 1)

    public void TC0003_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount"," ");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Your details could not be saved. Please enter valid Device Count and try again.");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without emailID", priority = 1)

    public void TC0004_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email"," ");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Your details could not be saved. Please enter valid Email ID and try again.");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without request identifier", priority = 1)

    public void TC0005_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier"," ");
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Invalid request identifier");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without entity type", priority = 1)

    public void TC0006_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType"," ");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Missing Entity Type");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without business Name", priority = 1)

    public void TC0007_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName"," ");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Missing business name.");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without displayname", priority = 1)

    public void TC0008_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName"," ");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Display Name is Mandatory");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without businessCategory", priority = 1)

    public void TC0009_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory"," ");
        body.put("subCategory","Loans");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"We could not save your details.Please Enter Category/SubCategory Details.");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without subCategory", priority = 1)

    public void TC0010_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory"," ");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"We could not save your details.Please Enter Category/SubCategory Details.");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without PAN", priority = 1)

    public void TC0011_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan"," ");
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"We could not save your details.Please Enter PAN Details.");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without BankName", priority = 1)

    public void TC0012_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName"," ");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Missing  Bank Name");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without bankAccountNumber", priority = 1)

    public void TC0013_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber"," ");
        body.put("ifsc","ICIC0006622");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Missing  Bank Account Number");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without IFSC code", priority = 1)

    public void TC0014_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc"," ");
        body.put("accountType","CURRENT");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"IFSC Code Missing");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without AccountType", priority = 1)

    public void TC0015_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType"," ");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Your details could not be saved. Please enter valid Account Type and try again.");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without line1 of Legal Address", priority = 1)

    public void TC0016_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1"," ");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without line2 of Legal Address", priority = 1)

    public void TC0017_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Ghaziabad");
        body.put("line2"," ");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without line3 of Legal Address", priority = 1)

    public void TC0018_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3"," ");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without pincode of billing Address", priority = 1)

    public void TC0019_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode"," ");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without line1 of billing Address", priority = 1)

    public void TC0020_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1"," ");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);


        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without line2 of billing Address", priority = 1)

    public void TC0021_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2"," ");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);


        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without line3 of billing Address", priority = 1)

    public void TC0022_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3"," ");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);


        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without pincode of billing Address", priority = 1)

    public void TC0023_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode"," ");
        body.put("line1","Ghaziabad");
        body.put("line2","Ghaziabad");
        body.put("line3","Ghaziabad");
        body.put("pincode","110041");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);


        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without line1 of correspondence Address", priority = 1)

    public void TC0024_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1"," ");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);


        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Address line 1 is empty");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without line2 of correspondence Address", priority = 1)

    public void TC0025_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2"," ");
        body.put("line3","Haryana");
        body.put("pincode","132104");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without line3 of correspondence Address", priority = 1)

    public void TC0026_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3"," ");
        body.put("pincode","132104");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Address line 3 is empty");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI without pincode of correspondence Address", priority = 1)

    public void TC0027_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode"," ");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),500);
        //Assert.(fetchLeadResponse.jsonPath().getString("displayMessage"),"No such pincode exists");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with incorrect of solution type", priority = 1)

    public void TC0028_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","online_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Missing Agent Customer Id");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with incorrect of mobile number", priority = 1)

    public void TC0029_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile","*********");
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Invalid Mobile Number");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with incorrect device count", priority = 1)

    public void TC0030_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","-1");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with incorrect Email ID", priority = 1)

    public void TC0031_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","abcd");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Invalid email");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with incorrect entity type", priority = 1)

    public void TC0032_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Invalid entity type entered");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with incorrect business category", priority = 1)

    public void TC0033_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSII");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Please enter valid category/subcategory");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with incorrect sub-category", priority = 1)

    public void TC0034_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurancee");
        body.put("pan",PAN);
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Please enter valid category/subcategory");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with incorrect PAN", priority = 1)

    public void TC0035_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan","**********");
        body.put("bankName","ICICI Bank");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Invalid entity type entered");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with incorrect BankName", priority = 1)

    public void TC0036_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with incorrect IFSC code", priority = 1)

    public void TC0037_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC6622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Invalid ifsc");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with incorrect account Type", priority = 1)

    public void TC0038_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVINGS");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with incorrect pincode of legal address", priority = 1)

    public void TC0039_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","**********");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with incorrect pincode of billing address", priority = 1)

    public void TC0040_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","**********");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Lead created successfully");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Create_Lead_ICICI with incorrect pincode of correspondance address", priority = 1)

    public void TC0041_CreateLead() throws Exception
    {
        CreateLeadWithOutGSTIN createLeadWithoutGSTIN = new CreateLeadWithOutGSTIN();
        String mobilenumber = utilities.randomMobileNumberGenerator();
        String PAN = utilities.randomIndividualPANValueGenerator();
        Long bankAccNumber = utilities.generateRandom(10);
        String bankAccountNumber = bankAccNumber.toString();
        String bankAccountNum = "3" + bankAccountNumber;
        String requestIdentifier = utilities.randomMobileNumberGeneratorStartWith(1);
        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String,String>();
        body.put("solutionType","offline_merchant");
        body.put("mobile",mobilenumber);
        body.put("deviceCount","0");
        body.put("email","<EMAIL>");
        body.put("requestIdentifier",requestIdentifier);
        body.put("entityType","PROPRIETORSHIP");
        body.put("businessName","TOUCH WOOD LIMITED");
        body.put("displayName","TOUCH WOOD LIMITED");
        body.put("businessCategory","BFSI");
        body.put("subCategory","Insurance");
        body.put("pan",PAN);
        body.put("bankName","ICICI");
        body.put("bankAccountNumber",bankAccountNum);
        body.put("ifsc","ICIC0006622");
        body.put("accountType","SAVING");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","132104");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","1321040");
        body.put("line1","Haryana");
        body.put("line2","Haryana");
        body.put("line3","Haryana");
        body.put("pincode","*********");

        Response fetchLeadResponse = middlewareServicesObject.CreateLeadWithOutGST(createLeadWithoutGSTIN,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),500);
        //Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"No such pincode exists");

    }
    */

}
