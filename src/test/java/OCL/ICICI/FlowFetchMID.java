package OCL.ICICI;

import Request.MerchantService.v2.ICICI.FetchMID;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FlowFetchMID extends BaseMethod
{
   // public static final Logger LOGGER = Logger.getLogger(FlowUnifiedPaymentWithoutPan.class);

    public static String mobileNo = "";
    public static String ApplicantToken = "";
    public static String Additional = "9810618965";

    Map<String, String> CommonHeaders ;
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    Utilities utilities = new Utilities();
    String AuthorizationToken = utilities.generateJwtTokenForICICI();


    public FlowFetchMID() throws Exception {
    }

    public Map<String,String> SetCommonHeader()
    {
        Map<String,String> SetUp = new HashMap<>();
        SetUp.put("Content-Type", "application/json");
        SetUp.put("Authorization", AuthorizationToken);
        SetUp.put("additional", Additional);

        return SetUp;
    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_MID with incorrect mobile Number", priority = 1)

    public void TC0001_FetchMid() throws Exception {
        FetchMID fetchMID = new FetchMID();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mobile", "5882616621");
        queryParams.put("solutionType", "offline_merchant");
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchMIDICICI(fetchMID,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Mid Not found");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_MID with empty mobile Number", priority = 2)

    public void TC0002_FetchMid() throws Exception {
        FetchMID fetchMID = new FetchMID();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mobile", "");
        queryParams.put("solutionType", "offline_merchant");
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchMIDICICI(fetchMID,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Mid Not found");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_MID with empty solutionType", priority = 3)

    public void TC0003_FetchMid() throws Exception {
        FetchMID fetchMID = new FetchMID();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mobile", "5882616621");
        queryParams.put("solutionType", "");
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchMIDICICI(fetchMID,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Mid Not found");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_MID with empty channel", priority = 4)

    public void TC0004_FetchMid() throws Exception {
        FetchMID fetchMID = new FetchMID();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mobile", "5882616621");
        queryParams.put("solutionType", "offline_merchant");
        queryParams.put("channel", "");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchMIDICICI(fetchMID,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Mid Not found");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_MID with empty query params", priority = 5)

    public void TC0005_FetchMid() throws Exception {
        FetchMID fetchMID = new FetchMID();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mobile", "");
        queryParams.put("solutionType", "");
        queryParams.put("channel", "");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchMIDICICI(fetchMID,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),500);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"No enum constant com.paytm.oe.enums.SolutionType.");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Positive_Fetch_MID with correct mobile number", priority = 6)

    public void TC0006_FetchMid() throws Exception {
        FetchMID fetchMID = new FetchMID();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mobile", "5882616992");
        queryParams.put("solutionType", "offline_merchant");
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchMIDICICI(fetchMID,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertNotNull(fetchLeadResponse.jsonPath().getString("midDetails.mid"));

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Positive_Fetch_MID with incorrect solution type", priority = 7)

    public void TC0007_FetchMid() throws Exception {
        FetchMID fetchMID = new FetchMID();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mobile", "8545224555");
        queryParams.put("solutionType", "online");
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchMIDICICI(fetchMID,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),500);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"No enum constant com.paytm.oe.enums.SolutionType.online");
    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Positive_Fetch_MID with incorrect channel", priority = 8)

    public void TC0008_FetchMid() throws Exception {
        FetchMID fetchMID = new FetchMID();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mobile", "8545224555");
        queryParams.put("solutionType", "offline_merchant");
        queryParams.put("channel", "ICICIC");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchMIDICICI(fetchMID,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertNotNull(fetchLeadResponse.jsonPath().getString("midDetails.mid"));
    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_MID with incorrect queryParams", priority = 7)

    public void TC0009_FetchMid() throws Exception {
        FetchMID fetchMID = new FetchMID();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mobile", "8545224555");
        queryParams.put("solutionType", "online");
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchMIDICICI(fetchMID,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),500);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"No enum constant com.paytm.oe.enums.SolutionType.online");
    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_MID without mobile and solution type", priority = 7)

    public void TC0010_FetchMid() throws Exception {
        FetchMID fetchMID = new FetchMID();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mobile", "");
        queryParams.put("solutionType", "");
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchMIDICICI(fetchMID,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),500);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"No enum constant com.paytm.oe.enums.SolutionType.");
    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_MID without mobile and channel", priority = 7)

    public void TC0011_FetchMid() throws Exception {
        FetchMID fetchMID = new FetchMID();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mobile", "");
        queryParams.put("solutionType", "");
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchMIDICICI(fetchMID,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),500);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"No enum constant com.paytm.oe.enums.SolutionType.");
    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_MID without solution type and channel", priority = 7)

    public void TC0012_FetchMid() throws Exception {
        FetchMID fetchMID = new FetchMID();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mobile", "5882616992");
        queryParams.put("solutionType", "");
        queryParams.put("channel", "");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchMIDICICI(fetchMID,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),500);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"No enum constant com.paytm.oe.enums.SolutionType.");
    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_MID without solution type and channel", priority = 7)

    public void TC0013_FetchMid() throws Exception {
        FetchMID fetchMID = new FetchMID();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchMIDICICI(fetchMID,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
    }
}
