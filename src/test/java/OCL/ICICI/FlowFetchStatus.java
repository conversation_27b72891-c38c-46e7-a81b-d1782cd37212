package OCL.ICICI;

import Request.MerchantService.v2.ICICI.FetchStatus;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FlowFetchStatus extends BaseMethod
{

    //public static final Logger LOGGER = Logger.getLogger(FlowUnifiedPaymentWithoutPan.class);

    public static String mobileNo = "";
    public static String ApplicantToken = "";
    public static String Additional = "9810618965";
    public static String cookie = "JSESSIONID=ACEE6806EEDC06F8340B2BBB512DC094";

    Map<String, String> CommonHeaders ;
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    Utilities utilities = new Utilities();
    String AuthorizationToken = utilities.generateJwtTokenForICICI();

    public FlowFetchStatus() throws Exception {
    }

    public Map<String,String> SetCommonHeader()
    {
        Map<String,String> SetUp = new HashMap<>();

        SetUp.put("Content-Type", "application/json");
        SetUp.put("Authorization", AuthorizationToken);
        SetUp.put("additional", Additional);
        SetUp.put("Cookie", cookie);

        return SetUp;
    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_Status with incorrect request identifier", priority = 1)

    public void TC0001_FetchStatus() throws Exception {
        FetchStatus fetchStatus = new FetchStatus();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "offline_merchant");
        queryParams.put("requestID", "9293HSJJ");
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchStatusICICI(fetchStatus,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Invalid request identifier");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_Status with empty solution Type", priority = 2)

    public void TC0002_FetchStatus() throws Exception {
        FetchStatus fetchStatus = new FetchStatus();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "");
        queryParams.put("requestID", "9293HSJJ");
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchStatusICICI(fetchStatus,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),500);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"No enum constant com.paytm.oe.enums.SolutionType.");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_Status with empty request identifier", priority = 3)

    public void TC0003_FetchStatus() throws Exception {
        FetchStatus fetchStatus = new FetchStatus();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "offline_merchant");
        queryParams.put("requestID", "");
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchStatusICICI(fetchStatus,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Invalid request identifier");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_Status with empty channel", priority = 4)

    public void TC0004_FetchStatus() throws Exception {
        FetchStatus fetchStatus = new FetchStatus();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "offline_merchant");
        queryParams.put("requestID", "9293HSJJ");
        queryParams.put("channel", "");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchStatusICICI(fetchStatus,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Invalid request identifier");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_Status with empty query params", priority = 5)

    public void TC0005_FetchStatus() throws Exception {
        FetchStatus fetchStatus = new FetchStatus();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "");
        queryParams.put("requestID", "");
        queryParams.put("channel", "");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchStatusICICI(fetchStatus,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),500);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"No enum constant com.paytm.oe.enums.SolutionType.");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_Status with incorrect solution type", priority = 6)

    public void TC0006_FetchStatus() throws Exception {
        FetchStatus fetchStatus = new FetchStatus();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "online");
        queryParams.put("requestID", "9293HSJJ");
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchStatusICICI(fetchStatus,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),500);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"No enum constant com.paytm.oe.enums.SolutionType.online");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_Status with correct Request Identifier", priority = 7)

    public void TC00087_FetchStatus() throws Exception {
        FetchStatus fetchStatus = new FetchStatus();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "offline_merchant");
        queryParams.put("requestID", "9293HSJJ");
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchStatusICICI(fetchStatus,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertNull(fetchLeadResponse.jsonPath().getString("displayMessage"));
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("merchantOnboardingLeadStatus"),"LEAD_CLOSED");
        Assert.assertNotNull(fetchLeadResponse.jsonPath().getString("mid"));

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Positive_Fetch_Status with correct details", priority = 8)

    public void TC0008_FetchStatus() throws Exception {
        FetchStatus fetchStatus = new FetchStatus();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "offline_merchant");
        queryParams.put("requestID", "1629350923sd9");
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchStatusICICI(fetchStatus,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertNull(fetchLeadResponse.jsonPath().getString("displayMessage"));
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("merchantOnboardingLeadStatus"),"LEAD_CLOSED");
        Assert.assertNotNull(fetchLeadResponse.jsonPath().getString("mid"));
        Assert.assertNotNull(fetchLeadResponse.jsonPath().getString("deviceMappingLeadStatus.leadId"));
        Assert.assertNotNull(fetchLeadResponse.jsonPath().getString("deviceMappingLeadStatus.status"));
    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_Status with incorrect channel", priority = 9)

    public void TC0009_FetchStatus() throws Exception {
        FetchStatus fetchStatus = new FetchStatus();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "offline_merchant");
        queryParams.put("requestID", "1629350923sd9");
        queryParams.put("channel", "ICICIC");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchStatusICICI(fetchStatus,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"Invalid request identifier");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_Status with all incorrect details", priority = 9)

    public void TC0010_FetchStatus() throws Exception {
        FetchStatus fetchStatus = new FetchStatus();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "offline_merchants");
        queryParams.put("requestID", "1629350923sd");
        queryParams.put("channel", "ICICIC");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchStatusICICI(fetchStatus,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),500);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"No enum constant com.paytm.oe.enums.SolutionType.offline_merchants");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_Status with all multiple entries", priority = 9)

    public void TC0011_FetchStatus() throws Exception {
        FetchStatus fetchStatus = new FetchStatus();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "offline_merchant");
        queryParams.put("requestID", "162935099");
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchStatusICICI(fetchStatus,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),200);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"multiple entries exist for entered request identifier");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_Status with all multiple entries with invalid solution type", priority = 9)

    public void TC0012_FetchStatus() throws Exception {
        FetchStatus fetchStatus = new FetchStatus();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "offline_merchants");
        queryParams.put("requestID", "162935099");
        queryParams.put("channel", "ICICI");

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchStatusICICI(fetchStatus,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),500);
        Assert.assertEquals(fetchLeadResponse.jsonPath().getString("displayMessage"),"No enum constant com.paytm.oe.enums.SolutionType.offline_merchants");

    }

    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "Negative_Fetch_Status without any query params", priority = 9)

    public void TC0013_FetchStatus() throws Exception {
        FetchStatus fetchStatus = new FetchStatus();

        CommonHeaders = SetCommonHeader();
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> body = new HashMap<String, String>();

        Response fetchLeadResponse = middlewareServicesObject.FetchStatusICICI(fetchStatus,queryParams,CommonHeaders,body);

        Assert.assertEquals(fetchLeadResponse.getStatusCode(),400);
    }
}
