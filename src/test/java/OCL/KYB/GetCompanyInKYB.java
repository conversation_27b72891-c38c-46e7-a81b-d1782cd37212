package OCL.KYB;

import Request.CIF.EditAddressInKyb;
import Request.KYB.GetCompany;
import Services.KYB.KybServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class GetCompanyInKYB extends BaseMethod {
    KybServices KybServicesObj = new KybServices();
    private static final Logger LOGGER = LogManager.getLogger(GetCompanyInKYB.class);
    public static String jwtToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE2OTUwMTc0NTQxOTIiLCJjdXN0X2lkIjoiMTUyMDM0MjAzOTAwMCIsImNsaWVudF9pZCI6IkdHLU9FLXN0YWdpbmcifQ.wUfmRcTShXGBnSm1KfTezEDEwI1TXOpm9k6jBalIZzw";
    public static String businessId = "A011hm77tekzu179";

    @Test(groups = {"Regression"}, description = "Get company in kyb ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_GetCompanyInKYB() {

        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }


        GetCompany GetCompanyObj = new GetCompany();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", KYBToken);
        headers.put("businessId", businessId);
        Response KybServicesObjResp = KybServicesObj.KybGetCompany(GetCompanyObj, headers);
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(groups = {"Regression"}, description = "Get company in kyb  with invalid kyb id")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_GetCompanyInKYBWithInvalidKybId() {

        GetCompany GetCompanyObj = new GetCompany();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);
        headers.put("businessId", "A02poyrvreekg5751");
        Response KybServicesObjResp =null;
        try {
            KybServicesObjResp = KybServicesObj.KybGetCompany(GetCompanyObj, headers);
        } catch (PatternSyntaxException e) {
            // Handle the exception here
            e.printStackTrace();
        }

        if (KybServicesObjResp != null) {
            LOGGER.info("Ods Acl Service Response" + KybServicesObjResp.statusCode());
            Assert.assertEquals(KybServicesObjResp.statusCode(), 410);
        }
        //int StatusCode = KybServicesObjResp.getStatusCode();
       // Assert.assertEquals(StatusCode, 410);

    }
    @Test(groups = {"Regression"}, description = "Get company in kyb  with empty kyb id")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_GetCompanyInKYBWithEmptyKybId() {
        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }
        GetCompany GetCompanyObj = new GetCompany();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", KYBToken);
        headers.put("businessId", "");
        Response KybServicesObjResp = KybServicesObj.KybGetCompany(GetCompanyObj, headers);
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(groups = {"Regression"}, description = "Get company in kyb  with merchant kyb id")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_GetCompanyInKYBWithMerchantKybId() {
        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }
        GetCompany GetCompanyObj = new GetCompany();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", KYBToken);
        headers.put("businessId", "B011hfiw1m0aw456");
        Response KybServicesObjResp = KybServicesObj.KybGetCompany(GetCompanyObj, headers);
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }
    @Test(groups = {"Regression"}, description = "Fetch company kyb id")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_FetchCompanyKybId() {
        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }
        GetCompany GetCompanyObj = new GetCompany();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", KYBToken);
        headers.put("businessId", businessId);
        Response KybServicesObjResp = KybServicesObj.KybGetCompany(GetCompanyObj, headers);
        String companyKybId=KybServicesObjResp.jsonPath().getJsonObject("company.businessId").toString();
        System.out.print("Company kyb id is : "+companyKybId);
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }
}
