package OCL.KYB;

import OCL.Subscription.CreateSubscriptionPlan;
import Request.CIF.EditAddressInKyb;
import Request.CIF.KybGet;
import Request.KYB.GetAddressInKyb;
import Services.KYB.KybServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class EditAddressInKYB  extends BaseMethod
{
    KybServices KybServicesObj = new KybServices();
    GetAddressInKyb GetAddressInKybObj = new GetAddressInKyb();
    KybGet KybGetObj=new KybGet();
    private static final Logger LOGGER = LogManager.getLogger(EditAddressInKYB.class);

    public static String jwtToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE2ODMwMzI4Nzg2MzciLCJjdXN0X2lkIjoiMTUyMDM0MjAzOTAwMCIsImNsaWVudF9pZCI6IkdHLU9FLXN0YWdpbmcifQ.p7T1uuAFbvkBd-q8zOXq2f7qHlowFJuWs6tiykl7Gx4";
    public static String deviceId = "";
    public static String mid = "oHGIDR4152536349006";
    public static String deviceType = "EDC  ";
    public static String addressType = "Deployment";
    public static String line1 = "B-262";
    public static String line2 = "Brij Vihar";
    public static String status = "1";
    public static String formattedAddress="";
    public static String propertyLandmark="";
    public static String propertyNumber="";

    @Test(groups = {"Regression"}, description = "Edit Address in kyb ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_EditAddressInKYB() {

        EditAddressInKyb EditAddressInKybObj = new EditAddressInKyb(P.TESTDATA.get("EditAddressInKYBReqBody"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("deviceId", deviceId);
        body.put("deviceType",deviceType);
        body.put("addressType",addressType);
        body.put("status",status);
        body.put("line1", line1);
        body.put("line2", line2);
        Response KybServicesObjResp = KybServicesObj.KybEditAddress(EditAddressInKybObj, headers, body);
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(groups = {"Regression"}, description = "Get Address in kyb ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_GetAddressInKYB() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> params = new HashMap<String, String>();
        params.put("pgMid", mid);
        params.put("fields", "deploymentAddress");

        Response KybServicesObjResp = KybServicesObj.KybGetAddress(GetAddressInKybObj, headers, params);
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        deviceId=KybServicesObjResp.jsonPath().get("deploymentAddress.deviceAddress[1].deviceId");
        System.out.println("Fetched Device id is :  " +deviceId);
        Assert.assertEquals(KybServicesObjResp.jsonPath().get("deploymentAddress.deviceAddress[1].deviceId"), deviceId);
        System.out.println("Address verified successfully");

    }


    @Test(groups = {"Regression"}, description = "Pass Empty mid in Get Addrress in kyb ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_WithEmptyMiDInGetAddress() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> params = new HashMap<String, String>();
        params.put("pgMid", "");
        params.put("fields", "deploymentAddress");

        Response KybServicesObjResp = KybServicesObj.KybGetAddress(GetAddressInKybObj, headers, params);
        KybServicesObjResp.prettyPrint();
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(groups = {"Regression"}, description = "Pass Null mid in Get Address in kyb ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_WithNullMiDInGetAddress() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> params = new HashMap<String, String>();
        params.put("pgMid", "null");
        params.put("fields", "deploymentAddress");

        Response KybServicesObjResp = KybServicesObj.KybGetAddress(GetAddressInKybObj, headers, params);
        KybServicesObjResp.prettyPrint();
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(groups = {"Regression"}, description = "Don't Pass mid in Get Address in kyb ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_WithoutPassingMiDInGetAddress() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> params = new HashMap<String, String>();
        params.put("fields", "deploymentAddress");

        Response KybServicesObjResp = KybServicesObj.KybGetAddress(GetAddressInKybObj, headers, params);
        KybServicesObjResp.prettyPrint();
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(groups = {"Regression"}, description = "Pass invalid mid in Get Address in kyb ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_WithInvalidMiDInGetAddress() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> params = new HashMap<String, String>();
        params.put("pgMid", "oHGIDR4152536349006123");
        params.put("fields", "deploymentAddress");

        Response KybServicesObjResp = KybServicesObj.KybGetAddress(GetAddressInKybObj, headers, params);
        KybServicesObjResp.prettyPrint();
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(groups = {"Regression"}, description = "Pass Empty field in Get Addrress in kyb ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_WithEmptyFieldInGetAddress() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> params = new HashMap<String, String>();
        params.put("pgMid", mid);
        params.put("fields", " ");

        Response KybServicesObjResp = KybServicesObj.KybGetAddress(GetAddressInKybObj, headers, params);
        KybServicesObjResp.prettyPrint();
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(groups = {"Regression"}, description = "Pass Null fields in Get Address in kyb ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_WithNullFieldsInGetAddress() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> params = new HashMap<String, String>();
        params.put("pgMid", mid);
        params.put("fields", "null");

        Response KybServicesObjResp = KybServicesObj.KybGetAddress(GetAddressInKybObj, headers, params);
        KybServicesObjResp.prettyPrint();
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(groups = {"Regression"}, description = "Don't Pass fields in Get Address in kyb ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_WithoutPassingFieldsInGetAddress() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> params = new HashMap<String, String>();
        params.put("pgMid", mid);

        Response KybServicesObjResp = KybServicesObj.KybGetAddress(GetAddressInKybObj, headers, params);
        KybServicesObjResp.prettyPrint();
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(groups = {"Regression"}, description = "Pass invalid fields in Get Address in kyb ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_WithInvalidFieldsInGetAddress() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> params = new HashMap<String, String>();
        params.put("pgMid", mid);
        params.put("fields", "deploymentAddress12");

        Response KybServicesObjResp = KybServicesObj.KybGetAddress(GetAddressInKybObj, headers, params);
        KybServicesObjResp.prettyPrint();
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(groups = {"Regression"}, description = "Edit Address in kyb with empty mid ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_EditAddressInKYBWithEmptyMid() {

        EditAddressInKyb EditAddressInKybObj = new EditAddressInKyb(P.TESTDATA.get("EditAddressInKYBReqBody"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", " ");
        body.put("deviceId", deviceId);
        body.put("deviceType",deviceType);
        body.put("addressType",addressType);
        body.put("status",status);
        body.put("line1", line1);
        body.put("line2", line2);
        Response KybServicesObjResp = KybServicesObj.KybEditAddress(EditAddressInKybObj, headers, body);
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(groups = {"Regression"}, description = "Edit Address in kyb with empty device Id")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_EditAddressInKYBWithEmptyDeviceId() {

        EditAddressInKyb EditAddressInKybObj = new EditAddressInKyb(P.TESTDATA.get("EditAddressInKYBReqBody"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("deviceId", " ");
        body.put("deviceType",deviceType);
        body.put("addressType",addressType);
        body.put("status",status);
        body.put("line1", line1);
        body.put("line2", line2);
        Response KybServicesObjResp = KybServicesObj.KybEditAddress(EditAddressInKybObj, headers, body);
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(groups = {"Regression"}, description = "Edit Address in kyb with empty device Type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_EditAddressInKYBWithEmptyDeviceType() {

        EditAddressInKyb EditAddressInKybObj = new EditAddressInKyb(P.TESTDATA.get("EditAddressInKYBReqBody"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("deviceId", deviceId);
        body.put("deviceType","");
        body.put("addressType",addressType);
        body.put("status",status);
        body.put("line1", line1);
        body.put("line2", line2);
        Response KybServicesObjResp = KybServicesObj.KybEditAddress(EditAddressInKybObj, headers, body);
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(groups = {"Regression"}, description = "Edit Address in kyb with invalid device Type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014_EditAddressInKYBWithInvalidDeviceType() {

        EditAddressInKyb EditAddressInKybObj = new EditAddressInKyb(P.TESTDATA.get("EditAddressInKYBReqBody"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("deviceId", deviceId);
        body.put("deviceType","EDC1");
        body.put("addressType",addressType);
        body.put("status",status);
        body.put("line1", line1);
        body.put("line2", line2);
        Response KybServicesObjResp = KybServicesObj.KybEditAddress(EditAddressInKybObj, headers, body);
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(groups = {"Regression"}, description = "Edit Address in kyb with empty address Type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015_EditAddressInKYBWithEmptyAddressType() {

        EditAddressInKyb EditAddressInKybObj = new EditAddressInKyb(P.TESTDATA.get("EditAddressInKYBReqBody"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("deviceId", deviceId);
        body.put("deviceType",deviceType);
        body.put("addressType"," ");
        body.put("status",status);
        body.put("line1", line1);
        body.put("line2", line2);
        Response KybServicesObjResp = KybServicesObj.KybEditAddress(EditAddressInKybObj, headers, body);
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(groups = {"Regression"}, description = "Edit Address in kyb with invalid address Type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016_EditAddressInKYBWithInvalidAddressType() {

        EditAddressInKyb EditAddressInKybObj = new EditAddressInKyb(P.TESTDATA.get("EditAddressInKYBReqBody"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("deviceId", deviceId);
        body.put("deviceType",deviceType);
        body.put("addressType","Deployment1");
        body.put("status",status);
        body.put("line1", line1);
        body.put("line2", line2);
        Response KybServicesObjResp = KybServicesObj.KybEditAddress(EditAddressInKybObj, headers, body);
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(groups = {"Regression"}, description = "Edit Address in kyb with status=0 address Type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017_EditAddressInKYBWithStatusZero() {

        EditAddressInKyb EditAddressInKybObj = new EditAddressInKyb(P.TESTDATA.get("EditAddressInKYBReqBody"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwtToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("deviceId", "JYOT12WSKOTI13");
        body.put("deviceType",deviceType);
        body.put("addressType",addressType);
        body.put("status","0");
        body.put("line1", line1);
        body.put("line2", line2);
        Response KybServicesObjResp = KybServicesObj.KybEditAddress(EditAddressInKybObj, headers, body);
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(groups = {"Regression"}, description = "Get KYB")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_018_GetKyb() {

        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", KYBToken);
        headers.put("uid", "1700918408");
        Response KybServicesObjResp = KybServicesObj.KybGet(KybGetObj, headers);
        int StatusCode = KybServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }
    @Test(groups = {"Regression"}, description = "Fetch Formatted address from Get KYB")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_019_FetchFormattedAddressFromGetKyb() {

        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", KYBToken);
        headers.put("uid", "1700918408");
        Response KybServicesObjResp = KybServicesObj.KybGet(KybGetObj, headers);
       formattedAddress= KybServicesObjResp.jsonPath().get("roles[0].contractDetails[0].resources.Shop[0].address.addressResources.formattedAddress");
        int StatusCode = KybServicesObjResp.getStatusCode();
        System.out.println("Formatted Address is : "+formattedAddress);
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(groups = {"Regression"}, description = "Fetch Property Landmark from Get KYB")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_020_FetchPropertyLandmarkFromGetKyb() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE2ODY4MzAyNTIyNTciLCJjdXN0X2lkIjoiMTEwNzE5OTQwNyIsImNsaWVudF9pZCI6InRlc3RjbGllbnQifQ.srjSzsILroQiNOv1AFliX8bgW3Gpu8zxwVL-F6joLME");
        headers.put("uid", "1700918408");
        Response KybServicesObjResp = KybServicesObj.KybGet(KybGetObj, headers);
        propertyLandmark= KybServicesObjResp.jsonPath().get("roles[0].contractDetails[0].resources.Shop[0].address.addressResources.propertyLandmark");
        int StatusCode = KybServicesObjResp.getStatusCode();
        System.out.println("Property Landmark is : "+propertyLandmark);
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(groups = {"Regression"}, description = "Fetch Property Number from Get KYB")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_021_FetchPropertyNumberFromGetKyb() {

        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", KYBToken);
        headers.put("uid", "1700918408");
        Response KybServicesObjResp = KybServicesObj.KybGet(KybGetObj, headers);
        propertyNumber= KybServicesObjResp.jsonPath().get("roles[0].contractDetails[0].resources.Shop[0].address.addressResources.propertyNumber");
        int StatusCode = KybServicesObjResp.getStatusCode();
        System.out.println("Property Number is : "+propertyNumber);
        Assert.assertEquals(StatusCode, 200);

    }
}

