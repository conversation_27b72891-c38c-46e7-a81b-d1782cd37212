package OCL.EDCDeviceUpgradeV2;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;

import Request.EDCDeviceUpgradeV2.DeviceUpgradevalidateEDCQr;
import Request.EDCDeviceUpgradeV2.EDCDeviceUpgradeV2;
import Services.EDCDeviceUpgradeEDC.EDCDeviceUpgradeServices;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestDeviceUpgradevalidateEDCQr extends BaseMethod{

	public String sessionToken;
	EDCDeviceUpgradeServices EDCDeviceUpgradeServicesobject =new EDCDeviceUpgradeServices();
	public Response response;
	public MiddlewareServices services;
	
	
	public Map<String, String> updateBody;
	public DeviceUpgradevalidateEDCQr obj;
	String mobileNo= "5509877651";
	String solution_type="edc_device_upgrade";
	
	@BeforeMethod()
	public void agentToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	@Test
	public void getStatusInCaseOfSuccessfullQrValidation() {
		   EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

	        Map<String, String> queryParam = new HashMap<String, String>();
	        queryParam.put("upgrade", "device");

	        HashMap<String, String> headers = new HashMap<>();
	        headers.put("content-type", "application/json");
	        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers.put("session_token",sessionToken);
	        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers.put("version", "7.1.9");

	        Map<String, String> body = new HashMap<String, String>();
	        body.put("entityType", "PROPRIETORSHIP");
	        body.put("userCustId","1002195189");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","5509877651");
	        body.put("businessLeadId","");
	        body.put("mid","KOnaKH62779279496920");
	        body.put("kybId","A01ay2ssfbqpc555");
	        body.put("wfVersion","V2");
	        body.put("serviceReason","Device physical damage");

	        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

	        Assert.assertEquals(respObj.statusCode(), 200);
	        String leadId = respObj.jsonPath().getJsonObject("leadId");

	     

	        updateBody = new HashMap<>();
	        updateBody.put("leadId", leadId);
	        updateBody.put("oem", "PAX");
	        updateBody.put("modelName", "A50");
	        updateBody.put("serialNo", "SEA508993368");
	        updateBody.put("deviceType", "ANDROID");
	        updateBody.put("osType", "ANDROID");
	        
	    	sessionToken=AgentSessionToken("7771216290", "paytm@123");

	        HashMap<String, String> headers2 = new HashMap<>();
	        headers2.put("content-type", "application/json");
	        headers2.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers2.put("session_token",sessionToken);
	        headers2.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers2.put("version", "7.1.9");

	        services = new MiddlewareServices();
	        obj = new DeviceUpgradevalidateEDCQr();
	        response=services.validateEDCQr(obj, updateBody, headers2);
	        Assert.assertEquals(response.getStatusCode(), 200);
	        

	        
	}

	
	@Test
	public void getStatusInCaseOfDeviceBindedWithDifferentMid() {
		   EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

	        Map<String, String> queryParam = new HashMap<String, String>();
	        queryParam.put("upgrade", "device");

	        HashMap<String, String> headers = new HashMap<>();
	        headers.put("content-type", "application/json");
	        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers.put("session_token",sessionToken);
	        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers.put("version", "7.1.9");

	        Map<String, String> body = new HashMap<String, String>();
	        body.put("entityType", "PROPRIETORSHIP");
	        body.put("userCustId","1700547833");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","8888333331");
	        body.put("businessLeadId","");
	        body.put("mid","pGeGtp45562989369059");
	        body.put("kybId","A08fy2yxc9h258");
	        body.put("wfVersion","V2");
	        body.put("serviceReason","Device physical damage");

	        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

	        Assert.assertEquals(respObj.statusCode(), 200);
	        String leadId = respObj.jsonPath().getJsonObject("leadId");

	     

	        updateBody = new HashMap<>();
	        updateBody.put("leadId", leadId);
	        updateBody.put("oem", "PAX");
	        updateBody.put("modelName", "A50");
	        updateBody.put("serialNo", "SEA508993368");
	        updateBody.put("deviceType", "ANDROID");
	        updateBody.put("osType", "ANDROID");
	        
	    	sessionToken=AgentSessionToken("7771216290", "paytm@123");

	        HashMap<String, String> headers2 = new HashMap<>();
	        headers2.put("content-type", "application/json");
	        headers2.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers2.put("session_token",sessionToken);
	        headers2.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers2.put("version", "7.1.9");

	        services = new MiddlewareServices();
	        obj = new DeviceUpgradevalidateEDCQr();
	        response=services.validateEDCQr(obj, updateBody, headers2);
	        Assert.assertEquals(response.getStatusCode(), 400);
	        

	        
	}
	
	
	@Test
	public void getStatusInCaseOfDeviceBindingDoesNotExist() {
		   EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

	        Map<String, String> queryParam = new HashMap<String, String>();
	        queryParam.put("upgrade", "device");

	        HashMap<String, String> headers = new HashMap<>();
	        headers.put("content-type", "application/json");
	        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers.put("session_token",sessionToken);
	        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers.put("version", "7.1.9");

	        Map<String, String> body = new HashMap<String, String>();
	        body.put("entityType", "PROPRIETORSHIP");
	        body.put("userCustId","1002195189");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","5509877651");
	        body.put("businessLeadId","");
	        body.put("mid","KOnaKH62779279496920");
	        body.put("kybId","A01ay2ssfbqpc555");
	        body.put("wfVersion","V2");
	        body.put("serviceReason","Device physical damage");

	        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

	        Assert.assertEquals(respObj.statusCode(), 200);
	        String leadId = respObj.jsonPath().getJsonObject("leadId");

	     

	        updateBody = new HashMap<>();
	        updateBody.put("leadId", leadId);
	        updateBody.put("oem", "PAX");
	        updateBody.put("modelName", "A50");
	        updateBody.put("serialNo", "SEA50899336x788");
	        updateBody.put("deviceType", "ANDROID");
	        updateBody.put("osType", "ANDROID");
	        
	    	sessionToken=AgentSessionToken("7771216290", "paytm@123");

	        HashMap<String, String> headers2 = new HashMap<>();
	        headers2.put("content-type", "application/json");
	        headers2.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers2.put("session_token",sessionToken);
	        headers2.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers2.put("version", "7.1.9");

	        services = new MiddlewareServices();
	        obj = new DeviceUpgradevalidateEDCQr();
	        response=services.validateEDCQr(obj, updateBody, headers2);
	        Assert.assertEquals(response.getStatusCode(), 400);
	        

	        
	}
	
	@Test
	public void getStatusInCaseOfLeadIdISNotvalid() {
		   EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

	        Map<String, String> queryParam = new HashMap<String, String>();
	        queryParam.put("upgrade", "device");

	        HashMap<String, String> headers = new HashMap<>();
	        headers.put("content-type", "application/json");
	        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers.put("session_token",sessionToken);
	        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers.put("version", "7.1.9");

	        Map<String, String> body = new HashMap<String, String>();
	        body.put("entityType", "PROPRIETORSHIP");
	        body.put("userCustId","1002195189");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","5509877651");
	        body.put("businessLeadId","");
	        body.put("mid","KOnaKH62779279496920");
	        body.put("kybId","A01ay2ssfbqpc555");
	        body.put("wfVersion","V2");
	        body.put("serviceReason","Device physical damage");

	        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

	        Assert.assertEquals(respObj.statusCode(), 200);
	        String leadId = respObj.jsonPath().getJsonObject("leadId");

	     

	        updateBody = new HashMap<>();
	        updateBody.put("leadId", leadId+"123");
	        updateBody.put("oem", "PAX");
	        updateBody.put("modelName", "A50");
	        updateBody.put("serialNo", "SEA508993368");
	        updateBody.put("deviceType", "ANDROID");
	        updateBody.put("osType", "ANDROID");
	        
	    	sessionToken=AgentSessionToken("7771216290", "paytm@123");

	        HashMap<String, String> headers2 = new HashMap<>();
	        headers2.put("content-type", "application/json");
	        headers2.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers2.put("session_token",sessionToken);
	        headers2.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers2.put("version", "7.1.9");

	        services = new MiddlewareServices();
	        obj = new DeviceUpgradevalidateEDCQr();
	        response=services.validateEDCQr(obj, updateBody, headers2);
	        Assert.assertEquals(response.getStatusCode(), 500);
	        

	}
	
	@Test
	public void getStatusInCaseOfInvalidDeviceOEM() {
		   EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

	        Map<String, String> queryParam = new HashMap<String, String>();
	        queryParam.put("upgrade", "device");

	        HashMap<String, String> headers = new HashMap<>();
	        headers.put("content-type", "application/json");
	        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers.put("session_token",sessionToken);
	        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers.put("version", "7.1.9");

	        Map<String, String> body = new HashMap<String, String>();
	        body.put("entityType", "PROPRIETORSHIP");
	        body.put("userCustId","1002195189");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","5509877651");
	        body.put("businessLeadId","");
	        body.put("mid","KOnaKH62779279496920");
	        body.put("kybId","A01ay2ssfbqpc555");
	        body.put("wfVersion","V2");
	        body.put("serviceReason","Device physical damage");

	        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

	        Assert.assertEquals(respObj.statusCode(), 200);
	        String leadId = respObj.jsonPath().getJsonObject("leadId");

	     

	        updateBody = new HashMap<>();
	        updateBody.put("leadId", leadId);
	        updateBody.put("oem", "PX");
	        updateBody.put("modelName", "A50");
	        updateBody.put("serialNo", "SEA508993368");
	        updateBody.put("deviceType", "ANDROID");
	        updateBody.put("osType", "ANDROID");
	        
	    	sessionToken=AgentSessionToken("7771216290", "paytm@123");

	        HashMap<String, String> headers2 = new HashMap<>();
	        headers2.put("content-type", "application/json");
	        headers2.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers2.put("session_token",sessionToken);
	        headers2.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers2.put("version", "7.1.9");

	        services = new MiddlewareServices();
	        obj = new DeviceUpgradevalidateEDCQr();
	        response=services.validateEDCQr(obj, updateBody, headers2);
	        Assert.assertEquals(response.getStatusCode(), 400);
	        
	        
	}
	
	@Test
	public void getStatusInCaseOfInvalidModel() {
		   EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

	        Map<String, String> queryParam = new HashMap<String, String>();
	        queryParam.put("upgrade", "device");

	        HashMap<String, String> headers = new HashMap<>();
	        headers.put("content-type", "application/json");
	        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers.put("session_token",sessionToken);
	        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers.put("version", "7.1.9");

	        Map<String, String> body = new HashMap<String, String>();
	        body.put("entityType", "PROPRIETORSHIP");
	        body.put("userCustId","1002195189");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","5509877651");
	        body.put("businessLeadId","");
	        body.put("mid","KOnaKH62779279496920");
	        body.put("kybId","A01ay2ssfbqpc555");
	        body.put("wfVersion","V2");
	        body.put("serviceReason","Device physical damage");

	        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

	        Assert.assertEquals(respObj.statusCode(), 200);
	        String leadId = respObj.jsonPath().getJsonObject("leadId");

	     

	        updateBody = new HashMap<>();
	        updateBody.put("leadId", leadId);
	        updateBody.put("oem", "PAX");
	        updateBody.put("modelName", "A8950");
	        updateBody.put("serialNo", "SEA508993368");
	        updateBody.put("deviceType", "ANDROID");
	        updateBody.put("osType", "ANDROID");
	        
	    	sessionToken=AgentSessionToken("7771216290", "paytm@123");

	        HashMap<String, String> headers2 = new HashMap<>();
	        headers2.put("content-type", "application/json");
	        headers2.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers2.put("session_token",sessionToken);
	        headers2.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers2.put("version", "7.1.9");

	        services = new MiddlewareServices();
	        obj = new DeviceUpgradevalidateEDCQr();
	        response=services.validateEDCQr(obj, updateBody, headers2);
	        Assert.assertEquals(response.getStatusCode(), 200);
	        

	        
	}
	
	
	@Test
	public void getStatusInCaseOfInvalidDeviceType() {
		   EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

	        Map<String, String> queryParam = new HashMap<String, String>();
	        queryParam.put("upgrade", "device");

	        HashMap<String, String> headers = new HashMap<>();
	        headers.put("content-type", "application/json");
	        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers.put("session_token",sessionToken);
	        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers.put("version", "7.1.9");

	        Map<String, String> body = new HashMap<String, String>();
	        body.put("entityType", "PROPRIETORSHIP");
	        body.put("userCustId","1002195189");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","5509877651");
	        body.put("businessLeadId","");
	        body.put("mid","KOnaKH62779279496920");
	        body.put("kybId","A01ay2ssfbqpc555");
	        body.put("wfVersion","V2");
	        body.put("serviceReason","Device physical damage");

	        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

	        Assert.assertEquals(respObj.statusCode(), 200);
	        String leadId = respObj.jsonPath().getJsonObject("leadId");

	     

	        updateBody = new HashMap<>();
	        updateBody.put("leadId", leadId);
	        updateBody.put("oem", "PAX");
	        updateBody.put("modelName", "A8950");
	        updateBody.put("serialNo", "SEA508993368");
	        updateBody.put("deviceType", "ANDROI");
	        updateBody.put("osType", "ANDROID");
	        
	    	sessionToken=AgentSessionToken("7771216290", "paytm@123");

	        HashMap<String, String> headers2 = new HashMap<>();
	        headers2.put("content-type", "application/json");
	        headers2.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers2.put("session_token",sessionToken);
	        headers2.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers2.put("version", "7.1.9");

	        services = new MiddlewareServices();
	        obj = new DeviceUpgradevalidateEDCQr();
	        response=services.validateEDCQr(obj, updateBody, headers2);
	        Assert.assertEquals(response.getStatusCode(), 200);
	        

	        
	}
	
	@Test
	public void getStatusInCaseOfInvalidOS() {
		   EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

	        Map<String, String> queryParam = new HashMap<String, String>();
	        queryParam.put("upgrade", "device");

	        HashMap<String, String> headers = new HashMap<>();
	        headers.put("content-type", "application/json");
	        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers.put("session_token",sessionToken);
	        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers.put("version", "7.1.9");

	        Map<String, String> body = new HashMap<String, String>();
	        body.put("entityType", "PROPRIETORSHIP");
	        body.put("userCustId","1002195189");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","5509877651");
	        body.put("businessLeadId","");
	        body.put("mid","KOnaKH62779279496920");
	        body.put("kybId","A01ay2ssfbqpc555");
	        body.put("wfVersion","V2");
	        body.put("serviceReason","Device physical damage");

	        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

	        Assert.assertEquals(respObj.statusCode(), 200);
	        String leadId = respObj.jsonPath().getJsonObject("leadId");

	     

	        updateBody = new HashMap<>();
	        updateBody.put("leadId", leadId);
	        updateBody.put("oem", "PAX");
	        updateBody.put("modelName", "A8950");
	        updateBody.put("serialNo", "SEA508993368");
	        updateBody.put("deviceType", "ANDROID");
	        updateBody.put("osType", "abc");
	        
	    	sessionToken=AgentSessionToken("7771216290", "paytm@123");

	        HashMap<String, String> headers2 = new HashMap<>();
	        headers2.put("content-type", "application/json");
	        headers2.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers2.put("session_token",sessionToken);
	        headers2.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers2.put("version", "7.1.9");

	        services = new MiddlewareServices();
	        obj = new DeviceUpgradevalidateEDCQr();
	        response=services.validateEDCQr(obj, updateBody, headers2);
	        Assert.assertEquals(response.getStatusCode(), 200);
	        

	        
	}
	
	@Test
	public void getStatusInCaseOfLeadIsNotPassedINRequest() {
		   EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

	        Map<String, String> queryParam = new HashMap<String, String>();
	        queryParam.put("upgrade", "device");

	        HashMap<String, String> headers = new HashMap<>();
	        headers.put("content-type", "application/json");
	        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers.put("session_token",sessionToken);
	        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers.put("version", "7.1.9");

	        Map<String, String> body = new HashMap<String, String>();
	        body.put("entityType", "PROPRIETORSHIP");
	        body.put("userCustId","1002195189");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","5509877651");
	        body.put("businessLeadId","");
	        body.put("mid","KOnaKH62779279496920");
	        body.put("kybId","A01ay2ssfbqpc555");
	        body.put("wfVersion","V2");
	        body.put("serviceReason","Device physical damage");

	        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

	        Assert.assertEquals(respObj.statusCode(), 200);
	        String leadId = respObj.jsonPath().getJsonObject("leadId");

	     

	        updateBody = new HashMap<>();
	        updateBody.put("leadId", "");
	        updateBody.put("oem", "PAX");
	        updateBody.put("modelName", "A8950");
	        updateBody.put("serialNo", "SEA508993368");
	        updateBody.put("deviceType", "ANDROID");
	        updateBody.put("osType", "abc");
	        
	    	sessionToken=AgentSessionToken("7771216290", "paytm@123");

	        HashMap<String, String> headers2 = new HashMap<>();
	        headers2.put("content-type", "application/json");
	        headers2.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers2.put("session_token",sessionToken);
	        headers2.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers2.put("version", "7.1.9");

	        services = new MiddlewareServices();
	        obj = new DeviceUpgradevalidateEDCQr();
	        response=services.validateEDCQr(obj, updateBody, headers2);
	        Assert.assertEquals(response.getStatusCode(), 400);
	        

	        
	}
	
	@Test
	public void getStatusInCaseOfDeviceIDNotPassedInRequest() {
		   EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

	        Map<String, String> queryParam = new HashMap<String, String>();
	        queryParam.put("upgrade", "device");

	        HashMap<String, String> headers = new HashMap<>();
	        headers.put("content-type", "application/json");
	        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers.put("session_token",sessionToken);
	        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers.put("version", "7.1.9");

	        Map<String, String> body = new HashMap<String, String>();
	        body.put("entityType", "PROPRIETORSHIP");
	        body.put("userCustId","1002195189");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","5509877651");
	        body.put("businessLeadId","");
	        body.put("mid","KOnaKH62779279496920");
	        body.put("kybId","A01ay2ssfbqpc555");
	        body.put("wfVersion","V2");
	        body.put("serviceReason","Device physical damage");

	        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

	        Assert.assertEquals(respObj.statusCode(), 200);
	        String leadId = respObj.jsonPath().getJsonObject("leadId");

	     

	        updateBody = new HashMap<>();
	        updateBody.put("leadId", leadId);
	        updateBody.put("oem", "PAX");
	        updateBody.put("modelName", "A8950");
//	        updateBody.put("serialNo", "");
	        updateBody.put("deviceType", "ANDROID");
	        updateBody.put("osType", "abc");
	        
	    	sessionToken=AgentSessionToken("7771216290", "paytm@123");

	        HashMap<String, String> headers2 = new HashMap<>();
	        headers2.put("content-type", "application/json");
	        headers2.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers2.put("session_token",sessionToken);
	        headers2.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers2.put("version", "7.1.9");

	        services = new MiddlewareServices();
	        obj = new DeviceUpgradevalidateEDCQr();
	        response=services.validateEDCQr(obj, updateBody, headers2);
	        Assert.assertEquals(response.getStatusCode(), 400);	        
	        
	}

	@Test
	public void getStatusInCaseOfTokenIsNotPassedInRequest() {
		   EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

	        Map<String, String> queryParam = new HashMap<String, String>();
	        queryParam.put("upgrade", "device");

	        HashMap<String, String> headers = new HashMap<>();
	        headers.put("content-type", "application/json");
	        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers.put("session_token",sessionToken);
	        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers.put("version", "7.1.9");

	        Map<String, String> body = new HashMap<String, String>();
	        body.put("entityType", "PROPRIETORSHIP");
	        body.put("userCustId","1002195189");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","5509877651");
	        body.put("businessLeadId","");
	        body.put("mid","KOnaKH62779279496920");
	        body.put("kybId","A01ay2ssfbqpc555");
	        body.put("wfVersion","V2");
	        body.put("serviceReason","Device physical damage");

	        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

	        Assert.assertEquals(respObj.statusCode(), 200);
	        String leadId = respObj.jsonPath().getJsonObject("leadId");

	     

	        updateBody = new HashMap<>();
	        updateBody.put("leadId", leadId);
	        updateBody.put("oem", "PAX");
	        updateBody.put("modelName", "A8950");
	        updateBody.put("serialNo", "SEA508993368");
	        updateBody.put("deviceType", "ANDROID");
	        updateBody.put("osType", "abc");
	        
	    	sessionToken=AgentSessionToken("7771216290", "paytm@123");

	        HashMap<String, String> headers2 = new HashMap<>();
	        headers2.put("content-type", "application/json");
	        headers2.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
//	        headers2.put("session_token",sessionToken);
	        headers2.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers2.put("version", "7.1.9");

	        services = new MiddlewareServices();
	        obj = new DeviceUpgradevalidateEDCQr();
	        response=services.validateEDCQr(obj, updateBody, headers2);
	        Assert.assertEquals(response.getStatusCode(), 401);
	        

	        
	}
	
	@Test
	public void getStatusInCaseOfInvalidTokenIsPassedInRequest() {
		   EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

	        Map<String, String> queryParam = new HashMap<String, String>();
	        queryParam.put("upgrade", "device");

	        HashMap<String, String> headers = new HashMap<>();
	        headers.put("content-type", "application/json");
	        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers.put("session_token",sessionToken);
	        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers.put("version", "7.1.9");

	        Map<String, String> body = new HashMap<String, String>();
	        body.put("entityType", "PROPRIETORSHIP");
	        body.put("userCustId","1002195189");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","5509877651");
	        body.put("businessLeadId","");
	        body.put("mid","KOnaKH62779279496920");
	        body.put("kybId","A01ay2ssfbqpc555");
	        body.put("wfVersion","V2");
	        body.put("serviceReason","Device physical damage");

	        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

	        Assert.assertEquals(respObj.statusCode(), 200);
	        String leadId = respObj.jsonPath().getJsonObject("leadId");

	     

	        updateBody = new HashMap<>();
	        updateBody.put("leadId", leadId);
	        updateBody.put("oem", "PAX");
	        updateBody.put("modelName", "A8950");
	        updateBody.put("serialNo", "SEA508993368");
	        updateBody.put("deviceType", "ANDROID");
	        updateBody.put("osType", "abc");
	        
	    	sessionToken="invalidtoken";

	        HashMap<String, String> headers2 = new HashMap<>();
	        headers2.put("content-type", "application/json");
	        headers2.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers2.put("session_token",sessionToken);
	        headers2.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers2.put("version", "7.1.9");

	        services = new MiddlewareServices();
	        obj = new DeviceUpgradevalidateEDCQr();
	        response=services.validateEDCQr(obj, updateBody, headers2);
	        Assert.assertEquals(response.getStatusCode(), 410);
	        

	}



	@Test
	public void getStatusInCaseOfVersionIsNotPassedInReq() {
		   EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

	        Map<String, String> queryParam = new HashMap<String, String>();
	        queryParam.put("upgrade", "device");

	        HashMap<String, String> headers = new HashMap<>();
	        headers.put("content-type", "application/json");
	        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers.put("session_token",sessionToken);
	        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers.put("version", "7.1.0");

	        Map<String, String> body = new HashMap<String, String>();
	        body.put("entityType", "PROPRIETORSHIP");
	        body.put("userCustId","1002195189");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","5509877651");
	        body.put("businessLeadId","");
	        body.put("mid","KOnaKH62779279496920");
	        body.put("kybId","A01ay2ssfbqpc555");
	        body.put("wfVersion","V2");
	        body.put("serviceReason","Device physical damage");

	        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

	        Assert.assertEquals(respObj.statusCode(), 200);
	        String leadId = respObj.jsonPath().getJsonObject("leadId");

	     

	        updateBody = new HashMap<>();
	        updateBody.put("leadId", leadId);
	        updateBody.put("oem", "PAX");
	        updateBody.put("modelName", "A8950");
	        updateBody.put("serialNo", "SEA508993368");
	        updateBody.put("deviceType", "ANDROID");
	        updateBody.put("osType", "abc");
	        
	    	sessionToken=AgentSessionToken("7771216290", "paytm@123");

	        HashMap<String, String> headers2 = new HashMap<>();
	        headers2.put("content-type", "application/json");
	        headers2.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers2.put("session_token",sessionToken);
	        headers2.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
//	        headers2.put("version", "7.1.9");

	        services = new MiddlewareServices();
	        obj = new DeviceUpgradevalidateEDCQr();
	        response=services.validateEDCQr(obj, updateBody, headers2);
	        Assert.assertEquals(response.path("message"), "version is empty in header");
	        

	        
	}

	
	
	@Test
	public void getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader() {
		   EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

	        Map<String, String> queryParam = new HashMap<String, String>();
	        queryParam.put("upgrade", "device");

	        HashMap<String, String> headers = new HashMap<>();
	        headers.put("content-type", "application/json");
	        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers.put("session_token",sessionToken);
	        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
	        headers.put("version", "7.1.9");

	        Map<String, String> body = new HashMap<String, String>();
	        body.put("entityType", "PROPRIETORSHIP");
	        body.put("userCustId","1002195189");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","5509877651");
	        body.put("businessLeadId","");
	        body.put("mid","KOnaKH62779279496920");
	        body.put("kybId","A01ay2ssfbqpc555");
	        body.put("wfVersion","V2");
	        body.put("serviceReason","Device physical damage");

	        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

	        Assert.assertEquals(respObj.statusCode(), 200);
	        String leadId = respObj.jsonPath().getJsonObject("leadId");

	     

	        updateBody = new HashMap<>();
	        updateBody.put("leadId", leadId);
	        updateBody.put("oem", "PAX");
	        updateBody.put("modelName", "A50");
	        updateBody.put("serialNo", "SEA508993368");
	        updateBody.put("deviceType", "ANDROID");
	        updateBody.put("osType", "ANDROID");
	        
	    	sessionToken=AgentSessionToken("7771216290", "paytm@123");

	        HashMap<String, String> headers2 = new HashMap<>();
	        headers2.put("content-type", "application/json");
	        headers2.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
	        headers2.put("session_token",sessionToken);

	        headers2.put("version", "7.1.9");

	        services = new MiddlewareServices();
	        obj = new DeviceUpgradevalidateEDCQr();
	        response=services.validateEDCQr(obj, updateBody, headers2);
	        Assert.assertEquals(response.getStatusCode(), 412);
	        

	        
	}







}
