package OCL.EDCDeviceUpgradeV2;


import Request.EDCDeviceUpgradeV2.EDCDeviceUpgradeV2;
import Services.DBConnection.DBConnection;
import Services.EDCDeviceUpgradeEDC.EDCDeviceUpgradeServices;
import com.goldengate.common.BaseMethod;
import com.goldengate.common.RetryAnalyzer;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class V2EDCDeviceUpgrade extends BaseMethod {

    EDCDeviceUpgradeServices EDCDeviceUpgradeServicesobject =new EDCDeviceUpgradeServices();

    private static final Logger LOGGER = LogManager.getLogger(V2EDCDeviceUpgrade.class);

    String AgentToken ="";

    String mobileNo= "7722127717";
    String solution_type="edc_device_upgrade";

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLogin() throws Exception {
        AgentToken = AgentSessionToken("7771216290", "paytm@123");
        LOGGER.info("Agent Token  : " + AgentToken);
        waitForLoad(3000);
     /* TestBase testBase =new TestBase();
      DbName = DbStagingSprint;
       testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '"+mobileNo+"' and status = '2' and solution_type='device_accessory_deployment';");
       int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes); */

        DBConnection.UpdateQueryToCloseLead(mobileNo,solution_type);

    }

    @Test(priority = 0, description = "Create a new lead with all valid details", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "device");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1704740696");
        body.put("userMobile","7722127717");
        body.put("businessLeadId","");
        body.put("mid","BpqoRH01002607972307");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V2");
        body.put("solutionType","edc_device_upgrade");
        body.put("requestType","UPGRADE_UNMAP");
        body.put("serviceReason","Device physical damage");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info(" Create a new lead with all valid details" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        String leadId = respObj.jsonPath().getJsonObject("leadId");
        LOGGER.info(" LeadId : " +leadId);
    }


    @Test(priority = 0, description = "Create a new lead without token", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "device");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token","");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V2");
        body.put("serviceReason","Device physical damage");

        Response respObj = null;
        try{
            respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {

            Assert.assertEquals(respObj.statusCode(), 401);
            LOGGER.info("Create a new lead without token" + respObj.statusCode());
        }
    }

    @Test(priority = 0, description = "Create a new lead with invalid token", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "device");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token","");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","assdd");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V2");
        body.put("serviceReason","Device physical damage");

        Response respObj = null;
        try{
            respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {

            Assert.assertEquals(respObj.statusCode(), 401);
            LOGGER.info("Create a new lead with invalid token" + respObj.statusCode());
        }
    }

    @Test(priority = 0, description = "Create a new lead without param", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V2");
        body.put("serviceReason","Device physical damage");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead without param " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        String leadId = respObj.jsonPath().getJsonObject("leadId");
        LOGGER.info(" LeadId : " +leadId);
    }

    @Test(priority = 0, description = "Create a new lead with inavlid param", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V2");
        body.put("serviceReason","Device physical damage");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead with inavlid param " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        String leadId = respObj.jsonPath().getJsonObject("leadId");
        LOGGER.info(" LeadId : " +leadId);
    }


    @Test(priority = 0, description = "Create a new lead without entity type", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V2");
        body.put("serviceReason","Device physical damage");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead without entity type " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
       // String leadId = respObj.jsonPath().getJsonObject("leadId");
       // LOGGER.info(" LeadId : " +leadId);
    }


    @Test(priority = 0, description = "Create a new lead with invalid entity type", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "deee");
        body.put("userCustId","1700547833");
        body.put("agentCustId","1107195733");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V2");
        body.put("serviceReason","Device physical damage");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead with invalid entity type " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
        // String leadId = respObj.jsonPath().getJsonObject("leadId");
        // LOGGER.info(" LeadId : " +leadId);

    }

    @Test(priority = 0, description = "Create a new lead without user custid", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","");
        body.put("agentCustId","1107195733");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V2");
        body.put("serviceReason","Device physical damage");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead without user custid " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        String leadId = respObj.jsonPath().getJsonObject("leadId");
         LOGGER.info(" LeadId : " +leadId);

    }

    @Test(priority = 0, description = "Create a new lead with invalid custid", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","PROPRIETORSHIP");
        body.put("agentCustId","1107195733");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V2");
        body.put("serviceReason","Device physical damage");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead with invalid custid " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 500);
       // String leadId = respObj.jsonPath().getJsonObject("leadId");
       // LOGGER.info(" LeadId : " +leadId);

    }


    @Test(priority = 0, description = "Create a new lead without agent custid", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700946008");
        body.put("agentCustId","");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V2");
        body.put("serviceReason","Device physical damage");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead without agent custid " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
         String leadId = respObj.jsonPath().getJsonObject("leadId");
        LOGGER.info(" LeadId : " +leadId);

    }


    @Test(priority = 0, description = "Create a new lead with invalid agent custid", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700946008");
        body.put("agentCustId","ddd");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V2");
        body.put("serviceReason","Device physical damage");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead with invalid agent custid " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        String leadId = respObj.jsonPath().getJsonObject("leadId");
         LOGGER.info(" LeadId : " +leadId);

    }


    @Test(priority = 0, description = "Create a new lead without mobile number", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700946008");
        body.put("agentCustId","22223");
        body.put("userMobile","");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V2");
        body.put("serviceReason","Device physical damage");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead without mobile number " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        String leadId = respObj.jsonPath().getJsonObject("leadId");
        LOGGER.info(" LeadId : " +leadId);

    }


    @Test(priority = 0, description = "Create a new lead with invalid mobile number", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700946008");
        body.put("agentCustId","22223");
        body.put("userMobile","aaaa");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V2");
        body.put("serviceReason","Device physical damage");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead with invalid mobile number " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        String leadId = respObj.jsonPath().getJsonObject("leadId");
        LOGGER.info(" LeadId : " +leadId);

    }



    @Test(priority = 0, description = "Create a new lead with invalid MID", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700946008");
        body.put("agentCustId","22223");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp4556298936905");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V2");
        body.put("serviceReason","Device physical damage");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead with invalid MID " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 500);
        //String leadId = respObj.jsonPath().getJsonObject("leadId");
        //  LOGGER.info(" LeadId : " +leadId);

    }


    @Test(priority = 0, description = "Create a new lead without KYBID", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700946008");
        body.put("agentCustId","22223");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","");
        body.put("wfVersion","V2");
        body.put("serviceReason","Device physical damage");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead without KYBID " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
        //String leadId = respObj.jsonPath().getJsonObject("leadId");
        //  LOGGER.info(" LeadId : " +leadId);

    }


    @Test(priority = 0, description = "Create a new lead with INVALID KYBID", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700946008");
        body.put("agentCustId","22223");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h28");
        body.put("wfVersion","V2");
        body.put("serviceReason","Device physical damage");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead with INVALID KYBID" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 500);
        //String leadId = respObj.jsonPath().getJsonObject("leadId");
        //  LOGGER.info(" LeadId : " +leadId);

    }


    @Test(priority = 0, description = "Create a new lead withOUT WORKFLOW VERSION", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700946008");
        body.put("agentCustId","22223");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","");
        body.put("serviceReason","Device physical damage");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead withOUT WORKFLOW VERSION" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        String leadId = respObj.jsonPath().getJsonObject("leadId");
        LOGGER.info(" LeadId : " +leadId);

    }

    @Test(priority = 0, description = "Create a new lead with INVALID WORKFLOW VERSION", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_018() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700946008");
        body.put("agentCustId","22223");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V5");
        body.put("serviceReason","Device physical damage");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead with INVALID WORKFLOW VERSION" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        String leadId = respObj.jsonPath().getJsonObject("leadId");
        LOGGER.info(" LeadId : " +leadId);

    }

    @Test(priority = 0, description = "Create a new lead without service reason", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_019() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700946008");
        body.put("agentCustId","22223");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V5");
        body.put("serviceReason","");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead without service reason" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        String leadId = respObj.jsonPath().getJsonObject("leadId");
        LOGGER.info(" LeadId : " +leadId);

    }



    @Test(priority = 0, description = "Create a new lead without version", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_020() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700946008");
        body.put("agentCustId","22223");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V5");
        body.put("serviceReason","SHOP CLOSED");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead without version" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        String leadId = respObj.jsonPath().getJsonObject("leadId");
        LOGGER.info(" LeadId : " +leadId);

    }


    @Test(priority = 0, description = "Create a new lead without device identifier", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_021() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700946008");
        body.put("agentCustId","22223");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V5");
        body.put("serviceReason","SHOP CLOSED");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead without version" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 410);
        //String leadId = respObj.jsonPath().getJsonObject("leadId");
       // LOGGER.info(" LeadId : " +leadId);

    }


    @Test(priority = 0, description = "Create a new lead without checksum", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_022() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        EDCDeviceUpgradeV2 eDCDeviceUpgradeV2 = new EDCDeviceUpgradeV2(P.TESTDATA.get("EDCDeviceUpgradeV2"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("upgrade", "2344");

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("deviceidentifier", "");
        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "OnePlus-CPH2487-b9b7925156682c40");
        headers.put("version", "7.1.9");

        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId","1700946008");
        body.put("agentCustId","22223");
        body.put("userMobile","8888333331");
        body.put("businessLeadId","");
        body.put("mid","pGeGtp45562989369059");
        body.put("kybId","A08fy2yxc9h258");
        body.put("wfVersion","V5");
        body.put("serviceReason","SHOP CLOSED");

        Response respObj = EDCDeviceUpgradeServicesobject.EDCDeviceUpgradenMiddleware(eDCDeviceUpgradeV2,queryParam,body, headers);

        LOGGER.info("Create a new lead without checksum" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 412);
        //String leadId = respObj.jsonPath().getJsonObject("leadId");
        // LOGGER.info(" LeadId : " +leadId);

    }






}
