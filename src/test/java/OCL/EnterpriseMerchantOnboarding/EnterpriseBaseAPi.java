package OCL.EnterpriseMerchantOnboarding;

import Request.EnterpriseMerchantOnboard.CreateEnterpriseMerchantOnPG;
import Request.EnterpriseMerchantOnboard.GetMIDUsingMobileNumber;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.ssh.SSHConnection;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;

import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

public class EnterpriseBaseAPi extends BaseMethod {
	
	
	 private static final Logger LOGGER = LogManager.getLogger(EnterpriseBaseAPi.class);

	    oAuthServices oAuthServicesObject = new oAuthServices();
	    static Connection connection = null;
	    static SSHConnection sshConnection = new SSHConnection();
	    public static String DbName = "sprint40_2";
	    
	    
	    
	    /*
	     * Method to create User
	     * @param headers
	     * @param body
	     * @return
	     */
	    
	    
	    
	  //Generate Unique 10 Digit Mobile Number
	    public String GenerateMobileNumber()
	    {
	    	   String str=GenerateRandomDigit9();
	    	   System.out.println(str);
	    	   String MobileNumber= "5"+str;
	    	   System.out.println(MobileNumber);
	    	   return MobileNumber;

	    }	

	       //Generate Random 9 Digit Number
	    static String GenerateRandomDigit9() { 
	        String numbers = "**********"; 
	        // create a super set of all characters 
	        String allCharacters = numbers; 
	        // initialize a string to hold result 
	        StringBuffer randomString = new StringBuffer(); 
	        // loop for 10 times 
	        for (int i = 0; i < 9; i++) { 
	          // generate a random number between 0 and 9
	          int randomIndex = (int)(Math.random() * allCharacters.length()); 
	          // retrieve character at index and add it to result 
	          randomString.append(allCharacters.charAt(randomIndex)); 
	        } 
	        return randomString.toString(); 
	      }

	    //Generate Random 10 Digit RequestID for Create Merchant
	    static String GenerateRandomDigit10() { 
	       String numbers = "**********"; 
	       // create a super set of all characters 
	       String allCharacters = numbers; 
	       // initialize a string to hold result 
	       StringBuffer randomString = new StringBuffer(); 
	       // loop for 10 times 
	       for (int i = 0; i < 10; i++) { 
	         // generate a random number between 0 and 9
	         int randomIndex = (int)(Math.random() * allCharacters.length()); 
	         // retrieve character at index and add it to result 
	         randomString.append(allCharacters.charAt(randomIndex)); 
	       } 
	       return randomString.toString(); 
	     } 

	    //Generate Random 13 Digit Account No. for Create Merchant
	    static String GenerateAccountNo() { 
	     String numbers = "**********"; 
	     // create a super set of all characters 
	     String allCharacters = numbers; 
	     // initialize a string to hold result 
	     StringBuffer randomString = new StringBuffer(); 
	     // loop for 10 times 
	     for (int i = 0; i < 13; i++) { 
	       // generate a random number between 0 and 9
	       int randomIndex = (int)(Math.random() * allCharacters.length()); 
	       // retrieve character at index and add it to result 
	       randomString.append(allCharacters.charAt(randomIndex)); 
	     } 
	     return randomString.toString(); 
	    } 

	      //Generate Random 4 Digit number for Pan Card
	    static String GenerateRandomDigit4() { 
	        String numbers = "**********"; 
	        // create a super set of all characters 
	        String allCharacters = numbers; 
	        // initialize a string to hold result 
	        StringBuffer randomString = new StringBuffer(); 
	        // loop for 10 times 
	        for (int i = 0; i < 4; i++) { 
	          // generate a random number between 0 and 9  
	          int randomIndex = (int)(Math.random() * allCharacters.length()); 
	          // retrieve character at index and add it to result 
	          randomString.append(allCharacters.charAt(randomIndex)); 
	        } 
	        return randomString.toString(); 
	      } 
	    //Generate Unique 4 Digit Random Number
	    public String GenerateRandomNumber()
	    {
	    	   String str = GenerateRandomDigit4();
	    	   String RandomNumber4= str;
	    	   System.out.println(RandomNumber4);
	    	   return RandomNumber4;

	    }	

	        //Generate 3 Character Random String
	    static String usingMath() { 
	        String alphabetsInUpperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"; 
	        
	       
	        // create a super set of all characters 
	        String allCharacters = alphabetsInUpperCase; 
	        // initialize a string to hold result 
	        StringBuffer randomString = new StringBuffer(); 
	        // loop for 3 times 
	        for (int i = 0; i < 3; i++) { 
	          // generate a random number of all characters 
	          int randomIndex = (int)(Math.random() * allCharacters.length()); 
	          // retrieve character at index and add it to result 
	          randomString.append(allCharacters.charAt(randomIndex)); 
	        } 
	        return randomString.toString(); 
	      } 

	    //Generate Unique Pan Number
	    public String GeneratePanNumber() {
	    	
	    	String First3Letter= usingMath();
	    	String RandomNumberforPan=GenerateRandomNumber();
	    	String UniquePanNumber= First3Letter+"PW"+RandomNumberforPan+"H";
	    	return UniquePanNumber;
	    	
	    	
	    	
	    }

	    //Generate Current Date Time Stamp
	    public String GenerateCurrentDateTime() {
	    	String timeStamp = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(new Date());
	        return timeStamp;
	    }

	    
	   
	    
	    public Response MerchantCreationonPG(Map<String, String> headers, Map<String, String> body,String RequestPath) {

	    	CreateEnterpriseMerchantOnPG MerchantCreationonPGObject = new CreateEnterpriseMerchantOnPG(RequestPath);

	        for (Map.Entry m : headers.entrySet()) {
	        	MerchantCreationonPGObject.setHeader(m.getKey().toString(), m.getValue().toString());
	        }

	        for (Map.Entry m : body.entrySet()) {
	        	MerchantCreationonPGObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
	        }

	        Response MerchantCreationonPGObjectResponse = MerchantCreationonPGObject.callAPI();

	        return MerchantCreationonPGObjectResponse;
	    }
	    
	   
	    
	    public Response CheckCreatedMerchant(Map<String, String> headers,String RequestPath, String mobile) {

	    	GetMIDUsingMobileNumber CheckCreatedMerchantObject = new GetMIDUsingMobileNumber(RequestPath,mobile);

	        for (Map.Entry m : headers.entrySet()) {
	        	CheckCreatedMerchantObject.setHeader(m.getKey().toString(), m.getValue().toString());
	        }
	        
	       


	        Response CheckCreatedMerchantObjectResponse = CheckCreatedMerchantObject.callAPI();

	        return CheckCreatedMerchantObjectResponse;
	    }
	    
	    
	    
	    /**
	     * Verify  Response Code as 200 OK
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs200OK(Response responseObject) {

	        LOGGER.info("Status Code : " +responseObject.getStatusCode());

	        Assert.assertEquals(responseObject.getStatusCode(),200);

	    }
	    
	    /**
	     * Verify  Response Code as 400 Bad Request
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs400BadRequest(Response responseObject) {

	        LOGGER.info("Status Code : " +responseObject.getStatusCode());

	        Assert.assertEquals(responseObject.getStatusCode(),400);

	    }
	    /**
	     * Verify  Response Code as 403 Bad Request
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs403BadRequest(Response responseObject) {

	        LOGGER.info("Status Code : " +responseObject.getStatusCode());

	        Assert.assertEquals(responseObject.getStatusCode(),403);

	    }
	    
	    
	    /**
	     * Verify  Response Code as 401 Unauthorized
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs401Unauthorized(Response responseObject) {

	    	
	        LOGGER.info("Status Code : " +responseObject.getStatusCode());
	        Assert.assertEquals(responseObject.getStatusCode(),401);

	    }
	    
	    /**
	     * Verify  Response Code as 415 Unsupported Media Type
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs415UnsupportedMediaType(Response responseObject) {

	    	
	        LOGGER.info("Status Code : " +responseObject.getStatusCode());
	        Assert.assertEquals(responseObject.getStatusCode(),415);

	    }
	    

}