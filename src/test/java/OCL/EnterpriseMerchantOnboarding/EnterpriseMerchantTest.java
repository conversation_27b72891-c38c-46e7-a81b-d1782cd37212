package OCL.EnterpriseMerchantOnboarding;

import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.openqa.selenium.WebDriver;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class EnterpriseMerchantTest extends EnterpriseBaseAPi {
	
	private static final Logger LOGGER = LogManager.getLogger(EnterpriseMerchantTest.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	
	protected WebDriver driver;
	
	
	EnterpriseBaseAPi EnterpriseBaseClassObject= new EnterpriseBaseAPi();
	BaseMethod BaseMethodObject=new BaseMethod();
	

	
	String mobile=EnterpriseBaseClassObject.GenerateMobileNumber();
	String loginPassword= "paytm@123";
	String RequestPath="";
	String code="";
	String fetch_strategy="phone";
	String CUST_ID="";
	String KybJWTtoken="Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE1MTgwMDA4MTAwMDAiLCJjdXN0X2lkIjoiMTEwNzE5OTQwNyIsImNsaWVudF9pZCI6InRlc3RjbGllbnQifQ.J69gTrP1Ct6-C0rBuBUKl0Gsvo/4lDXBojYOzwhJN2o";
	String companyFlags="APPLICANT";
	String companyType="PROPRIETORSHIP";
	String name="TOUCH WOOD LIMITED";
	String entityType="Company";
	String nameOnPan="TOUCH WOOD LIMITED";
	String docCode="pan";
	String docValue=GeneratePanNumber();
	String submittedAs="Poi";
	String placeOfIssue="00";
	String docIssueDate=GenerateCurrentDateTime();
	String role="Company";
	String businessId="";
	String CREATED_BY="sobeer_sales";
	String ACTION="Submit for Approval";
	String REQUEST_ID="OE"+usingMath()+"TEST"+GenerateRandomDigit10();
	String USER_NAME=REQUEST_ID;
	String ACCOUNT_FOR="unifiedMerchantPanel";
	String SOURCE_ID="OE";
	String MERCHANT_TYPE="UNLIMITED_SD";
	String OFFLINE_ENABLED="FALSE";
	String PPI_LIMITED_MERCHANT="4";
	String KYB_ID="";
	String BUSINESS_NAME="Rohan";
	String BUSINESS_TYPE="PROPRIETORSHIP";
	String CALLBACK_URL_ENABLED="TRUE";
	String CUSTOM="SYSTEM GENERATED";
	String MERCHANT_NAME="PRATEEK SRIVASTAVA";
	String CURRENCY="INR";
	String REFUND_TO_BANK_ENABLED="TRUE";
	String STORE_CARD_DETAILS="NO";
	String ADD_MONEY_ENABLE="TRUE";
	String CHECKSUM_ENABLED="TRUE";
	String NUMBER_OF_RETRY="1";
	String CATEGORY="BFSI";
	String SUB_CATEGORY="Loans";
	String INDUSTRY_TYPE="Retail";
	String WALLET_RECHARGE_OPT="MANUAL_RECHARGE";
	String PROFILE_ID="1";
	String EMAIL_ALERT="TRUE";
	String CONVENIENCE_FEE_TYPE="1";
	String VALID_FROM="12/20/2021";
	String VALID_TO="07/25/2024";
	String MULTI_SUPPORT="YES";
	String HOW_MANY="3";
	String OCP="TRUE";
	String REQUEST_NAME="DP2Web";
	String FIRST_NAME="Rohan";
	String LAST_NAME="Saxena";
	String MOBILE_NUMBER=mobile;
	String PHONE_NUMBER=MOBILE_NUMBER;
	String MerchUniqRef="X";
	String ACCOUNT_PRIMARY="FALSE";
	String CAN_EDIT_PMOBILE="TRUE";
	String IS_SUB_USER="FALSE";
	String ADDRESS1="B-265";
	String ADDRESS2="Brij Vihar";
	String ADDRESS3="Ghaziabad";
	String COUNTRY="India";
	String STATE="Uttar Pradesh";
	String CITY="Noida";
	String PIN="201011";
	String SAME_AS_BUSINESS_ADDR="FALSE";
	String COMMUNICATION_ADDRESS1="B-265";
	String COMMUNICATION_ADDRESS2= "Brij Vihar";
	String COMMUNICATION_ADDRESS3= "Ghaziabad";
	String COMMUNICATION_COUNTRY= "India";
	String COMMUNICATION_STATE= "Uttar Pradesh";
	String COMMUNICATION_CITY= "Noida";
	String COMMUNICATION_PIN="201011";
	String COMMUNICATION_LATITUDE= "28.6740548";
	String COMMUNICATION_LONGITUDE= "77.3340577";
	String KYC_BANK_NAME= "ALLAHABAD";
	String KYC_BANK_ACCOUNT_HOLDER_NAME= "Adsd";
	String KYC_BANK_ACCOUNT_NO= GenerateAccountNo();
	String KYC_BUSINESS_PAN_NO= docValue;
	String KYC_AUTHORIZED_SIGNATORY_PAN_NO= docValue;
	String KYC_BUSINESS_IFSC_NO= "ALLA0123456";
	String KYC_AUTHORIZED_SIGNATORY_NAME= "Rohan G1627034886877";
	String KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO= "**********";
	String COMM_STAT_SELECT= "1";
	String EMAIL_MERCHANT= "TRUE";
	String EMAIL_CONSUMER= "FALSE";
	String REQUEST_TYPE_NAME= "QR_ORDER,SELF_DECLARED_MERCHANT,EDC";
	String WEBSITE_NAME= "paytm";
	String SIZE_OF_KEY="16";
	String SMS_MERCHANT="TRUE";
	String PAYOUT_DAYS="1";
	String ONLINE_SETTLEMENT= "FALSE";
	String FLAG_MERCHANT= "FALSE";
	String BW_ENABLED= "TRUE";

	String TRANSFER_MODE= "M2B";
	String AUTO = "TRUE";
	String TRIGGER_MODE = "TIME_INTERVAL";
	String TRIGGER_VALUE="1";

	String API_DISABLED= "TRUE";
	String MERCHANT_INDUSTRY_TYPE= "BIG";
	String P2M_ENABLED= "TRUE";
	String OB_CHANNEL= "GG_APP";
	String SOLUTION_TYPE= "ONLINE";

	String  WEBSITE_NAME1= "dp2wen";
	String  REQUEST_URL1= "https://secure.paytm.in/MerchantSite/bankResponse";
	String  RESPONSE_URL1= "https://secure.paytm.in/oltp-web/smsInvoiceAddMoney/displayPaymentStatus?ORDER_ID=m1";
	String  PEON_URL1= "http://dp2web.com";
	String  IMAGE_NAME1= "paytm_log";


	String  WEBSITE_NAME2="paytm";
	String  REQUEST_URL2= "https://www.paytm.com";
	String RESPONSE_URL2= "https://cart-beta.paytm.com/payment/status";
	String IMAGE_NAME2= "paytm_log";
	 
	String  WEBSITE_NAME3="retail";
	String  REQUEST_URL3= "https://www.paytm.com";
	String RESPONSE_URL3= "https://cart-beta.paytm.com/payment/status";
	String IMAGE_NAME3= "paytm_log";

	String DETAILED_LIST="CARD";
	String VELOCITY_TYPE="PER_MID";
	String MAX_AMT_PER_DAY="100";
	String MAX_AMT_PER_MONTH="100";
	String ACTION_ConfigureMerchnatComm="EDIT";
	String FEE_TYPE ="simple";
	String PERCENT_COMMISSION="1";
	String COMMISSION_TYPE_BOTH="FALSE";

	String TXN_TYPE="Payments";
	
	String PAY_MODE="NB";
	String CookieEditMerchantOnPG="JSESSIONID=314BDC407AA0A84DDB37963ABB45EC01.adminjvm1; JSESSIONID=D739C98D9B0B5EF5F82B944DF4DBFAF2.adminjvm1; SESSION=d6f5d8e3-0f63-4997-9874-f33a35dd9cac";
	String TYPE="BILLING";
	String MID="";

	@SuppressWarnings("static-access")
	String xssotoken= BaseMethodObject.ApplicantToken("9891497839", "paytm@123");

	@BeforeClass()
	public void intitializeInputData() throws IOException {

		LOGGER.info(" Before Suite Method for User Creation ");
		//commonHeaders = setcommonHeaders();

	}
	
	@Test(description = "Create Enterprise Merchant on PG without Source ID", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC01_CreateMerchantonPGWithoutSourceID() throws InterruptedException {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cache-control", "no-cache");
			headers.put("x-jwt-token",KybJWTtoken);
			
			RequestPath="BrandEMI/MerchantCreationRequest.json";
			String SOURCE_ID="";
		
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("CREATED_BY", CREATED_BY);
			body.put("ACTION", ACTION);
			body.put("REQUEST_ID", REQUEST_ID);
			body.put("USER_NAME", USER_NAME);
			body.put("ACCOUNT_FOR", ACCOUNT_FOR);
			body.put("SOURCE_ID", SOURCE_ID);
			body.put("MERCHANT_TYPE", MERCHANT_TYPE);
			body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
			body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
			body.put("KYB_ID", KYB_ID);
			body.put("BUSINESS_NAME", BUSINESS_NAME);
			body.put("BUSINESS_TYPE", BUSINESS_TYPE);
			body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
			body.put("CUSTOM", CUSTOM);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("CURRENCY", CURRENCY);
			body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
			body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
			body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
			body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
			body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
			body.put("CATEGORY", CATEGORY);
			body.put("SUB_CATEGORY", SUB_CATEGORY);
			body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
			body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
			body.put("PROFILE_ID", PROFILE_ID);
			body.put("EMAIL_ALERT", EMAIL_ALERT);
			body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
			body.put("VALID_FROM", VALID_FROM);
			body.put("VALID_TO", VALID_TO);
			body.put("MULTI_SUPPORT", MULTI_SUPPORT);
			body.put("HOW_MANY", HOW_MANY);
			body.put("OCP", OCP);
			body.put("REQUEST_NAME", REQUEST_NAME);
			body.put("FIRST_NAME", FIRST_NAME);
			body.put("LAST_NAME", LAST_NAME);
			body.put("MOBILE_NUMBER", MOBILE_NUMBER);
			body.put("PHONE_NUMBER", PHONE_NUMBER);
			body.put("MerchUniqRef", MerchUniqRef);
			body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
			body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
			body.put("IS_SUB_USER", IS_SUB_USER);
			body.put("ADDRESS1", ADDRESS1);
			body.put("ADDRESS2", ADDRESS2);
			body.put("ADDRESS3", ADDRESS3);
			body.put("COUNTRY", COUNTRY);
			body.put("STATE", STATE);
			body.put("CITY", CITY);
			body.put("PIN", PIN);
			body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
			body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
			body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
			body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
			body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
			body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
			body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
			body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
			body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
			body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
			body.put("KYC_BANK_NAME", KYC_BANK_NAME);
			body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
			body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
			body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
			body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
			body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
			body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
			body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
			body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
			body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
			body.put("WEBSITE_NAME", WEBSITE_NAME);
			body.put("SIZE_OF_KEY", SIZE_OF_KEY);
			body.put("SMS_MERCHANT", SMS_MERCHANT);
			body.put("PAYOUT_DAYS", PAYOUT_DAYS);
			body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
			body.put("FLAG_MERCHANT", FLAG_MERCHANT);
			body.put("BW_ENABLED", BW_ENABLED);
			body.put("TRANSFER_MODE", TRANSFER_MODE);
			body.put("AUTO", AUTO);
			body.put("TRIGGER_MODE", TRIGGER_MODE);
			body.put("TRIGGER_VALUE", TRIGGER_VALUE);
			body.put("API_DISABLED", API_DISABLED);
			body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
			body.put("P2M_ENABLED", P2M_ENABLED);
			body.put("OB_CHANNEL", OB_CHANNEL);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			body.put("WEBSITE_NAME1", WEBSITE_NAME1);
			body.put("WEBSITE_NAME2", WEBSITE_NAME2);
			body.put("WEBSITE_NAME3", WEBSITE_NAME3);
			body.put("VELOCITY_TYPE", VELOCITY_TYPE);
			body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
			body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
			body.put("FEE_TYPE", FEE_TYPE);
			body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
			body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
			body.put("TXN_TYPE", TXN_TYPE);
			body.put("PAY_MODE", PAY_MODE);
			body.put("CUST_ID", CUST_ID);
			
			
			
			
		
			Response responseObject = EnterpriseBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
				
		
		} 
	
	@Test(description = "Create Enterprise Merchant on PG with incorrect Source ID", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC02_CreateMerchantonPGWithIncorrectSourceID() throws InterruptedException {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cache-control", "no-cache");
			headers.put("x-jwt-token",KybJWTtoken);
			
			RequestPath="BrandEMI/MerchantCreationRequest.json";
			String SOURCE_ID="TEST";
		
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("CREATED_BY", CREATED_BY);
			body.put("ACTION", ACTION);
			body.put("REQUEST_ID", REQUEST_ID);
			body.put("USER_NAME", USER_NAME);
			body.put("ACCOUNT_FOR", ACCOUNT_FOR);
			body.put("SOURCE_ID", SOURCE_ID);
			body.put("MERCHANT_TYPE", MERCHANT_TYPE);
			body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
			body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
			body.put("KYB_ID", KYB_ID);
			body.put("BUSINESS_NAME", BUSINESS_NAME);
			body.put("BUSINESS_TYPE", BUSINESS_TYPE);
			body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
			body.put("CUSTOM", CUSTOM);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("CURRENCY", CURRENCY);
			body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
			body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
			body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
			body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
			body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
			body.put("CATEGORY", CATEGORY);
			body.put("SUB_CATEGORY", SUB_CATEGORY);
			body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
			body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
			body.put("PROFILE_ID", PROFILE_ID);
			body.put("EMAIL_ALERT", EMAIL_ALERT);
			body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
			body.put("VALID_FROM", VALID_FROM);
			body.put("VALID_TO", VALID_TO);
			body.put("MULTI_SUPPORT", MULTI_SUPPORT);
			body.put("HOW_MANY", HOW_MANY);
			body.put("OCP", OCP);
			body.put("REQUEST_NAME", REQUEST_NAME);
			body.put("FIRST_NAME", FIRST_NAME);
			body.put("LAST_NAME", LAST_NAME);
			body.put("MOBILE_NUMBER", MOBILE_NUMBER);
			body.put("PHONE_NUMBER", PHONE_NUMBER);
			body.put("MerchUniqRef", MerchUniqRef);
			body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
			body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
			body.put("IS_SUB_USER", IS_SUB_USER);
			body.put("ADDRESS1", ADDRESS1);
			body.put("ADDRESS2", ADDRESS2);
			body.put("ADDRESS3", ADDRESS3);
			body.put("COUNTRY", COUNTRY);
			body.put("STATE", STATE);
			body.put("CITY", CITY);
			body.put("PIN", PIN);
			body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
			body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
			body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
			body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
			body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
			body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
			body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
			body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
			body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
			body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
			body.put("KYC_BANK_NAME", KYC_BANK_NAME);
			body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
			body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
			body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
			body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
			body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
			body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
			body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
			body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
			body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
			body.put("WEBSITE_NAME", WEBSITE_NAME);
			body.put("SIZE_OF_KEY", SIZE_OF_KEY);
			body.put("SMS_MERCHANT", SMS_MERCHANT);
			body.put("PAYOUT_DAYS", PAYOUT_DAYS);
			body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
			body.put("FLAG_MERCHANT", FLAG_MERCHANT);
			body.put("BW_ENABLED", BW_ENABLED);
			body.put("TRANSFER_MODE", TRANSFER_MODE);
			body.put("AUTO", AUTO);
			body.put("TRIGGER_MODE", TRIGGER_MODE);
			body.put("TRIGGER_VALUE", TRIGGER_VALUE);
			body.put("API_DISABLED", API_DISABLED);
			body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
			body.put("P2M_ENABLED", P2M_ENABLED);
			body.put("OB_CHANNEL", OB_CHANNEL);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			body.put("WEBSITE_NAME1", WEBSITE_NAME1);
			body.put("WEBSITE_NAME2", WEBSITE_NAME2);
			body.put("WEBSITE_NAME3", WEBSITE_NAME3);
			body.put("VELOCITY_TYPE", VELOCITY_TYPE);
			body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
			body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
			body.put("FEE_TYPE", FEE_TYPE);
			body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
			body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
			body.put("TXN_TYPE", TXN_TYPE);
			body.put("PAY_MODE", PAY_MODE);
			body.put("CUST_ID", CUST_ID);
			
			
			
			
		
			Response responseObject = EnterpriseBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
				
		
		}
	
	@Test(description = "Create Enterprise Merchant on PG without Source ID and RequestID", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC03_CreateMerchantonPGWithoutSourceIDRequestID() throws InterruptedException {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cache-control", "no-cache");
			headers.put("x-jwt-token",KybJWTtoken);
			
			RequestPath="BrandEMI/MerchantCreationRequest.json";
			String SOURCE_ID="";
			String REQUEST_ID="";
		
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("CREATED_BY", CREATED_BY);
			body.put("ACTION", ACTION);
			body.put("REQUEST_ID", REQUEST_ID);
			body.put("USER_NAME", USER_NAME);
			body.put("ACCOUNT_FOR", ACCOUNT_FOR);
			body.put("SOURCE_ID", SOURCE_ID);
			body.put("MERCHANT_TYPE", MERCHANT_TYPE);
			body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
			body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
			body.put("KYB_ID", KYB_ID);
			body.put("BUSINESS_NAME", BUSINESS_NAME);
			body.put("BUSINESS_TYPE", BUSINESS_TYPE);
			body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
			body.put("CUSTOM", CUSTOM);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("CURRENCY", CURRENCY);
			body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
			body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
			body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
			body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
			body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
			body.put("CATEGORY", CATEGORY);
			body.put("SUB_CATEGORY", SUB_CATEGORY);
			body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
			body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
			body.put("PROFILE_ID", PROFILE_ID);
			body.put("EMAIL_ALERT", EMAIL_ALERT);
			body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
			body.put("VALID_FROM", VALID_FROM);
			body.put("VALID_TO", VALID_TO);
			body.put("MULTI_SUPPORT", MULTI_SUPPORT);
			body.put("HOW_MANY", HOW_MANY);
			body.put("OCP", OCP);
			body.put("REQUEST_NAME", REQUEST_NAME);
			body.put("FIRST_NAME", FIRST_NAME);
			body.put("LAST_NAME", LAST_NAME);
			body.put("MOBILE_NUMBER", MOBILE_NUMBER);
			body.put("PHONE_NUMBER", PHONE_NUMBER);
			body.put("MerchUniqRef", MerchUniqRef);
			body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
			body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
			body.put("IS_SUB_USER", IS_SUB_USER);
			body.put("ADDRESS1", ADDRESS1);
			body.put("ADDRESS2", ADDRESS2);
			body.put("ADDRESS3", ADDRESS3);
			body.put("COUNTRY", COUNTRY);
			body.put("STATE", STATE);
			body.put("CITY", CITY);
			body.put("PIN", PIN);
			body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
			body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
			body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
			body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
			body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
			body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
			body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
			body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
			body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
			body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
			body.put("KYC_BANK_NAME", KYC_BANK_NAME);
			body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
			body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
			body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
			body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
			body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
			body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
			body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
			body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
			body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
			body.put("WEBSITE_NAME", WEBSITE_NAME);
			body.put("SIZE_OF_KEY", SIZE_OF_KEY);
			body.put("SMS_MERCHANT", SMS_MERCHANT);
			body.put("PAYOUT_DAYS", PAYOUT_DAYS);
			body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
			body.put("FLAG_MERCHANT", FLAG_MERCHANT);
			body.put("BW_ENABLED", BW_ENABLED);
			body.put("TRANSFER_MODE", TRANSFER_MODE);
			body.put("AUTO", AUTO);
			body.put("TRIGGER_MODE", TRIGGER_MODE);
			body.put("TRIGGER_VALUE", TRIGGER_VALUE);
			body.put("API_DISABLED", API_DISABLED);
			body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
			body.put("P2M_ENABLED", P2M_ENABLED);
			body.put("OB_CHANNEL", OB_CHANNEL);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			body.put("WEBSITE_NAME1", WEBSITE_NAME1);
			body.put("WEBSITE_NAME2", WEBSITE_NAME2);
			body.put("WEBSITE_NAME3", WEBSITE_NAME3);
			body.put("VELOCITY_TYPE", VELOCITY_TYPE);
			body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
			body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
			body.put("FEE_TYPE", FEE_TYPE);
			body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
			body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
			body.put("TXN_TYPE", TXN_TYPE);
			body.put("PAY_MODE", PAY_MODE);
			body.put("CUST_ID", CUST_ID);
			
			
			
			
		
			Response responseObject = EnterpriseBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
				
		
		} 
	
	@Test(description = "Create Enterprise Merchant on PG without Source ID,RequestID and Merchant Type", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC04_CreateMerchantonPGWithoutMerchantType() throws InterruptedException {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cache-control", "no-cache");
			headers.put("x-jwt-token",KybJWTtoken);
			
			RequestPath="BrandEMI/MerchantCreationRequest.json";
			String SOURCE_ID="";
			String REQUEST_ID="";
			String MERCHANT_TYPE="";
		
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("CREATED_BY", CREATED_BY);
			body.put("ACTION", ACTION);
			body.put("REQUEST_ID", REQUEST_ID);
			body.put("USER_NAME", USER_NAME);
			body.put("ACCOUNT_FOR", ACCOUNT_FOR);
			body.put("SOURCE_ID", SOURCE_ID);
			body.put("MERCHANT_TYPE", MERCHANT_TYPE);
			body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
			body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
			body.put("KYB_ID", KYB_ID);
			body.put("BUSINESS_NAME", BUSINESS_NAME);
			body.put("BUSINESS_TYPE", BUSINESS_TYPE);
			body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
			body.put("CUSTOM", CUSTOM);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("CURRENCY", CURRENCY);
			body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
			body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
			body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
			body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
			body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
			body.put("CATEGORY", CATEGORY);
			body.put("SUB_CATEGORY", SUB_CATEGORY);
			body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
			body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
			body.put("PROFILE_ID", PROFILE_ID);
			body.put("EMAIL_ALERT", EMAIL_ALERT);
			body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
			body.put("VALID_FROM", VALID_FROM);
			body.put("VALID_TO", VALID_TO);
			body.put("MULTI_SUPPORT", MULTI_SUPPORT);
			body.put("HOW_MANY", HOW_MANY);
			body.put("OCP", OCP);
			body.put("REQUEST_NAME", REQUEST_NAME);
			body.put("FIRST_NAME", FIRST_NAME);
			body.put("LAST_NAME", LAST_NAME);
			body.put("MOBILE_NUMBER", MOBILE_NUMBER);
			body.put("PHONE_NUMBER", PHONE_NUMBER);
			body.put("MerchUniqRef", MerchUniqRef);
			body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
			body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
			body.put("IS_SUB_USER", IS_SUB_USER);
			body.put("ADDRESS1", ADDRESS1);
			body.put("ADDRESS2", ADDRESS2);
			body.put("ADDRESS3", ADDRESS3);
			body.put("COUNTRY", COUNTRY);
			body.put("STATE", STATE);
			body.put("CITY", CITY);
			body.put("PIN", PIN);
			body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
			body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
			body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
			body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
			body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
			body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
			body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
			body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
			body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
			body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
			body.put("KYC_BANK_NAME", KYC_BANK_NAME);
			body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
			body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
			body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
			body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
			body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
			body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
			body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
			body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
			body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
			body.put("WEBSITE_NAME", WEBSITE_NAME);
			body.put("SIZE_OF_KEY", SIZE_OF_KEY);
			body.put("SMS_MERCHANT", SMS_MERCHANT);
			body.put("PAYOUT_DAYS", PAYOUT_DAYS);
			body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
			body.put("FLAG_MERCHANT", FLAG_MERCHANT);
			body.put("BW_ENABLED", BW_ENABLED);
			body.put("TRANSFER_MODE", TRANSFER_MODE);
			body.put("AUTO", AUTO);
			body.put("TRIGGER_MODE", TRIGGER_MODE);
			body.put("TRIGGER_VALUE", TRIGGER_VALUE);
			body.put("API_DISABLED", API_DISABLED);
			body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
			body.put("P2M_ENABLED", P2M_ENABLED);
			body.put("OB_CHANNEL", OB_CHANNEL);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			body.put("WEBSITE_NAME1", WEBSITE_NAME1);
			body.put("WEBSITE_NAME2", WEBSITE_NAME2);
			body.put("WEBSITE_NAME3", WEBSITE_NAME3);
			body.put("VELOCITY_TYPE", VELOCITY_TYPE);
			body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
			body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
			body.put("FEE_TYPE", FEE_TYPE);
			body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
			body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
			body.put("TXN_TYPE", TXN_TYPE);
			body.put("PAY_MODE", PAY_MODE);
			body.put("CUST_ID", CUST_ID);
			
			
			
			
		
			Response responseObject = EnterpriseBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
				
		
		} 
	
	@Test(description = "Create Enterprise Merchant on PG without Source ID,RequestID and PPI", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC05_CreateMerchantonPGWithoutPPI() throws InterruptedException {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cache-control", "no-cache");
			headers.put("x-jwt-token",KybJWTtoken);
			
			RequestPath="BrandEMI/MerchantCreationRequest.json";
			String SOURCE_ID="";
			String REQUEST_ID="";
			String PPI_LIMITED_MERCHANT="";
		
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("CREATED_BY", CREATED_BY);
			body.put("ACTION", ACTION);
			body.put("REQUEST_ID", REQUEST_ID);
			body.put("USER_NAME", USER_NAME);
			body.put("ACCOUNT_FOR", ACCOUNT_FOR);
			body.put("SOURCE_ID", SOURCE_ID);
			body.put("MERCHANT_TYPE", MERCHANT_TYPE);
			body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
			body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
			body.put("KYB_ID", KYB_ID);
			body.put("BUSINESS_NAME", BUSINESS_NAME);
			body.put("BUSINESS_TYPE", BUSINESS_TYPE);
			body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
			body.put("CUSTOM", CUSTOM);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("CURRENCY", CURRENCY);
			body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
			body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
			body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
			body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
			body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
			body.put("CATEGORY", CATEGORY);
			body.put("SUB_CATEGORY", SUB_CATEGORY);
			body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
			body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
			body.put("PROFILE_ID", PROFILE_ID);
			body.put("EMAIL_ALERT", EMAIL_ALERT);
			body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
			body.put("VALID_FROM", VALID_FROM);
			body.put("VALID_TO", VALID_TO);
			body.put("MULTI_SUPPORT", MULTI_SUPPORT);
			body.put("HOW_MANY", HOW_MANY);
			body.put("OCP", OCP);
			body.put("REQUEST_NAME", REQUEST_NAME);
			body.put("FIRST_NAME", FIRST_NAME);
			body.put("LAST_NAME", LAST_NAME);
			body.put("MOBILE_NUMBER", MOBILE_NUMBER);
			body.put("PHONE_NUMBER", PHONE_NUMBER);
			body.put("MerchUniqRef", MerchUniqRef);
			body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
			body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
			body.put("IS_SUB_USER", IS_SUB_USER);
			body.put("ADDRESS1", ADDRESS1);
			body.put("ADDRESS2", ADDRESS2);
			body.put("ADDRESS3", ADDRESS3);
			body.put("COUNTRY", COUNTRY);
			body.put("STATE", STATE);
			body.put("CITY", CITY);
			body.put("PIN", PIN);
			body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
			body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
			body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
			body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
			body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
			body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
			body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
			body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
			body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
			body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
			body.put("KYC_BANK_NAME", KYC_BANK_NAME);
			body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
			body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
			body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
			body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
			body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
			body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
			body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
			body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
			body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
			body.put("WEBSITE_NAME", WEBSITE_NAME);
			body.put("SIZE_OF_KEY", SIZE_OF_KEY);
			body.put("SMS_MERCHANT", SMS_MERCHANT);
			body.put("PAYOUT_DAYS", PAYOUT_DAYS);
			body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
			body.put("FLAG_MERCHANT", FLAG_MERCHANT);
			body.put("BW_ENABLED", BW_ENABLED);
			body.put("TRANSFER_MODE", TRANSFER_MODE);
			body.put("AUTO", AUTO);
			body.put("TRIGGER_MODE", TRIGGER_MODE);
			body.put("TRIGGER_VALUE", TRIGGER_VALUE);
			body.put("API_DISABLED", API_DISABLED);
			body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
			body.put("P2M_ENABLED", P2M_ENABLED);
			body.put("OB_CHANNEL", OB_CHANNEL);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			body.put("WEBSITE_NAME1", WEBSITE_NAME1);
			body.put("WEBSITE_NAME2", WEBSITE_NAME2);
			body.put("WEBSITE_NAME3", WEBSITE_NAME3);
			body.put("VELOCITY_TYPE", VELOCITY_TYPE);
			body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
			body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
			body.put("FEE_TYPE", FEE_TYPE);
			body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
			body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
			body.put("TXN_TYPE", TXN_TYPE);
			body.put("PAY_MODE", PAY_MODE);
			body.put("CUST_ID", CUST_ID);
			
			
			
			
		
			Response responseObject = EnterpriseBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
				
		
		} 
	
	
	@Test(description = "Create Enterprise Merchant on PG without Source ID,RequestID and Category", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC06_CreateMerchantonPGWithoutCategory() throws InterruptedException {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cache-control", "no-cache");
			headers.put("x-jwt-token",KybJWTtoken);
			
			RequestPath="BrandEMI/MerchantCreationRequest.json";
			String SOURCE_ID="";
			String REQUEST_ID="";
			String CATEGORY="";
		
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("CREATED_BY", CREATED_BY);
			body.put("ACTION", ACTION);
			body.put("REQUEST_ID", REQUEST_ID);
			body.put("USER_NAME", USER_NAME);
			body.put("ACCOUNT_FOR", ACCOUNT_FOR);
			body.put("SOURCE_ID", SOURCE_ID);
			body.put("MERCHANT_TYPE", MERCHANT_TYPE);
			body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
			body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
			body.put("KYB_ID", KYB_ID);
			body.put("BUSINESS_NAME", BUSINESS_NAME);
			body.put("BUSINESS_TYPE", BUSINESS_TYPE);
			body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
			body.put("CUSTOM", CUSTOM);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("CURRENCY", CURRENCY);
			body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
			body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
			body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
			body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
			body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
			body.put("CATEGORY", CATEGORY);
			body.put("SUB_CATEGORY", SUB_CATEGORY);
			body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
			body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
			body.put("PROFILE_ID", PROFILE_ID);
			body.put("EMAIL_ALERT", EMAIL_ALERT);
			body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
			body.put("VALID_FROM", VALID_FROM);
			body.put("VALID_TO", VALID_TO);
			body.put("MULTI_SUPPORT", MULTI_SUPPORT);
			body.put("HOW_MANY", HOW_MANY);
			body.put("OCP", OCP);
			body.put("REQUEST_NAME", REQUEST_NAME);
			body.put("FIRST_NAME", FIRST_NAME);
			body.put("LAST_NAME", LAST_NAME);
			body.put("MOBILE_NUMBER", MOBILE_NUMBER);
			body.put("PHONE_NUMBER", PHONE_NUMBER);
			body.put("MerchUniqRef", MerchUniqRef);
			body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
			body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
			body.put("IS_SUB_USER", IS_SUB_USER);
			body.put("ADDRESS1", ADDRESS1);
			body.put("ADDRESS2", ADDRESS2);
			body.put("ADDRESS3", ADDRESS3);
			body.put("COUNTRY", COUNTRY);
			body.put("STATE", STATE);
			body.put("CITY", CITY);
			body.put("PIN", PIN);
			body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
			body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
			body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
			body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
			body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
			body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
			body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
			body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
			body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
			body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
			body.put("KYC_BANK_NAME", KYC_BANK_NAME);
			body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
			body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
			body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
			body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
			body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
			body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
			body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
			body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
			body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
			body.put("WEBSITE_NAME", WEBSITE_NAME);
			body.put("SIZE_OF_KEY", SIZE_OF_KEY);
			body.put("SMS_MERCHANT", SMS_MERCHANT);
			body.put("PAYOUT_DAYS", PAYOUT_DAYS);
			body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
			body.put("FLAG_MERCHANT", FLAG_MERCHANT);
			body.put("BW_ENABLED", BW_ENABLED);
			body.put("TRANSFER_MODE", TRANSFER_MODE);
			body.put("AUTO", AUTO);
			body.put("TRIGGER_MODE", TRIGGER_MODE);
			body.put("TRIGGER_VALUE", TRIGGER_VALUE);
			body.put("API_DISABLED", API_DISABLED);
			body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
			body.put("P2M_ENABLED", P2M_ENABLED);
			body.put("OB_CHANNEL", OB_CHANNEL);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			body.put("WEBSITE_NAME1", WEBSITE_NAME1);
			body.put("WEBSITE_NAME2", WEBSITE_NAME2);
			body.put("WEBSITE_NAME3", WEBSITE_NAME3);
			body.put("VELOCITY_TYPE", VELOCITY_TYPE);
			body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
			body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
			body.put("FEE_TYPE", FEE_TYPE);
			body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
			body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
			body.put("TXN_TYPE", TXN_TYPE);
			body.put("PAY_MODE", PAY_MODE);
			body.put("CUST_ID", CUST_ID);
			
			
			
			
		
			Response responseObject = EnterpriseBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
				
		
		} 
	
	@Test(description = "Create Enterprise Merchant on PG without Source ID,RequestID and SubCategory", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC07_CreateMerchantonPGWithoutSubCategory() throws InterruptedException {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cache-control", "no-cache");
			headers.put("x-jwt-token",KybJWTtoken);
			
			RequestPath="BrandEMI/MerchantCreationRequest.json";
			String SOURCE_ID="";
			String REQUEST_ID="";
			String SUB_CATEGORY="";
		
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("CREATED_BY", CREATED_BY);
			body.put("ACTION", ACTION);
			body.put("REQUEST_ID", REQUEST_ID);
			body.put("USER_NAME", USER_NAME);
			body.put("ACCOUNT_FOR", ACCOUNT_FOR);
			body.put("SOURCE_ID", SOURCE_ID);
			body.put("MERCHANT_TYPE", MERCHANT_TYPE);
			body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
			body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
			body.put("KYB_ID", KYB_ID);
			body.put("BUSINESS_NAME", BUSINESS_NAME);
			body.put("BUSINESS_TYPE", BUSINESS_TYPE);
			body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
			body.put("CUSTOM", CUSTOM);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("CURRENCY", CURRENCY);
			body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
			body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
			body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
			body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
			body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
			body.put("CATEGORY", CATEGORY);
			body.put("SUB_CATEGORY", SUB_CATEGORY);
			body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
			body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
			body.put("PROFILE_ID", PROFILE_ID);
			body.put("EMAIL_ALERT", EMAIL_ALERT);
			body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
			body.put("VALID_FROM", VALID_FROM);
			body.put("VALID_TO", VALID_TO);
			body.put("MULTI_SUPPORT", MULTI_SUPPORT);
			body.put("HOW_MANY", HOW_MANY);
			body.put("OCP", OCP);
			body.put("REQUEST_NAME", REQUEST_NAME);
			body.put("FIRST_NAME", FIRST_NAME);
			body.put("LAST_NAME", LAST_NAME);
			body.put("MOBILE_NUMBER", MOBILE_NUMBER);
			body.put("PHONE_NUMBER", PHONE_NUMBER);
			body.put("MerchUniqRef", MerchUniqRef);
			body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
			body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
			body.put("IS_SUB_USER", IS_SUB_USER);
			body.put("ADDRESS1", ADDRESS1);
			body.put("ADDRESS2", ADDRESS2);
			body.put("ADDRESS3", ADDRESS3);
			body.put("COUNTRY", COUNTRY);
			body.put("STATE", STATE);
			body.put("CITY", CITY);
			body.put("PIN", PIN);
			body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
			body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
			body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
			body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
			body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
			body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
			body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
			body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
			body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
			body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
			body.put("KYC_BANK_NAME", KYC_BANK_NAME);
			body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
			body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
			body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
			body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
			body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
			body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
			body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
			body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
			body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
			body.put("WEBSITE_NAME", WEBSITE_NAME);
			body.put("SIZE_OF_KEY", SIZE_OF_KEY);
			body.put("SMS_MERCHANT", SMS_MERCHANT);
			body.put("PAYOUT_DAYS", PAYOUT_DAYS);
			body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
			body.put("FLAG_MERCHANT", FLAG_MERCHANT);
			body.put("BW_ENABLED", BW_ENABLED);
			body.put("TRANSFER_MODE", TRANSFER_MODE);
			body.put("AUTO", AUTO);
			body.put("TRIGGER_MODE", TRIGGER_MODE);
			body.put("TRIGGER_VALUE", TRIGGER_VALUE);
			body.put("API_DISABLED", API_DISABLED);
			body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
			body.put("P2M_ENABLED", P2M_ENABLED);
			body.put("OB_CHANNEL", OB_CHANNEL);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			body.put("WEBSITE_NAME1", WEBSITE_NAME1);
			body.put("WEBSITE_NAME2", WEBSITE_NAME2);
			body.put("WEBSITE_NAME3", WEBSITE_NAME3);
			body.put("VELOCITY_TYPE", VELOCITY_TYPE);
			body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
			body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
			body.put("FEE_TYPE", FEE_TYPE);
			body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
			body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
			body.put("TXN_TYPE", TXN_TYPE);
			body.put("PAY_MODE", PAY_MODE);
			body.put("CUST_ID", CUST_ID);
			
			
			
			
		
			Response responseObject = EnterpriseBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
				
		
		} 
	
	@Test(description = "Create Enterprise Merchant on PG without Source ID,RequestID, Category and SubCategory", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC08_CreateMerchantonPGWithoutCatandSubCat() throws InterruptedException {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cache-control", "no-cache");
			headers.put("x-jwt-token",KybJWTtoken);
			
			RequestPath="BrandEMI/MerchantCreationRequest.json";
			String SOURCE_ID="";
			String REQUEST_ID="";
			String CATEGORY="";
			String SUB_CATEGORY="";
		
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("CREATED_BY", CREATED_BY);
			body.put("ACTION", ACTION);
			body.put("REQUEST_ID", REQUEST_ID);
			body.put("USER_NAME", USER_NAME);
			body.put("ACCOUNT_FOR", ACCOUNT_FOR);
			body.put("SOURCE_ID", SOURCE_ID);
			body.put("MERCHANT_TYPE", MERCHANT_TYPE);
			body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
			body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
			body.put("KYB_ID", KYB_ID);
			body.put("BUSINESS_NAME", BUSINESS_NAME);
			body.put("BUSINESS_TYPE", BUSINESS_TYPE);
			body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
			body.put("CUSTOM", CUSTOM);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("CURRENCY", CURRENCY);
			body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
			body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
			body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
			body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
			body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
			body.put("CATEGORY", CATEGORY);
			body.put("SUB_CATEGORY", SUB_CATEGORY);
			body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
			body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
			body.put("PROFILE_ID", PROFILE_ID);
			body.put("EMAIL_ALERT", EMAIL_ALERT);
			body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
			body.put("VALID_FROM", VALID_FROM);
			body.put("VALID_TO", VALID_TO);
			body.put("MULTI_SUPPORT", MULTI_SUPPORT);
			body.put("HOW_MANY", HOW_MANY);
			body.put("OCP", OCP);
			body.put("REQUEST_NAME", REQUEST_NAME);
			body.put("FIRST_NAME", FIRST_NAME);
			body.put("LAST_NAME", LAST_NAME);
			body.put("MOBILE_NUMBER", MOBILE_NUMBER);
			body.put("PHONE_NUMBER", PHONE_NUMBER);
			body.put("MerchUniqRef", MerchUniqRef);
			body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
			body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
			body.put("IS_SUB_USER", IS_SUB_USER);
			body.put("ADDRESS1", ADDRESS1);
			body.put("ADDRESS2", ADDRESS2);
			body.put("ADDRESS3", ADDRESS3);
			body.put("COUNTRY", COUNTRY);
			body.put("STATE", STATE);
			body.put("CITY", CITY);
			body.put("PIN", PIN);
			body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
			body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
			body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
			body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
			body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
			body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
			body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
			body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
			body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
			body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
			body.put("KYC_BANK_NAME", KYC_BANK_NAME);
			body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
			body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
			body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
			body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
			body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
			body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
			body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
			body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
			body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
			body.put("WEBSITE_NAME", WEBSITE_NAME);
			body.put("SIZE_OF_KEY", SIZE_OF_KEY);
			body.put("SMS_MERCHANT", SMS_MERCHANT);
			body.put("PAYOUT_DAYS", PAYOUT_DAYS);
			body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
			body.put("FLAG_MERCHANT", FLAG_MERCHANT);
			body.put("BW_ENABLED", BW_ENABLED);
			body.put("TRANSFER_MODE", TRANSFER_MODE);
			body.put("AUTO", AUTO);
			body.put("TRIGGER_MODE", TRIGGER_MODE);
			body.put("TRIGGER_VALUE", TRIGGER_VALUE);
			body.put("API_DISABLED", API_DISABLED);
			body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
			body.put("P2M_ENABLED", P2M_ENABLED);
			body.put("OB_CHANNEL", OB_CHANNEL);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			body.put("WEBSITE_NAME1", WEBSITE_NAME1);
			body.put("WEBSITE_NAME2", WEBSITE_NAME2);
			body.put("WEBSITE_NAME3", WEBSITE_NAME3);
			body.put("VELOCITY_TYPE", VELOCITY_TYPE);
			body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
			body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
			body.put("FEE_TYPE", FEE_TYPE);
			body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
			body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
			body.put("TXN_TYPE", TXN_TYPE);
			body.put("PAY_MODE", PAY_MODE);
			body.put("CUST_ID", CUST_ID);
			
			
			
			
		
			Response responseObject = EnterpriseBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
				
		
		} 
	
	@Test(description = "Create Enterprise Merchant on PG without Mobile Number", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC09_CreateMerchantonPGWithoutMobileNumber() throws InterruptedException {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cache-control", "no-cache");
			headers.put("x-jwt-token",KybJWTtoken);
			
			RequestPath="BrandEMI/MerchantCreationRequest.json";
			String SOURCE_ID="";
			String REQUEST_ID="";
			String MOBILE_NUMBER="";
			
		
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("CREATED_BY", CREATED_BY);
			body.put("ACTION", ACTION);
			body.put("REQUEST_ID", REQUEST_ID);
			body.put("USER_NAME", USER_NAME);
			body.put("ACCOUNT_FOR", ACCOUNT_FOR);
			body.put("SOURCE_ID", SOURCE_ID);
			body.put("MERCHANT_TYPE", MERCHANT_TYPE);
			body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
			body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
			body.put("KYB_ID", KYB_ID);
			body.put("BUSINESS_NAME", BUSINESS_NAME);
			body.put("BUSINESS_TYPE", BUSINESS_TYPE);
			body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
			body.put("CUSTOM", CUSTOM);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("CURRENCY", CURRENCY);
			body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
			body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
			body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
			body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
			body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
			body.put("CATEGORY", CATEGORY);
			body.put("SUB_CATEGORY", SUB_CATEGORY);
			body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
			body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
			body.put("PROFILE_ID", PROFILE_ID);
			body.put("EMAIL_ALERT", EMAIL_ALERT);
			body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
			body.put("VALID_FROM", VALID_FROM);
			body.put("VALID_TO", VALID_TO);
			body.put("MULTI_SUPPORT", MULTI_SUPPORT);
			body.put("HOW_MANY", HOW_MANY);
			body.put("OCP", OCP);
			body.put("REQUEST_NAME", REQUEST_NAME);
			body.put("FIRST_NAME", FIRST_NAME);
			body.put("LAST_NAME", LAST_NAME);
			body.put("MOBILE_NUMBER", MOBILE_NUMBER);
			body.put("PHONE_NUMBER", PHONE_NUMBER);
			body.put("MerchUniqRef", MerchUniqRef);
			body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
			body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
			body.put("IS_SUB_USER", IS_SUB_USER);
			body.put("ADDRESS1", ADDRESS1);
			body.put("ADDRESS2", ADDRESS2);
			body.put("ADDRESS3", ADDRESS3);
			body.put("COUNTRY", COUNTRY);
			body.put("STATE", STATE);
			body.put("CITY", CITY);
			body.put("PIN", PIN);
			body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
			body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
			body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
			body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
			body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
			body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
			body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
			body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
			body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
			body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
			body.put("KYC_BANK_NAME", KYC_BANK_NAME);
			body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
			body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
			body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
			body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
			body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
			body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
			body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
			body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
			body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
			body.put("WEBSITE_NAME", WEBSITE_NAME);
			body.put("SIZE_OF_KEY", SIZE_OF_KEY);
			body.put("SMS_MERCHANT", SMS_MERCHANT);
			body.put("PAYOUT_DAYS", PAYOUT_DAYS);
			body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
			body.put("FLAG_MERCHANT", FLAG_MERCHANT);
			body.put("BW_ENABLED", BW_ENABLED);
			body.put("TRANSFER_MODE", TRANSFER_MODE);
			body.put("AUTO", AUTO);
			body.put("TRIGGER_MODE", TRIGGER_MODE);
			body.put("TRIGGER_VALUE", TRIGGER_VALUE);
			body.put("API_DISABLED", API_DISABLED);
			body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
			body.put("P2M_ENABLED", P2M_ENABLED);
			body.put("OB_CHANNEL", OB_CHANNEL);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			body.put("WEBSITE_NAME1", WEBSITE_NAME1);
			body.put("WEBSITE_NAME2", WEBSITE_NAME2);
			body.put("WEBSITE_NAME3", WEBSITE_NAME3);
			body.put("VELOCITY_TYPE", VELOCITY_TYPE);
			body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
			body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
			body.put("FEE_TYPE", FEE_TYPE);
			body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
			body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
			body.put("TXN_TYPE", TXN_TYPE);
			body.put("PAY_MODE", PAY_MODE);
			body.put("CUST_ID", CUST_ID);
			
			
			
			
		
			Response responseObject = EnterpriseBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
				
		
		} 
	
	@Test(description = "Create Enterprise Merchant on PG without Phone Number", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC10_CreateMerchantonPGWithoutPhoneNumber() throws InterruptedException {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cache-control", "no-cache");
			headers.put("x-jwt-token",KybJWTtoken);
			
			RequestPath="BrandEMI/MerchantCreationRequest.json";
			String SOURCE_ID="";
			String REQUEST_ID="";
			String PHONE_NUMBER="";
			
		
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("CREATED_BY", CREATED_BY);
			body.put("ACTION", ACTION);
			body.put("REQUEST_ID", REQUEST_ID);
			body.put("USER_NAME", USER_NAME);
			body.put("ACCOUNT_FOR", ACCOUNT_FOR);
			body.put("SOURCE_ID", SOURCE_ID);
			body.put("MERCHANT_TYPE", MERCHANT_TYPE);
			body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
			body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
			body.put("KYB_ID", KYB_ID);
			body.put("BUSINESS_NAME", BUSINESS_NAME);
			body.put("BUSINESS_TYPE", BUSINESS_TYPE);
			body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
			body.put("CUSTOM", CUSTOM);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("CURRENCY", CURRENCY);
			body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
			body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
			body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
			body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
			body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
			body.put("CATEGORY", CATEGORY);
			body.put("SUB_CATEGORY", SUB_CATEGORY);
			body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
			body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
			body.put("PROFILE_ID", PROFILE_ID);
			body.put("EMAIL_ALERT", EMAIL_ALERT);
			body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
			body.put("VALID_FROM", VALID_FROM);
			body.put("VALID_TO", VALID_TO);
			body.put("MULTI_SUPPORT", MULTI_SUPPORT);
			body.put("HOW_MANY", HOW_MANY);
			body.put("OCP", OCP);
			body.put("REQUEST_NAME", REQUEST_NAME);
			body.put("FIRST_NAME", FIRST_NAME);
			body.put("LAST_NAME", LAST_NAME);
			body.put("MOBILE_NUMBER", MOBILE_NUMBER);
			body.put("PHONE_NUMBER", PHONE_NUMBER);
			body.put("MerchUniqRef", MerchUniqRef);
			body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
			body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
			body.put("IS_SUB_USER", IS_SUB_USER);
			body.put("ADDRESS1", ADDRESS1);
			body.put("ADDRESS2", ADDRESS2);
			body.put("ADDRESS3", ADDRESS3);
			body.put("COUNTRY", COUNTRY);
			body.put("STATE", STATE);
			body.put("CITY", CITY);
			body.put("PIN", PIN);
			body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
			body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
			body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
			body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
			body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
			body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
			body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
			body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
			body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
			body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
			body.put("KYC_BANK_NAME", KYC_BANK_NAME);
			body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
			body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
			body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
			body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
			body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
			body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
			body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
			body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
			body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
			body.put("WEBSITE_NAME", WEBSITE_NAME);
			body.put("SIZE_OF_KEY", SIZE_OF_KEY);
			body.put("SMS_MERCHANT", SMS_MERCHANT);
			body.put("PAYOUT_DAYS", PAYOUT_DAYS);
			body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
			body.put("FLAG_MERCHANT", FLAG_MERCHANT);
			body.put("BW_ENABLED", BW_ENABLED);
			body.put("TRANSFER_MODE", TRANSFER_MODE);
			body.put("AUTO", AUTO);
			body.put("TRIGGER_MODE", TRIGGER_MODE);
			body.put("TRIGGER_VALUE", TRIGGER_VALUE);
			body.put("API_DISABLED", API_DISABLED);
			body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
			body.put("P2M_ENABLED", P2M_ENABLED);
			body.put("OB_CHANNEL", OB_CHANNEL);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			body.put("WEBSITE_NAME1", WEBSITE_NAME1);
			body.put("WEBSITE_NAME2", WEBSITE_NAME2);
			body.put("WEBSITE_NAME3", WEBSITE_NAME3);
			body.put("VELOCITY_TYPE", VELOCITY_TYPE);
			body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
			body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
			body.put("FEE_TYPE", FEE_TYPE);
			body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
			body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
			body.put("TXN_TYPE", TXN_TYPE);
			body.put("PAY_MODE", PAY_MODE);
			body.put("CUST_ID", CUST_ID);
			
			
			
			
		
			Response responseObject = EnterpriseBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
				
		
		} 
	
	@Test(description = "Create Enterprise Merchant on PG without Mobile Number and Phone Number", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC11_CreateMerchantonPGWithoutMobileandPhone() throws InterruptedException {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cache-control", "no-cache");
			headers.put("x-jwt-token",KybJWTtoken);
			
			RequestPath="BrandEMI/MerchantCreationRequest.json";
			String SOURCE_ID="";
			String REQUEST_ID="";
			String MOBILE_NUMBER="";
			String PHONE_NUMBER="";
			
		
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("CREATED_BY", CREATED_BY);
			body.put("ACTION", ACTION);
			body.put("REQUEST_ID", REQUEST_ID);
			body.put("USER_NAME", USER_NAME);
			body.put("ACCOUNT_FOR", ACCOUNT_FOR);
			body.put("SOURCE_ID", SOURCE_ID);
			body.put("MERCHANT_TYPE", MERCHANT_TYPE);
			body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
			body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
			body.put("KYB_ID", KYB_ID);
			body.put("BUSINESS_NAME", BUSINESS_NAME);
			body.put("BUSINESS_TYPE", BUSINESS_TYPE);
			body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
			body.put("CUSTOM", CUSTOM);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("CURRENCY", CURRENCY);
			body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
			body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
			body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
			body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
			body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
			body.put("CATEGORY", CATEGORY);
			body.put("SUB_CATEGORY", SUB_CATEGORY);
			body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
			body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
			body.put("PROFILE_ID", PROFILE_ID);
			body.put("EMAIL_ALERT", EMAIL_ALERT);
			body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
			body.put("VALID_FROM", VALID_FROM);
			body.put("VALID_TO", VALID_TO);
			body.put("MULTI_SUPPORT", MULTI_SUPPORT);
			body.put("HOW_MANY", HOW_MANY);
			body.put("OCP", OCP);
			body.put("REQUEST_NAME", REQUEST_NAME);
			body.put("FIRST_NAME", FIRST_NAME);
			body.put("LAST_NAME", LAST_NAME);
			body.put("MOBILE_NUMBER", MOBILE_NUMBER);
			body.put("PHONE_NUMBER", PHONE_NUMBER);
			body.put("MerchUniqRef", MerchUniqRef);
			body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
			body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
			body.put("IS_SUB_USER", IS_SUB_USER);
			body.put("ADDRESS1", ADDRESS1);
			body.put("ADDRESS2", ADDRESS2);
			body.put("ADDRESS3", ADDRESS3);
			body.put("COUNTRY", COUNTRY);
			body.put("STATE", STATE);
			body.put("CITY", CITY);
			body.put("PIN", PIN);
			body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
			body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
			body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
			body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
			body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
			body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
			body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
			body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
			body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
			body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
			body.put("KYC_BANK_NAME", KYC_BANK_NAME);
			body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
			body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
			body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
			body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
			body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
			body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
			body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
			body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
			body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
			body.put("WEBSITE_NAME", WEBSITE_NAME);
			body.put("SIZE_OF_KEY", SIZE_OF_KEY);
			body.put("SMS_MERCHANT", SMS_MERCHANT);
			body.put("PAYOUT_DAYS", PAYOUT_DAYS);
			body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
			body.put("FLAG_MERCHANT", FLAG_MERCHANT);
			body.put("BW_ENABLED", BW_ENABLED);
			body.put("TRANSFER_MODE", TRANSFER_MODE);
			body.put("AUTO", AUTO);
			body.put("TRIGGER_MODE", TRIGGER_MODE);
			body.put("TRIGGER_VALUE", TRIGGER_VALUE);
			body.put("API_DISABLED", API_DISABLED);
			body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
			body.put("P2M_ENABLED", P2M_ENABLED);
			body.put("OB_CHANNEL", OB_CHANNEL);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			body.put("WEBSITE_NAME1", WEBSITE_NAME1);
			body.put("WEBSITE_NAME2", WEBSITE_NAME2);
			body.put("WEBSITE_NAME3", WEBSITE_NAME3);
			body.put("VELOCITY_TYPE", VELOCITY_TYPE);
			body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
			body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
			body.put("FEE_TYPE", FEE_TYPE);
			body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
			body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
			body.put("TXN_TYPE", TXN_TYPE);
			body.put("PAY_MODE", PAY_MODE);
			body.put("CUST_ID", CUST_ID);
			
			
			
			
		
			Response responseObject = EnterpriseBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
				
		
		} 
	
	@Test(description = "Create Enterprise Merchant on PG without REQUEST_NAME", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC12_CreateMerchantonPGWithoutRequestName() throws InterruptedException {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cache-control", "no-cache");
			headers.put("x-jwt-token",KybJWTtoken);
			
			RequestPath="BrandEMI/MerchantCreationRequest.json";
			String SOURCE_ID="";
			String REQUEST_ID="";
			String REQUEST_NAME="";
			
		
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("CREATED_BY", CREATED_BY);
			body.put("ACTION", ACTION);
			body.put("REQUEST_ID", REQUEST_ID);
			body.put("USER_NAME", USER_NAME);
			body.put("ACCOUNT_FOR", ACCOUNT_FOR);
			body.put("SOURCE_ID", SOURCE_ID);
			body.put("MERCHANT_TYPE", MERCHANT_TYPE);
			body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
			body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
			body.put("KYB_ID", KYB_ID);
			body.put("BUSINESS_NAME", BUSINESS_NAME);
			body.put("BUSINESS_TYPE", BUSINESS_TYPE);
			body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
			body.put("CUSTOM", CUSTOM);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("CURRENCY", CURRENCY);
			body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
			body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
			body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
			body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
			body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
			body.put("CATEGORY", CATEGORY);
			body.put("SUB_CATEGORY", SUB_CATEGORY);
			body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
			body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
			body.put("PROFILE_ID", PROFILE_ID);
			body.put("EMAIL_ALERT", EMAIL_ALERT);
			body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
			body.put("VALID_FROM", VALID_FROM);
			body.put("VALID_TO", VALID_TO);
			body.put("MULTI_SUPPORT", MULTI_SUPPORT);
			body.put("HOW_MANY", HOW_MANY);
			body.put("OCP", OCP);
			body.put("REQUEST_NAME", REQUEST_NAME);
			body.put("FIRST_NAME", FIRST_NAME);
			body.put("LAST_NAME", LAST_NAME);
			body.put("MOBILE_NUMBER", MOBILE_NUMBER);
			body.put("PHONE_NUMBER", PHONE_NUMBER);
			body.put("MerchUniqRef", MerchUniqRef);
			body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
			body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
			body.put("IS_SUB_USER", IS_SUB_USER);
			body.put("ADDRESS1", ADDRESS1);
			body.put("ADDRESS2", ADDRESS2);
			body.put("ADDRESS3", ADDRESS3);
			body.put("COUNTRY", COUNTRY);
			body.put("STATE", STATE);
			body.put("CITY", CITY);
			body.put("PIN", PIN);
			body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
			body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
			body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
			body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
			body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
			body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
			body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
			body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
			body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
			body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
			body.put("KYC_BANK_NAME", KYC_BANK_NAME);
			body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
			body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
			body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
			body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
			body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
			body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
			body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
			body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
			body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
			body.put("WEBSITE_NAME", WEBSITE_NAME);
			body.put("SIZE_OF_KEY", SIZE_OF_KEY);
			body.put("SMS_MERCHANT", SMS_MERCHANT);
			body.put("PAYOUT_DAYS", PAYOUT_DAYS);
			body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
			body.put("FLAG_MERCHANT", FLAG_MERCHANT);
			body.put("BW_ENABLED", BW_ENABLED);
			body.put("TRANSFER_MODE", TRANSFER_MODE);
			body.put("AUTO", AUTO);
			body.put("TRIGGER_MODE", TRIGGER_MODE);
			body.put("TRIGGER_VALUE", TRIGGER_VALUE);
			body.put("API_DISABLED", API_DISABLED);
			body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
			body.put("P2M_ENABLED", P2M_ENABLED);
			body.put("OB_CHANNEL", OB_CHANNEL);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			body.put("WEBSITE_NAME1", WEBSITE_NAME1);
			body.put("WEBSITE_NAME2", WEBSITE_NAME2);
			body.put("WEBSITE_NAME3", WEBSITE_NAME3);
			body.put("VELOCITY_TYPE", VELOCITY_TYPE);
			body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
			body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
			body.put("FEE_TYPE", FEE_TYPE);
			body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
			body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
			body.put("TXN_TYPE", TXN_TYPE);
			body.put("PAY_MODE", PAY_MODE);
			body.put("CUST_ID", CUST_ID);
			
			
			
			
		
			Response responseObject = EnterpriseBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
				
		
		} 

	@Test(description = "Create Enterprise Merchant on PG without MERCHANT_INDUSTRY_TYPE", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC13_CreateMerchantonPGWithoutMerchantIndustryType() throws InterruptedException {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cache-control", "no-cache");
			headers.put("x-jwt-token",KybJWTtoken);
			
			RequestPath="BrandEMI/MerchantCreationRequest.json";
			String SOURCE_ID="";
			String REQUEST_ID="";
			String MERCHANT_INDUSTRY_TYPE="";
			
		
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("CREATED_BY", CREATED_BY);
			body.put("ACTION", ACTION);
			body.put("REQUEST_ID", REQUEST_ID);
			body.put("USER_NAME", USER_NAME);
			body.put("ACCOUNT_FOR", ACCOUNT_FOR);
			body.put("SOURCE_ID", SOURCE_ID);
			body.put("MERCHANT_TYPE", MERCHANT_TYPE);
			body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
			body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
			body.put("KYB_ID", KYB_ID);
			body.put("BUSINESS_NAME", BUSINESS_NAME);
			body.put("BUSINESS_TYPE", BUSINESS_TYPE);
			body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
			body.put("CUSTOM", CUSTOM);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("CURRENCY", CURRENCY);
			body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
			body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
			body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
			body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
			body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
			body.put("CATEGORY", CATEGORY);
			body.put("SUB_CATEGORY", SUB_CATEGORY);
			body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
			body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
			body.put("PROFILE_ID", PROFILE_ID);
			body.put("EMAIL_ALERT", EMAIL_ALERT);
			body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
			body.put("VALID_FROM", VALID_FROM);
			body.put("VALID_TO", VALID_TO);
			body.put("MULTI_SUPPORT", MULTI_SUPPORT);
			body.put("HOW_MANY", HOW_MANY);
			body.put("OCP", OCP);
			body.put("REQUEST_NAME", REQUEST_NAME);
			body.put("FIRST_NAME", FIRST_NAME);
			body.put("LAST_NAME", LAST_NAME);
			body.put("MOBILE_NUMBER", MOBILE_NUMBER);
			body.put("PHONE_NUMBER", PHONE_NUMBER);
			body.put("MerchUniqRef", MerchUniqRef);
			body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
			body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
			body.put("IS_SUB_USER", IS_SUB_USER);
			body.put("ADDRESS1", ADDRESS1);
			body.put("ADDRESS2", ADDRESS2);
			body.put("ADDRESS3", ADDRESS3);
			body.put("COUNTRY", COUNTRY);
			body.put("STATE", STATE);
			body.put("CITY", CITY);
			body.put("PIN", PIN);
			body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
			body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
			body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
			body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
			body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
			body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
			body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
			body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
			body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
			body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
			body.put("KYC_BANK_NAME", KYC_BANK_NAME);
			body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
			body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
			body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
			body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
			body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
			body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
			body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
			body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
			body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
			body.put("WEBSITE_NAME", WEBSITE_NAME);
			body.put("SIZE_OF_KEY", SIZE_OF_KEY);
			body.put("SMS_MERCHANT", SMS_MERCHANT);
			body.put("PAYOUT_DAYS", PAYOUT_DAYS);
			body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
			body.put("FLAG_MERCHANT", FLAG_MERCHANT);
			body.put("BW_ENABLED", BW_ENABLED);
			body.put("TRANSFER_MODE", TRANSFER_MODE);
			body.put("AUTO", AUTO);
			body.put("TRIGGER_MODE", TRIGGER_MODE);
			body.put("TRIGGER_VALUE", TRIGGER_VALUE);
			body.put("API_DISABLED", API_DISABLED);
			body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
			body.put("P2M_ENABLED", P2M_ENABLED);
			body.put("OB_CHANNEL", OB_CHANNEL);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			body.put("WEBSITE_NAME1", WEBSITE_NAME1);
			body.put("WEBSITE_NAME2", WEBSITE_NAME2);
			body.put("WEBSITE_NAME3", WEBSITE_NAME3);
			body.put("VELOCITY_TYPE", VELOCITY_TYPE);
			body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
			body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
			body.put("FEE_TYPE", FEE_TYPE);
			body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
			body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
			body.put("TXN_TYPE", TXN_TYPE);
			body.put("PAY_MODE", PAY_MODE);
			body.put("CUST_ID", CUST_ID);
			
			
			
			
		
			Response responseObject = EnterpriseBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
				
		
		} 
	
	
	@Test(description = "Create Enterprise Merchant on PG without OB_CHANNEL", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC14_CreateMerchantonPGWithoutOBChannel() throws InterruptedException {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cache-control", "no-cache");
			headers.put("x-jwt-token",KybJWTtoken);
			
			RequestPath="BrandEMI/MerchantCreationRequest.json";
			String SOURCE_ID="";
			String REQUEST_ID="";
			String OB_CHANNEL="";
			
		
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("CREATED_BY", CREATED_BY);
			body.put("ACTION", ACTION);
			body.put("REQUEST_ID", REQUEST_ID);
			body.put("USER_NAME", USER_NAME);
			body.put("ACCOUNT_FOR", ACCOUNT_FOR);
			body.put("SOURCE_ID", SOURCE_ID);
			body.put("MERCHANT_TYPE", MERCHANT_TYPE);
			body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
			body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
			body.put("KYB_ID", KYB_ID);
			body.put("BUSINESS_NAME", BUSINESS_NAME);
			body.put("BUSINESS_TYPE", BUSINESS_TYPE);
			body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
			body.put("CUSTOM", CUSTOM);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("CURRENCY", CURRENCY);
			body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
			body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
			body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
			body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
			body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
			body.put("CATEGORY", CATEGORY);
			body.put("SUB_CATEGORY", SUB_CATEGORY);
			body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
			body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
			body.put("PROFILE_ID", PROFILE_ID);
			body.put("EMAIL_ALERT", EMAIL_ALERT);
			body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
			body.put("VALID_FROM", VALID_FROM);
			body.put("VALID_TO", VALID_TO);
			body.put("MULTI_SUPPORT", MULTI_SUPPORT);
			body.put("HOW_MANY", HOW_MANY);
			body.put("OCP", OCP);
			body.put("REQUEST_NAME", REQUEST_NAME);
			body.put("FIRST_NAME", FIRST_NAME);
			body.put("LAST_NAME", LAST_NAME);
			body.put("MOBILE_NUMBER", MOBILE_NUMBER);
			body.put("PHONE_NUMBER", PHONE_NUMBER);
			body.put("MerchUniqRef", MerchUniqRef);
			body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
			body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
			body.put("IS_SUB_USER", IS_SUB_USER);
			body.put("ADDRESS1", ADDRESS1);
			body.put("ADDRESS2", ADDRESS2);
			body.put("ADDRESS3", ADDRESS3);
			body.put("COUNTRY", COUNTRY);
			body.put("STATE", STATE);
			body.put("CITY", CITY);
			body.put("PIN", PIN);
			body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
			body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
			body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
			body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
			body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
			body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
			body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
			body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
			body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
			body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
			body.put("KYC_BANK_NAME", KYC_BANK_NAME);
			body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
			body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
			body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
			body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
			body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
			body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
			body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
			body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
			body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
			body.put("WEBSITE_NAME", WEBSITE_NAME);
			body.put("SIZE_OF_KEY", SIZE_OF_KEY);
			body.put("SMS_MERCHANT", SMS_MERCHANT);
			body.put("PAYOUT_DAYS", PAYOUT_DAYS);
			body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
			body.put("FLAG_MERCHANT", FLAG_MERCHANT);
			body.put("BW_ENABLED", BW_ENABLED);
			body.put("TRANSFER_MODE", TRANSFER_MODE);
			body.put("AUTO", AUTO);
			body.put("TRIGGER_MODE", TRIGGER_MODE);
			body.put("TRIGGER_VALUE", TRIGGER_VALUE);
			body.put("API_DISABLED", API_DISABLED);
			body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
			body.put("P2M_ENABLED", P2M_ENABLED);
			body.put("OB_CHANNEL", OB_CHANNEL);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			body.put("WEBSITE_NAME1", WEBSITE_NAME1);
			body.put("WEBSITE_NAME2", WEBSITE_NAME2);
			body.put("WEBSITE_NAME3", WEBSITE_NAME3);
			body.put("VELOCITY_TYPE", VELOCITY_TYPE);
			body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
			body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
			body.put("FEE_TYPE", FEE_TYPE);
			body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
			body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
			body.put("TXN_TYPE", TXN_TYPE);
			body.put("PAY_MODE", PAY_MODE);
			body.put("CUST_ID", CUST_ID);
			
			
			
			
		
			Response responseObject = EnterpriseBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
				
		
		} 
	

	@Test(description = "Create Enterprise Merchant on PG without SOLUTION_TYPE", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC15_CreateMerchantonPGWithoutSolutionType() throws InterruptedException {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cache-control", "no-cache");
			headers.put("x-jwt-token",KybJWTtoken);
			
			RequestPath="BrandEMI/MerchantCreationRequest.json";
			String SOURCE_ID="";
			String REQUEST_ID="";
			String SOLUTION_TYPE="";
			
		
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("CREATED_BY", CREATED_BY);
			body.put("ACTION", ACTION);
			body.put("REQUEST_ID", REQUEST_ID);
			body.put("USER_NAME", USER_NAME);
			body.put("ACCOUNT_FOR", ACCOUNT_FOR);
			body.put("SOURCE_ID", SOURCE_ID);
			body.put("MERCHANT_TYPE", MERCHANT_TYPE);
			body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
			body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
			body.put("KYB_ID", KYB_ID);
			body.put("BUSINESS_NAME", BUSINESS_NAME);
			body.put("BUSINESS_TYPE", BUSINESS_TYPE);
			body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
			body.put("CUSTOM", CUSTOM);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("CURRENCY", CURRENCY);
			body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
			body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
			body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
			body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
			body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
			body.put("CATEGORY", CATEGORY);
			body.put("SUB_CATEGORY", SUB_CATEGORY);
			body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
			body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
			body.put("PROFILE_ID", PROFILE_ID);
			body.put("EMAIL_ALERT", EMAIL_ALERT);
			body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
			body.put("VALID_FROM", VALID_FROM);
			body.put("VALID_TO", VALID_TO);
			body.put("MULTI_SUPPORT", MULTI_SUPPORT);
			body.put("HOW_MANY", HOW_MANY);
			body.put("OCP", OCP);
			body.put("REQUEST_NAME", REQUEST_NAME);
			body.put("FIRST_NAME", FIRST_NAME);
			body.put("LAST_NAME", LAST_NAME);
			body.put("MOBILE_NUMBER", MOBILE_NUMBER);
			body.put("PHONE_NUMBER", PHONE_NUMBER);
			body.put("MerchUniqRef", MerchUniqRef);
			body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
			body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
			body.put("IS_SUB_USER", IS_SUB_USER);
			body.put("ADDRESS1", ADDRESS1);
			body.put("ADDRESS2", ADDRESS2);
			body.put("ADDRESS3", ADDRESS3);
			body.put("COUNTRY", COUNTRY);
			body.put("STATE", STATE);
			body.put("CITY", CITY);
			body.put("PIN", PIN);
			body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
			body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
			body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
			body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
			body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
			body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
			body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
			body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
			body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
			body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
			body.put("KYC_BANK_NAME", KYC_BANK_NAME);
			body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
			body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
			body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
			body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
			body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
			body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
			body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
			body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
			body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
			body.put("WEBSITE_NAME", WEBSITE_NAME);
			body.put("SIZE_OF_KEY", SIZE_OF_KEY);
			body.put("SMS_MERCHANT", SMS_MERCHANT);
			body.put("PAYOUT_DAYS", PAYOUT_DAYS);
			body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
			body.put("FLAG_MERCHANT", FLAG_MERCHANT);
			body.put("BW_ENABLED", BW_ENABLED);
			body.put("TRANSFER_MODE", TRANSFER_MODE);
			body.put("AUTO", AUTO);
			body.put("TRIGGER_MODE", TRIGGER_MODE);
			body.put("TRIGGER_VALUE", TRIGGER_VALUE);
			body.put("API_DISABLED", API_DISABLED);
			body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
			body.put("P2M_ENABLED", P2M_ENABLED);
			body.put("OB_CHANNEL", OB_CHANNEL);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			body.put("WEBSITE_NAME1", WEBSITE_NAME1);
			body.put("WEBSITE_NAME2", WEBSITE_NAME2);
			body.put("WEBSITE_NAME3", WEBSITE_NAME3);
			body.put("VELOCITY_TYPE", VELOCITY_TYPE);
			body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
			body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
			body.put("FEE_TYPE", FEE_TYPE);
			body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
			body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
			body.put("TXN_TYPE", TXN_TYPE);
			body.put("PAY_MODE", PAY_MODE);
			body.put("CUST_ID", CUST_ID);
			
			
			
			
		
			Response responseObject = EnterpriseBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "ERROR");
			Assert.assertEquals(responseObject.jsonPath().getString("errorMessage"), "Invalid request id or source id");
			verifyResponseCodeAs200OK(responseObject);
				
		
		} 
	
	
	@Test(description = "Create Enterprise Merchant on PG", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC16_CreateMerchantonPG() throws InterruptedException {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cache-control", "no-cache");
			headers.put("x-jwt-token",KybJWTtoken);
			
			
			RequestPath="BrandEMI/MerchantCreationRequest.json";
		
	    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("CREATED_BY", CREATED_BY);
			body.put("ACTION", ACTION);
			body.put("REQUEST_ID", REQUEST_ID);
			body.put("USER_NAME", USER_NAME);
			body.put("ACCOUNT_FOR", ACCOUNT_FOR);
			body.put("SOURCE_ID", SOURCE_ID);
			body.put("MERCHANT_TYPE", MERCHANT_TYPE);
			body.put("OFFLINE_ENABLED", OFFLINE_ENABLED);
			body.put("PPI_LIMITED_MERCHANT", PPI_LIMITED_MERCHANT);
			body.put("KYB_ID", KYB_ID);
			body.put("BUSINESS_NAME", BUSINESS_NAME);
			body.put("BUSINESS_TYPE", BUSINESS_TYPE);
			body.put("CALLBACK_URL_ENABLED", CALLBACK_URL_ENABLED);
			body.put("CUSTOM", CUSTOM);
			body.put("MERCHANT_NAME", MERCHANT_NAME);
			body.put("CURRENCY", CURRENCY);
			body.put("REFUND_TO_BANK_ENABLED", REFUND_TO_BANK_ENABLED);
			body.put("STORE_CARD_DETAILS", STORE_CARD_DETAILS);
			body.put("ADD_MONEY_ENABLE", ADD_MONEY_ENABLE);
			body.put("CHECKSUM_ENABLED", CHECKSUM_ENABLED);
			body.put("NUMBER_OF_RETRY", NUMBER_OF_RETRY);
			body.put("CATEGORY", CATEGORY);
			body.put("SUB_CATEGORY", SUB_CATEGORY);
			body.put("INDUSTRY_TYPE", INDUSTRY_TYPE);
			body.put("WALLET_RECHARGE_OPT", WALLET_RECHARGE_OPT);
			body.put("PROFILE_ID", PROFILE_ID);
			body.put("EMAIL_ALERT", EMAIL_ALERT);
			body.put("CONVENIENCE_FEE_TYPE", CONVENIENCE_FEE_TYPE);
			body.put("VALID_FROM", VALID_FROM);
			body.put("VALID_TO", VALID_TO);
			body.put("MULTI_SUPPORT", MULTI_SUPPORT);
			body.put("HOW_MANY", HOW_MANY);
			body.put("OCP", OCP);
			body.put("REQUEST_NAME", REQUEST_NAME);
			body.put("FIRST_NAME", FIRST_NAME);
			body.put("LAST_NAME", LAST_NAME);
			body.put("MOBILE_NUMBER", MOBILE_NUMBER);
			body.put("PHONE_NUMBER", PHONE_NUMBER);
			body.put("MerchUniqRef", MerchUniqRef);
			body.put("ACCOUNT_PRIMARY", ACCOUNT_PRIMARY);
			body.put("CAN_EDIT_PMOBILE", CAN_EDIT_PMOBILE);
			body.put("IS_SUB_USER", IS_SUB_USER);
			body.put("ADDRESS1", ADDRESS1);
			body.put("ADDRESS2", ADDRESS2);
			body.put("ADDRESS3", ADDRESS3);
			body.put("COUNTRY", COUNTRY);
			body.put("STATE", STATE);
			body.put("CITY", CITY);
			body.put("PIN", PIN);
			body.put("SAME_AS_BUSINESS_ADDR", SAME_AS_BUSINESS_ADDR);
			body.put("COMMUNICATION_ADDRESS1", COMMUNICATION_ADDRESS1);
			body.put("COMMUNICATION_ADDRESS2", COMMUNICATION_ADDRESS2);
			body.put("COMMUNICATION_ADDRESS3", COMMUNICATION_ADDRESS3);
			body.put("COMMUNICATION_COUNTRY", COMMUNICATION_COUNTRY);
			body.put("COMMUNICATION_STATE", COMMUNICATION_STATE);
			body.put("COMMUNICATION_CITY", COMMUNICATION_CITY);
			body.put("COMMUNICATION_PIN", COMMUNICATION_PIN);
			body.put("COMMUNICATION_LATITUDE", COMMUNICATION_LATITUDE);
			body.put("COMMUNICATION_LONGITUDE", COMMUNICATION_LONGITUDE);
			body.put("KYC_BANK_NAME", KYC_BANK_NAME);
			body.put("KYC_BANK_ACCOUNT_HOLDER_NAME", KYC_BANK_ACCOUNT_HOLDER_NAME);
			body.put("KYC_BANK_ACCOUNT_NO", KYC_BANK_ACCOUNT_NO);
			body.put("KYC_BUSINESS_PAN_NO", KYC_BUSINESS_PAN_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_PAN_NO", KYC_AUTHORIZED_SIGNATORY_PAN_NO);
			body.put("KYC_BUSINESS_IFSC_NO", KYC_BUSINESS_IFSC_NO);
			body.put("KYC_AUTHORIZED_SIGNATORY_NAME", KYC_AUTHORIZED_SIGNATORY_NAME);
			body.put("KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO);
			body.put("COMM_STAT_SELECT", COMM_STAT_SELECT);
			body.put("EMAIL_MERCHANT", EMAIL_MERCHANT);
			body.put("EMAIL_CONSUMER", EMAIL_CONSUMER);
			body.put("REQUEST_TYPE_NAME", REQUEST_TYPE_NAME);
			body.put("WEBSITE_NAME", WEBSITE_NAME);
			body.put("SIZE_OF_KEY", SIZE_OF_KEY);
			body.put("SMS_MERCHANT", SMS_MERCHANT);
			body.put("PAYOUT_DAYS", PAYOUT_DAYS);
			body.put("ONLINE_SETTLEMENT", ONLINE_SETTLEMENT);
			body.put("FLAG_MERCHANT", FLAG_MERCHANT);
			body.put("BW_ENABLED", BW_ENABLED);
			body.put("TRANSFER_MODE", TRANSFER_MODE);
			body.put("AUTO", AUTO);
			body.put("TRIGGER_MODE", TRIGGER_MODE);
			body.put("TRIGGER_VALUE", TRIGGER_VALUE);
			body.put("API_DISABLED", API_DISABLED);
			body.put("MERCHANT_INDUSTRY_TYPE", MERCHANT_INDUSTRY_TYPE);
			body.put("P2M_ENABLED", P2M_ENABLED);
			body.put("OB_CHANNEL", OB_CHANNEL);
			body.put("SOLUTION_TYPE", SOLUTION_TYPE);
			body.put("WEBSITE_NAME1", WEBSITE_NAME1);
			body.put("WEBSITE_NAME2", WEBSITE_NAME2);
			body.put("WEBSITE_NAME3", WEBSITE_NAME3);
			body.put("VELOCITY_TYPE", VELOCITY_TYPE);
			body.put("MAX_AMT_PER_DAY", MAX_AMT_PER_DAY);
			body.put("MAX_AMT_PER_MONTH", MAX_AMT_PER_MONTH);
			body.put("FEE_TYPE", FEE_TYPE);
			body.put("PERCENT_COMMISSION", PERCENT_COMMISSION);
			body.put("COMMISSION_TYPE_BOTH", COMMISSION_TYPE_BOTH);
			body.put("TXN_TYPE", TXN_TYPE);
			body.put("PAY_MODE", PAY_MODE);
			body.put("CUST_ID", CUST_ID);
			
			
			
			
		
			Response responseObject = EnterpriseBaseClassObject.MerchantCreationonPG(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("STATUS"), "MID generation is in progress");
				verifyResponseCodeAs200OK(responseObject);
				Thread.sleep(8000);  //wait for MID Generation
				System.out.println("Mobile number is "+ mobile);
				
		
		} 
	
	
	
	@Test(description = "Check Created merchant on PG without token ", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC17_CheckCreatedMerchantonPGWithoutToken() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("x-sso-token", "");
			headers.put("Cookie", "BOSS_SESSION=ab9d25bc-f8aa-4be5-bd5a-b0e25d8bbbb5");
			
			
			
			RequestPath="BrandEMI/CheckCreatedMerchant.json";
		
			Response responseObject = EnterpriseBaseClassObject.CheckCreatedMerchant(headers, RequestPath , mobile);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("message"), "Access Denied");
			verifyResponseCodeAs403BadRequest(responseObject);
			
			
				
		
		} 
	

	@Test(description = "Check Created merchant on PG without Mobile ", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC18_CheckCreatedMerchantonPGWithoutMobile() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("x-sso-token", xssotoken);
			headers.put("Cookie", "BOSS_SESSION=ab9d25bc-f8aa-4be5-bd5a-b0e25d8bbbb5");
			
			String mobile="";
			
			
			RequestPath="BrandEMI/CheckCreatedMerchant.json";
		
			Response responseObject = EnterpriseBaseClassObject.CheckCreatedMerchant(headers, RequestPath , mobile);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("BO_412"), "Data not found");
			verifyResponseCodeAs400BadRequest(responseObject);
			
			
				
		
		}  
	
	@Test(description = "Check Created merchant on PG without token and cookie ", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC19_CheckCreatedMerchantonPGWithoutTokenCookie() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("x-sso-token", "");
			headers.put("Cookie", "");
			
			
			
			RequestPath="BrandEMI/CheckCreatedMerchant.json";
		
			Response responseObject = EnterpriseBaseClassObject.CheckCreatedMerchant(headers, RequestPath , mobile);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("message"), "Access Denied");
			verifyResponseCodeAs403BadRequest(responseObject);
			
			
				
		
		} 
	
	@Test(description = "Check Created merchant on PG ", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC18_CheckCreatedMerchantonPG() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("x-sso-token", xssotoken);
			headers.put("Cookie", "BOSS_SESSION=ab9d25bc-f8aa-4be5-bd5a-b0e25d8bbbb5");
			
			
			RequestPath="BrandEMI/CheckCreatedMerchant.json";
		
			Response responseObject = EnterpriseBaseClassObject.CheckCreatedMerchant(headers, RequestPath , mobile);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				//Assert.assertEquals(responseObject.jsonPath().getString("state"), "null");
			verifyResponseCodeAs200OK(responseObject);
			MID=responseObject.jsonPath().getString("MID");
			
				
		
		}   
	
	
	
	
	
	
}
