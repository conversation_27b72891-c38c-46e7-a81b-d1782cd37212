package OCL.RestApiForEdcPlans;


import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;


public class RestApiTest extends RestApiBase{
	
	private static final Logger LOGGER = LogManager.getLogger(RestApiTest.class);
	
	
	public String RequestPath="";
	Random random=new Random();
	
	String model="PR"+random.nextInt(200);
	String deviceType1="Android";
	String deviceType2="Linux";
	String productID="1201285044";
	

	
	RestApiBase RestApiBaseClassObject= new RestApiBase();
	BaseMethod BaseMethodObject=new BaseMethod();
	
	
	
	@Test(description = "Create Product with devicetype1", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC01_CreateProduct() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			
			headers.put("Content-Type", "application/json");
			headers.put("cookie", "_ga=GA1.3.2092618495.1644414088; _gid=GA1.3.576927819.1644809664; X-MW-TOKEN=943f64f0-df5b-465c-9e75-6e072e5a8fec; X-MW-TOKEN-EX=sdT4i1cT2RWbY3k0E0%2BS6Y4pmgabKyPAVVmKzWdQM1vRVxLak9SFDzfadj7Mj%2BHWUo3wibdKhU6ZM5ARDHvADg%3D%3D; X-MW-TOKEN-EXTERNAL=ueBNKjq9sJEjRa%2FsJb2u2gOlGMGu8UzjUMazOPbf2ZFlAV%2FOkqjdZkBmM4ADR%2BuWjGx7a2xqByOaq3DGBTsEYg%3D%3D; market-onboard.sid=s%3AwQ1I_KMVNqt-AkVqaJs00o-6l2dShlpX.Zj1Ul03nw4%2BCZIgKLjvUtGNZzPjuUQ17IYIl3iMVubU; _gat_UA-48995472-4=1");
			
			RequestPath="RestApiEdc/RestApi/ProductCreate/ProductCreateRequest.json";
			
			Map<String, String> body = new HashMap<String, String>();
			
			body.put("model", model);
			body.put("deviceType", deviceType1);
			body.put("productID", productID);
			
			Response responseObject = RestApiBaseClassObject.CreateProduct(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("errorCode"), "0");
			verifyResponseCodeAs201OK(responseObject);
			
	}   
	
	@Test(description = "CreateProduct with devicetype2", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC02_CreateProduct() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			
			headers.put("Content-Type", "application/json");
			headers.put("cookie", "_ga=GA1.3.2092618495.1644414088; _gid=GA1.3.576927819.1644809664; X-MW-TOKEN=c3cb0266-5a12-4f79-8b25-e1d96994e2fb; X-MW-TOKEN-EX=XlvXPmCvmrAFghKb2Zjzmyi93e%2BSIMSDEh1irZoYuD2QWRyU6PqLS6%2BkRysyPwb91C%2FrZ6dJliEviI9esCHfIA%3D%3D; X-MW-TOKEN-EXTERNAL=EcurM2UzvpRw4zTw6eofiyPuqKRvHUvG9noQqo5l7sB8AmwtDziEm9apzQJFiUo7rwalvjAEodujUgzupE8QkA%3D%3D; market-onboard.sid=s%3AXO_-n_1PgnbgmJa6A5mCxnA1MMkLc6T2.1kQaKCu73tF0HD6LvidcXPAlJtaVJgDiYJdB0E5Y39w; ff.sid=s%3A-CTErckwbxJBf0SUvxlk7pSnL9YUksIb.4zpm8IuJ8KW0EJbTY%2FWEmSpLjZY7z4tKdqhnEwyiBPY; _gat_UA-48995472-4=1");
			
			RequestPath="RestApiEdc/RestApi/ProductCreate/ProductCreateRequest.json";
			Map<String, String> body = new HashMap<String, String>();
			
			body.put("model", model);
			body.put("deviceType", deviceType2);
			body.put("productID", productID);
			
			Response responseObject = RestApiBaseClassObject.CreateProduct(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
	
			Assert.assertEquals(responseObject.jsonPath().getString("errorCode"), "0");
			verifyResponseCodeAs201OK(responseObject);
			
			
	}   
	@Test(description = "Create ProductExisting with deviceType1", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC03_CreateProductExistingWithdeviceType1() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cookie", "_ga=GA1.3.2092618495.1644414088; _gid=GA1.3.576927819.1644809664; X-MW-TOKEN=c3cb0266-5a12-4f79-8b25-e1d96994e2fb; X-MW-TOKEN-EX=XlvXPmCvmrAFghKb2Zjzmyi93e%2BSIMSDEh1irZoYuD2QWRyU6PqLS6%2BkRysyPwb91C%2FrZ6dJliEviI9esCHfIA%3D%3D; X-MW-TOKEN-EXTERNAL=EcurM2UzvpRw4zTw6eofiyPuqKRvHUvG9noQqo5l7sB8AmwtDziEm9apzQJFiUo7rwalvjAEodujUgzupE8QkA%3D%3D; market-onboard.sid=s%3AXO_-n_1PgnbgmJa6A5mCxnA1MMkLc6T2.1kQaKCu73tF0HD6LvidcXPAlJtaVJgDiYJdB0E5Y39w; ff.sid=s%3A-CTErckwbxJBf0SUvxlk7pSnL9YUksIb.4zpm8IuJ8KW0EJbTY%2FWEmSpLjZY7z4tKdqhnEwyiBPY; _gat_UA-48995472-4=1");
			
			RequestPath="RestApiEdc/RestApi/ProductCreate/ProductCreateRequest.json";
			Map<String, String> body = new HashMap<String, String>();
			
			body.put("model", model);
			body.put("deviceType", deviceType1);
			body.put("productID", productID);
			
			Response responseObject = RestApiBaseClassObject.CreateProduct(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("errorCode"), "409");
			verifyResponseCodeAs409IdentifierExists(responseObject);
			
	}
	@Test(description = "Create ProductExisting with devicetype2", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC04_CreateProductExistingWithdeviceType2() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cookie", "_ga=GA1.3.2092618495.1644414088; _gid=GA1.3.576927819.1644809664; X-MW-TOKEN=c3cb0266-5a12-4f79-8b25-e1d96994e2fb; X-MW-TOKEN-EX=XlvXPmCvmrAFghKb2Zjzmyi93e%2BSIMSDEh1irZoYuD2QWRyU6PqLS6%2BkRysyPwb91C%2FrZ6dJliEviI9esCHfIA%3D%3D; X-MW-TOKEN-EXTERNAL=EcurM2UzvpRw4zTw6eofiyPuqKRvHUvG9noQqo5l7sB8AmwtDziEm9apzQJFiUo7rwalvjAEodujUgzupE8QkA%3D%3D; market-onboard.sid=s%3AXO_-n_1PgnbgmJa6A5mCxnA1MMkLc6T2.1kQaKCu73tF0HD6LvidcXPAlJtaVJgDiYJdB0E5Y39w; ff.sid=s%3A-CTErckwbxJBf0SUvxlk7pSnL9YUksIb.4zpm8IuJ8KW0EJbTY%2FWEmSpLjZY7z4tKdqhnEwyiBPY; _gat_UA-48995472-4=1");
			
			RequestPath="RestApiEdc/RestApi/ProductCreate/ProductCreateRequest.json";
			Map<String, String> body = new HashMap<String, String>();
			
			body.put("model", model);
			body.put("deviceType", deviceType2);
			body.put("productID", productID);
			
			Response responseObject = RestApiBaseClassObject.CreateProduct(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			Assert.assertEquals(responseObject.jsonPath().getString("errorCode"), "409");
			verifyResponseCodeAs409IdentifierExists(responseObject);
			
	}
	
	@Test(description = "Create Product With out permissions with devicetype1", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC05_CreateProductWithoutPermissionsWithdeviceType1() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cookie", " ");
			
			RequestPath="RestApiEdc/RestApi/ProductCreate/ProductCreateRequest.json";
			Map<String, String> body = new HashMap<String, String>();
			
			body.put("model", model);
			body.put("deviceType", deviceType1);
			body.put("productID", productID);
			
			Response responseObject = RestApiBaseClassObject.CreateProduct(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			
			verifyResponseCodeAs401Unauthorized(responseObject);
			
	}
	@Test(description = "Create Product With out permissions with devicetype2", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC06_CreateProductWithoutpermissionsWithdeviceType2() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cookie", " ");
			RequestPath="RestApiEdc/RestApi/ProductCreate/ProductCreateRequest.json";
			Map<String, String> body = new HashMap<String, String>();
			
			body.put("model", model);
			body.put("deviceType", deviceType2);
			body.put("productID", productID);
			
			Response responseObject = RestApiBaseClassObject.CreateProduct(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			
			verifyResponseCodeAs401Unauthorized(responseObject);
			
	}
	@Test(description = "Create Product With out headers", groups = {"Regression" },enabled=false)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC07_CreateProductWithoutheaders() throws InterruptedException
	{
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type","");
			headers.put("cookie","");
			RequestPath="RestApiEdc/RestApi/ProductCreate/ProductCreateRequest.json";
			
			Map<String, String> body = new HashMap<String, String>();
			
			body.put("model", model);
			body.put("deviceType", deviceType2);
			body.put("productID", productID);
			
			Response responseObject = RestApiBaseClassObject.CreateProduct(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			
			verifyResponseCodeAs401Unauthorized(responseObject);
			
	}
	@Test(description = "Create Product With out model with devicetype1", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC08_CreateProductWithOutModelWithdeviceType1() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cookie", "_ga=GA1.3.2092618495.1644414088; _gid=GA1.3.576927819.1644809664; X-MW-TOKEN=c3cb0266-5a12-4f79-8b25-e1d96994e2fb; X-MW-TOKEN-EX=XlvXPmCvmrAFghKb2Zjzmyi93e%2BSIMSDEh1irZoYuD2QWRyU6PqLS6%2BkRysyPwb91C%2FrZ6dJliEviI9esCHfIA%3D%3D; X-MW-TOKEN-EXTERNAL=EcurM2UzvpRw4zTw6eofiyPuqKRvHUvG9noQqo5l7sB8AmwtDziEm9apzQJFiUo7rwalvjAEodujUgzupE8QkA%3D%3D; market-onboard.sid=s%3AXO_-n_1PgnbgmJa6A5mCxnA1MMkLc6T2.1kQaKCu73tF0HD6LvidcXPAlJtaVJgDiYJdB0E5Y39w; ff.sid=s%3A-CTErckwbxJBf0SUvxlk7pSnL9YUksIb.4zpm8IuJ8KW0EJbTY%2FWEmSpLjZY7z4tKdqhnEwyiBPY; _gat_UA-48995472-4=1");
			
			RequestPath="RestApiEdc/RestApi/ProductCreate/ProductCreateRequest.json";
			
			String model="";
			String deviceType1="Android";
			String productID="1201285044";
			Map<String, String> body = new HashMap<String, String>();
			
			body.put("model", model);
			body.put("deviceType", deviceType1);
			body.put("productID", productID);
			
			Response responseObject = RestApiBaseClassObject.CreateProduct(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			
			verifyResponseCodeAs404Notfound(responseObject);
			
	}
	@Test(description = "Create Product With out model with devicetype2", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC09_CreateProductWithOutModelWithdeviceType2() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cookie", "ff.sid=s%3AiHhXGCYNYq0lQnMIs1rg3ap3t5z-FW68.Tig6z2UYQO6UoBD0tzEIzq8spnvQ%2F9YMtP2b%2BFlZDhI; _ga=GA1.3.2092618495.16444140ff.sid=s%3AiHhXGCYNYq0lQnMIs1rg3ap3t588; _gid=GA1.3.818739736.1644414088; _gat_UA-48995472-4=1; X-MW-TOKEN=379944cd-0fa7-4451-a74e-091eedb5c661; X-MW-TOKEN-EX=7FY5MNMXcb%2BRbam%2FOo6R4Xf%2BSnaoeJGaBkDanWnGbLItdmzO%2BMm%2BakXTdoMQDhNVkfZfYHAjzqE84CMq%2B0L4gQ%3D%3D; X-MW-TOKEN-EXTERNAL=edo0x0X1Gkdxnng338Eb7Esk8yiy8TZxoEgoKDPf3tBDgLJfD0D8a4en7gaotmCdtLp%2Fia0mNkwOqzNgVToODw%3D%3D; market-onboard.sid=s%3AHmmf_TDI2UoCruTv9X4mlwMZilffCIQb.7z19qe839A8RBAcTLGUdLDhhSqKOn9CEb99jovBd0Fg");
			
			RequestPath="RestApiEdc/RestApi/ProductCreate/ProductCreateRequest.json";
			
			String model="";
			String deviceType2="Linux";
			String productID="120128504478";
			
			Map<String, String> body = new HashMap<String, String>();
			
			body.put("model", model);
			body.put("deviceType", deviceType2);
			body.put("productID", productID);
			
			Response responseObject = RestApiBaseClassObject.CreateProduct(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			
			verifyResponseCodeAs401Unauthorized(responseObject);
			
	}
	@Test(description = "Create Product With out devicetype1", groups = {"Regression" },enabled=false)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC10_CreateProductWithOutdeviceType1() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cookie", "_ga=GA1.3.2092618495.1644414088; _gid=GA1.3.576927819.1644809664; X-MW-TOKEN=c3cb0266-5a12-4f79-8b25-e1d96994e2fb; X-MW-TOKEN-EX=XlvXPmCvmrAFghKb2Zjzmyi93e%2BSIMSDEh1irZoYuD2QWRyU6PqLS6%2BkRysyPwb91C%2FrZ6dJliEviI9esCHfIA%3D%3D; X-MW-TOKEN-EXTERNAL=EcurM2UzvpRw4zTw6eofiyPuqKRvHUvG9noQqo5l7sB8AmwtDziEm9apzQJFiUo7rwalvjAEodujUgzupE8QkA%3D%3D; market-onboard.sid=s%3AXO_-n_1PgnbgmJa6A5mCxnA1MMkLc6T2.1kQaKCu73tF0HD6LvidcXPAlJtaVJgDiYJdB0E5Y39w; ff.sid=s%3A-CTErckwbxJBf0SUvxlk7pSnL9YUksIb.4zpm8IuJ8KW0EJbTY%2FWEmSpLjZY7z4tKdqhnEwyiBPY; _gat_UA-48995472-4=1");
			
			RequestPath="RestApiEdc/RestApi/ProductCreate/ProductCreateRequest.json";
			
			String model="PR001";
			String deviceType1="";
			String productID="1201285044";
			Map<String, String> body = new HashMap<String, String>();
			
			body.put("model", model);
			body.put("deviceType", deviceType1);
			body.put("productID", productID);
			
			Response responseObject = RestApiBaseClassObject.CreateProduct(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			
			verifyResponseCodeAs404Notfound(responseObject);
			
	}
	@Test(description = "Create Product With out ProductId with deviceType1", groups = {"Regression" },enabled=false)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC11_CreateProductWithOutProductIdWithdeviceType1() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cookie", "ff.sid=s%3AiHhXGCYNYq0lQnMIs1rg3ap3t5z-FW68.Tig6z2UYQO6UoBD0tzEIzq8spnvQ%2F9YMtP2b%2BFlZDhI; _ga=GA1.3.2092618495.16444140ff.sid=s%3AiHhXGCYNYq0lQnMIs1rg3ap3t588; _gid=GA1.3.818739736.1644414088; _gat_UA-48995472-4=1; X-MW-TOKEN=379944cd-0fa7-4451-a74e-091eedb5c661; X-MW-TOKEN-EX=7FY5MNMXcb%2BRbam%2FOo6R4Xf%2BSnaoeJGaBkDanWnGbLItdmzO%2BMm%2BakXTdoMQDhNVkfZfYHAjzqE84CMq%2B0L4gQ%3D%3D; X-MW-TOKEN-EXTERNAL=edo0x0X1Gkdxnng338Eb7Esk8yiy8TZxoEgoKDPf3tBDgLJfD0D8a4en7gaotmCdtLp%2Fia0mNkwOqzNgVToODw%3D%3D; market-onboard.sid=s%3AHmmf_TDI2UoCruTv9X4mlwMZilffCIQb.7z19qe839A8RBAcTLGUdLDhhSqKOn9CEb99jovBd0Fg");
			
			RequestPath="RestApiEdc/RestApi/ProductCreate/ProductCreateRequest.json";
			
			String model="PR001";
			String deviceType1="Android";
			String productID="";
			Map<String, String> body = new HashMap<String, String>();
			
			body.put("model", model);
			body.put("deviceType", deviceType1);
			body.put("productID", productID);
			
			Response responseObject = RestApiBaseClassObject.CreateProduct(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			
			verifyResponseCodeAs404Notfound(responseObject);
			
	}
	@Test(description = "Create Product With out ProductId with deviceType2", groups = {"Regression" },enabled=false)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC12_CreateProductWithOutProductIdWithdeviceType2() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cookie", "ff.sid=s%3AiHhXGCYNYq0lQnMIs1rg3ap3t5z-FW68.Tig6z2UYQO6UoBD0tzEIzq8spnvQ%2F9YMtP2b%2BFlZDhI; _ga=GA1.3.2092618495.16444140ff.sid=s%3AiHhXGCYNYq0lQnMIs1rg3ap3t588; _gid=GA1.3.818739736.1644414088; _gat_UA-48995472-4=1; X-MW-TOKEN=379944cd-0fa7-4451-a74e-091eedb5c661; X-MW-TOKEN-EX=7FY5MNMXcb%2BRbam%2FOo6R4Xf%2BSnaoeJGaBkDanWnGbLItdmzO%2BMm%2BakXTdoMQDhNVkfZfYHAjzqE84CMq%2B0L4gQ%3D%3D; X-MW-TOKEN-EXTERNAL=edo0x0X1Gkdxnng338Eb7Esk8yiy8TZxoEgoKDPf3tBDgLJfD0D8a4en7gaotmCdtLp%2Fia0mNkwOqzNgVToODw%3D%3D; market-onboard.sid=s%3AHmmf_TDI2UoCruTv9X4mlwMZilffCIQb.7z19qe839A8RBAcTLGUdLDhhSqKOn9CEb99jovBd0Fg");
			
			RequestPath="RestApiEdc/RestApi/ProductCreate/ProductCreateRequest.json";
			
			String model="PR001";
			String deviceType2="Linux";
			String productID="";
			Map<String, String> body = new HashMap<String, String>();
			
			body.put("model", model);
			body.put("deviceType", deviceType2);
			body.put("productID", productID);
			
			Response responseObject = RestApiBaseClassObject.CreateProduct(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			
			verifyResponseCodeAs404Notfound(responseObject);
	
	}
	@Test(description = "Create Product With out Xmwtoken devicetype1", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC13_CreateProductWithOutxmwtokendeviceType1() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cookie", "_ga=GA1.3.2092618495.1644414088; _gid=GA1.3.576927819.1644809664; X-MW-TOKEN= ; X-MW-TOKEN-EX=XlvXPmCvmrAFghKb2Zjzmyi93e%2BSIMSDEh1irZoYuD2QWRyU6PqLS6%2BkRysyPwb91C%2FrZ6dJliEviI9esCHfIA%3D%3D; X-MW-TOKEN-EXTERNAL=EcurM2UzvpRw4zTw6eofiyPuqKRvHUvG9noQqo5l7sB8AmwtDziEm9apzQJFiUo7rwalvjAEodujUgzupE8QkA%3D%3D; market-onboard.sid=s%3AXO_-n_1PgnbgmJa6A5mCxnA1MMkLc6T2.1kQaKCu73tF0HD6LvidcXPAlJtaVJgDiYJdB0E5Y39w; ff.sid=s%3A-CTErckwbxJBf0SUvxlk7pSnL9YUksIb.4zpm8IuJ8KW0EJbTY%2FWEmSpLjZY7z4tKdqhnEwyiBPY; _gat_UA-48995472-4=1");
			
			RequestPath="RestApiEdc/RestApi/ProductCreate/ProductCreateRequest.json";
			
			
			Map<String, String> body = new HashMap<String, String>();
			
			body.put("model", model);
			body.put("deviceType", deviceType1);
			body.put("productID", productID);
			
			Response responseObject = RestApiBaseClassObject.CreateProduct(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			
			verifyResponseCodeAs401Unauthorized(responseObject);
			
	}
	
	@Test(description = "Create Product With out Xmwtoken devicetype2", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC14_CreateProductWithOutxmwtokendeviceType2() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("cookie", "_ga=GA1.3.2092618495.1644414088; _gid=GA1.3.576927819.1644809664; X-MW-TOKEN= ; X-MW-TOKEN-EX=XlvXPmCvmrAFghKb2Zjzmyi93e%2BSIMSDEh1irZoYuD2QWRyU6PqLS6%2BkRysyPwb91C%2FrZ6dJliEviI9esCHfIA%3D%3D; X-MW-TOKEN-EXTERNAL=EcurM2UzvpRw4zTw6eofiyPuqKRvHUvG9noQqo5l7sB8AmwtDziEm9apzQJFiUo7rwalvjAEodujUgzupE8QkA%3D%3D; market-onboard.sid=s%3AXO_-n_1PgnbgmJa6A5mCxnA1MMkLc6T2.1kQaKCu73tF0HD6LvidcXPAlJtaVJgDiYJdB0E5Y39w; ff.sid=s%3A-CTErckwbxJBf0SUvxlk7pSnL9YUksIb.4zpm8IuJ8KW0EJbTY%2FWEmSpLjZY7z4tKdqhnEwyiBPY; _gat_UA-48995472-4=1");
						
			RequestPath="RestApiEdc/RestApi/ProductCreate/ProductCreateRequest.json";
			
			
			Map<String, String> body = new HashMap<String, String>();
			
			body.put("model", model);
			body.put("deviceType", deviceType2);
			body.put("productID", productID);
			
			Response responseObject = RestApiBaseClassObject.CreateProduct(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			
			verifyResponseCodeAs401Unauthorized(responseObject);
			
	}
	
}
