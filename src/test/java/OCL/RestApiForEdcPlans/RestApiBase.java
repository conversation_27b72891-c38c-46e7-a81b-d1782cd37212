package OCL.RestApiForEdcPlans;


import Request.RestApi.ProductCreate;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;

import java.util.Map;

public class RestApiBase extends BaseMethod {
	
	 
	private static final Logger LOGGER = LogManager.getLogger(RestApiBase.class);
		
		/*
	     * Method to create Product
	     * @param headers
	     * @param body
	     * @return
	     */
	
	    public Response CreateProduct(Map<String, String> headers, Map<String, String> body,String RequestPath) {

	        ProductCreate createProductObject = new ProductCreate(RequestPath);

	        for (Map.Entry m : headers.entrySet()) {
	        	createProductObject.setHeader(m.getKey().toString(), m.getValue().toString());
	        }

	        for (Map.Entry m : body.entrySet()) {
	        	createProductObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
	        }

	        Response createProductObjectResponse = createProductObject.callAPI();

	        return createProductObjectResponse;
	    }
	    
	    /**
	     * Verify  Response Code as 201 OK
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs201OK(Response responseObject) {

	        LOGGER.info("Status Code : " +responseObject.getStatusCode());

	        Assert.assertEquals(responseObject.getStatusCode(),201);

	    }
	    
	    /**
	     * Verify  Response Code as 409 identifier already exists
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs409IdentifierExists(Response responseObject) {

	        LOGGER.info("Status Code : " +responseObject.getStatusCode());

	        Assert.assertEquals(responseObject.getStatusCode(),409);

	    }
	
	    /**
	     * Verify  Response Code as 401 Unauthorized
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs401Unauthorized(Response responseObject) {

	    	
	        LOGGER.info("Status Code : " +responseObject.getStatusCode());
	        Assert.assertEquals(responseObject.getStatusCode(),401);

	    }
	    /**
	     * Verify  Response Code as 415 Unsupported Media Type
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs415UnsupportedMediaType(Response responseObject) {

	    	
	        LOGGER.info("Status Code : " +responseObject.getStatusCode());
	        Assert.assertEquals(responseObject.getStatusCode(),415);

	    }
	    
	    /**
	     * Verify  Response Code as 404 Notfound
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs404Notfound(Response responseObject) {

	    	
	        LOGGER.info("Status Code : " +responseObject.getStatusCode());
	        Assert.assertEquals(responseObject.getStatusCode(),404);

	    }
	    /**
	     * Verify  Response Code as 400 Bad Request
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs400BadRequest(Response responseObject) {

	        LOGGER.info("Status Code : " +responseObject.getStatusCode());

	        Assert.assertEquals(responseObject.getStatusCode(),400);

	    }
	    
}
