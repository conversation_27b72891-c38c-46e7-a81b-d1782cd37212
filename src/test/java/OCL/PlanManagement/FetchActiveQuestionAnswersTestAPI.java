package OCL.PlanManagement;

import Request.ODS.FetchActiveQuestionAnswers;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goldengate.common.BaseMethod;
import Services.ODS.OdsMiddlewareServices;
import com.google.gson.JsonArray;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;

import java.util.*;


public class FetchActiveQuestionAnswersTestAPI extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(FetchDeviceDetailsAPITest.class);

    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";

    @Test(priority = 0, description = "Checking if API is able to fetch Active Question Answer on Category Soundbox - Checking the status code - 200", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_FetchActiveQuestionAnswersTestCode() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswers"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "SOUNDBOX");
        body.put("type", "4G");
        body.put("model", "SOUNDBOX 5.0");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        int httpCode = respObj.statusCode();

        System.out.println(jwt);
        Assert.assertTrue(httpCode == 200, "TestcasePass");
    }

    @Test(priority = 0, description = "Checking if API is able to fetch Active Question Answer on Category EDC - Checking the status code - 200", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_FetchActiveQuestionAnswersTestCode() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswers"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");


        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        int httpCode = respObj.statusCode();

        System.out.println(jwt);
        Assert.assertTrue(httpCode == 200, "TestcasePass");
    }

    @Test(priority = 0, description = "Checking 400 response on sending nothing in request body", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_FetchActiveQuestionAnswersTestCode() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswers"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "");
        body.put("type", "");
        body.put("model", "");


        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        int httpCode = respObj.statusCode();

        System.out.println(jwt);
        Assert.assertTrue(httpCode == 400, "TestcasePass");
    }

    @Test(priority = 0, description = "Checking if API is able to fetch Active Question Answer on Category Soundbox - Checking the Result message", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_FetchActiveQuestionAnswersTestCode() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswers"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "SOUNDBOX");
        body.put("type", "4G");
        body.put("model", "SOUNDBOX 5.0");


        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        int httpCode = respObj.statusCode();

        System.out.println(jwt);
        Assert.assertEquals(respObj.jsonPath().getJsonObject("resultInfo.resultCode"), "SUCCESS");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("resultInfo.resultStatus"), "S");
    }

    @Test(priority = 0, description = "Checking if the device is fetching correctly", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_FetchActiveQuestionAnswersTestCode() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswers"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "SOUNDBOX");
        body.put("type", "4G");
        body.put("model", "SOUNDBOX 5.0");


        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.type"), "4G");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.model"), "SOUNDBOX 5.0");
    }

    @Test(priority = 0, description = "Checking if the device is fetching correctly", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_FetchActiveQuestionAnswersTestCode() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswers"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");


        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.type"), "Android");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.model"), "A910");
    }

    @Test(priority = 0, description = "Checking if the device is fetching correctly", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_FetchActiveQuestionAnswersWithSectionList() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.type"), "Android");

        ArrayList<String> responseListOfQuestionType = respObj.jsonPath().getJsonObject("data.questions.questionType");

        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if (hset.contains("meta_charges")) {
            Assert.assertTrue(true);
        } else Assert.assertTrue(false);

    }


    @Test(priority = 0, description = "Checking if the device is fetching correctly", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_FetchActiveQuestionAnswersWithSectionList() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.type"), "Android");

        ArrayList<String> responseListOfQuestionType = respObj.jsonPath().getJsonObject("data.questions.questionType");

        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if (hset.contains("meta_charges")) {
            Assert.assertTrue(false);
        } else Assert.assertTrue(true);

    }

    @Test(priority = 0, description = "Checking if the device is fetching correctly", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_FetchActiveQuestionAnswersWithSectionList() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.type"), "Android");

        ArrayList<String> responseListOfQuestionType = respObj.jsonPath().getJsonObject("data.questions.questionType");
        System.out.println(responseListOfQuestionType);
        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if (hset.contains("accessory")) {
            Assert.assertTrue(true);
        } else Assert.assertTrue(false);

    }

    @Test(priority = 0, description = "Checking if the device is fetching correctly", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_FetchActiveQuestionAnswersWithSectionList() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.type"), "Android");

        ArrayList<String> responseListOfQuestionType = respObj.jsonPath().getJsonObject("data.questions.questionType");

        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if (hset.contains("asset_replacement_charger")) {
            Assert.assertTrue(true);
        } else Assert.assertTrue(false);

    }

    @Test(priority = 0, description = "Checking if the device is fetching correctly", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_FetchActiveQuestionAnswersWithSectionList() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.type"), "Android");

        ArrayList<String> responseListOfQuestionType = respObj.jsonPath().getJsonObject("data.questions.questionType");

        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if (hset.contains("device")) {
            if (hset.contains("keyboardDamagedCost"))
                Assert.assertTrue(true);
        } else Assert.assertTrue(false);

    }

    @Test(priority = 0, description = "Checking if the device is fetching correctly", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_FetchActiveQuestionAnswersWithSectionList() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.type"), "Android");

        ArrayList<String> responseListOfQuestionType = respObj.jsonPath().getJsonObject("data.questions.questionType");
        System.out.println(responseListOfQuestionType);
        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if (hset.contains("device")) {
            if (hset.contains("cameraLensDamagedCost"))
                Assert.assertTrue(true);
        } else Assert.assertTrue(false);

    }

    @Test(priority = 0, description = "Checking if the Reverse Logistics Cost test 1 displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Reverse Logistics Cost test 1";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

    }

    @Test(priority = 0, description = "Checking if the Screen Damaged displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Screen Damaged";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

    }

    @Test(priority = 0, description = "Checking if the Reverse Logistics Cost test 2 displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Reverse Logistics Cost test 2";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

    }

    @Test(priority = 0, description = "Checking if the Reverse Printer Working displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Printer Working";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

    }

    @Test(priority = 0, description = "Checking if the Printer Cover Damaged displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Printer Cover Damaged";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

    }

    @Test(priority = 0, description = "Checking if the Printer Roller Damaged displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_018_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Printer Roller Damaged";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 0);

    }

    @Test(priority = 0, description = "Checking if the Non-screen Body Damaged displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_019_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Non-screen Body Damaged";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

    }

    @Test(priority = 0, description = "Checking if the Camera Lens Damaged displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_020_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Camera Lens Damaged";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 0);

    }

    @Test(priority = 0, description = "Checking if the Reverse Keyboard Damaged displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_021_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Keyboard Damaged";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

    }

    @Test(priority = 0, description = "Checking if the USB Port Working displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_022_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "USB Port Working";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

    }

    @Test(priority = 0, description = "Checking if the Battery Received displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_023_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Battery Received";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

    }

    @Test(priority = 0, description = "Checking if the Data Cable Received displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_024_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Data Cable Received";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 0);

    }

    @Test(priority = 0, description = "Checking if the Adapter Received & Working displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_025_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Adapter Received & Working";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

    }

    @Test(priority = 0, description = "Checking if the Battery not available Cost displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_026_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Battery not available Cost";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

    }

    @Test(priority = 0, description = "Checking if the Battery not working Cost displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_027_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Battery not working Cost";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

    }

    @Test(priority = 0, description = "Checking if the Charger not available Cost displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_028_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Charger not available Cost";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

    }

    @Test(priority = 0, description = "Checking if the Charger not working Cost displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_029_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Charger not working Cost";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

    }

    @Test(priority = 0, description = "Checking if the Reverse Logistics Cost displayStringBilling is present or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_030_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringBilling");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringBilling");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Reverse Logistics Cost";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

    }

    @Test(priority = 0, description = "Making sure only legit questionType is present in the response", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_031_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("questionType");

        List<String> questionList = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode questionListNode = question.findValue("questionType");
            if (questionListNode != null) {
                String displayStringBilling = questionListNode.asText();
                questionList.add(displayStringBilling);
            }
        }

        Set<String> questionsSet = new HashSet<>();
        questionsSet.addAll(questionList);

        Set<String> allowedValues = new HashSet<>();
        allowedValues.add("asset_replacement_charger");
        allowedValues.add("meta_charges");
        allowedValues.add("asset_replacement_battery");
        allowedValues.add("device");
        allowedValues.add("accessory");
        boolean containsOnlyAllowedValues = questionsSet.equals(allowedValues);
        Assert.assertEquals(containsOnlyAllowedValues, true);

    }

    //this function will be used by TC 32 for the validation.
    private void validateKeyPresence(JsonNode node, String key, String message) {
        if (!node.has(key)) {
            Assert.fail("Key '" + key + "' is missing.");
        } else {
            System.out.println(message);
        }
    }

    @Test(priority = 0, description = "Making sure all the neccessary keys are present in the reponse", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_032_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "EDC");
        body.put("type", "android");
        body.put("model", "a910");
        body.put("sectionList", "device\",\"accessory\",\"asset_replacement_charger\",\"asset_replacement_battery\",\"meta_charges");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(resp);

        JsonNode dataNode = jsonNode.path("data"); // Get the "data" object

        if (dataNode.isMissingNode()) {
            Assert.fail("The 'data' key is missing.");
        } else {
            // Check for the presence of each key within the "data" object
            validateKeyPresence(dataNode, "recoveryConfigurationId", "recoveryConfigurationId is present.");
            validateKeyPresence(dataNode, "serviceItemId", "serviceItemId is present.");
            validateKeyPresence(dataNode, "serviceCategoryId", "serviceCategoryId is present.");
            validateKeyPresence(dataNode, "type", "type is present.");
            validateKeyPresence(dataNode, "model", "model is present.");

            // Assuming "questions" is an array, check its presence and elements
            JsonNode questionsNode = dataNode.path("questions");
            if (questionsNode.isMissingNode()) {
                Assert.fail("The 'questions' key is missing.");
            } else if (!questionsNode.isArray()) {
                Assert.fail("The 'questions' key should be an array.");
            } else {
                for (JsonNode question : questionsNode) {
                    // Check for the presence of keys within each question object
                    validateKeyPresence(question, "questionType", "questionType is present.");
                    validateKeyPresence(question, "displayStringPSA", "displayStringPSA is present.");
                    validateKeyPresence(question, "displayStringBilling", "displayStringBilling is present.");
                    validateKeyPresence(question, "questionKey", "questionKey is present.");

                    // Assuming "answers" is an array, check its presence and elements
                    JsonNode answersNode = question.path("answers");
                    if (answersNode.isMissingNode()) {
                        Assert.fail("The 'answers' key is missing in a question.");
                    } else if (!answersNode.isArray()) {
                        Assert.fail("The 'answers' key should be an array in a question.");
                    } else {
                        for (JsonNode answer : answersNode) {
                            // Check for the presence of keys within each answer object
                            validateKeyPresence(answer, "displayString", "displayString is present.");
                            validateKeyPresence(answer, "cost", "cost is present.");
                        }
                    }
                }
            }
        }
    }

    @Test(priority = 0, description = "Checking if serviceCategory, type, model is the same that we're sending or is it different.", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_033_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "SOUNDBOX");
        body.put("type", "2G");
        body.put("model", "Made In India Soundbox 2.0");
        body.put("sectionList", "device\",\"accessory");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode;
        jsonNode = objectMapper.readTree(resp);

        // Validate the values of serviceCategoryId, type, and model
        int serviceCategoryId = jsonNode.path("data").path("serviceCategoryId").asInt();
        String type = jsonNode.path("data").path("type").asText();
        String model = jsonNode.path("data").path("model").asText();

        Assert.assertEquals(serviceCategoryId, 2, "serviceCategoryId should be 2.");
        Assert.assertEquals(type, "2G", "type should be 2g.");
        Assert.assertEquals(model, "Made In India Soundbox 2.0", "model should be Made In India Soundbox 2.0.");

    }
    @Test(priority = 0, description = "Checking if serviceCategory, type, model is the same that we're sending or is it different. ", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_034_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "SOUNDBOX");
        body.put("type", "4G");
        body.put("model", "SOUNDBOX 5.0");
        body.put("sectionList", "device\",\"accessory");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode;
        jsonNode = objectMapper.readTree(resp);

        // Validate the values of serviceCategoryId, type, and model
        int serviceCategoryId = jsonNode.path("data").path("serviceCategoryId").asInt();
        String type = jsonNode.path("data").path("type").asText();
        String model = jsonNode.path("data").path("model").asText();

        Assert.assertEquals(serviceCategoryId, 2, "serviceCategoryId should be 2.");
        Assert.assertEquals(type, "4G", "type should be 4G.");
        Assert.assertEquals(model, "SOUNDBOX 5.0", "model should be SOUNDBOX 5.0.");
    }

    @Test(priority = 0, description = "Checking if API is able to fetch Active Question Answer For Tap And Pay - Checking the status code - 200", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_035_FetchActiveQuestionAnswersForTapAndPay() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswers"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "TAPANDPAY");
        body.put("type", "4G");
        body.put("model", "Soundbox 8 - D135");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        int httpCode = respObj.statusCode();

        Assert.assertTrue(httpCode == 200, "TestcasePass");
    }

    @Test(priority = 0, description = "Checking if API is able to fetch Active Question Answer For Tap and pay - Checking the Result message", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_036_FetchActiveQuestionAnswersForTapAndPay() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswers"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "TAPANDPAY");
        body.put("type", "4G");
        body.put("model", "Soundbox 8 - D135");


        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        int httpCode = respObj.statusCode();

        Assert.assertTrue(httpCode == 200, "TestcasePass");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("resultInfo.resultCode"), "SUCCESS");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("resultInfo.resultStatus"), "S");
    }

    @Test(priority = 0, description = "Checking if API is able to fetch Active Question Answer For Tap and pay - Checking the Device type in response", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_037_FetchActiveQuestionAnswersForTapAndPay() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswers"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "TAPANDPAY");
        body.put("type", "4G");
        body.put("model", "Soundbox 8 - D135");


        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        int httpCode = respObj.statusCode();

        Assert.assertTrue(httpCode == 200, "TestcasePass");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.type"), "4G");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.serviceCategoryId").toString(), "3");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.serviceCategory"), "TAPANDPAY");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.model"), "SOUNDBOX 8 - D135");

    }

    @Test(priority = 0, description = "Checking API response for Incorrect model for Tap and Pay", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_038_FetchActiveQuestionAnswersForTapAndPay() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswers"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "TAPANDPAY");
        body.put("type", "4G");
        body.put("model", "Soundbox 5.0");


        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        int httpCode = respObj.statusCode();
        Assert.assertTrue(httpCode == 400, "TestcasePass");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("resultInfo.resultStatus"), "F");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("resultInfo.resultCode"), "INVALID_INPUT");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("resultInfo.resultCodeId"), "400");

    }

    @Test(priority = 0, description = "Tap and Pay , Checking the api response if section list if passed", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_039_FetchActiveQuestionAnswersForTapAndPay() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "TAPANDPAY");
        body.put("type", "4G");
        body.put("model", "Soundbox 8 - D135");
        body.put("sectionList", "device\",\"accessory\",\"standalone_asset_replacement_device_return_charger\",\"device_replacement_upgrade_charger");


        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        int httpCode = respObj.statusCode();

        Assert.assertTrue(httpCode == 200, "TestcasePass");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.type"), "4G");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.serviceCategoryId").toString(), "3");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.serviceCategory"), "TAPANDPAY");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.model"), "SOUNDBOX 8 - D135");

    }

    @Test(priority = 0, description = "Tap and Pay , Checking the sections in api response", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_040_FetchActiveQuestionAnswersForTapAndPay() {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "TAPANDPAY");
        body.put("type", "4G");
        body.put("model", "Soundbox 8 - D135");
        body.put("sectionList", "device\",\"accessory\",\"standalone_asset_replacement_device_return_charger\",\"device_replacement_upgrade_charger");


        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);

        int httpCode = respObj.statusCode();

        ArrayList<String> responseListOfQuestionType = respObj.jsonPath().getJsonObject("data.questions.questionType");

        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if (hset.contains("device")) {
            Assert.assertTrue(true);
        }
        else
            Assert.assertTrue(false);

        if (hset.contains("accessory")) {
            Assert.assertTrue(true);
        }
        else
           // Assert.assertTrue(false);

        if (hset.contains("standalone_asset_replacement_device_return_charger")) {
            Assert.assertTrue(true);
        }
        else
            Assert.assertTrue(false);

        if (hset.contains("device_replacement_upgrade_charger")) {
            Assert.assertTrue(true);
        }
        else
            Assert.assertTrue(false);


        Assert.assertTrue(httpCode == 200, "TestcasePass");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.type"), "4G");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.serviceCategoryId").toString(), "3");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.serviceCategory"), "TAPANDPAY");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("data.model"), "SOUNDBOX 8 - D135");

    }

    @Test(priority = 0, description = "Tap And Pay , Checking displayStringPSA response ", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_041_FetchActiveQuestionAnswersWithSectionList() throws JsonProcessingException {
        FetchActiveQuestionAnswers FetchActiveQuestionAnswers_Obj = new FetchActiveQuestionAnswers(P.TESTDATA.get("OdsFetchActiveQuestionAnswersWithSectionList"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "TAPANDPAY");
        body.put("type", "4G");
        body.put("model", "Soundbox 8 - D135");
        body.put("sectionList", "device\",\"accessory\",\"standalone_asset_replacement_device_return_charger\",\"device_replacement_upgrade_charger");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchActiveQuestionAnswers(FetchActiveQuestionAnswers_Obj, body, headers);


        String resp = respObj.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> questionType = jsonNode.findParents("displayStringPSA");

        List<String> displayStrings = new ArrayList<>();

        for (JsonNode question : questionType) {
            JsonNode displayStringBillingNode = question.findValue("displayStringPSA");
            if (displayStringBillingNode != null) {
                String displayStringBilling = displayStringBillingNode.asText();
                displayStrings.add(displayStringBilling);
            }
        }

        int flag = 0;
        String toSearch = "Is Merchant returning the old charger?";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

        flag = 0;
        toSearch = "Charging port damaged?";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

        flag = 0;
        toSearch = "Does the Merchant need new charger?";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

        flag = 0;
        toSearch = "Buttons damaged?";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

        flag = 0;
        toSearch = "Device damaged?";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 1);

        flag = 0;
        toSearch = "Charger received?";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 0);

        flag = 0;
        toSearch = "Cable received?";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 0);

        flag = 0;
        toSearch = "Battery received?";
        for (String displayStringBilling : displayStrings) {
            if (displayStringBilling.equals(toSearch)) {
                flag = 1;
                break;
            }
        }
        Assert.assertEquals(flag, 0);

    }

}