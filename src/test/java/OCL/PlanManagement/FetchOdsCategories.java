package OCL.PlanManagement;

import Request.ODS.v1.OdsCategories;
import Services.ODS.OdsMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class FetchOdsCategories extends BaseMethod {

	OdsMiddlewareServices odsMiddlewareServicesObject = new OdsMiddlewareServices();

	private static final Logger LOGGER = LogManager.getLogger(FetchOdsCategories.class);

	public static String clientId = "OE";
	public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
	String encodedXMWExToken = findXMWExTokenforPanel("5123456788","paytm@123");


	@Test(priority = 0, description = "Fetch Ods Categories Response", groups = {
			"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_001_FetchOdsCategoriesStatusCode() {
		OdsCategories odsCategories = new OdsCategories(P.TESTDATA.get("OdsCategoriesPath"));
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

		Response respObj = odsMiddlewareServicesObject.odsfetchV1CategoriesMethod(odsCategories, headers);

		LOGGER.info("Fetch Ods Categories Response" + respObj.statusCode());

		Assert.assertEquals(respObj.statusCode(), 200);
	}

	@Test(priority = 0, description = "Fetch Ods Categories With Wrong Token", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_002_FetchOdsCategoriesWithWrongToken() {
		OdsCategories odsCategories = new OdsCategories(P.TESTDATA.get("OdsCategoriesPath"));
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX","aaaa");
		Response respObj = null;
		try {
			respObj = odsMiddlewareServicesObject.odsfetchV1CategoriesMethod(odsCategories, headers);

		} catch (PatternSyntaxException e) {

		}
		if (respObj != null) {
			LOGGER.info("Fetch Ods Categories Response" + respObj.statusCode());
			Assert.assertEquals(respObj.statusCode(), 401);
		}
	}
	
	
	@Test(priority = 0, description = "Fetch Ods Categories Check Result Code Success", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_003_FetchOdsCategoriesCheckResultMsgSuccess() throws ParseException {
		OdsCategories odsCategories = new OdsCategories(P.TESTDATA.get("OdsCategoriesPath"));
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		
		Response respObj = odsMiddlewareServicesObject.odsfetchV1CategoriesMethod(odsCategories, headers);
		LOGGER.info("Fetch Ods Categories Check Result Code Success Response" + respObj.statusCode());
		
		
		respObj.getBody();
		
		JSONParser parser = new JSONParser();  
		
		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString()); 
		
		JSONObject jsObj2=(JSONObject) jsObj1.get("resultInfo");
		
		LOGGER.info("Fetch Ods Categories Check Result Code Success Response" + respObj.statusCode());
		
		Assert.assertEquals(jsObj2.get("resultCode"),"SUCCESS");
	
	}
	@Test(priority = 0, description = "Fetch Ods Categories Check Data Value To Be EDC", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_004_FetchOdsCategoriesCheckDataValueToBeEDC() throws ParseException {
		OdsCategories odsCategories = new OdsCategories(P.TESTDATA.get("OdsCategoriesPath"));
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		
		Response respObj = odsMiddlewareServicesObject.odsfetchV1CategoriesMethod(odsCategories, headers);
		LOGGER.info("Fetch Ods Categories Check Data Value To Be EDC" + respObj.statusCode());
		
		
		respObj.getBody();
		
		JSONParser parser = new JSONParser();  
		
		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString()); 
		
		JSONArray jsObj2=(JSONArray) jsObj1.get("data");
	
		JSONObject jsObj3=(JSONObject) jsObj2.get(0);
		
		LOGGER.info("Fetch Ods Categories Check Data Value To Be EDC" + respObj.statusCode());
		
		Assert.assertEquals(jsObj3.get("value"),"EDC");
	
	}
	@Test(priority = 0, description = "Fetch Ods Categories Check Data Key", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_005_FetchOdsCategoriesCheckDataKey() throws ParseException {
		OdsCategories odsCategories = new OdsCategories(P.TESTDATA.get("OdsCategoriesPath"));
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		
		Response respObj = odsMiddlewareServicesObject.odsfetchV1CategoriesMethod(odsCategories, headers);
		LOGGER.info("Fetch Ods Categories Check Data Key" + respObj.statusCode());
		
		
		respObj.getBody();
		
		JSONParser parser = new JSONParser();  
		
		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString()); 
		
		JSONArray jsObj2=(JSONArray) jsObj1.get("data");
	
		JSONObject jsObj3=(JSONObject) jsObj2.get(0);
		
		LOGGER.info("Fetch Ods Categories Check Data Key" + respObj.statusCode());
		
		int key=Integer.parseInt(jsObj3.get("key").toString());
		Assert.assertEquals(key,1);
	
	}
	
	

}
