package OCL.PlanManagement;

import Request.ODS.PlanDifferenceAPI;
import Services.ODS.OdsMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.util.ArrayList;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;

import static io.restassured.RestAssured.given;

public class PlanDifferenceTest extends BaseMethod {
    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";

    String encodedXMWExToken = findXMWExTokenforPanel("7771216290","paytm@123");

    @Test(priority = 0, description = "", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_PlanDifferenceTest() {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13046");
        queryParams.put("planId2", "13040");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);

        int httpCode = respObj.statusCode();
        Assert.assertTrue(httpCode==200, "TestcasePass");
    }

    @Test(priority = 0, description = "", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_PlanDifferenceTest() {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13186");
        queryParams.put("planId2", "13185");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);

        String jsonResponse = respObj.getBody().asString();
        JsonPath jsonPath = new JsonPath(jsonResponse);

        Assert.assertTrue(jsonPath.get("data.planName") != null);
    }

    @Test(priority = 0, description = "", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_PlanDifferenceTest() {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "1");
        queryParams.put("planId2", "2");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);

        int httpCode = respObj.statusCode();
        Assert.assertTrue(httpCode==400, "TestcasePass");
    }

    @Test(priority = 0, description = "", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_PlanDifferenceTest() {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13186");
        queryParams.put("planId2", "13185");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);

        String jsonResponse = respObj.getBody().asString();
        JsonPath jsonPath = new JsonPath(jsonResponse);

        Assert.assertTrue(jsonPath.get("data.planName.reason") != null);
    }

    @Test(priority = 0, description = "", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_PlanDifferenceTest() {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13186");
        queryParams.put("planId2", "13185");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);

        String jsonResponse = respObj.getBody().asString();
        JsonPath jsonPath = new JsonPath(jsonResponse);

        Assert.assertTrue(jsonPath.get("data.planName.value1") != null);
    }

    @Test(priority = 0, description = "", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_PlanDifferenceTest() {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13186");
        queryParams.put("planId2", "13185");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);

        String jsonResponse = respObj.getBody().asString();
        JsonPath jsonPath = new JsonPath(jsonResponse);

        Assert.assertTrue(jsonPath.get("data.planName.value2") != null);
    }

    @Test(priority = 0, description = "", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_PlanDifferenceTest() {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13186");
        queryParams.put("planId2", "13185");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);

        String jsonResponse = respObj.getBody().asString();
        JsonPath jsonPath = new JsonPath(jsonResponse);

        Assert.assertTrue(jsonPath.get("data.planName.value2") != null);
    }

    @Test(priority = 0, description = "", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_PlanDifferenceTest() throws JsonProcessingException{
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13186");
        queryParams.put("planId2", "13185");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);
        String jsonResponse = respObj.getBody().asString();

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(jsonResponse);

        String resultStatus = jsonNode.get("resultInfo").get("resultStatus").asText();

        Assert.assertEquals("S", resultStatus);
    }
    @Test(priority = 0, description = "", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_PlanDifferenceTest() throws JsonProcessingException{
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13186");
        queryParams.put("planId2", "13185");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);
        String jsonResponse = respObj.getBody().asString();

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(jsonResponse);

        String resultCode = jsonNode.get("resultInfo").get("resultCode").asText();

        Assert.assertEquals("SUCCESS", resultCode);
    }
    @Test(priority = 0, description = "", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_PlanDifferenceTest() throws JsonProcessingException{
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13186");
        queryParams.put("planId2", "13185");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);
        String jsonResponse = respObj.getBody().asString();

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(jsonResponse);

        String resultCodeId = jsonNode.get("resultInfo").get("resultCodeId").asText();

        Assert.assertEquals("01", resultCodeId);
    }

    @Test(priority = 0, description = "", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_PlanDifferenceTest() {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13669");
        queryParams.put("planId2", "13670");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);

        int httpCode = respObj.statusCode();
        Assert.assertTrue(httpCode==200, "TestcasePass");
    }
    @Test(priority = 0, description = "Checking if all the essential keys are present in the EDC - Onboarding Plan Difference", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_PlanDifferenceTest() throws JsonProcessingException {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13663");
        queryParams.put("planId2", "13662");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);
        String jsonResponse = respObj.getBody().asString();

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonResponse);

        boolean hasPlanName = root.path("data").has("planName");
        boolean hasFseAndMerchantDetailSRO = root.path("data").has("fseAndMerchantDetailSRO");
        boolean hasPricingComponents = root.path("data").has("pricingComponents");
        boolean hasMdrDetailSROList = root.path("data").has("mdrDetailSROList");
        boolean hasAdditionalInfo = root.path("data").has("additionalInfo");

        Assert.assertEquals(true, hasPlanName);
        Assert.assertEquals(true, hasFseAndMerchantDetailSRO);
        Assert.assertEquals(true, hasPricingComponents);
        Assert.assertEquals(true, hasMdrDetailSROList);
        Assert.assertEquals(true, hasAdditionalInfo);
    }

    @Test(priority = 0, description = "Checking if all the essential keys are present in the EDC - Plan Upgrade Plan Difference", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014_PlanDifferenceTest() throws JsonProcessingException {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13664");
        queryParams.put("planId2", "13674");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);
        String jsonResponse = respObj.getBody().asString();

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonResponse);

        boolean hasPlanName = root.path("data").has("planName");
        boolean hasFseAndMerchantDetailSRO = root.path("data").has("fseAndMerchantDetailSRO");
        boolean hasPricingComponents = root.path("data").has("pricingComponents");
        boolean hasMdrDetailSROList = root.path("data").has("mdrDetailSROList");
        boolean hasAdditionalInfo = root.path("data").has("additionalInfo");

        Assert.assertEquals(true, hasPlanName);
        Assert.assertEquals(true, hasFseAndMerchantDetailSRO);
        Assert.assertEquals(true, hasPricingComponents);
        Assert.assertEquals(true, hasMdrDetailSROList);
        Assert.assertEquals(true, hasAdditionalInfo);
    }

        @Test(priority = 0, description = "Checking if all the essential keys are present in the EDC - Device Upgrade Plan Difference", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015_PlanDifferenceTest() throws JsonProcessingException {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13665");
        queryParams.put("planId2", "13647");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);
        String jsonResponse = respObj.getBody().asString();

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonResponse);

        boolean hasPlanName = root.path("data").has("planName");
        boolean hasFseAndMerchantDetailSRO = root.path("data").has("fseAndMerchantDetailSRO");
        boolean hasPricingComponents = root.path("data").has("pricingComponents");
        boolean hasMdrDetailSROList = root.path("data").has("mdrDetailSROList");
        boolean hasAdditionalInfo = root.path("data").has("additionalInfo");

        Assert.assertEquals(true, hasPlanName);
        Assert.assertEquals(true, hasFseAndMerchantDetailSRO);
        Assert.assertEquals(true, hasPricingComponents);
        Assert.assertEquals(false, hasMdrDetailSROList);
        Assert.assertEquals(true, hasAdditionalInfo);
    }
    @Test(priority = 0, description = "Checking if all the essential keys are present in the EDC - Device Stand Plan Difference", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016_PlanDifferenceTest() throws JsonProcessingException {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13600");
        queryParams.put("planId2", "13585");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);
        String jsonResponse = respObj.getBody().asString();

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonResponse);

        boolean hasPlanName = root.path("data").has("planName");
        boolean hasFseAndMerchantDetailSRO = root.path("data").has("fseAndMerchantDetailSRO");
        boolean hasPricingComponents = root.path("data").has("pricingComponents");
        boolean hasMdrDetailSROList = root.path("data").has("mdrDetailSROList");
        boolean hasAdditionalInfo = root.path("data").has("additionalInfo");

        Assert.assertEquals(true, hasPlanName);
        Assert.assertEquals(true, hasFseAndMerchantDetailSRO);
        Assert.assertEquals(true, hasPricingComponents);
        Assert.assertEquals(false, hasMdrDetailSROList);
        Assert.assertEquals(true, hasAdditionalInfo);
    }
    @Test(priority = 0, description = "Checking if all the essential keys are present in the EDC - Brand EMI Plan Difference", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017_PlanDifferenceTest() throws JsonProcessingException {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13666");
        queryParams.put("planId2", "13648");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);
        String jsonResponse = respObj.getBody().asString();

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonResponse);

        boolean hasPlanName = root.path("data").has("planName");
        boolean hasFseAndMerchantDetailSRO = root.path("data").has("fseAndMerchantDetailSRO");
        boolean hasPricingComponents = root.path("data").has("pricingComponents");
        boolean hasMdrDetailSROList = root.path("data").has("mdrDetailSROList");
        boolean hasAdditionalInfo = root.path("data").has("additionalInfo");

        Assert.assertEquals(true, hasPlanName);
        Assert.assertEquals(true, hasFseAndMerchantDetailSRO);
        Assert.assertEquals(false, hasPricingComponents);
        Assert.assertEquals(true, hasMdrDetailSROList);
        Assert.assertEquals(true, hasAdditionalInfo);
    }

        @Test(priority = 0, description = "Checking if all the essential keys are present in the SB - Onboarding Plan Difference", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_018_PlanDifferenceTest() throws JsonProcessingException {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13652");
        queryParams.put("planId2", "13562");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);
        String jsonResponse = respObj.getBody().asString();

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonResponse);

        boolean hasPlanName = root.path("data").has("planName");
        boolean hasFseAndMerchantDetailSRO = root.path("data").has("fseAndMerchantDetailSRO");
        boolean hasPricingComponents = root.path("data").has("pricingComponents");
        boolean hasMdrDetailSROList = root.path("data").has("mdrDetailSROList");
        boolean hasAdditionalInfo = root.path("data").has("additionalInfo");

        Assert.assertEquals(true, hasPlanName);
        Assert.assertEquals(true, hasFseAndMerchantDetailSRO);
        Assert.assertEquals(true, hasPricingComponents);
        Assert.assertEquals(false, hasMdrDetailSROList);
        Assert.assertEquals(true, hasAdditionalInfo);
    }

    @Test(priority = 0, description = "Checking if all the essential keys are present in the SB - Device Upgrade Plan Difference", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_019_PlanDifferenceTest() throws JsonProcessingException {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13667");
        queryParams.put("planId2", "13654");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);
        String jsonResponse = respObj.getBody().asString();

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonResponse);

        boolean hasPlanName = root.path("data").has("planName");
        boolean hasFseAndMerchantDetailSRO = root.path("data").has("fseAndMerchantDetailSRO");
        boolean hasPricingComponents = root.path("data").has("pricingComponents");
        boolean hasMdrDetailSROList = root.path("data").has("mdrDetailSROList");
        boolean hasAdditionalInfo = root.path("data").has("additionalInfo");

        Assert.assertEquals(true, hasPlanName);
        Assert.assertEquals(true, hasFseAndMerchantDetailSRO);
        Assert.assertEquals(true, hasPricingComponents);
        Assert.assertEquals(false, hasMdrDetailSROList);
        Assert.assertEquals(true, hasAdditionalInfo);
    }
    @Test(priority = 0, description = "Checking if all the essential keys are present in the SB - Paid Replacement Plan Difference", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_020_PlanDifferenceTest() throws JsonProcessingException {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13354");
        queryParams.put("planId2", "13279");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);
        String jsonResponse = respObj.getBody().asString();

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonResponse);

        boolean hasPlanName = root.path("data").has("planName");
        boolean hasFseAndMerchantDetailSRO = root.path("data").has("fseAndMerchantDetailSRO");
        boolean hasPricingComponents = root.path("data").has("pricingComponents");
        boolean hasMdrDetailSROList = root.path("data").has("mdrDetailSROList");
        boolean hasAdditionalInfo = root.path("data").has("additionalInfo");

        Assert.assertEquals(true, hasPlanName);
        Assert.assertEquals(true, hasFseAndMerchantDetailSRO);
        Assert.assertEquals(true, hasPricingComponents);
        Assert.assertEquals(false, hasMdrDetailSROList);
        Assert.assertEquals(true, hasAdditionalInfo);
    }

    @Test(priority = 0, description = "Checking if all the essential keys are present in the SB - Premium Care Plan Difference", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_021_PlanDifferenceTest() throws JsonProcessingException {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13386");
        queryParams.put("planId2", "13373");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);
        String jsonResponse = respObj.getBody().asString();

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonResponse);

        boolean hasPlanName = root.path("data").has("planName");
        boolean hasFseAndMerchantDetailSRO = root.path("data").has("fseAndMerchantDetailSRO");
        boolean hasPricingComponents = root.path("data").has("pricingComponents");
        boolean hasMdrDetailSROList = root.path("data").has("mdrDetailSROList");
        boolean hasAdditionalInfo = root.path("data").has("additionalInfo");

        Assert.assertEquals(true, hasPlanName);
        Assert.assertEquals(true, hasFseAndMerchantDetailSRO);
        Assert.assertEquals(true, hasPricingComponents);
        Assert.assertEquals(false, hasMdrDetailSROList);
        Assert.assertEquals(true, hasAdditionalInfo);
    }

    @Test(priority = 0, description = "Checking if all the essential keys are present in the SB - Shop Insurance Plan Difference", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_022_PlanDifferenceTest() throws JsonProcessingException {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13453");
        queryParams.put("planId2", "13457");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);
        String jsonResponse = respObj.getBody().asString();

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonResponse);

        boolean hasPlanName = root.path("data").has("planName");
        boolean hasFseAndMerchantDetailSRO = root.path("data").has("fseAndMerchantDetailSRO");
        boolean hasPricingComponents = root.path("data").has("pricingComponents");
        boolean hasMdrDetailSROList = root.path("data").has("mdrDetailSROList");
        boolean hasAdditionalInfo = root.path("data").has("additionalInfo");

        Assert.assertEquals(true, hasPlanName);
        Assert.assertEquals(true, hasFseAndMerchantDetailSRO);
        Assert.assertEquals(true, hasPricingComponents);
        Assert.assertEquals(false, hasMdrDetailSROList);
        Assert.assertEquals(true, hasAdditionalInfo);
    }

    @Test(priority = 0, description = "Checking if all the essential keys are present in the Card Soundbox - Onboarding Plan Difference", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_023_PlanDifferenceTest() throws JsonProcessingException {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13659");
        queryParams.put("planId2", "13377");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);
        String jsonResponse = respObj.getBody().asString();

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonResponse);

        boolean hasPlanName = root.path("data").has("planName");
        boolean hasFseAndMerchantDetailSRO = root.path("data").has("fseAndMerchantDetailSRO");
        boolean hasPricingComponents = root.path("data").has("pricingComponents");
        boolean hasMdrDetailSROList = root.path("data").has("mdrDetailSROList");
        boolean hasAdditionalInfo = root.path("data").has("additionalInfo");

        Assert.assertEquals(true, hasPlanName);
        Assert.assertEquals(true, hasFseAndMerchantDetailSRO);
        Assert.assertEquals(true, hasPricingComponents);
        Assert.assertEquals(true, hasMdrDetailSROList);
        Assert.assertEquals(true, hasAdditionalInfo);
    }

    @Test(priority = 0, description = "Checking if the 2 different types of plans comparison giving error or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_024_PlanDifferenceTest() {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13667"); // Soundbox
        queryParams.put("planId2", "13377"); // Card Soundbox

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);

        int httpCode = respObj.statusCode();
        Assert.assertTrue(httpCode==400, "TestcasePass");
    }

    @Test(priority = 0, description = "Checking if the 2 different types of plans comparison giving error or not", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_025_PlanDifferenceTest() {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13674"); // EDC
        queryParams.put("planId2", "13377"); // Card Soundbox

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);

        int httpCode = respObj.statusCode();
        Assert.assertTrue(httpCode==400, "TestcasePass");
    }

    @Test(priority = 0, description = "Checking if all the keys are present in fse and merchant details", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_026_PlanDifferenceTest() throws JsonProcessingException {
        PlanDifferenceAPI Obj = new PlanDifferenceAPI(P.TESTDATA.get("FetchPlanDifferenceAPIRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        queryParams.put("planId1", "13665"); // EDC
        queryParams.put("planId2", "13675"); // Card Soundbox

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.planDifference(Obj, queryParams, headers);
        String jsonResponse = respObj.getBody().asString();

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonResponse);


        Assert.assertTrue(root.path("data").path("fseAndMerchantDetailSRO").has("genericRuleDetailsSRO"));
        Assert.assertTrue(root.path("data").path("fseAndMerchantDetailSRO").path("genericRuleDetailsSRO").has("bankAccountType"));
        Assert.assertTrue(root.path("data").path("fseAndMerchantDetailSRO").path("genericRuleDetailsSRO").has("merchantAgeType"));
        Assert.assertTrue(root.path("data").path("fseAndMerchantDetailSRO").path("genericRuleDetailsSRO").has("ppiLimit"));
        Assert.assertTrue(root.path("data").path("fseAndMerchantDetailSRO").path("genericRuleDetailsSRO").has("businessWallet"));
        Assert.assertTrue(root.path("data").path("fseAndMerchantDetailSRO").path("genericRuleDetailsSRO").has("gstIn"));
        Assert.assertTrue(root.path("data").path("fseAndMerchantDetailSRO").path("genericRuleDetailsSRO").has("category"));
        Assert.assertTrue(root.path("data").path("fseAndMerchantDetailSRO").path("genericRuleDetailsSRO").has("merchantType"));
        Assert.assertTrue(root.path("data").path("fseAndMerchantDetailSRO").path("genericRuleDetailsSRO").has("delayedSettlement"));

    }
}