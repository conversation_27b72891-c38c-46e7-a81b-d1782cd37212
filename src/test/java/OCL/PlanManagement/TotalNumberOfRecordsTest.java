package OCL.PlanManagement;

import Request.ODS.TotalNumberOfRecords;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;
import com.paytm.apitools.util.annotations.Owner;
import Services.ODS.OdsMiddlewareServices;
import java.util.HashMap;
import java.util.Map;
import io.restassured.response.Response;

public class TotalNumberOfRecordsTest extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(FetchDeviceDetailsAPITest.class);

    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
    String encodedXMWExToken = findXMWExTokenforPanel("5123456788","paytm@123");

        @Test(priority = 0, description = "", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_TotalNumberOfRecordsTestCode() {
            TotalNumberOfRecords TotalNumberOfRecords_Obj = new TotalNumberOfRecords(P.TESTDATA.get("OdsTotalNumberOfRecords"));
            Map<String, String> headers = new HashMap<String, String>();
            headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
            headers.put("Content-Type", "application/json");

            Map<String, String> queryParams = new HashMap<String, String>();

            queryParams.put("serviceCategory", "1");
            queryParams.put("creatorMobileNumber", "8884211099");

            OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

            Response respObj = odsMiddlewareServices.totalNumberOfRecords(TotalNumberOfRecords_Obj, queryParams, headers);

            int httpCode = respObj.statusCode();
            Assert.assertTrue(httpCode==200, "TestcasePass");
        }

    @Test(priority = 0, description = "", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_TotalNumberOfRecordsTestCode() {
        TotalNumberOfRecords TotalNumberOfRecords_Obj = new TotalNumberOfRecords(P.TESTDATA.get("OdsTotalNumberOfRecords"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
        headers.put("Content-Type", "application/json");

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("serviceCategory", "1");
        queryParams.put("creatorMobileNumber", "8884211099");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.totalNumberOfRecords(TotalNumberOfRecords_Obj, queryParams, headers);

        int httpCode = respObj.statusCode();
        Assert.assertFalse(httpCode==400, "TestcasePass");
    }

    @Test(priority = 0, description = "", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_TotalNumberOfRecordsTestCode() {
        TotalNumberOfRecords TotalNumberOfRecords_Obj = new TotalNumberOfRecords(P.TESTDATA.get("OdsTotalNumberOfRecords"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
        headers.put("Content-Type", "application/json");

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("serviceCategory", "1");
        queryParams.put("creatorMobileNumber", "8884211099");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.totalNumberOfRecords(TotalNumberOfRecords_Obj, queryParams, headers);

         Assert.assertEquals(respObj.jsonPath().getJsonObject("resultInfo.resultCode"),"SUCCESS");
    }

    @Test(priority = 0, description = "", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_TotalNumberOfRecordsTestCode() {
        TotalNumberOfRecords TotalNumberOfRecords_Obj = new TotalNumberOfRecords(P.TESTDATA.get("OdsTotalNumberOfRecords"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
        headers.put("Content-Type", "application/json");

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("serviceCategory", "1");
        queryParams.put("creatorMobileNumber", "8884211099");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.totalNumberOfRecords(TotalNumberOfRecords_Obj, queryParams, headers);

        Assert.assertEquals(respObj.jsonPath().getJsonObject("resultInfo.resultStatus"),"S");
    }

    @Test(priority = 0, description = "", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_TotalNumberOfRecordsTestCode() {
        TotalNumberOfRecords TotalNumberOfRecords_Obj = new TotalNumberOfRecords(P.TESTDATA.get("OdsTotalNumberOfRecords"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
        headers.put("Content-Type", "application/json");

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("serviceCategory", "1");
        queryParams.put("creatorMobileNumber", "8884211099");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.totalNumberOfRecords(TotalNumberOfRecords_Obj, queryParams, headers);
        Assert.assertNotNull(respObj.jsonPath().getJsonObject("data").toString());
    }
}
