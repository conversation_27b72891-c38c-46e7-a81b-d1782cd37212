package OCL.PlanManagement;


import Request.ODS.CreatePlanODS;
import Services.ODS.ODSPlan;
import Services.Utilities.Utilities;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONObject;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CreatePlanODSTest extends BaseMethod
{
    ODSPlan ODSPlanObject=new ODSPlan();
    private static final Logger LOGGER = LogManager.getLogger(CreatePlanODSTest.class);

    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
    String encodedXMWExToken = findXMWExTokenforPanel("5123456788","paytm@123");

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Create Plan ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_ODSCreatePlan() {

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanBasic"));

        String planName =  "Automation"+Long.toString(Utilities.generateRandom(6));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "1");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("subServiceCategoryId", "10002");
        body.put("planType", "onboarding");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Create Plan with invalid service category")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_ODSCreatePlan() {

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanBasic"));

        String planName =  "Plan23"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "165");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("subServiceCategoryId", "10002");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Create Plan with invalid sub service category")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_ODSCreatePlan() {

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanBasic"));

        String planName =  "AutomationPlangdjd"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "1");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("subServiceCategoryId", "512312");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Create Plan with invalid plan name")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_ODSCreatePlan() {

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanBasic"));

        String planName =  "AutomationPlansaaa"+Long.toString(Utilities.generateRandom(4))+"@!";

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "1");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("subServiceCategoryId", "10002");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    //@Test(priority = 0, groups = {"Regression"}, description = "ODS Create Plan without mandatory permission")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanBasic"));

        String planName =  "AutomationPlanssef3"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "1");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("subServiceCategoryId", "10002");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "Replicate Feature For EDC Onboarding Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanWithReplicate"));

        String planName =  "AutomationPlanmdmwl"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "1");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("planType", "onboarding");
        body.put("subServiceCategoryId", "10001");
        body.put("replicateFor", "10002");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.planName"));
        Assert.assertEquals("onboarding", CreatePlanObjResponse.jsonPath().getString("data.planType"));
        Assert.assertEquals("1", CreatePlanObjResponse.jsonPath().getString("data.serviceCategoryId"));
        Assert.assertEquals("DRAFT", CreatePlanObjResponse.jsonPath().getString("data.planStatus"));
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.alias"));
        Assert.assertEquals("10001", CreatePlanObjResponse.jsonPath().getString("data.subServiceCategoryId"));
    }


    @Test(priority = 0, groups = {"Regression"}, description = "Replicate Feature For EDC Onboarding Plan With replicate for two subtypes")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanBasicNew"));

        String planName =  "AutomationPlan83oie"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "1");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("planType", "onboarding");
        body.put("subServiceCategoryId", "10001");
      /*  List<String> replicateForList = new ArrayList<>();
        replicateForList.add("10002");
        replicateForList.add("10003");

        body.put("replicateFor", replicateForList); */

        ObjectMapper mapper = new ObjectMapper();
        try {
            String jsonString = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(body);
            System.out.println(jsonString);
        } catch (Exception e) {
            e.printStackTrace();
        }


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.planName"));
        Assert.assertEquals("onboarding", CreatePlanObjResponse.jsonPath().getString("data.planType"));
        Assert.assertEquals("1", CreatePlanObjResponse.jsonPath().getString("data.serviceCategoryId"));
        Assert.assertEquals("DRAFT", CreatePlanObjResponse.jsonPath().getString("data.planStatus"));
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.alias"));
        Assert.assertEquals("10001", CreatePlanObjResponse.jsonPath().getString("data.subServiceCategoryId"));
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Replicate Feature For EDC Onboarding Plan With replicate for two subtypes")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanBasicNew"));

        String planName =  "AutomationPlan9302n"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "1");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("planType", "onboarding");
        body.put("subServiceCategoryId", "10001");
        body.put("replicateFor", "10002,10003");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.planName"));
        Assert.assertEquals("onboarding", CreatePlanObjResponse.jsonPath().getString("data.planType"));
        Assert.assertEquals("1", CreatePlanObjResponse.jsonPath().getString("data.serviceCategoryId"));
        Assert.assertEquals("DRAFT", CreatePlanObjResponse.jsonPath().getString("data.planStatus"));
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.alias"));
        Assert.assertEquals("10001", CreatePlanObjResponse.jsonPath().getString("data.subServiceCategoryId"));
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Replicate Feature For EDC Plan Upgrade Plan With replicate")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanWithReplicate"));

        String planName =  "AutomationPlancmo0"+Long.toString(Utilities.generateRandom(6));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "1");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("planType", "plan_upgrade");
        body.put("subServiceCategoryId", "10001");
        body.put("replicateFor", "10002");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.planName"));
        Assert.assertEquals("plan_upgrade", CreatePlanObjResponse.jsonPath().getString("data.planType"));
        Assert.assertEquals("1", CreatePlanObjResponse.jsonPath().getString("data.serviceCategoryId"));
        Assert.assertEquals("DRAFT", CreatePlanObjResponse.jsonPath().getString("data.planStatus"));
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.alias"));
        Assert.assertEquals("10001", CreatePlanObjResponse.jsonPath().getString("data.subServiceCategoryId"));
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Replicate Feature For EDC Device Upgrade Plan With replicate")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanWithReplicate"));

        String planName =  "AutomationPlan32q12"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "1");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("planType", "device_upgrade");
        body.put("subServiceCategoryId", "10001");
        body.put("replicateFor", "10002");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.planName"));
        Assert.assertEquals("device_upgrade", CreatePlanObjResponse.jsonPath().getString("data.planType"));
        Assert.assertEquals("1", CreatePlanObjResponse.jsonPath().getString("data.serviceCategoryId"));
        Assert.assertEquals("DRAFT", CreatePlanObjResponse.jsonPath().getString("data.planStatus"));
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.alias"));
        Assert.assertEquals("10001", CreatePlanObjResponse.jsonPath().getString("data.subServiceCategoryId"));
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Replicate Feature For EDC Addon Device Stand Plan With replicate")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanWithReplicate"));

        String planName =  "AutomationPlan3829"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "1");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("planType", "add_ons");
        body.put("subPlanType","accessory_device_stand");
        body.put("subServiceCategoryId", "10001");
        body.put("replicateFor", "10002");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.planName"));
      //  Assert.assertEquals("device_upgrade", CreatePlanObjResponse.jsonPath().getString("data.planType"));
        Assert.assertEquals("1", CreatePlanObjResponse.jsonPath().getString("data.serviceCategoryId"));
        Assert.assertEquals("DRAFT", CreatePlanObjResponse.jsonPath().getString("data.planStatus"));
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.alias"));
        Assert.assertEquals("10001", CreatePlanObjResponse.jsonPath().getString("data.subServiceCategoryId"));
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Replicate Feature For EDC Addon shop Insurance Plan With replicate")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanWithReplicate"));

        String planName =  "AutomationPlan333"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "1");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("subServiceCategoryId", "10001");
        body.put("replicateFor", "10002");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.planName"));
      //  Assert.assertEquals("device_upgrade", CreatePlanObjResponse.jsonPath().getString("data.planType"));
        Assert.assertEquals("1", CreatePlanObjResponse.jsonPath().getString("data.serviceCategoryId"));
        Assert.assertEquals("DRAFT", CreatePlanObjResponse.jsonPath().getString("data.planStatus"));
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.alias"));
        Assert.assertEquals("10001", CreatePlanObjResponse.jsonPath().getString("data.subServiceCategoryId"));
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Replicate Feature For EDC Addon brand Emi Plan With replicate")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanBasicNew"));

        String planName =  "AutomationPlan3dswe"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "1");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("planType", "add_ons");
        body.put("subPlanType","brand_emi");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.planName"));
       // Assert.assertEquals("device_upgrade", CreatePlanObjResponse.jsonPath().getString("data.planType"));
        Assert.assertEquals("1", CreatePlanObjResponse.jsonPath().getString("data.serviceCategoryId"));
        Assert.assertEquals("DRAFT", CreatePlanObjResponse.jsonPath().getString("data.planStatus"));
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.alias"));
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Replicate Feature For SB Onboarding Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_14_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanWithReplicate"));

        String planName =  "AutomationPlan3w21q"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "2");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("planType", "onboarding");
        body.put("subServiceCategoryId", "10010");
        body.put("replicateFor", "10012");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.planName"));
        Assert.assertEquals("onboarding", CreatePlanObjResponse.jsonPath().getString("data.planType"));
        Assert.assertEquals("2", CreatePlanObjResponse.jsonPath().getString("data.serviceCategoryId"));
        Assert.assertEquals("DRAFT", CreatePlanObjResponse.jsonPath().getString("data.planStatus"));
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.alias"));
        Assert.assertEquals("10010", CreatePlanObjResponse.jsonPath().getString("data.subServiceCategoryId"));
    }


    @Test(priority = 0, groups = {"Regression"}, description = "Replicate Feature For SB Onboarding Plan With replicate for two subtypes")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_15_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanBasicNew"));

        String planName =  "AutomationPlanuy873"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "2");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("planType", "onboarding");
        body.put("subServiceCategoryId", "10010");
        body.put("replicateFor", "10012,10013");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.planName"));
        Assert.assertEquals("onboarding", CreatePlanObjResponse.jsonPath().getString("data.planType"));
        Assert.assertEquals("2", CreatePlanObjResponse.jsonPath().getString("data.serviceCategoryId"));
        Assert.assertEquals("DRAFT", CreatePlanObjResponse.jsonPath().getString("data.planStatus"));
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.alias"));
        Assert.assertEquals("10010", CreatePlanObjResponse.jsonPath().getString("data.subServiceCategoryId"));
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Replicate Feature For SB Onboarding Plan With replicate for two subtypes")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_16_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanBasicNew"));

        String planName =  "AutomationPlan3ywu"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "2");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("planType", "onboarding");
        body.put("subServiceCategoryId", "10010");
        body.put("replicateFor", "10012,10013");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.planName"));
        Assert.assertEquals("onboarding", CreatePlanObjResponse.jsonPath().getString("data.planType"));
        Assert.assertEquals("2", CreatePlanObjResponse.jsonPath().getString("data.serviceCategoryId"));
        Assert.assertEquals("DRAFT", CreatePlanObjResponse.jsonPath().getString("data.planStatus"));
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.alias"));
        Assert.assertEquals("10010", CreatePlanObjResponse.jsonPath().getString("data.subServiceCategoryId"));
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Replicate Feature For SB Device Upgrade Plan With replicate")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_17_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanWithReplicate"));

        String planName =  "AutomationPlan3e2"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "2");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("planType", "device_upgrade");
        body.put("subServiceCategoryId", "10010");
        body.put("replicateFor", "10012");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.planName"));
        Assert.assertEquals("device_upgrade", CreatePlanObjResponse.jsonPath().getString("data.planType"));
        Assert.assertEquals("2", CreatePlanObjResponse.jsonPath().getString("data.serviceCategoryId"));
        Assert.assertEquals("DRAFT", CreatePlanObjResponse.jsonPath().getString("data.planStatus"));
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.alias"));
        Assert.assertEquals("10010", CreatePlanObjResponse.jsonPath().getString("data.subServiceCategoryId"));
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Replicate Feature For SB paid replacement Plan With replicate")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_18_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanWithReplicate"));

        String planName =  "AutomationPlan22134r"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "2");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("planType", "paid_replacement");
        body.put("subServiceCategoryId", "10010");
        body.put("replicateFor", "10012");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.planName"));
        Assert.assertEquals("paid_replacement", CreatePlanObjResponse.jsonPath().getString("data.planType"));
        Assert.assertEquals("2", CreatePlanObjResponse.jsonPath().getString("data.serviceCategoryId"));
        Assert.assertEquals("DRAFT", CreatePlanObjResponse.jsonPath().getString("data.planStatus"));
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.alias"));
        Assert.assertEquals("10010", CreatePlanObjResponse.jsonPath().getString("data.subServiceCategoryId"));
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Replicate Feature For SB Addon premium care Plan With replicate")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_19_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanWithReplicate"));

        String planName =  "AutomationPlan3swe"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "2");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("planType", "add_ons");
        body.put("subPlanType","premium_care");
        body.put("subServiceCategoryId", "10010");
        body.put("replicateFor", "10012");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.planName"));
        Assert.assertEquals("add_ons", CreatePlanObjResponse.jsonPath().getString("data.planType"));
        Assert.assertEquals("2", CreatePlanObjResponse.jsonPath().getString("data.serviceCategoryId"));
        Assert.assertEquals("DRAFT", CreatePlanObjResponse.jsonPath().getString("data.planStatus"));
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.alias"));
        Assert.assertEquals("10010", CreatePlanObjResponse.jsonPath().getString("data.subServiceCategoryId"));
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Replicate Feature For SB Addon shop Insurance Plan With replicate")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_20_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanWithReplicate"));

        String planName =  "AutomationPlanm82ju2"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "2");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("subServiceCategoryId", "10010");
        body.put("replicateFor", "10012");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.planName"));
        Assert.assertEquals("add_ons", CreatePlanObjResponse.jsonPath().getString("data.planType"));
        Assert.assertEquals("2", CreatePlanObjResponse.jsonPath().getString("data.serviceCategoryId"));
        Assert.assertEquals("DRAFT", CreatePlanObjResponse.jsonPath().getString("data.planStatus"));
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.alias"));
        Assert.assertEquals("10010", CreatePlanObjResponse.jsonPath().getString("data.subServiceCategoryId"));
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Replicate Feature For SB Addon accessory charger Plan With replicate")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_21_ODSCreatePlan() {

        String encodedXMWExToken = findXMWExTokenforPanel("9654279917","paytm@123");

        CreatePlanODS odsObj=new CreatePlanODS(P.TESTDATA.get("CreatePlanWithReplicate"));

        String planName =  "AutomationPlan32bf"+Long.toString(Utilities.generateRandom(4));

        HashMap<String, Object> body = new HashMap<String, Object>();
        body.put("serviceCategoryId", "2");
        body.put("planName", planName);
        body.put("alias", planName);
        body.put("planType", "add_ons");
        body.put("subPlanType","accessory_charger");
        body.put("subServiceCategoryId", "10010");
        body.put("replicateFor", "10012");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsCreatePlan(odsObj,headers,body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.planName"));
        Assert.assertEquals("add_ons", CreatePlanObjResponse.jsonPath().getString("data.planType"));
        Assert.assertEquals("2", CreatePlanObjResponse.jsonPath().getString("data.serviceCategoryId"));
        Assert.assertEquals("DRAFT", CreatePlanObjResponse.jsonPath().getString("data.planStatus"));
        Assert.assertEquals(planName, CreatePlanObjResponse.jsonPath().getString("data.alias"));
        Assert.assertEquals("10010", CreatePlanObjResponse.jsonPath().getString("data.subServiceCategoryId"));

    }

}
