package OCL.PlanManagement;

import Request.ODS.AddOnMappingPlanSearchAPIODS;
import Request.ODS.FetchPlanODS;
import Services.ODS.ODSPlan;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class AddOnMappingPlanSearchAPIODSTestCases extends BaseMethod {

    ODSPlan ODSPlanObject=new ODSPlan();
    private static final Logger LOGGER = LogManager.getLogger(AddOnMappingPlanSearchAPIODSTestCases.class);

    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
    String encodedXMWExToken = findXMWExTokenforPanel("7771216290","paytm@123");

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Plan Search API for EDC Onboarding Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_ODSAddonMappingPlanSearchForEDC_Onboarding() {

        AddOnMappingPlanSearchAPIODS addOnMappingPlanSearchAPIODS=new AddOnMappingPlanSearchAPIODS(P.TESTDATA.get("AddOnMappingPlanSearchAPIODS"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("searchEntity", "servicePlan");
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("requestId", "207");

    //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingPlanSearchAPIODSMiddlewareFunction(addOnMappingPlanSearchAPIODS,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Plan Search API for EDC Plan Upgrade Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_ODSAddonMappingPlanSearchForEDC_Plan_Upgrade_Plan() {

        AddOnMappingPlanSearchAPIODS addOnMappingPlanSearchAPIODS=new AddOnMappingPlanSearchAPIODS(P.TESTDATA.get("AddOnMappingPlanSearchAPIODS"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("searchEntity", "servicePlan");
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("requestId", "197");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingPlanSearchAPIODSMiddlewareFunction(addOnMappingPlanSearchAPIODS,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Plan Search API for EDC Device Upgrade Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_ODSAddonMappingPlanSearchForEDC_Device_Upgrade_Plan() {

        AddOnMappingPlanSearchAPIODS addOnMappingPlanSearchAPIODS=new AddOnMappingPlanSearchAPIODS(P.TESTDATA.get("AddOnMappingPlanSearchAPIODS"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("searchEntity", "servicePlan");
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("requestId", "181");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingPlanSearchAPIODSMiddlewareFunction(addOnMappingPlanSearchAPIODS,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Plan Search API for Soundbox Onboarding Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_ODSAddonMappingPlanSearchForSoundBox_Onboarding() {

        AddOnMappingPlanSearchAPIODS addOnMappingPlanSearchAPIODS=new AddOnMappingPlanSearchAPIODS(P.TESTDATA.get("AddOnMappingPlanSearchAPIODS"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("searchEntity", "servicePlan");
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("requestId", "206");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingPlanSearchAPIODSMiddlewareFunction(addOnMappingPlanSearchAPIODS,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Plan Search API for Soundbox Paid Replacement Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_ODSAddonMappingPlanSearchForSoundBox_Paid_Replacement() {

        AddOnMappingPlanSearchAPIODS addOnMappingPlanSearchAPIODS=new AddOnMappingPlanSearchAPIODS(P.TESTDATA.get("AddOnMappingPlanSearchAPIODS"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("searchEntity", "servicePlan");
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("requestId", "196");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingPlanSearchAPIODSMiddlewareFunction(addOnMappingPlanSearchAPIODS,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Plan Search API for Soundbox Device Upgrade Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_ODSAddonMappingPlanSearchForSoundBox_Device_Upgrade_Plan() {

        AddOnMappingPlanSearchAPIODS addOnMappingPlanSearchAPIODS=new AddOnMappingPlanSearchAPIODS(P.TESTDATA.get("AddOnMappingPlanSearchAPIODS"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("searchEntity", "servicePlan");
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("requestId", "185");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingPlanSearchAPIODSMiddlewareFunction(addOnMappingPlanSearchAPIODS,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Plan Search API for TAP AND PAY Onboarding Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_ODSAddonMappingPlanSearchForTap_and_Pay_Onboarding_Plan() {

        AddOnMappingPlanSearchAPIODS addOnMappingPlanSearchAPIODS=new AddOnMappingPlanSearchAPIODS(P.TESTDATA.get("AddOnMappingPlanSearchAPIODS"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("searchEntity", "servicePlan");
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("requestId", "161");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingPlanSearchAPIODSMiddlewareFunction(addOnMappingPlanSearchAPIODS,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Plan Search API for TAP AND PAY Onboarding Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_ODSAddonMappingPlanSearch_IncorrectSearchEntity() {

        AddOnMappingPlanSearchAPIODS addOnMappingPlanSearchAPIODS=new AddOnMappingPlanSearchAPIODS(P.TESTDATA.get("AddOnMappingPlanSearchAPIODS"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("searchEntity", "servicePlan_1");
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("requestId", "161");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingPlanSearchAPIODSMiddlewareFunction(addOnMappingPlanSearchAPIODS,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Plan Search API for TAP AND PAY Onboarding Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_ODSAddonMappingPlanSearch_IncorrectPageNumber() {

        AddOnMappingPlanSearchAPIODS addOnMappingPlanSearchAPIODS=new AddOnMappingPlanSearchAPIODS(P.TESTDATA.get("AddOnMappingPlanSearchAPIODS"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("searchEntity", "servicePlan_1");
        body.put("pageNo", "abcd");
        body.put("pageSize", "10");
        body.put("requestId", "161");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingPlanSearchAPIODSMiddlewareFunction(addOnMappingPlanSearchAPIODS,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Plan Search API for TAP AND PAY Onboarding Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_ODSAddonMappingPlanSearch_IncorrectPageSize() {

        AddOnMappingPlanSearchAPIODS addOnMappingPlanSearchAPIODS=new AddOnMappingPlanSearchAPIODS(P.TESTDATA.get("AddOnMappingPlanSearchAPIODS"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("searchEntity", "servicePlan_1");
        body.put("pageNo", "1");
        body.put("pageSize", "abcd");
        body.put("requestId", "161");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingPlanSearchAPIODSMiddlewareFunction(addOnMappingPlanSearchAPIODS,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Plan Search API for TAP AND PAY Onboarding Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11_ODSAddonMappingPlanSearch_IncorrectRequestId() {

        AddOnMappingPlanSearchAPIODS addOnMappingPlanSearchAPIODS=new AddOnMappingPlanSearchAPIODS(P.TESTDATA.get("AddOnMappingPlanSearchAPIODS"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("searchEntity", "servicePlan_1");
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("requestId", "abcd");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingPlanSearchAPIODSMiddlewareFunction(addOnMappingPlanSearchAPIODS,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Plan Search API for TAP AND PAY Onboarding Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12_ODSAddonMappingPlanSearch_MissingToken() {

        AddOnMappingPlanSearchAPIODS addOnMappingPlanSearchAPIODS=new AddOnMappingPlanSearchAPIODS(P.TESTDATA.get("AddOnMappingPlanSearchAPIODS"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("searchEntity", "servicePlan_1");
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("requestId", "161");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingPlanSearchAPIODSMiddlewareFunction(addOnMappingPlanSearchAPIODS,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

}
