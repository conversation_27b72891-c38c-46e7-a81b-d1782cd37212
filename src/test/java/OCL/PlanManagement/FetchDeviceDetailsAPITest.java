package OCL.PlanManagement;

import Request.ODS.FetchDeviceDetailsAPI;
import Request.ODS.v1.OdsCategories;
import com.goldengate.common.BaseMethod;
import Services.ODS.OdsMiddlewareServices;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import org.json.simple.parser.JSONParser;
import org.testng.Assert;
import org.testng.annotations.Test;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.restassured.path.json.JsonPath;
import java.util.HashMap;
import java.util.Map;

public class FetchDeviceDetailsAPITest extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(FetchDeviceDetailsAPITest.class);

    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";

    String encodedXMWExToken = findXMWExTokenforPanel("9716954395","paytm@123");
    @Test(priority = 0, description = "Fetch ODS Device Details on Soundbox - Checking the status code", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_FetchOdsDeviceDetailCode() {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "Soundbox");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        int httpCode = respObj.statusCode();

        System.out.println(jwt);
        Assert.assertTrue(httpCode==200, "TestcasePass");
    }

    @Test(priority = 0, description = "Throwing error when passing no Params - Checking the status code", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_FetchOdsDeviceDetailCode() {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "");


        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        int httpCode = respObj.statusCode();

        Assert.assertTrue(httpCode==400, "TestcasePass");
    }

    @Test(priority = 0, description = "Fetch ODS Device Details EDC - Checking the status code", groups = {
            "Regression" })
    public void TC_003_FetchOdsDeviceDetailCode() {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        int httpCode = respObj.statusCode();
        Assert.assertTrue(httpCode==200, "TestcasePass");
    }

    @Test(priority = 0, description = "Fetch ODS Device Details Soundbox - Checking the response body", groups = {
            "Regression" })
    public void TC_004_FetchOdsDeviceDetailCode() {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "Soundbox");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        int httpCode = respObj.statusCode();

        respObj.getBody();

        JSONParser parser = new JSONParser();

        Assert.assertEquals(respObj.jsonPath().getJsonObject("resultInfo.resultCode"),"SUCCESS");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("resultInfo.resultStatus"),"S");
    }

    @Test(priority = 0, description = "Fetch ODS Device Details EDC - Checking the response body", groups = {
            "Regression" })
    public void TC_005_FetchOdsDeviceDetailCode() {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        int httpCode = respObj.statusCode();

        respObj.getBody();

        JSONParser parser = new JSONParser();

        Assert.assertEquals(respObj.jsonPath().getJsonObject("resultInfo.resultCode"),"SUCCESS");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("resultInfo.resultStatus"),"S");
    }

    @Test(priority = 0, description = "Fetch ODS Device Details EDC - Checking the response body", groups = {
            "Regression" })
    public void TC_006_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10001";
        String EXPserviceItemName = "Android/A910";
        String EXPmodel = "A910";
        String EXPosType = "Android";
        String EXPdeviceModel = "A910";
        String EXPitemIdentifier = "android_a910";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("osType");
            Assert.assertEquals(osType.asText(), EXPosType);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("deviceModel");
            Assert.assertEquals(deviceModel.asText(), EXPdeviceModel);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details EDC - Checking the response body", groups = {
            "Regression" })
    public void TC_007_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10002";
        String EXPserviceItemName = "Android/A50";
        String EXPmodel = "A50";
        String EXPosType = "Android";
        String EXPdeviceModel = "A50";
        String EXPitemIdentifier = "android_a50";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("osType");
            Assert.assertEquals(osType.asText(), EXPosType);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("deviceModel");
            Assert.assertEquals(deviceModel.asText(), EXPdeviceModel);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details EDC - Checking the response body", groups = {
            "Regression" })
    public void TC_008_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10003";
        String EXPserviceItemName = "Linux/NEXGOG2PLUS";
        String EXPmodel = "NEXGOG2PLUS";
        String EXPosType = "Linux";
        String EXPdeviceModel = "NEXGOG2PLUS";
        String EXPitemIdentifier = "linux_nexgog2plus";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("osType");
            Assert.assertEquals(osType.asText(), EXPosType);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("deviceModel");
            Assert.assertEquals(deviceModel.asText(), EXPdeviceModel);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details EDC - Checking the response body", groups = {
            "Regression" })
    public void TC_009_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10005";
        String EXPserviceItemName = "Android/DX8000";
        String EXPmodel = "DX8000";
        String EXPosType = "Android";
        String EXPdeviceModel = "DX8000";
        String EXPitemIdentifier = "android_dx8000";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("osType");
            Assert.assertEquals(osType.asText(), EXPosType);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("deviceModel");
            Assert.assertEquals(deviceModel.asText(), EXPdeviceModel);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details EDC - Checking the response body", groups = {
            "Regression" })
    public void TC_010_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10005";
        String EXPserviceItemName = "Android/DX8000";
        String EXPmodel = "DX8000";
        String EXPosType = "Android";
        String EXPdeviceModel = "DX8000";
        String EXPitemIdentifier = "android_dx8000";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("osType");
            Assert.assertEquals(osType.asText(), EXPosType);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("deviceModel");
            Assert.assertEquals(deviceModel.asText(), EXPdeviceModel);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details EDC - Checking the response body", groups = {
            "Regression" })
    public void TC_011_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10006";
        String EXPserviceItemName = "Linux/D190";
        String EXPmodel = "D190";
        String EXPosType = "Linux";
        String EXPdeviceModel = "D190";
        String EXPitemIdentifier = "linux_d19";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("osType");
            Assert.assertEquals(osType.asText(), EXPosType);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("deviceModel");
            Assert.assertEquals(deviceModel.asText(), EXPdeviceModel);
        }
    }
    @Test(priority = 0, description = "Fetch ODS Device Details EDC - Checking the response body", groups = {
            "Regression" })
    public void TC_012_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10007";
        String EXPserviceItemName = "Android/N3";
        String EXPmodel = "N3";
        String EXPosType = "Android";
        String EXPdeviceModel = "N3";
        String EXPitemIdentifier = "android_n3";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("osType");
            Assert.assertEquals(osType.asText(), EXPosType);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("deviceModel");
            Assert.assertEquals(deviceModel.asText(), EXPdeviceModel);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details EDC - Checking the response body", groups = {
            "Regression" })
    public void TC_013_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10008";
        String EXPserviceItemName = "Android/EX6000";
        String EXPmodel = "EX6000";
        String EXPosType = "Android";
        String EXPdeviceModel = "EX6000";
        String EXPitemIdentifier = "android_ex6000";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("osType");
            Assert.assertEquals(osType.asText(), EXPosType);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("deviceModel");
            Assert.assertEquals(deviceModel.asText(), EXPdeviceModel);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details EDC - Checking the response body", groups = {
            "Regression" })
    public void TC_014_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10009";
        String EXPserviceItemName = "REALTIME/MF66S";
        String EXPmodel = "MF66S";
        String EXPosType = "REALTIME";
        String EXPdeviceModel = "MF66S";
        String EXPitemIdentifier = "realtime_mf66s";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("osType");
            Assert.assertEquals(osType.asText(), EXPosType);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("deviceModel");
            Assert.assertEquals(deviceModel.asText(), EXPdeviceModel);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details SB - Checking the response body", groups = {
            "Regression" })
    public void TC_015_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10010";
        String EXPserviceItemName = "2G/Dynamic QR+";
        String EXPmodel = "Dynamic QR+";
        String EXPtype = "2G";
        String EXPmerchantServiceName = "SOUNDBOX";
        String EXPitemIdentifier = "morefun";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("type");
            Assert.assertEquals(osType.asText(), EXPtype);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("merchantServiceName");
            Assert.assertEquals(deviceModel.asText(), EXPmerchantServiceName);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details SB - Checking the response body", groups = {
            "Regression" })
    public void TC_016_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10011";
        String EXPserviceItemName = "2G/Made In India Soundbox 2.0";
        String EXPmodel = "Made In India Soundbox 2.0";
        String EXPtype = "2G";
        String EXPmerchantServiceName = "SOUNDBOX";
        String EXPitemIdentifier = "screen_confirmation_device";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("type");
            Assert.assertEquals(osType.asText(), EXPtype);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("merchantServiceName");
            Assert.assertEquals(deviceModel.asText(), EXPmerchantServiceName);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details SB - Checking the response body", groups = {
            "Regression" })
    public void TC_017_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10012";
        String EXPserviceItemName = "2G/PSB1 with Battery";
        String EXPmodel = "PSB1 with Battery";
        String EXPtype = "2G";
        String EXPmerchantServiceName = "SOUNDBOX";
        String EXPitemIdentifier = "sound_box";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("type");
            Assert.assertEquals(osType.asText(), EXPtype);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("merchantServiceName");
            Assert.assertEquals(deviceModel.asText(), EXPmerchantServiceName);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details SB - Checking the response body", groups = {
            "Regression" })
    public void TC_018_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10013";
        String EXPserviceItemName = "4G/Made In India Soundbox 2.0 4G";
        String EXPmodel = "Made In India Soundbox 2.0 4G";
        String EXPtype = "4G";
        String EXPmerchantServiceName = "SOUNDBOX";
        String EXPitemIdentifier = "screen_confirmation_device_4g";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("type");
            Assert.assertEquals(osType.asText(), EXPtype);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("merchantServiceName");
            Assert.assertEquals(deviceModel.asText(), EXPmerchantServiceName);
        }
    }
    @Test(priority = 0, description = "Fetch ODS Device Details SB - Checking the response body", groups = {
            "Regression" })
    public void TC_019_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10014";
        String EXPserviceItemName = "2G/Made In India Soundbox 3.0";
        String EXPmodel = "Made In India Soundbox 3.0";
        String EXPtype = "2G";
        String EXPmerchantServiceName = "SOUNDBOX";
        String EXPitemIdentifier = "sound_box_3_0_2g";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("type");
            Assert.assertEquals(osType.asText(), EXPtype);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("merchantServiceName");
            Assert.assertEquals(deviceModel.asText(), EXPmerchantServiceName);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details SB - Checking the response body", groups = {
            "Regression" })
    public void TC_020_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10015";
        String EXPserviceItemName = "4G/Made In India Soundbox 3.0 4G";
        String EXPmodel = "Made In India Soundbox 3.0 4G";
        String EXPtype = "4G";
        String EXPmerchantServiceName = "SOUNDBOX";
        String EXPitemIdentifier = "sound_box_3_0_4g";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("type");
            Assert.assertEquals(osType.asText(), EXPtype);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("merchantServiceName");
            Assert.assertEquals(deviceModel.asText(), EXPmerchantServiceName);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details SB - Checking the response body", groups = {
            "Regression" })
    public void TC_021_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10016";
        String EXPserviceItemName = "4G/Soundbox 3.0 4G_Premium Gold";
        String EXPmodel = "Soundbox 3.0 4G_Premium Gold";
        String EXPtype = "4G";
        String EXPmerchantServiceName = "SOUNDBOX";
        String EXPitemIdentifier = "sound_box_3_0_4g_premium_gold";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("type");
            Assert.assertEquals(osType.asText(), EXPtype);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("merchantServiceName");
            Assert.assertEquals(deviceModel.asText(), EXPmerchantServiceName);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details SB - Checking the response body", groups = {
            "Regression" })
    public void TC_022_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10017";
        String EXPserviceItemName = "4G/SOUNDBOX 3.0 BT";
        String EXPmodel = "SOUNDBOX 3.0 BT";
        String EXPtype = "4G";
        String EXPmerchantServiceName = "SOUNDBOX";
        String EXPitemIdentifier = "SOUNDBOX_3_BT";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("type");
            Assert.assertEquals(osType.asText(), EXPtype);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("merchantServiceName");
            Assert.assertEquals(deviceModel.asText(), EXPmerchantServiceName);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details SB - Checking the response body", groups = {
            "Regression" })
    public void TC_023_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10018";
        String EXPserviceItemName = "4G/SOUNDBOX 5.0";
        String EXPmodel = "SOUNDBOX 5.0";
        String EXPtype = "4G";
        String EXPmerchantServiceName = "SOUNDBOX";
        String EXPitemIdentifier = "SOUNDBOX_5";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("type");
            Assert.assertEquals(osType.asText(), EXPtype);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("merchantServiceName");
            Assert.assertEquals(deviceModel.asText(), EXPmerchantServiceName);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details SB - Checking the response body", groups = {
            "Regression" })
    public void TC_024_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10020";
        String EXPserviceItemName = "4G/Soundbox 6";
        String EXPmodel = "Soundbox 6";
        String EXPtype = "4G";
        String EXPmerchantServiceName = "SOUNDBOX";
        String EXPitemIdentifier = "SOUNDBOX_6";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("type");
            Assert.assertEquals(osType.asText(), EXPtype);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("merchantServiceName");
            Assert.assertEquals(deviceModel.asText(), EXPmerchantServiceName);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details SB - Checking the response body", groups = {
            "Regression" })
    public void TC_025_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10021";
        String EXPserviceItemName = "4G/Soundbox 4.0 4G";
        String EXPmodel = "Soundbox 4.0 4G";
        String EXPtype = "4G";
        String EXPmerchantServiceName = "SOUNDBOX";
        String EXPitemIdentifier = "SOUNDBOX_4";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("type");
            Assert.assertEquals(osType.asText(), EXPtype);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("merchantServiceName");
            Assert.assertEquals(deviceModel.asText(), EXPmerchantServiceName);
        }
    }

    @Test(priority = 0, description = "Fetch ODS Device Details on Soundbox - Checking the status code", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_026_FetchOdsDeviceDetailCode() {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "TAPANDPAY");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        int httpCode = respObj.statusCode();

        Assert.assertTrue(httpCode==200, "TestcasePass");
    }

    @Test(priority = 0, description = "Fetch ODS Device Details Soundbox - Checking the response body", groups = {
            "Regression" })
    public void TC_027_FetchOdsDeviceDetailCode() {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        Map<String, String> headers = new HashMap<String, String>();

        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "TAPANDPAY");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        int httpCode = respObj.statusCode();

        respObj.getBody();

        JSONParser parser = new JSONParser();

        Assert.assertEquals(respObj.jsonPath().getJsonObject("resultInfo.resultCode"),"SUCCESS");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("resultInfo.resultStatus"),"S");
    }

    @Test(priority = 0, description = "Fetch ODS Device Details SB - Checking the response body", groups = {
            "Regression" })
    public void TC_028_FetchOdsDeviceDetailCode() throws JsonProcessingException {
        FetchDeviceDetailsAPI FetchDeviceDetailAPI_Object = new FetchDeviceDetailsAPI(P.TESTDATA.get("OdsDeviceAPI"));

        String EXPserviceItemId = "10019";
        String EXPserviceItemName = "4G/Soundbox 8 - D135";
        String EXPmodel = "Soundbox 8 - D135";
        String EXPtype = "4G";
        String EXPmerchantServiceName = "CARD_SOUNDBOX";
        String EXPitemIdentifier = "SOUNDBOX_8_D135";
        Map<String, String> headers = new HashMap<String, String>();

        String jwt = createAuth0JwsHMACTEST(clientId, Key);

        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("serviceCategoryName", "EDC");

        OdsMiddlewareServices odsMiddlewareServices = new OdsMiddlewareServices();

        Response respObj = odsMiddlewareServices.fetchDeviceDetailsAPIMethod(FetchDeviceDetailAPI_Object, queryParams, headers);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseJson = objectMapper.readTree(respObj.getBody().asString());

        JsonNode androidA910 = responseJson.findPath("itemIdentifier");
        if (androidA910 != null && EXPitemIdentifier.equals(androidA910.asText())) {
            // Verify specific keys have expected values
            JsonNode serviceItemId = responseJson.findPath("serviceItemId");
            Assert.assertEquals(serviceItemId.asText(), EXPserviceItemId);

            JsonNode serviceItemName = responseJson.findPath("serviceItemName");
            Assert.assertEquals(serviceItemName.asText(), EXPserviceItemName);

            JsonNode model = responseJson.findPath("model");
            Assert.assertEquals(model.asText(), EXPmodel);

            JsonNode osType = responseJson.findPath("type");
            Assert.assertEquals(osType.asText(), EXPtype);

            JsonNode deviceModel = responseJson.findPath("deviceMetadata").findPath("merchantServiceName");
            Assert.assertEquals(deviceModel.asText(), EXPmerchantServiceName);
        }
    }
}
