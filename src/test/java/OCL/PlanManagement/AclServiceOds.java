package OCL.PlanManagement;

import Request.ODS.v1.OdsAclService;
import Services.ODS.OdsMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class AclServiceOds extends BaseMethod{
	OdsMiddlewareServices odsMiddlewareServicesObject = new OdsMiddlewareServices();

	//private static final Logger Logger = LogManager.getLogger(AclServiceOds.class);

	private static final Logger LOGGER = LogManager.getLogger(AclServiceOds.class);

	public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
    String encodedXMWExToken = findXMWExTokenforPanel("5123456788","paytm@123");
   
    @Test(priority = 0, description = "Fetch Ods Acl Service Response", groups = {
	"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_001_OdsAclServiceStatusCode() {
		OdsAclService odsAclService = new OdsAclService(P.TESTDATA.get("OdsAclServicePath"));
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		
		Response respObj = odsMiddlewareServicesObject.odsAclServiceMethod(odsAclService, headers);

		LOGGER.info("Fetch Ods Acl Service Response" + respObj.statusCode());

		Assert.assertEquals(respObj.statusCode(), 200);
	}

@Test(priority = 0, description = "Ods Acl Service With Wrong Token", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_002_OdsAclServiceWithWrongToken() {
	OdsAclService odsAclService = new OdsAclService(P.TESTDATA.get("OdsAclServicePath"));
	Map<String, String> headers = new HashMap<String, String>();
	headers.put("Content-Type", "application/json");
	headers.put("X-MW-TOKEN-EX", "ajkhsgdkjdkb");
	Response respObj = null;
	try {
		respObj = odsMiddlewareServicesObject.odsAclServiceMethod(odsAclService, headers);

	} catch (PatternSyntaxException e) {

	}
	if (respObj != null) {
		LOGGER.info("Ods Acl Service Response" + respObj.statusCode());
		Assert.assertEquals(respObj.statusCode(), 401);
	}
}
@Test(priority = 0, description = "Ods Acl Service Check Result Status Success", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_003_OdsAclServiceCheckResultStatusSuccess() throws ParseException {
	OdsAclService odsAclService = new OdsAclService(P.TESTDATA.get("OdsAclServicePath"));
	Map<String, String> headers = new HashMap<String, String>();
	headers.put("Content-Type", "application/json");
	headers.put("X-MW-TOKEN-EX",encodedXMWExToken);
	
	Response respObj = odsMiddlewareServicesObject.odsAclServiceMethod(odsAclService, headers);
	LOGGER.info("Ods Acl Service Check Result Status Success Response" + respObj.statusCode());
	
	
	respObj.getBody();
	
	JSONParser parser = new JSONParser();  
	
	JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString()); 
	
	JSONObject jsObj2=(JSONObject) jsObj1.get("resultInfo");
	
	LOGGER.info("Fetch Ods Acl Service Check Result Status Success Response" + respObj.statusCode());
	
	Assert.assertEquals(jsObj2.get("resultStatus"),"S");

}
@Test(priority = 0, description = "Ods Acl Service Check Result Code Success", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_004_OdsAclServiceCheckResultCodeSuccess() throws ParseException {
	OdsAclService odsAclService= new OdsAclService(P.TESTDATA.get("OdsAclServicePath"));
	Map<String, String> headers = new HashMap<String, String>();
	headers.put("Content-Type", "application/json");
	headers.put("X-MW-TOKEN-EX",encodedXMWExToken);
	
	Response respObj = odsMiddlewareServicesObject.odsAclServiceMethod(odsAclService, headers);
	LOGGER.info("Ods Acl Service Check Result Code Success Response" + respObj.statusCode());
	
	
	respObj.getBody();
	
	JSONParser parser = new JSONParser();  
	
	JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString()); 
	
	JSONObject jsObj2=(JSONObject) jsObj1.get("resultInfo");
	
	LOGGER.info("Ods Acl Service Check Result Code Success Response" + respObj.statusCode());
	
	Assert.assertEquals(jsObj2.get("resultCode"),"SUCCESS");

}
@Test(priority = 0, description = "Ods Acl Service Check Result CodeId Success", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_005_OdsAclServiceCheckResultCodeIdSuccess() throws ParseException {
	OdsAclService odsAclService = new OdsAclService(P.TESTDATA.get("OdsAclServicePath"));
	Map<String, String> headers = new HashMap<String, String>();
	headers.put("Content-Type", "application/json");
	headers.put("X-MW-TOKEN-EX",encodedXMWExToken);
	
	Response respObj = odsMiddlewareServicesObject.odsAclServiceMethod(odsAclService, headers);
	LOGGER.info("Ods Acl Service Check Result CodeId Success Response" + respObj.statusCode());
	
	
	respObj.getBody();
	
	JSONParser parser = new JSONParser();  
	
	JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString()); 
	
	JSONObject jsObj2=(JSONObject) jsObj1.get("resultInfo");
	
	LOGGER.info("Ods Acl Service Check Result Code Success Response" + respObj.statusCode());
	
	Assert.assertEquals(jsObj2.get("resultCodeId"),"01");

}
@Test(priority = 0, description = "Ods Acl Service Check Result Data CustId", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_006_OdsAclServiceCheckResultDataCustId() throws ParseException {
	OdsAclService odsAclService = new OdsAclService(P.TESTDATA.get("OdsAclServicePath"));
	Map<String, String> headers = new HashMap<String, String>();
	headers.put("Content-Type", "application/json");
	headers.put("X-MW-TOKEN-EX",encodedXMWExToken);
	
	Response respObj = odsMiddlewareServicesObject.odsAclServiceMethod(odsAclService, headers);
	LOGGER.info("Ods Acl Service Check Result Data CustId" + respObj.statusCode());
	
	
	respObj.getBody();
	
	JSONParser parser = new JSONParser();  
	
	JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());
	JSONObject jsObj2=(JSONObject) jsObj1.get("data");
	String CustId=(String)jsObj2.get("custId");
	Assert.assertEquals(CustId,"1000045689");
	
	
}
@Test(priority = 0, description = "Ods Acl Service Check Result Data CustId Should not be Null", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_007_OdsAclServiceCheckResultDataCustIdShouldNotNull() throws ParseException {
	OdsAclService odsAclService = new OdsAclService(P.TESTDATA.get("OdsAclServicePath"));
	Map<String, String> headers = new HashMap<String, String>();
	headers.put("Content-Type", "application/json");
	headers.put("X-MW-TOKEN-EX",encodedXMWExToken);
	
	Response respObj = odsMiddlewareServicesObject.odsAclServiceMethod(odsAclService, headers);
	LOGGER.info("Ods Acl Service Check Result Data CustId Should not be Null" + respObj.statusCode());
	
	
	respObj.getBody();
	
	JSONParser parser = new JSONParser();  
	
	JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());
	JSONObject jsObj2=(JSONObject) jsObj1.get("data");
	String CustId=(String)jsObj2.get("custId");
	Assert.assertNotNull(CustId);
	
}
@Test(priority = 0, description = "Ods Acl Service Check Result Data Permissions should not be null", groups = { "Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_008_OdsAclServiceCheckResultDataPermissionsShouldNotNull() throws ParseException {
	OdsAclService odsAclService = new OdsAclService(P.TESTDATA.get("OdsAclServicePath"));
	Map<String, String> headers = new HashMap<String, String>();
	headers.put("Content-Type", "application/json");
	headers.put("X-MW-TOKEN-EX",encodedXMWExToken);
	
	Response respObj = odsMiddlewareServicesObject.odsAclServiceMethod(odsAclService, headers);
	LOGGER.info("Ods Acl Service Check Result Data Permissions Should not be Null" + respObj.statusCode());
	
	
	respObj.getBody();
	
	JSONParser parser = new JSONParser();  
	
	JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());
	JSONObject jsObj2=(JSONObject) jsObj1.get("data");
	JSONArray  jsObj3=(JSONArray) jsObj2.get("permissions");
	Assert.assertNotNull(jsObj3);
}

}