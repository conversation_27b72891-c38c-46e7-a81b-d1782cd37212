package OCL.PlanManagement;

import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FetchSubCategoryTest extends BaseMethod {
    private Logger LOGGER = LogManager.getLogger(FetchSubCategoryTest.class);
    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    String category1="edc";
    String category2="soundbox";
    String EmptyToken="";
    String ExpiredToken="abcd";
    String category3="";
    String category4="edc,soundbox";

    @Test(description = "Fetching EDC as subcategory", groups = {"Regression" },enabled=true)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC01_FetchSubCategoryEDC() throws InterruptedException {
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("category", category1);

        Response responseObject = MiddlewareServicesObject.deviceFetchSubCategoryEDC( headers,queryParams);

        int statusCode=responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }
    @Test(description = "Fetching Soundbox as subcategory", groups = {"Regression" },enabled=true)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC02_FetchSubCategorySoundbox() throws InterruptedException {
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("category", category2);

        Response responseObject = MiddlewareServicesObject.deviceFetchSubCategoryEDC( headers,queryParams);

        int statusCode=responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        //   Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data Not present for customer");

    }
    @Test(description = "Fetching Empty as subcategory", groups = {"Regression" },enabled=true)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC03_FetchSubCategoryEmpty() throws InterruptedException {
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("category", category3);

        Response responseObject = MiddlewareServicesObject.deviceFetchSubCategoryEDC( headers,queryParams);

        int statusCode=responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(description = "Fetching edc and soundbox as subcategory", groups = {"Regression" },enabled=true)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC04_FetchSubCategoryEdcAndSoundbox() throws InterruptedException {
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("category", category4);

        Response responseObject = MiddlewareServicesObject.deviceFetchSubCategoryEDC( headers,queryParams);

        int statusCode=responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(description = "Fetching edc as subcategory with invalid/expired token", groups = {"Regression" },enabled=false)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC05_FetchSubCategoryExpiredToken() throws InterruptedException {
        //String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",ExpiredToken);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("category", category1);

        Response responseObject = MiddlewareServicesObject.deviceFetchSubCategoryEDC( headers,queryParams);

        int statusCode=responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(description = "Fetching edc as subcategory with no token", groups = {"Regression" },enabled=false)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC06_FetchSubCategoryNoToken() throws InterruptedException {
        // String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",EmptyToken);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("category", category1);

        Response responseObject = MiddlewareServicesObject.deviceFetchSubCategoryEDC( headers,queryParams);

        int statusCode=responseObject.getStatusCode();
        Assert.assertNotEquals(statusCode, 200);
    }
    @Test(description = "Fetching invalid data as subcategory", groups = {"Regression" },enabled=true)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC07_FetchSubCategoryInvalidData() throws InterruptedException {
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("category", "abc");

        Response responseObject = MiddlewareServicesObject.deviceFetchSubCategoryEDC( headers,queryParams);

        int statusCode=responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(description = "Fetching no category in params", groups = {"Regression" },enabled=true)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC08_FetchWithNoSubCategory() throws InterruptedException {
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);
        Map<String, String> queryParams = new HashMap<String, String>();
        //queryParams.put("category", "abc");

        Response responseObject = MiddlewareServicesObject.deviceFetchSubCategoryEDC( headers,queryParams);

        int statusCode=responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
}
