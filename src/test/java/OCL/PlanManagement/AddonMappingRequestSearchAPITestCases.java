package OCL.PlanManagement;

import Request.ODS.AddonMappingRequestSearchAPI;
import Services.ODS.ODSPlan;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class AddonMappingRequestSearchAPITestCases extends BaseMethod {

    ODSPlan ODSPlanObject=new ODSPlan();
    private static final Logger LOGGER = LogManager.getLogger(AddOnMappingPlanSearchAPIODSTestCases.class);

    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
    String encodedXMWExToken = findXMWExTokenforPanel("7771216290","paytm@123");

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Request Search for EDC Onboarding Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_ODSAddonMappingRequestSearchForEDC_Onboarding() {

        AddonMappingRequestSearchAPI addonMappingRequestSearchAPI = new AddonMappingRequestSearchAPI(P.TESTDATA.get("AddonMappingRequestSearchAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "1");
        body.put("serviceItemId", "10001");
        body.put("planType", "onboarding");
        body.put("subPlanTypes", "brand_emi");
        body.put("mappingType", "MAP_ADD_ON");
        body.put("status", "ACTIVE");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingRequestSearchAPIMiddlewareFunction(addonMappingRequestSearchAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Request Search for EDC Device Upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_ODSAddonMappingRequestSearchForEDC_DeviceUpgrade() {

        AddonMappingRequestSearchAPI addonMappingRequestSearchAPI = new AddonMappingRequestSearchAPI(P.TESTDATA.get("AddonMappingRequestSearchAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "1");
        body.put("serviceItemId", "10001");
        body.put("planType", "device_upgrade");
        body.put("subPlanTypes", "brand_emi");
        body.put("mappingType", "MAP_ADD_ON");
        body.put("status", "ACTIVE");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingRequestSearchAPIMiddlewareFunction(addonMappingRequestSearchAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Request Search for EDC Plan Upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_ODSAddonMappingRequestSearchForEDC_PlanUpgrade() {

        AddonMappingRequestSearchAPI addonMappingRequestSearchAPI = new AddonMappingRequestSearchAPI(P.TESTDATA.get("AddonMappingRequestSearchAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "1");
        body.put("serviceItemId", "10001");
        body.put("planType", "plan_upgrade");
        body.put("subPlanTypes", "brand_emi");
        body.put("mappingType", "MAP_ADD_ON");
        body.put("status", "ACTIVE");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingRequestSearchAPIMiddlewareFunction(addonMappingRequestSearchAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Request Search for EDC Addon Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_ODSAddonMappingRequestSearchForEDC_AddonPlan() {

        AddonMappingRequestSearchAPI addonMappingRequestSearchAPI = new AddonMappingRequestSearchAPI(P.TESTDATA.get("AddonMappingRequestSearchAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "1");
        body.put("serviceItemId", "10001");
        body.put("planType", "add_ons");
        body.put("subPlanTypes", "brand_emi");
        body.put("mappingType", "MAP_ADD_ON");
        body.put("status", "ACTIVE");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingRequestSearchAPIMiddlewareFunction(addonMappingRequestSearchAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Request Search for SB onboarding Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_ODSAddonMappingRequestSearchForSoundbox_Onboarding() {

        AddonMappingRequestSearchAPI addonMappingRequestSearchAPI = new AddonMappingRequestSearchAPI(P.TESTDATA.get("AddonMappingRequestSearchAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "2");
        body.put("planType", "onboarding");
        body.put("subPlanTypes", "premium_care");
        body.put("mappingType", "MAP_ADD_ON");
        body.put("status", "ACTIVE");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingRequestSearchAPIMiddlewareFunction(addonMappingRequestSearchAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Request Search for SB Device Upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_ODSAddonMappingRequestSearchForSB_DeviceUpgrade() {

        AddonMappingRequestSearchAPI addonMappingRequestSearchAPI = new AddonMappingRequestSearchAPI(P.TESTDATA.get("AddonMappingRequestSearchAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "2");
        body.put("planType", "device_upgrade");
        body.put("subPlanTypes", "premium_care");
        body.put("mappingType", "MAP_ADD_ON");
        body.put("status", "ACTIVE");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingRequestSearchAPIMiddlewareFunction(addonMappingRequestSearchAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Request Search for SB Paid Replacement Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_ODSAddonMappingRequestSearchForSB_PaidReplacement() {

        AddonMappingRequestSearchAPI addonMappingRequestSearchAPI = new AddonMappingRequestSearchAPI(P.TESTDATA.get("AddonMappingRequestSearchAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "2");
        body.put("planType", "paid_replacement");
        body.put("subPlanTypes", "premium_care");
        body.put("mappingType", "MAP_ADD_ON");
        body.put("status", "ACTIVE");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingRequestSearchAPIMiddlewareFunction(addonMappingRequestSearchAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Request Search for SB Addon Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_ODSAddonMappingRequestSearchForSB_Addons() {

        AddonMappingRequestSearchAPI addonMappingRequestSearchAPI = new AddonMappingRequestSearchAPI(P.TESTDATA.get("AddonMappingRequestSearchAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "2");
        body.put("planType", "add_ons");
        body.put("subPlanTypes", "premium_care");
        body.put("mappingType", "MAP_ADD_ON");
        body.put("status", "ACTIVE");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingRequestSearchAPIMiddlewareFunction(addonMappingRequestSearchAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Request Search for TapnPay Onboarding Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_ODSAddonMappingRequestSearchForTapnPay_Onboarding() {

        AddonMappingRequestSearchAPI addonMappingRequestSearchAPI = new AddonMappingRequestSearchAPI(P.TESTDATA.get("AddonMappingRequestSearchAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "3");
        body.put("planType", "onboarding");
        body.put("mappingType", "MAP_ADD_ON");
        body.put("status", "ACTIVE");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingRequestSearchAPIMiddlewareFunction(addonMappingRequestSearchAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Request Search for TapnPay Onboarding Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_ODSAddonMappingRequestSearch_IncorrectServiceCategoryID() {

        AddonMappingRequestSearchAPI addonMappingRequestSearchAPI = new AddonMappingRequestSearchAPI(P.TESTDATA.get("AddonMappingRequestSearchAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "111");
        body.put("planType", "onboarding");
        body.put("mappingType", "MAP_ADD_ON");
        body.put("status", "ACTIVE");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingRequestSearchAPIMiddlewareFunction(addonMappingRequestSearchAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Request Search for TapnPay Onboarding Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11_ODSAddonMappingRequestSearch_IncorrectPlanType() {

        AddonMappingRequestSearchAPI addonMappingRequestSearchAPI = new AddonMappingRequestSearchAPI(P.TESTDATA.get("AddonMappingRequestSearchAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "3");
        body.put("planType", "onboarding123");
        body.put("mappingType", "MAP_ADD_ON");
        body.put("status", "ACTIVE");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingRequestSearchAPIMiddlewareFunction(addonMappingRequestSearchAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Request Search for TapnPay Onboarding Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12_ODSAddonMappingRequestSearch_IncorrectMappingType() {

        AddonMappingRequestSearchAPI addonMappingRequestSearchAPI = new AddonMappingRequestSearchAPI(P.TESTDATA.get("AddonMappingRequestSearchAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "3");
        body.put("planType", "onboarding");
        body.put("mappingType", "MAP_ADD_ON11");
        body.put("status", "ACTIVE");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingRequestSearchAPIMiddlewareFunction(addonMappingRequestSearchAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Request Search for TapnPay Onboarding Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13_ODSAddonMappingRequestSearch_IncorrectPlanType() {

        AddonMappingRequestSearchAPI addonMappingRequestSearchAPI = new AddonMappingRequestSearchAPI(P.TESTDATA.get("AddonMappingRequestSearchAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "3");
        body.put("planType", "onboarding");
        body.put("mappingType", "MAP_ADD_ON");
        body.put("status", "ACTIVE123");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingRequestSearchAPIMiddlewareFunction(addonMappingRequestSearchAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Addon Mapping Request Search for TapnPay Onboarding Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_14_ODSAddonMappingRequestSearch_IncorrectSubPlanType() {

        AddonMappingRequestSearchAPI addonMappingRequestSearchAPI = new AddonMappingRequestSearchAPI(P.TESTDATA.get("AddonMappingRequestSearchAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "3");
        body.put("planType", "onboarding");
        body.put("mappingType", "MAP_ADD_ON");
        body.put("subPlanTypes", "premium_care123");
        body.put("status", "ACTIVE");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.addonMappingRequestSearchAPIMiddlewareFunction(addonMappingRequestSearchAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }
}
