package OCL.PlanManagement;

import Services.ODS.OdsMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class ODSnumberofRecords extends BaseMethod {
    OdsMiddlewareServices odsMiddlewareServicesObject = new OdsMiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(ODSnumberofRecords.class);
    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
    String invalid_token = "GJTQ5b7fGUQ54nqkQyxQ04xw0";
    String encodedXMWExToken = findXMWExTokenforPanel("7771216290","paytm@123");
    //sshHost=************

    //8884211099       5123456788


    @Test(priority = 0,description = "Ods Records Response")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_ODSnumberofRecordsStatusCode()
    {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Map<String, String> params = new HashMap<String,String>();
        params.put("serviceCategory","1");
        params.put("creatorMobileNumber","8884211099");

        Response respObj = odsMiddlewareServicesObject.odsNumberofRecordsmethod(headers, params);

        LOGGER.info("Records status Response ==> " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);
        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultCode"),"SUCCESS");


    }

    @Test(priority = 0,description = "Invalid token Response")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_ODSinvalidtoken()
    {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("X-MW-TOKEN-EX", invalid_token);

        Map<String, String> params = new HashMap<String,String>();
        params.put("serviceCategory","1");
        params.put("creatorMobileNumber","8884211099");
        Response respObj = null;
        try{
            respObj = odsMiddlewareServicesObject.odsNumberofRecordsmethod(headers, params);
        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {

            int statusCode = respObj.getStatusCode();
            LOGGER.info("Records status Response ==> " + respObj.statusCode());
            Assert.assertEquals(statusCode, 401);
        }


    }
    @Test(priority = 0,description = "Invalid Service Category")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_ODSinvalidServiceCategory()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Map<String, String> params = new HashMap<String,String>();
        params.put("serviceCategory","a");
        params.put("creatorMobileNumber","8884211099");

        Response respObj = odsMiddlewareServicesObject.odsNumberofRecordsmethod(headers, params);
        LOGGER.info("result Message ==> " + respObj.jsonPath().getString("resultInfo.resultMsg"));
        Assert.assertEquals(respObj.getStatusCode(), 400);


    }

    @Test(priority = 0,description = "No Service Category chosen")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_ODSnoServiceCategory()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Map<String, String> params = new HashMap<String,String>();
        params.put("serviceCategory","");
        params.put("creatorMobileNumber","8884211099");
        Response respObj = odsMiddlewareServicesObject.odsNumberofRecordsmethod(headers, params);
        LOGGER.info("Result Status ==> " + respObj.jsonPath().getString("resultInfo.resultStatus"));
        LOGGER.info("Result Code ==> " + respObj.jsonPath().getString("resultInfo.resultCode"));
        Assert.assertEquals(respObj.getStatusCode(), 500);

    }
}

