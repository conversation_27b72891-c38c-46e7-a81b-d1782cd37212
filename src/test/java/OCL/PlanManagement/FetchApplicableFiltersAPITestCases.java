package OCL.PlanManagement;

import Request.ODS.AddOnMappingPlanSearchAPIODS;
import Request.ODS.FetchApplicableFiltersAPI;
import Services.ODS.ODSPlan;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.util.HashMap;
import java.util.Map;

public class FetchApplicableFiltersAPITestCases extends BaseMethod {

    ODSPlan ODSPlanObject=new ODSPlan();

    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";

    String encodedXMWExToken = findXMWExTokenforPanel("7771216290","paytm@123");

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch Applicable filters for EDC Onboarding Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_FetchApplicableFiltersForEDC_Onboarding() {

        FetchApplicableFiltersAPI fetchApplicableFiltersAPI = new FetchApplicableFiltersAPI(P.TESTDATA.get("FetchApplicableFiltersAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("serviceCategoryId", "1");
        queryParam.put("planType", "onboarding");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response response = ODSPlanObject.fetchApplicableFiltersAPIMiddlewareFunction(fetchApplicableFiltersAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch Applicable filters for EDC Device Upgrade Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_FetchApplicableFiltersForEDC_DeviceUpgrade() {

        FetchApplicableFiltersAPI fetchApplicableFiltersAPI = new FetchApplicableFiltersAPI(P.TESTDATA.get("FetchApplicableFiltersAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("serviceCategoryId", "1");
        queryParam.put("planType", "device_upgrade");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response response = ODSPlanObject.fetchApplicableFiltersAPIMiddlewareFunction(fetchApplicableFiltersAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch Applicable filters for EDC Plan Upgrade Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_FetchApplicableFiltersForEDC_PlanUpgrade() {

        FetchApplicableFiltersAPI fetchApplicableFiltersAPI = new FetchApplicableFiltersAPI(P.TESTDATA.get("FetchApplicableFiltersAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("serviceCategoryId", "1");
        queryParam.put("planType", "plan_upgrade");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response response = ODSPlanObject.fetchApplicableFiltersAPIMiddlewareFunction(fetchApplicableFiltersAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch Applicable filters for EDC Addon BrandEMI Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_FetchApplicableFiltersForEDC_AddonPlanBrandEMI() {

        FetchApplicableFiltersAPI fetchApplicableFiltersAPI = new FetchApplicableFiltersAPI(P.TESTDATA.get("FetchApplicableFiltersAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("serviceCategoryId", "1");
        queryParam.put("planType", "add_ons");
        queryParam.put("subPlanType","brand_emi");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response response = ODSPlanObject.fetchApplicableFiltersAPIMiddlewareFunction(fetchApplicableFiltersAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch Applicable filters for SB Onboarding Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_FetchApplicableFiltersForSB_Onboarding() {

        FetchApplicableFiltersAPI fetchApplicableFiltersAPI = new FetchApplicableFiltersAPI(P.TESTDATA.get("FetchApplicableFiltersAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("serviceCategoryId", "2");
        queryParam.put("planType", "onboarding");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response response = ODSPlanObject.fetchApplicableFiltersAPIMiddlewareFunction(fetchApplicableFiltersAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch Applicable filters for SB Device Upgrade Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_FetchApplicableFiltersForSB_DeviceUpgrade() {

        FetchApplicableFiltersAPI fetchApplicableFiltersAPI = new FetchApplicableFiltersAPI(P.TESTDATA.get("FetchApplicableFiltersAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("serviceCategoryId", "2");
        queryParam.put("planType", "device_upgrade");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response response = ODSPlanObject.fetchApplicableFiltersAPIMiddlewareFunction(fetchApplicableFiltersAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch Applicable filters for SB Paid Replacement Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_FetchApplicableFiltersForSB_PaidReplacement() {

        FetchApplicableFiltersAPI fetchApplicableFiltersAPI = new FetchApplicableFiltersAPI(P.TESTDATA.get("FetchApplicableFiltersAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("serviceCategoryId", "2");
        queryParam.put("planType", "paid_replacement");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response response = ODSPlanObject.fetchApplicableFiltersAPIMiddlewareFunction(fetchApplicableFiltersAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch Applicable filters for SB Addon Premium care Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_FetchApplicableFiltersForSB_AddonPremiumCare() {

        FetchApplicableFiltersAPI fetchApplicableFiltersAPI = new FetchApplicableFiltersAPI(P.TESTDATA.get("FetchApplicableFiltersAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("serviceCategoryId", "2");
        queryParam.put("planType", "add_ons");
        queryParam.put("subPlanType", "premium_care");


        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response response = ODSPlanObject.fetchApplicableFiltersAPIMiddlewareFunction(fetchApplicableFiltersAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch Applicable filters for SB Addon shop insurance  Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_FetchApplicableFiltersForSB_AddonAMC() {

        FetchApplicableFiltersAPI fetchApplicableFiltersAPI = new FetchApplicableFiltersAPI(P.TESTDATA.get("FetchApplicableFiltersAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("serviceCategoryId", "2");
        queryParam.put("planType", "add_ons");
        queryParam.put("subPlanType", "shop_insurance");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response response = ODSPlanObject.fetchApplicableFiltersAPIMiddlewareFunction(fetchApplicableFiltersAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch Applicable filters for TapnPay Onboarding Plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_FetchApplicableFiltersForTapNPay_Onboarding() {

        FetchApplicableFiltersAPI fetchApplicableFiltersAPI = new FetchApplicableFiltersAPI(P.TESTDATA.get("FetchApplicableFiltersAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("serviceCategoryId", "3");
        queryParam.put("planType", "onboarding");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response response = ODSPlanObject.fetchApplicableFiltersAPIMiddlewareFunction(fetchApplicableFiltersAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch Applicable filters for Incorrect ServiceCategoryID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11_FetchApplicableFiltersForIncorrectServiceCategoryID() {

        FetchApplicableFiltersAPI fetchApplicableFiltersAPI = new FetchApplicableFiltersAPI(P.TESTDATA.get("FetchApplicableFiltersAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("serviceCategoryId", "386");
        queryParam.put("planType", "onboarding");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response response = ODSPlanObject.fetchApplicableFiltersAPIMiddlewareFunction(fetchApplicableFiltersAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch Applicable filters for Incorrect Plan Type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12_FetchApplicableFiltersForIncorrectplanType() {

        FetchApplicableFiltersAPI fetchApplicableFiltersAPI = new FetchApplicableFiltersAPI(P.TESTDATA.get("FetchApplicableFiltersAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("serviceCategoryId", "1");
        queryParam.put("planType", "onboarding123");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response response = ODSPlanObject.fetchApplicableFiltersAPIMiddlewareFunction(fetchApplicableFiltersAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch Applicable filters for Incorrect Sub Plan Type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13_FetchApplicableFiltersForIncorrectSubPlanType() {

        FetchApplicableFiltersAPI fetchApplicableFiltersAPI = new FetchApplicableFiltersAPI(P.TESTDATA.get("FetchApplicableFiltersAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("serviceCategoryId", "1");
        queryParam.put("planType", "add_ons");
        queryParam.put("subPlanType", "1234");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response response = ODSPlanObject.fetchApplicableFiltersAPIMiddlewareFunction(fetchApplicableFiltersAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }


}
