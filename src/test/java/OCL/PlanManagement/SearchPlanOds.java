package OCL.PlanManagement;

import Request.ODS.v1.OdsSearchPlan;
import Services.ODS.OdsMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class SearchPlanOds extends BaseMethod {

	OdsMiddlewareServices odsMiddlewareServicesObject = new OdsMiddlewareServices();

	private static final Logger LOGGER = LogManager.getLogger(SearchPlanOds.class);

	public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
    String encodedXMWExToken = findXMWExTokenforPanel("5123456788","paytm@123");
	
	@Test(priority = 0, description = "Ods Search Plan", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_001_OdsSearchPlanStatusCode() {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("creatorMobileNumber", "5123456788");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan Info" + respObj.statusCode());
	}
	@Test(priority = 0, description = "Ods Search Plan With Wrong Token", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_002_OdsSearchPlanWithWrongToken() {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("creatorMobileNumber", "5123456788");
		

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", "acccvv");
		Response respObj = null;
		try {
			respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);

		} catch (PatternSyntaxException e) {

		}
		if (respObj != null) {
			LOGGER.info("Ods Search Plan Response" + respObj.statusCode());
			Assert.assertEquals(respObj.statusCode(), 401);
		}
	}
	@Test(priority = 0, description = "Ods Search Plan Check Result Status Success", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_003_OdsSearchPlanCheckResultStatusSuccess() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("creatorMobileNumber", "5123456788");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan Check Result Status Success Response" + respObj.statusCode());

		respObj.getBody();

		JSONParser parser = new JSONParser();

		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

		JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");

		LOGGER.info("Ods Search Plan Check Result Status Success Response" + respObj.statusCode());

		Assert.assertEquals(jsObj2.get("resultStatus"), "S");
	}
	@Test(priority = 0, description = "Ods Search Plan Check Result Code Success", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_004_OdsSearchPlanCheckResultCodeSuccess() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("creatorMobileNumber", "5123456788");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan Check Result Code Success Response" + respObj.statusCode());

		respObj.getBody();

		JSONParser parser = new JSONParser();

		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

		JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");

		LOGGER.info("Ods Search Plan Check Result Code Success Response" + respObj.statusCode());

		Assert.assertEquals(jsObj2.get("resultCode"), "SUCCESS");
	}
	@Test(priority = 0, description = "Ods Search Plan Check Result Code Id Success", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_005_OdsSearchPlanCheckResultCodeIdSuccess() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("creatorMobileNumber", "5123456788");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan Check Result Code Id Success Response" + respObj.statusCode());

		respObj.getBody();

		JSONParser parser = new JSONParser();

		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

		JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");

		LOGGER.info("Ods Search Plan Check Result Code Id Success Response" + respObj.statusCode());

		Assert.assertEquals(jsObj2.get("resultCodeId"), "01");
	}
	
	@Test(priority = 0, description = "Ods Search Plan for Android/A910", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_006_OdsSearchPlanForAndroidA910() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10001");
		queryParam.put("creatorMobileNumber", "5123456788");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan for Android/A910" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}
	@Test(priority = 0, description = "Ods Search Plan for Android/A50", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_007_OdsSearchPlanForAndroidA50() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10002");
		queryParam.put("creatorMobileNumber", "5123456788");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan for Android/A50" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}
	@Test(priority = 0, description = "Ods Search Plan for Linux/NEXGOG2PLUS", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_008_OdsSearchPlanForLinuxNEXGOG2PLUS() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10003");
		queryParam.put("creatorMobileNumber", "5123456788");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan for Linux/NEXGOG2PLUS" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}
	@Test(priority = 0, description = "Ods Search Plan for Android With POS/A910", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_009_OdsSearchPlanAndroidWithPOSA910() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10004");
		queryParam.put("creatorMobileNumber", "5123456788");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan for Android With POS/A910" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}
	@Test(priority = 0, description = "Ods Search Plan for Android/DX8000", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_010_OdsSearchPlanForAndroidDX8000() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10005");
		queryParam.put("creatorMobileNumber", "5123456788");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan for Android/DX8000" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}
	@Test(priority = 0, description = "Ods Search Plan for Linux/D190", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_011_OdsSearchPlanForLinuxD190() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10006");
		queryParam.put("creatorMobileNumber", "5123456788");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan for Linux/D190" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}	
	
	@Test(priority = 0, description = "Ods Search Plan for Android/N3", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_012_OdsSearchPlanForAndroidN3() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10007");
		queryParam.put("creatorMobileNumber", "5123456788");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan for Android/N3" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}
	@Test(priority = 0, description = "Ods Search Plan for Android/EX8000", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_013_OdsSearchPlanForAndroidEX8000() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10008");
		queryParam.put("creatorMobileNumber", "5123456788");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan for Android EX8000" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}	
	@Test(priority = 0, description = "Ods Search Plan for Realtime/MF66S", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_014_OdsSearchPlanForRealtimeMF66S() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10009");
		queryParam.put("creatorMobileNumber", "5123456788");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan for Realtime/MF66S" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}	
	@Test(priority = 0, description = "Ods Search Plan with proper Date format", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_015_OdsSearchPlanWithProperDateFormat() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10009");
		queryParam.put("creatorMobileNumber", "5123456788");
		queryParam.put("startDate", "2022-10-09");
		queryParam.put("endDate","2022-10-09");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}	
	@Test(priority = 0, description = "Ods Search Plan with invalid date format", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_016_OdsSearchPlanDateTimeInvalidFormat() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10009");
		queryParam.put("creatorMobileNumber", "5123456788");
		queryParam.put("startDate", "09-10-2022");
		queryParam.put("endDate","09-10-2022");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}	
	@Test(priority = 0, description = "Ods Search Plan with status DRAFT", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_017_OdsSearchPlanWithStatusDRAFT() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10001");
		queryParam.put("creatorMobileNumber", "5123456788");
		queryParam.put("status","DRAFT");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan with status DRAFT" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}
	@Test(priority = 0, description = "Ods Search Plan with status PENDING", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_018_OdsSearchPlanWithStatusPENDING() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10002");
		queryParam.put("creatorMobileNumber", "5123456788");
		queryParam.put("status","PENDING");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan with status PENDING" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}
	
	@Test(priority = 0, description = "Ods Search Plan with status ACTIVE", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_019_OdsSearchPlanWithStatusACTIVE() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10003");
		queryParam.put("creatorMobileNumber", "5123456788");
		queryParam.put("status","ACTIVE");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan with status ACTIVE" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}
	@Test(priority = 0, description = "Ods Search Plan with status REJECTED", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_020_OdsSearchPlanWithStatusREJECTED() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10001");
		queryParam.put("creatorMobileNumber", "5123456788");
		queryParam.put("status","REJECTED");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan with status REJECTED" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}	
	@Test(priority = 0, description = "Ods Search Plan with status DELETE REQUESTED", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_021_OdsSearchPlanWithStatusDELETEREQUESTED() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10001");
		queryParam.put("creatorMobileNumber", "5123456788");
		queryParam.put("status","DELETE_REQUESTED");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan with status DELETE REQUESTED" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}	
	@Test(priority = 0, description = "Ods Search Plan with status DELETION APPROVED", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_022_OdsSearchPlanWithStatusDELETIONAPPROVED() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10001");
		queryParam.put("creatorMobileNumber", "5123456788");
		queryParam.put("status","DELETION_APPROVED");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan with status DELETION APPROVED" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}
	@Test(priority = 0, description = "Ods Search Plan with status INACTIVE", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_023_OdsSearchPlanWithStatusINACTIVE() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "1");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10001");
		queryParam.put("creatorMobileNumber", "5123456788");
		queryParam.put("status","INACTIVE");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan with status INACTIVE" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 200);
	}	
	@Test(priority = 0, description = "Ods Search Plan with incorrect params", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_024_OdsSearchPlanWithIncorrectParams() throws ParseException {
		OdsSearchPlan odsSearchPlan = new OdsSearchPlan(P.TESTDATA.get("OdsSearchPlanPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pageNo", "abs");
		queryParam.put("pageSize", "10");
		queryParam.put("serviceCategory","1");
		queryParam.put("subserviceCategory","10001");
		queryParam.put("creatorMobileNumber", "5123456788");
		queryParam.put("status","INACTIVE");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsSearchPlanMethod(odsSearchPlan, queryParam, headers);
		LOGGER.info("Ods Search Plan with incorrect param" + respObj.statusCode());
		
		Assert.assertEquals(respObj.statusCode(), 400);
	}	
}