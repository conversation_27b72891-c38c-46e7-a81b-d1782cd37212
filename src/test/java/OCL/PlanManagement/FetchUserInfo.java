package OCL.PlanManagement;

import Request.ODS.v1.OdsUserInfo;
import Services.ODS.OdsMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class FetchUserInfo extends BaseMethod {

	OdsMiddlewareServices odsMiddlewareServicesObject = new OdsMiddlewareServices();

	private static final Logger LOGGER = LogManager.getLogger(FetchUserInfo.class);

	public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
    String encodedXMWExToken = findXMWExTokenforPanel("5123456788","paytm@123");
		
	
	
	@Test(priority = 0, description = "Fetch Ods User Info", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_001_FetchOdsUserInfoStatusCode() {
		OdsUserInfo odsUserInfo = new OdsUserInfo(P.TESTDATA.get("OdsUserInfoPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("custId", "1000540528");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsuserdetailsMethod(odsUserInfo, queryParam, headers);
		LOGGER.info("Fetch Ods User Info" + respObj.statusCode());
		Assert.assertEquals(respObj.statusCode(), 200);
	}

	@Test(priority = 0, description = "Fetch Ods User Info With Wrong Token", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_002_FetchOdsUserInfoWithWrongToken() {
		OdsUserInfo odsUserInfo = new OdsUserInfo(P.TESTDATA.get("OdsUserInfoPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("custId", "1000540528");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", "acccvv");
		Response respObj = null;
		try {
			respObj = odsMiddlewareServicesObject.odsuserdetailsMethod(odsUserInfo, queryParam, headers);

		} catch (PatternSyntaxException e) {

		}
		if (respObj != null) {
			LOGGER.info("Fetch Ods Categories Response" + respObj.statusCode());
			Assert.assertEquals(respObj.statusCode(), 401);
		}
	}

	@Test(priority = 0, description = "Fetch Ods User Info Check Result Code Success", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_003_FetchOdsUserInfoCheckResultMsgSuccess() throws ParseException {
		OdsUserInfo odsUserInfo = new OdsUserInfo(P.TESTDATA.get("OdsCategoriesPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("custId", "1000540528");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsuserdetailsMethod(odsUserInfo, queryParam, headers);
		LOGGER.info("Fetch Ods User Info Check Result Code Success Response" + respObj.statusCode());

		respObj.getBody();

		JSONParser parser = new JSONParser();

		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

		JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");

		LOGGER.info("Fetch Ods User Info Check Result Code Success Response" + respObj.statusCode());

		Assert.assertEquals(jsObj2.get("resultCode"), "SUCCESS");
	}

	@Test(priority = 0, description = "Fetch Ods User Info Check Result Status Success", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_004_FetchOdsUserInfoCheckResultStatusSuccess() throws ParseException {
		OdsUserInfo odsUserInfo = new OdsUserInfo(P.TESTDATA.get("OdsCategoriesPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("custId", "1000540528");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

		Response respObj = odsMiddlewareServicesObject.odsuserdetailsMethod(odsUserInfo, queryParam, headers);
		LOGGER.info("Fetch Ods User Info Check Result Status Success Response" + respObj.statusCode());

		respObj.getBody();

		JSONParser parser = new JSONParser();

		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

		JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");

		LOGGER.info("Fetch Ods User Info Check Result Status Success Response" + respObj.statusCode());

		Assert.assertEquals(jsObj2.get("resultStatus"), "S");
	}

	@Test(priority = 0, description = "Fetch Ods User Info Check Result Code Id ", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_005_FetchOdsUserInfoCheckResultCodeId() throws ParseException {
		OdsUserInfo odsUserInfo = new OdsUserInfo(P.TESTDATA.get("OdsCategoriesPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("custId", "1000540528");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

		Response respObj = odsMiddlewareServicesObject.odsuserdetailsMethod(odsUserInfo, queryParam, headers);
		LOGGER.info("Fetch Ods User Info Check Result Code Id  Response" + respObj.statusCode());

		respObj.getBody();

		JSONParser parser = new JSONParser();

		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

		JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");

		LOGGER.info("Fetch Ods User Info Check Result Code Id Success Response" + respObj.statusCode());

		Assert.assertEquals(jsObj2.get("resultCodeId"), "01");
	}

	@Test(priority = 0, description = "Fetch Ods User Info Check Phone For Cust Id", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_006_FetchOdsUserInfoCheckPhoneForCustid() throws ParseException {
		OdsUserInfo odsUserInfo = new OdsUserInfo(P.TESTDATA.get("OdsCategoriesPath"));

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("custId", "1000540528");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);
		Response respObj = odsMiddlewareServicesObject.odsuserdetailsMethod(odsUserInfo, queryParam, headers);
		LOGGER.info("Fetch Ods User Info Check Phone For Given CustId In Response" + respObj.statusCode());

	/*	respObj.getBody();

		JSONParser parser = new JSONParser();

		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

		JSONObject jsObj2 = (JSONObject) jsObj1.get("data"); */

		LOGGER.info("Fetch Ods User Info Check Result Code Success Response" + respObj.statusCode());

		//Assert.assertTrue(jsObj2.get("phone").toString().contains("9917"));
	}

	@Test(priority = 0, description = "Fetch Ods User Info Check firstName For Cust Id", groups = {
	"Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_007_FetchOdsUserInfoCheckfirstNameForCustid() throws ParseException {
OdsUserInfo odsUserInfo = new OdsUserInfo(P.TESTDATA.get("OdsCategoriesPath"));

Map<String, String> queryParam = new HashMap<String, String>();
queryParam.put("custId", "1000540528");

Map<String, String> headers = new HashMap<String, String>();
headers.put("Content-Type", "application/json");
headers.put("X-MW-TOKEN-EX",encodedXMWExToken);
Response respObj = odsMiddlewareServicesObject.odsuserdetailsMethod(odsUserInfo,queryParam, headers);
LOGGER.info("Fetch Ods User Info Check firstName For Given CustId In Response" + respObj.statusCode());

respObj.getBody();

JSONParser parser = new JSONParser();

JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

JSONObject jsObj2 = (JSONObject) jsObj1.get("data");

LOGGER.info("Fetch Ods User Info Check firstName in Response" + respObj.statusCode());

Assert.assertEquals(jsObj2.get("firstName"), "");
}
	@Test(priority = 0, description = "Fetch Ods User Info Check lastName For Cust Id", groups = {
	"Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_008_FetchOdsUserInfoChecklastNameForCustid() throws ParseException {
OdsUserInfo odsUserInfo = new OdsUserInfo(P.TESTDATA.get("OdsCategoriesPath"));

Map<String, String> queryParam = new HashMap<String, String>();
queryParam.put("custId", "1000540528");

Map<String, String> headers = new HashMap<String, String>();
headers.put("Content-Type", "application/json");
headers.put("X-MW-TOKEN-EX",encodedXMWExToken);
Response respObj = odsMiddlewareServicesObject.odsuserdetailsMethod(odsUserInfo,queryParam, headers);
LOGGER.info("Fetch Ods User Info Check lastName For Given CustId In Response" + respObj.statusCode());

respObj.getBody();

JSONParser parser = new JSONParser();

JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

JSONObject jsObj2 = (JSONObject) jsObj1.get("data");

LOGGER.info("Fetch Ods User Info Check lastName in Response" + respObj.statusCode());

Assert.assertEquals(jsObj2.get("lastName"), "");
}
	@Test(priority = 0, description = "Fetch Ods User Info Status Code With Wrong Cust Id", groups = {
	"Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_009_FetchOdsUserInfoStatusCodeWithWrongCustId() {
OdsUserInfo odsUserInfo = new OdsUserInfo(P.TESTDATA.get("OdsUserInfoPath"));

Map<String, String> queryParam = new HashMap<String, String>();
queryParam.put("custId", "8000540528");

Map<String, String> headers = new HashMap<String, String>();
headers.put("Content-Type", "application/json");
headers.put("X-MW-TOKEN-EX",encodedXMWExToken);
Response respObj = odsMiddlewareServicesObject.odsuserdetailsMethod(odsUserInfo,queryParam, headers);
LOGGER.info("Fetch Ods User Info" + respObj.statusCode());
Assert.assertEquals(respObj.statusCode(),400);
}
	@Test(priority = 0, description = "Fetch Ods User Info Check Result Status With Wrong CustId", groups = {
	"Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_010_FetchOdsUserInfoCheckResultStatusWithWrongCustId() throws ParseException {
OdsUserInfo odsUserInfo = new OdsUserInfo(P.TESTDATA.get("OdsCategoriesPath"));

Map<String, String> queryParam = new HashMap<String, String>();
queryParam.put("custId", "8000540528");

Map<String, String> headers = new HashMap<String, String>();
headers.put("Content-Type", "application/json");
headers.put("X-MW-TOKEN-EX",encodedXMWExToken);
Response respObj = odsMiddlewareServicesObject.odsuserdetailsMethod(odsUserInfo,queryParam, headers);
LOGGER.info("Fetch Ods User Info Check Result Status With Wrong CustId" + respObj.statusCode());

respObj.getBody();

JSONParser parser = new JSONParser();

JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");

LOGGER.info("Fetch Ods User Info Check Result Status With Wrong CustId" + respObj.statusCode());

Assert.assertEquals(jsObj2.get("resultStatus"), "F");
}
	@Test(priority = 0, description = "Fetch Ods User Info Check Result Code With Wrong CustId", groups = {
	"Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_011_FetchOdsUserInfoCheckResultCodeWithWrongCustId() throws ParseException {
OdsUserInfo odsUserInfo = new OdsUserInfo(P.TESTDATA.get("OdsCategoriesPath"));

Map<String, String> queryParam = new HashMap<String, String>();
queryParam.put("custId", "8000540528");

Map<String, String> headers = new HashMap<String, String>();
headers.put("Content-Type", "application/json");
headers.put("X-MW-TOKEN-EX",encodedXMWExToken);
Response respObj = odsMiddlewareServicesObject.odsuserdetailsMethod(odsUserInfo,queryParam, headers);
LOGGER.info("Fetch Ods User Info Check Check Result Code With Wrong CustId" + respObj.statusCode());

respObj.getBody();

JSONParser parser = new JSONParser();

JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");

LOGGER.info("Fetch Ods User Info Check Result Code With Wrong CustId" + respObj.statusCode());

Assert.assertEquals(jsObj2.get("resultCode"), "BUSINESS_EXCEPTION");
}
	@Test(priority = 0, description = "Fetch Ods User Info Check Result CodeId With Wrong CustId", groups = {
	"Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_012_FetchOdsUserInfoCheckResultCodeIdWithWrongCustId() throws ParseException {
OdsUserInfo odsUserInfo = new OdsUserInfo(P.TESTDATA.get("OdsCategoriesPath"));

Map<String, String> queryParam = new HashMap<String, String>();
queryParam.put("custId", "8000540528");

Map<String, String> headers = new HashMap<String, String>();
headers.put("Content-Type", "application/json");
headers.put("X-MW-TOKEN-EX",encodedXMWExToken);
Response respObj = odsMiddlewareServicesObject.odsuserdetailsMethod(odsUserInfo,queryParam, headers);
LOGGER.info("Fetch Ods User Info Check Check Result CodeId With Wrong CustId" + respObj.statusCode());

respObj.getBody();

JSONParser parser = new JSONParser();

JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");

LOGGER.info("Fetch Ods User Info Check Result CodeId With Wrong CustId" + respObj.statusCode());

Assert.assertEquals(jsObj2.get("resultCodeId"), "403");
}
	@Test(priority = 0, description = "Fetch Ods User Info Status Code With Invalid Cust Id Having Less Digits", groups = {
	"Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_013_FetchOdsUserInfoStatusCodeWithInvalidCustId() {
OdsUserInfo odsUserInfo = new OdsUserInfo(P.TESTDATA.get("OdsUserInfoPath"));

Map<String, String> queryParam = new HashMap<String, String>();
queryParam.put("custId", "100054052");

Map<String, String> headers = new HashMap<String, String>();
headers.put("Content-Type", "application/json");
headers.put("X-MW-TOKEN-EX",encodedXMWExToken);
Response respObj = odsMiddlewareServicesObject.odsuserdetailsMethod(odsUserInfo,queryParam, headers);
LOGGER.info("Fetch Ods User Info" + respObj.statusCode());
Assert.assertEquals(respObj.statusCode(),400);
}
	@Test(priority = 0, description = "Fetch Ods User Info Without Cust Id", groups = {
	"Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_014_FetchOdsUserInfoWithoutCustId() {
OdsUserInfo odsUserInfo = new OdsUserInfo(P.TESTDATA.get("OdsUserInfoPath"));

Map<String, String> queryParam = new HashMap<String, String>();
queryParam.put("custId", "");

Map<String, String> headers = new HashMap<String, String>();
headers.put("Content-Type", "application/json");
headers.put("X-MW-TOKEN-EX",encodedXMWExToken);
Response respObj = odsMiddlewareServicesObject.odsuserdetailsMethod(odsUserInfo,queryParam, headers);
LOGGER.info("Fetch Ods User Info" + respObj.statusCode());
Assert.assertEquals(respObj.statusCode(),400);
}
	@Test(priority = 0, description = "Fetch Ods User Info With Cust Id In Alphanumeric Form ", groups = {
	"Regression" })
@Owner(emailId = "<EMAIL>", isAutomated = true)
public void TC_015_FetchOdsUserInfoWithCustIdInAlphanumericForm() {
OdsUserInfo odsUserInfo = new OdsUserInfo(P.TESTDATA.get("OdsUserInfoPath"));

Map<String, String> queryParam = new HashMap<String, String>();
queryParam.put("custId", "100d5c05A8");

Map<String, String> headers = new HashMap<String, String>();
headers.put("Content-Type", "application/json");
headers.put("X-MW-TOKEN-EX",encodedXMWExToken);
Response respObj = odsMiddlewareServicesObject.odsuserdetailsMethod(odsUserInfo,queryParam, headers);
LOGGER.info("Fetch Ods User Info" + respObj.statusCode());
Assert.assertEquals(respObj.statusCode(),400);
}
	
}
