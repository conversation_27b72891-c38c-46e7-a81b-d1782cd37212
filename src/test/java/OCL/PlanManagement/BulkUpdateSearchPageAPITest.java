package OCL.PlanManagement;

import Request.ODS.BulkUpdateSearchPageAPI;
import Services.ODS.ODSPlan;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class BulkUpdateSearchPageAPITest extends BaseMethod {

    ODSPlan ODSPlanObject=new ODSPlan();
    private static final Logger LOGGER = LogManager.getLogger(BulkUpdateSearchPageAPITest.class);

    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
    String encodedXMWExToken = findXMWExTokenforPanel("7771216290","paytm@123");

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC Onboarding Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_ODSBulkUpdateRequestSearchForEDC_Onboarding() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "1");
        body.put("serviceItemId", "10001");
        body.put("planType", "onboarding");
        body.put("status", "COMPLETED");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC plan upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_ODSBulkUpdateRequestSearchForEDC_PlanUpgrade() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "1");
        body.put("serviceItemId", "10001");
        body.put("planType", "plan_upgrade");
        body.put("status", "COMPLETED");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC plan upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_ODSBulkUpdateRequestSearchForEDC_DeviceUpgrade() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "1");
        body.put("serviceItemId", "10001");
        body.put("planType", "device_upgrade");
        body.put("status", "COMPLETED");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC plan upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_ODSBulkUpdateRequestSearchForEDC_Addon() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "1");
        body.put("serviceItemId", "10001");
        body.put("planType", "add_ons");
        body.put("subPlanTypes", "brand_emi");
        body.put("status", "COMPLETED");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC plan upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_ODSBulkUpdateRequestSearchForSB_Onboarding() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "2");
        body.put("serviceItemId", "10010");
        body.put("planType", "onboarding");
        body.put("status", "COMPLETED");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC plan upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_ODSBulkUpdateRequestSearchForSB_DeviceUpgrade() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "2");
        body.put("serviceItemId", "10010");
        body.put("planType", "device_upgrade");
        body.put("status", "COMPLETED");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC plan upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_ODSBulkUpdateRequestSearchForSB_PaidReplacement() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "2");
        body.put("serviceItemId", "10010");
        body.put("planType", "paid_replacement");
        body.put("status", "COMPLETED");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC plan upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_ODSBulkUpdateRequestSearchForSB_AddonPremiumCare() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "2");
        body.put("serviceItemId", "10010");
        body.put("planType", "add_ons");
        body.put("subPlanTypes", "premium_care");
        body.put("status", "COMPLETED");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC plan upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_ODSBulkUpdateRequestSearchForSB_AddonAMC() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "2");
        body.put("serviceItemId", "10010");
        body.put("planType", "add_ons");
        body.put("subPlanTypes", "amc");
        body.put("status", "COMPLETED");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC plan upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_ODSBulkUpdateRequestSearchForSB_AddonPremiumCareAndAMC() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "2");
        body.put("serviceItemId", "10010");
        body.put("planType", "add_ons");
        body.put("subPlanTypes", "premium_care,amc");
        body.put("creatorEmailId", "");
        body.put("endDate", "");
        body.put("requestId", "");
        body.put("status", "COMPLETED");


        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC plan upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11_ODSBulkUpdateRequestSearchForTapNPay_Onboarding() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "3");
        body.put("serviceItemId", "10019");
        body.put("planType", "onboarding");
        body.put("status", "COMPLETED");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC plan upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12_ODSBulkUpdateRequestSearchWithRequesterEmail() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "1");
        body.put("serviceItemId", "10001");
        body.put("planType", "onboarding");
        body.put("status", "COMPLETED");
        body.put("creatorEmailId","<EMAIL>");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC plan upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13_ODSBulkUpdateRequestSearch_IncorrectServiceCategoryID() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "111");
        body.put("serviceItemId", "10001");
        body.put("planType", "device_upgrade");
        body.put("status", "COMPLETED");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC plan upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_14_ODSBulkUpdateRequestSearch_IncorrectPlanType() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "1");
        body.put("serviceItemId", "10001");
        body.put("planType", "onboarding123");
        body.put("status", "COMPLETED");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC plan upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_15_ODSBulkUpdateRequestSearch_IncorrectServiceItem() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "1");
        body.put("serviceItemId", "100786");
        body.put("planType", "device_upgrade");
        body.put("status", "COMPLETED");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC plan upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13_ODSBulkUpdateRequestSearch_IncorrectStatus() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "1");
        body.put("serviceItemId", "10001");
        body.put("planType", "device_upgrade");
        body.put("status", "COMPLETED");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Bulk Update Request Search for EDC plan upgrade Plans")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13_ODSBulkUpdateRequestSearch_IncorrectSubPlanType() {

        BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI = new BulkUpdateSearchPageAPI(P.TESTDATA.get("BulkUpdateSearchPageAPI"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("pageNo", "1");
        body.put("pageSize", "10");
        body.put("serviceCategoryId", "2");
        body.put("serviceItemId", "10010");
        body.put("planType", "add_ons");
        body.put("subPlanTypes", "amc123");
        body.put("status", "COMPLETED");

        //    String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response FetchPlanObjResponse = ODSPlanObject.bulkUpdateSearchPageAPIMiddlewareFunction(bulkUpdateSearchPageAPI,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }


}
