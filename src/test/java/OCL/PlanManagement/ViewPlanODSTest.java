package OCL.PlanManagement;

import Request.ODS.FetchPlanODS;
import Request.ODS.ViewPlanODS;
import Services.ODS.ODSPlan;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;

public class ViewPlanODSTest extends BaseMethod {

    ODSPlan ODSPlanObject = new ODSPlan();
    private static final Logger LOGGER = LogManager.getLogger(ViewPlanODSTest.class);

    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
    String encodedXMWExToken = findXMWExTokenforPanel("5123456788", "paytm@123");

    @Test(priority = 0, groups = {"Regression"}, description = "ODS View Plan ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_ODSViewPlan() {

        ViewPlanODS odsObj = new ViewPlanODS(P.TESTDATA.get("ViewPlanBasic"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("planId", "10452");

        Map<String, String> body = new HashMap<String, String>();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsViewPlan(odsObj, queryParam, headers, body);
        int StatusCode = CreatePlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Verifying if each key is present or not in an EDC - Draft plan inside the Key - Data")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_ODSViewPlan() throws JsonProcessingException {

        ViewPlanODS odsObj = new ViewPlanODS(P.TESTDATA.get("ViewPlanBasic"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("planId", "14036");

        Map<String, String> body = new HashMap<String, String>();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsViewPlan(odsObj, queryParam, headers, body);

        String resp = CreatePlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(resp);
        System.out.println(jsonNode);
        JsonNode dataNode = jsonNode.path("data");
        boolean serviceCategoryId = dataNode.has("serviceCategoryId");
        boolean subServiceCategoryId = dataNode.has("subServiceCategoryId");
        boolean planId = dataNode.has("planId");
        boolean planName = dataNode.has("planName");
        boolean planType = dataNode.has("planType");
        boolean subPlanType = dataNode.has("subPlanType");
        boolean serviceCategory = dataNode.has("serviceCategory");
        boolean subServiceCategory = dataNode.has("subServiceCategory");
        boolean planStatus = dataNode.has("planStatus");
        boolean alias = dataNode.has("alias");
        boolean isGeneric = dataNode.has("isGeneric");
        boolean referenceServicePlanId = dataNode.has("referenceServicePlanId");
        boolean bulkUpdateRequestId = dataNode.has("bulkUpdateRequestId");
        boolean isEligibleForPlanUpgrade = dataNode.has("isEligibleForPlanUpgrade");
        boolean fseAndMerchantDetailSRO = dataNode.has("fseAndMerchantDetailSRO");
        boolean refundConfiguration = dataNode.has("refundConfiguration");
        boolean mdrDetailSROList = dataNode.has("mdrDetailSROList");
        boolean pricingComponents = dataNode.has("pricingComponents");
        boolean mandatoryPricingComponents = dataNode.has("mandatoryPricingComponents");
        boolean pricingComponentFieldMap = dataNode.has("pricingComponentFieldMap");
        boolean pricingComponentGroupId = dataNode.has("pricingComponentGroupId");
        boolean pricingComponentLabels = dataNode.has("pricingComponentLabels");
        boolean taxApplicableValues = dataNode.has("taxApplicableValues");
        boolean additionalInfo = dataNode.has("additionalInfo");
        boolean additionalInfoValueList = dataNode.has("additionalInfoValueList");
        boolean additionalInfoMap = dataNode.has("additionalInfoMap");
        boolean additionalInfoDisplayOrder = dataNode.has("additionalInfoDisplayOrder");
        boolean sectionList = dataNode.has("sectionList");
        boolean pricingComponentValues = dataNode.has("pricingComponentValues");
        boolean pricingComponentAttributeValueList = dataNode.has("pricingComponentAttributeValueList");
        boolean pricingComponentAttributeDisplayOrderMap = dataNode.has("pricingComponentAttributeDisplayOrderMap");
        boolean planLockedByBulkUpdateRequest = dataNode.has("planLockedByBulkUpdateRequest");
        Assert.assertTrue(serviceCategoryId);
        Assert.assertTrue(subServiceCategoryId);
        Assert.assertTrue(planId);
        Assert.assertTrue(planName);
        Assert.assertTrue(planType);
        Assert.assertTrue(subPlanType);
        Assert.assertTrue(subServiceCategory);
        Assert.assertTrue(serviceCategory);
        Assert.assertTrue(planStatus);
        Assert.assertTrue(alias);
        Assert.assertTrue(isGeneric);
        Assert.assertTrue(referenceServicePlanId);
        Assert.assertTrue(bulkUpdateRequestId);
        Assert.assertTrue(isEligibleForPlanUpgrade);
        Assert.assertTrue(fseAndMerchantDetailSRO);
        Assert.assertTrue(refundConfiguration);
        Assert.assertTrue(mdrDetailSROList);
        Assert.assertTrue(pricingComponents);
        Assert.assertTrue(mandatoryPricingComponents);
        Assert.assertTrue(pricingComponentFieldMap);
        Assert.assertTrue(pricingComponentGroupId);
        Assert.assertTrue(pricingComponentLabels);
        Assert.assertTrue(taxApplicableValues);
        Assert.assertTrue(additionalInfo);
        Assert.assertTrue(additionalInfoValueList);
        Assert.assertTrue(additionalInfoMap);
        Assert.assertTrue(additionalInfoDisplayOrder);
        Assert.assertTrue(sectionList);
        Assert.assertTrue(pricingComponentValues);
        Assert.assertTrue(pricingComponentAttributeValueList);
        Assert.assertTrue(pricingComponentAttributeDisplayOrderMap);
        Assert.assertTrue(planLockedByBulkUpdateRequest);
        System.out.println(dataNode);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "Verifying if each key is present or not in an EDC - Draft plan inside the Key - mdrDetails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_ODSViewPlan() throws JsonProcessingException {

        ViewPlanODS odsObj = new ViewPlanODS(P.TESTDATA.get("ViewPlanBasic"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("planId", "14036");

        Map<String, String> body = new HashMap<String, String>();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsViewPlan(odsObj, queryParam, headers, body);

        String resp = CreatePlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(resp);
        JsonNode mdrDetailSROList = jsonNode.path("data").path("mdrDetailSROList").get(0).path("mdrDetails").get(0);
        boolean paymentModeId = mdrDetailSROList.has("paymentModeId");
        boolean paymentSubModeId = mdrDetailSROList.has("paymentSubModeId");
        boolean paymentModeDisplayName = mdrDetailSROList.has("paymentModeDisplayName");
        boolean paymentModeSlug = mdrDetailSROList.has("paymentModeSlug");
        boolean paymentSubModeDisplayName = mdrDetailSROList.has("paymentSubModeDisplayName");
        boolean paymentSubModeSlug = mdrDetailSROList.has("paymentSubModeSlug");
        boolean feeType = mdrDetailSROList.has("feeType");
        boolean mdrCommissions = mdrDetailSROList.has("mdrCommissions");

        Assert.assertTrue(paymentModeId);
        Assert.assertTrue(paymentSubModeId);
        Assert.assertTrue(paymentModeDisplayName);
        Assert.assertTrue(paymentModeSlug);
        Assert.assertTrue(paymentSubModeDisplayName);
        Assert.assertTrue(paymentSubModeSlug);
        Assert.assertTrue(feeType);
        Assert.assertTrue(mdrCommissions);

    }
    @Test(priority = 0, groups = {"Regression"}, description = "Verifying if each key is present or not in an EDC - Draft plan inside the Key - pricingComponents")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_ODSViewPlan() throws JsonProcessingException {

        ViewPlanODS odsObj = new ViewPlanODS(P.TESTDATA.get("ViewPlanBasic"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("planId", "14036");

        Map<String, String> body = new HashMap<String, String>();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsViewPlan(odsObj, queryParam, headers, body);

        String resp = CreatePlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(resp);

        JsonNode pricingComponents = jsonNode.path("data").path("pricingComponents");

        for(JsonNode pricingComponent : pricingComponents) {
            String component = pricingComponent.get("name").asText();
            if(component.equals("amc")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                boolean initialDeductionIntervalCount = pricingData.has("initialDeductionIntervalCount");
                boolean initialDeductionIntervalFormat = pricingData.has("initialDeductionIntervalFormat");
                boolean deductionInitiationDate = pricingData.has("deductionInitiationDate");

                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
                Assert.assertTrue(initialDeductionIntervalCount);
                Assert.assertTrue(initialDeductionIntervalFormat);
                Assert.assertTrue(deductionInitiationDate);
            }
            if(component.equals("usageDeposit")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
            }
            if(component.equals("installationCharge")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
            }
            if(component.equals("emiRentalCharge")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                boolean initialDeductionIntervalCount = pricingData.has("initialDeductionIntervalCount");
                boolean initialDeductionIntervalFormat = pricingData.has("initialDeductionIntervalFormat");
                boolean deductionInitiationDate = pricingData.has("deductionInitiationDate");

                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
                Assert.assertTrue(initialDeductionIntervalCount);
                Assert.assertTrue(initialDeductionIntervalFormat);
                Assert.assertTrue(deductionInitiationDate);
            }
            if(component.equals("emiOneTimeCharge")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
            }
            if(component.equals("rentalAmount")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                boolean initialDeductionIntervalCount = pricingData.has("initialDeductionIntervalCount");
                boolean initialDeductionIntervalFormat = pricingData.has("initialDeductionIntervalFormat");
                boolean deductionInitiationDate = pricingData.has("deductionInitiationDate");

              //  Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
                Assert.assertTrue(initialDeductionIntervalCount);
                Assert.assertTrue(initialDeductionIntervalFormat);
                Assert.assertTrue(deductionInitiationDate);
            }
            if(component.equals("lowUsageCharge")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean initialDeductionIntervalCount = pricingData.has("initialDeductionIntervalCount");
                boolean initialDeductionIntervalFormat = pricingData.has("initialDeductionIntervalFormat");
                boolean deductionInitiationDate = pricingData.has("deductionInitiationDate");

                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(initialDeductionIntervalCount);
                Assert.assertTrue(initialDeductionIntervalFormat);
                Assert.assertTrue(deductionInitiationDate);
            }
        }
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Verifying if each key is present or not in an EDC - Pending plan inside the Key - Data")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_ODSViewPlan() throws JsonProcessingException {

        ViewPlanODS odsObj = new ViewPlanODS(P.TESTDATA.get("ViewPlanBasic"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("planId", "10381");

        Map<String, String> body = new HashMap<String, String>();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsViewPlan(odsObj, queryParam, headers, body);

        String resp = CreatePlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(resp);
        System.out.println(jsonNode);
        JsonNode dataNode = jsonNode.path("data");
        boolean serviceCategoryId = dataNode.has("serviceCategoryId");
        boolean subServiceCategoryId = dataNode.has("subServiceCategoryId");
        boolean planId = dataNode.has("planId");
        boolean planName = dataNode.has("planName");
        boolean planType = dataNode.has("planType");
        boolean subPlanType = dataNode.has("subPlanType");
        boolean serviceCategory = dataNode.has("serviceCategory");
        boolean subServiceCategory = dataNode.has("subServiceCategory");
        boolean planStatus = dataNode.has("planStatus");
        boolean alias = dataNode.has("alias");
        boolean isGeneric = dataNode.has("isGeneric");
        boolean referenceServicePlanId = dataNode.has("referenceServicePlanId");
        boolean bulkUpdateRequestId = dataNode.has("bulkUpdateRequestId");
        boolean isEligibleForPlanUpgrade = dataNode.has("isEligibleForPlanUpgrade");
        boolean fseAndMerchantDetailSRO = dataNode.has("fseAndMerchantDetailSRO");
        boolean refundConfiguration = dataNode.has("refundConfiguration");
        boolean mdrDetailSROList = dataNode.has("mdrDetailSROList");
        boolean pricingComponents = dataNode.has("pricingComponents");
        boolean mandatoryPricingComponents = dataNode.has("mandatoryPricingComponents");
        boolean pricingComponentFieldMap = dataNode.has("pricingComponentFieldMap");
        boolean pricingComponentGroupId = dataNode.has("pricingComponentGroupId");
        boolean pricingComponentLabels = dataNode.has("pricingComponentLabels");
        boolean taxApplicableValues = dataNode.has("taxApplicableValues");
        boolean additionalInfo = dataNode.has("additionalInfo");
        boolean additionalInfoValueList = dataNode.has("additionalInfoValueList");
        boolean additionalInfoMap = dataNode.has("additionalInfoMap");
        boolean additionalInfoDisplayOrder = dataNode.has("additionalInfoDisplayOrder");
        boolean sectionList = dataNode.has("sectionList");
        boolean pricingComponentValues = dataNode.has("pricingComponentValues");
        boolean pricingComponentAttributeValueList = dataNode.has("pricingComponentAttributeValueList");
        boolean pricingComponentAttributeDisplayOrderMap = dataNode.has("pricingComponentAttributeDisplayOrderMap");
        boolean planLockedByBulkUpdateRequest = dataNode.has("planLockedByBulkUpdateRequest");
        Assert.assertTrue(serviceCategoryId);
        Assert.assertTrue(subServiceCategoryId);
        Assert.assertTrue(planId);
        Assert.assertTrue(planName);
        Assert.assertTrue(planType);
        Assert.assertTrue(subPlanType);
        Assert.assertTrue(subServiceCategory);
        Assert.assertTrue(serviceCategory);
        Assert.assertTrue(planStatus);
        Assert.assertTrue(alias);
        Assert.assertTrue(isGeneric);
        Assert.assertTrue(referenceServicePlanId);
        Assert.assertTrue(bulkUpdateRequestId);
        Assert.assertTrue(isEligibleForPlanUpgrade);
        Assert.assertTrue(fseAndMerchantDetailSRO);
        Assert.assertTrue(mdrDetailSROList);
        Assert.assertTrue(pricingComponents);
        Assert.assertTrue(mandatoryPricingComponents);
        Assert.assertTrue(pricingComponentFieldMap);
        Assert.assertTrue(pricingComponentGroupId);
        Assert.assertTrue(pricingComponentLabels);
        Assert.assertTrue(taxApplicableValues);
        Assert.assertTrue(additionalInfo);
        Assert.assertTrue(additionalInfoValueList);
        Assert.assertTrue(additionalInfoMap);
        Assert.assertTrue(additionalInfoDisplayOrder);
        Assert.assertTrue(sectionList);
        Assert.assertTrue(pricingComponentValues);
        Assert.assertTrue(pricingComponentAttributeValueList);
        Assert.assertTrue(pricingComponentAttributeDisplayOrderMap);
        Assert.assertTrue(planLockedByBulkUpdateRequest);
        System.out.println(dataNode);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "Verifying if each key is present or not in an EDC - Pending plan inside the Key - mdrDetails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_ODSViewPlan() throws JsonProcessingException {

        ViewPlanODS odsObj = new ViewPlanODS(P.TESTDATA.get("ViewPlanBasic"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("planId", "10381");

        Map<String, String> body = new HashMap<String, String>();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsViewPlan(odsObj, queryParam, headers, body);

        String resp = CreatePlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(resp);
        JsonNode mdrDetailSROList = jsonNode.path("data").path("mdrDetailSROList").get(0).path("mdrDetails").get(0);
        boolean paymentModeId = mdrDetailSROList.has("paymentModeId");
        boolean paymentSubModeId = mdrDetailSROList.has("paymentSubModeId");
        boolean paymentModeDisplayName = mdrDetailSROList.has("paymentModeDisplayName");
        boolean paymentModeSlug = mdrDetailSROList.has("paymentModeSlug");
        boolean paymentSubModeDisplayName = mdrDetailSROList.has("paymentSubModeDisplayName");
        boolean paymentSubModeSlug = mdrDetailSROList.has("paymentSubModeSlug");
        boolean feeType = mdrDetailSROList.has("feeType");
        boolean mdrCommissions = mdrDetailSROList.has("mdrCommissions");

        Assert.assertTrue(paymentModeId);
        Assert.assertTrue(paymentSubModeId);
        Assert.assertTrue(paymentModeDisplayName);
        Assert.assertTrue(paymentModeSlug);
        Assert.assertTrue(paymentSubModeDisplayName);
        Assert.assertTrue(paymentSubModeSlug);
        Assert.assertTrue(feeType);
        Assert.assertTrue(mdrCommissions);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "Verifying if each key is present or not in an EDC - Pending plan inside the Key - pricingComponents")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_ODSViewPlan() throws JsonProcessingException {

        ViewPlanODS odsObj = new ViewPlanODS(P.TESTDATA.get("ViewPlanBasic"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("planId", "10381");

        Map<String, String> body = new HashMap<String, String>();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsViewPlan(odsObj, queryParam, headers, body);

        String resp = CreatePlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(resp);

        JsonNode pricingComponents = jsonNode.path("data").path("pricingComponents");

        for(JsonNode pricingComponent : pricingComponents) {
            String component = pricingComponent.get("name").asText();
            if(component.equals("amc")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                boolean initialDeductionIntervalCount = pricingData.has("initialDeductionIntervalCount");
                boolean initialDeductionIntervalFormat = pricingData.has("initialDeductionIntervalFormat");
                boolean deductionInitiationDate = pricingData.has("deductionInitiationDate");

                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
                Assert.assertTrue(initialDeductionIntervalCount);
                Assert.assertTrue(initialDeductionIntervalFormat);
                Assert.assertTrue(deductionInitiationDate);
            }
            if(component.equals("usageDeposit")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
            }
            if(component.equals("installationCharge")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
            }
            if(component.equals("emiRentalCharge")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                boolean initialDeductionIntervalCount = pricingData.has("initialDeductionIntervalCount");
                boolean initialDeductionIntervalFormat = pricingData.has("initialDeductionIntervalFormat");
                boolean deductionInitiationDate = pricingData.has("deductionInitiationDate");

                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
                Assert.assertTrue(initialDeductionIntervalCount);
                Assert.assertTrue(initialDeductionIntervalFormat);
                Assert.assertTrue(deductionInitiationDate);
            }
            if(component.equals("emiOneTimeCharge")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
            }
            if(component.equals("rentalAmount")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                boolean initialDeductionIntervalCount = pricingData.has("initialDeductionIntervalCount");
                boolean initialDeductionIntervalFormat = pricingData.has("initialDeductionIntervalFormat");
                boolean deductionInitiationDate = pricingData.has("deductionInitiationDate");

               // Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
            }
            if(component.equals("lowUsageCharge")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean initialDeductionIntervalCount = pricingData.has("initialDeductionIntervalCount");
                boolean initialDeductionIntervalFormat = pricingData.has("initialDeductionIntervalFormat");
                boolean deductionInitiationDate = pricingData.has("deductionInitiationDate");

                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(initialDeductionIntervalCount);
                Assert.assertTrue(initialDeductionIntervalFormat);
                Assert.assertTrue(deductionInitiationDate);
            }
        }
    }
    @Test(priority = 0, groups = {"Regression"}, description = "Verifying if each key is present or not in an EDC - Active plan inside the Key - Data")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_ODSViewPlan() throws JsonProcessingException {

        ViewPlanODS odsObj = new ViewPlanODS(P.TESTDATA.get("ViewPlanBasic"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("planId", "14031");

        Map<String, String> body = new HashMap<String, String>();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsViewPlan(odsObj, queryParam, headers, body);

        String resp = CreatePlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(resp);
        System.out.println(jsonNode);
        JsonNode dataNode = jsonNode.path("data");
        boolean serviceCategoryId = dataNode.has("serviceCategoryId");
        boolean subServiceCategoryId = dataNode.has("subServiceCategoryId");
        boolean planId = dataNode.has("planId");
        boolean planName = dataNode.has("planName");
        boolean planType = dataNode.has("planType");
        boolean subPlanType = dataNode.has("subPlanType");
        boolean serviceCategory = dataNode.has("serviceCategory");
        boolean subServiceCategory = dataNode.has("subServiceCategory");
        boolean planStatus = dataNode.has("planStatus");
        boolean alias = dataNode.has("alias");
        boolean isGeneric = dataNode.has("isGeneric");
        boolean referenceServicePlanId = dataNode.has("referenceServicePlanId");
        boolean bulkUpdateRequestId = dataNode.has("bulkUpdateRequestId");
        boolean isEligibleForPlanUpgrade = dataNode.has("isEligibleForPlanUpgrade");
        boolean fseAndMerchantDetailSRO = dataNode.has("fseAndMerchantDetailSRO");
        boolean refundConfiguration = dataNode.has("refundConfiguration");
        boolean mdrDetailSROList = dataNode.has("mdrDetailSROList");
        boolean pricingComponents = dataNode.has("pricingComponents");
        boolean mandatoryPricingComponents = dataNode.has("mandatoryPricingComponents");
        boolean pricingComponentFieldMap = dataNode.has("pricingComponentFieldMap");
        boolean pricingComponentGroupId = dataNode.has("pricingComponentGroupId");
        boolean pricingComponentLabels = dataNode.has("pricingComponentLabels");
        boolean taxApplicableValues = dataNode.has("taxApplicableValues");
        boolean additionalInfo = dataNode.has("additionalInfo");
        boolean additionalInfoValueList = dataNode.has("additionalInfoValueList");
        boolean additionalInfoMap = dataNode.has("additionalInfoMap");
        boolean additionalInfoDisplayOrder = dataNode.has("additionalInfoDisplayOrder");
        boolean sectionList = dataNode.has("sectionList");
        boolean pricingComponentValues = dataNode.has("pricingComponentValues");
        boolean pricingComponentAttributeValueList = dataNode.has("pricingComponentAttributeValueList");
        boolean pricingComponentAttributeDisplayOrderMap = dataNode.has("pricingComponentAttributeDisplayOrderMap");
        boolean planLockedByBulkUpdateRequest = dataNode.has("planLockedByBulkUpdateRequest");
        Assert.assertTrue(serviceCategoryId);
        Assert.assertTrue(subServiceCategoryId);
        Assert.assertTrue(planId);
        Assert.assertTrue(planName);
        Assert.assertTrue(planType);
        Assert.assertTrue(subPlanType);
        Assert.assertTrue(subServiceCategory);
        Assert.assertTrue(serviceCategory);
        Assert.assertTrue(planStatus);
        Assert.assertTrue(alias);
        Assert.assertTrue(isGeneric);
        Assert.assertTrue(referenceServicePlanId);
        Assert.assertTrue(bulkUpdateRequestId);
        Assert.assertTrue(isEligibleForPlanUpgrade);
        Assert.assertTrue(fseAndMerchantDetailSRO);
        Assert.assertTrue(refundConfiguration);
        Assert.assertTrue(mdrDetailSROList);
        Assert.assertTrue(pricingComponents);
        Assert.assertTrue(mandatoryPricingComponents);
        Assert.assertTrue(pricingComponentFieldMap);
        Assert.assertTrue(pricingComponentGroupId);
        Assert.assertTrue(pricingComponentLabels);
        Assert.assertTrue(taxApplicableValues);
        Assert.assertTrue(additionalInfo);
        Assert.assertTrue(additionalInfoValueList);
        Assert.assertTrue(additionalInfoMap);
        Assert.assertTrue(additionalInfoDisplayOrder);
        Assert.assertTrue(sectionList);
        Assert.assertTrue(pricingComponentValues);
        Assert.assertTrue(pricingComponentAttributeValueList);
        Assert.assertTrue(pricingComponentAttributeDisplayOrderMap);
        Assert.assertTrue(planLockedByBulkUpdateRequest);
        System.out.println(dataNode);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "Verifying if each key is present or not in an EDC - Draft plan inside the Key - mdrDetails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_ODSViewPlan() throws JsonProcessingException {

        ViewPlanODS odsObj = new ViewPlanODS(P.TESTDATA.get("ViewPlanBasic"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("planId", "14031");

        Map<String, String> body = new HashMap<String, String>();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsViewPlan(odsObj, queryParam, headers, body);

        String resp = CreatePlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(resp);
        JsonNode mdrDetailSROList = jsonNode.path("data").path("mdrDetailSROList").get(0).path("mdrDetails").get(0);
        boolean paymentModeId = mdrDetailSROList.has("paymentModeId");
        boolean paymentSubModeId = mdrDetailSROList.has("paymentSubModeId");
        boolean paymentModeDisplayName = mdrDetailSROList.has("paymentModeDisplayName");
        boolean paymentModeSlug = mdrDetailSROList.has("paymentModeSlug");
        boolean paymentSubModeDisplayName = mdrDetailSROList.has("paymentSubModeDisplayName");
        boolean paymentSubModeSlug = mdrDetailSROList.has("paymentSubModeSlug");
        boolean feeType = mdrDetailSROList.has("feeType");
        boolean mdrCommissions = mdrDetailSROList.has("mdrCommissions");

        Assert.assertTrue(paymentModeId);
        Assert.assertTrue(paymentSubModeId);
        Assert.assertTrue(paymentModeDisplayName);
        Assert.assertTrue(paymentModeSlug);
        Assert.assertTrue(paymentSubModeDisplayName);
        Assert.assertTrue(paymentSubModeSlug);
        Assert.assertTrue(feeType);
        Assert.assertTrue(mdrCommissions);

    }
    @Test(priority = 0, groups = {"Regression"}, description = "Verifying if each key is present or not in an EDC - Draft plan inside the Key - pricingComponents")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_ODSViewPlan() throws JsonProcessingException {

        ViewPlanODS odsObj = new ViewPlanODS(P.TESTDATA.get("ViewPlanBasic"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("planId", "14031");

        Map<String, String> body = new HashMap<String, String>();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

        Response CreatePlanObjResponse = ODSPlanObject.odsViewPlan(odsObj, queryParam, headers, body);

        String resp = CreatePlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(resp);

        JsonNode pricingComponents = jsonNode.path("data").path("pricingComponents");

        for(JsonNode pricingComponent : pricingComponents) {
            String component = pricingComponent.get("name").asText();
            if(component.equals("amc")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                boolean initialDeductionIntervalCount = pricingData.has("initialDeductionIntervalCount");
                boolean initialDeductionIntervalFormat = pricingData.has("initialDeductionIntervalFormat");
                boolean deductionInitiationDate = pricingData.has("deductionInitiationDate");

                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
                Assert.assertTrue(initialDeductionIntervalCount);
                Assert.assertTrue(initialDeductionIntervalFormat);
                Assert.assertTrue(deductionInitiationDate);
            }
            if(component.equals("usageDeposit")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
            }
            if(component.equals("installationCharge")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
            }
            if(component.equals("emiRentalCharge")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                boolean initialDeductionIntervalCount = pricingData.has("initialDeductionIntervalCount");
                boolean initialDeductionIntervalFormat = pricingData.has("initialDeductionIntervalFormat");
                boolean deductionInitiationDate = pricingData.has("deductionInitiationDate");

                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
                Assert.assertTrue(initialDeductionIntervalCount);
                Assert.assertTrue(initialDeductionIntervalFormat);
                Assert.assertTrue(deductionInitiationDate);
            }
            if(component.equals("emiOneTimeCharge")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
            }
            if(component.equals("rentalAmount")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                boolean initialDeductionIntervalCount = pricingData.has("initialDeductionIntervalCount");
                boolean initialDeductionIntervalFormat = pricingData.has("initialDeductionIntervalFormat");
                boolean deductionInitiationDate = pricingData.has("deductionInitiationDate");

               // Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
                Assert.assertTrue(initialDeductionIntervalCount);
                Assert.assertTrue(initialDeductionIntervalFormat);
                Assert.assertTrue(deductionInitiationDate);
            }
            if(component.equals("lowUsageCharge")) {
                JsonNode pricingData = pricingComponent.path("data");
                boolean taxApplicable = pricingData.has("taxApplicable");
                boolean amount = pricingData.has("amount");
                boolean initialDeductionIntervalCount = pricingData.has("initialDeductionIntervalCount");
                boolean initialDeductionIntervalFormat = pricingData.has("initialDeductionIntervalFormat");
                boolean deductionInitiationDate = pricingData.has("deductionInitiationDate");

                Assert.assertTrue(taxApplicable);
                Assert.assertTrue(amount);
                Assert.assertTrue(initialDeductionIntervalCount);
                Assert.assertTrue(initialDeductionIntervalFormat);
                Assert.assertTrue(deductionInitiationDate);
            }
        }
    }

}
