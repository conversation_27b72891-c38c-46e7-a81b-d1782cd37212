package OCL.PlanManagement;

import Request.ODS.FetchApplicableFiltersAPI;
import Request.ODS.FetchFilterValuesAPI;
import Services.ODS.ODSPlan;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FetchFilterValuesAPITest extends BaseMethod {

    ODSPlan ODSPlanObject=new ODSPlan();
    private static final Logger LOGGER = LogManager.getLogger(FetchApplicableFiltersAPITestCases.class);

    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";


    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC Onboarding PID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_FetchFilterValuesForEDC_Onboarding_PID() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "pid");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC Onboarding AgentPermission")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_FetchFilterValuesForEDC_Onboarding_AgentPermission() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "agentPermission");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC Onboarding ppiLimit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_FetchFilterValuesForEDC_Onboarding_ppiLimit() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "ppiLimit");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC Onboarding DELAYED_SETTLEMENT")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_FetchFilterValuesForEDC_Onboarding_DELAYED_SETTLEMENT() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "delayedSettlement");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC Onboarding BUSINESS_WALLET")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_FetchFilterValuesForEDC_Onboarding_BUSINESS_WALLET() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "businessWallet");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC Onboarding MERCHANT_AGE_TYPE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_FetchFilterValuesForEDC_Onboarding_MERCHANT_AGE_TYPE() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "merchantAgeType");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC Onboarding MERCHANT_TYPE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_FetchFilterValuesForEDC_Onboarding_MERCHANT_TYPE() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "merchantType");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC Onboarding CATEGORY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_FetchFilterValuesForEDC_Onboarding_CATEGORY() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "category");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC device upgrade PID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_FetchFilterValuesForEDC_Device_Upgrade_PID() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "pid");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "device_upgrade");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC device upgrade AgentPermission")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_FetchFilterValuesForEDC_Device_Upgrade_AgentPermission() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "agentPermission");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "device_upgrade");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC device upgrade ppiLimit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11_FetchFilterValuesForEDC_Device_Upgrade_ppiLimit() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "ppiLimit");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "device_upgrade");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC device upgrade DELAYED_SETTLEMENT")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12_FetchFilterValuesForEDC_Device_Upgrade_DELAYED_SETTLEMENT() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "delayedSettlement");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "device_upgrade");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC device upgrade BUSINESS_WALLET")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13_FetchFilterValuesForEDC_Device_Upgrade_BUSINESS_WALLET() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "businessWallet");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "device_upgrade");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC device upgrade MERCHANT_AGE_TYPE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_14_FetchFilterValuesForEDC_Device_Upgrade_MERCHANT_AGE_TYPE() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "merchantAgeType");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "device_upgrade");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC device upgrade MERCHANT_TYPE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_15_FetchFilterValuesForEDC_Device_Upgrade_MERCHANT_TYPE() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "merchantType");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "device_upgrade");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC device upgrade CATEGORY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_16_FetchFilterValuesForEDC_Device_Upgrade_CATEGORY() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "category");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "device_upgrade");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @org.testng.annotations.Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC plan upgrade PID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_17_FetchFilterValuesForEDC_Plan_Upgrade_PID() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "pid");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "plan_upgrade");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @org.testng.annotations.Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC plan upgrade AgentPermission")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_18_FetchFilterValuesForEDC_Plan_Upgrade_AgentPermission() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "agentPermission");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "plan_upgrade");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @org.testng.annotations.Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC plan upgrade ppiLimit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_19_FetchFilterValuesForEDC_Plan_Upgrade_ppiLimit() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "ppiLimit");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "plan_upgrade");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @org.testng.annotations.Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC plan upgrade DELAYED_SETTLEMENT")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_20_FetchFilterValuesForEDC_Plan_Upgrade_DELAYED_SETTLEMENT() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "delayedSettlement");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "plan_upgrade");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @org.testng.annotations.Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC plan upgrade BUSINESS_WALLET")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_21_FetchFilterValuesForEDC_Plan_Upgrade_BUSINESS_WALLET() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "businessWallet");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "plan_upgrade");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @org.testng.annotations.Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC plan upgrade MERCHANT_AGE_TYPE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_22_FetchFilterValuesForEDC_Plan_Upgrade_MERCHANT_AGE_TYPE() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "merchantAgeType");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "plan_upgrade");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @org.testng.annotations.Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC plan upgrade MERCHANT_TYPE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_23_FetchFilterValuesForEDC_Plan_Upgrade_MERCHANT_TYPE() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "merchantType");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "plan_upgrade");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @org.testng.annotations.Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for EDC plan upgrade CATEGORY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_24_FetchFilterValuesForEDC_Plan_Upgrade_CATEGORY() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "category");
        queryParam.put("serviceCategory", "edc");
        queryParam.put("planType", "plan_upgrade");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for SB Onboarding PID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_25_FetchFilterValuesForSB_Onboarding_PID() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "pid");
        queryParam.put("serviceCategory", "soundbox");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for SB Onboarding AgentPermission")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_26_FetchFilterValuesForSB_Onboarding_AgentPermission() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "agentPermission");
        queryParam.put("serviceCategory", "soundbox");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for SB Onboarding ppiLimit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_27_FetchFilterValuesForSB_Onboarding_ppiLimit() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "ppiLimit");
        queryParam.put("serviceCategory", "soundbox");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for SB Onboarding DELAYED_SETTLEMENT")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_28_FetchFilterValuesForSB_Onboarding_DELAYED_SETTLEMENT() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "delayedSettlement");
        queryParam.put("serviceCategory", "soundbox");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for SB Onboarding BUSINESS_WALLET")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_29_FetchFilterValuesForSB_Onboarding_BUSINESS_WALLET() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "businessWallet");
        queryParam.put("serviceCategory", "soundbox");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for SB Onboarding MERCHANT_AGE_TYPE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_30_FetchFilterValuesForSB_Onboarding_MERCHANT_AGE_TYPE() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "merchantAgeType");
        queryParam.put("serviceCategory", "soundbox");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for SB Onboarding MERCHANT_TYPE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_31_FetchFilterValuesForSB_Onboarding_MERCHANT_TYPE() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "merchantType");
        queryParam.put("serviceCategory", "soundbox");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for SB Onboarding CATEGORY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_32_FetchFilterValuesForSB_Onboarding_CATEGORY() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "category");
        queryParam.put("serviceCategory", "soundbox");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for Tap and Pay Onboarding PID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_33_FetchFilterValuesForTapAndPay_Onboarding_PID() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "pid");
        queryParam.put("serviceCategory", "TAPANDPAY");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for Tap and Pay Onboarding AgentPermission")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_34_FetchFilterValuesForTapAndPay_Onboarding_AgentPermission() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "agentPermission");
        queryParam.put("serviceCategory", "TAPANDPAY");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for Tap and Pay Onboarding ppiLimit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_35_FetchFilterValuesForTapAndPay_Onboarding_ppiLimit() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "ppiLimit");
        queryParam.put("serviceCategory", "TAPANDPAY");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for Tap and Pay Onboarding DELAYED_SETTLEMENT")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_36_FetchFilterValuesForTapAndPay_Onboarding_DELAYED_SETTLEMENT() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "delayedSettlement");
        queryParam.put("serviceCategory", "TAPANDPAY");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for Tap and Pay Onboarding BUSINESS_WALLET")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_37_FetchFilterValuesForTapAndPay_Onboarding_BUSINESS_WALLET() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "businessWallet");
        queryParam.put("serviceCategory", "TAPANDPAY");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for Tap and Pay Onboarding MERCHANT_AGE_TYPE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_38_FetchFilterValuesForTapAndPay_Onboarding_MERCHANT_AGE_TYPE() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "merchantAgeType");
        queryParam.put("serviceCategory", "TAPANDPAY");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for Tap and Pay Onboarding MERCHANT_TYPE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_39_FetchFilterValuesForTapAndPay_Onboarding_MERCHANT_TYPE() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "merchantType");
        queryParam.put("serviceCategory", "TAPANDPAY");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch filter values for Tap and Pay Onboarding CATEGORY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_40_FetchFilterValuesForTapAndPay_Onboarding_CATEGORY() {

        FetchFilterValuesAPI fetchFilterValuesAPI = new FetchFilterValuesAPI(P.TESTDATA.get("FetchFilterValuesAPIRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("filterType", "category");
        queryParam.put("serviceCategory", "TAPANDPAY");
        queryParam.put("planType", "onboarding");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response response = ODSPlanObject.fetchFilterValuesAPIMiddlewareFunction(fetchFilterValuesAPI,queryParam,headers);
        int StatusCode = response.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

}
