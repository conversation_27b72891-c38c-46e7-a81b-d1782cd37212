package OCL.PlanManagement;

import Request.ODS.v1.OdsPayMode;
import Services.ODS.OdsMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class FetchOdsPayMode extends BaseMethod{
	OdsMiddlewareServices odsMiddlewareServicesObject = new OdsMiddlewareServices();

	private static final Logger LOGGER = LogManager.getLogger(FetchOdsPayMode.class);

	public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
    String encodedXMWExToken = findXMWExTokenforPanel("5123456788","paytm@123");
    
    
    
	@Test(priority = 0, description = "Fetch Ods PayMode Response", groups = {
	"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_001_FetchOdsPayModeStatusCode() {
		OdsPayMode odsPayMode = new OdsPayMode(P.TESTDATA.get("OdsPayModePath"));
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", encodedXMWExToken);

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("serviceCategoryId", "2");
		queryParam.put("planType", "onboarding");

		Response respObj = odsMiddlewareServicesObject.odsPayModeMethod(odsPayMode, headers, queryParam);

		LOGGER.info("Fetch Ods PayMode Response" + respObj.statusCode());

		Assert.assertEquals(respObj.statusCode(), 200);
	}
	@Test(priority = 0, description = "Fetch Ods PayMode With Wrong Token", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_002_FetchOdsPayModeWithWrongToken() {
		OdsPayMode odsPayMode = new OdsPayMode(P.TESTDATA.get("OdsPayModePath"));
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX", "ajkhsgdkjdkb");


		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("serviceCategoryId", "2");
		queryParam.put("planType", "onboarding");

		Response respObj = null;
		try {
			respObj = odsMiddlewareServicesObject.odsPayModeMethod(odsPayMode, headers, queryParam);

		} catch (PatternSyntaxException e) {

		}
		if (respObj != null) {
			LOGGER.info("Fetch Ods PayMode Response" + respObj.statusCode());
			Assert.assertEquals(respObj.statusCode(), 401);
		}
	}
	@Test(priority = 0, description = "Fetch Ods PayMode Check Result Status Success", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_003_FetchOdsPayModeCheckResultStatusSuccess() throws ParseException {
		OdsPayMode odsPayMode = new OdsPayMode(P.TESTDATA.get("OdsPayModePath"));
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("serviceCategoryId", "2");
		queryParam.put("planType", "onboarding");

		headers.put("X-MW-TOKEN-EX",encodedXMWExToken);
		
		Response respObj = odsMiddlewareServicesObject.odsPayModeMethod(odsPayMode, headers,queryParam);
		LOGGER.info("Fetch Ods PayMode Check Result Status Success Response" + respObj.statusCode());
		
		
		respObj.getBody();
		
		JSONParser parser = new JSONParser();  
		
		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString()); 
		
		JSONObject jsObj2=(JSONObject) jsObj1.get("resultInfo");
		
		LOGGER.info("Fetch Ods PayMode Check Result Status Success Response" + respObj.statusCode());
		
		Assert.assertEquals(jsObj2.get("resultStatus"),"S");
	
	}
	@Test(priority = 0, description = "Fetch Ods PayMode Check Result Code Success", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_004_FetchOdsPayModeCheckResultCodeSuccess() throws ParseException {
		OdsPayMode odsPayMode = new OdsPayMode(P.TESTDATA.get("OdsPayModePath"));
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX",encodedXMWExToken);

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("serviceCategoryId", "2");
		queryParam.put("planType", "onboarding");
		
		Response respObj = odsMiddlewareServicesObject.odsPayModeMethod(odsPayMode, headers,queryParam);
		LOGGER.info("Fetch Ods PayMode Check Result Code Success Response" + respObj.statusCode());
		
		
		respObj.getBody();
		
		JSONParser parser = new JSONParser();  
		
		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString()); 
		
		JSONObject jsObj2=(JSONObject) jsObj1.get("resultInfo");
		
		LOGGER.info("Fetch Ods PayMode Check Result Code Success Response" + respObj.statusCode());
		
		Assert.assertEquals(jsObj2.get("resultCode"),"SUCCESS");
	
	}
	@Test(priority = 0, description = "Fetch Ods PayMode Check Result CodeId Success", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_005_FetchOdsPayModeCheckResultCodeIdSuccess() throws ParseException {
		OdsPayMode odsPayMode = new OdsPayMode(P.TESTDATA.get("OdsPayModePath"));
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX",encodedXMWExToken);

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("serviceCategoryId", "2");
		queryParam.put("planType", "onboarding");
		
		Response respObj = odsMiddlewareServicesObject.odsPayModeMethod(odsPayMode, headers,queryParam);
		LOGGER.info("Fetch Ods PayMode Check Result CodeId Success Response" + respObj.statusCode());
		
		
		respObj.getBody();
		
		JSONParser parser = new JSONParser();  
		
		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString()); 
		
		JSONObject jsObj2=(JSONObject) jsObj1.get("resultInfo");
		
		LOGGER.info("Fetch Ods PayMode Check Result Code Success Response" + respObj.statusCode());
		
		Assert.assertEquals(jsObj2.get("resultCodeId"),"01");
	
	}
	
	@Test(priority = 0, description = "Fetch Ods PayMode Check Result Data", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_006_FetchOdsPayModeCheckResultData() throws ParseException {
		OdsPayMode odsPayMode = new OdsPayMode(P.TESTDATA.get("OdsPayModePath"));
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX",encodedXMWExToken);

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("serviceCategoryId", "2");
		queryParam.put("planType", "onboarding");
		
		Response respObj = odsMiddlewareServicesObject.odsPayModeMethod(odsPayMode, headers,queryParam);
		LOGGER.info("Fetch Ods PayMode Check Result Data" + respObj.statusCode());
		
		
		respObj.getBody();
		
		JSONParser parser = new JSONParser();  
		
		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString()); 
		
		JSONObject jsObj2=(JSONObject) jsObj1.get("data");
		
		//JSONArray  jsObj3=(JSONArray) jsObj2.get("KeyValuePair(key=1, value=Credit Card)");
		
	//	JSONObject jsObj4=(JSONObject) jsObj3.get(0);
		
	//	LOGGER.info("Key"+jsObj4.get("key"));
		
		LOGGER.info("Fetch Ods PayMode Check Result Code Success Response" + respObj.statusCode());
		
	/*	if(jsObj4.get("key").toString().equals("Key2")) {
			Assert.assertEquals(jsObj4.get("value"),"Diners");
		}*/
	}
	@Test(priority = 0, description = "Fetch Ods PayMode Check Result Data2", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_007_FetchOdsPayModeCheckResultData2() throws ParseException {
		OdsPayMode odsPayMode = new OdsPayMode(P.TESTDATA.get("OdsPayModePath"));
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX",encodedXMWExToken);

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("serviceCategoryId", "2");
		queryParam.put("planType", "onboarding");


		Response respObj = odsMiddlewareServicesObject.odsPayModeMethod(odsPayMode, headers,queryParam);
		LOGGER.info("Fetch Ods PayMode Check Result Data" + respObj.statusCode());
		
		
		respObj.getBody();
		
		JSONParser parser = new JSONParser();  
		
		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString()); 
		
		JSONObject jsObj2=(JSONObject) jsObj1.get("data");
		
		//JSONArray  jsObj3=(JSONArray) jsObj2.get("KeyValuePair(key=1, value=Credit Card)");
		
		//JSONObject jsObj4=(JSONObject) jsObj3.get(2);
		
	//	LOGGER.info("Key"+jsObj4.get("key"));
		
		LOGGER.info("Fetch Ods PayMode Check Result Data2 Response" + respObj.statusCode());
		
		/*if(jsObj4.get("key").toString().equals("Key1")) {
			Assert.assertEquals(jsObj4.get("value"),"Amex");
		} */
	}
	@Test(priority = 0, description = "Fetch Ods PayMode Check Result Data2", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_008_FetchOdsPayModeCheckResultData3() throws ParseException {
		OdsPayMode odsPayMode = new OdsPayMode(P.TESTDATA.get("OdsPayModePath"));
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX",encodedXMWExToken);

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("serviceCategoryId", "2");
		queryParam.put("planType", "onboarding");
		
		Response respObj = odsMiddlewareServicesObject.odsPayModeMethod(odsPayMode, headers,queryParam);
		LOGGER.info("Fetch Ods PayMode Check Result Data" + respObj.statusCode());
		
		
		respObj.getBody();
		
		JSONParser parser = new JSONParser();  
		
		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString()); 
		
		JSONObject jsObj2=(JSONObject) jsObj1.get("data");
		
	//	JSONArray  jsObj3=(JSONArray) jsObj2.get("KeyValuePair(key=1, value=Credit Card)");
		
	//	JSONObject jsObj4=(JSONObject) jsObj3.get(2);
		
	//	LOGGER.info("Key"+jsObj4.get("key"));
		
		LOGGER.info("Fetch Ods PayMode Check Result Data2 Response" + respObj.statusCode());
		
	/*	if(jsObj4.get("key").toString().equals("Key3")) {
			Assert.assertEquals(jsObj4.get("value"),"Corp Card");
		} */
	}
	@Test(priority = 0, description = "Fetch Ods PayMode Check Result Data2", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_009_FetchOdsPayModeCheckResultData4() throws ParseException {
		OdsPayMode odsPayMode = new OdsPayMode(P.TESTDATA.get("OdsPayModePath"));
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX",encodedXMWExToken);

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("serviceCategoryId", "2");
		queryParam.put("planType", "onboarding");
		
		Response respObj = odsMiddlewareServicesObject.odsPayModeMethod(odsPayMode, headers,queryParam);
		LOGGER.info("Fetch Ods PayMode Check Result Data" + respObj.statusCode());
		
		
		respObj.getBody();
		
		JSONParser parser = new JSONParser();  
		
		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString()); 
		
		JSONObject jsObj2=(JSONObject) jsObj1.get("data");
		
	//	JSONArray  jsObj3=(JSONArray) jsObj2.get("KeyValuePair(key=2, value=Debit Card)");
		
		//JSONObject jsObj4=(JSONObject) jsObj3.get(0);
		
	//	LOGGER.info("Key"+jsObj4.get("key"));
		
		LOGGER.info("Fetch Ods PayMode Check Result Data2 Response" + respObj.statusCode());
		
	/*	if(jsObj4.get("key").toString().equals("Key4")) {
			Assert.assertEquals(jsObj4.get("value"),"Rupay");
		} */
	}
	@Test(priority = 0, description = "Fetch Ods PayMode Check Result Data2", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_010_FetchOdsPayModeCheckResultData5() throws ParseException {
		OdsPayMode odsPayMode = new OdsPayMode(P.TESTDATA.get("OdsPayModePath"));
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-MW-TOKEN-EX",encodedXMWExToken);

		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("serviceCategoryId", "2");
		queryParam.put("planType", "onboarding");
		
		Response respObj = odsMiddlewareServicesObject.odsPayModeMethod(odsPayMode, headers,queryParam);
		LOGGER.info("Fetch Ods PayMode Check Result Data" + respObj.statusCode());
		
		
		respObj.getBody();
		
		JSONParser parser = new JSONParser();  
		
		JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString()); 
		
	//	JSONObject jsObj2=(JSONObject) jsObj1.get("data");
		
		//JSONArray  jsObj3=(JSONArray) jsObj2.get("KeyValuePair(key=2, value=Debit Card)");
		
	//	JSONObject jsObj4=(JSONObject) jsObj3.get(1);
		
	//	LOGGER.info("Key"+jsObj4.get("key"));
		
		LOGGER.info("Fetch Ods PayMode Check Result Data2 Response" + respObj.statusCode());
		
	/*	if(jsObj4.get("key").toString().equals("Key5")) {
			Assert.assertEquals(jsObj4.get("value"),"Prepaid");
		}*/
	}
	
}
