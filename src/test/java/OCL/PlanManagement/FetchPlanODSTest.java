package OCL.PlanManagement;


import Request.ODS.FetchPlanODS;
import Services.ODS.ODSPlan;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.util.ArrayList;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;

public class FetchPlanODSTest extends BaseMethod
{
    ODSPlan ODSPlanObject=new ODSPlan();
    private static final Logger LOGGER = LogManager.getLogger(FetchPlanODSTest.class);

    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "XwQyYa55436102842530");
        body.put("servicePlanId", "10397");
        body.put("merchantType", "BIG");
        body.put("category", "GROCERY");
        body.put("subCategory", "GROCERY");
        body.put("serviceCategory", "EDC");

        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan with invalid mid")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_ODSFetchPlanWithInvalidMid() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "XwQyYa554361028425301");
        body.put("merchantType", "BIG");
        body.put("category", "GROCERY");
        body.put("subCategory", "All");
        body.put("serviceCategory", "EDC");

        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 500);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan with invalid service plan id")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_ODSFetchPlanWithInvalidServicePlanId() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "XwQyYa55436102842530");
        body.put("servicePlanId", "103971");
        body.put("merchantType", "BIG");
        body.put("category", "GROCERY");
        body.put("subCategory", "All");
        body.put("serviceCategory", "EDC");

        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan with invalid category")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_ODSFetchPlanWithInvalidCategory() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "XwQyYa55436102842530");
        body.put("servicePlanId", "10397");
        body.put("merchantType", "BIG");
        body.put("category", "GROCERY1");
        body.put("subCategory", "GROCERY");
        body.put("serviceCategory", "EDC");

        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan with invalid  sub category")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_ODSFetchPlanWithInvalidSubCategory() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "XwQyYa55436102842530");
        body.put("servicePlanId", "10397");
        body.put("merchantType", "BIG");
        body.put("category", "GROCERY");
        body.put("subCategory", "GROCERY1");
        body.put("serviceCategory", "EDC");

        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan without mid")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_ODSFetchPlanWithoutMid()
    {
        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithoutMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("delayedSettlement", "false");
        body.put("merchantType", "BIG");
        body.put("ppiLimit", "2");
        body.put("category", "BFSI");
        body.put("businessWallet", "true");
        body.put("subCategory", "Loans");
        body.put("merchantAgeType", "NEW");
        body.put("agentId", "1107195733");
        body.put("serviceCategory", "EDC");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
       headers.put("Content-Type", "application/json");
        headers.put("jwt",jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
       int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan with null mid")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_ODSFetchPlanWithNullMid() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "");
        body.put("servicePlanId", "10397");
        body.put("merchantType", "BIG");
        body.put("category", "GROCERY");
        body.put("subCategory", "GROCERY");
        body.put("serviceCategory", "EDC");

        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan with null plan id")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_ODSFetchPlanWithNullPlanId() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "XwQyYa55436102842530");
        body.put("servicePlanId", "");
        body.put("merchantType", "BIG");
        body.put("category", "GROCERY");
        body.put("subCategory", "GROCERY");
        body.put("serviceCategory", "EDC");

        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan with invalid service category")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_ODSFetchPlanWithInvalidServiceCategory() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "XwQyYa55436102842530");
        body.put("servicePlanId", "");
        body.put("merchantType", "BIG");
        body.put("category", "GROCERY");
        body.put("subCategory", "GROCERY");
        body.put("serviceCategory", "abc");

        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan with invalid business wallet")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_ODSFetchPlanWithInvalidBusinessWallet()
    {
        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithoutMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("delayedSettlement", "false");
        body.put("merchantType", "BIG");
        body.put("ppiLimit", "2");
        body.put("category", "BFSI");
        body.put("businessWallet", "true1");
        body.put("subCategory", "Loans");
        body.put("merchantAgeType", "NEW");
        body.put("agentId", "1107195733");
        body.put("serviceCategory", "EDC");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt",jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Device Upgrade")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "qa11VE51594478161509");
        body.put("merchantType", "All");
        body.put("category", "All");
        body.put("subCategory", "All");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "device_upgrade");
        body.put("sourceDevice", "android_a50");
        body.put("sourcePlanType", "lifetime_rental");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Device Upgrade - Checking plan type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "qa11VE51594478161509");
        body.put("merchantType", "All");
        body.put("category", "All");
        body.put("subCategory", "All");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "device_upgrade");
        body.put("sourceDevice", "android_a50");
        body.put("sourcePlanType", "lifetime_rental");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        Assert.assertEquals(FetchPlanObjResponse.jsonPath().getJsonObject("data.deviceInfo[0].plans[0].planType"), "device_upgrade");
    }


    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Device Upgrade - Checking Reseller ID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "SzTdKQ48631048031505");
        body.put("merchantType", "All");
        body.put("category", "All");
        body.put("subCategory", "All");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "device_upgrade");
        body.put("sourceDevice", "android_a50");


        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);
        body.put("sourcePlanType", "lifetime_rental");

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        Assert.assertEquals(FetchPlanObjResponse.jsonPath().getJsonObject("data.deviceInfo[0].plans[0].filters.resellerId[0]"), "QpbzGy94146454712712");
    }

   // @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Device Upgrade - Gas and Petrol")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_14_ODSFetchPlan() {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("merchantType", "All");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "device_upgrade");
        body.put("sourceDevice", "android_a50");
        body.put("subCategory", "All");
        body.put("sourcePlanType", "lifetime_rental");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);
        List<List<String>> filters = FetchPlanObjResponse.jsonPath().getList("data.deviceInfo.plans.filters.category");


        List<String> flatFilters = new ArrayList<>();
        for (List<String> categoryList : filters) {
            if (categoryList != null) {
                flatFilters.addAll(categoryList);
            }
        }
    }


    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Device Upgrade - Agent Permission with MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_15_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("merchantType", "All");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "device_upgrade");
        body.put("sourceDevice", "android_a50");
        body.put("subCategory", "All");
        body.put("agentPermissions", "edc_mapping_amanath_bank");
        body.put("sourcePlanType", "lifetime_rental");

        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Device Upgrade - Agent Permission")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_16_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("merchantType", "All");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "device_upgrade");
        body.put("sourceDevice", "android_a50");
        body.put("subCategory", "All");
        body.put("sourcePlanType", "lifetime_rental");

        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Device Upgrade - Checking Source Device")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_17_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("merchantType", "All");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "device_upgrade");
        body.put("sourceDevice", "android_a50");
        body.put("subCategory", "All");
        body.put("sourcePlanType", "lifetime_rental");

        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        Assert.assertEquals(FetchPlanObjResponse.jsonPath().getJsonObject("data.deviceInfo[0].plans[0].sourceDevices[0]"), "android_a50");
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Plan Upgrade")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_18_ODSFetchPlan() {
        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("EDCPlanUpgradeRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("merchantType", "All");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "plan_upgrade");
        body.put("category", "All");
        body.put("sourcePlan", "Super Saver Gold");
        body.put("subCategory", "All");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Plan Upgrade - With MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_19_ODSFetchPlan() {
        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("EDCPlanUpgradeRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("merchantType", "All");
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "plan_upgrade");
        body.put("category", "All");
        body.put("sourcePlan", "Super Saver Gold");
        body.put("subCategory", "All");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Plan Upgrade - With Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_20_ODSFetchPlan() {
        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("EDCPlanUpgradeRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("merchantType", "All");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "plan_upgrade");
        body.put("category", "All");
        body.put("sourcePlan", "Super Saver Gold");
        body.put("subCategory", "All");
        body.put("agentPermissions", "edc_mapping_sefsdfsdfs");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Plan Upgrade - source plan Case Insensitivity")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_21_ODSFetchPlan() {
        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("EDCPlanUpgradeRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("merchantType", "All");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "plan_upgrade");
        body.put("category", "All");
        body.put("sourcePlan", "tEsTpLan1");
        body.put("subCategory", "All");
        body.put("agentPermissions", "edc_mapping_sefsdfsdfs");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Plan Upgrade - With Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_22_ODSFetchPlan() {
        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("EDCPlanUpgradeRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("merchantType", "All");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "plan_upgrade");
        body.put("category", "All");
        body.put("sourcePlan", "Super Saver Gold");
        body.put("subCategory", "All");
        body.put("agentPermissions", "edc_mapping_sefsdfsdfs");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        Assert.assertEquals(FetchPlanObjResponse.jsonPath().getJsonObject("resultInfo.resultStatus"),"S");
    }

 //   @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Plan Upgrade - MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_23_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMIDPlanUpgrade"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("merchantType", "All");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "plan_upgrade");
        body.put("sourcePlan", "Super Saver Gold");
        body.put("subCategory", "All");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        ArrayList<String> responseListOfQuestionType = FetchPlanObjResponse.jsonPath().getJsonObject("data");

        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if(hset.contains("qa11VE51594478161509")){
            Assert.assertTrue(true);
        }else Assert.assertTrue(false);
    }

//    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Plan Upgrade - Case Insensitive ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_24_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("merchantType", "All");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "plan_upgrade");
        body.put("sourcePlan", "Super SAVER Gold");
        body.put("subCategory", "All");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        ArrayList<String> responseListOfQuestionType = FetchPlanObjResponse.jsonPath().getJsonObject("data");

        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if(hset.contains("qa11VE51594478161509")){
            Assert.assertTrue(true);
        }else Assert.assertTrue(false);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding with MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_25_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding With Generic SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_26_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        body.put("merchantType", "All");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding With Generic SRO with Strategy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_27_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        body.put("merchantType", "All");
        body.put("category", "All");
        body.put("strategy", "PLAN_FILTERS");
        body.put("subCategory", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding With Generic SRO with Strategy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_28_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        body.put("merchantType", "All");
        body.put("category", "All");
        body.put("subCategory", "All");
        body.put("strategy", "PLAN_FILTERS");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding With Generic SRO with Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_29_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        body.put("merchantType", "All");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding with MID with Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_30_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        body.put("planType", "onboarding");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding with Cleavertap Campagin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_31_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding with Cleavertap Campagin and Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_32_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        body.put("planType", "onboarding");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Gas and Petrol - Success Status Check")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_33_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
//    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Gas and Petrol - Searching for category Gas and Petrol")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_34_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        ArrayList<String> responseListOfQuestionType = FetchPlanObjResponse.jsonPath().getJsonObject("data");

        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if(hset.contains("Gas and Petrol")){
            Assert.assertTrue(true);
        }else Assert.assertTrue(false);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Without Agent ID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_35_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Without Generic SRO and MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_36_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

//    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding With Generic SRO with Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_37_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        body.put("ppiLimit", "1");
        body.put("category", "All");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        ArrayList<String> responseListOfQuestionType = FetchPlanObjResponse.jsonPath().getJsonObject("data");

        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if(hset.contains("sb_mapping_ashwin1")){
            Assert.assertTrue(true);
        }else Assert.assertTrue(false);
    }
//    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding with Cleavertap Campagin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_38_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMIDPlanUpgrade"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        ArrayList<String> responseListOfQuestionType = FetchPlanObjResponse.jsonPath().getJsonObject("data");

        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if(hset.contains("sb_plan_punjab")){
            Assert.assertTrue(true);
        }else Assert.assertTrue(false);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding with MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_39_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        Assert.assertEquals(FetchPlanObjResponse.jsonPath().getJsonObject("resultInfo.resultStatus"),"S");
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding With Generic SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_40_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        body.put("merchantType", "All");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        Assert.assertEquals(FetchPlanObjResponse.jsonPath().getJsonObject("resultInfo.resultStatus"),"F");
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding Without MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_41_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
       // body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        body.put("merchantType", "All");
        body.put("category", "All");
        body.put("subCategory", "All");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        Assert.assertEquals(FetchPlanObjResponse.jsonPath().getJsonObject("resultInfo.resultStatus"),"S");
    }


    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Plan Paid replacement with MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_42_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Paid replacement With Generic SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_43_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("merchantType", "All");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Paid replacement With Generic SRO with Strategy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_44_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("merchantType", "All");
        body.put("category", "All");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Paid replacement With Generic SRO with Strategy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_45_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("merchantType", "All");
        body.put("category", "All");
        body.put("subCategory", "All");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Paid replacement With Generic SRO with Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_46_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMIDAndAgentSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("merchantType", "All");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Paid replacement with MID with Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_47_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        body.put("planType", "paid_replacement");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Paid replacement with Cleavertap Campagin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_48_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Paid replacement with Cleavertap Campagin and Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_49_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        body.put("planType", "paid_replacement");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox -Paid replacement Gas and Petrol - Success Status Check")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_50_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    //    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Gas and Petrol - Searching for category Gas and Petrol")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_51_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        ArrayList<String> responseListOfQuestionType = FetchPlanObjResponse.jsonPath().getJsonObject("data");

        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if(hset.contains("Gas and Petrol")){
            Assert.assertTrue(true);
        }else Assert.assertTrue(false);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox Paid replacement - Without Agent ID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_52_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox Paid replacement - Without Generic SRO and MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_53_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    //    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Paid replacement With Generic SRO with Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_54_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("ppiLimit", "1");
        body.put("category", "All");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        ArrayList<String> responseListOfQuestionType = FetchPlanObjResponse.jsonPath().getJsonObject("data");

        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if(hset.contains("sb_mapping_ashwin1")){
            Assert.assertTrue(true);
        }else Assert.assertTrue(false);
    }
    //    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Paid replacement with Cleaver`tap` Campagin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_55_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMIDPlanUpgrade"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        ArrayList<String> responseListOfQuestionType = FetchPlanObjResponse.jsonPath().getJsonObject("data");

        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if(hset.contains("sb_plan_punjab")){
            Assert.assertTrue(true);
        }else Assert.assertTrue(false);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Paid replacement with MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_56_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        Assert.assertEquals(FetchPlanObjResponse.jsonPath().getJsonObject("resultInfo.resultStatus"),"S");
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Paid replacement With Generic SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_57_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("merchantType", "All");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        Assert.assertEquals(FetchPlanObjResponse.jsonPath().getJsonObject("resultInfo.resultStatus"),"S");
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Paid replacement Without MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_58_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("merchantType", "All");
        body.put("category", "All");
        body.put("subCategory", "All");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        Assert.assertEquals(FetchPlanObjResponse.jsonPath().getJsonObject("resultInfo.resultStatus"),"S");
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Plan device upgrade with MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_59_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - device upgrade With Generic SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_60_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("merchantType", "All");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - device upgrade With Generic SRO with Strategy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_61_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("merchantType", "All");
        body.put("category", "All");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - device upgrade With Generic SRO with Strategy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_62_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("merchantType", "All");
        body.put("category", "All");
        body.put("subCategory", "All");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - device upgrade With Generic SRO with Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_63_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMIDAndAgentSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("merchantType", "All");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - device upgrade with MID with Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_64_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        body.put("planType", "device_upgrade");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - device upgrade with Cleavertap Campagin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_65_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - device upgrade with Cleavertap Campagin and Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_66_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        body.put("planType", "device_upgrade");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox -device upgrade Gas and Petrol - Success Status Check")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_67_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    //    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Gas and Petrol - Searching for category Gas and Petrol")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_68_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        ArrayList<String> responseListOfQuestionType = FetchPlanObjResponse.jsonPath().getJsonObject("data");

        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if(hset.contains("Gas and Petrol")){
            Assert.assertTrue(true);
        }else Assert.assertTrue(false);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox device upgrade - Without Agent ID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_69_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox device upgrade - Without Generic SRO and MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_70_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    //    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - device upgrade With Generic SRO with Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_71_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("ppiLimit", "1");
        body.put("category", "All");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        ArrayList<String> responseListOfQuestionType = FetchPlanObjResponse.jsonPath().getJsonObject("data");

        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if(hset.contains("sb_mapping_ashwin1")){
            Assert.assertTrue(true);
        }else Assert.assertTrue(false);
    }
    //    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - device upgrade with Cleavertap Campagin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_72_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMIDPlanUpgrade"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        ArrayList<String> responseListOfQuestionType = FetchPlanObjResponse.jsonPath().getJsonObject("data");

        HashSet<String> hset = new HashSet<String>(responseListOfQuestionType);

        if(hset.contains("sb_plan_punjab")){
            Assert.assertTrue(true);
        }else Assert.assertTrue(false);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - device upgrade with MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_73_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        Assert.assertEquals(FetchPlanObjResponse.jsonPath().getJsonObject("resultInfo.resultStatus"),"S");
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - device upgrade With Generic SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_74_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("merchantType", "All");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        Assert.assertEquals(FetchPlanObjResponse.jsonPath().getJsonObject("resultInfo.resultStatus"),"S");
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - device upgrade Without Generic MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_75_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("merchantType", "All");
        body.put("category", "All");
        body.put("subCategory", "All");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        Assert.assertEquals(FetchPlanObjResponse.jsonPath().getJsonObject("resultInfo.resultStatus"),"S");
    }

    /* From now onwards there will be test cases where instead of checking the status of a particular request, we'll be searching the desired outputs. */

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Onboarding - Agent Permission - Search")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_76_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMIDAndAgentSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "onboarding");
        body.put("merchantType", "Small");
        body.put("category", "All");
        body.put("subCategory", "All");
        body.put("agentPermissions", "monthly_plan_edc_android_a910");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> agentPermissionValue = new ArrayList<>();

            for(JsonNode objectNode : plans) {
                List<JsonNode> filters = objectNode.findValues("filters");

                for(JsonNode agentPermission : filters) {
                    if(!agentPermission.isEmpty()) {
                        JsonNode agentPermissionNode = agentPermission.get("agentPermission");
                        if (agentPermissionNode != null && agentPermissionNode.isArray()) {
                            for (JsonNode valueNode : agentPermissionNode) {
                                String agentPermissionValues = valueNode.asText();
                                agentPermissionValue.add(agentPermissionValues);
                            }
                        }
                    }
                }
            }
            int flag = 1;
            for(String s : agentPermissionValue) {
                if(!s.equals("monthly_plan_edc_android_a910")) {
                    flag = 0;
                }
            }
        Assert.assertEquals(flag, 1);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Device Upgrade - Agent Permission - Search")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_77_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMIDAndAgentSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "onboarding");
        body.put("merchantType", "Small");
        body.put("category", "All");
        body.put("sourceDevice", "android_a910");
        body.put("subCategory", "All");
        body.put("agentPermissions", "monthly_plan_edc_android_a910");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> agentPermissionValue = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode agentPermission : filters) {
                if(!agentPermission.isEmpty()) {
                    JsonNode agentPermissionNode = agentPermission.get("agentPermission");
                    if (agentPermissionNode != null && agentPermissionNode.isArray()) {
                        for (JsonNode valueNode : agentPermissionNode) {
                            String agentPermissionValues = valueNode.asText();
                            agentPermissionValue.add(agentPermissionValues);
                        }
                    }
                }
            }
        }
        int flag = 1;
        for(String s : agentPermissionValue) {
            if(!s.equals("monthly_plan_edc_android_a910")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);

    }


    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Onboarding - Filter - Gas and Petrol -Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_78_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("merchantType", "All");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "onboarding");
        body.put("subCategory", "All");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode gasAndPetrol : filters) {
                if(!gasAndPetrol.isEmpty()) {
                    JsonNode gasAndPetrolNode = gasAndPetrol.get("category");
                    if (gasAndPetrolNode != null && gasAndPetrolNode.isArray()) {
                        for (JsonNode valueNode : gasAndPetrolNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("Gas and Petrol")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Onboarding - Filter - Reseller Id -Searching - 1")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_79_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("resellerId", "QpbzGy94146454712712");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "onboarding");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode resellerIds : filters) {
                if(!resellerIds.isEmpty()) {
                    JsonNode resellerIdsNode = resellerIds.get("resellerId");
                    if (resellerIdsNode != null && resellerIdsNode.isArray()) {
                        for (JsonNode valueNode : resellerIdsNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("QpbzGy94146454712712")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Onboarding - Filter - Reseller Id -Searching - 2")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_80_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "SzTdKQ48631048031505");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "onboarding");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode resellerIds : filters) {
                if(!resellerIds.isEmpty()) {
                    JsonNode resellerIdsNode = resellerIds.get("resellerId");
                    if (resellerIdsNode != null && resellerIdsNode.isArray()) {
                        for (JsonNode valueNode : resellerIdsNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("QpbzGy94146454712712")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Onboarding - MID -Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_81_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "onboarding");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode mids : filters) {
                if(!mids.isEmpty()) {
                    JsonNode midNode = mids.get("midOfMerchant");
                    if (midNode != null && midNode.isArray()) {
                        for (JsonNode valueNode : midNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("qa11VE51594478161509")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Onboarding - Cleavertap - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_82_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "onboarding");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode CTs : filters) {
                if(!CTs.isEmpty()) {
                    JsonNode CTNodes = CTs.get("cleverTapCampaign");
                    if (CTNodes != null && CTNodes.isArray()) {
                        for (JsonNode valueNode : CTNodes) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("sb_plan_punjab")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Onboarding - PPI - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_83_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "onboarding");
        body.put("ppiLimit", "1");
        body.put("category", "All");
        body.put("subCategory", "All");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<Integer> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode ppis : filters) {
                if (!ppis.isEmpty()) {
                    JsonNode ppiNodes = ppis.get("ppiLimit");
                    if (ppiNodes != null && ppiNodes.isArray()) {
                        for (JsonNode valueNode : ppiNodes) {
                            Integer categoryValues = valueNode.asInt();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 0;
        for(Integer a : filterValues) {
            if(a.equals(1)) {
                flag = 1;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Onboarding - gstIn - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_84_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "onboarding");
        body.put("gstIn", "false");
        body.put("category", "All");
        body.put("subCategory", "All");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode gstIns : filters) {
                if (!gstIns.isEmpty()) {
                    JsonNode gstInNodes = gstIns.get("gstIn");
                    if (gstInNodes != null && gstInNodes.isArray()) {
                        for (JsonNode valueNode : gstInNodes) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String a : filterValues) {
            if(!a.equals("NO")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Onboarding - merchantAgeType - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_85_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "onboarding");
        body.put("merchantAgeType", "New");
        body.put("category", "All");
        body.put("subCategory", "All");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode merchantAgeTypes : filters) {
                if (!merchantAgeTypes.isEmpty()) {
                    JsonNode merchantAgeTypeNode = merchantAgeTypes.get("merchantAgeType");
                    if (merchantAgeTypeNode != null && merchantAgeTypeNode.isArray()) {
                        for (JsonNode valueNode : merchantAgeTypeNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String a : filterValues) {
            if(!a.equals("NEW")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Plan Upgrade - merchantAgeType - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_86_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("EDCPlanUpgradeRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");

        body.put("merchantAgeType", "New");
        body.put("category", "All");
        body.put("subCategory", "All");
        body.put("planType", "plan_upgrade");
        body.put("sourcePlan", "testPlan1");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");
        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode merchantAgeTypes : filters) {
                if (!merchantAgeTypes.isEmpty()) {
                    JsonNode merchantAgeTypeNode = merchantAgeTypes.get("merchantAgeType");
                    if (merchantAgeTypeNode != null && merchantAgeTypeNode.isArray()) {
                        for (JsonNode valueNode : merchantAgeTypeNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String a : filterValues) {
            if(!a.equals("NEW")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }


    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Onboarding - merchantType - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_87_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "onboarding");
        body.put("merchantType", "Small");
        body.put("category", "All");
        body.put("subCategory", "All");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode merchantTypes : filters) {
                if (!merchantTypes.isEmpty()) {
                    JsonNode merchantTypesNode = merchantTypes.get("merchantType");
                    if (merchantTypesNode != null && merchantTypesNode.isArray()) {
                        for (JsonNode valueNode : merchantTypesNode) {
                            String merchantTypesValue = valueNode.asText();
                            filterValues.add(merchantTypesValue);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String a : filterValues) {
            if(!a.equals("SMALL")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Plan Upgrade - Agent Permission - Search")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_88_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("EDCPlanUpgradeRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("merchantType", "All");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "plan_upgrade");
        body.put("category", "All");
        body.put("sourcePlan", "testPlan1");
        body.put("subCategory", "All");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");
        body.put("agentPermissions", "edc_mapping_amanath_bank");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> agentPermissionValue = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode agentPermission : filters) {
                if(!agentPermission.isEmpty()) {
                    JsonNode agentPermissionNode = agentPermission.get("agentPermission");
                    if (agentPermissionNode != null && agentPermissionNode.isArray()) {
                        for (JsonNode valueNode : agentPermissionNode) {
                            String agentPermissionValues = valueNode.asText();
                            agentPermissionValue.add(agentPermissionValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s : agentPermissionValue) {
            if(!s.equals("edc_mapping_amanath_bank")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);

    }
    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Plan Upgrade - Filter - Gas and Petrol -Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_89_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "SBTest10068671323127");
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "plan_upgrade");
        body.put("sourcePlan", "testPlan1");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");
        body.put("agentPermissions", "edc_mapping_amanath_bank");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode gasAndPetrol : filters) {
                if(!gasAndPetrol.isEmpty()) {
                    JsonNode gasAndPetrolNode = gasAndPetrol.get("category");
                    if (gasAndPetrolNode != null && gasAndPetrolNode.isArray()) {
                        for (JsonNode valueNode : gasAndPetrolNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("Gas and Petrol")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Plan Upgrade - Filter - Reseller Id -Searching - 1")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_90_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("resellerId", "QpbzGy94146454712712");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "plan_upgrade");
        body.put("sourcePlan", "testPlan1");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode resellerIds : filters) {
                if(!resellerIds.isEmpty()) {
                    JsonNode resellerIdsNode = resellerIds.get("resellerId");
                    if (resellerIdsNode != null && resellerIdsNode.isArray()) {
                        for (JsonNode valueNode : resellerIdsNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("QpbzGy94146454712712")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Onboarding - Filter - Reseller Id -Searching - 2")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_91_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "GaQZQB01149355164878");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "plan_upgrade");
        body.put("sourcePlan", "testPlan1");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode resellerIds : filters) {
                if(!resellerIds.isEmpty()) {
                    JsonNode resellerIdsNode = resellerIds.get("resellerId");
                    if (resellerIdsNode != null && resellerIdsNode.isArray()) {
                        for (JsonNode valueNode : resellerIdsNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("GaQZQB01149355164878")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Onboarding - MID -Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_92_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "plan_upgrade");
        body.put("sourcePlan", "testPlan1");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");


        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode mids : filters) {
                if(!mids.isEmpty()) {
                    JsonNode midNode = mids.get("midOfMerchant");
                    if (midNode != null && midNode.isArray()) {
                        for (JsonNode valueNode : midNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("qa11VE51594478161509")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Plan Upgrade - Cleavertap - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_93_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "plan_upgrade");
        body.put("sourcePlan", "testPlan1");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");


        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode CTs : filters) {
                if(!CTs.isEmpty()) {
                    JsonNode CTNodes = CTs.get("cleverTapCampaign");
                    if (CTNodes != null && CTNodes.isArray()) {
                        for (JsonNode valueNode : CTNodes) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("sb_plan_punjab")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Plan Upgrade - PPI - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_94_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("EDCPlanUpgradeRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("ppiLimit", "1");
        body.put("category", "All");
        body.put("subCategory", "All");
        body.put("planType", "plan_upgrade");
        body.put("sourcePlan", "testPlan1");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<Integer> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode ppis : filters) {
                if (!ppis.isEmpty()) {
                    JsonNode ppiNodes = ppis.get("ppiLimit");
                    if (ppiNodes != null && ppiNodes.isArray()) {
                        for (JsonNode valueNode : ppiNodes) {
                            Integer categoryValues = valueNode.asInt();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(Integer a : filterValues) {
            if(a.equals(1)) {
                flag = 1;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Plan Upgrade - gstIn - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_95_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("EDCPlanUpgradeRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "plan_upgrade");
        body.put("sourcePlan", "testPlan1");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");
        body.put("gstIn", "false");
        body.put("category", "All");
        body.put("subCategory", "All");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode gstIns : filters) {
                if (!gstIns.isEmpty()) {
                    JsonNode gstInNodes = gstIns.get("gstIn");
                    if (gstInNodes != null && gstInNodes.isArray()) {
                        for (JsonNode valueNode : gstInNodes) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String a : filterValues) {
            if(!a.equals("NO")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Onboarding - merchantType - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_96_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("EDCPlanUpgradeRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "plan_upgrade");
        body.put("sourcePlan", "testPlan1");
        body.put("itemIdentifiers", "android_dx8000\", \"android_a50\", \"android_a910\", \"android_n3\", \"linux_nexgog2plus");
        body.put("merchantType", "Small");
        body.put("category", "All");
        body.put("subCategory", "All");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode merchantTypes : filters) {
                if (!merchantTypes.isEmpty()) {
                    JsonNode merchantTypesNode = merchantTypes.get("merchantType");
                    if (merchantTypesNode != null && merchantTypesNode.isArray()) {
                        for (JsonNode valueNode : merchantTypesNode) {
                            String merchantTypesValue = valueNode.asText();
                            filterValues.add(merchantTypesValue);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String a : filterValues) {
            if(!a.equals("SMALL")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Device Upgrade - Filter - Gas and Petrol -Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_97_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("merchantType", "All");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("planType", "device_upgrade");
        body.put("sourceDevice", "android_a50");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode gasAndPetrol : filters) {
                if(!gasAndPetrol.isEmpty()) {
                    JsonNode gasAndPetrolNode = gasAndPetrol.get("category");
                    if (gasAndPetrolNode != null && gasAndPetrolNode.isArray()) {
                        for (JsonNode valueNode : gasAndPetrolNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("Gas and Petrol")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Device Upgrade - Filter - Reseller Id -Searching - 1")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_98_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("resellerId", "QpbzGy94146454712712");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "device_upgrade");
        body.put("sourceDevice", "android_a50");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode resellerIds : filters) {
                if(!resellerIds.isEmpty()) {
                    JsonNode resellerIdsNode = resellerIds.get("resellerId");
                    if (resellerIdsNode != null && resellerIdsNode.isArray()) {
                        for (JsonNode valueNode : resellerIdsNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("QpbzGy94146454712712")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Device Upgrade - MID -Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_99_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "device_upgrade");
        body.put("sourceDevice", "android_a50");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode mids : filters) {
                if(!mids.isEmpty()) {
                    JsonNode midNode = mids.get("midOfMerchant");
                    if (midNode != null && midNode.isArray()) {
                        for (JsonNode valueNode : midNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("qa11VE51594478161509")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Onboarding - Cleavertap - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_100_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "device_upgrade");
        body.put("sourceDevice", "android_a50");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode CTs : filters) {
                if(!CTs.isEmpty()) {
                    JsonNode CTNodes = CTs.get("cleverTapCampaign");
                    if (CTNodes != null && CTNodes.isArray()) {
                        for (JsonNode valueNode : CTNodes) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("sb_plan_punjab")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Device Upgrade - PPI - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_101_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "device_upgrade");
        body.put("sourceDevice", "android_a50");
        body.put("ppiLimit", "1");
        body.put("category", "All");
        body.put("subCategory", "All");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<Integer> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode ppis : filters) {
                if (!ppis.isEmpty()) {
                    JsonNode ppiNodes = ppis.get("ppiLimit");
                    if (ppiNodes != null && ppiNodes.isArray()) {
                        for (JsonNode valueNode : ppiNodes) {
                            Integer categoryValues = valueNode.asInt();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(Integer a : filterValues) {
            if(a.equals(1)) {
                flag = 1;
            }
        }
        if(filterValues.isEmpty()) {
            flag = 1;
        }
        Assert.assertEquals(flag, 1);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Device Upgrade - gstIn - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_102_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "device_upgrade");
        body.put("sourceDevice", "android_a50");
        body.put("gstIn", "false");
        body.put("category", "All");
        body.put("subCategory", "All");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode gstIns : filters) {
                if (!gstIns.isEmpty()) {
                    JsonNode gstInNodes = gstIns.get("gstIn");
                    if (gstInNodes != null && gstInNodes.isArray()) {
                        for (JsonNode valueNode : gstInNodes) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String a : filterValues) {
            if(!a.equals("NO")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Device Upgrade - merchantAgeType - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_103_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "device_upgrade");
        body.put("sourceDevice", "android_a50");
        body.put("merchantAgeType", "New");
        body.put("category", "All");
        body.put("subCategory", "All");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode merchantAgeTypes : filters) {
                if (!merchantAgeTypes.isEmpty()) {
                    JsonNode merchantAgeTypeNode = merchantAgeTypes.get("merchantAgeType");
                    if (merchantAgeTypeNode != null && merchantAgeTypeNode.isArray()) {
                        for (JsonNode valueNode : merchantAgeTypeNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String a : filterValues) {
            if(!a.equals("NEW")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "EDC - Onboarding - merchantType - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_104_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "1107195733");
        body.put("serviceCategory", "EDC");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "device_upgrade");
        body.put("sourceDevice", "android_a50");
        body.put("merchantType", "Small");
        body.put("category", "All");
        body.put("subCategory", "All");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode merchantTypes : filters) {
                if (!merchantTypes.isEmpty()) {
                    JsonNode merchantTypesNode = merchantTypes.get("merchantType");
                    if (merchantTypesNode != null && merchantTypesNode.isArray()) {
                        for (JsonNode valueNode : merchantTypesNode) {
                            String merchantTypesValue = valueNode.asText();
                            filterValues.add(merchantTypesValue);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String a : filterValues) {
            if(!a.equals("SMALL")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Premium Care Addon with MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_106_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","premium_care");
        body.put("saleChannel","P4B");
        body.put("soldStandAlone","true");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Premium Care Addon with Generic SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_107_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","premium_care");
        body.put("saleChannel","P4B");
        body.put("soldStandAlone","true");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Premium Care Addon with Generic SRO and Strategy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_108_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","premium_care");
        body.put("saleChannel","P4B");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Premium Care Addon with MID with Strategy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_109_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","premium_care");
        body.put("saleChannel","P4B");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Premium Care Addon with P4B sale channel and soldStandalone true")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_110_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","premium_care");
        body.put("saleChannel","P4B");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Premium Care Addon with P4B sale channel and soldStandalone false")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_111_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","premium_care");
        body.put("saleChannel","P4B");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Premium Care Addon with PSA App sale channel and soldStandalone true")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_112_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","premium_care");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Premium Care Addon with PSA App sale channel and soldStandalone false")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_113_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","premium_care");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Premium Care Addon with Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_114_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithAgentSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","premium_care");
        body.put("saleChannel","P4B");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Premium Care Addon with Cleavertap Campagin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_115_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","premium_care");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Premium Care Addon with Gas and Petrol MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_116_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","premium_care");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Premium Care Addon without Agent ID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_117_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","premium_care");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Premium Care Addon without MID and Generic SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_118_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","premium_care");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Premium Care Addon with Gas and Petrol MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_119_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","premium_care");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - shop insurance Addon with MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_120_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","P4B");
        body.put("soldStandAlone","true");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Shop Insurance Addon with Generic SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_121_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","P4B");
        body.put("soldStandAlone","true");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - shop insurance Addon with Generic SRO and Strategy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_122_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","P4B");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Shop Insurance Addon with MID with Strategy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_123_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","P4B");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Shop Insurance Addon with P4B sale channel and soldStandalone true")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_124_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","P4B");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Shop Insurance Addon with P4B sale channel and soldStandalone false")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_125_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","P4B");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - shop insurance Addon with PSA App sale channel and soldStandalone true")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_126_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Shop Insurance Addon with PSA App sale channel and soldStandalone false")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_127_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Shop Insurance Addon with Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_128_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithAgentSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","P4B");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Shop Insurance Addon with Cleavertap Campagin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_129_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Shop Insurnce Addon with Gas and Petrol MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_130_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Shop Insurnce Addon without Agent ID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_131_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - AMC Addon without MID and Generic SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_132_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","amc");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Shop Insurnce Addon with Gas and Petrol MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_133_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    /*TapAndPay Test Cases */

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Tap and Pay - Onboarding With Generic SRO with Strategy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_134_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("TapAndPayWithSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "TAPANDPAY");
        body.put("planType", "onboarding");
        body.put("merchantType", "All");
        body.put("category", "All");
        body.put("strategy", "PLAN_FILTERS");
        body.put("subCategory", "All");
        body.put("ppiLimit", "1");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Tap And Pay - Onboarding With MID and Strategy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_135_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("TapAndPayWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "TAPANDPAY");
        body.put("planType", "onboarding");
        body.put("strategy", "PLAN_FILTERS");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Tap And Pay - Onboarding With Generic SRO with Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_136_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("TapAndPayWithSROandAgent"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        body.put("merchantType", "All");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("agentPermissions", "tapNPay_mapping_1_upfront_49_rental_plan");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Tap And Pay - Onboarding With MID and Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_137_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("TapAndPayWithAgentSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "TAPANDPAY");
        body.put("planType", "onboarding");
        body.put("strategy", "PLAN_FILTERS");
        body.put("agentPermissions", "tapNPay_mapping_1_upfront_49_rental_plan");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Tap And Pay - Onboarding with Cleavertap Campagin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_138_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("TapAndPayWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "TAPANDPAY");
        body.put("planType", "onboarding");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - TAP AND PAY - Onboarding with Cleavertap Campagin and Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_139_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("TapAndPayWithAgentSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "TAPANDPAY");
        body.put("agentPermissions", "tapNPay_mapping_1_upfront_49_rental_plan");
        body.put("planType", "onboarding");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - TAP AND PAY - Gas and Petrol - Success Status Check")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_140_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("TapAndPayWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "TAPANDPAY");
        body.put("planType", "onboarding");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - TAP AND PAY - Without Agent ID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_141_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("TapAndPayWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "TAPANDPAY");
        body.put("planType", "onboarding");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "TAP AND PAY - Onboarding - Filter - Gas and Petrol -Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_142_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("TapAndPayWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "TAPANDPAY");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "onboarding");

        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode gasAndPetrol : filters) {
                if(!gasAndPetrol.isEmpty()) {
                    JsonNode gasAndPetrolNode = gasAndPetrol.get("category");
                    if (gasAndPetrolNode != null && gasAndPetrolNode.isArray()) {
                        for (JsonNode valueNode : gasAndPetrolNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("Gas and Petrol")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Tap And Pay - Onboarding with MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_143_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("TapAndPayWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "TAPANDPAY");
        body.put("planType", "onboarding");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Tap and Pay - Onboarding With Generic SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_144_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("TapAndPayWithSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "TAPANDPAY");
        body.put("planType", "onboarding");
        body.put("merchantType", "All");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("ppiLimit", "1");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding With Generic SRO checking if the correct filter plan is coming or not")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_145_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        body.put("merchantType", "All");
        body.put("subCategory", "All");
        body.put("category", "Books");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode books : filters) {
                if(!books.isEmpty()) {
                    JsonNode bookNode = books.get("category");
                    if (bookNode != null && bookNode.isArray()) {
                        for (JsonNode valueNode : bookNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }
        System.out.println(filterValues);
        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("Books")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Checking if correct ppi is getting returned or not")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_146_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("ppiLimit", "1");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<Integer> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode ppis : filters) {
                if (!ppis.isEmpty()) {
                    JsonNode ppiNodes = ppis.get("ppiLimit");
                    if (ppiNodes != null && ppiNodes.isArray()) {
                        for (JsonNode valueNode : ppiNodes) {
                            Integer categoryValues = valueNode.asInt();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 0;
        for(Integer a : filterValues) {
            if(a.equals(1)) {
                flag = 1;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Checking if correct ppi is getting returned or not")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_147_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("ppiLimit", "1");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> planTypes = new ArrayList<>();

        for(JsonNode planType : plans) {
            JsonNode planTypeNode = planType.findValue("planType");
            if (planTypeNode != null) {
                String val = planTypeNode.asText();
                planTypes.add(val);
            }
        }

        int flag = 1;
        for(String s : planTypes) {
            if(!s.equals("onboarding")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Paid replacement With Generic SRO with Agent SRO and searching agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_148_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("ppiLimit", "1");
        body.put("category", "All");
        body.put("subCategory", "All");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> agentPermissionValue = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode agentPermission : filters) {
                if(!agentPermission.isEmpty()) {
                    JsonNode agentPermissionNode = agentPermission.get("agentPermission");
                    if (agentPermissionNode != null && agentPermissionNode.isArray()) {
                        for (JsonNode valueNode : agentPermissionNode) {
                            String agentPermissionValues = valueNode.asText();
                            agentPermissionValue.add(agentPermissionValues);
                        }
                    }
                }
            }
        }
        int flag = 1;
        for(String s : agentPermissionValue) {
            if(!s.equals("sb_mapping_ashwin1")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding with Cleavertap Campagin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_149_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "onboarding");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode CTs : filters) {
                if(!CTs.isEmpty()) {
                    JsonNode CTNodes = CTs.get("cleverTapCampaign");
                    if (CTNodes != null && CTNodes.isArray()) {
                        for (JsonNode valueNode : CTNodes) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("sb_plan_punjab")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Device Upgrade With Generic SRO checking if the correct filter plan is coming or not")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_150_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("merchantType", "All");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "Books");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode books : filters) {
                if(!books.isEmpty()) {
                    JsonNode bookNode = books.get("category");
                    if (bookNode != null && bookNode.isArray()) {
                        for (JsonNode valueNode : bookNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }
        System.out.println(filterValues);
        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("Books")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Checking if correct ppi is getting returned or not")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_151_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("ppiLimit", "1");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("subCategory", "All");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<Integer> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode ppis : filters) {
                if (!ppis.isEmpty()) {
                    JsonNode ppiNodes = ppis.get("ppiLimit");
                    if (ppiNodes != null && ppiNodes.isArray()) {
                        for (JsonNode valueNode : ppiNodes) {
                            Integer categoryValues = valueNode.asInt();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 0;
        for(Integer a : filterValues) {
            if(a.equals(1)) {
                flag = 1;
            }
        }
        Assert.assertEquals(flag, 0);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Checking if correct ppi is getting returned or not")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_152_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("ppiLimit", "1");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "device_upgrade");
        body.put("subCategory", "All");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> planTypes = new ArrayList<>();

        for(JsonNode planType : plans) {
            JsonNode planTypeNode = planType.findValue("planType");
            if (planTypeNode != null) {
                String val = planTypeNode.asText();
                planTypes.add(val);
            }
        }

        int flag = 1;
        for(String s : planTypes) {
            if(!s.equals("device_upgrade")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding with Cleavertap Campagin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_153_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "device_upgrade");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode CTs : filters) {
                if(!CTs.isEmpty()) {
                    JsonNode CTNodes = CTs.get("cleverTapCampaign");
                    if (CTNodes != null && CTNodes.isArray()) {
                        for (JsonNode valueNode : CTNodes) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("sb_plan_punjab")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Device Upgrade With Generic SRO checking if the correct filter plan is coming or not")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_154_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("merchantType", "All");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "Books");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode books : filters) {
                if(!books.isEmpty()) {
                    JsonNode bookNode = books.get("category");
                    if (bookNode != null && bookNode.isArray()) {
                        for (JsonNode valueNode : bookNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }
        System.out.println(filterValues);
        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("Books")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Checking if correct ppi is getting returned or not")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_155_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("ppiLimit", "1");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("subCategory", "All");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<Integer> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode ppis : filters) {
                if (!ppis.isEmpty()) {
                    JsonNode ppiNodes = ppis.get("ppiLimit");
                    if (ppiNodes != null && ppiNodes.isArray()) {
                        for (JsonNode valueNode : ppiNodes) {
                            Integer categoryValues = valueNode.asInt();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 0;
        for(Integer a : filterValues) {
            if(a.equals(1)) {
                flag = 1;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Checking if correct ppi is getting returned or not")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_156_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("ppiLimit", "1");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("planType", "paid_replacement");
        body.put("subCategory", "All");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> planTypes = new ArrayList<>();

        for(JsonNode planType : plans) {
            JsonNode planTypeNode = planType.findValue("planType");
            if (planTypeNode != null) {
                String val = planTypeNode.asText();
                planTypes.add(val);
            }
        }

        int flag = 1;
        for(String s : planTypes) {
            if(!s.equals("paid_replacement")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - Soundbox - Onboarding with Cleavertap Campagin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_157_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("SoundboxRequestWithoutSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "SOUNDBOX");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("planType", "paid_replacement");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode CTs : filters) {
                if(!CTs.isEmpty()) {
                    JsonNode CTNodes = CTs.get("cleverTapCampaign");
                    if (CTNodes != null && CTNodes.isArray()) {
                        for (JsonNode valueNode : CTNodes) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("sb_plan_punjab")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Brand EMI Addon with MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_158_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","brand_emi");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Brand EMI Addon with Generic SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_159_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","brand_emi");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Brand EMI Addon with Generic SRO and Strategy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_160_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","brand_emi");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Brand EMI Addon with MID with Strategy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_161_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","brand_emi");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Brand EMI Addon with P4B sale channel and soldStandalone true")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_162_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","brand_emi");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Brand EMI Addon with P4B sale channel and soldStandalone false")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_163_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","brand_emi");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Brand EMI Addon with PSA App sale channel and soldStandalone true")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_164_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","brand_emi");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Brand EMI Addon with PSA App sale channel and soldStandalone false")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_165_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","brand_emi");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Brand EMI Addon with Agent SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_166_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithAgentSRO"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("agentPermissions", "sb_mapping_ashwin1");
        body.put("mid", "qa11VE51594478161509");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","brand_emi");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Brand EMI Addon with Cleavertap Campagin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_167_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "OHDpzh00093359209989");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","brand_emi");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Brand EMI Addon with Gas and Petrol MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_168_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","brand_emi");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Brand EMI Addon without Agent ID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_169_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","brand_emi");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Brand EMI Addon without MID and Generic SRO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_170_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","brand_emi");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Brand EMI Addon with Gas and Petrol MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_171_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("mid", "SBTest10068671323127");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","brand_emi");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","false");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Accessory Device Stand- Checking Response Code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_172_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","accessory_device_stand");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("delayedSettlement", "false");
        body.put("merchantType", "BIG");
        body.put("ppiLimit", "2");
        body.put("category", "BFSI");
        body.put("businessWallet", "true");
        body.put("subCategory", "Loans");
        body.put("merchantAgeType", "NEW");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Accessory Device Stand Checking if for wrong keys it is sending 400 or not")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_173_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","accessory_device_standd");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("delayedSettlement", "false");
        body.put("merchantType", "BIG");
        body.put("ppiLimit", "2");
        body.put("category", "BFSI");
        body.put("businessWallet", "true");
        body.put("subCategory", "Loans");
        body.put("merchantAgeType", "NEW");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);
        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Accessory Device Stand Checking if for wrong keys it is sending 400 or not")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_174_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","accessory_device_standd");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("delayedSettlement", "false");
        body.put("merchantType", "BIG");
        body.put("ppiLimit", "2");
        body.put("category", "BFSI");
        body.put("businessWallet", "true");
        body.put("subCategory", "Loans");
        body.put("merchantAgeType", "NEW");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);
        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - AddOn - Accessory Device Stand - saleChannel - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_175_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","accessory_device_stand");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("delayedSettlement", "false");
        body.put("merchantType", "BIG");
        body.put("ppiLimit", "2");
        body.put("category", "BFSI");
        body.put("businessWallet", "true");
        body.put("subCategory", "Loans");
        body.put("merchantAgeType", "NEW");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);
        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode saleChannel : filters) {
                if (!saleChannel.isEmpty()) {
                    JsonNode saleChannelNode = saleChannel.get("saleChannel");
                    if (saleChannelNode != null && saleChannelNode.isArray()) {
                        for (JsonNode valueNode : saleChannelNode) {
                            String saleChannelValue = valueNode.asText();
                            filterValues.add(saleChannelValue);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String a : filterValues) {
            if(!a.equals("PSA_APP")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - AddOn - Accessory Device Stand - soldStandAlone - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_176_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","accessory_device_stand");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("delayedSettlement", "false");
        body.put("merchantType", "BIG");
        body.put("ppiLimit", "2");
        body.put("category", "BFSI");
        body.put("businessWallet", "true");
        body.put("subCategory", "Loans");
        body.put("merchantAgeType", "NEW");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);
        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode soldStandAlone : filters) {
                if (!soldStandAlone.isEmpty()) {
                    JsonNode soldStandAloneNode = soldStandAlone.get("soldStandAlone");
                    if (soldStandAloneNode != null && soldStandAloneNode.isArray()) {
                        for (JsonNode valueNode : soldStandAloneNode) {
                            String soldStandAloneValue = valueNode.asText();
                            filterValues.add(soldStandAloneValue);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String a : filterValues) {
            System.out.println(a);
            if(!a.equals("YES")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - AddOn - Accessory Device Stand - SubPlanType Seaching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_177_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType", "accessory_device_stand");
        body.put("saleChannel", "PSA_APP");
        body.put("soldStandAlone", "true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("delayedSettlement", "false");
        body.put("merchantType", "BIG");
        body.put("ppiLimit", "2");
        body.put("category", "BFSI");
        body.put("businessWallet", "true");
        body.put("subCategory", "Loans");
        body.put("merchantAgeType", "NEW");
        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);
        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        JsonNode plansNode = jsonNode.get("data").get("deviceInfo").get(0).get("plans");

        // Extracting values of "subPlanType" from the JSON response
        List<String> subPlanTypes = new ArrayList<>();
        for (JsonNode plan : plansNode) {
            JsonNode subPlanTypeNode = plan.get("subPlanType");
            if (subPlanTypeNode != null) {
                subPlanTypes.add(subPlanTypeNode.asText());
            }
        }
        int flag = 1;
        for (String subPlanType : subPlanTypes) {

            if(!subPlanType.equals("accessory_device_stand")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - AddOn - Accessory Device Stand - Gas and Petrol")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_178_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType", "accessory_device_stand");
        body.put("saleChannel", "PSA_APP");
        body.put("soldStandAlone", "true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("mid", "SBTest10068671323127");
        body.put("delayedSettlement", "false");
        body.put("ppiLimit", "2");
        body.put("businessWallet", "true");
        body.put("merchantAgeType", "NEW");
        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - AddOn - Accessory Device Stand - Gas and Petrol - Search")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_179_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType", "accessory_device_stand");
        body.put("saleChannel", "PSA_APP");
        body.put("soldStandAlone", "true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("mid", "SBTest10068671323127");
        body.put("delayedSettlement", "false");
        body.put("ppiLimit", "2");
        body.put("businessWallet", "true");
        body.put("merchantAgeType", "NEW");
        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode gasAndPetrol : filters) {
                if(!gasAndPetrol.isEmpty()) {
                    JsonNode gasAndPetrolNode = gasAndPetrol.get("category");
                    if (gasAndPetrolNode != null && gasAndPetrolNode.isArray()) {
                        for (JsonNode valueNode : gasAndPetrolNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("Gas and Petrol")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC -Shop Insurance- Checking Response Code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_180_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("delayedSettlement", "false");
        body.put("merchantType", "BIG");
        body.put("ppiLimit", "2");
        body.put("category", "BFSI");
        body.put("businessWallet", "true");
        body.put("subCategory", "Loans");
        body.put("merchantAgeType", "NEW");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Shop Insurance Checking if for wrong keys it is sending 400 or not")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_181_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","PSA");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("delayedSettlement", "false");
        body.put("merchantType", "BIG");
        body.put("ppiLimit", "2");
        body.put("category", "BFSI");
        body.put("businessWallet", "true");
        body.put("subCategory", "Loans");
        body.put("merchantAgeType", "NEW");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);
        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "ODS Fetch Plan - EDC - Shop Insurance Checking if for wrong keys it is sending 400 or not")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_182_ODSFetchPlan() {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_onns");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("delayedSettlement", "false");
        body.put("merchantType", "BIG");
        body.put("ppiLimit", "2");
        body.put("category", "BFSI");
        body.put("businessWallet", "true");
        body.put("subCategory", "Loans");
        body.put("merchantAgeType", "NEW");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);
        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - AddOn - Shop Insurance - saleChannel - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_183_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("delayedSettlement", "false");
        body.put("merchantType", "BIG");
        body.put("ppiLimit", "2");
        body.put("category", "BFSI");
        body.put("businessWallet", "true");
        body.put("subCategory", "Loans");
        body.put("merchantAgeType", "NEW");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);
        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode saleChannel : filters) {
                if (!saleChannel.isEmpty()) {
                    JsonNode saleChannelNode = saleChannel.get("saleChannel");
                    if (saleChannelNode != null && saleChannelNode.isArray()) {
                        for (JsonNode valueNode : saleChannelNode) {
                            String saleChannelValue = valueNode.asText();
                            filterValues.add(saleChannelValue);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String a : filterValues) {
            if(!a.equals("PSA_APP")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 0);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - AddOn - Shop Insurance - soldStandAlone - Searching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_184_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj=new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType","shop_insurance");
        body.put("saleChannel","PSA_APP");
        body.put("soldStandAlone","true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("delayedSettlement", "false");
        body.put("merchantType", "BIG");
        body.put("ppiLimit", "2");
        body.put("category", "BFSI");
        body.put("businessWallet", "true");
        body.put("subCategory", "Loans");
        body.put("merchantAgeType", "NEW");
        String jwt=createAuth0JwsHMACTEST(clientId,Key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);
        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj,headers,body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for (JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for (JsonNode soldStandAlone : filters) {
                if (!soldStandAlone.isEmpty()) {
                    JsonNode soldStandAloneNode = soldStandAlone.get("soldStandAlone");
                    if (soldStandAloneNode != null && soldStandAloneNode.isArray()) {
                        for (JsonNode valueNode : soldStandAloneNode) {
                            String soldStandAloneValue = valueNode.asText();
                            filterValues.add(soldStandAloneValue);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String a : filterValues) {
            System.out.println(a);
            if(!a.equals("YES")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - AddOn - Shop Insurance - SubPlanType Seaching")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_185_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType", "shop_insurance");
        body.put("saleChannel", "PSA_APP");
        body.put("soldStandAlone", "true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("delayedSettlement", "false");
        body.put("merchantType", "BIG");
        body.put("ppiLimit", "2");
        body.put("category", "BFSI");
        body.put("businessWallet", "true");
        body.put("subCategory", "Loans");
        body.put("merchantAgeType", "NEW");
        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);
        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        JsonNode plansNode = jsonNode.get("data").get("deviceInfo").get(0).get("plans");

        // Extracting values of "subPlanType" from the JSON response
        List<String> subPlanTypes = new ArrayList<>();
        for (JsonNode plan : plansNode) {
            JsonNode subPlanTypeNode = plan.get("subPlanType");
            if (subPlanTypeNode != null) {
                subPlanTypes.add(subPlanTypeNode.asText());
            }
        }
        int flag = 1;
        for (String subPlanType : subPlanTypes) {

            if(!subPlanType.equals("shop_insurance")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - AddOn - Shop Insurance - Gas and Petrol")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_186_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType", "shop_insurance");
        body.put("saleChannel", "PSA_APP");
        body.put("soldStandAlone", "true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("mid", "SBTest10068671323127");
        body.put("delayedSettlement", "false");
        body.put("ppiLimit", "2");
        body.put("businessWallet", "true");
        body.put("merchantAgeType", "NEW");
        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "EDC - AddOn - Shop Insurance - Gas and Petrol - Search")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_187_ODSFetchPlan() throws JsonProcessingException {

        FetchPlanODS odsObj = new FetchPlanODS(P.TESTDATA.get("FetchPlanPRCAddonWithMID"));
        Map<String, String> body = new HashMap<>();
        body.put("agentId", "**********");
        body.put("serviceCategory", "EDC");
        body.put("planType", "add_ons");
        body.put("subPlanType", "shop_insurance");
        body.put("saleChannel", "PSA_APP");
        body.put("soldStandAlone", "true");
        body.put("strategy", "PRICING_COMPONENT");
        body.put("subCategory", "All");
        body.put("category", "All");
        body.put("mid", "SBTest10068671323127");
        body.put("delayedSettlement", "false");
        body.put("ppiLimit", "2");
        body.put("businessWallet", "true");
        body.put("merchantAgeType", "NEW");
        String jwt = createAuth0JwsHMACTEST(clientId, Key);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("jwt", jwt);

        Response FetchPlanObjResponse = ODSPlanObject.odsFetchPlan(odsObj, headers, body);

        String resp = FetchPlanObjResponse.asString();
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode jsonNode = objectMapper.readTree(resp);
        List<JsonNode> plans = jsonNode.findParents("plans");
        List<String> filterValues = new ArrayList<>();

        for(JsonNode objectNode : plans) {
            List<JsonNode> filters = objectNode.findValues("filters");

            for(JsonNode gasAndPetrol : filters) {
                if(!gasAndPetrol.isEmpty()) {
                    JsonNode gasAndPetrolNode = gasAndPetrol.get("category");
                    if (gasAndPetrolNode != null && gasAndPetrolNode.isArray()) {
                        for (JsonNode valueNode : gasAndPetrolNode) {
                            String categoryValues = valueNode.asText();
                            filterValues.add(categoryValues);
                        }
                    }
                }
            }
        }

        int flag = 1;
        for(String s: filterValues) {
            if(!s.equals("Gas and Petrol")) {
                flag = 0;
            }
        }
        Assert.assertEquals(flag, 1);
    }
}
