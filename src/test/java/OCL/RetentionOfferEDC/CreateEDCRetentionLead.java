package OCL.RetentionOfferEDC;


import Request.RetentionOfferEDC.RetentionOfferEDC;
import Services.DBConnection.DBConnection;
import Services.RetentionOfferEDC.RetentionOfferEDCServices;
import com.goldengate.common.BaseMethod;
import com.goldengate.common.RetryAnalyzer;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class CreateEDCRetentionLead extends BaseMethod {

    RetentionOfferEDCServices retentionOfferEDCServices=new RetentionOfferEDCServices();

    private static final Logger LOGGER = LogManager.getLogger(CreateEDCRetentionLead.class);

    String AgentToken ="";

    String mobileNo= "7722127717";
    String solution_type="retention";

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLogin() throws Exception {
        AgentToken = AgentSessionToken("8010630022", "paytm@123");
        LOGGER.info("Agent Token  : " + AgentToken);
       // establishConnectiontoServer(AgentToken,5);
        waitForLoad(3000);
     /* TestBase testBase =new TestBase();
      DbName = DbStagingSprint;
       testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '"+mobileNo+"' and status = '2' and solution_type='device_accessory_deployment';");
       int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes); */

        DBConnection.UpdateQueryToCloseLead(mobileNo,solution_type);

    }

    @Test(priority = 0, description = "Create a new lead with all valid details", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
       headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
       // headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info(" Create a new lead with all valid details" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
         String leadId = respObj.jsonPath().getJsonObject("leadId");
         LOGGER.info(" LeadId : " +leadId);
    }

    @Test(priority = 0, description = "Create a new lead without token", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
       // headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token","");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead without token" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 401);

    }


    @Test(priority = 0, description = "Create a new lead with invalid token", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
        //headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token","ananna");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");


        Response respObj = null;
        try {

            respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        } catch (PatternSyntaxException e) {

        }

        if (respObj != null) {
            LOGGER.info("Create a new lead with invalid token" + respObj.statusCode());
            Assert.assertEquals(respObj.statusCode(), 410);
        }
    }


    @Test(priority = 0, description = "Create a new lead without solution Type", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
       // headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead without solution Type" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("displayMessage").contains("Mobile number not associated to your account. Kindly update through 'Need Help section on Login screen of Paytm App/Web' to proceed further"));

        Assert.assertTrue(respObj.jsonPath().getString("internalMessage").contains("Request Validation failed for create lead request"));


    }

    @Test(priority = 0, description = "Create a new lead without solution Type Level 2", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
       headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
        //headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead without solution Type Level 2" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);

        Assert.assertTrue(respObj.jsonPath().getString("displayMessage").contains("Lead already exists. Please continue working on same lead"));

    }

    @Test(priority = 0, description = "Create a new lead without model name", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
       // headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead without model name" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);

        //Assert.assertTrue(respObj.jsonPath().getString("displayMessage").contains("Lead already exists. Please continue working on same lead"));

    }

    @Test(priority = 0, description = "Create a new lead without serial number", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
       // headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead without serial number" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);

        Assert.assertTrue(respObj.jsonPath().getString("displayMessage").contains("Lead already exists. Please continue working on same lead"));

    }

    @Test(priority = 0, description = "Create a new lead without OS Type", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
       // headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead without OS Type" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);

        Assert.assertTrue(respObj.jsonPath().getString("displayMessage").contains("Lead already exists. Please continue working on same lead"));

    }

    @Test(priority = 0, description = "Create a new lead without EDC QR SCAN DETAILS", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
      //  headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead without EDC QR SCAN DETAILS" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);

        Assert.assertTrue(respObj.jsonPath().getString("displayMessage").contains("Lead already exists. Please continue working on same lead"));

    }


    @Test(priority = 0, description = "Create a new lead without device ID", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
       // headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead without device ID" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);

        //Assert.assertTrue(respObj.jsonPath().getString("displayMessage").contains("Lead already exists. Please continue working on same lead"));

    }


    @Test(priority = 0, description = "Create a new lead without user Mobile Number", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
       // headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead without user Mobile Number" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 500);

        //Assert.assertTrue(respObj.jsonPath().getString("displayMessage").contains("Lead already exists. Please continue working on same lead"));

    }

    @Test(priority = 0, description = "Create a new lead without MID", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
        //headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead without MID" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("displayMessage").contains("Mandatory parameters missing"));

    }

    @Test(priority = 0, description = "Create a new lead invalid MID", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
       headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
       // headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","abaan");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead invalid MID" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("displayMessage").contains("Invalid MID. MID not found on BOSS, please proceed with correct MID."));

    }

    @Test(priority = 0, description = "Create a new lead without entity type", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
        //headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead without entity type" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 500);

       // Assert.assertTrue(respObj.jsonPath().getString("displayMessage").contains("Invalid MID. MID not found on BOSS, please proceed with correct MID."));

    }

    @Test(priority = 0, description = "Create a new lead with invalid type", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
        //headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","ababab");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead with invalid type" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 500);

        // Assert.assertTrue(respObj.jsonPath().getString("displayMessage").contains("Invalid MID. MID not found on BOSS, please proceed with correct MID."));

    }

    @Test(priority = 0, description = "Create a new lead without merchant Name", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
        //headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead without merchant Name" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);

        Assert.assertTrue(respObj.jsonPath().getString("displayMessage").contains("Lead already exists. Please continue working on same lead"));

    }

    @Test(priority = 0, description = "Create a new lead without user CUST ID", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
       headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
       // headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead without user CUST ID" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);

        Assert.assertTrue(respObj.jsonPath().getString("displayMessage").contains("Lead already exists. Please continue working on same lead"));

    }

    @Test(priority = 0, description = "Create a new lead without agent CUST ID", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_018() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
       // headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","");
        body.put("solutionSubType","edc");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead without agent CUST ID" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 500);

        Assert.assertTrue(respObj.jsonPath().getString("statusCode").contains("500"));

    }

    @Test(priority = 0, description = "Create a new lead without Solution Subtype", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_019() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
       // headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","");
        body.put("beatTagId","************");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead without Solution Subtype" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 500);

        Assert.assertTrue(respObj.jsonPath().getString("statusCode").contains("500"));

    }

    @Test(priority = 0, description = "Create a new lead without beatTagID", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_020() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
        //headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","**********");
        body.put("solutionSubType","edc");
        body.put("beatTagId","");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead without beatTagID" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("statusCode").contains("400"));

        Assert.assertTrue(respObj.jsonPath().getString("displayMessage").contains("Failed to create a retention offer lead. Beat details are mandatory for this operation. Please provide the beat details and try again."));
    }

    @Test(priority = 0, description = "Create a new lead with invalid agentCUSTID", groups = {
            "Regression" },retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_021() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        RetentionOfferEDC retentionOfferEDC = new RetentionOfferEDC(P.TESTDATA.get("CreateRetentionOfferRequest"));

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json;charset=UTF-8");
        headers.put("deviceidentifier", "OnePlus-CPH2487-f14072026ac7dc49");
       // headers.put("deviceidentifier", "OPPO-CPH1859-***************");

        headers.put("session_token",AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionType", "retention");
        body.put("solutionTypeLevel2","edc");
        body.put("modelName","A910");
        body.put("serialNo","JYOTK13517011");
        body.put("osType","ANDROID");
        body.put("edcQrScanDetailsString","{\\\"modelName\\\":\\\"A910\\\",\\\"serialNo\\\":\\\"JYOTK13517011\\\",\\\"osType\\\":\\\"ANDROID\\\"}");
        body.put("deviceId","JYOTK13517011");
        body.put("userMobile","8888333331");
        body.put("mid","KyeiXd63386517982023");
        body.put("entityType","PROPRIETORSHIP");
        body.put("merchantName","Gagan Tyagi");
        body.put("userCustId","1700946008");
        body.put("agentCustId","hahaj");
        body.put("solutionSubType","edc");
        body.put("beatTagId","bahajaj");

        Response respObj = retentionOfferEDCServices.CreateLeadRetentionOfferEDC(retentionOfferEDC,body, headers);

        LOGGER.info("Create a new lead with invalid agentCUSTID" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 500);

        Assert.assertTrue(respObj.jsonPath().getString("statusCode").contains("500"));

        //Assert.assertTrue(respObj.jsonPath().getString("displayMessage").contains("Failed to create a retention offer lead. Beat details are mandatory for this operation. Please provide the beat details and try again."));
    }



}
