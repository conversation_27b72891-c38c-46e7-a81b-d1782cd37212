package OCL.Billing;

import Request.Billing.BillingOnboard;
import Services.Billing.BillingMiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import java.util.HashMap;
import java.util.Map;

public class OnboardUSNBilling extends BaseMethod{
    BillingMiddlewareServices billingMiddlewareServicesObject = new BillingMiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(OnboardUSNBilling.class);
    public static String MID = "dievrQ41934892349254";
    public static String custId = "1701064376";
    public static String Mobile_Number = "5567123093";
    public static String USN1 = "";
    public static String USN2 = "";



    @Test(priority = 0, groups = {"Regression"}, description = "Generate USN")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void GenerateUSN1() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN1 = "BHA"+SerialNo+"W";
        LOGGER.info("USN1 is :" +USN1);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "Generate USN")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void GenerateUSN2(){
        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN2 = "BHA"+SerialNo+"W";
        LOGGER.info("USN2 is :" +USN2);
    }

    @Test(priority = 0, description = "Onboard Terminal on Billing with Usage Deposit Plan", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "dievrQ41934892349254");
        body.put("usn",USN1);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "USAGE_DEPOSIT");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);
        LOGGER.info("Billing Onboard with USAGE Deposit " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);
    }
    @Test(priority = 0, description = "Onboard Terminal on Billing with LifeTime Plan ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Billing Onboard with LifeTime Fee " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
    }
    @Test(priority = 0, description = "Onboard USN on Billing with invalid token ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token","SDERVDC");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard USN on Billing with invalid token " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 401);
    }
    @Test(priority = 0, description = "Onboard Terminal on Billing without token ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard Terminal on Billing without token" + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 401);
    }
    @Test(priority = 0, description = "Onboard Terminal on Billing without MID ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "");
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard Terminal on Billing without MID " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }

    @Test(priority = 0, description = "Onboard Terminal on Billing without USN ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn","");
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard Terminal on Billing without USN " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }

    @Test(priority = 0, description = "Onboard Terminal on Billing without Service Type EDC ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard Terminal on Billing without Service Type EDC " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }
    @Test(priority = 0, description = "Onboard Terminal on Billing without device Type ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard Terminal on Billing without device Type " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }
    @Test(priority = 0, description = "Onboard Terminal on Billing without device Model ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);


        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard Terminal on Billing without device Model " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }

    @Test(priority = 0, description = "Onboard Terminal on Billing without Upfront order id ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard Terminal on Billing without device Model " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }


    @Test(priority = 0, description = "Onboard Terminal on Billing without Onboard date ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate","");
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard Terminal on Billing without Onboard date " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }
    @Test(priority = 0, description = "Onboard Terminal on Billing without Refund Source ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard Terminal on Billing without Refund Source " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }

    @Test(priority = 0, description = "Onboard Terminal on Billing without Start Date ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate","");
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard Terminal on Billing without Start Date " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }

    @Test(priority = 0, description = "Onboard Terminal on Billing without End Date ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate","");
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard Terminal on Billing without End Date " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }

    @Test(priority = 0, description = "Onboard Terminal on Billing without Amount", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard Terminal on Billing without Amount " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }

    @Test(priority = 0, description = "Onboard Terminal on Billing with invalid Onboard Date", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate","0000-00-00");
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard Terminal on Billing with invalid Onboard Date " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }

    @Test(priority = 0, description = "Onboard Terminal on Billing with invalid Start Date", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate","0000-00-00");
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard Terminal on Billing with invalid Start Date " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }

    @Test(priority = 0, description = "Onboard Terminal on Billing with invalid End Date", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_018_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate","0000-00-00");
        body.put("amount","");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard Terminal on Billing with invalid End Date " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }

    @Test(priority = 0, description = "Onboard Terminal with same USN again", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_019_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Billing Onboard with LifeTime Fee " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "Onboard Terminal on Billing with invalid amount ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_020_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);


        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","absd");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Onboard Terminal on Billing with invalid amount " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }

}

