package OCL.Billing;

import Request.Billing.BillingOnboard;
import Request.Billing.BillingReturn;
import Services.Billing.BillingMiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class ReturnUSNBilling extends BaseMethod {

    BillingMiddlewareServices billingMiddlewareServicesObject = new BillingMiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(OnboardUSNBilling.class);
    public static String MID = "dievrQ41934892349254";
    public static String custId = "1701064376";
    public static String Mobile_Number = "5567123093";
    public static String USN1 = "";
    public static String USN2 = "";

    // create a method to generate token for the following script
   
    @Test(priority = 0, groups = {"Regression"}, description = "Generate USN")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void GenerateUSN1() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN1 = "BHA"+SerialNo+"W";
        LOGGER.info("USN1 is :" +USN1);
    }
    @Test(priority = 0, groups = {"Regression"}, description = "Generate USN")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void GenerateUSN2(){
        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN2 = "BHA"+SerialNo+"W";
        LOGGER.info("USN2 is :" +USN2);
    }


    @Test(priority = 0, description = "Onboard Terminal on Billing with Usage Deposit Plan to be returned", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "dievrQ41934892349254");
        body.put("usn",USN1);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "USAGE_DEPOSIT");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Billing Onboard with USAGE Deposit " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
    }
    @Test(priority = 0, description = "Onboard Terminal on Billing with LifeTime Plan to be returned ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_OnboardUSNBilling() {
        BillingOnboard billingOnboard = new BillingOnboard(P.TESTDATA.get("BillingOnboardRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("deviceType","ANDROID");
        body.put("deviceModel","A50");
        body.put("upfrontOrderId","28393477");
        body.put("onboardDate",java.time.LocalDate.now().toString());
        body.put("refundSource", "LIFETIME_FEE");
        body.put("startDate",java.time.LocalDate.now().toString());
        body.put("endDate",java.time.LocalDate.now().plusDays(30).toString());
        body.put("amount","100.00");
        body.put("startDate1",java.time.LocalDate.now().plusDays(31).toString());
        body.put("endDate1","9999-12-31");
        body.put("amount1","0");


        Response respObj = billingMiddlewareServicesObject.billingonboardservices(billingOnboard,body, headers);

        LOGGER.info("Billing Onboard with LifeTime Fee " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
    }
    @Test(priority = 0, description = "Return Terminal from Billing with amc opted true ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003() {
        BillingReturn billingReturn = new BillingReturn(P.TESTDATA.get("BillingReturnRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN1);
        body.put("service","EDC");
        body.put("amcOpted","true");
        body.put("returnReqDate",java.time.LocalDate.now().toString());
        body.put("question","screenDamagedCost");
        body.put("answer","no");
        body.put("question2","screenDamagedCost");
        body.put("answer2","no");
        body.put("question3","screenDamagedCost");
        body.put("answer3","no");
        body.put("question4","screenDamagedCost");
        body.put("answer4","no");
        body.put("question5","screenDamagedCost");
        body.put("answer5","no");
        body.put("question6","screenDamagedCost");
        body.put("answer6","no");
        body.put("question7","screenDamagedCost");
        body.put("answer7","no");
        body.put("question8","screenDamagedCost");
        body.put("answer8","no");
        body.put("question9","screenDamagedCost");
        body.put("answer9","no");
        body.put("question10","screenDamagedCost");
        body.put("answer10","no");
        body.put("question11","screenDamagedCost");
        body.put("answer11","no");


        Response respObj = billingMiddlewareServicesObject.billingreturnservices(billingReturn,body, headers);
        LOGGER.info("Return Terminal from Billing with amc opted true " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);
    }
    @Test(priority = 0, description = "Return Terminal from Billing with amc opted false ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004() {
        BillingReturn billingReturn = new BillingReturn(P.TESTDATA.get("BillingReturnRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("amcOpted","false");
        body.put("returnReqDate",java.time.LocalDate.now().toString());
        body.put("question","screenDamagedCost");
        body.put("answer","no");
        body.put("question2","screenDamagedCost");
        body.put("answer2","no");
        body.put("question3","screenDamagedCost");
        body.put("answer3","no");
        body.put("question4","screenDamagedCost");
        body.put("answer4","no");
        body.put("question5","screenDamagedCost");
        body.put("answer5","no");
        body.put("question6","screenDamagedCost");
        body.put("answer6","no");
        body.put("question7","screenDamagedCost");
        body.put("answer7","no");
        body.put("question8","screenDamagedCost");
        body.put("answer8","no");
        body.put("question9","screenDamagedCost");
        body.put("answer9","no");
        body.put("question10","screenDamagedCost");
        body.put("answer10","no");
        body.put("question11","screenDamagedCost");
        body.put("answer11","no");


        Response respObj = billingMiddlewareServicesObject.billingreturnservices(billingReturn,body, headers);
        LOGGER.info("Return Terminal from Billing with amc opted false " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);
    }


    @Test(priority = 0, description = "Return Terminal from Billing without token", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005() {
        BillingReturn billingReturn = new BillingReturn(P.TESTDATA.get("BillingReturnRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token","");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn","yfgdhjsjs");
        body.put("service","EDC");
        body.put("amcOpted","true");
        body.put("returnReqDate",java.time.LocalDate.now().toString());
        body.put("question","screenDamagedCost");
        body.put("answer","no");
        body.put("question2","screenDamagedCost");
        body.put("answer2","no");
        body.put("question3","screenDamagedCost");
        body.put("answer3","no");
        body.put("question4","screenDamagedCost");
        body.put("answer4","no");
        body.put("question5","screenDamagedCost");
        body.put("answer5","no");
        body.put("question6","screenDamagedCost");
        body.put("answer6","no");
        body.put("question7","screenDamagedCost");
        body.put("answer7","no");
        body.put("question8","screenDamagedCost");
        body.put("answer8","no");
        body.put("question9","screenDamagedCost");
        body.put("answer9","no");
        body.put("question10","screenDamagedCost");
        body.put("answer10","no");
        body.put("question11","screenDamagedCost");
        body.put("answer11","no");


        Response respObj = billingMiddlewareServicesObject.billingreturnservices(billingReturn,body, headers);
        LOGGER.info("Return Terminal from Billing without token " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 401);
    }

    @Test(priority = 0, description = "Return Terminal from Billing with invalid token", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006() {
        BillingReturn billingReturn = new BillingReturn(P.TESTDATA.get("BillingReturnRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token","ghsjkaaa");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("usn","yfgdhjsjs");
        body.put("service","EDC");
        body.put("amcOpted","true");
        body.put("returnReqDate",java.time.LocalDate.now().toString());
        body.put("question","screenDamagedCost");
        body.put("answer","no");
        body.put("question2","screenDamagedCost");
        body.put("answer2","no");
        body.put("question3","screenDamagedCost");
        body.put("answer3","no");
        body.put("question4","screenDamagedCost");
        body.put("answer4","no");
        body.put("question5","screenDamagedCost");
        body.put("answer5","no");
        body.put("question6","screenDamagedCost");
        body.put("answer6","no");
        body.put("question7","screenDamagedCost");
        body.put("answer7","no");
        body.put("question8","screenDamagedCost");
        body.put("answer8","no");
        body.put("question9","screenDamagedCost");
        body.put("answer9","no");
        body.put("question10","screenDamagedCost");
        body.put("answer10","no");
        body.put("question11","screenDamagedCost");
        body.put("answer11","no");

        Response respObj = null;
        try {
            respObj =  billingMiddlewareServicesObject.billingreturnservices(billingReturn,body, headers);
        }
        catch (PatternSyntaxException e) {

        }
        if (respObj != null) {
            LOGGER.info("Return Terminal from Billing with invalid token " + respObj.statusCode());
            Assert.assertEquals(respObj.statusCode(), 401);
        }
    }
    @Test(priority = 0, description = "Return Terminal from Billing without MID ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007() {
        BillingReturn billingReturn = new BillingReturn(P.TESTDATA.get("BillingReturnRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "");
        body.put("usn",USN1);
        body.put("service","EDC");
        body.put("amcOpted","true");
        body.put("returnReqDate",java.time.LocalDate.now().toString());
        body.put("question","screenDamagedCost");
        body.put("answer","no");
        body.put("question2","screenDamagedCost");
        body.put("answer2","no");
        body.put("question3","screenDamagedCost");
        body.put("answer3","no");
        body.put("question4","screenDamagedCost");
        body.put("answer4","no");
        body.put("question5","screenDamagedCost");
        body.put("answer5","no");
        body.put("question6","screenDamagedCost");
        body.put("answer6","no");
        body.put("question7","screenDamagedCost");
        body.put("answer7","no");
        body.put("question8","screenDamagedCost");
        body.put("answer8","no");
        body.put("question9","screenDamagedCost");
        body.put("answer9","no");
        body.put("question10","screenDamagedCost");
        body.put("answer10","no");
        body.put("question11","screenDamagedCost");
        body.put("answer11","no");


        Response respObj = billingMiddlewareServicesObject.billingreturnservices(billingReturn,body, headers);
        LOGGER.info("Return Terminal from Billing without MID " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
    }
    @Test(priority = 0, description = "Return Terminal from Billing with invalid MID ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008() {
        BillingReturn billingReturn = new BillingReturn(P.TESTDATA.get("BillingReturnRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "gshhsjaka");
        body.put("usn",USN1);
        body.put("service","EDC");
        body.put("amcOpted","true");
        body.put("returnReqDate",java.time.LocalDate.now().toString());
        body.put("question","screenDamagedCost");
        body.put("answer","no");
        body.put("question2","screenDamagedCost");
        body.put("answer2","no");
        body.put("question3","screenDamagedCost");
        body.put("answer3","no");
        body.put("question4","screenDamagedCost");
        body.put("answer4","no");
        body.put("question5","screenDamagedCost");
        body.put("answer5","no");
        body.put("question6","screenDamagedCost");
        body.put("answer6","no");
        body.put("question7","screenDamagedCost");
        body.put("answer7","no");
        body.put("question8","screenDamagedCost");
        body.put("answer8","no");
        body.put("question9","screenDamagedCost");
        body.put("answer9","no");
        body.put("question10","screenDamagedCost");
        body.put("answer10","no");
        body.put("question11","screenDamagedCost");
        body.put("answer11","no");


        Response respObj = billingMiddlewareServicesObject.billingreturnservices(billingReturn,body, headers);
        LOGGER.info("Return Terminal from Billing without MID " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 417);
    }
    @Test(priority = 0, description = "Return Terminal from Billing without USN ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009() {
        BillingReturn billingReturn = new BillingReturn(P.TESTDATA.get("BillingReturnRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",MID );
        body.put("usn","");
        body.put("service","EDC");
        body.put("amcOpted","true");
        body.put("returnReqDate",java.time.LocalDate.now().toString());
        body.put("question","screenDamagedCost");
        body.put("answer","no");
        body.put("question2","screenDamagedCost");
        body.put("answer2","no");
        body.put("question3","screenDamagedCost");
        body.put("answer3","no");
        body.put("question4","screenDamagedCost");
        body.put("answer4","no");
        body.put("question5","screenDamagedCost");
        body.put("answer5","no");
        body.put("question6","screenDamagedCost");
        body.put("answer6","no");
        body.put("question7","screenDamagedCost");
        body.put("answer7","no");
        body.put("question8","screenDamagedCost");
        body.put("answer8","no");
        body.put("question9","screenDamagedCost");
        body.put("answer9","no");
        body.put("question10","screenDamagedCost");
        body.put("answer10","no");
        body.put("question11","screenDamagedCost");
        body.put("answer11","no");


        Response respObj = billingMiddlewareServicesObject.billingreturnservices(billingReturn,body, headers);
        LOGGER.info("Return Terminal from Billing without USN " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
    }

    @Test(priority = 0, description = "Return Terminal from Billing with invalid USN ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010() {
        BillingReturn billingReturn = new BillingReturn(P.TESTDATA.get("BillingReturnRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",MID );
        body.put("usn","bcvcbn");
        body.put("service","EDC");
        body.put("amcOpted","true");
        body.put("returnReqDate",java.time.LocalDate.now().toString());
        body.put("question","screenDamagedCost");
        body.put("answer","no");
        body.put("question2","screenDamagedCost");
        body.put("answer2","no");
        body.put("question3","screenDamagedCost");
        body.put("answer3","no");
        body.put("question4","screenDamagedCost");
        body.put("answer4","no");
        body.put("question5","screenDamagedCost");
        body.put("answer5","no");
        body.put("question6","screenDamagedCost");
        body.put("answer6","no");
        body.put("question7","screenDamagedCost");
        body.put("answer7","no");
        body.put("question8","screenDamagedCost");
        body.put("answer8","no");
        body.put("question9","screenDamagedCost");
        body.put("answer9","no");
        body.put("question10","screenDamagedCost");
        body.put("answer10","no");
        body.put("question11","screenDamagedCost");
        body.put("answer11","no");


        Response respObj = billingMiddlewareServicesObject.billingreturnservices(billingReturn,body, headers);
        LOGGER.info("Return Terminal from Billing without USN " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 417);
    }

    @Test(priority = 0, description = "Return Terminal from Billing without Service ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011() {
        BillingReturn billingReturn = new BillingReturn(P.TESTDATA.get("BillingReturnRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",MID );
        body.put("usn","gshsakak");
        body.put("service","");
        body.put("amcOpted","true");
        body.put("returnReqDate",java.time.LocalDate.now().toString());
        body.put("question","screenDamagedCost");
        body.put("answer","no");
        body.put("question2","screenDamagedCost");
        body.put("answer2","no");
        body.put("question3","screenDamagedCost");
        body.put("answer3","no");
        body.put("question4","screenDamagedCost");
        body.put("answer4","no");
        body.put("question5","screenDamagedCost");
        body.put("answer5","no");
        body.put("question6","screenDamagedCost");
        body.put("answer6","no");
        body.put("question7","screenDamagedCost");
        body.put("answer7","no");
        body.put("question8","screenDamagedCost");
        body.put("answer8","no");
        body.put("question9","screenDamagedCost");
        body.put("answer9","no");
        body.put("question10","screenDamagedCost");
        body.put("answer10","no");
        body.put("question11","screenDamagedCost");
        body.put("answer11","no");


        Response respObj = billingMiddlewareServicesObject.billingreturnservices(billingReturn,body, headers);
        LOGGER.info("Return Terminal from Billing without Service " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
    }
    @Test(priority = 0, description = "Return Terminal from Billing with invalid Service ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012() {
        BillingReturn billingReturn = new BillingReturn(P.TESTDATA.get("BillingReturnRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",MID );
        body.put("usn","gshsakak");
        body.put("service","gshsjs");
        body.put("amcOpted","true");
        body.put("returnReqDate",java.time.LocalDate.now().toString());
        body.put("question","screenDamagedCost");
        body.put("answer","no");
        body.put("question2","screenDamagedCost");
        body.put("answer2","no");
        body.put("question3","screenDamagedCost");
        body.put("answer3","no");
        body.put("question4","screenDamagedCost");
        body.put("answer4","no");
        body.put("question5","screenDamagedCost");
        body.put("answer5","no");
        body.put("question6","screenDamagedCost");
        body.put("answer6","no");
        body.put("question7","screenDamagedCost");
        body.put("answer7","no");
        body.put("question8","screenDamagedCost");
        body.put("answer8","no");
        body.put("question9","screenDamagedCost");
        body.put("answer9","no");
        body.put("question10","screenDamagedCost");
        body.put("answer10","no");
        body.put("question11","screenDamagedCost");
        body.put("answer11","no");


        Response respObj = billingMiddlewareServicesObject.billingreturnservices(billingReturn,body, headers);
        LOGGER.info("Return Terminal from Billing with invalid Service " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
    }
    @Test(priority = 0, description = "Return Terminal from Billing without AMC opted ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013() {
        BillingReturn billingReturn = new BillingReturn(P.TESTDATA.get("BillingReturnRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",MID );
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("amcOpted","");
        body.put("returnReqDate",java.time.LocalDate.now().toString());
        body.put("question","screenDamagedCost");
        body.put("answer","no");
        body.put("question2","screenDamagedCost");
        body.put("answer2","no");
        body.put("question3","screenDamagedCost");
        body.put("answer3","no");
        body.put("question4","screenDamagedCost");
        body.put("answer4","no");
        body.put("question5","screenDamagedCost");
        body.put("answer5","no");
        body.put("question6","screenDamagedCost");
        body.put("answer6","no");
        body.put("question7","screenDamagedCost");
        body.put("answer7","no");
        body.put("question8","screenDamagedCost");
        body.put("answer8","no");
        body.put("question9","screenDamagedCost");
        body.put("answer9","no");
        body.put("question10","screenDamagedCost");
        body.put("answer10","no");
        body.put("question11","screenDamagedCost");
        body.put("answer11","no");


        Response respObj = billingMiddlewareServicesObject.billingreturnservices(billingReturn,body, headers);
        LOGGER.info("Return Terminal from Billing without AMC opted " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);
    }
    @Test(priority = 0, description = "Return Terminal from Billing without date ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014() {
        BillingReturn billingReturn = new BillingReturn(P.TESTDATA.get("BillingReturnRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",MID );
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("amcOpted","true");
        body.put("returnReqDate","");
        body.put("question","screenDamagedCost");
        body.put("answer","no");
        body.put("question2","screenDamagedCost");
        body.put("answer2","no");
        body.put("question3","screenDamagedCost");
        body.put("answer3","no");
        body.put("question4","screenDamagedCost");
        body.put("answer4","no");
        body.put("question5","screenDamagedCost");
        body.put("answer5","no");
        body.put("question6","screenDamagedCost");
        body.put("answer6","no");
        body.put("question7","screenDamagedCost");
        body.put("answer7","no");
        body.put("question8","screenDamagedCost");
        body.put("answer8","no");
        body.put("question9","screenDamagedCost");
        body.put("answer9","no");
        body.put("question10","screenDamagedCost");
        body.put("answer10","no");
        body.put("question11","screenDamagedCost");
        body.put("answer11","no");


        Response respObj = billingMiddlewareServicesObject.billingreturnservices(billingReturn,body, headers);
        LOGGER.info("Return Terminal from Billing without date " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
    }

    @Test(priority = 0, description = "Return Terminal from Billing with invalid date ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015() {
        BillingReturn billingReturn = new BillingReturn(P.TESTDATA.get("BillingReturnRequest"));
        String billingToken=createAuth0JwsHMAC("oe-billing-client","ZjM0OGRkYTdlY2YxMmE4ZjZhN2YzNg==");
        System.out.println("Billing Token is:"+ billingToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-client-id","oe-billing-client");
        headers.put("x-client-token",billingToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",MID );
        body.put("usn",USN2);
        body.put("service","EDC");
        body.put("amcOpted","true");
        body.put("returnReqDate","0000-00-00");
        body.put("question","screenDamagedCost");
        body.put("answer","no");
        body.put("question2","screenDamagedCost");
        body.put("answer2","no");
        body.put("question3","screenDamagedCost");
        body.put("answer3","no");
        body.put("question4","screenDamagedCost");
        body.put("answer4","no");
        body.put("question5","screenDamagedCost");
        body.put("answer5","no");
        body.put("question6","screenDamagedCost");
        body.put("answer6","no");
        body.put("question7","screenDamagedCost");
        body.put("answer7","no");
        body.put("question8","screenDamagedCost");
        body.put("answer8","no");
        body.put("question9","screenDamagedCost");
        body.put("answer9","no");
        body.put("question10","screenDamagedCost");
        body.put("answer10","no");
        body.put("question11","screenDamagedCost");
        body.put("answer11","no");


        Response respObj = billingMiddlewareServicesObject.billingreturnservices(billingReturn,body, headers);
        LOGGER.info("Return Terminal from Billing with invalid date " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);
    }


}

