package OCL.HDFC.POS.Onboarding;

import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.openqa.selenium.WebDriver;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class HDFCPosTest extends HDFCPosBaseAPi{
	
	
	private static final Logger LOGGER = LogManager.getLogger(HDFCPosTest.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	
	protected WebDriver driver;
	
	
	HDFCPosBaseAPi HDFCPosBaseAPiBaseClassObject= new HDFCPosBaseAPi();
	BaseMethod BaseMethodObject=new BaseMethod();
	
	String clientId="OEBatch";
	String iss="OE";
	String RequestPath="";
	String Authorization= HDFCPosBaseAPiBaseClassObject.generateJwtTokenForHDFC( clientId,iss);
    String solutionType="offline_merchant";
    String solutionTypeLevel2="HDFC";
    String leadId;
    String mobile=UtilitiesObject.randomMobileNumberGenerator();
    String email="<EMAIL>";
    String businessName="TOUCH WOOD LIMITED";
    String displayName="EUROSPACE CONSULTANT PVT LTD";
    String mcc="4214";
    String bankAccountHolderName="TOUCH WOOD LIMITED";
    String tid=HDFCPosBaseAPiBaseClassObject.GenerateUniqueTID();
    String meCode=HDFCPosBaseAPiBaseClassObject.GenerateUniqueMECode();
    String pan=UtilitiesObject.randomIndividualPANValueGenerator();
    String bankAccountNumber=HDFCPosBaseAPiBaseClassObject.GenerateAccountNo();
    
    String bankName="HDFC Bank";
    String ifsc="HDFC0000039";
    String accountType="Current";
    String line1="corres12";
    String line3="corres12";
    String pincode="110078";
   
    
	
	@Test(description = "Create Lead Without Solution Type", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC01_CreateLeadWithoutSolutionType() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String solutionType="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Missing Entity Type");
				verifyResponseCodeAs400BadRequest(responseObject);
				
				
		}   
	
	
	@Test(description = "Create Lead Without Solution Type Level 2", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC02_CreateLeadWithoutSolutionTypeLevel2() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String solutionTypeLevel2="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Missing solutionTypeLevel2 in request");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	@Test(description = "Create Lead Without Mobile", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC03_CreateLeadWithoutMobile() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String mobile="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Missing mobile number");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 

	
	@Test(description = "Create Lead With Invalid Solution Type Level 2", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC04_CreateLeadWithInvalidSolutionTypeLevel2() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String solutionTypeLevel2="Testing";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);
			
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "no mapped setname found ");
				//verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	
	
	@Test(description = "Create Lead Without MCC Code", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC05_CreateLeadWithoutMCC() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String mcc="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);
			
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid mcc code. No cat-subcat mapped");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	@Test(description = "Create Lead With Invalid MCC code", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC06_CreateLeadWithInvlalidMCC() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String mcc="4545";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);
			
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid mcc code. No cat-subcat mapped");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		}
	
	
	@Test(description = "Create Lead Without Email", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC07_CreateLeadWithoutMobile() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String email="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please enter valid Email ID and try again.");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	
	@Test(description = "Create Lead Without Business Name", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC08_CreateLeadWithoutBusinessName() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String businessName="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Missing business name.");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	
	@Test(description = "Create Lead Without Display Name", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC09_CreateLeadWithoutDisplayName() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String displayName ="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Display Name is Mandatory");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	
	@Test(description = "Create Lead Without Bank Account Holder Name", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC10_CreateLeadWithoutBankAccHolderName() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String bankAccountHolderName ="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Account Holder Name is empty in request");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	
	
	@Test(description = "Create Lead Without Solution Type & Solution Type Level 2", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC11_CreateLeadWithoutSolutionTypeandSolutionTypeLevel2() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String solutionType="";
		    String solutionTypeLevel2="";
		    		
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Missing Entity Type");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	@Test(description = "Create Lead Without Solution Type , Solution Type Level 2 and Mobile", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC12_CreateLeadWithoutSolutionMobile() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String solutionType="";
		    String solutionTypeLevel2="";
		    String mobile="";
		    		
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Missing Entity Type");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	@Test(description = "Create Lead Without Solution Type , Solution Type Level 2, Mobile and Email", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC13_CreateLeadWithoutMobileEmail() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String solutionType="";
		    String solutionTypeLevel2="";
		    String mobile="";
		    String email="";
		    		
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Missing Entity Type");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	@Test(description = "Create Lead Without Solution Type , Solution Type Level 2, Mobile, Email and Business Name", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC14_CreateLeadWithoutEmailBusinessName() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String solutionType="";
		    String solutionTypeLevel2="";
		    String mobile="";
		    String email="";
		    String businessName="";
		    		
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Missing business name.");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	
	@Test(description = "Create Lead Without Solution Type , Solution Type Level 2, Mobile, Email,Business Name and Display Name", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC15_CreateLeadWithoutBusinessDisplayName() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String solutionType="";
		    String solutionTypeLevel2="";
		    String mobile="";
		    String email="";
		    String businessName="";
		    String displayName="";
		    		
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Missing business name.");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	@Test(description = "Create Lead Without Solution Type , Solution Type Level 2, Mobile, Email,Business Name, Display Name and mcc", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC16_CreateLeadWithoutBusinessDisplayMCC() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String solutionType="";
		    String solutionTypeLevel2="";
		    String mobile="";
		    String email="";
		    String businessName="";
		    String displayName="";
		    String mcc="";
		    		
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Missing business name.");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	@Test(description = "Create Lead Without MCC and TID ", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC17_CreateLeadWithoutMCCandTID() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String mcc="";
		    String tid="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);
			
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid mcc code. No cat-subcat mapped");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	@Test(description = "Create Lead Without Bank Account number", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC18_CreateLeadWithoutBankAccNum() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String bankAccountNumber ="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Missing  Bank Account Number");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	
	@Test(description = "Create Lead Without ME Code", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC19_CreateLeadWithoutMECode() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String meCode ="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "MeCode is empty in request");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	@Test(description = "Create Lead Without ME Code and TID", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC20_CreateLeadWithoutMECodeTID() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String meCode ="";
		    String tid="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "MeCode is empty in request");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	
	@Test(description = "Create Lead Without ME Code and MCC", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC21_CreateLeadWithoutMECodeMCC() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String meCode ="";
		    String mcc="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid mcc code. No cat-subcat mapped");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	
	@Test(description = "Create Lead Without ME Code, TID and MCC", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC22_CreateLeadWithoutMECodeMCC() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String meCode ="";
		    String tid="";
		    String mcc="";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid mcc code. No cat-subcat mapped");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	@Test(description = "Create Lead With Invalid Bank Account number", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC23_CreateLeadWithInvalidBankAccNum() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    
		    String bankAccountNumber ="vhfbvhf3643";
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				//Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Missing  Bank Account Number");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	
	@Test(description = "Create Lead Without Bank name", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC24_CreateLeadWithoutBankName() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    String bankName="";
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Missing  Bank Name");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	@Test(description = "Create Lead Without IFSC Code", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC25_CreateLeadWithoutIFSC() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    String ifsc="";
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "IFSC Code Missing");
				verifyResponseCodeAs400BadRequest(responseObject);
				
		} 
	
	@Test(description = "Create Lead With Invalid IFSC Code", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC26_CreateLeadWithInvalidIFSC() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    String ifsc="TEST123";
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid ifsc");
				
				
		} 
	
	@Test(description = "Create Lead Without Pincode", groups = {"Regression" },enabled=true)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC27_CreateLeadWithoutPincode() {
			
			Map<String, String> headers = new HashMap<String, String>();
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", Authorization);
			
		    RequestPath="HDFC_POS/CompanyOnboardRequest.json";
		    String pincode="";
		    
		
			Map<String, String> body = new HashMap<String, String>();
			body.put("solutionType", solutionType);
			body.put("solutionTypeLevel2", solutionTypeLevel2);
			body.put("mobile", mobile);
			body.put("email", email);
			body.put("businessName", businessName);
			body.put("displayName", displayName);
			body.put("mcc", mcc);
			body.put("bankAccountHolderName", bankAccountHolderName);
			body.put("tid", tid);
			body.put("meCode", meCode);
			
			body.put("pan",pan);
			body.put("bankAccountNumber", bankAccountNumber);
			body.put("bankName", bankName);
			body.put("ifsc", ifsc);
			body.put("accountType", accountType);
			body.put("line1", line1);
			body.put("line3", line3);
			body.put("pincode", pincode);


			
			
		
			Response responseObject = HDFCPosBaseAPiBaseClassObject.CreateLead(headers, body, RequestPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
				Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("No such pincode exists"));
				
				
				
				
		} 
}
