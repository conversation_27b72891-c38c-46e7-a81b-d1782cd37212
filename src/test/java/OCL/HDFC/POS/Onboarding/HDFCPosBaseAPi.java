package OCL.HDFC.POS.Onboarding;

import Request.HDFCPOS.CreateHDFCLead;
import Services.oAuth.oAuthServices;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.ssh.SSHConnection;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;

import java.sql.Connection;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

public class HDFCPosBaseAPi extends BaseMethod{

	 private static final Logger LOGGER = LogManager.getLogger(HDFCPosBaseAPi.class);

	    oAuthServices oAuthServicesObject = new oAuthServices();
	    static Connection connection = null;
	    static SSHConnection sshConnection = new SSHConnection();
	    public static String DbName = "sprint33_2";
	
	    
	    
	    /*
	     * Method to create Lead
	     * @param headers
	     * @param body
	     * @return
	     */
	    
	    
	    public Response CreateLead(Map<String, String> headers, Map<String, String> body,String RequestPath) {

	        CreateHDFCLead createLeadObject = new CreateHDFCLead(RequestPath);

	        for (Map.Entry m : headers.entrySet()) {
	        	createLeadObject.setHeader(m.getKey().toString(), m.getValue().toString());
	        }

	        for (Map.Entry m : body.entrySet()) {
	        	createLeadObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
	        }

	        Response createUserObjectResponse = createLeadObject.callAPI();

	        return createUserObjectResponse;
	    }
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    /**
	     * Verify  Response Code as 200 OK
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs200OK(Response responseObject) {

	        LOGGER.info("Status Code : " +responseObject.getStatusCode());

	        Assert.assertEquals(responseObject.getStatusCode(),200);

	    }
	    
	    /**
	     * Verify  Response Code as 400 Bad Request
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs400BadRequest(Response responseObject) {

	        LOGGER.info("Status Code : " +responseObject.getStatusCode());

	        Assert.assertEquals(responseObject.getStatusCode(),400);

	    }
	    /**
	     * Verify  Response Code as 403 Bad Request
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs403BadRequest(Response responseObject) {

	        LOGGER.info("Status Code : " +responseObject.getStatusCode());

	        Assert.assertEquals(responseObject.getStatusCode(),403);

	    }
	    
	    
	    /**
	     * Verify  Response Code as 401 Unauthorized
	     * @param responseObject
	     */
	    public void verifyResponseCodeAs401Unauthorized(Response responseObject) {

	    	
	        LOGGER.info("Status Code : " +responseObject.getStatusCode());
	        Assert.assertEquals(responseObject.getStatusCode(),401);

	    }
	    
	    
	    /**
	     * Method to generate JWT token using epoch time
	     * @param clientId,iss
	     * @return
	     */
	    public String generateJwtTokenForHDFC(String clientId,String iss)
	    {
	        LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of("GMT+05:30"));
	        String timeStamp = localDateTime.toString();
	        Algorithm buildAlgorithm = Algorithm.HMAC256( "OEBatch_SECRET" );
	        String token = JWT.create()
	                .withClaim("clientId", clientId)
	                .withClaim("iss",iss)
	                .withClaim("timestamp", timeStamp + "+05:30")
	                .withIssuer("OE")
	                .sign(buildAlgorithm);
	        return token;
	    }
	    
	    /**
	     * Method to generate 15 digit account number
	     * 
	     * @return
	     */
	    
	    
	  //Generate Random 15 Digit Account No. for Create Merchant
	    public String GenerateAccountNo() { 
	     String numbers = "**********"; 
	     // create a super set of all characters 
	     String allCharacters = numbers; 
	     // initialize a string to hold result 
	     StringBuffer randomString = new StringBuffer(); 
	     // loop for 10 times 
	     for (int i = 0; i < 15; i++) { 
	       // generate a random number between 0 and 9
	       int randomIndex = (int)(Math.random() * allCharacters.length()); 
	       // retrieve character at index and add it to result 
	       randomString.append(allCharacters.charAt(randomIndex)); 
	     } 
	     return randomString.toString(); 
	    } 
	    
	    
	    
	  //Generate Unique 4 Digit Random Number
	    public String GenerateRandomNumber()
	    {
	    	   String str = GenerateRandomDigit4();
	    	   String RandomNumber4= str;
	    	   return RandomNumber4;

	    }	
	    
	    
	    //Generate 3 Character Random String
	    static String usingMath() { 
	        String alphabetsInUpperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"; 
	        
	       
	        // create a super set of all characters 
	        String allCharacters = alphabetsInUpperCase; 
	        // initialize a string to hold result 
	        StringBuffer randomString = new StringBuffer(); 
	        // loop for 3 times 
	        for (int i = 0; i < 3; i++) { 
	          // generate a random number of all characters 
	          int randomIndex = (int)(Math.random() * allCharacters.length()); 
	          // retrieve character at index and add it to result 
	          randomString.append(allCharacters.charAt(randomIndex)); 
	        } 
	        return randomString.toString(); 
	      } 
	    
	    //Generate Random 4 Digit number for Pan Card
	    static String GenerateRandomDigit4() { 
	        String numbers = "**********"; 
	        // create a super set of all characters 
	        String allCharacters = numbers; 
	        // initialize a string to hold result 
	        StringBuffer randomString = new StringBuffer(); 
	        // loop for 10 times 
	        for (int i = 0; i < 4; i++) { 
	          // generate a random number between 0 and 9  
	          int randomIndex = (int)(Math.random() * allCharacters.length()); 
	          // retrieve character at index and add it to result 
	          randomString.append(allCharacters.charAt(randomIndex)); 
	        } 
	        return randomString.toString(); 
	      } 
	    
	  //Generate Unique Pan Number
	    public String GeneratePanNumber() {
	    	
	    	String First3Letter= usingMath();
	    	String RandomNumberforPan=GenerateRandomNumber();
	    	String UniquePanNumber= First3Letter+"PW"+RandomNumberforPan+"H";
	    	return UniquePanNumber;
	    	
	    	
	    	
	    }
	    
	  //generates a unique MeCode
		public String GenerateUniqueMECode() {
		int n = 9;
		Random randGen = new Random();

		int startNum = (int) Math.pow(10, n-1);
		int range = (int) (Math.pow(10, n) - startNum + 1);

		int randomNum = randGen.nextInt(range) + startNum;
		String ME_CODE="MECODE"+randomNum;
		return ME_CODE;
		}
		
		 //generates a unique TID
		public String GenerateUniqueTID() {
		int n = 9;
		Random randGen = new Random();

		int startNum = (int) Math.pow(10, n-1);
		int range = (int) (Math.pow(10, n) - startNum + 1);

		int randomNum = randGen.nextInt(range) + startNum;
		String TID="TID"+randomNum;
		return TID;
		}
		
		 //Generate Random 10 Digit mobileNumber
		public String GenerateMobileNumber() { 
		    String numbers = "**********"; 
		    // create a super set of all characters 
		    String allCharacters = numbers; 
		    // initialize a string to hold result 
		    StringBuffer randomString = new StringBuffer(); 
		    // loop for 10 times 
		    for (int i = 0; i < 9; i++) { 
		      // generate a random number between 0 and 9
		      int randomIndex = (int)(Math.random() * allCharacters.length()); 
		      // retrieve character at index and add it to result 
		      randomString.append(allCharacters.charAt(randomIndex)); 
		    } 
		    randomString.toString(); 
		    String MobileNumber="5"+randomString;
		    return MobileNumber; 
		  }

		  /**
	     * Method to generate Boss Session token 
	     * @return
	     */
	    
		public String createJwtHMAC(String clientId, String key) {
			byte[] decodedKey = Base64.getMimeDecoder().decode(key);
			Map<String, Object> claims = new HashMap<String, Object>();
			claims.put("client-id", clientId);
			@SuppressWarnings("deprecation")
			String token = Jwts.builder().setIssuedAt(new Date()).addClaims(claims)
					.signWith(SignatureAlgorithm.HS512, decodedKey).compact();
			return token;
		}
	    
	    
	    
}
