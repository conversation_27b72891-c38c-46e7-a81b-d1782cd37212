package OCL.BankChannel;

import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.v1.bankcp.BankChannelLeadCreation;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class BankChannelSubventionFlow extends BaseMethod {

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(BankChannelSubventionFlow.class);

    public static String jwtToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FQmF0Y2giLCJpc3MiOiJPRSIsInRpbWVzdGFtcCI6IjIwMjMtMTAtMTNUMTg6NDc6MTkuMTUxKzA1OjMwIn0.5rCW7Q_pGR0Jo_2gogaEoVUNplsahaU5e-PPc1otsxI";
    public static String clientId = "OEBatch";
    public static String clientsecret = "OEBatch_SECRET";
    public static String leadId = "";
    public static String AUBankLeadId = "";
    public static String JanaBankLeadId = "";
    public static String SvcBankLeadId = "";
    public static String SaraswatBankLeadId = "";
    public static String KARNATAKABankLeadId = "";
    public static String DcbBankLeadId = "";
    public static Integer mobileNumber;

    @Test(priority = 1, groups = {"Regression"}, description = "Au Bank channel lead creation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_bankChannelLeadCreationForAUBank() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "AU SMALL FINANCE BANK LIMITED");
        body.put("solutionTypeLevel2", "AU_SMALL_FINANCE_BANK_LIMITED");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 200) {
            AUBankLeadId = bankObjResp.jsonPath().get("leadId");
            LOGGER.info(" AU bank LeadId is  :" + AUBankLeadId);
            Assert.assertEquals(bankObjResp.jsonPath().get("displayMessage"), "Lead successfully created");
            Assert.assertEquals(bankObjResp.statusCode(), 200);
        } else {
            LOGGER.info("Lead is not created");
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Au Bank channel lead creation when existing lead present")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_bankChannelLeadCreationWhenExistingLeadPresent() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "AU SMALL FINANCE BANK LIMITED");
        body.put("solutionTypeLevel2", "AU_SMALL_FINANCE_BANK_LIMITED");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Dedupe Failed. Bank intent Lead is in progress for mobile"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }


    }

    @Test(priority = 1, groups = {"Regression"}, description = "Au Bank channel lead creation with lifetime amount zero")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_bankChannelLeadCreationWithLifetimeAmountZero() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "AU SMALL FINANCE BANK LIMITED");
        body.put("solutionTypeLevel2", "AU_SMALL_FINANCE_BANK_LIMITED");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "200");
        body.put("rentalAmount", "0");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("If LifeTime fee is greater than 0 , rental frequency should be lifeTime or rental amount should be 0"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }


    }

    @Test(priority = 1, groups = {"Regression"}, description = "Verify All Pricing Mandatory Or None")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_VerifyAllPricingMandatoryOrNone() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "AU SMALL FINANCE BANK LIMITED");
        body.put("solutionTypeLevel2", "AU_SMALL_FINANCE_BANK_LIMITED");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("either all pricing components should be there or none"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }


    }

    @Test(priority = 1, groups = {"Regression"}, description = "Verify All DC Slab Should Present")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_VerifyAllDcSlabShouldPresent() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "AU SMALL FINANCE BANK LIMITED");
        body.put("solutionTypeLevel2", "AU_SMALL_FINANCE_BANK_LIMITED");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("either both (debit card less than 2k or debit card greater than 2k) values should present or not"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }


    }

    @Test(priority = 1, groups = {"Regression"}, description = "Verify Amex Card Mdr Not More Than 10")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_VerifyAmexCardMdrNotMoreThan10() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "AU SMALL FINANCE BANK LIMITED");
        body.put("solutionTypeLevel2", "AU_SMALL_FINANCE_BANK_LIMITED");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "12");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Invalid value provided.it should be between 0 and 10"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }


    }

    @Test(priority = 1, groups = {"Regression"}, description = "Sum Of Pricing Greater Than Zero")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_SumOfPricingGreaterThanZero() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "AU SMALL FINANCE BANK LIMITED");
        body.put("solutionTypeLevel2", "AU_SMALL_FINANCE_BANK_LIMITED");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "0");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "0");
        body.put("usageDeposit", "0");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "9");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Sum of all pricing params should be greater than 0"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }


    }

    @Test(priority = 1, groups = {"Regression"}, description = "Approver Email Required For Subvention")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_ApproverEmailRequiredForSubvention() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "AU SMALL FINANCE BANK LIMITED");
        body.put("solutionTypeLevel2", "AU_SMALL_FINANCE_BANK_LIMITED");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "220");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "220");
        body.put("usageDeposit", "330");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "9");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Approver Email is required if default MDR and pricing Params are not used"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }


    }

    @Test(priority = 1, groups = {"Regression"}, description = "Dc Is Required With EmiDc")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_DcIsRequiredWithEmiDc() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "AU SMALL FINANCE BANK LIMITED");
        body.put("solutionTypeLevel2", "AU_SMALL_FINANCE_BANK_LIMITED");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "");
        body.put("lifeTimeFee", "");
        body.put("rentalAmount", "");
        body.put("usageDeposit", "");
        body.put("debitCardsLessThan2K", "");
        body.put("debitCardsGreaterThan2K", "");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "");
        body.put("amex", "9");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Emi CC can only be provided when credit card value is there"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }


    }

    @Test(priority = 1, groups = {"Regression"}, description = "Valid Approver Email Required")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_ValidApproverEmailRequired() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "AU SMALL FINANCE BANK LIMITED");
        body.put("solutionTypeLevel2", "AU_SMALL_FINANCE_BANK_LIMITED");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "220");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "2");
        body.put("creditCard", "3");
        body.put("amex", "9");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "jbkgmail.com");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Invalid Email Address Provided"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }


    }

    @Test(priority = 1, groups = {"Regression"}, description = "Jana Bank channel lead creation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_bankChannelLeadCreationForJanaBank() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 200) {
            JanaBankLeadId = bankObjResp.jsonPath().get("leadId");
            LOGGER.info(" Jana bank LeadId is  :" + JanaBankLeadId);
            Assert.assertEquals(bankObjResp.jsonPath().get("displayMessage"), "Lead successfully created");
            Assert.assertEquals(bankObjResp.statusCode(), 200);
        } else {
            LOGGER.info("Lead is not created");
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Svc Bank channel lead creation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_bankChannelLeadCreationForSvcBank() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "SVC Cooperative Bank");
        body.put("solutionTypeLevel2", "SVC_COOPERATIVE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 200) {
            SvcBankLeadId = bankObjResp.jsonPath().get("leadId");
            LOGGER.info(" Svc bank LeadId is  :" + SvcBankLeadId);
            Assert.assertEquals(bankObjResp.jsonPath().get("displayMessage"), "Lead successfully created");
            Assert.assertEquals(bankObjResp.statusCode(), 200);
        } else {
            LOGGER.info("Lead is not created");
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Saraswat Bank channel lead creation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_bankChannelLeadCreationForSaraswatBank() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "Saraswat Bank Ltd");
        body.put("solutionTypeLevel2", "SARASWAT_BANK_LTD");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 200) {
            SaraswatBankLeadId = bankObjResp.jsonPath().get("leadId");
            LOGGER.info(" Saraswat bank LeadId is  :" + SaraswatBankLeadId);
            Assert.assertEquals(bankObjResp.jsonPath().get("displayMessage"), "Lead successfully created");
            Assert.assertEquals(bankObjResp.statusCode(), 200);
        } else {
            LOGGER.info("Lead is not created");
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Karnataka Bank channel lead creation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014_bankChannelLeadCreationForKarnatakaBank() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "KARNATAKA BANK LTD");
        body.put("solutionTypeLevel2", "KARNATAKA_BANK_LTD");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 200) {
            KARNATAKABankLeadId = bankObjResp.jsonPath().get("leadId");
            LOGGER.info(" KARNATAKA bank LeadId is  :" + KARNATAKABankLeadId);
            Assert.assertEquals(bankObjResp.jsonPath().get("displayMessage"), "Lead successfully created");
            Assert.assertEquals(bankObjResp.statusCode(), 200);
        } else {
            LOGGER.info("Lead is not created");
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "DCB Bank channel lead creation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015_bankChannelLeadCreationForDCBBank() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "DCB BANK LIMITED");
        body.put("solutionTypeLevel2", "DCB_BANK_LIMITED");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 200) {
            DcbBankLeadId = bankObjResp.jsonPath().get("leadId");
            LOGGER.info(" DCB bank LeadId is  :" + DcbBankLeadId);
            Assert.assertEquals(bankObjResp.jsonPath().get("displayMessage"), "Lead successfully created");
            Assert.assertEquals(bankObjResp.statusCode(), 200);
        } else {
            LOGGER.info("Lead is not created");
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Bank channel lead creation for diff reseller")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016_bankChannelLeadCreationForDiffReseller() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "DCB BANK LIMITED");
        body.put("solutionTypeLevel2", "DCB_BANK_LIMITED");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Mid linked with mobile is not tagged with reseller"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Bank channel lead creation without subvention")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017_bankChannelLeadCreationWithoutSubvention() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "DCB BANK LIMITED");
        body.put("solutionTypeLevel2", "DCB_BANK_LIMITED");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "");
        body.put("lifeTimeFee", "");
        body.put("rentalAmount", "");
        body.put("usageDeposit", "");
        body.put("debitCardsLessThan2K", "");
        body.put("debitCardsGreaterThan2K", "");
        body.put("debitCardsRupay", "");
        body.put("creditCard", "");
        body.put("amex", "");
        body.put("diners", "");
        body.put("corporateCards", "");
        body.put("emiCC", "");
        body.put("emiDC", "");
        body.put("approverEmail", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 200) {
            DcbBankLeadId = bankObjResp.jsonPath().get("leadId");
            LOGGER.info(" Jana bank LeadId is  :" + DcbBankLeadId);
            Assert.assertEquals(bankObjResp.jsonPath().get("displayMessage"), "Lead successfully created");
            Assert.assertEquals(bankObjResp.statusCode(), 200);
        } else {
            LOGGER.info("Lead is not created");
        }
    }

    @Test(priority = 1, description = "fetch lead status", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_018_fetchLeadStatusforSubvention() {

        if (SaraswatBankLeadId != null) {
            FetchLead v1FetchLeadObj = new FetchLead(SaraswatBankLeadId);
            XMWCookie = findXMWTokenforPanel("**********", "paytm@123");
            Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
            String leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
            System.out.println("Lead stage is " + leadStage);
        } else {
            LOGGER.info("Lead is not created yet");
        }
    }

    @Test(priority = 1, description = "fetch lead status", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_O19_fetchLeadStatusWithoutSubvention() {

        if (DcbBankLeadId != null) {
            FetchLead v1FetchLeadObj = new FetchLead(DcbBankLeadId);
            XMWCookie = findXMWTokenforPanel("**********", "paytm@123");
            Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
            String leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
            System.out.println("Lead stage is " + leadStage);
        } else {
            LOGGER.info("Lead is not created yet");
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Jana Bank channel lead creation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_020_bankChannelLeadCreationForExistingMidWithSubvention() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "uTvuRY68885032516020");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 200) {
            JanaBankLeadId = bankObjResp.jsonPath().get("leadId");
            LOGGER.info(" Jana bank LeadId is  :" + JanaBankLeadId);
            Assert.assertEquals(bankObjResp.jsonPath().get("displayMessage"), "Lead successfully created");
            Assert.assertEquals(bankObjResp.statusCode(), 200);
        } else {
            LOGGER.info("Lead is not created");
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "EMI_DC Is Required With DC")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_021_EMI_DCIsRequiredWithDC() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "AU SMALL FINANCE BANK LIMITED");
        body.put("solutionTypeLevel2", "AU_SMALL_FINANCE_BANK_LIMITED");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "");
        body.put("lifeTimeFee", "");
        body.put("rentalAmount", "");
        body.put("usageDeposit", "");
        body.put("debitCardsLessThan2K", "2");
        body.put("debitCardsGreaterThan2K", "3");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "4");
        body.put("amex", "9");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "");
        body.put("emiDC", "");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Emi CC can only be provided when credit card value is there"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }

    }

    @Test(priority = 1, groups = {"Regression"}, description = "CC override  Is Required With all cards Amex")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_022_CCOverrideIsRequiredWithCCCards() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "AU SMALL FINANCE BANK LIMITED");
        body.put("solutionTypeLevel2", "AU_SMALL_FINANCE_BANK_LIMITED");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "");
        body.put("lifeTimeFee", "");
        body.put("rentalAmount", "");
        body.put("usageDeposit", "");
        body.put("debitCardsLessThan2K", "");
        body.put("debitCardsGreaterThan2K", "");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "");
        body.put("amex", "9");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "");
        body.put("emiDC", "");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Emi CC can only be provided when credit card value is there"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }

    }

    @Test(priority = 1, description = "fetch lead status with different bank agent", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_023_fetchLeadWithDiffBankAgent() {

        if (SaraswatBankLeadId != null) {
            FetchLead v1FetchLeadObj = new FetchLead(SaraswatBankLeadId);
            XMWCookie = findXMWTokenforPanel("<EMAIL>", "paytm@123");
            Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
            LOGGER.info("Status code is : " + v1FetchLeadResp.statusCode());
            if (v1FetchLeadResp.getStatusCode() == 400) {
                String actualMsg = v1FetchLeadResp.jsonPath().get("message");
                Assert.assertTrue(actualMsg.contains("Lead is not associated with Bank Channel Partner"));
                Assert.assertEquals(v1FetchLeadResp.statusCode(), 400);
            }
        } else {
            LOGGER.info("Lead is not created yet");
        }
    }

    @Test(priority = 1, description = "fetch lead by admin Having Bank Partner Permission", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_024_VerifyfetchLeadByAdminHavingBankPartnerPermission() {

        if (JanaBankLeadId != null) {
            FetchLead v1FetchLeadObj = new FetchLead(JanaBankLeadId);
            XMWCookie = findXMWTokenforPanel("<EMAIL>", "paytm@123");
            Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
            LOGGER.info("Status code is : " + v1FetchLeadResp.statusCode());
            Assert.assertEquals(v1FetchLeadResp.statusCode(), 200);
            if (v1FetchLeadResp.getStatusCode() == 400) {
                String actualMsg = v1FetchLeadResp.jsonPath().get("message");
                Assert.assertTrue(actualMsg.contains("Lead is not associated with Bank Channel Partner"));
                Assert.assertEquals(v1FetchLeadResp.statusCode(), 400);
            }
        } else {
            LOGGER.info("Lead is not created yet");
        }
    }

    @Test(priority = 1, description = "fetch lead by admin Not Having Bank Partner Permission", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_025_VerifyfetchLeadByAdminNotHavingBankPartnerPermission() {

        if (DcbBankLeadId != null) {
            FetchLead v1FetchLeadObj = new FetchLead(DcbBankLeadId);
            XMWCookie = findXMWTokenforPanel("<EMAIL>", "paytm@123");
            Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
            LOGGER.info("Status code is : " + v1FetchLeadResp.statusCode());
            if (v1FetchLeadResp.getStatusCode() == 400) {
                String actualMsg = v1FetchLeadResp.jsonPath().get("message");
                Assert.assertTrue(actualMsg.contains("Lead is not associated with Bank Channel Partner"));
                Assert.assertEquals(v1FetchLeadResp.statusCode(), 400);
            }
        } else {
            LOGGER.info("Lead is not created yet");
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Karnataka Bank channel lead creation with invalid device identifier")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_026_bankChannelLeadCreationForKarnatakaBankWithInvalidModel() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "KARNATAKA BANK LTD");
        body.put("solutionTypeLevel2", "KARNATAKA_BANK_LTD");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Device details not present for model: linux_nexgog2"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Karnataka Bank channel lead creation with invalid device count")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_027_bankChannelLeadCreationForKarnatakaBankWithInvalidDeviceCount() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "KARNATAKA BANK LTD");
        body.put("solutionTypeLevel2", "KARNATAKA_BANK_LTD");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "0");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Your details could not be saved. Please enter valid Device Count and try again"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Karnataka Bank channel lead creation with invalid bank")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_028_bankChannelLeadCreationForKarnatakaBankWithInvalidbank() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "KARNATAKA BANK LTD 1");
        body.put("solutionTypeLevel2", "KARNATAKA_BANK_LTD");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Invalid bank provided"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Karnataka Bank channel lead creation with invalid solution type level 2")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_029_bankChannelLeadCreationForKarnatakaBankWithInvalidSolutionTypeLevel2() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "KARNATAKA BANK LTD");
        body.put("solutionTypeLevel2", "KARNATAKA_BANK_LTD1");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "0");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Bank and SolutionTypeLevel2 mismatched"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Karnataka Bank channel lead creation with empty line3")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_030_bankChannelLeadCreationForKarnatakaBankWithEmptyLine3() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "KARNATAKA BANK LTD");
        body.put("solutionTypeLevel2", "KARNATAKA_BANK_LTD");
        body.put("line1", "test 1");
        body.put("line3", "");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Line 3 is null or empty"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Karnataka Bank channel lead creation with empty line1")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_031_bankChannelLeadCreationForKarnatakaBankWithEmptyLine1() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "KARNATAKA BANK LTD");
        body.put("solutionTypeLevel2", "KARNATAKA_BANK_LTD");
        body.put("line1", "");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Line 1 is null or empty"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Karnataka Bank channel lead creation with empty requestIdentifier")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_032_bankChannelLeadCreationForKarnatakaBankWithEmptyrequestIdentifier() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "KARNATAKA BANK LTD");
        body.put("solutionTypeLevel2", "KARNATAKA_BANK_LTD");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Request identifier is empty in request"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Karnataka Bank channel lead creation with empty deviceIdentifier")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_033_bankChannelLeadCreationForKarnatakaBankWithEmptydeviceIdentifier() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "KARNATAKA BANK LTD");
        body.put("solutionTypeLevel2", "KARNATAKA_BANK_LTD");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Device model is empty in request "));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Karnataka Bank channel lead creation with empty bank")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_034_bankChannelLeadCreationForKarnatakaBankWithEmptyBank() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", " ");
        body.put("solutionTypeLevel2", "KARNATAKA_BANK_LTD");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "0");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Invalid bank provided"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Karnataka Bank channel lead creation with empty solutionTypeLevel2")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_035_bankChannelLeadCreationForKarnatakaBankWithEmptysolutionTypeLevel2() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "KARNATAKA BANK LTD");
        body.put("solutionTypeLevel2", "  ");
        body.put("line1", "test 1");
        body.put("line3", "");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "0");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Bank and SolutionTypeLevel2 mismatched"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Karnataka Bank channel lead creation with empty mobile")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_036_bankChannelLeadCreationForKarnatakaBankWithEmptyMobile() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", " ");
        body.put("bank", "KARNATAKA BANK LTD");
        body.put("solutionTypeLevel2", "KARNATAKA_BANK_LTD");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Invalid Mobile"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Karnataka Bank channel lead creation with invalid mobile")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_037_bankChannelLeadCreationForKarnatakaBankWithInvalidMobile() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "91" + mobileNumber);
        body.put("bank", "KARNATAKA BANK LTD");
        body.put("solutionTypeLevel2", "KARNATAKA_BANK_LTD");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Invalid Mobile"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Karnataka Bank channel lead creation with invalid bank")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_038_bankChannelLeadCreationForKarnatakaBankWithInvalidBank() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "KARNATAKA BANK LTD1");
        body.put("solutionTypeLevel2", "KARNATAKA_BANK_LTD");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("line1", "test 1");
        body.put("line3", "");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "0");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Invalid bank provided"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Karnataka Bank channel lead creation with invalid solutionTypeLevel2")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_039_bankChannelLeadCreationForKarnatakaBankWithInvalidSolutionTypeLevel2() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "KARNATAKA BANK LTD");
        body.put("solutionTypeLevel2", "KARNATAKA_BANK_LTD1");
        body.put("line1", "test 1");
        body.put("line3", "teat 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Bank and SolutionTypeLevel2 mismatched"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Karnataka Bank channel lead creation with invalid deviceIdentifier")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_040_bankChannelLeadCreationForKarnatakaBankWithInvaliddeviceIdentifier() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelSubventionRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        mobileNumber = Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9" + mobileNumber);
        body.put("bank", "KARNATAKA BANK LTD");
        body.put("solutionTypeLevel2", "KARNATAKA_BANK_LTD");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "abc1");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");
        body.put("installationCharge", "200");
        body.put("lifeTimeFee", "0");
        body.put("rentalAmount", "200");
        body.put("usageDeposit", "1000");
        body.put("debitCardsLessThan2K", "1");
        body.put("debitCardsGreaterThan2K", "2");
        body.put("debitCardsRupay", "0");
        body.put("creditCard", "3");
        body.put("amex", "2.1");
        body.put("diners", "2.2");
        body.put("corporateCards", "2.3");
        body.put("emiCC", "1");
        body.put("emiDC", "2");
        body.put("approverEmail", "<EMAIL>");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400) {
            String actualMsg = bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Device details not present for model: abc1"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }
    }
}
