package OCL.BankChannel;

import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.v1.bankcp.BankChannelLeadCreation;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class BankChannelLeadCreationFlow extends BaseMethod
{
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(BankChannelLeadCreationFlow.class);

    public static String jwtToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FQmF0Y2giLCJpc3MiOiJPRSIsInRpbWVzdGFtcCI6IjIwMjMtMTAtMTNUMTg6NDc6MTkuMTUxKzA1OjMwIn0.5rCW7Q_pGR0Jo_2gogaEoVUNplsahaU5e-PPc1otsxI";
    public static String clientId = "OEBatch";
    public static String clientsecret = "OEBatch_SECRET";
    public static String leadId="";
    public static String AUBankLeadId="";

    @Test(priority = 1,groups = {"Regression"},description = "Au Bank channel lead creation")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_001_bankChannelLeadCreationForAUBank()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Integer mobileNumber=Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9"+ mobileNumber);
        body.put("bank", "AU SMALL FINANCE BANK LIMITED");
        body.put("solutionTypeLevel2", "AU_SMALL_FINANCE_BANK_LIMITED");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "linux_nexgog2plus");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if(bankObjResp.getStatusCode()==200)
        {
            AUBankLeadId=bankObjResp.jsonPath().get("leadId");
            LOGGER.info(" AU bank LeadId is  :" + AUBankLeadId);
            Assert.assertEquals(bankObjResp.jsonPath().get("displayMessage"),"Lead successfully created");
            Assert.assertEquals(bankObjResp.statusCode(), 200);
        }
        else
        {
            LOGGER.info("Lead is not created");
        }
    }

    @Test(priority = 1,groups = {"Regression"},description = "Jana Bank channel lead creation when lead is present")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_002_bankChannelLeadCreationWhenLeadIsPresent()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code when lead is present : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with invalid mobile number")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_003_bankChannelLeadCreationWithInvalidMobileNumber()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9999555");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code  : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }

    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with empty mobile number")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_004_bankChannelLeadCreationWithEmptyMobileNumber()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code: " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with invalid bank")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_005_bankChannelLeadCreationWithInvalidBank()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK1");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code  : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with empty bank")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_006_bankChannelLeadCreationWithEmptyBank()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code  : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with invalid solutionTypeLevel2")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_007_bankChannelLeadCreationWithInvalidSolutionTypeLevel2()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK1");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with empty solutionTypeLevel2")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_008_bankChannelLeadCreationWithSolutionTypeLevel2()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code  : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with invalid line1")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_009_bankChannelLeadCreationWithLine1()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "@test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation empty line1")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_010_bankChannelLeadCreationWithEmptyLine1()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with invalid line 3")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_011_bankChannelLeadCreationWithInvalidLine3()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "@test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with empty line3")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_012_bankChannelLeadCreationWithEmptyLine3()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code: " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }

    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with invalid requestIdentifier")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_013_bankChannelLeadCreationWithInvalidRequestIdentifier()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "@abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with empty requestIdentifier")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_014_bankChannelLeadCreationWithEmptyRequestIdentifier()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code  : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with invalid mid")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_015_bankChannelLeadCreationWithInvalidMid()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "abc");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with empty mid")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_016_bankChannelLeadCreationWithEmptyMid()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with invalid deviceIdentifier")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_017_bankChannelLeadCreationWithInvalidDeviceIdentifier()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A9101");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code  : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation empty deviceIdentifier")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_018_bankChannelLeadCreationWithEmptyDeviceIdentifier()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code  : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with invalid deviceCount")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_019_bankChannelLeadCreationWithInvalidDeviceCount()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "@");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code  : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with empty deviceCount")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_020_bankChannelLeadCreationWithEmptyDeviceCount()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with invalid rentalFrequency")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_021_bankChannelLeadCreationWithInvalidRentalFrequency()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime@");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with empty rentalFrequency")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_022_bankChannelLeadCreationWithEmptyRentalFrequency()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code  : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with invalid agent custid")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_023_bankChannelLeadCreationWithInvalidAgentCustId()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********1");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code when lead is present : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 500);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with empty agent custid")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_024_bankChannelLeadCreationWithEmptyAgentCustId()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code when lead is present : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }

    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with invalid channel ")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_025_bankChannelLeadCreationWithInvalidChannel()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL1");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code when lead is present : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with empty channel")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_026_bankChannelLeadCreationWithEmptyChannel()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "lifeTime");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        int StatusCode = bankObjResp.getStatusCode();
        LOGGER.info("Status code when lead is present : " + bankObjResp.statusCode());
        Assert.assertEquals(bankObjResp.statusCode(), 400);
    }

    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation for new merchant")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_027_bankChannelLeadCreationForNewMerchant()
    {
        BankChannelLeadCreation bankObj= new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

       Integer mobileNumber= Utilities.randomNumberGenerator(9);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "9"+mobileNumber );
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj,queryParams,headers,body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if(bankObjResp.getStatusCode()==200)
        {
            leadId=bankObjResp.jsonPath().get("leadId");
            LOGGER.info("LeadId is  :" + leadId);
            Assert.assertEquals(bankObjResp.jsonPath().get("displayMessage"),"Lead successfully created");
            Assert.assertEquals(bankObjResp.statusCode(), 200);
        }
       else
        {
            LOGGER.info("Lead is not created");
        }
    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation for existing merchant without reseller id")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_028_bankChannelLeadCreationForExistingMerchantWithoutResellerId() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400)
        {
           String actualMsg= bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Mid linked with mobile is not tagged with reseller"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }

    }

    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation for existing mid without reseller id")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_029_bankChannelLeadCreationForExistingMidWithoutResellerId() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "GwTQfa64289211413533");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if (bankObjResp.getStatusCode() == 400)
        {
            String actualMsg= bankObjResp.jsonPath().get("displayMessage");
            Assert.assertTrue(actualMsg.contains("Mid provided is not tagged with reseller"));
            Assert.assertEquals(bankObjResp.statusCode(), 400);
        }

    }
    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation for existing merchant with reseller id")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_030_bankChannelLeadCreationForExistingMerchantWithResellerId() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if(bankObjResp.getStatusCode()==200)
        {
            leadId=bankObjResp.jsonPath().get("leadId");
            LOGGER.info("LeadId is  :" + leadId);
            Assert.assertEquals(bankObjResp.jsonPath().get("displayMessage"),"Lead successfully created");
            Assert.assertEquals(bankObjResp.statusCode(), 200);
        }
        else
        {
            LOGGER.info("Lead is not created");
        }

    }

    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation for existing mid with reseller id")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_031_bankChannelLeadCreationForExistingMidWithResellerId() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "uTvuRY68885032516020");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if(bankObjResp.getStatusCode()==200)
        {
            leadId=bankObjResp.jsonPath().get("leadId");
            LOGGER.info(" Jana bank LeadId is  :" + leadId);
            Assert.assertEquals(bankObjResp.jsonPath().get("displayMessage"),"Lead successfully created");
            Assert.assertEquals(bankObjResp.statusCode(), 200);
        }
        else
        {
            LOGGER.info("Lead is not created");
        }
    }

    @Test(priority = 1,groups = {"Regression"},description = "bank channel lead creation with AU bank Mid")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_032_bankChannelLeadCreationWithAuBankMid() {
        BankChannelLeadCreation bankObj = new BankChannelLeadCreation(P.TESTDATA.get("BankChannelLeadCreationRequest"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", "OE_PANEL");
        queryParams.put("agentCustId", "**********");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mobile", "**********");
        body.put("bank", "JANA SMALL FINANCE BANK");
        body.put("solutionTypeLevel2", "JANA_SMALL_FINANCE_BANK");
        body.put("line1", "test 1");
        body.put("line3", "test 3");
        body.put("requestIdentifier", "abc");
        body.put("mid", "ZHqtKo80679529587794");
        body.put("deviceIdentifier", "android_A910");
        body.put("deviceCount", "1");
        body.put("rentalFrequency", "monthly");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", jwtToken);
        headers.put("Content-Type", "application/json");

        Response bankObjResp = middlewareServicesObject.bankChannelLeadCreation(bankObj, queryParams, headers, body);
        LOGGER.info("Status code is : " + bankObjResp.statusCode());
        if(bankObjResp.getStatusCode()==200)
        {
            leadId=bankObjResp.jsonPath().get("leadId");
            LOGGER.info("LeadId is  :" + leadId);
            Assert.assertEquals(bankObjResp.jsonPath().get("displayMessage"),"Lead successfully created");
            Assert.assertEquals(bankObjResp.statusCode(), 200);
        }
        else
        {
            LOGGER.info("Lead is not created");
        }
    }

    @Test(priority = 1, description = "fetch lead status", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_O33_fetchLeadStatus()
    {

        if (AUBankLeadId != null)
        {
            FetchLead v1FetchLeadObj = new FetchLead(AUBankLeadId);
            XMWCookie = findXMWTokenforPanel("**********", "paytm@123");
            Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
            String leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
            System.out.println("Lead stage is " + leadStage);
        }
        else
        {
            LOGGER.info("Lead is not created yet");
        }
    }

}


