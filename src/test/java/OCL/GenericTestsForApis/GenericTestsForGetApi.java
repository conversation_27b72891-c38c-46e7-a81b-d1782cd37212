package OCL.GenericTestsForApis;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class GenericTestsForGetApi extends BaseMethod {

    public static final Logger LOGGER = LogManager.getLogger(GenericTestsForGetApi.class);

    private Map<String,String> HearderDiy = new HashMap<>();
    private static String ApplicantSessionToken = "";
    public static String AgentSessionToken = "";

    @BeforeClass
    @Owner(emailId = "<EMAIL>")
    public void GetSessionToken()
    {
        ApplicantSessionToken = ApplicantToken("6667770000","paytm@123");
        //ApplicantSessionToken = "1918d65a-2aaa-4a51-9c78-348bf4902100";
        LOGGER.info("DIY Applicant Token : " +ApplicantSessionToken);

        AgentSessionToken =CommonAgentToken;
    }

    private void CommonHeaderParamDiy()
    {

        HearderDiy.put("session_token",ApplicantSessionToken);
        HearderDiy.put("androidId","AashitAndroid");
        HearderDiy.put("browserName","chrome");
        HearderDiy.put("browserVersion","4.6.3");
        HearderDiy.put("ipAddress","************");
        HearderDiy.put("longitude","28.32");
        HearderDiy.put("latitude","77.213");
        HearderDiy.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

    }
    @Test(description = "Generic Fetch Test cases for Fetch Bank in Bank Details Update")
    @Owner(emailId = "<EMAIL>")
    public void TC_0001_FetchBankProfileUpdate()
    {
        String URL = "/v1/profile/update/bank";

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("entityType","INDIVIDUAL");
        QueryParam.put("solution","pg_profile_update");

         CommonHeaderParamDiy();
        GetApiTest(URL,QueryParam,HearderDiy);
    }

    @Test(description = "Generic Fetch Test cases for Fetch Lead Details for sdMerchant Controller")
    @Owner(emailId = "<EMAIL>")
    public void TC_0002_FetchLeadDetailsV1SdMerchant()
    {
        String URL = "/v1/sdMerchant/lead";

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("entityType","INDIVIDUAL");
        QueryParam.put("solution","unified_payment_merchant");
        QueryParam.put("channel","DIY_PAYTM_APP");

        CommonHeaderParamDiy();
        GetApiTest(URL,QueryParam,HearderDiy);
    }

    @Test(description = "Generic Fetch Test cases for Fetch Category for ump controller")
    @Owner(emailId = "<EMAIL>")
    public void TC_0003_FetchCategoryV1Ump()
    {
        String URL = "/v1/umpMerchant/category";

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("entityType","INDIVIDUAL");
        QueryParam.put("solution","offline_50k");

        CommonHeaderParamDiy();
        GetApiTest(URL,QueryParam,HearderDiy);
    }

    @Test(description = "Generic Fetch Test cases for Fetch Sub-Category for ump controller")
    @Owner(emailId = "<EMAIL>")
    public void TC_0004_FetchSubCategoryV1Ump()
    {
        String URL = "/v1/umpMerchant/category/1/subcategory";

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("entityType","INDIVIDUAL");
        QueryParam.put("solution","offline_50k");

        CommonHeaderParamDiy();
        GetApiTest(URL,QueryParam,HearderDiy);
    }

    @Test(description = "Generic Fetch Test cases for Fetch Bank List for sdMerchant controller")
    @Owner(emailId = "<EMAIL>")
    public void TC_0005_FetchBankListV1SdMerchant()
    {
        String URL = "/v1/sdMerchant/banks";

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("entityType","INDIVIDUAL");
        QueryParam.put("solution","offline_50k");

        CommonHeaderParamDiy();
        GetApiTest(URL,QueryParam,HearderDiy);
    }

    @Test(description = "Generic Fetch Test cases for Fetch Lead Status for Upgrade MID controller")
    @Owner(emailId = "<EMAIL>")
    public void TC_0006_FetchLeadStatusV1UpgradeMid()
    {
        String URL = "/v1/upgradeMid/lead/status";

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("entityType","PROPRIETORSHIP");
        QueryParam.put("solution","upgrade_mid");
        QueryParam.put("solutionLeadId","cee665e5-d5ae-43b3-a150-b32e57c41191");
        QueryParam.put("channel","UMP_WEB");

        CommonHeaderParamDiy();
        GetApiTest(URL,QueryParam,HearderDiy);
    }

    @Test(description = "Generic Fetch Test cases for Fetch Business for Upgrade MID controller")
    @Owner(emailId = "<EMAIL>")
    public void TC_0007_FetchBusinessV1UpgradeMid()
    {
        String URL = "/v1/upgradeMid/business";

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("channel","UMP_WEB");

        CommonHeaderParamDiy();
        GetApiTest(URL,QueryParam,HearderDiy);
    }

    @Test(description = "Generic Fetch Test cases for Fetch TnC for Upgrade MID controller")
    @Owner(emailId = "<EMAIL>")
    public void TC_0008_FetchTnCv1UpgradeMid()
    {
        String URL = "/v1/upgradeMid/tnc";

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("channel","UMP_WEB");
        QueryParam.put("entityType","PROPRIETORSHIP");
        QueryParam.put("solution","upgrade_mid");

        CommonHeaderParamDiy();
        GetApiTest(URL,QueryParam,HearderDiy);
    }

    @Test(description = "Generic Fetch Test cases for Fetch Lead Details for Upgrade MID controller")
    @Owner(emailId = "<EMAIL>")
    public void TC_0009_FetchLeadv1UpgradeMid()
    {
        String URL = "/v1/upgradeMid/lead";

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("channel","UMP_WEB");
        QueryParam.put("entityType","PROPRIETORSHIP");
        QueryParam.put("solution","upgrade_mid");
        QueryParam.put("solutionLeadId","dd5f5d47-9467-41da-8a40-89ecb2d2b788");

        CommonHeaderParamDiy();
        GetApiTest(URL,QueryParam,HearderDiy);
    }

    @Test(description = "Generic Fetch Test cases for Fetch Common Lead Status for  Instrument Enable/Disable")
    @Owner(emailId = "<EMAIL>")
    public void TC_0010_FetchCombineLeadStatusV1ProfileUpdate()
    {
        String URL = "/v1/profile/update/instrument/lead/status";

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("channel","UMP_WEB");
        QueryParam.put("entityType","INDIVIDUAL");
        QueryParam.put("solution","pg_profile_update");
        QueryParam.put("status","ALL");

        CommonHeaderParamDiy();
        GetApiTest(URL,QueryParam,HearderDiy);
    }
}
