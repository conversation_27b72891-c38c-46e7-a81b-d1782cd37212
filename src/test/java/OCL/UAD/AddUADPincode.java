
package OCL.UAD;

import Services.UAD.UADServices;
import com.goldengate.common.BaseMethod;
import com.google.gson.Gson;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AddUADPincode extends BaseMethod {

	
    private static final Logger LOGGER = LogManager.getLogger(AddUADPincode.class);
    public static String username= "5555508546";
    public static String password="paytm@123";
    public static String pin1 = "605501";
    public static String zonenew = "South";
    public static String statenew = "Puducherry";
    public static String tierType = "City";
    public static String tierTypeSecondary = "District";
    public static String tierTypeValue = "Pondicherry";
    public static String tierTypeValue1 = "North West Delhi";
    public static String tierTypeValue2 = "North Delhi";
    public static String tierTypeValue3 = "New Delhi";
    public static String pinnew = "110085";
    public static String zonenew1 = "North";
    public static String statenew1 = "Delhi";
    public static String PrimaryaddressResponseType= "PRIMARY";
    public static String SecondaryResponseType= "SECONDARY";
    public static String InvalidPINErrorMessage="Invalid pincode";
    public static String BlankPINErrorMessage="Pincode list cannot be empty";

    String sToken = AgentSessionToken("8010630022","paytm@123");

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLogin() throws Exception {
        sToken = AgentSessionToken("8010630022", "paytm@123");
        LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
        waitForLoad(3000);
    }
    
    @Test(priority = 1,description = "Fetching Multiple PIN with Primary details")
    public void AddingMultiplePINs()
    {

       String session_token = AgentSessionToken(username, password);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sToken);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String, String> body = new HashMap<String, String>();
        List<String> lt=new ArrayList<String>();
        lt.add(pin1); lt.add(pinnew);
//        String ls=new Gson().toJson(lt);
       String ls=lt.toString();
        body.put("pincodes", ls);
        body.put("addressResponseType", PrimaryaddressResponseType);
        
        UADServices uad =new UADServices();
        
        Response categoryRes = uad.getPincode(headers, body);
        int httpcode = categoryRes.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        
        
//        Assert.assertEquals(categoryRes.jsonPath().get("responseMessage"),"Success");
        
        String pincode1 = "addressDictionary."+pin1+".pincode[0]";
        String zone1 = "addressDictionary."+pin1+".zone[0]";
        String state1 = "addressDictionary."+pin1+".state[0]";
        String tier3Type = "addressDictionary."+pin1+".tier3Type[0]";
        String pincode2 = "addressDictionary."+pinnew+".pincode[0]";
        String zone2 = "addressDictionary."+pinnew+".zone[0]";
        String state2 = "addressDictionary."+pinnew+".state[0]";
//
//        Assert.assertEquals(categoryRes.jsonPath().get(pincode1),pin1);
//       Assert.assertEquals(categoryRes.jsonPath().get(zone1),zonenew);
//       Assert.assertEquals(categoryRes.jsonPath().get(state1),statenew);

       
//
//
//       Assert.assertEquals(categoryRes.jsonPath().get(tier3Type),tierType);
//       Assert.assertEquals(categoryRes.jsonPath().get(pincode2),pinnew);
//       Assert.assertEquals(categoryRes.jsonPath().get(zone2),zonenew1);
//       Assert.assertEquals(categoryRes.jsonPath().get(state2),statenew1);



    }

    @Test(priority = 2,description = "Fetching Single PIN with Primary details")
    public void AddingSinglePINs()
    {

       String session_token = AgentSessionToken(username, password);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sToken);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String, String> body = new HashMap<String, String>();
        List<String> lt=new ArrayList<String>();
        lt.add(pinnew);
        String ls=new Gson().toJson(lt);
//       String ls=lt.toString();
        body.put("pincodes", ls);
        body.put("addressResponseType", PrimaryaddressResponseType);
        
        UADServices uad =new UADServices();
        
        Response categoryRes = uad.getPincode(headers, body);
        int httpcode = categoryRes.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
//
//
//        Assert.assertEquals(categoryRes.jsonPath().get("responseMessage"),"Success");
//
        String pincode1 = "addressDictionary."+pinnew+".pincode[0]";
        String zone1 = "addressDictionary."+pinnew+".zone[0]";
        String state1 = "addressDictionary."+pinnew+".state[0]";
        String tier3Type = "addressDictionary."+pinnew+".tier3Type[0]";
//
//
//        Assert.assertEquals(categoryRes.jsonPath().get(pincode1),pinnew);
//       Assert.assertEquals(categoryRes.jsonPath().get(zone1),zonenew1);
//       Assert.assertEquals(categoryRes.jsonPath().get(state1),statenew1);



    }
    
    @Test(priority = 3,description = "Fetching Multiple PIN with Secondary details")
    public void AddingMultiplePINSecondary()
    {

       String session_token = AgentSessionToken(username, password);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sToken);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String, String> body = new HashMap<String, String>();
        List<String> lt=new ArrayList<String>();
        lt.add(pin1); lt.add(pinnew);
//        String ls=new Gson().toJson(lt);
       String ls=lt.toString();
        body.put("pincodes", ls);
        body.put("addressResponseType", SecondaryResponseType);
        
        UADServices uad =new UADServices();
        
        Response categoryRes = uad.getPincode(headers, body);
        int httpcode = categoryRes.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");

        

//        Assert.assertEquals(categoryRes.jsonPath().get("responseMessage"),"Success");
        
        String pincode1 = "addressDictionary."+pin1+".pincode[0]";
        String zone1 = "addressDictionary."+pin1+".zone[0]";
        String state1 = "addressDictionary."+pin1+".state[0]";
        String tier3Type = "addressDictionary."+pin1+".tier3Type[0]";
        String tier3TypeVal = "addressDictionary."+pin1+".tier3Value[0]";
        String pincode2 = "addressDictionary."+pinnew+".pincode[0]";
        String zone2 = "addressDictionary."+pinnew+".zone[0]";
        String state2 = "addressDictionary."+pinnew+".state[0]";
        String tier3TypeValnew = "addressDictionary."+pinnew+".tier3Value[0]";
        String tier3TypeValnew1 = "addressDictionary."+pinnew+".tier3Value[1]";
        String tier3TypeValnew2 = "addressDictionary."+pinnew+".tier3Value[2]";

//
//        Assert.assertEquals(categoryRes.jsonPath().get(pincode1),pin1);
//       Assert.assertEquals(categoryRes.jsonPath().get(zone1),zonenew);
//       Assert.assertEquals(categoryRes.jsonPath().get(state1),statenew);

//
//
//
//       Assert.assertEquals(categoryRes.jsonPath().get(tier3Type),tierTypeSecondary);
//       Assert.assertEquals(categoryRes.jsonPath().get(tier3TypeVal),tierTypeValue);
//       Assert.assertEquals(categoryRes.jsonPath().get(pincode2),pinnew);
//       Assert.assertEquals(categoryRes.jsonPath().get(zone2),zonenew1);
//       Assert.assertEquals(categoryRes.jsonPath().get(state2),statenew1);
//       Assert.assertEquals(categoryRes.jsonPath().get(tier3TypeValnew),tierTypeValue1);
//       Assert.assertEquals(categoryRes.jsonPath().get(tier3TypeValnew1),tierTypeValue2);
//       Assert.assertEquals(categoryRes.jsonPath().get(tier3TypeValnew2),tierTypeValue3);

    }
    
    @Test(priority = 4,description = "Fetching Single PIN with Secondary details")
    public void AddingSinglePINSecondary()
    {

       String session_token = AgentSessionToken(username, password);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sToken);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String, String> body = new HashMap<String, String>();
        List<String> lt=new ArrayList<String>();
        lt.add(pinnew);
        String ls=new Gson().toJson(lt);
//       String ls=lt.toString();
        body.put("pincodes", ls);
        body.put("addressResponseType", SecondaryResponseType);
        
        UADServices uad =new UADServices();
        
        Response categoryRes = uad.getPincode(headers, body);
        int httpcode = categoryRes.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//        Assert.assertEquals(categoryRes.jsonPath().get("responseMessage"),"Success");
        
        String pincode2 = "addressDictionary."+pinnew+".pincode[0]";
        String zone2 = "addressDictionary."+pinnew+".zone[0]";
        String state2 = "addressDictionary."+pinnew+".state[0]";
        String tier3TypeValnew = "addressDictionary."+pinnew+".tier3Value[0]";
        String tier3TypeValnew1 = "addressDictionary."+pinnew+".tier3Value[1]";
        String tier3TypeValnew2 = "addressDictionary."+pinnew+".tier3Value[2]";
//
//       Assert.assertEquals(categoryRes.jsonPath().get(pincode2),pinnew);
//       Assert.assertEquals(categoryRes.jsonPath().get(zone2),zonenew1);
//       Assert.assertEquals(categoryRes.jsonPath().get(state2),statenew1);
//       Assert.assertEquals(categoryRes.jsonPath().get(tier3TypeValnew),tierTypeValue1);
//       Assert.assertEquals(categoryRes.jsonPath().get(tier3TypeValnew1),tierTypeValue2);
//       Assert.assertEquals(categoryRes.jsonPath().get(tier3TypeValnew2),tierTypeValue3);

    }

    @Test(priority = 5,description = "Invalid pincode")
    public void InvalidPin()
    {

       String session_token = AgentSessionToken(username, password);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sToken);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String, String> body = new HashMap<String, String>();
        List<String> lt=new ArrayList<String>();
       
        lt.add("");
        String ls=new Gson().toJson(lt);
        body.put("pincodes", ls);
        body.put("addressResponseType", PrimaryaddressResponseType);
        
        UADServices uad =new UADServices();
        
        Response categoryRes = uad.getPincode(headers, body);
        int httpcode = categoryRes.getStatusCode();
//        Assert.assertTrue(httpcode == 400, "Testcase Failed");
//
//
//        Assert.assertEquals(categoryRes.jsonPath().get("responseCode"),"400");
//
//       Assert.assertEquals(categoryRes.jsonPath().get("responseMessage"),InvalidPINErrorMessage);
//
    }
    
    @Test(priority = 6,description = "Blank pincode")
    public void BlankPin()
    {

       String session_token = AgentSessionToken(username, password);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sToken);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String, String> body = new HashMap<String, String>();
        List<String> lt=new ArrayList<String>();
       
        lt.add("");
        lt.clear();
        String ls=new Gson().toJson(lt);
        body.put("pincodes", ls);
        body.put("addressResponseType", PrimaryaddressResponseType);
        
        UADServices uad =new UADServices();
        
        Response categoryRes = uad.getPincode(headers, body);
        int httpcode = categoryRes.getStatusCode();
//        Assert.assertTrue(httpcode == 400, "Testcase Failed");
//
//
//        Assert.assertEquals(categoryRes.jsonPath().get("responseCode"),"400");
//
//       Assert.assertEquals(categoryRes.jsonPath().get("responseMessage"),BlankPINErrorMessage);
//
    }
    

	
}
