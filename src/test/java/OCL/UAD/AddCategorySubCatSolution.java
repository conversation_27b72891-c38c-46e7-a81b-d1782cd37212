package OCL.UAD;


import Request.UAD.AddCatSubcatSol;
import Request.UAD.Category;
import Request.UAD.SubCategory;
import Services.UAD.UADServices;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AddCategorySubCatSolution extends BaseMethod
{
    private static final Logger LOGGER = LogManager.getLogger(AddCategorySubCatSolution.class);

    Faker GenerateFake = new Faker();

    public static String SessionToken  = "b4f4d556-9751-48a1-809b-a4e28d787700";
    public  String Solution = GenerateFake.space().planet();
    public  String Entity = GenerateFake.space().galaxy();
    public  String Category = GenerateFake.color().name();
    public  String SubCategory1 = GenerateFake.space().moon();
    public  String SubCategory2 = GenerateFake.space().star();
    public static String MapSuccess = "Mapping Added Successfully";
    public static String MapExistMessage = "Mapping already Exists";
    Map CatFetch = new HashMap<>();
    Map<String,Object> SubCatFetch = new HashMap<>();


    UADServices uadServices = new UADServices();

    @Test(priority = 0,description = "Adding new category, sub-category, entity, solution",enabled = false)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void AddingNewCatSubCatSolEntity()
    {
        AddCatSubcatSol v1AddNew = new AddCatSubcatSol();
        Request.UAD.Category getCat = new Category();
        SubCategory getSubCat = new SubCategory();

        LOGGER.info("This is new Solution : " + Solution);
        LOGGER.info("This is new Entity : " + Entity);
        LOGGER.info("This is new Category : " + Category);
        LOGGER.info("This is new SubCategory one : " + SubCategory1);
        LOGGER.info("This is new SubCategory Two : " + SubCategory2);

        Map<String, String> body = new HashMap<String, String>();

        body.put("solutionName", Solution);
        body.put("entityType", Entity);
        body.put("categoryName", Category);
        body.put("name1", SubCategory1);
        body.put("name2", SubCategory2);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", SessionToken);

        Response V1AddResp = uadServices.AddCatSubcatSol(v1AddNew,headers,body);

        String ExpectedMsg = V1AddResp.jsonPath().getJsonObject("message");
        Assert.assertTrue(ExpectedMsg.contains(MapSuccess));

        Response ResObjectCat = uadServices.getCategory(getCat,Entity, Solution, SessionToken, "3.6.5");

        CatFetch = ResObjectCat.jsonPath().getJsonObject("catSubList[0]");
        LOGGER.info("This is Fetched Category : " + CatFetch);

        Assert.assertTrue(CatFetch.containsValue(Category));

        int GetCatID = ResObjectCat.jsonPath().getJsonObject("catSubList[0].id");
        LOGGER.info("Fetched Category ID is  : " + GetCatID);

        Response ResObjectSub = uadServices.getSubCategory(getSubCat,Entity, Solution, SessionToken, "3.6.5", GetCatID);

        //Capturing Array in list//
        List sublist = ResObjectSub.jsonPath().getList("catSubList");

        LOGGER.info("Fetched response " + sublist);

        SubCatFetch = (Map<String, Object>) ResObjectSub.jsonPath().getJsonObject("catSubList[0]");

        Assert.assertTrue(sublist.contains(SubCategory1) || sublist.contains(SubCategory2));


    }

    @Test(priority = 1,description = "Adding new sub-category in existing Category" ,enabled = false)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void AddingNewSubCatInExistingCat()
    {
      SubCategory1 = GenerateFake.space().moon();
      SubCategory2 = GenerateFake.space().star();

        AddCatSubcatSol v1AddNew = new AddCatSubcatSol();
        Request.UAD.Category getCat = new Category();
        SubCategory getSubCat = new SubCategory();

        LOGGER.info("This is new Solution : " + Solution);
        LOGGER.info("This is new Entity : " + Entity);
        LOGGER.info("This is new Category : " + Category);
        LOGGER.info("This is new SubCategory one : " + SubCategory1);
        LOGGER.info("This is new SubCategory Two : " + SubCategory2);

        Map<String, String> body = new HashMap<String, String>();

        body.put("solutionName", Solution);
        body.put("entityType", Entity);
        body.put("categoryName", Category);
        body.put("name1", SubCategory1);
        body.put("name2", SubCategory2);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", SessionToken);

        Response V1AddResp = uadServices.AddCatSubcatSol(v1AddNew,headers,body);

        String ExpectedMsg = V1AddResp.jsonPath().getJsonObject("message");
        Assert.assertTrue(ExpectedMsg.contains(MapSuccess));

        Response ResObjectCat = uadServices.getCategory(getCat,Entity, Solution, SessionToken, "3.6.5");

        CatFetch = (Map<String, Object>) ResObjectCat.jsonPath().getJsonObject("catSubList[0]");
        LOGGER.info("This is Fetched Category : " + CatFetch);

        Assert.assertTrue(CatFetch.containsValue(Category));

        int GetCatID = ResObjectCat.jsonPath().getJsonObject("catSubList[0].id");
        LOGGER.info("Fetched Category ID is  : " + GetCatID);
        Response ResObjectSub = uadServices.getSubCategory(getSubCat,Entity, Solution, SessionToken, "3.6.5", GetCatID);


        SubCatFetch = (Map<String, Object>) ResObjectSub.jsonPath().getJsonObject("catSubList[]");

        Assert.assertTrue(SubCatFetch.containsValue(SubCategory1) || SubCatFetch.containsValue(SubCategory2));

    }

    @Test(priority = 1,description = "Adding same sub-category in existing Category",enabled = false )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void AddingSameSubCatInExistingCat()
    {
        AddCatSubcatSol v1AddNew = new AddCatSubcatSol();

        Map<String, String> body = new HashMap<String, String>();

        body.put("solutionName", Solution);
        body.put("entityType", Entity);
        body.put("categoryName", Category);
        body.put("name1", SubCategory1);
        body.put("name2", SubCategory2);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", SessionToken);

        Response V1AddResp = uadServices.AddCatSubcatSol(v1AddNew,headers,body);

        String Messsage = V1AddResp.jsonPath().getJsonObject("message").toString();
        LOGGER.info("This is Response message for the request : " + Messsage);

        Assert.assertTrue(Messsage.contains(MapExistMessage));

    }

}
