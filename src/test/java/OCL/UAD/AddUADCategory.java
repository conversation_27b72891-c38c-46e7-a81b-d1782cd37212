package OCL.UAD;

import Services.UAD.UADServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class AddUADCategory extends BaseMethod {

	
    private static final Logger LOGGER = LogManager.getLogger(AddUADCategory.class);
    public static String username= "9654279917";
    public static String password="paytm@123";
    public static String Solution = "upgrade_mid";
    public static String entity= "Proprietorship";

    String sToken = AgentSessionToken("8010630022","paytm@123");

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLogin() throws Exception {
        sToken = AgentSessionToken("8010630022", "paytm@123");
        LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
        waitForLoad(3000);
    }
    
    @Test(priority = 1,description = "Adding new category")
    public void AddingNewCategory()
    {

       String session_token = AgentSessionToken(username, password);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sToken);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("solutionName", Solution);
        body.put("entityType", entity);
        
        UADServices uad =new UADServices();
        
        Response categoryRes = uad.getCategory(headers, body);
        int httpcode = categoryRes.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");


    }

    
	
}
