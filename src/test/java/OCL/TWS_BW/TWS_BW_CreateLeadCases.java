package OCL.TWS_BW;

import OCL.Subscription.FetchPlanSubscription;
import Services.CreateLeadP4b.GetLeadP4bTWStoBW;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

import static com.goldengate.common.BaseMethod.ApplicantToken;

public class TWS_BW_CreateLeadCases {


    private static final Logger LOGGER = LogManager.getLogger(FetchPlanSubscription.class);
    public String Token ;



    public static String solution="pg_profile_update";
    public static String entityType="INDIVIDUAL";
    public static String channel = "UMP_WEB";
    public static String merchantMobileNumber = "8888444225";
    public static String solutionSubType = "TWS_BW_SETTLEMENT";
    public static String mid = "jrDqEi77793593331363";
    String settlementType = "BUSINESS_WALLET";
    String triggerValue = "3";

    GetLeadP4bTWStoBW getStrategy;



    @BeforeClass
    public void generateToke(){
        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
        LOGGER.info("Token for PG : " + Token);
        getStrategy = new GetLeadP4bTWStoBW();
    }





    @Test(priority = 1,description = "To check that create lead api for tws gives error when tokne is missing", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_001() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", " ");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);



    }

    @Test(priority = 1,description = "To check that create lead api for tws gives error when tokne is invalid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_002() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", "InvalidToken");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);



    }
    @Test(priority = 1,description = "To check that create lead api for tws gives error when tokne key is invalid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_003() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Invalid_session_token_key", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);



    }
    @Test(priority = 1,description = "To check that create lead api for tws gives error when valid token is passed with special chacter at statring", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_004() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", "@#@@@"+Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);



    }
    @Test(priority = 1,description = "To check that create lead api for tws gives error when valid tokne  is passed with special characters ar last", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_005() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token+ "2132313");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);



    }

    @Test(priority = 1,description = "To check that create lead api for tws gives error when tokne key is not passed but rest other headers are passedg", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_006() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        //headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);



    }

    @Test(priority = 1,description = "To check that create lead api for tws gives error when headers are not passed", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_007_TWStoBW_positiceflow() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("session_token", Token);
//        headers.put("version","7.3.0");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("accept", "*/*");
//        headers.put("Accept-Language", "en");
//        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);



    }

    @Test(priority = 1,description = "To check that create lead api for tws gives error when solutionType is missing", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_008() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
       // params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }

    @Test(priority = 1,description = "To check that create lead api for tws gives error when channel is missing", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_009() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
       // params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }

    @Test(priority = 1,description = "To check that create lead api for tws gives error when entuty type is missing", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_010() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
       // params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }

    @Test(priority = 1,description = "To check that create lead api for tws gives error when solution is missing", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_011_TWStoBW_positiceflow() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }

}
