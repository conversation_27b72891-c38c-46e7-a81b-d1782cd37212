package OCL.TWS_BW;

import OCL.Subscription.FetchPlanSubscription;
import Services.GetSettlementStrategy.GetSettlementStrategy;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

import static com.goldengate.common.BaseMethod.ApplicantToken;

public class GetSettlementStrategie {



    private static final Logger LOGGER = LogManager.getLogger(FetchPlanSubscription.class);
    public static String clientid= "oe-subscription-client";

    public String Token ;



    public static String isRefundDetailsRequired="true";
    public static String isOtherChargesRequired="true";
    public static String deviceLoginNumebr = "**********";
    public static String merchantMobileNumber = "**********";
    public static String leadId= "********-b4e6-45ab-9c56-bef41d5df689";
    public static String custId = "**********";
    public static String mid = "AktwiS63783770034316";

    String strategy="Transaction Wise Settlement";
    String displayLabel = "Instant Settlement";
    String displaySubLabel = "Payments settled instantly to bank account";
    String isDefault = "true";
    String isCurrent = "false";

    @BeforeClass
    public void generateToke(){
        Token = ApplicantToken(deviceLoginNumebr, "paytm@123");
        LOGGER.info("Token for PG : " + Token);
    }

    @Test(priority = 1,description = "To check that with valid mandatory params we are getting 200 response code", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_001_GetsettlementStrategyWithMandatoryParams() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("deviceidentifier", "Xiaomi-M2101K6I-78517053699d822b");
        headers.put("version","4.9.1");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);
        params.put("merchantMobileNumber", merchantMobileNumber);
        params.put("merchantCustId", custId);

        Map<String, String> body = new HashMap<String, String>();

        GetSettlementStrategy getStrategy = new GetSettlementStrategy();

        Response subfetchResponse= getStrategy.GetSettlementStrategyplan(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();
        Assert.assertEquals(httpcode,200);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].strategy"),strategy);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].displayLabel"),displayLabel);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].displaySubLabel"),displaySubLabel);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].isDefault").toString(),isDefault);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].isCurrent").toString(),isCurrent);

    }

    @Test(priority = 1,description = "To check that with all valid mandatory/non mandatroy  params we are getting 200 response code", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_002_GetsettlementStrategyWithAllParams() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("deviceidentifier", "Xiaomi-M2101K6I-78517053699d822b");
        headers.put("version","4.9.1");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);
        params.put("merchantMobileNumber", merchantMobileNumber);
        params.put("merchantCustId", custId);
        params.put("mid", mid);
        params.put("fetchCurrentStrategyOnly", "true");

        Map<String, String> body = new HashMap<String, String>();

        GetSettlementStrategy getStrategy = new GetSettlementStrategy();

        Response subfetchResponse= getStrategy.GetSettlementStrategyplan(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();
        Assert.assertEquals(httpcode,200);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].strategy"),strategy);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].displayLabel"),displayLabel);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].displaySubLabel"),displaySubLabel);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].isDefault").toString(),isDefault);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].isCurrent").toString(),isCurrent);

    }

    @Test(priority = 1,description = "To check that with error is given with missing mandatory param merchantMobileNumber", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_GetsettlementStrategyWithMissingMandatoryParams() throws JSONException {



        String Token = ApplicantToken(deviceLoginNumebr, "paytm@123");
        LOGGER.info("Token for PG : " + Token);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("deviceidentifier", "Xiaomi-M2101K6I-78517053699d822b");
        headers.put("version","4.9.1");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);
        params.put("merchantCustId", custId);

        Map<String, String> body = new HashMap<String, String>();

        GetSettlementStrategy getStrategy = new GetSettlementStrategy();

        Response subfetchResponse= getStrategy.GetSettlementStrategyplan(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();
        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To check that with error is given with missing mandatory param leadId", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_GetsettlementStrategyWithMissingMandatoryParams() throws JSONException {



        String Token = ApplicantToken(deviceLoginNumebr, "paytm@123");
        LOGGER.info("Token for PG : " + Token);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("deviceidentifier", "Xiaomi-M2101K6I-78517053699d822b");
        headers.put("version","4.9.1");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("merchantMobileNumber", merchantMobileNumber);
        params.put("merchantCustId", custId);

        Map<String, String> body = new HashMap<String, String>();

        GetSettlementStrategy getStrategy = new GetSettlementStrategy();

        Response subfetchResponse= getStrategy.GetSettlementStrategyplan(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();
        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To check that without valid token we must get error code unauthorized", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_005_GetsettlementStrategyWithoutToken() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "Xiaomi-M2101K6I-78517053699d822b");
        headers.put("version","4.9.1");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);
        params.put("merchantMobileNumber", merchantMobileNumber);
        params.put("merchantCustId", custId);

        Map<String, String> body = new HashMap<String, String>();

        GetSettlementStrategy getStrategy = new GetSettlementStrategy();

        Response subfetchResponse= getStrategy.GetSettlementStrategyplan(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();
        Assert.assertEquals(httpcode,401);
     

    }

    @Test(priority = 1,description = "To check that without single non mandatory param api is working fine ->mid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_006_GetsettlementStrategyWithoutOneNonMandatParams() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("deviceidentifier", "Xiaomi-M2101K6I-78517053699d822b");
        headers.put("version","4.9.1");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);
        params.put("merchantMobileNumber", merchantMobileNumber);
        params.put("merchantCustId", custId);
        params.put("fetchCurrentStrategyOnly", "true");

        Map<String, String> body = new HashMap<String, String>();

        GetSettlementStrategy getStrategy = new GetSettlementStrategy();

        Response subfetchResponse= getStrategy.GetSettlementStrategyplan(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();
        Assert.assertEquals(httpcode,200);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].strategy"),strategy);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].displayLabel"),displayLabel);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].displaySubLabel"),displaySubLabel);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].isDefault").toString(),isDefault);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].isCurrent").toString(),isCurrent);

    }

    @Test(priority = 1,description = "To check that without single non mandatory param api is working fine ->merchantCustID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_007_GetsettlementStrategyWithoutOneNonMandatParams() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("deviceidentifier", "Xiaomi-M2101K6I-78517053699d822b");
        headers.put("version","4.9.1");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);
        params.put("merchantMobileNumber", merchantMobileNumber);
        params.put("mid", mid);
        params.put("fetchCurrentStrategyOnly", "true");

        Map<String, String> body = new HashMap<String, String>();

        GetSettlementStrategy getStrategy = new GetSettlementStrategy();

        Response subfetchResponse= getStrategy.GetSettlementStrategyplan(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();
        Assert.assertEquals(httpcode,200);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].strategy"),strategy);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].displayLabel"),displayLabel);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].displaySubLabel"),displaySubLabel);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].isDefault").toString(),isDefault);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].isCurrent").toString(),isCurrent);

    }



    @Test(priority = 1,description = "To check that without single non mandatory param api is working fine ->fetchCurrentStrategyOnly", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_008_GetsettlementStrategyWithoutOneNonMandatParams() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("deviceidentifier", "Xiaomi-M2101K6I-78517053699d822b");
        headers.put("version","4.9.1");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);
        params.put("merchantMobileNumber", merchantMobileNumber);
        params.put("merchantCustId", custId);
        params.put("mid", mid);


        Map<String, String> body = new HashMap<String, String>();

        GetSettlementStrategy getStrategy = new GetSettlementStrategy();

        Response subfetchResponse= getStrategy.GetSettlementStrategyplan(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();
        Assert.assertEquals(httpcode,200);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].strategy"),strategy);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].displayLabel"),displayLabel);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].displaySubLabel"),displaySubLabel);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].isDefault").toString(),isDefault);
        Assert.assertEquals(subfetchResponse.jsonPath().get("strategiesList[0].isCurrent").toString(),isCurrent);

    }


    @Test(priority = 1,description = "To check that when on passing empty value in mandatory params(merchantMobileNumber) then we must get error in response", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_0019_GetsettlementStrategyWithEmptyMandatoryParams() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("deviceidentifier", "Xiaomi-M2101K6I-78517053699d822b");
        headers.put("version","4.9.1");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);
        params.put("merchantMobileNumber", " ");
        params.put("merchantCustId", custId);

        Map<String, String> body = new HashMap<String, String>();

        GetSettlementStrategy getStrategy = new GetSettlementStrategy();

        Response subfetchResponse= getStrategy.GetSettlementStrategyplan(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();
        //validating that on empty params passed in manadory params we are getting 400 from resp
        Assert.assertEquals(httpcode,400);
        Assert.assertFalse(subfetchResponse.jsonPath().get("strategiesList[0].strategy"),strategy);
        Assert.assertFalse(subfetchResponse.jsonPath().get("strategiesList[0].displayLabel"),displayLabel);
        Assert.assertFalse(subfetchResponse.jsonPath().get("strategiesList[0].displaySubLabel"),displaySubLabel);


    }


    @Test(priority = 1,description = "To check that when on passing empty value in mandatory params(merchantMobileNumber) then we must get error in response", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_0110_GetsettlementStrategyWithEmptyMandatoryParams() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("deviceidentifier", "Xiaomi-M2101K6I-78517053699d822b");
        headers.put("version","4.9.1");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", " ");
        params.put("merchantMobileNumber",merchantMobileNumber);
        params.put("merchantCustId", custId);

        Map<String, String> body = new HashMap<String, String>();

        GetSettlementStrategy getStrategy = new GetSettlementStrategy();

        Response subfetchResponse= getStrategy.GetSettlementStrategyplan(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();
        //validating that on empty params passed in manadory params we are getting 400 from resp
        Assert.assertEquals(httpcode,400);
        Assert.assertFalse(subfetchResponse.jsonPath().get("strategiesList[0].strategy"),strategy);
        Assert.assertFalse(subfetchResponse.jsonPath().get("strategiesList[0].displayLabel"),displayLabel);
        Assert.assertFalse(subfetchResponse.jsonPath().get("strategiesList[0].displaySubLabel"),displaySubLabel);


    }


    @Test(priority = 1,description = "To check that when on passing empty value in mandatory params(merchantMobileNumber) then we must get error in response", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_0009_GetsettlementStrategyWithEmptyMandatoryParams() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("deviceidentifier", "Xiaomi-M2101K6I-78517053699d822b");
        headers.put("version","4.9.1");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);
        params.put("merchantMobileNumber", " ");
        params.put("merchantCustId", custId);

        Map<String, String> body = new HashMap<String, String>();

        GetSettlementStrategy getStrategy = new GetSettlementStrategy();

        Response subfetchResponse= getStrategy.GetSettlementStrategyplan(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();
        //validating that on empty params passed in manadory params we are getting 400 from resp
        Assert.assertEquals(httpcode,400);
        Assert.assertFalse(subfetchResponse.jsonPath().get("strategiesList[0].strategy"),strategy);
        Assert.assertFalse(subfetchResponse.jsonPath().get("strategiesList[0].displayLabel"),displayLabel);
        Assert.assertFalse(subfetchResponse.jsonPath().get("strategiesList[0].displaySubLabel"),displaySubLabel);


    }


    @Test(priority = 1,description = "To check that when on passing empty value in mandatory params(merchantMobileNumber) then we must get error in response", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_0010_GetsettlementStrategyWithEmptyMandatoryParams() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("deviceidentifier", "Xiaomi-M2101K6I-78517053699d822b");
        headers.put("version","4.9.1");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", " ");
        params.put("merchantMobileNumber",merchantMobileNumber);
        params.put("merchantCustId", custId);

        Map<String, String> body = new HashMap<String, String>();

        GetSettlementStrategy getStrategy = new GetSettlementStrategy();

        Response subfetchResponse= getStrategy.GetSettlementStrategyplan(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();
        //validating that on empty params passed in manadory params we are getting 400 from resp
        Assert.assertEquals(httpcode,400);
        Assert.assertFalse(subfetchResponse.jsonPath().get("strategiesList[0].strategy"),strategy);
        Assert.assertFalse(subfetchResponse.jsonPath().get("strategiesList[0].displayLabel"),displayLabel);
        Assert.assertFalse(subfetchResponse.jsonPath().get("strategiesList[0].displaySubLabel"),displaySubLabel);


    }



}
