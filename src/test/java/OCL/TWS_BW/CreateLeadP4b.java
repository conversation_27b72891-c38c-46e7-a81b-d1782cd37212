package OCL.TWS_BW;

import OCL.Subscription.FetchPlanSubscription;
import Services.CreateLeadP4b.GetLeadP4bTWStoBW;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

import static com.goldengate.common.BaseMethod.ApplicantToken;

public class CreateLeadP4b {



    private static final Logger LOGGER = LogManager.getLogger(FetchPlanSubscription.class);
    public String Token ;



    public static String solution="pg_profile_update";
    public static String entityType="INDIVIDUAL";
    public static String channel = "UMP_WEB";
    public static String merchantMobileNumber = "8888444225";
    public static String solutionSubType = "TWS_BW_SETTLEMENT";
    public static String mid = "jrDqEi77793593331363";
    String settlementType = "BUSINESS_WALLET";
    String triggerValue = "3";

    GetLeadP4bTWStoBW getStrategy;



    @BeforeClass
    public void generateToke(){
        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
        LOGGER.info("Token for PG : " + Token);
        getStrategy = new GetLeadP4bTWStoBW();
    }

    @Test(priority = 1,description = "To check that with valid mandatory params we are getting 200 response code", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_001_TWStoBW_positiceflow() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,200);



    }

    @Test(priority = 1,description = "To check that create lead must be giving error with invalid session_token ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_002_TWStoBW_positiceflow() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", "000000");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);



    }
    @Test(priority = 1,description = "To check that create lead must be giving error with empty session_token ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_003_TWStoBW_positiceflow_emptySessionToken() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", "");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);



    }

    @Test(priority = 1,description = "To check with empty version", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_004_TWStoBW_positiceflow_emptyVersion() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("version","");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }
    @Test(priority = 1,description = "To check with incorrect version", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_001_TWStoBW_positiceflow_incorrectVersion() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("version","20.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }

    @Test(priority = 1,description = "To check with empty solution type", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_001_TWStoBW_positiceflow_withEmptySolutionType() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", "");
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("settlementType",settlementType);
        body.put("triggerValue",triggerValue);



        Response subfetchResponse= getStrategy.GetCreateLeadP4bResponse(headers, body,params);
        subfetchResponse.prettyPrint();

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }



}
