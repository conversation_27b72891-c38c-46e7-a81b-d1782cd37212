package OCL.OnboardBankV3;

import Request.OnboardBankV3.OnboardBankV3;
import Services.Billing.BillingMiddlewareServices;
import Services.OnboardBankV3.OnboardBankV3MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

public class V3OnboardTerminal extends BaseMethod {

    OnboardBankV3MiddlewareServices onboardBankV3MiddlewareServices = new OnboardBankV3MiddlewareServices();

    private static final Logger LOGGER = Logger.getLogger(String.valueOf(V3OnboardTerminal.class));

    public static String MobileNumber = "**********";

    public static String Token = "";


    @Test(priority = 0, description = "Create Token for PG", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_MerchantLogin() throws SQLException {
        Token = ApplicantToken(MobileNumber, "paytm@123");
        LOGGER.info("Token for PG : " + Token);
    }

    @Test(priority = 0, description = "Onboard Terminal with correct request", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal with correct request " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);

    }

    @Test(priority = 0, description = "Onboard Terminal without serial number", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", "");
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without serial number " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Device serial number is mandatory field."));

    }

    @Test(priority = 0, description = "Onboard Terminal without MID", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without MID " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("MID is mandatory field, cannot be empty"));

    }


    @Test(priority = 0, description = "Onboard Terminal with invalid MID", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "agaah");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "agaah");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal with invalid MID" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("MID is invalid."));

    }


    @Test(priority = 0, description = "Onboard Terminal without Model", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without Model" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Model name is mandatory field, cannot be empty."));

    }


    @Test(priority = 0, description = "Onboard Terminal without address line 1", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without address line 1" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Address1 is invalid, either empty or length greater than 130 characters"));

    }


    @Test(priority = 0, description = "Onboard Terminal without state", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without state" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Address state is mandatory field, cannot be empty."));

    }

    @Test(priority = 0, description = "Onboard Terminal without city", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without city" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Address city is invalid, either empty or length greater than 30 characters."));

    }

    @Test(priority = 0, description = "Onboard Terminal without country", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without country" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Address country is mandatory field, cannot be empty"));

    }


    @Test(priority = 0, description = "Onboard Terminal with invalid country", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "ahahah");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal with invalid country" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Address country is invalid."));

    }




    @Test(priority = 0, description = "Onboard Terminal without latitude", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without latitude" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Latitude is mandatory field, cannot be empty."));

    }


    @Test(priority = 0, description = "Onboard Terminal with invalid latitude", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "anan");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal with invalid latitude" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Invalid latitude value"));

    }


    @Test(priority = 0, description = "Onboard Terminal without longitude", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without longitude" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Longitude is mandatory field, cannot be empty"));

    }




    @Test(priority = 0, description = "Onboard Terminal with invalid longitude", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "anan");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal with invalid longitude" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Invalid longitude value."));

    }


    @Test(priority = 0, description = "Onboard Terminal without vendor", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without vendor" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Vendor name is mandatory field, cannot be empty."));

    }


    @Test(priority = 0, description = "Onboard Terminal with invalid vendor", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_018_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "anna");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal with invalid vendor" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Vendor Name is not Valid."));

    }

    @Test(priority = 0, description = "Onboard Terminal without OS Type", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_019_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without OS Type" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_1906").contains("OS Type is not Valid."));

    }


    @Test(priority = 0, description = "Onboard Terminal with invalid OS Type", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_020_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "amma");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal with invalid OS Type" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_1906").contains("OS Type is not Valid."));

    }


    @Test(priority = 0, description = "Onboard Terminal without Source", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_021_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without Source" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

       // Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("OS Type is not Valid"));

    }



    @Test(priority = 0, description = "Onboard Terminal with invalid Instrument", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_022_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "nanan");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal with invalid Instrument" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Only UPI and SYSTEM instrument are supported!"));

    }


    @Test(priority = 0, description = "Onboard Terminal without  Instrument", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_023_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without  Instrument" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Only UPI and SYSTEM instrument are supported!"));

    }


    @Test(priority = 0, description = "Onboard Terminal without Token", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_024_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", "");

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without Token" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 403);

        //Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Only UPI and SYSTEM instrument are supported!"));

    }

    @Test(priority = 0, description = "Onboard Terminal with invalid Token", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_025_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", "amaaa");

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "agahah");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal with invalid Token" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        Assert.assertTrue(respObj.jsonPath().getString("BO_406").contains("Error from Granter"));

    }

    @Test(priority = 0, description = "Onboard Terminal without Landmark", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_026_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token",Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "");
        body.put("formattedAddress", "amjajam");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without Landmark" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);

        //Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Only UPI and SYSTEM instrument are supported!"));

    }

    @Test(priority = 0, description = "Onboard Terminal without formatted address", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_027_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token",Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "amjajam");
        body.put("formattedAddress", "");
        body.put("propertyNumber", "amjaj");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without formatted address" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);

        //Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Only UPI and SYSTEM instrument are supported!"));

    }

    @Test(priority = 0, description = "Onboard Terminal without property number", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_028_V3OnboardTerminal(){
        OnboardBankV3 OnboardBankV3=new OnboardBankV3(P.TESTDATA.get("OnboardBankV3Request"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token",Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "GFSRW" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("serialNo", MapSerialNo);
        body.put("mid", "Aggreg66852330295863");
        body.put("modelName", "NEXGOG2PLUS");
        body.put("monthlyRental", "0.0");
        body.put("address1", "Paramount");
        body.put("address2", "Sector-1 Kale Khan");
        body.put("address3", "Delhi");
        body.put("state", "Delhi");
        body.put("city", "North Delhi");
        body.put("country", "India");
        body.put("latitude", "13.11");
        body.put("longitude", "47.453");
        body.put("landmark", "amjajam");
        body.put("formattedAddress", "amjaj");
        body.put("propertyNumber", "");
        body.put("vendorName", "NEXGO");
        body.put("osType", "LINUX");
        body.put("source", "OE");
        body.put("requestId", "3b6fbe14-0f7e-4806-a30a-e68a24994939");
        body.put("sendNoSMS", "true");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = onboardBankV3MiddlewareServices.onboardBankV3MiddlewareServices(OnboardBankV3,body, headers);
        LOGGER.info("Onboard Terminal without property number" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);

        //Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("Only UPI and SYSTEM instrument are supported!"));

    }







}
