/*
 * package OCL.ConsumerLending;
 * 
 * import java.time.LocalDateTime; import java.time.ZoneId;
 * 
 * import org.apache.log4j.Logger; import org.testng.Assert; import
 * org.testng.annotations.BeforeSuite; import org.testng.annotations.Test;
 * 
 * import com.auth0.jwt.JWT; import com.auth0.jwt.algorithms.Algorithm; import
 * com.goldengate.common.BaseMethod; import
 * com.paytm.apitools.util.annotations.Owner;
 * 
 * import OCL.Lending.BusinessLending.business_Lending; import
 * Request.MerchantService.loan.lead.dynamicTnC_LOS; import
 * Request.MerchantService.loan.lead.saveTncAndSubmitApplication_LOS; import
 * Request.MerchantService.v1.sdMerchant.createLead_LOS; import
 * Request.MerchantService.v1.sdMerchant.fetchLead_LOS; import
 * Request.MerchantService.v1.sdMerchant.updateLeadAddress_LOS; import
 * Request.MerchantService.v1.sdMerchant.updateLead_LOS; import
 * Request.MerchantService.v2.lending.checkBreStatus_LOS; import
 * Request.MerchantService.v2.lending.getBreStatus_LOS; import
 * Request.MerchantService.v2.lending.getKycStatus_LOS; import
 * Request.MerchantService.v2.lending.dataUpdate.dataUpdate_LOS; import
 * Request.MerchantService.v5.callback.alternateNoCallBack_LOS; import
 * Request.MerchantService.v5.callback.lmsCallBack_LOS; import
 * Request.MerchantService.v5.callback.otpCallback_LOS; import
 * Services.MechantService.MiddlewareServices; import
 * Services.Utilities.Utilities; import io.restassured.response.Response;
 * 
 * public class consumerLendingPP_HappyLoans extends BaseMethod {
 * 
 * public static String AgentNo = "6111111149"; public static String
 * session_token = ""; public static String pan = ""; public static String mid =
 * ""; public static String leadId = ""; public static String state = ""; public
 * static String token = ""; public static String custId = "1000514767"; public
 * static String tncName = ""; public static String uniqueIdentifier = "";
 * public static String md5 = ""; public static Integer accept = 1;
 * 
 * private static final Logger LOGGER =
 * Logger.getLogger(business_Lending.class); MiddlewareServices
 * middlewareServicesObject = new MiddlewareServices(); Utilities utilities =
 * new Utilities();
 * 
 * @BeforeSuite
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true) public void
 * getAgentToken() { LOGGER.info(" Before Suite Method for Agent Login ");
 * session_token = ApplicantToken(AgentNo, "paytm@123");
 * LOGGER.info("Applicant Token for Lending : " + session_token);
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "jwt token creation") public String generateJwtToken() {
 * 
 * LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of("GMT+05:30")); //
 * LocalDate localDate = localDateTime.toLocalDate();
 * System.out.println("Date is :" + localDateTime); String ts =
 * localDateTime.toString();
 * 
 * Algorithm buildAlgorithm =
 * Algorithm.HMAC256("4f438a17-2436-4231-a172-c338ab9d40bb"); token =
 * JWT.create().withIssuer("OE").withClaim("clientId",
 * "LENDING").withClaim("custId", custId) .withClaim("timestamp", ts +
 * "+05:30").sign(buildAlgorithm); return token; }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Positive fetch lead Call Test Case", priority = 1)
 * public void fetchLeadPostPaid() { fetchLead_LOS fetchLeadObjectPP = new
 * fetchLead_LOS(); Response responseObject =
 * middlewareServicesObject.fetchLead_LOS(fetchLeadObjectPP, "INDIVIDUAL",
 * "HAPPY_LOANS", "postpaid_v2", "PAYTM_APP", session_token);
 * fetchLeadObjectPP.setRefId(responseObject.jsonPath().getString("refId"));
 * fetchLeadObjectPP.setStatusCode(responseObject.jsonPath().getInt("statusCode"
 * ));
 * fetchLeadObjectPP.setLeadId(responseObject.jsonPath().getString("leadId"));
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode); pan = responseObject.jsonPath().getString("business.pan");
 * LOGGER.info("pan in business object of fetch lead Response is " + pan);
 * fetchLeadObjectPP.setStatusCode(responseObject.jsonPath().getInt("statusCode"
 * )); fetchLeadObjectPP.getStatusCode(); int statusCode =
 * responseObject.jsonPath().getInt("statusCode");
 * LOGGER.info("Status Code in fetch lead Response is " + statusCode);
 * Assert.assertEquals(statusCode, 404);
 * fetchLeadObjectPP.validateResponseAgainstJSONSchema(
 * "MerchantService/V1/sdMerchant/lead/fetchLead_LOS/leadResponseSchema.json");
 * 
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Positive Create Lead Call Test Case", priority = 2)
 * public void createLeadPostPaid() {
 * 
 * createLead_LOS createLeadPP = new createLead_LOS(); Response responseObject =
 * middlewareServicesObject.createLead_LOS(createLeadPP, "INDIVIDUAL",
 * "HAPPY_LOANS", "postpaid_v2", "PAYTM_APP", session_token);
 * createLeadPP.setRefId(responseObject.jsonPath().getString("refId"));
 * createLeadPP.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * createLeadPP.setLeadId(responseObject.jsonPath().getString("leadId"));
 * createLeadPP.setDisplayMessage(responseObject.jsonPath().getString(
 * "displayMessage")); leadId = createLeadPP.getLeadId();
 * LOGGER.info("Lead id in Response of create lead call is " + leadId); int
 * StatusCode = responseObject.getStatusCode(); Assert.assertEquals(StatusCode,
 * 200); LOGGER.info("Status Code is " + StatusCode);
 * LOGGER.info("Status Code in fetch lead Response is " +
 * createLeadPP.getStatusCode());
 * Assert.assertEquals(createLeadPP.getStatusCode().toString(), "200");
 * createLeadPP.validateResponseAgainstJSONSchema(
 * "MerchantService/V1/sdMerchant/lead/createLead_LOS/leadResponseSchema.json");
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Positive Update Basic detaial Api Call Test Case",
 * priority = 3) public void updateLeadBasicDetailsPostPaid() {
 * 
 * updateLead_LOS updateLeadPP = new updateLead_LOS(); Response responseObject =
 * middlewareServicesObject.updateLead_LOS(updateLeadPP, "INDIVIDUAL",
 * "HAPPY_LOANS", "postpaid_v2", "PAYTM_APP", session_token,pan );
 * updateLeadPP.setRefId(responseObject.jsonPath().getString("refId"));
 * updateLeadPP.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * updateLeadPP.setLeadId(responseObject.jsonPath().getString("leadId"));
 * updateLeadPP.setisLeadAlreadyExistsFlage(responseObject.jsonPath().getBoolean
 * ("isLeadAlreadyExists")); leadId = updateLeadPP.getLeadId();
 * LOGGER.info("Lead id in Response of basic details lead call is " + leadId);
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode);
 * LOGGER.info("Status Code in update basic details lead call Response is " +
 * updateLeadPP.getStatusCode());
 * Assert.assertEquals(updateLeadPP.getStatusCode().toString(), "200");
 * Assert.assertEquals(updateLeadPP.getisLeadAlreadyExistsFlag(), true);
 * updateLeadPP.validateResponseAgainstJSONSchema(
 * "MerchantService/V1/sdMerchant/lead/updateLead_LOS/leadResponseSchema.json");
 * 
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Positive OTP callBack Verification Test Case", priority
 * = 4) public void otpCallBackVerficationPostPaid() {
 * 
 * otpCallback_LOS otpCallbackPP = new otpCallback_LOS(); generateJwtToken();
 * LOGGER.info("token is" + " " + token); Response responseObject =
 * middlewareServicesObject.callBackSaveOtp_LOS(otpCallbackPP, leadId,
 * "postpaid_v2", token, "application/json", "PAYTM_APP", custId);
 * otpCallbackPP.setRefId(responseObject.jsonPath().getString("refId"));
 * otpCallbackPP.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * otpCallbackPP.setDisplayMessage(responseObject.jsonPath().getString(
 * "displayMessage")); int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode);
 * LOGGER.info("Status Code in OTP callback Verification Response is " +
 * otpCallbackPP.getStatusCode());
 * Assert.assertEquals(otpCallbackPP.getStatusCode().toString(), "200");
 * LOGGER.info("Display Message in OTP callback Verification Response is " +
 * otpCallbackPP.getDisplayMessage());
 * otpCallbackPP.validateResponseAgainstJSONSchema(
 * "MerchantServicev5callbackforSaveOTP/otpCallBack_LOS/leadResponseSchema.json"
 * );
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Positive Get Kyc Status Api Call Test Case", priority =
 * 5) public void getKycStatusPostPaid() {
 * 
 * getKycStatus_LOS getKycStatusPP = new getKycStatus_LOS(); Response
 * responseObject = middlewareServicesObject.getKycStatus_LOS(getKycStatusPP,
 * "INDIVIDUAL", "HAPPY_LOANS", "postpaid_v2", "PAYTM_APP", session_token,
 * "application/json");
 * getKycStatusPP.setRefId(responseObject.jsonPath().getString("refId"));
 * getKycStatusPP.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * getKycStatusPP.setLeadId(responseObject.jsonPath().getString("leadId"));
 * getKycStatusPP.setKycStatus(responseObject.jsonPath().getString("kycStatus"))
 * ; getKycStatusPP.setPanNameMatchSuccess(responseObject.jsonPath().getBoolean(
 * "panNameMatchSuccess"));
 * getKycStatusPP.setStage(responseObject.jsonPath().getString("stage")); int
 * StatusCode = responseObject.getStatusCode(); Assert.assertEquals(StatusCode,
 * 200); LOGGER.info("Status Code is " + StatusCode);
 * LOGGER.info("Status Code in getKycStatus API call Response is " +
 * getKycStatusPP.getStatusCode());
 * Assert.assertEquals(getKycStatusPP.getStatusCode().toString(), "200");
 * LOGGER.info("stage in getKycStatus API call Response is " +
 * getKycStatusPP.getStage()); Assert.assertEquals(getKycStatusPP.getStage(),
 * "PAN_VERIFIED"); getKycStatusPP.validateResponseAgainstJSONSchema(
 * "MerchantServicev2LendinggetKycStatus/lending_LOS/leadResponseSchema.json");
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Positive alternate Number OTP Verification Test Case",
 * priority = 6) public void alternateNumberOtpCallBackVerficationPostPaid() {
 * 
 * alternateNoCallBack_LOS alternateNoCallBackPP = new
 * alternateNoCallBack_LOS(); generateJwtToken(); LOGGER.info("token is" + " " +
 * token); Response responseObject =
 * middlewareServicesObject.alternateNoOtpcallBack_LOS(alternateNoCallBackPP,
 * leadId, "postpaid_v2", token, "application/json", "PAYTM_APP", custId);
 * alternateNoCallBackPP.setRefId(responseObject.jsonPath().getString("refId"));
 * alternateNoCallBackPP.setStatusCode(responseObject.jsonPath().getInt(
 * "statusCode"));
 * alternateNoCallBackPP.setDisplayMessage(responseObject.jsonPath().getString(
 * "displayMessage")); int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode); LOGGER.
 * info("Status Code in alternate Number OTP callback Verification Response is "
 * + alternateNoCallBackPP.getStatusCode());
 * Assert.assertEquals(alternateNoCallBackPP.getStatusCode().toString(), "200");
 * LOGGER.
 * info("Display Message in alternate Number OTP callback Verification Response is "
 * + alternateNoCallBackPP.getDisplayMessage());
 * alternateNoCallBackPP.validateResponseAgainstJSONSchema(
 * "MerchantServicev5callbackforAlternateNumber/alternateNoCallBack_LOS/leadResponseSchema.json"
 * );
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Positive getBreStautus API call Verification Test Case",
 * priority = 7) public void getBreStatusPostPaid() {
 * 
 * getBreStatus_LOS getBreStatusPP = new getBreStatus_LOS(); Response
 * responseObject = middlewareServicesObject.getBreStatus_LOS(getBreStatusPP,
 * "INDIVIDUAL", "HAPPY_LOANS", "postpaid_v2", "PAYTM_APP",
 * "<EMAIL>", session_token, "application/json");
 * getBreStatusPP.setRefId(responseObject.jsonPath().getString("refId"));
 * getBreStatusPP.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode); LOGGER.info("Status Code in getBreStatus Api Call Response is "
 * + getBreStatusPP.getStatusCode());
 * Assert.assertEquals(getBreStatusPP.getStatusCode().toString(), "200");
 * getBreStatusPP.validateResponseAgainstJSONSchema(
 * "MerchantServicev2lendingGetBreStatus/getBreStatus_LOS/leadResponseSchema.json"
 * ); }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description =
 * "Positive Bre Callback data Update API call Verification Test Case", priority
 * = 8) public void breCallBackPostPaid() {
 * 
 * dataUpdate_LOS dataUpdatePP = new dataUpdate_LOS(); generateJwtToken();
 * LOGGER.info("token is" + " " + token); Response responseObject =
 * middlewareServicesObject.dataUpdateCallback_LOS(dataUpdatePP, "HAPPY_LOANS",
 * "INDIVIDUAL", leadId, "postpaid_v2", token, "application/json", "PAYTM_APP",
 * custId); dataUpdatePP.setRefId(responseObject.jsonPath().getString("refId"));
 * dataUpdatePP.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * dataUpdatePP.setDisplayMessage(responseObject.jsonPath().getString(
 * "displayMessage"));
 * dataUpdatePP.setOeStage(responseObject.jsonPath().getString("oeStage")); int
 * StatusCode = responseObject.getStatusCode(); Assert.assertEquals(StatusCode,
 * 200); LOGGER.info("Status Code is " + StatusCode);
 * LOGGER.info("Status Code in bre callback data update Api Call Response is " +
 * dataUpdatePP.getStatusCode());
 * Assert.assertEquals(dataUpdatePP.getStatusCode().toString(), "200");
 * Assert.assertEquals(dataUpdatePP.getOeStage(), "BRE_SUCCESS");
 * dataUpdatePP.validateResponseAgainstJSONSchema(
 * "MerchantServicev2LendingDataUpdate/dataUpdate_LOS/leadResponseSchema.json");
 * 
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description =
 * "Positive checkBreStautus API call Verification Test Case", priority = 9)
 * public void checkBreStatusPostPaid() { checkBreStatus_LOS checkBreStatusPP=
 * new checkBreStatus_LOS(); Response responseObject =
 * middlewareServicesObject.checkBreStatus_LOS(checkBreStatusPP, "postpaid_v2",
 * "INDIVIDUAL", "PAYTM_APP", "HAPPY_LOANS", session_token);
 * checkBreStatusPP.setRefId(responseObject.jsonPath().getString("refId"));
 * checkBreStatusPP.setStatusCode(responseObject.jsonPath().getInt("statusCode")
 * ); checkBreStatusPP.setStage(responseObject.jsonPath().getString("stage"));
 * checkBreStatusPP.setMaxLoanAmount(responseObject.jsonPath().getString(
 * "maxLoanAmount"));
 * checkBreStatusPP.setMinLoanAmount(responseObject.jsonPath().getString(
 * "minLoanAmount"));
 * checkBreStatusPP.setBureau(responseObject.jsonPath().getString("bureau"));
 * //checkBreStatusPP.setCreditScore(responseObject.jsonPath().getInt(
 * "creditScore"));
 * checkBreStatusPP.setLastFetchDate(responseObject.jsonPath().getLong(
 * "lastFetchDate")); int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode); Assert.assertEquals(checkBreStatusPP.getStage(), "BRE_SUCCESS");
 * checkBreStatusPP.validateResponseAgainstJSONSchema(
 * "MerchantServicev2LendingLead/checkBreStatus_LOS/leadResponseSchema.json");
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Positive add Address API call Verification Test Case",
 * priority = 10) public void addAddressPostPaid() {
 * 
 * updateLeadAddress_LOS updateLeadAddressPP = new updateLeadAddress_LOS();
 * Response responseObject =
 * middlewareServicesObject.updateLeadAddress_LOS(updateLeadAddressPP,
 * "INDIVIDUAL", "HAPPY_LOANS", "postpaid_v2", "PAYTM_APP", session_token);
 * updateLeadAddressPP.setStatusCode(responseObject.jsonPath().getInt(
 * "statusCode"));
 * updateLeadAddressPP.setLeadId(responseObject.jsonPath().getString("leadId"));
 * updateLeadAddressPP.setRefId(responseObject.jsonPath().getString("refId"));
 * updateLeadAddressPP.setisLeadAlreadyExistsFlage(responseObject.jsonPath().
 * getBoolean("isLeadAlreadyExists")); int StatusCode =
 * responseObject.getStatusCode(); Assert.assertEquals(StatusCode, 200);
 * LOGGER.info("Status Code is " + StatusCode);
 * LOGGER.info("Status Code in Add Address Api Call Response is " +
 * updateLeadAddressPP.getStatusCode());
 * Assert.assertEquals(updateLeadAddressPP.getStatusCode().toString(), "200");
 * updateLeadAddressPP.validateResponseAgainstJSONSchema(
 * "MerchantService/V1/sdMerchant/lead/updateLeadAddress_LOS/leadResponseSchema.json"
 * );
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description =
 * "Positive generate dynamic tnc API call Verification Test Case", priority =
 * 11) public void generateDynamicTnCPostPaid() {
 * 
 * dynamicTnC_LOS dynamicTncPP = new dynamicTnC_LOS(); Response responseObject =
 * middlewareServicesObject.generateTNC_LOS(dynamicTncPP, leadId,
 * session_token);
 * dynamicTncPP.setStatus(responseObject.jsonPath().getString("meta.status"));
 * dynamicTncPP.setRefId(responseObject.jsonPath().getString("meta.refId"));
 * dynamicTncPP.setCode(responseObject.jsonPath().getString("data.state.code"));
 * dynamicTncPP.setUniqueIdentifier(responseObject.jsonPath().getString(
 * "data.state.uniqueIdentifier"));
 * dynamicTncPP.setMd5(responseObject.jsonPath().getString("data.state.md5"));
 * dynamicTncPP.setAccept(responseObject.jsonPath().getInt("data.state.accept"))
 * ; dynamicTncPP.setTncName(responseObject.jsonPath().getString(
 * "data.state.tncName")); tncName = dynamicTncPP.getTncName(); uniqueIdentifier
 * = dynamicTncPP.getUniqueIdentifier(); md5 = dynamicTncPP.getMd5();
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode); LOGGER.info("Tnc Name is " + tncName);
 * LOGGER.info("Unique Identfier is " + uniqueIdentifier); LOGGER.info("md5 is "
 * + md5); dynamicTncPP.validateResponseAgainstJSONSchema(
 * "MerchantServiceloanLeadDynamicTNC/dynamicTnC_LOS/leadResponseSchema.json");
 * 
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description =
 * "Positive Save Tnc & Submit Application API call Verification Test Case",
 * priority = 12) public void saveDynamicTnCPostPaid() throws
 * InterruptedException {
 * 
 * saveTncAndSubmitApplication_LOS saveTnCsubmitAppPP = new
 * saveTncAndSubmitApplication_LOS(); Response responseObject =
 * middlewareServicesObject.saveSubMitTnC_LOS(saveTnCsubmitAppPP, leadId,
 * session_token, "application/json", tncName, uniqueIdentifier, md5);
 * saveTnCsubmitAppPP.setStage(responseObject.jsonPath().getString("meta.stage")
 * ); saveTnCsubmitAppPP.setSubStage(responseObject.jsonPath().getString(
 * "meta.subStage"));
 * saveTnCsubmitAppPP.setStatus(responseObject.jsonPath().getString(
 * "meta.status"));
 * saveTnCsubmitAppPP.setRefId(responseObject.jsonPath().getString("meta.refId")
 * );
 * saveTnCsubmitAppPP.setState(responseObject.jsonPath().getString("data.state")
 * ); int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode); Assert.assertEquals(saveTnCsubmitAppPP.getStatus(), "success");
 * Assert.assertEquals(saveTnCsubmitAppPP.getState(),
 * " Loan Application is Accepted");
 * saveTnCsubmitAppPP.validateResponseAgainstJSONSchema(
 * "MerchantServiceLoanLeadSubmitApplication/saveTncAndSubmitApplication_LOS/leadResponseSchema.json"
 * );
 * 
 * Thread.sleep(300000); }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Positive fetch lead Call Test Case", priority = 13)
 * public void checkLeadStatusInfetchLeadPostPaid() { fetchLead_LOS
 * fetchLeadObjectPP = new fetchLead_LOS(); Response responseObject =
 * middlewareServicesObject.fetchLead_LOS(fetchLeadObjectPP, "INDIVIDUAL",
 * "HAPPY_LOANS", "postpaid_v2", "PAYTM_APP", session_token); int StatusCode =
 * responseObject.getStatusCode(); Assert.assertEquals(StatusCode, 200);
 * LOGGER.info("Stage in fetch lead Response is  "
 * +responseObject.jsonPath().get("stage"));
 * Assert.assertEquals(responseObject.jsonPath().get("stage"),
 * "LMS_SUBMIT_APPLICATION_SUCCESS");
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description =
 * "Positive LMS call After Submit Application API call Verification Test Case",
 * priority = 14) public void lmsCallBackAfterSubmitApplicationPostPaid() {
 * 
 * lmsCallBack_LOS lmsCallBackPP= new lmsCallBack_LOS(); generateJwtToken();
 * LOGGER.info("token is" + " " + token); Response responseObject =
 * middlewareServicesObject.lmsCallbackAfterSubmitApplication_LOS(lmsCallBackPP,
 * leadId, "postpaid_v2", token, "application/json", custId, "SUCCESS");
 * lmsCallBackPP.setRefId(responseObject.jsonPath().getString("refId"));
 * lmsCallBackPP.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * lmsCallBackPP.setDisplayMessage(responseObject.jsonPath().getString(
 * "displayMessage")); int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode); Assert.assertEquals(lmsCallBackPP.getStatusCode().toString(),
 * "200"); lmsCallBackPP.validateResponseAgainstJSONSchema(
 * "MerchantService.v5/callback/lmsCallBack_LOS/leadResponseSchema.json");
 * 
 * 
 * 
 * }
 * 
 * 
 * 
 * 
 * 
 * 
 * 
 * 
 * 
 * 
 * 
 * 
 * }
 */