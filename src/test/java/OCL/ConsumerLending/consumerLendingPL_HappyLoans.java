/*
 * package OCL.ConsumerLending;
 * 
 * import java.time.LocalDateTime; import java.time.ZoneId;
 * 
 * import org.apache.log4j.Logger; import org.testng.Assert; import
 * org.testng.annotations.BeforeSuite; import org.testng.annotations.Test;
 * 
 * import com.auth0.jwt.JWT; import com.auth0.jwt.algorithms.Algorithm; import
 * com.goldengate.common.BaseMethod; import
 * com.paytm.apitools.util.annotations.Owner;
 * 
 * import OCL.Lending.BusinessLending.business_Lending; import
 * Request.MerchantService.v1.sdMerchant.addAddressPL_LOS; import
 * Request.MerchantService.v1.sdMerchant.createLeadPL_LOS; import
 * Request.MerchantService.v1.sdMerchant.createLead_LOS; import
 * Request.MerchantService.v1.sdMerchant.fetchLead_LOS; import
 * Request.MerchantService.v1.sdMerchant.updateLeadAddressPL_LOS; import
 * Request.MerchantService.v1.sdMerchant.updateLeadAddress_LOS; import
 * Request.MerchantService.v1.sdMerchant.updateLeadPL_LOS; import
 * Request.MerchantService.v1.sdMerchant.updateLead_LOS; import
 * Request.MerchantService.v2.lending.checkBreStatus_LOS; import
 * Request.MerchantService.v2.lending.getBreStatus_LOS; import
 * Request.MerchantService.v2.lending.getKycStatus_LOS; import
 * Request.MerchantService.v2.lending.dataUpdate.dataUpdate_LOS; import
 * Request.MerchantService.v5.callback.emailCallBackPL_LOS; import
 * Request.MerchantService.v5.callback.otpCallback_LOS; import
 * Services.MechantService.MiddlewareServices; import
 * Services.Utilities.Utilities; import io.restassured.response.Response;
 * 
 * public class consumerLendingPL_HappyLoans extends BaseMethod {
 * 
 * public static String AgentNo = "6665550059"; public static String
 * session_token = ""; public static String pan = ""; public static String mid =
 * ""; public static String leadId = ""; public static String state = ""; public
 * static String token = ""; public static String custId = "1000494550"; public
 * static String tncName = ""; public static String uniqueIdentifier = "";
 * public static String md5 = ""; public static Integer accept = 1;
 * 
 * private static final Logger LOGGER =
 * Logger.getLogger(business_Lending.class); MiddlewareServices
 * middlewareServicesObject = new MiddlewareServices(); Utilities utilities =
 * new Utilities();
 * 
 * @BeforeSuite
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true) public void
 * getAgentToken() { LOGGER.info(" Before Suite Method for Agent Login ");
 * session_token = ApplicantToken(AgentNo, "paytm@123");
 * LOGGER.info("Applicant Token for Lending : " + session_token);
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "jwt token creation") public String generateJwtToken() {
 * 
 * LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of("GMT+05:30")); //
 * LocalDate localDate = localDateTime.toLocalDate();
 * System.out.println("Date is :" + localDateTime); String ts =
 * localDateTime.toString();
 * 
 * Algorithm buildAlgorithm =
 * Algorithm.HMAC256("4f438a17-2436-4231-a172-c338ab9d40bb"); token =
 * JWT.create().withIssuer("OE").withClaim("clientId",
 * "LENDING").withClaim("custId", custId) .withClaim("timestamp", ts +
 * "+05:30").sign(buildAlgorithm); return token; }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(priority = 1, description = "Fetch Lead Personal Loan", groups = {
 * "Regression" }) public void fetchLeadPersonalLoan() { fetchLead_LOS
 * fetchLeadObjectPL = new fetchLead_LOS(); Response responseObject =
 * middlewareServicesObject.fetchLead_LOS(fetchLeadObjectPL, "INDIVIDUAL",
 * "HAPPY_LOANS", "personal_loan", "PAYTM_APP", session_token);
 * fetchLeadObjectPL.setRefId(responseObject.jsonPath().getString("refId"));
 * fetchLeadObjectPL.setStatusCode(responseObject.jsonPath().getInt("statusCode"
 * ));
 * fetchLeadObjectPL.setLeadId(responseObject.jsonPath().getString("leadId"));
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode); pan = responseObject.jsonPath().getString("business.pan");
 * LOGGER.info("pan in business object of fetch lead Response is " + pan);
 * fetchLeadObjectPL.setStatusCode(responseObject.jsonPath().getInt("statusCode"
 * )); fetchLeadObjectPL.getStatusCode(); int statusCode =
 * responseObject.jsonPath().getInt("statusCode");
 * LOGGER.info("Status Code in fetch lead Response is " + statusCode);
 * Assert.assertEquals(statusCode, 404); //
 * fetchLeadObjectPL.validateResponseAgainstJSONSchema(
 * "MerchantService/V1/sdMerchant/lead/fetchLead_LOS/leadResponseSchema.json");
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(priority = 2, description = "Create Lead Personal Loan", groups = {
 * "Regression" }) public void createLeadPersonalLoan() {
 * 
 * createLeadPL_LOS createLeadPL = new createLeadPL_LOS(); Response
 * responseObject = middlewareServicesObject.createLeadPL_LOS(createLeadPL,
 * "INDIVIDUAL", "HAPPY_LOANS", "personal_loan", "PAYTM_APP", session_token);
 * createLeadPL.setRefId(responseObject.jsonPath().getString("refId"));
 * createLeadPL.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * createLeadPL.setLeadId(responseObject.jsonPath().getString("leadId"));
 * createLeadPL.setDisplayMessage(responseObject.jsonPath().getString(
 * "displayMessage")); leadId = createLeadPL.getLeadId();
 * LOGGER.info("Lead id in Response of create lead call is " + leadId); int
 * StatusCode = responseObject.getStatusCode(); Assert.assertEquals(StatusCode,
 * 200); LOGGER.info("Status Code is " + StatusCode);
 * LOGGER.info("Status Code in fetch lead Response is " +
 * createLeadPL.getStatusCode());
 * Assert.assertEquals(createLeadPL.getStatusCode().toString(), "200");
 * createLeadPL.validateResponseAgainstJSONSchema(
 * "MerchantService/V1/sdMerchant/lead/createLead_LOS/leadResponseSchema.json");
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(priority = 3, description =
 * "Update Basic Details of Lead Personal Loan", groups = { "Regression" })
 * public void updateLeadBasicDetailsPersonalLoan() {
 * 
 * updateLeadPL_LOS updateLeadPL = new updateLeadPL_LOS(); Response
 * responseObject = middlewareServicesObject.updateLeadPL_LOS(updateLeadPL,
 * "INDIVIDUAL", "HAPPY_LOANS", "personal_loan", "PAYTM_APP", session_token,
 * pan); updateLeadPL.setRefId(responseObject.jsonPath().getString("refId"));
 * updateLeadPL.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * updateLeadPL.setFirstNameAsPerPan(responseObject.jsonPath().getString(
 * "firstNameAsPerPan"));
 * updateLeadPL.setLastNameAsPerPan(responseObject.jsonPath().get(
 * "lastNameAsPerPan"));
 * updateLeadPL.setMiddleNameAsPerPan(responseObject.jsonPath().get(
 * "middleNameAsPerPan"));
 * updateLeadPL.setMobileNumber(responseObject.jsonPath().get("mobileNumber"));
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode);
 * LOGGER.info("Status Code in update basic details lead call Response is " +
 * updateLeadPL.getStatusCode());
 * Assert.assertEquals(updateLeadPL.getStatusCode().toString(), "200");
 * updateLeadPL.validateResponseAgainstJSONSchema(
 * "MerchantService/V1/sdMerchant/lead/updateLeadPL_LOS/leadResponseSchema.json"
 * );
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(priority = 4, description = "Email Call Back for Personal Loan Lead",
 * groups = { "Regression" }) public void emailCallBackVerficationPersonalLoan()
 * {
 * 
 * emailCallBackPL_LOS emailCallBackPL = new emailCallBackPL_LOS();
 * generateJwtToken(); LOGGER.info("token is" + " " + token); Response
 * responseObject =
 * middlewareServicesObject.emailCallBackPL_LOS(emailCallBackPL, leadId,
 * "personal_loan", token, "application/json", custId);
 * emailCallBackPL.setRefId(responseObject.jsonPath().getString("refId"));
 * emailCallBackPL.setStatusCode(responseObject.jsonPath().getInt("statusCode"))
 * ; emailCallBackPL.setDisplayMessage(responseObject.jsonPath().getString(
 * "displayMessage")); int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode);
 * LOGGER.info("Status Code in Email callback Verification Response is " +
 * emailCallBackPL.getStatusCode());
 * Assert.assertEquals(emailCallBackPL.getStatusCode().toString(), "200");
 * LOGGER.info( "Display Message in Email callback Verification Response is " +
 * emailCallBackPL.getDisplayMessage());
 * emailCallBackPL.validateResponseAgainstJSONSchema(
 * "MerchantService.v5/callback/emailCallBackPL_LOS/leadResponseSchema.json");
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(priority = 5, description =
 * "Update Additional Details for Personal Loan Lead", groups = { "Regression"
 * }) public void updateAdditionalDetailsPersonalLoan() {
 * 
 * updateLeadAddressPL_LOS updateLeadPL = new updateLeadAddressPL_LOS();
 * Response responseObject =
 * middlewareServicesObject.updateLeadAdditionalDetailPL_LOS9(updateLeadPL,
 * "INDIVIDUAL", "HAPPY_LOANS", "personal_loan", "PAYTM_APP",
 * "application/json", session_token);
 * updateLeadPL.setRefId(responseObject.jsonPath().getString("refId"));
 * updateLeadPL.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * updateLeadPL.setFirstNameAsPerPan(responseObject.jsonPath().getString(
 * "firstNameAsPerPan"));
 * updateLeadPL.setLastNameAsPerPan(responseObject.jsonPath().get(
 * "lastNameAsPerPan"));
 * updateLeadPL.setMiddleNameAsPerPan(responseObject.jsonPath().get(
 * "middleNameAsPerPan"));
 * updateLeadPL.setMobileNumber(responseObject.jsonPath().get("mobileNumber"));
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode);
 * LOGGER.info("Status Code in update Additional details lead call Response is "
 * + updateLeadPL.getStatusCode());
 * Assert.assertEquals(updateLeadPL.getStatusCode().toString(), "200");
 * updateLeadPL.validateResponseAgainstJSONSchema(
 * "MerchantService/V1/sdMerchant/lead/updateLeadAddressPL_LOS/leadResponseSchema.json"
 * );
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(priority = 6, description =
 * "OTP callback Verification for Personal Loan", groups = { "Regression" })
 * public void otpCallBackVerficationPersonalLoan() {
 * 
 * otpCallback_LOS otpCallbackPP = new otpCallback_LOS(); generateJwtToken();
 * LOGGER.info("token is" + " " + token); Response responseObject =
 * middlewareServicesObject.callBackSaveOtp_LOS(otpCallbackPP, leadId,
 * "personal_loan", token, "application/json", "PAYTM_APP", custId);
 * otpCallbackPP.setRefId(responseObject.jsonPath().getString("refId"));
 * otpCallbackPP.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * otpCallbackPP.setDisplayMessage(responseObject.jsonPath().getString(
 * "displayMessage")); int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode);
 * LOGGER.info("Status Code in OTP callback Verification Response is " +
 * otpCallbackPP.getStatusCode());
 * Assert.assertEquals(otpCallbackPP.getStatusCode().toString(), "200");
 * LOGGER.info("Display Message in OTP callback Verification Response is " +
 * otpCallbackPP.getDisplayMessage());
 * otpCallbackPP.validateResponseAgainstJSONSchema(
 * "MerchantServicev5callbackforSaveOTP/otpCallBack_LOS/leadResponseSchema.json"
 * );
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(priority = 7, description = "Get Kyc Status for Personal Loan", groups
 * = { "Regression" }) public void getKycStatusPersonalLoan() {
 * 
 * getKycStatus_LOS getKycStatusPL = new getKycStatus_LOS(); Response
 * responseObject = middlewareServicesObject.getKycStatus_LOS(getKycStatusPL,
 * "INDIVIDUAL", "HAPPY_LOANS", "personal_loan", "PAYTM_APP", session_token,
 * "application/json");
 * getKycStatusPL.setRefId(responseObject.jsonPath().getString("refId"));
 * getKycStatusPL.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * getKycStatusPL.setLeadId(responseObject.jsonPath().getString("leadId"));
 * getKycStatusPL.setKycStatus(responseObject.jsonPath().getString("kycStatus"))
 * ; getKycStatusPL.setPanNameMatchSuccess(responseObject.jsonPath().getBoolean(
 * "panNameMatchSuccess"));
 * getKycStatusPL.setStage(responseObject.jsonPath().getString("stage")); int
 * StatusCode = responseObject.getStatusCode(); Assert.assertEquals(StatusCode,
 * 200); LOGGER.info("Status Code is " + StatusCode);
 * LOGGER.info("Status Code in getKycStatus API call Response is " +
 * getKycStatusPL.getStatusCode());
 * Assert.assertEquals(getKycStatusPL.getStatusCode().toString(), "200");
 * LOGGER.info("stage in getKycStatus API call Response is " +
 * getKycStatusPL.getStage()); Assert.assertEquals(getKycStatusPL.getStage(),
 * "PAN_VERIFIED"); getKycStatusPL.validateResponseAgainstJSONSchema(
 * "MerchantServicev2LendinggetKycStatus/lending_LOS/leadResponseSchema.json");
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(priority = 8, description = "Add address call for Personal Loan",
 * groups = { "Regression" }) public void addAddressPersonalLoan() {
 * 
 * addAddressPL_LOS addLeadAddressPP= new addAddressPL_LOS(); Response
 * responseObject = middlewareServicesObject.addAddressPL_LOS(addLeadAddressPP,
 * "INDIVIDUAL", "HAPPY_LOANS", "personal_loan", "PAYTM_APP", session_token,
 * "ADDRESS_DETAILS");
 * addLeadAddressPP.setStatusCode(responseObject.jsonPath().getInt("statusCode")
 * ); addLeadAddressPP.setRefId(responseObject.jsonPath().getString("refId"));
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode); LOGGER.info("Status Code in Add Address Api Call Response is " +
 * addLeadAddressPP.getStatusCode());
 * Assert.assertEquals(addLeadAddressPP.getStatusCode().toString(), "200");
 * addLeadAddressPP.validateResponseAgainstJSONSchema(
 * "MerchantService/V1/sdMerchant/lead/addAddressPL_LOS/leadResponseSchema.json"
 * );
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(priority = 9, description = " Get Bre Status call for Personal Loan",
 * groups = { "Regression" }) public void getBreStatusPersonalLoan() {
 * 
 * getBreStatus_LOS getBreStatusPP = new getBreStatus_LOS(); Response
 * responseObject = middlewareServicesObject.getBreStatus_LOS(getBreStatusPP,
 * "INDIVIDUAL", "HAPPY_LOANS", "personal_loan", "PAYTM_APP",
 * "<EMAIL>", session_token, "application/json");
 * getBreStatusPP.setRefId(responseObject.jsonPath().getString("refId"));
 * getBreStatusPP.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode); LOGGER.info("Status Code in getBreStatus Api Call Response is "
 * + getBreStatusPP.getStatusCode());
 * Assert.assertEquals(getBreStatusPP.getStatusCode().toString(), "200");
 * getBreStatusPP.validateResponseAgainstJSONSchema(
 * "MerchantServicev2lendingGetBreStatus/getBreStatus_LOS/leadResponseSchema.json"
 * ); }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(priority = 10, description = " Bre Status call back for Personal Loan",
 * groups = { "Regression" }) public void breCallBackPersonalLoan() {
 * 
 * dataUpdate_LOS dataUpdatePP = new dataUpdate_LOS(); generateJwtToken();
 * LOGGER.info("token is" + " " + token); Response responseObject =
 * middlewareServicesObject.dataUpdateCallback_LOS(dataUpdatePP, "HAPPY_LOANS",
 * "INDIVIDUAL", leadId, "personal_loan", token, "application/json",
 * "PAYTM_APP", custId);
 * dataUpdatePP.setRefId(responseObject.jsonPath().getString("refId"));
 * dataUpdatePP.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * dataUpdatePP.setDisplayMessage(responseObject.jsonPath().getString(
 * "displayMessage"));
 * dataUpdatePP.setOeStage(responseObject.jsonPath().getString("oeStage")); int
 * StatusCode = responseObject.getStatusCode(); Assert.assertEquals(StatusCode,
 * 200); LOGGER.info("Status Code is " + StatusCode);
 * LOGGER.info("Status Code in bre callback data update Api Call Response is " +
 * dataUpdatePP.getStatusCode());
 * Assert.assertEquals(dataUpdatePP.getStatusCode().toString(), "200");
 * Assert.assertEquals(dataUpdatePP.getOeStage(), "BRE_SUCCESS");
 * dataUpdatePP.validateResponseAgainstJSONSchema(
 * "MerchantServicev2LendingDataUpdate/dataUpdate_LOS/leadResponseSchema.json");
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(priority = 11, description =
 * " Check Bre Status call back for Personal Loan", groups = { "Regression" })
 * public void checkBreStatusPersonalLoan() { checkBreStatus_LOS
 * checkBreStatusPP = new checkBreStatus_LOS(); Response responseObject =
 * middlewareServicesObject.checkBreStatus_LOS(checkBreStatusPP,
 * "personal_loan", "INDIVIDUAL", "PAYTM_APP", "HAPPY_LOANS", session_token);
 * checkBreStatusPP.setRefId(responseObject.jsonPath().getString("refId"));
 * checkBreStatusPP.setStatusCode(responseObject.jsonPath().getInt("statusCode")
 * ); checkBreStatusPP.setStage(responseObject.jsonPath().getString("stage"));
 * checkBreStatusPP.setMaxLoanAmount(responseObject.jsonPath().getString(
 * "maxLoanAmount"));
 * checkBreStatusPP.setMinLoanAmount(responseObject.jsonPath().getString(
 * "minLoanAmount"));
 * checkBreStatusPP.setBureau(responseObject.jsonPath().getString("bureau")); //
 * checkBreStatusPP.setCreditScore(responseObject.jsonPath().getInt(
 * "creditScore"));
 * checkBreStatusPP.setLastFetchDate(responseObject.jsonPath().getLong(
 * "lastFetchDate")); int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); LOGGER.info("Status Code is " +
 * StatusCode); Assert.assertEquals(checkBreStatusPP.getStage(), "BRE_SUCCESS");
 * //checkBreStatusPP.validateResponseAgainstJSONSchema(
 * "MerchantServicev2LendingLead/checkBreStatus_LOS/leadResponseSchema.json");
 * 
 * }
 * 
 * }
 */