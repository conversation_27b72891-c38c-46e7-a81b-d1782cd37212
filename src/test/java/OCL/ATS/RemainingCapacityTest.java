package OCL.ATS;

import Request.ats.CheckRemainingCapacity;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class RemainingCapacityTest extends BaseMethod
{
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();

    private static final Logger LOGGER = LogManager.getLogger(RemainingCapacityTest.class);
    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";

    String token = findXMWExTokenforPanel("8010630022", "paytm@123");
    //CheckRemainingCapacityMethod
    @Test(priority = 0, description = "Capacity EDC and SoundBox for ********* agent")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_capaforagent1()
    {
        CheckRemainingCapacity reqobj = new CheckRemainingCapacity(P.TESTDATA.get("ThresholdCountRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("current_pendency", 0);
        body.put("device_category", "soundbox");
        body.put("current_pendency1", 0);
        body.put("device_category1", "edc");
        body.put("fse_emp_id", "22223");

        Response respObj = MiddlewareServicesObject.CheckRemainingCapacityMethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);

    }

    @Test(priority = 0,description = "Capacity EDC and SoundBox for 7771216290 agent")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_002_capaforagent1()
    {
        CheckRemainingCapacity reqobj = new CheckRemainingCapacity(P.TESTDATA.get("ThresholdCountRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept","*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("current_pendency", 0);
        body.put("device_category", "soundbox");
        body.put("current_pendency1", 0);
        body.put("device_category1", "edc");
        body.put("fse_emp_id", "802337");////101010

        Response respObj = MiddlewareServicesObject.CheckRemainingCapacityMethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(),200);
    }

    @Test(priority = 0,description = "Remaining Capacity of EDC for 8010630022 agent")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_003_capacityforEDC()
    {
        CheckRemainingCapacity reqobj = new CheckRemainingCapacity(P.TESTDATA.get("ThresholdCountRequestSingleDevice"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept","*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("current_pendency", 0);
        body.put("device_category", "edc");
        body.put("fse_emp_id", "22223");

        Response respObj = MiddlewareServicesObject.CheckRemainingCapacityMethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(),200);
    }

    @Test(priority = 0,description = "Remaining Capacity of SoundBox for 8010630022 agent")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_004_capacityforSB()
    {
        CheckRemainingCapacity reqobj = new CheckRemainingCapacity(P.TESTDATA.get("ThresholdCountRequestSingleDevice"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept","*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("current_pendency", 0);
        body.put("device_category", "soundbox");
        body.put("fse_emp_id", "22223");

        Response respObj = MiddlewareServicesObject.CheckRemainingCapacityMethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(),200);
    }

    @Test(priority = 0,description = "Token is missing")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_005_missingtoken()
    {
        CheckRemainingCapacity reqobj = new CheckRemainingCapacity(P.TESTDATA.get("ThresholdCountRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept","*/*");
        //headers.put("X-MW-TOKEN-EX", token);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("current_pendency", 0);
        body.put("device_category", "soundbox");
        body.put("current_pendency1", 0);
        body.put("device_category1", "edc");
        body.put("fse_emp_id", "22223");

        Response respObj = null;
        try{
            respObj = MiddlewareServicesObject.CheckRemainingCapacityMethod(reqobj, headers, body);
        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {

            int statusCode = respObj.getStatusCode();
            Assert.assertEquals(statusCode, 401);
        }

    }

    @Test(priority = 0,description = "Token is invalid")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_006_invalidtoken1()
    {
        CheckRemainingCapacity reqobj = new CheckRemainingCapacity(P.TESTDATA.get("ThresholdCountRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept","*/*");
        headers.put("X-MW-TOKEN-EX", "1XAQAZHNlzPDfmg");
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("current_pendency", 0);
        body.put("device_category", "soundbox");
        body.put("current_pendency1", 0);
        body.put("device_category1", "edc");
        body.put("fse_emp_id", "22223");

        Response respObj = null;
        try{
            respObj = MiddlewareServicesObject.CheckRemainingCapacityMethod(reqobj, headers, body);
        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {

            int statusCode = respObj.getStatusCode();
            Assert.assertEquals(statusCode, 401);
        }

    }

    @Test(priority = 0,description = "Token is invalid")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_007_invalidtoken2()
    {
        CheckRemainingCapacity reqobj = new CheckRemainingCapacity(P.TESTDATA.get("ThresholdCountRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept","*/*");
        headers.put("X-MW-TOKEN-EX", "TOKEN");
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("current_pendency", 0);
        body.put("device_category", "soundbox");
        body.put("current_pendency1", 0);
        body.put("device_category1", "edc");
        body.put("fse_emp_id", "22223");

        Response respObj = null;
        try{
            respObj = MiddlewareServicesObject.CheckRemainingCapacityMethod(reqobj, headers, body);
        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {

            int statusCode = respObj.getStatusCode();
            Assert.assertEquals(statusCode, 500);
        }

    }

    @Test(priority = 0,description = "Changing current_pendency values")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_008_Changingpendency()
    {
        CheckRemainingCapacity reqobj = new CheckRemainingCapacity(P.TESTDATA.get("ThresholdCountRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept","*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("current_pendency", 5);
        body.put("device_category", "soundbox");
        body.put("current_pendency1", 5);
        body.put("device_category1", "edc");
        body.put("fse_emp_id", "22223");

        Response respObj = MiddlewareServicesObject.CheckRemainingCapacityMethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);
        LOGGER.info("Remaining Capacity : " + respObj.jsonPath().getString("data.fse_limit_details[0].device_limit_details[0].remaining_capacity"));

    }

    @Test(priority = 0,description = "Changing device category")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_009_otherdevicecategory()
    {
        CheckRemainingCapacity reqobj = new CheckRemainingCapacity(P.TESTDATA.get("ThresholdCountRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept","*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("current_pendency1", 0);
        body.put("device_category1", "other");
        body.put("fse_emp_id", "22223");

        Response respObj = MiddlewareServicesObject.CheckRemainingCapacityMethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 500);
        LOGGER.info("Remaining Capacity for other category : " + respObj.jsonPath().getString("data.fse_limit_details[0].device_limit_details[0].remaining_capacity"));
    }


    @Test(priority = 0,description = "without fse limit details")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_010_without_fse_limit_details()
    {
        CheckRemainingCapacity reqobj = new CheckRemainingCapacity(P.TESTDATA.get("ThresholdCountRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("fse_emp_id", "22223");

        Response respObj = MiddlewareServicesObject.CheckRemainingCapacityMethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 500);
        LOGGER.info("Remaining Capacity : " + respObj.jsonPath().getString("data.fse_limit_details[0].device_limit_details[0].remaining_capacity"));

    }

    @Test(priority = 0,description = "without fse emp id")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_011_without_fse_id()
    {
        CheckRemainingCapacity reqobj = new CheckRemainingCapacity(P.TESTDATA.get("ThresholdCountRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("current_pendency", 0);
        body.put("device_category", "soundbox");
        body.put("current_pendency1", 0);
        body.put("device_category1", "edc");

        Response respObj = MiddlewareServicesObject.CheckRemainingCapacityMethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);
    }
    @Test(priority = 0,description = "Remaining Capacity when skulimit is not set the agent ")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_012_noskulimit()
    {
        CheckRemainingCapacity reqobj = new CheckRemainingCapacity(P.TESTDATA.get("ThresholdCountRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept","*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("current_pendency", 0);
        body.put("device_category", "soundbox");
        body.put("current_pendency1", 0);
        body.put("device_category1", "edc");
        body.put("fse_emp_id", "101010");

        Response respObj = MiddlewareServicesObject.CheckRemainingCapacityMethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(),200);
        LOGGER.info("Remaining Capacity : " + respObj.jsonPath().getString("data.fse_limit_details[0].device_limit_details[0].remaining_capacity"));
    }

    @Test(priority = 0,description = "fse emp id is invalid")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_013_invalidfseid()
    {
        CheckRemainingCapacity reqobj = new CheckRemainingCapacity(P.TESTDATA.get("ThresholdCountRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept","*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("current_pendency", 0);
        body.put("device_category", "soundbox");
        body.put("current_pendency1", 0);
        body.put("device_category1", "edc");
        body.put("fse_emp_id", "101550");

        Response respObj = MiddlewareServicesObject.CheckRemainingCapacityMethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(),200);
        LOGGER.info("Remaining Capacity : " + respObj.jsonPath().getString("data.fse_limit_details[0].device_limit_details[0].remaining_capacity"));
    }

    @Test(priority = 0,description = "when current pendency has no integer value")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_014_invalidpendency1()
    {
        CheckRemainingCapacity reqobj = new CheckRemainingCapacity(P.TESTDATA.get("ThresholdCountRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept","*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("current_pendency", "abc");
        body.put("device_category", "soundbox");
        body.put("current_pendency1", "abc");
        body.put("device_category1", "edc");
        body.put("fse_emp_id", "22223");

        Response respObj = MiddlewareServicesObject.CheckRemainingCapacityMethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(),400);
    }

    @Test(priority = 0,description = "current pendency has String number")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC_015_invalidpendency2()
    {
        CheckRemainingCapacity reqobj = new CheckRemainingCapacity(P.TESTDATA.get("ThresholdCountRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept","*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("current_pendency", "0");
        body.put("device_category", "soundbox");
        body.put("current_pendency1", "0");
        body.put("device_category1", "edc");
        body.put("fse_emp_id", "22223");

        Response respObj = MiddlewareServicesObject.CheckRemainingCapacityMethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(),200);
        LOGGER.info("Remaining Capacity : " + respObj.jsonPath().getString("data.fse_limit_details[0].device_limit_details[0].remaining_capacity"));

    }
}
