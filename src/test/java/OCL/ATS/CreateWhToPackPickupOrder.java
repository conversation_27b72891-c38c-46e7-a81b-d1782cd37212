package OCL.ATS;



import Request.ats.CreateOrderPackForPickUp;
import Services.ATS.ATSServices;
import com.goldengate.common.ATSUtilityJWTGenerator;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class CreateWhToPackPickupOrder extends BaseMethod
{
    ATSServices ATSServicesObj = new ATSServices();
    ATSUtilityJWTGenerator jwtSTS = new ATSUtilityJWTGenerator();
    private static final Logger LOGGER = LogManager.getLogger(CreateWhToPackPickupOrder.class);


    public static String MobileNumber = "9891497839";
//    public static String Token = "PBXm3VM9RkDHkPJHguYsLQNDrZaDDvvWuKgHGcw61OQwApKeGsdZoDHGNv2JrlLpTlBCsOp%2FE1UwSidl7b4YDg%3D%3D";
//    public static String Token = "h%2FSuh7EGLQBbQRDrCBL4OU9YPWcfjpG11WwXsVwZ8YsPlZWwnEOlsf0W9SA4bW3itecXyiIOGFZnYNTuS%2B%2BdyA%3D%3D";
    public  String Token=jwtSTS.generateJwtToken_STS();
    public static String mid = "pvufVk98108581129485";



    @Test(priority = 0, description = "Create pack for pickup order", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);


        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "True");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
       String orderID= respObj.jsonPath().getString("data.orderId");
     System.out.print("Order ID is : "+orderID);
        Assert.assertEquals(StatusCode, 200);
        respObj.prettyPrint();
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Success");
//        Assert.assertEquals(respObj.jsonPath().getString("data.orderCreated"),"true");
    }

    @Test(priority = 0, description = "Create pack for pickup order with empty processName", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);


        Map<String, String> body = new HashMap<>();
        body.put("processName", " ");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "False");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultStatus"),"F");
        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultCode"),"BUSINESS_EXCEPTION");
        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Invalid values for No enum constant com.paytm.enums.FileProcessEnum. . Please enter correct values and retry");
    }

    @Test(priority = 0, description = "Create pack for pickup order with invalid processName", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP@");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "False");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultStatus"),"F");
        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultCode"),"BUSINESS_EXCEPTION");
        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Invalid values for No enum constant com.paytm.enums.FileProcessEnum.PACK_PICKUP@. Please enter correct values and retry");
    }

    @Test(priority = 0, description = "Create pack for pickup order with empty sourceEntityCode", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "False");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultStatus"),"F");
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultCode"),"BUSINESS_EXCEPTION");
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Invalid values for Cannot invoke \"com.paytm.entity.PhysicalEntityMaster.getId()\" because \"srcEntity\" is null. Please enter correct values and retry");
    }

    @Test(priority = 0, description = "Create pack for pickup order with invalid sourceEntityCode", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1@");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "False");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultStatus"),"F");
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultCode"),"BUSINESS_EXCEPTION");
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Invalid values for Cannot invoke \"com.paytm.entity.PhysicalEntityMaster.getId()\" because \"srcEntity\" is null. Please enter correct values and retry");
    }

    @Test(priority = 0, description = "Create pack for pickup order with empty destinationEntityCode", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "False");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultStatus"),"F");
        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultCode"),"BUSINESS_EXCEPTION");
        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Invalid values for Pickup Entity Code. Please enter correct values and retry");
    }

    @Test(priority = 0, description = "Create pack for pickup order with invalid destinationEntityCode", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);


        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002@");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "False");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultStatus"),"F");
        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultCode"),"BUSINESS_EXCEPTION");
        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Invalid values for Pickup Entity Code. Please enter correct values and retry");
    }

    @Test(priority = 0, description = "Create pack for pickup order with empty skuid", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "False");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Success");
//        Assert.assertEquals(respObj.jsonPath().getString("data.orderCreated"),"true");
    }

    @Test(priority = 0, description = "Create pack for pickup order with invalid skuid", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046@");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "False");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("status"),"400");
        Assert.assertEquals(respObj.jsonPath().getString("title"),"Bad Request");
        Assert.assertEquals(respObj.jsonPath().getString("detail"),"Failed to read request");
    }
    @Test(priority = 0, description = "Create pack for pickup order with empty quantity", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "False");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Success");
//        Assert.assertEquals(respObj.jsonPath().getString("data.orderCreated"),"true");
    }

    @Test(priority = 0, description = "Create pack for pickup order with invalid quantity", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "1@");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "False");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("status"),"400");
        Assert.assertEquals(respObj.jsonPath().getString("title"),"Bad Request");
        Assert.assertEquals(respObj.jsonPath().getString("detail"),"Failed to read request");
    }
    @Test(priority = 0, description = "Create pack for pickup order with zero quantity", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "0");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "False");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Success");
//        Assert.assertEquals(respObj.jsonPath().getString("data.orderCreated"),"true");
    }

    @Test(priority = 0, description = "Create pack for pickup order with empty externalBucket", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "");
        body.put("priority", "P1");
        body.put("isHold", "False");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Success");
//        Assert.assertEquals(respObj.jsonPath().getString("data.orderCreated"),"true");;
    }

    @Test(priority = 0, description = "Create pack for pickup order with invalid externalBucket", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_14() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "New@");
        body.put("priority", "P1");
        body.put("isHold", "False");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Success");
//        Assert.assertEquals(respObj.jsonPath().getString("data.orderCreated"),"true");
    }
    @Test(priority = 0, description = "Create pack for pickup order with empty priority", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_15() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "");
        body.put("isHold", "False");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Success");
//        Assert.assertEquals(respObj.jsonPath().getString("data.orderCreated"),"true");;
    }

    @Test(priority = 0, description = "Create pack for pickup order with invalid priority", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_16() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "P1@");
        body.put("isHold", "False");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Success");
//        Assert.assertEquals(respObj.jsonPath().getString("data.orderCreated"),"true");
    }
    @Test(priority = 0, description = "Create pack for pickup order with empty isHold", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_17() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Success");
//        Assert.assertEquals(respObj.jsonPath().getString("data.orderCreated"),"true");;
    }

    @Test(priority = 0, description = "Create pack for pickup order with invalid isHold", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_18() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "False@");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Success");
//        Assert.assertEquals(respObj.jsonPath().getString("data.orderCreated"),"true");
    }
    @Test(priority = 0, description = "Create pack for pickup order with empty businessCategory", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_19() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "False");
        body.put("businessCategory", "");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Success");
//        Assert.assertEquals(respObj.jsonPath().getString("data.orderCreated"),"true");;
    }

    @Test(priority = 0, description = "Create pack for pickup order with invalid businessCategory", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_20() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "False");
        body.put("businessCategory", "28@");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Success");
//        Assert.assertEquals(respObj.jsonPath().getString("data.orderCreated"),"true");
    }

    @Test(priority = 0, description = "Create pack for pickup order with false isHold", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_21() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "False");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Success");
//        Assert.assertEquals(respObj.jsonPath().getString("data.orderCreated"),"true");;
    }

    @Test(priority = 0, description = "Create pack for pickup order with true isHold", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_22() throws Exception {
        CreateOrderPackForPickUp CreateOrderPackForPickUpObj = new CreateOrderPackForPickUp(P.TESTDATA.get("CreatePackPickupOrderRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
//        headers.put("X-MW-TOKEN-EX", Token);
        headers.put("jwt", Token);

        Map<String, String> body = new HashMap<>();
        body.put("processName", "PACK_PICKUP");
        body.put("sourceEntityCode", "ROUTECHFC1");
        body.put("destinationEntityCode", "ocl-002");
        body.put("skuId", "100046");
        body.put("quantity", "1");
        body.put("externalBucket", "New");
        body.put("priority", "P1");
        body.put("isHold", "True");
        body.put("businessCategory", "28");

        Response respObj = ATSServices.createPackForPickupOrder(CreateOrderPackForPickUpObj, headers, body);
        respObj.prettyPrint();
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
//        Assert.assertEquals(respObj.jsonPath().getString("resultInfo.resultMsg"),"Success");
//        Assert.assertEquals(respObj.jsonPath().getString("data.orderCreated"),"true");;
    }

}
