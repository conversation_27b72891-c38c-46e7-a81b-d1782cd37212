package OCL.Subscription;

import Services.Subscription.SubscriptionPlan;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class SubscriptionPlanOnboarding extends BaseMethod {

	
    private static final Logger LOGGER = LogManager.getLogger(SubscriptionPlanOnboarding.class);
    public static String clientid= "oe-subscription-client";
    public static String clientsecret="E7MYmjY7sZNONEGtcPbM5lYjk2sBG+RSsyJVeMqF8DQMd+ToQSJ3tU4ii3Sz/YD0/L6+CG4SoWccRgcuaKiasQ==";
    public static String mid = "DcFcoJ97348642540712";
    public static String usna910= "MSPRC193MLQ";
    public static String usna910monthly= "MSJRC123FPQ";

    public static String usndx8000= "BNJSL172TPQ";
    public static String usndx8000monthly= "REPL433245476";
    public static String usna50= "MSALN356TPQ";
    public static String usnex6000lifetime= "KAJCP260LRN";
    public static String usnex6000supersaver= "KAJCP262LRN";
    public static String usnDX8000lifetime= "DBJCP262LRN";
    public static String usndx8000supersaver= "DBJCP269LRN";
    
    public static String usnA910lifetime= "MBDCP281LRN";
    public static String usnA910supersaver= "MBNRP271LRN";
    
    public static String usnA50lifetime= "MBCRP265LRN";
    public static String usnA50supersaver= "MVCRP278LRN";
    
    public static String usnNEXGOlifetime= "MVKAP278LRN";
    public static String usnNEXGOSUPERSAVER= "MBTAP178LRN";


    public static String usna50monthly= "MQJRB173MRN";
    
    public static String usnnexgo= "CQJRB173LRN";
    public static String usnnexgomonthly= "RQJRB178LRN";
    
    public static String usng2= "SAJRB178LRN";
    public static String usng2monthly= "KAJRB178LRN";
    

    public static String usnex6000= "KAJRP278LRN";
    public static String usnex6000monthly= "KAJCP259LRN";
    


    public static String yearlyplan="Yearly ";
    public static String Monthly="Monthly ";
    public static String supersavergold="Super Saver Gold";
    public static String lifetimeplangold="Lifetime Plan Gold";
    public static String startDate="2022-06-23";
    public static String endDate="2022-06-24";
    public static float amount=(float) 1.0;
    public static float percentage=(float) 0.0;
    public static String startDatesecond="2022-06-25";
    public static String endDatesecond="9999-06-23";
    public static float amountsecond=(float) 0.0;
    public static float percentagesecond=(float) 100.0;
    public static String simCharge="simCharge";
    public static String labelSim="Sim Charge";
    public static String installationCharge="installationCharge";
    public static String labelInstallation="Installation Charge";
    public static float amountInstallation=(float) 1.18;




    public static String supersaverplanInActive="Super Saver Gold";
    public static String SerialNumber="MSJRC193MLQ";
    public static String a910="A910";
    public static String A50="A50";
    public static String NEXGOG2PLUS="NEXGOG2PLUS";
    public static String G2="G2";

    public static String DX8000="DX8000";
    public static String EX6000="EX6000";

    
    @Test(priority = 1,description = "Yearly Plan Onboarding Subscription A910 for a merchant")
    public void YearlySubscriptionPlanOnboardingA910()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usna910);
        body.put("planName", yearlyplan);
        body.put("deviceName", a910);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    } 
    
    

    
    @Test(priority = 1,description = "Yearly Plan Onboarding Subscription A50 for a merchant")
    public void YearlySubscriptionPlanOnboardingA50()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usna50);
        body.put("planName", yearlyplan);
        body.put("deviceName", A50);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    } 

    

    
    
    @Test(priority = 1,description = "Yearly Plan Onboarding Subscription DX8000 for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void YearlySubscriptionPlanOnboardingDX8000()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usndx8000);
        body.put("planName", yearlyplan);
        body.put("deviceName", DX8000);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    
    
   

    @Test(priority = 1,description = "Yearly Plan Onboarding Subscription NEXGOG2PLUS for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void YearlySubscriptionPlanOnboardingNEXGOG2PLUS()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usnnexgo);
        body.put("planName", yearlyplan);
        body.put("deviceName", NEXGOG2PLUS);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    

    @Test(priority = 1,description = "Yearly Plan Onboarding Subscription g2 for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void YearlySubscriptionPlanOnboardingg2()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usng2);
        body.put("planName", yearlyplan);
        body.put("deviceName", G2);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    


    @Test(priority = 1,description = "Yearly Plan Onboarding Subscription ex6000 for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void YearlySubscriptionPlanOnboardingEX6000()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usnex6000);
        body.put("planName", yearlyplan);
        body.put("deviceName", EX6000);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    
    

    @Test(priority = 1,description = "Lifetime Plan Onboarding Subscription ex6000 for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void LifetimeSubscriptionPlanOnboardingEX6000()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usnex6000lifetime);
        body.put("planName", lifetimeplangold);
        body.put("deviceName", EX6000);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    

    @Test(priority = 1,description = "Supersaver Onboarding Subscription ex6000 for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void SupersaverSubscriptionPlanOnboardingEX6000()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usnex6000supersaver);
        body.put("planName", supersavergold);
        body.put("deviceName", EX6000);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    
    

    @Test(priority = 1,description = "Lifetime Plan Onboarding Subscription A910 for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void LifetimeSubscriptionPlanOnboardingA910()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usnA910lifetime);
        body.put("planName", lifetimeplangold);
        body.put("deviceName", a910);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    

    @Test(priority = 1,description = "Supersaver Onboarding Subscription A910 for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void SupersaverSubscriptionPlanOnboardingA910()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usnA910supersaver);
        body.put("planName", supersavergold);
        body.put("deviceName", a910);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    
    

    @Test(priority = 1,description = "Lifetime Plan Onboarding Subscription NEXGO for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void LifetimeSubscriptionPlanOnboardingNEXGO()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usnNEXGOlifetime);
        body.put("planName", lifetimeplangold);
        body.put("deviceName", NEXGOG2PLUS);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    

    @Test(priority = 1,description = "Supersaver Onboarding Subscription NEXGO for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void SupersaverSubscriptionPlanOnboardingNEXGOG2PLUS()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usnNEXGOSUPERSAVER);
        body.put("planName", supersavergold);
        body.put("deviceName", NEXGOG2PLUS);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    



    @Test(priority = 1,description = "Lifetime Plan Onboarding Subscription A50 for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void LifetimeSubscriptionPlanOnboardingA50()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usnA50lifetime);
        body.put("planName", lifetimeplangold);
        body.put("deviceName", A50);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    

    @Test(priority = 1,description = "Supersaver Onboarding Subscription A50 for a merchant")
    public void SupersaverSubscriptionPlanOnboardingA50()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usnA50supersaver);
        body.put("planName", supersavergold);
        body.put("deviceName", A50);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    
    
    
    
    @Test(priority = 1,description = "Lifetime Plan Onboarding Subscription DX8000 for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void LifetimeSubscriptionPlanOnboardingDX8000()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usnDX8000lifetime);
        body.put("planName", lifetimeplangold);
        body.put("deviceName", DX8000);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    

    @Test(priority = 1,description = "Supersaver Onboarding Subscription DX8000 for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void SupersaverSubscriptionPlanOnboardingDX8000()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usndx8000supersaver);
        body.put("planName", supersavergold);
        body.put("deviceName", DX8000);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    
    
    

    @Test(priority = 1,description = "Monthly Plan Onboarding Subscription EX6000 for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void monthlySubscriptionPlanOnboardingEX6000()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usnex6000monthly);
        body.put("planName", Monthly);
        body.put("deviceName", EX6000);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    
    


    @Test(priority = 1,description = "Monthly Plan Onboarding Subscription NEXGOG2PLUS for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void monthlySubscriptionPlanOnboardingNEXGOG2PLUS()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usnnexgomonthly);
        body.put("planName", Monthly);
        body.put("deviceName", NEXGOG2PLUS);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    
    
    

    @Test(priority = 1,description = "monthly Plan Onboarding Subscription g2 for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void monthlySubscriptionPlanOnboardingg2()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usng2monthly);
        body.put("planName", Monthly);
        body.put("deviceName", G2);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

//
//
    } 
    

   
    @Test(priority = 1,description = "Monthly Plan Onboarding Subscription A910 for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void monthlySubscriptionPlanOnboardingA910()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usna910monthly);
        body.put("planName", Monthly);
        body.put("deviceName", a910);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    } 
    

    @Test(priority = 1,description = "Monthly Plan Onboarding Subscription DX8000 for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void monthlySubscriptionPlanOnboardingDX8000()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usndx8000monthly);
        body.put("planName", Monthly);
        body.put("deviceName", DX8000);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].deviceName"),planName);
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planName"),yearlyplanActive);
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].status"),status);
//
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].startDate"),"2022-06-23");
//        float expectedamount=(float) 1.00;
//        float expectedpercentage=(float) 0.0;
//        float expectedamountnew=(float) 0.0;
//        float expectedpercentagenew=(float) 100.0;
//        float expectedamountInstallation=(float) 1.18;
//
//        //
//
//
//        float actualamount=subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].amount");
//        float actualpercentage=subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].percentage");
//        float actualamountnew=subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].amount");
//        float actualpercentagenew=subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].percentage");
//        float actualamountsim=subfetchResponse.jsonPath().get("results[0].planOtherCharges[0].amount");
//        float actualamountInstallation=subfetchResponse.jsonPath().get("results[0].planOtherCharges[1].amount");
//
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].endDate"),"2022-06-24");
//        Assert.assertEquals(actualamount,expectedamount);
//        Assert.assertEquals(actualpercentage,expectedpercentage);
//
//
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].startDate"),"2022-06-25");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].endDate"),"9999-12-31");
//        Assert.assertEquals(actualpercentagenew,expectedpercentagenew);
//
//        Assert.assertEquals(actualamountnew,expectedamountnew);
//
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planOtherCharges[0].chargeType"),"simCharge");
//        Assert.assertEquals(actualamountsim,expectedpercentage);
//
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planOtherCharges[0].label"),"Sim Charge");
//
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planOtherCharges[1].chargeType"),"installationCharge");
//        Assert.assertEquals(actualamountInstallation,expectedamountInstallation);
//
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planOtherCharges[1].label"),"Installation Charge");
//
//
    } 
    
    


    @Test(priority = 1,description = "Monthly Plan Onboarding Subscription a50 for a merchant", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void monthlySubscriptionPlanOnboardingA50()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<Object, Object> headers = new HashMap<Object, Object>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<Object,Object> body=new HashMap<Object,Object>();
        
        body.put("mid", mid);
        body.put("usn", usna50monthly);
        body.put("planName", Monthly);
        body.put("deviceName", A50);
        body.put("allowOverwrite", true);
        
        
        body.put("startDate", startDate);
        body.put("endDate", endDate);
        body.put("amount", amount);
        body.put("percentage", percentage);
        body.put("startDate", startDatesecond);
        body.put("endDate", endDatesecond);
        body.put("amount", amountsecond);
        body.put("percentage", percentagesecond);


        body.put("chargeType", simCharge);
        body.put("label", labelSim);
        body.put("amount", amount);
        body.put("chargeType", installationCharge);
        body.put("label", labelInstallation);
        body.put("amount", amountInstallation);



               SubscriptionPlan subonboard =new SubscriptionPlan();
        Response subOnboardResponse= subonboard.subscriptionOnboardPlan(headers, body);
        subOnboardResponse.prettyPrint();
        int httpcode = subOnboardResponse.getStatusCode();

        Assert.assertTrue(httpcode == 200, "Testcase Failed");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].deviceName"),planName);
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planName"),yearlyplanActive);
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].status"),status);
//
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].startDate"),"2022-06-23");
//        float expectedamount=(float) 1.00;
//        float expectedpercentage=(float) 0.0;
//        float expectedamountnew=(float) 0.0;
//        float expectedpercentagenew=(float) 100.0;
//        float expectedamountInstallation=(float) 1.18;
//
//        //
//
//
//        float actualamount=subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].amount");
//        float actualpercentage=subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].percentage");
//        float actualamountnew=subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].amount");
//        float actualpercentagenew=subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].percentage");
//        float actualamountsim=subfetchResponse.jsonPath().get("results[0].planOtherCharges[0].amount");
//        float actualamountInstallation=subfetchResponse.jsonPath().get("results[0].planOtherCharges[1].amount");
//
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].endDate"),"2022-06-24");
//        Assert.assertEquals(actualamount,expectedamount);
//        Assert.assertEquals(actualpercentage,expectedpercentage);
//
//
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].startDate"),"2022-06-25");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].endDate"),"9999-12-31");
//        Assert.assertEquals(actualpercentagenew,expectedpercentagenew);
//
//        Assert.assertEquals(actualamountnew,expectedamountnew);
//
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planOtherCharges[0].chargeType"),"simCharge");
//        Assert.assertEquals(actualamountsim,expectedpercentage);
//
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planOtherCharges[0].label"),"Sim Charge");
//
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planOtherCharges[1].chargeType"),"installationCharge");
//        Assert.assertEquals(actualamountInstallation,expectedamountInstallation);
//
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planOtherCharges[1].label"),"Installation Charge");
//
//
    } 
    
    
    //    
//    
//    @Test(priority = 1,description = "Fetching Plan Subscription for a merhant where terminal is INACTIVE")
//    public void FetchSubscriptionPlanTerminalInActive()
//    {
//
//    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("x-client-token", jwttoken);
//        headers.put("x-client-id", clientid);
//        headers.put("accept", "*/*");
//        headers.put("Accept-Language", "en");
//        headers.put("Content-Type", "application/json");
//        
//        Map<String,String> params=new HashMap<String,String>();
//        params.put("mid", mid);
//        params.put("usn", usn);
//        params.put("status", inactivestatus);
//        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
//        params.put("isOtherChargesRequired", isOtherChargesRequired);
//        
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("mid", mid);
//        body.put("usn", usn);
//        body.put("status", inactivestatus);
//
//        
//        
//        SubscriptionPlan subfetch =new SubscriptionPlan();
//        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
//        subfetchResponse.prettyPrint();
//        int httpcode = subfetchResponse.getStatusCode();
//      
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].deviceName"),planName);
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planName"),supersaverplanInActive);
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].status"),inactivestatus);
//
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].startDate"),"2022-06-23");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].endDate"),"2022-06-24");
//
//        float expectedamount=(float) 1.00;
//        float expectedpercentage=(float) 0.0;
//        float expectedamountnew=(float) 0.0;
//        float expectedpercentagenew=(float) 100.0;
//
//
//
//        float actualamount=subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].amount");
//        float actualpercentage=subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].percentage");
//        float actualamountnew=subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].amount");
//        float actualpercentagenew=subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].percentage");
//
//        Assert.assertEquals(actualamount,expectedamount);
//        Assert.assertEquals(actualpercentage,expectedpercentage);
//
//
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].startDate"),"2022-06-25");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].endDate"),"9999-12-31");
//        Assert.assertEquals(actualpercentagenew,expectedpercentagenew);
//
//        Assert.assertEquals(actualamountnew,expectedamountnew);
//
//
//    } 
//    
//    
//    @Test(priority = 1,description = "Fetching Plan Subscription for a merhant without refund")
//    public void FetchSubscriptionPlanWithoutRefund()
//    {
//
//    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("x-client-token", jwttoken);
//        headers.put("x-client-id", clientid);
//        headers.put("accept", "*/*");
//        headers.put("Accept-Language", "en");
//        headers.put("Content-Type", "application/json");
//        
//        Map<String,String> params=new HashMap<String,String>();
//        params.put("mid", mid);
//        params.put("usn", usn);
//        params.put("status", status);
//        
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("mid", mid);
//        body.put("usn", usn);
//        body.put("status", status);
//
//        
//        
//        SubscriptionPlan subfetch =new SubscriptionPlan();
//        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
//        subfetchResponse.prettyPrint();
//        int httpcode = subfetchResponse.getStatusCode();
//      
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].deviceName"),planName);
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planName"),yearlyplanActive);
//        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].status"),status);
//
//        
//
//    } 
//    
//
//    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant By not providing mid in the params")
//    public void NegativeFetchSubscriptionPlanNoMID()
//    {
//
//    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("x-client-token", jwttoken);
//        headers.put("x-client-id", clientid);
//        headers.put("accept", "*/*");
//        headers.put("Accept-Language", "en");
//        headers.put("Content-Type", "application/json");
//        
//        Map<String,String> params=new HashMap<String,String>();
////        params.put("mid", mid);
//        params.put("usn", usn);
//        params.put("status", inactivestatus);
//        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
//        params.put("isOtherChargesRequired", isOtherChargesRequired);
//        
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("mid", mid);
//        body.put("usn", usn);
//        body.put("status", inactivestatus);
//
//        
//        SubscriptionPlan subfetch =new SubscriptionPlan();
//        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
//        subfetchResponse.prettyPrint();
//        int httpcode = subfetchResponse.getStatusCode();
//      
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"400");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Required String parameter 'mid' is not present");
//         Assert.assertEquals(subfetchResponse.jsonPath().get("errorCode"),"SUP-007");
//
//    } 
//
//
//    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant By not providing USN in the params")
//    public void NegativeFetchSubscriptionPlanNoUSN()
//    {
//
//    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("x-client-token", jwttoken);
//        headers.put("x-client-id", clientid);
//        headers.put("accept", "*/*");
//        headers.put("Accept-Language", "en");
//        headers.put("Content-Type", "application/json");
//        
//        Map<String,String> params=new HashMap<String,String>();
//        params.put("mid", mid);
////        params.put("usn", usn);
//        params.put("status", inactivestatus);
//        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
//        params.put("isOtherChargesRequired", isOtherChargesRequired);
//        
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("mid", mid);
//        body.put("usn", usn);
//        body.put("status", inactivestatus);
//
//        
//        SubscriptionPlan subfetch =new SubscriptionPlan();
//        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
//        subfetchResponse.prettyPrint();
//        int httpcode = subfetchResponse.getStatusCode();
//      
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"400");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Required String parameter 'usn' is not present");
//         Assert.assertEquals(subfetchResponse.jsonPath().get("errorCode"),"SUP-007");
//
//    } 
//    
//
//    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant By not providing Status in the params")
//    public void NegativeFetchSubscriptionPlanNoSTATUS()
//    {
//
//    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("x-client-token", jwttoken);
//        headers.put("x-client-id", clientid);
//        headers.put("accept", "*/*");
//        headers.put("Accept-Language", "en");
//        headers.put("Content-Type", "application/json");
//        
//        Map<String,String> params=new HashMap<String,String>();
//        params.put("mid", mid);
//        params.put("usn", usn);
////        params.put("status", inactivestatus);
//        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
//        params.put("isOtherChargesRequired", isOtherChargesRequired);
//        
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("mid", mid);
//        body.put("usn", usn);
//        body.put("status", inactivestatus);
//
//        
//        SubscriptionPlan subfetch =new SubscriptionPlan();
//        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
//        subfetchResponse.prettyPrint();
//        int httpcode = subfetchResponse.getStatusCode();
//      
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"400");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Required String parameter 'status' is not present");
//         Assert.assertEquals(subfetchResponse.jsonPath().get("errorCode"),"SUP-007");
//
//    } 
//    
//
//    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant By providing invalid Status in the params")
//    public void NegativeFetchSubscriptionPlaninvalidstatus()
//    {
//
//    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("x-client-token", jwttoken);
//        headers.put("x-client-id", clientid);
//        headers.put("accept", "*/*");
//        headers.put("Accept-Language", "en");
//        headers.put("Content-Type", "application/json");
//        
//        Map<String,String> params=new HashMap<String,String>();
//        params.put("mid", mid);
//        params.put("usn", usn);
//        params.put("status", "ACTIVEINACTIVE");
//        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
//        params.put("isOtherChargesRequired", isOtherChargesRequired);
//        
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("mid", mid);
//        body.put("usn", usn);
//        body.put("status", inactivestatus);
//
//        
//        SubscriptionPlan subfetch =new SubscriptionPlan();
//        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
//        subfetchResponse.prettyPrint();
//        int httpcode = subfetchResponse.getStatusCode();
//      
//        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"400");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"status can be only ACTIVE|INACTIVE");
//
//    } 
//    
//
//    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant By providing other VALID USN not linked with the provided mid in the params")
//    public void NegativeFetchSubscriptionPlan()
//    {
//
//    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("x-client-token", jwttoken);
//        headers.put("x-client-id", clientid);
//        headers.put("accept", "*/*");
//        headers.put("Accept-Language", "en");
//        headers.put("Content-Type", "application/json");
//        
//        Map<String,String> params=new HashMap<String,String>();
//        params.put("mid", mid);
//        params.put("usn", "MSJLN873RCQ");
//        params.put("status", "ACTIVE");
//        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
//        params.put("isOtherChargesRequired", isOtherChargesRequired);
//        
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("mid", mid);
//        body.put("usn", usn);
//        body.put("status", inactivestatus);
//
//        
//        SubscriptionPlan subfetch =new SubscriptionPlan();
//        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
//        subfetchResponse.prettyPrint();
//        int httpcode = subfetchResponse.getStatusCode();
//      
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"204");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"No data found.");
//
//    } 
//    
//
//
//    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant with invalid xclientId")
//    public void NegativeFetchSubscriptionPlanInvalidAuthxclientId()
//    {
//
//    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("x-client-token", jwttoken);
//        headers.put("x-client-id", "oe");
//        headers.put("accept", "*/*");
//        headers.put("Accept-Language", "en");
//        headers.put("Content-Type", "application/json");
//        
//        Map<String,String> params=new HashMap<String,String>();
//        params.put("mid", mid);
//        params.put("usn", "MSJLN873RCQ");
//        params.put("status", "ACTIVE");
//        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
//        params.put("isOtherChargesRequired", isOtherChargesRequired);
//        
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("mid", mid);
//        body.put("usn", usn);
//        body.put("status", inactivestatus);
//
//        
//        SubscriptionPlan subfetch =new SubscriptionPlan();
//        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
//        subfetchResponse.prettyPrint();
//        int httpcode = subfetchResponse.getStatusCode();
//      
//        Assert.assertTrue(httpcode == 401, "Testcase Failed");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"401");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Invalid client id/token");
//
//    } 
//    
//
//    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant with invalid xclienttoken")
//    public void NegativeFetchSubscriptionPlanInvalidAuthxclienttoken()
//    {
//
//    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("x-client-token", "test");
//        headers.put("x-client-id", clientid);
//        headers.put("accept", "*/*");
//        headers.put("Accept-Language", "en");
//        headers.put("Content-Type", "application/json");
//        
//        Map<String,String> params=new HashMap<String,String>();
//        params.put("mid", mid);
//        params.put("usn", "MSJLN873RCQ");
//        params.put("status", "ACTIVE");
//        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
//        params.put("isOtherChargesRequired", isOtherChargesRequired);
//        
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("mid", mid);
//        body.put("usn", usn);
//        body.put("status", inactivestatus);
//
//        
//        SubscriptionPlan subfetch =new SubscriptionPlan();
//        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
//        subfetchResponse.prettyPrint();
//        int httpcode = subfetchResponse.getStatusCode();
//      
//        Assert.assertTrue(httpcode == 401, "Testcase Failed");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"401");
//        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Malformed JWT");
//
  //  } 
//    
    

}