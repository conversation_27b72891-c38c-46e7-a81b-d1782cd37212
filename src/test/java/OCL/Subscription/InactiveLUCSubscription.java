package OCL.Subscription;

import Request.Subscription.CreateSubscription;
import Request.Subscription.SubscriptionStatus;
import Services.MechantService.MiddlewareServices;
import Services.Subscription.SubscriptionPlan;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class InactiveLUCSubscription extends BaseMethod
{
    SubscriptionPlan SubscriptionPlanObj = new SubscriptionPlan();
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(InactiveLUCSubscription.class);

    public static String custId = "1701064376";
    public static String MID = "dievrQ41934892349254";
    public static String Mobile_Number = "5567123093";
    public static String jwt = "";
    public static String clientId = "oe-subscription-client";
    public static String clientsecret = "E7MYmjY7sZNONEGtcPbM5lYjk2sBG+RSsyJVeMqF8DQMd+ToQSJ3tU4ii3Sz/YD0/L6+CG4SoWccRgcuaKiasQ==";


    @Test(priority = 0, groups = {"Regression"}, description = "Generate US")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_GenerateJwtToken() {

        jwt = createAuth0JwsHMAC(clientId, clientsecret);
        LOGGER.info("Token is : " + jwt);


    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_CreateSubscription() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "TXN_COUNT");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Inactive Luc Subscription with invalid status")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_InactiveLUCSubscriptionWithInvalidStatus() {

        SubscriptionStatus SubscriptionStatusObj = new SubscriptionStatus(P.TESTDATA.get("SubscriptionStatusRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", MID);
        body.put("status", "INACTIVE1");
        body.put("serviceName","EDC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionStatus(SubscriptionStatusObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Status can be INACTIVE or SUSPEND or RESUME"));
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Inactive Luc Subscription with empty status")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_InactiveLUCSubscriptionWithEmptyStatus() {

        SubscriptionStatus SubscriptionStatusObj = new SubscriptionStatus(P.TESTDATA.get("SubscriptionStatusRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", MID);
        body.put("status", " ");
        body.put("serviceName","EDC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionStatus(SubscriptionStatusObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("status cannot be blank"));
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Inactive Luc Subscription with invalid USN ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_InactiveLUCSubscriptionWithInvalidUSN() {

        SubscriptionStatus SubscriptionStatusObj = new SubscriptionStatus(P.TESTDATA.get("SubscriptionStatusRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", "abc");
        body.put("status", "INACTIVE");
        body.put("serviceName","EDC1");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionStatus(SubscriptionStatusObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("subscriptions does not exist"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Inactive Luc Subscription with empty USN ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_InactiveLUCSubscriptionWithEmptyUSN() {

        SubscriptionStatus SubscriptionStatusObj = new SubscriptionStatus(P.TESTDATA.get("SubscriptionStatusRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", " ");
        body.put("status", "INACTIVE");
        body.put("serviceName"," ");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionStatus(SubscriptionStatusObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Inactive Luc Subscription with empty mid")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_InactiveLUCSubscriptionWithEmptyMid() {

        SubscriptionStatus SubscriptionStatusObj = new SubscriptionStatus(P.TESTDATA.get("SubscriptionStatusRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", " ");
        body.put("usn", MID);
        body.put("status", "INACTIVE");
        body.put("serviceName","EDC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionStatus(SubscriptionStatusObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Inactive Luc Subscription with invalid mid")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_InactiveLUCSubscriptionWithInvalidMid() {

        SubscriptionStatus SubscriptionStatusObj = new SubscriptionStatus(P.TESTDATA.get("SubscriptionStatusRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", "abc");
        body.put("usn", MID);
        body.put("status", "INACTIVE");
        body.put("serviceName","EDC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionStatus(SubscriptionStatusObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("subscriptions does not exist"));
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Inactive Luc Subscription")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_InactiveLUCSubscription() {

        SubscriptionStatus SubscriptionStatusObj = new SubscriptionStatus(P.TESTDATA.get("SubscriptionStatusRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", MID);
        body.put("status", "INACTIVE");
        body.put("serviceName","EDC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionStatus(SubscriptionStatusObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("results").contains("User Subscriptions Updated Successfully"));
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Inactive Luc Subscription when luc is already inactive")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_InactiveLUCSubscriptionWhenLUCAlreadyInactive() {

        SubscriptionStatus SubscriptionStatusObj = new SubscriptionStatus(P.TESTDATA.get("SubscriptionStatusRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", MID);
        body.put("status", "INACTIVE");
        body.put("serviceName", "EDC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionStatus(SubscriptionStatusObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Invalid Operation on the existing state of one or more subscriptions"));
        Assert.assertEquals(StatusCode, 200);

    }
}
