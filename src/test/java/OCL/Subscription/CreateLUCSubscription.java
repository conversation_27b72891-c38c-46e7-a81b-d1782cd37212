package OCL.Subscription;

import Request.Subscription.CreateSubscription;
import Request.Subscription.SubscriptionStatus;
import Services.MechantService.MiddlewareServices;
import Services.Subscription.SubscriptionPlan;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class CreateLUCSubscription extends BaseMethod {
    SubscriptionPlan SubscriptionPlanObj = new SubscriptionPlan();
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(CreateLUCSubscription.class);

    public static String custId = "1002305549";
    public static String MID = "cvqaRF11652124862409";
    public static String Mobile_Number = "7770008821";
    public static String USN = "";
    public static String jwt = "";
    public static String clientId = "oe-subscription-client";
    public static String clientsecret = "E7MYmjY7sZNONEGtcPbM5lYjk2sBG+RSsyJVeMqF8DQMd+ToQSJ3tU4ii3Sz/YD0/L6+CG4SoWccRgcuaKiasQ==";


    @Test(priority = 0, groups = {"Regression"}, description = "Generate US")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_GenerateUSN() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOT" + SerialNo;
        LOGGER.info("USN is :" + USN);
        jwt = createAuth0JwsHMAC(clientId, clientsecret);
        LOGGER.info("Token is : " + jwt);


    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create duplicate Luc Subscription")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_CreateDuplicateSubscription() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "TXN_COUNT");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("This usn is already taken by another MID for the given Service"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with invalid service type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_CreateLUCSubscriptionWithInvalidServiceType() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "TXN_COUNT");
        body.put("serviceType", "Android1");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", "2023-11-07");
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Rule does not exist for given service, service type"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with invalid service name")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_CreateLUCSubscriptionWithInvalidServiceName() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "TXN_COUNT");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC1");
        body.put("frequency", "1M");
        body.put("deductionStartDate", "2023-11-07");
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Rule does not exist for given service, service type"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with invalid frequency")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_CreateLUCSubscriptionWithInvalidFrequency() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "TXN_COUNT");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "12M");
        body.put("deductionStartDate", "2023-11-07");
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Invalid Conditional Frequency value or format"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with past deduction start date")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_CreateLUCSubscriptionWithPastDeductionStartDate() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "TXN_COUNT");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", "2023-11-02");
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Deduction start date should not be in the past"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with invalid conditional parameter")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_CreateLUCSubscriptionWithInvalidConditionalParameter() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "TXN_COUNT1");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(20).toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Invalid conditional parameter"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with empty conditional parameter")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_CreateLUCSubscriptionWithEmptyConditionalParameter() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(20).toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Invalid conditional parameter"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with empty service name")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_CreateLUCSubscriptionWithEmptyServiceName() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "GMV");
        body.put("serviceType", "Android");
        body.put("serviceName", "");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(20).toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Service cannot be blank"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with empty service type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_CreateLUCSubscriptionWithEmptyServiceType() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "GMV");
        body.put("serviceType", "");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(20).toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Service type cannot be blank"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with empty frequency ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11_CreateLUCSubscriptionWithEmptyFrequency() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "GMV");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", " ");
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(20).toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with invalid USN ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12_CreateLUCSubscriptionWithInvalidUSN() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID + "1");
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "GMV");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(20).toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Invalid character length for EDC usn"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with empty USN ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13_CreateLUCSubscriptionWithEmptyUSN() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", " ");
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "GMV");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(20).toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with empty subscription type ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_14_CreateLUCSubscriptionWithEmptySubscriptionType() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", " ");
        body.put("conditionalParameter", "GMV");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(20).toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with invalid subscription type ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_15_CreateLUCSubscriptionWithInvalidSubscriptionType() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", " Rental1");
        body.put("conditionalParameter", "GMV");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(20).toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with invalid mid ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_16_CreateLUCSubscriptionWithInvalidMid() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID + "1");
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "GMV");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(20).toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("This usn is already taken by another MID for the given Service"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with empty mid  ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_17_CreateLUCSubscriptionWithEmptyMid() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", " ");
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "GMV");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(20).toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with empty onboarding date  ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_18_CreateLUCSubscriptionWithEmptyOnboardingDate() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "GMV");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(20).toString());
        body.put("onboardingDate", " ");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Onboarding date cannot be blank"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Luc Subscription with empty deduction date  ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_19_CreateLUCSubscriptionWithEmptyDeductionDate() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "GMV");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", " ");
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }


    @Test(priority = 1, groups = {"Regression"}, description = "Inactive luc subscription ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_20_InactiveLUCSubscription() {

        SubscriptionStatus SubscriptionStatusObj = new SubscriptionStatus(P.TESTDATA.get("SubscriptionStatusRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", MID);
        body.put("status", "INACTIVE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionStatus(SubscriptionStatusObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create  Luc Subscription")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_21_CreateSubscription() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateLucSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", MID);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("conditionalParameter", "TXN_COUNT");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", "2023-11-07");
        body.put("onboardingDate", java.time.LocalDate.now().toString());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }






}
