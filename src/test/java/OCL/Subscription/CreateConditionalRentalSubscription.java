package OCL.Subscription;

import Request.Subscription.CreateSubscription;
import Services.Subscription.SubscriptionPlan;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class CreateConditionalRentalSubscription extends BaseMethod {

    SubscriptionPlan SubscriptionPlanObj = new SubscriptionPlan();

    private static final Logger LOGGER = LogManager.getLogger(CreateLUCSubscription.class);

    public static String custId = "1002305549";

    public static String MID = "cvqaRF11652124862409";

    public static String Mobile_Number = "7770008821";

    public static String USN1 = "";
    public static String USN2 = "";
    public static String USN3 = "";
    public static String USN4 = "";
    public static String USN5 = "";
    public static String USN6 = "";
    public static String USN7 = "";
    public static String USN8 = "";
    public static String USN9 = "";
    public static String USN10 = "";

    public static String USN11 = "";
    public static String jwt = "";

    public static String clientId = "oe-subscription-client";

    public static String clientsecret = "E7MYmjY7sZNONEGtcPbM5lYjk2sBG+RSsyJVeMqF8DQMd+ToQSJ3tU4ii3Sz/YD0/L6+CG4SoWccRgcuaKiasQ==";

    @Test(priority = 0, groups = {"Regression"}, description = "Generate US")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_GenerateUSN() {

        Integer SerialNo1 = Utilities.randomNumberGenerator(5);
        Integer SerialNo2 = Utilities.randomNumberGenerator(5);
        Integer SerialNo3 = Utilities.randomNumberGenerator(5);
        Integer SerialNo4 = Utilities.randomNumberGenerator(5);
        Integer SerialNo5 = Utilities.randomNumberGenerator(5);
        Integer SerialNo6 = Utilities.randomNumberGenerator(5);
        Integer SerialNo7 = Utilities.randomNumberGenerator(5);
        Integer SerialNo8 = Utilities.randomNumberGenerator(5);
        Integer SerialNo9 = Utilities.randomNumberGenerator(5);
        Integer SerialNo10 = Utilities.randomNumberGenerator(5);
        Integer SerialNo11 = Utilities.randomNumberGenerator(5);
        USN1 = "SUBS" + SerialNo1;
        USN2 = "SUBS" + SerialNo2;
        USN3 = "SUBS" + SerialNo3;
        USN4 = "SUBS" + SerialNo4;
        USN5 = "SUBS" + SerialNo5;
        USN6 = "SUBS" + SerialNo6;
        USN7 = "SUBS" + SerialNo7;
        USN8 = "SUBS" + SerialNo8;
        USN9 = "SUBS" + SerialNo9;
        USN10 = "SUBS" + SerialNo10;
        USN11 = "SUBS" + SerialNo11;
        LOGGER.info("USN1 is :" + USN1);
        LOGGER.info("USN2 is :" + USN2);
        LOGGER.info("USN3 is :" + USN3);
        LOGGER.info("USN4 is :" + USN4);
        LOGGER.info("USN5 is :" + USN5);
        LOGGER.info("USN6 is :" + USN6);
        LOGGER.info("USN7 is :" + USN7);
        LOGGER.info("USN8 is :" + USN8);
        LOGGER.info("USN9 is :" + USN9);
        LOGGER.info("USN10 is :" + USN10);
        LOGGER.info("USN10 is :" + USN11);
        jwt = createAuth0JwsHMAC(clientId, clientsecret);
        LOGGER.info("Token is : " + jwt);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create conditional rental Subscription for MID condition TXN COUNT ,with all paymodes and Deduction date")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", USN1);
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "TXN_COUNT");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        //Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Duplicate subscription"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create conditional rental Subscription for MID condition GMV ,with all paymodes and Deduction date")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", USN2);
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "GMV");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        //Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Duplicate subscription"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create conditional rental Subscription for MID condition TXN COUNT ,with all paymodes and without Deduction date")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", USN3);
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
       // body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "TXN_COUNT");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        //Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Duplicate subscription"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create conditional rental Subscription for MID condition GMV ,with all paymodes and without deduction date")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", USN4);
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
       // body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "GMV");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        //Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Duplicate subscription"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create conditional rental Subscription for MID condition TXN COUNT ,with all paymodes and Deduction date")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", USN5);
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "TXN_COUNT");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        //Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Duplicate subscription"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create conditional rental Subscription for MID condition GMV ,with all paymodes and Deduction date")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", USN6);
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "GMV");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        //Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Duplicate subscription"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create conditional rental Subscription for MID condition TXN COUNT ,with all paymodes and without Deduction date")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", USN7);
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        // body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "TXN_COUNT");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        //Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Duplicate subscription"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create conditional rental Subscription for MID condition GMV ,with all paymodes and without deduction date")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", USN8);
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        // body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "GMV");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        //Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Duplicate subscription"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Duplicate Subscription error")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", USN8);
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        // body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "GMV");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Duplicate subscription"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "create subscription without conditional parmams")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", "ASWERRR");
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        // body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("TID only applicable for conditional subscription"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "create subscription without onboarding date ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", "LADGH233");
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", "");
        // body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "GMV");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Onboarding date cannot be blank"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "create subscription without frequency ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", "DVCVCARE1");
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        // body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "GMV");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
       // Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Invalid frequency format || Plan frequency cannot be blank"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "create subscription with invalid frequency ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_14() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", "BCVDFRERE1");
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "0M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        // body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "GMV");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Invalid frequency format"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "create subscription without subscription Type ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_15() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", "BCVTRTY2");
        body.put("subscriptionType", "");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        // body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "GMV");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
       // Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Internal Server Error"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "create subscription without service Type ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_16() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", "CBGGTTEUW");
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        // body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "GMV");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Service type cannot be blank"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "create subscription without service Name ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_17() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", "MNYYYUU4RT");
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        // body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "GMV");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Service cannot be blank"));
        Assert.assertEquals(StatusCode, 200);
    }


    @Test(priority = 1, groups = {"Regression"}, description = "create subscription without security deposit ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_18() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", "JUITYT5E");
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        // body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "GMV");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Duplicate subscription"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "create subscription without MID ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_19() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", "");
        body.put("usn", "OPIUYTRE");
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        // body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "GMV");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
       // Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Invalid MID format || MID cannot be blank"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "create subscription without CUSTID ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_20() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", "");
        body.put("mid", MID);
        body.put("usn", "CVDFRERTE");
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        // body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "GMV");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Duplicate subscription"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "create subscription with invalid onboarding date ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_21() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", "5LADGH2332");
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", "00-0000-00");
        // body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("conditionalParameter", "GMV");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Text '00-0000-00' could not be parsed at index 0"));
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusCode").contains("400"));
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("status").contains("FAILURE"));
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("errorCode").contains("SUP-008"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "create subscription with invalid deduction date ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_22() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", "2LADGH2332");
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate",java.time.LocalDate.now().toString() );
        body.put("deductionStartDate", "00-0000-00");
        body.put("conditionalParameter", "GMV");
        body.put("isTid", "true");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Text '00-0000-00' could not be parsed at index 0"));
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusCode").contains("400"));
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("status").contains("FAILURE"));
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("errorCode").contains("SUP-008"));
        Assert.assertEquals(StatusCode, 200);
    }


    @Test(priority = 1, groups = {"Regression"}, description = "create subscription without any value of istid key ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_23() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRenalSubsRequestWithTID"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", USN10);
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate",java.time.LocalDate.now().toString() );
       // body.put("deductionStartDate", "00-0000-00");
        body.put("conditionalParameter", "GMV");
        body.put("isTid", "");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        //Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Text '00-0000-00' could not be parsed at index 0"));
       // Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusCode").contains("400"));
       // Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("status").contains("FAILURE"));
      //  Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("errorCode").contains("SUP-008"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create conditional rental Subscription for MID condition GMV ,with all paymodes and DEDUCTION INTERVAL")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_24() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRentalSubscriptionWithDeductionInterval"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", USN11);
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        //body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("deductionInterval", "1M");
        body.put("conditionalParameter", "GMV");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        //Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Duplicate subscription"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create conditional rental Subscription for MID condition GMV ,with all paymodes and incorrect DEDUCTION INTERVAL")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_25() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRentalSubscriptionWithDeductionInterval"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", USN11);
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "AS");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        //body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("deductionInterval", "1M");
        body.put("conditionalParameter", "GMV");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        //Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Duplicate subscription"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create conditional rental Subscription for MID condition GMV ,with all paymodes and empty DEDUCTION INTERVAL")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_26() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateConditionalRentalSubscriptionWithDeductionInterval"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("usn", USN11);
        body.put("subscriptionType", "RENTAL");
        body.put("phoneNumber", Mobile_Number);
        body.put("securityDeposit", "590.0");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("planPrice", "0");
        body.put("frequency", "AS");
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        //body.put("deductionStartDate", java.time.LocalDate.now().plusDays(30).toString());
        body.put("deductionInterval", "");
        body.put("conditionalParameter", "GMV");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        //Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Duplicate subscription"));
        Assert.assertEquals(StatusCode, 200);
    }







}
