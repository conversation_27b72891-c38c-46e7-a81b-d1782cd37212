package OCL.Subscription;

import Request.Subscription.CreateSubscription;
import Services.MechantService.MiddlewareServices;
import Services.Subscription.SubscriptionPlan;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class CreateUpfrontAdvanceSubscription extends BaseMethod
{
    SubscriptionPlan SubscriptionPlanObj = new SubscriptionPlan();
    private static final Logger LOGGER = LogManager.getLogger(CreateUpfrontAdvanceSubscription.class);

    public static String custId = "1002468320";
    public static String MID = "oLlJuh39723009659347";
    public static String Mobile_Number = "8123647895";
    public static String USN = "";
    public static String jwt = "";
    public static String clientId = "oe-subscription-client";
    public static String clientsecret = "E7MYmjY7sZNONEGtcPbM5lYjk2sBG+RSsyJVeMqF8DQMd+ToQSJ3tU4ii3Sz/YD0/L6+CG4SoWccRgcuaKiasQ==";


    @Test(priority = 0, groups = {"Regression"}, description = "Generate jwt")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_GenerateJwt() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        LOGGER.info("USN is :" + USN);
        jwt = createAuth0JwsHMAC(clientId, clientsecret);
        LOGGER.info("Token is : " + jwt);


    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Upfront Advance Subscription with prorata true")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_CreateSubscription() {

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "RENTAL");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "1");
        body.put("proRata", "true");
        body.put("deductionInterval","5D");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create  Upfront Advance Subscription")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_CreateSubscriptionWithNullDeductionInterval() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "RENTAL");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "0");
        body.put("proRata", "false");
        body.put("deductionInterval","null");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Invalid Deduction Interval"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create  Upfront Advance Subscription with empty deduction interval")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_CreateSubscriptionWithEmptyDeductionInterval() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "RENTAL");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "0");
        body.put("proRata", "false");
        body.put("deductionInterval"," ");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Invalid Deduction Interval"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create  Upfront Advance Subscription with invalid deduction interval")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_CreateSubscriptionWithInvalidDeductionInterval() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "RENTAL");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "0");
        body.put("proRata", "false");
        body.put("deductionInterval","abc");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Invalid Deduction Interval"));
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Create  Upfront Advance Subscription with null advance period")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_CreateSubscriptionWithNullAdvancePeriod() {
        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;

        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "RENTAL");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "null");
        body.put("proRata", "false");
        body.put("deductionInterval","5D");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create  Upfront Advance Subscription with empty advance period")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_CreateSubscriptionWithEmptyAdvancePeriod() {
        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "RENTAL");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", " ");
        body.put("proRata", "false");
        body.put("deductionInterval","5D");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create  Upfront Advance Subscription with invalid advance period")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_CreateSubscriptionWithInvalidAdvancePeriod() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "RENTAL");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "-1");
        body.put("proRata", "false");
        body.put("deductionInterval","5D");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Advance period value should be between 0 and 36"));
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Create  Upfront Advance Subscription with null pro rata")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_CreateSubscriptionWithNullProRata() {
        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "RENTAL");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "1");
        body.put("proRata", "null");
        body.put("deductionInterval","5D");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create  Upfront Advance Subscription with empty pro rata")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_CreateSubscriptionWithEmptyProRata() {
        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "RENTAL");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "1");
        body.put("proRata", " ");
        body.put("deductionInterval","5D");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }


    @Test(priority = 1, groups = {"Regression"}, description = "Create  Upfront Advance Subscription with invalid pro rata")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11_CreateSubscriptionWithInvalidProRata() {
        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "RENTAL");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "1");
        body.put("proRata", "abd");
        body.put("deductionInterval","5D");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusCode").contains("400"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Upfront Advance Subscription with prorata false")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12_CreateSubscription() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "RENTAL");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "1");
        body.put("proRata", "false");
        body.put("deductionInterval","5D");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Upfront Advance Subscription for slc")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13_CreateSubscription() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "SLC");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "2");
        body.put("proRata", "true");
        body.put("deductionInterval","5D");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Create Upfront Advance Subscription for sim charge")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_14_CreateSubscription() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "SIM_CHARGE");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "3");
        body.put("proRata", "true");
        body.put("deductionInterval","5D");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Create Upfront Advance Subscription for AMC")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_15_CreateSubscription() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "AMC");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "1");
        body.put("proRata", "true");
        body.put("deductionInterval","5D");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Create Upfront Advance Subscription for EMI Rental")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_16_CreateSubscription() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "EMIRENTAL");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "1");
        body.put("proRata", "false");
        body.put("deductionInterval","5D");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Upfront Advance Subscription for low usage charge")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_17_CreateSubscription() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "LOW_USAGE_CHARGE");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "1");
        body.put("proRata", "true");
        body.put("deductionInterval","5D");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Upfront Advance Subscription for lifetime plan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_18_CreateSubscription() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "RENTAL");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "12M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "null");
        body.put("proRata", "null");
        body.put("deductionInterval","5D");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Duplicate Subscription")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_19_CreateDuplicateSubscription() {


        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateUpfrontAdvanceSubscriptionRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "RENTAL");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "1");
        body.put("proRata", "true");
        body.put("deductionInterval","3D");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Duplicate subscription"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Upfront Advance Subscription with conditional rental")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_20_CreateSubscription() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOTI" + SerialNo;
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("ConditionalRentalWithUpfrontAdvanceRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "RENTAL");
        body.put("serviceType", "Android");
        body.put("serviceName", "EDC");
        body.put("frequency", "1M");
        body.put("deductionStartDate", java.time.LocalDate.now().toString());
        body.put("onboardingDate", java.time.LocalDate.now().toString());
        body.put("advancePeriod", "1");
        body.put("proRata", "true");
        body.put("deductionInterval","5D");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusMessage").contains("Internal Server Error"));
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("statusCode").contains("500"));
        Assert.assertTrue(FetchPlanObjResponse.jsonPath().getString("status").contains("FAILURE"));
        Assert.assertEquals(StatusCode, 200);
    }

}
