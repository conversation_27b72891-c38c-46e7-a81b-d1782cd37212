package OCL.Subscription;

import Services.Subscription.SubscriptionPlan;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FetchPlanSubscription extends BaseMethod {

	
    private static final Logger LOGGER = LogManager.getLogger(FetchPlanSubscription.class);
    public static String clientid= "oe-subscription-client";
    public static String clientsecret="E7MYmjY7sZNONEGtcPbM5lYjk2sBG+RSsyJVeMqF8DQMd+ToQSJ3tU4ii3Sz/YD0/L6+CG4SoWccRgcuaKiasQ==";
    public static String mid = "DcFcoJ97348642540712";
    public static String usn= "MSJRC193MLQ";
    public static String status="ACTIVE";
    public static String inactivestatus="INACTIVE";
    public static String yearlyplanActive="Yearly ";
    public static String supersaverplanInActive="Super Saver Gold";
    public static String SerialNumber="MSJRC193MLQ";
    public static String planName="A910";



    public static String isRefundDetailsRequired="true";
    public static String isOtherChargesRequired="true";
    
    @Test(priority = 1,description = "Fetching Plan Subscription for a merhant where terminal is ACTIVE", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void FetchSubscriptionPlanTerminalActive()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
        params.put("usn", usn);
        params.put("status", status);
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", status);

        
        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].deviceName"),planName);
        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planName"),"Lifetime Plan Gold");
        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].status"),status);

      /*  Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].startDate"),"2022-06-23");
        float expectedamount=(float) 1.00;
        float expectedpercentage=(float) 0.0;
        float expectedamountnew=(float) 0.0;
        float expectedpercentagenew=(float) 100.0;
        float expectedamountInstallation=(float) 1.18;

        //


        float actualamount=subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].amount");
        float actualpercentage=subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].percentage");
        float actualamountnew=subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].amount");
        float actualpercentagenew=subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].percentage");
        float actualamountsim=subfetchResponse.jsonPath().get("results[0].planOtherCharges[0].amount");
        float actualamountInstallation=subfetchResponse.jsonPath().get("results[0].planOtherCharges[1].amount");

        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].endDate"),"2022-06-24");
        Assert.assertEquals(actualamount,expectedamount);
        Assert.assertEquals(actualpercentage,expectedpercentage);


        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].startDate"),"2022-06-25");
        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].endDate"),"9999-12-31");
        Assert.assertEquals(actualpercentagenew,expectedpercentagenew);

        Assert.assertEquals(actualamountnew,expectedamountnew);

      //  Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planOtherCharges[0].chargeType"),"simCharge");
      //  Assert.assertEquals(actualamountsim,expectedpercentage);

        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planOtherCharges[0].label"),"Sim Charge");

        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planOtherCharges[1].chargeType"),"installationCharge");
        Assert.assertEquals(actualamountInstallation,expectedamountInstallation);

        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planOtherCharges[1].label"),"Installation Charge"); */


    } 
    
    
    @Test(priority = 1,description = "Fetching Plan Subscription for a merhant where terminal is INACTIVE", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void FetchSubscriptionPlanTerminalInActive()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
        params.put("usn", usn);
        params.put("status", inactivestatus);
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", inactivestatus);

        
        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].deviceName"),planName);
        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planName"),supersaverplanInActive);
        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].status"),inactivestatus);

        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].startDate"),"2022-06-23");
        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].endDate"),"2022-06-24");

        float expectedamount=(float) 1.00;
        float expectedpercentage=(float) 0.0;
        float expectedamountnew=(float) 0.0;
        float expectedpercentagenew=(float) 100.0;



        float actualamount=subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].amount");
        float actualpercentage=subfetchResponse.jsonPath().get("results[0].planRefundDetails[0].percentage");
        float actualamountnew=subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].amount");
        float actualpercentagenew=subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].percentage");

        Assert.assertEquals(actualamount,expectedamount);
        Assert.assertEquals(actualpercentage,expectedpercentage);


        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].startDate"),"2022-06-25");
        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planRefundDetails[1].endDate"),"9999-12-31");
        Assert.assertEquals(actualpercentagenew,expectedpercentagenew);

        Assert.assertEquals(actualamountnew,expectedamountnew);


    } 
    
    
    @Test(priority = 1,description = "Fetching Plan Subscription for a merhant without refund", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void FetchSubscriptionPlanWithoutRefund()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
        params.put("usn", usn);
        params.put("status", status);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", status);

        
        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].deviceName"),planName);
        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].planName"),"Lifetime Plan Gold");
        Assert.assertEquals(subfetchResponse.jsonPath().get("results[0].status"),status);

        

    } 
    

    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant By not providing mid in the params", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void NegativeFetchSubscriptionPlanNoMID()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
//        params.put("mid", mid);
        params.put("usn", usn);
        params.put("status", inactivestatus);
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", inactivestatus);

        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"400");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Required String parameter 'mid' is not present");
         Assert.assertEquals(subfetchResponse.jsonPath().get("errorCode"),"SUP-007");

    } 


    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant By not providing USN in the params", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void NegativeFetchSubscriptionPlanNoUSN()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
//        params.put("usn", usn);
        params.put("status", inactivestatus);
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", inactivestatus);

        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"400");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Required String parameter 'usn' is not present");
         Assert.assertEquals(subfetchResponse.jsonPath().get("errorCode"),"SUP-007");

    } 
    

    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant By not providing Status in the params", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void NegativeFetchSubscriptionPlanNoSTATUS()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
        params.put("usn", usn);
//        params.put("status", inactivestatus);
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", inactivestatus);

        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"400");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Required String parameter 'status' is not present");
         Assert.assertEquals(subfetchResponse.jsonPath().get("errorCode"),"SUP-007");

    } 
    

    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant By providing invalid Status in the params", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void NegativeFetchSubscriptionPlaninvalidstatus()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
        params.put("usn", usn);
        params.put("status", "ACTIVEINACTIVE");
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", inactivestatus);

        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"400");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"status can be only ACTIVE|INACTIVE");

    } 
    

    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant By providing other VALID USN not linked with the provided mid in the params", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void NegativeFetchSubscriptionPlan()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
        params.put("usn", "MSJLN873RCQ");
        params.put("status", "ACTIVE");
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", inactivestatus);

        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"204");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"No data found.");

    } 
    


    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant with invalid xclientId", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void NegativeFetchSubscriptionPlanInvalidAuthx()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", "oe");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
        params.put("usn", "MSJLN873RCQ");
        params.put("status", "ACTIVE");
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", inactivestatus);

        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 401, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"401");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Invalid client id/token");

    } 
    

    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant with invalid xclienttoken", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void NegativeFetchSubscriptionPlanInvalidtoken()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", "test");
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
        params.put("usn", "MSJLN873RCQ");
        params.put("status", "ACTIVE");
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", inactivestatus);

        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 401, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"401");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Malformed JWT");

    } 
    


    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant with invalid xclientId", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void NegativeFetchSubscriptionPlanInvalidId()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", "oe");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
        params.put("usn", "MSJLN873RCQ");
        params.put("status", "ACTIVE");
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", inactivestatus);

        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 401, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"401");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Invalid client id/token");

    } 
    

    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant with invalid xclienttoken", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void NegativeFetchSubscriptionPlanInvalid()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", "test");
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
        params.put("usn", "MSJLN873RCQ");
        params.put("status", "ACTIVE");
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", inactivestatus);

        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 401, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"401");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Malformed JWT");

    } 
    


    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant with invalid xclientId", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void NegativeFetchSubscriptionPlanInvalidCientId()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", "oe");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
        params.put("usn", "MSJLN873RCQ");
        params.put("status", "ACTIVE");
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", inactivestatus);

        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 401, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"401");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Invalid client id/token");

    } 
    

    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant with invalid xclienttoken", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void NegativeFetchSubscriptionPlanBlankAuthxclienttoken()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", "test");
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
        params.put("usn", "MSJLN873RCQ");
        params.put("status", "ACTIVE");
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", inactivestatus);

        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 401, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"401");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Malformed JWT");

    } 
    


    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant with invalid xclientId", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void NegativeFetchSubscriptionPlanBlankAuthxclientId()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", "oe");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
        params.put("usn", "MSJLN873RCQ");
        params.put("status", "ACTIVE");
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", inactivestatus);

        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 401, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"401");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Invalid client id/token");

    } 
    

    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant with invalid xclienttoken", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void NegativeFetchSubscriptionPlanBlankAuth()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", "test");
        headers.put("x-client-id", clientid);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
        params.put("usn", "MSJLN873RCQ");
        params.put("status", "ACTIVE");
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", inactivestatus);

        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 401, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"401");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Malformed JWT");

    } 
    


    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant with invalid xclientId", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void NegativeFetchSubscriptionPlanBlankxclientId()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", "oe");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
        params.put("usn", "MSJLN873RCQ");
        params.put("status", "ACTIVE");
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", inactivestatus);

        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 401, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"401");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Invalid client id/token");

    } 
    
    @Test(priority = 1,description = "Negative case Fetching Plan Subscription for a merhant with invalid xclientId", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)

    public void NegativeFetchSubscriptionPlanBlankID()
    {

    	String jwttoken=createAuth0JwsHMAC(clientid,clientsecret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwttoken);
        headers.put("x-client-id", "oe");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        
        Map<String,String> params=new HashMap<String,String>();
        params.put("mid", mid);
        params.put("usn", "MSJLN873RCQ");
        params.put("status", "ACTIVE");
        params.put("isRefundDetailsRequired", isRefundDetailsRequired);
        params.put("isOtherChargesRequired", isOtherChargesRequired);
        
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("status", inactivestatus);

        
        SubscriptionPlan subfetch =new SubscriptionPlan();
        Response subfetchResponse= subfetch.subscriptionfetchPlan(headers, body,params);
        subfetchResponse.prettyPrint();
        int httpcode = subfetchResponse.getStatusCode();
      
        Assert.assertTrue(httpcode == 401, "Testcase Failed");
        Assert.assertEquals(subfetchResponse.jsonPath().get("status"),"FAILURE");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusCode"),"401");
        Assert.assertEquals(subfetchResponse.jsonPath().get("statusMessage"),"Invalid client id/token");

    } 
    

   

}
