package OCL.Subscription;


import Request.Subscription.CreateSubscription;
import Services.MechantService.MiddlewareServices;
import Services.Subscription.SubscriptionPlan;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class CreateSubscriptionPlan extends BaseMethod
{
    SubscriptionPlan SubscriptionPlanObj=new SubscriptionPlan();
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(CreateSubscriptionPlan.class);

    public static String custId = "1002305549";
    public static String MID = "cvqaRF11652124862409";
    public static String Mobile_Number = "7770008821";
    public static String USN = "";
    public static String clientId = "oe-subscription-client";
    public static String clientsecret="E7MYmjY7sZNONEGtcPbM5lYjk2sBG+RSsyJVeMqF8DQMd+ToQSJ3tU4ii3Sz/YD0/L6+CG4SoWccRgcuaKiasQ==";


    @Test(priority = 0, groups = {"Regression"}, description = "Generate US")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void GenerateUSN() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        USN = "JYOT"+SerialNo;
        LOGGER.info("USN is :" +USN);


    }
    @Test(priority = 1, groups = {"Regression"}, description = "Create Subscription Plan ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_CreateAMCSubscriptionPlan() {

        CreateSubscription CreateSubscriptionObj=new CreateSubscription(P.TESTDATA.get("CreateSubscriptionBody"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "AMC");

        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());

       String jwt= createAuth0JwsHMAC(clientId,clientsecret);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Subscription Plan ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_CreateEMIRentalSubscriptionPlan() {

        CreateSubscription CreateSubscriptionObj=new CreateSubscription(P.TESTDATA.get("CreateSubscriptionBody"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "EMIRENTAL");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());

        String jwt= createAuth0JwsHMAC(clientId,clientsecret);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Subscription Plan ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_CreateRentalSubscriptionPlan() {

        CreateSubscription CreateSubscriptionObj=new CreateSubscription(P.TESTDATA.get("CreateSubscriptionBody"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "RENTAL");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());

        String jwt= createAuth0JwsHMAC(clientId,clientsecret);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Create Subscription Plan ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_CreateSLCSubscriptionPlan() {

        CreateSubscription CreateSubscriptionObj=new CreateSubscription(P.TESTDATA.get("CreateSubscriptionBody"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "SLC");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());

        String jwt= createAuth0JwsHMAC(clientId,clientsecret);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Create Subscription Plan ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_CreateSIMSubscriptionPlan() {

        CreateSubscription CreateSubscriptionObj=new CreateSubscription(P.TESTDATA.get("CreateSubscriptionBody"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "SIM_CHARGE");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());

        String jwt= createAuth0JwsHMAC(clientId,clientsecret);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Create Subscription Plan ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_CreateBrandEmiSubscriptionPlan() {

        CreateSubscription CreateSubscriptionObj=new CreateSubscription(P.TESTDATA.get("CreateSubscriptionBody"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "BRAND_EMI_RENTAL");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());

        String jwt= createAuth0JwsHMAC(clientId,clientsecret);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Subscription Plan ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_CreatePaymentConfirmationSubscriptionPlan() {

        CreateSubscription CreateSubscriptionObj=new CreateSubscription(P.TESTDATA.get("CreateSubscriptionBody"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "PAYMENT_CONFIRMATION");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());

        String jwt= createAuth0JwsHMAC(clientId,clientsecret);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Subscription Plan ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_CreateCashPosSubscriptionPlan() {

        CreateSubscription CreateSubscriptionObj=new CreateSubscription(P.TESTDATA.get("CreateSubscriptionBody"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "CASHPOS");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());

        String jwt= createAuth0JwsHMAC(clientId,clientsecret);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Create Subscription Plan ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_CreateNPSSubscriptionPlan() {

        CreateSubscription CreateSubscriptionObj=new CreateSubscription(P.TESTDATA.get("CreateSubscriptionBody"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "NPS_SURVEY");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());

        String jwt= createAuth0JwsHMAC(clientId,clientsecret);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Subscription Plan ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_CreateASCSubscriptionPlan() {

        CreateSubscription CreateSubscriptionObj=new CreateSubscription(P.TESTDATA.get("CreateSubscriptionBody"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "ASC");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());

        String jwt= createAuth0JwsHMAC(clientId,clientsecret);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create Subscription Plan ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11_InvalidSubscriptionPlan() {

        CreateSubscription CreateSubscriptionObj=new CreateSubscription(P.TESTDATA.get("CreateSubscriptionBody"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "ASC1");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());

        String jwt= createAuth0JwsHMAC(clientId,clientsecret);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Duplicate Subscription Plan ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12_DuplicateSubscriptionPlan() {

        CreateSubscription CreateSubscriptionObj=new CreateSubscription(P.TESTDATA.get("CreateSubscriptionBody"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", MID);
        body.put("phoneNumber", Mobile_Number);
        body.put("usn", USN);
        body.put("subscriptionType", "SLC");
        body.put("frequency", "1M");
        body.put("onboardingDate", java.time.LocalDate.now().toString());

        String jwt= createAuth0JwsHMAC(clientId,clientsecret);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj,headers,body);
        int StatusCode = FetchPlanObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
}
