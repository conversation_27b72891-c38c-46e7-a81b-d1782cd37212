package OCL.Subscription;

import Request.Subscription.CreateSubscription;
import Request.Subscription.FetchNewUsnSubscription;
import Request.Subscription.FetchOldUsnSubscription;
import Request.Subscription.ReplaceSubscriptionRequest;
import Services.Subscription.SubscriptionPlan;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class ReplaceSubscription extends BaseMethod
{
    SubscriptionPlan SubscriptionPlanObj = new SubscriptionPlan();
    private static final Logger LOGGER = LogManager.getLogger(ReplaceSubscription.class);

    public static String custId = "";
    public static String MID = "";
    public static String Mobile_Number = "";
    public static String NewUSN = "";
    public static String jwt = "";
    public static String clientId = "oe-subscription-client";
    public static String clientsecret = "E7MYmjY7sZNONEGtcPbM5lYjk2sBG+RSsyJVeMqF8DQMd+ToQSJ3tU4ii3Sz/YD0/L6+CG4SoWccRgcuaKiasQ==";

    public ReplaceSubscription(String replaceSubscriptionReqBody) {
        super();
    }


    @Test(priority = 0, groups = {"Regression"}, description = "Generate US")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_GenerateUSN() {

        Integer SerialNo = Utilities.randomNumberGenerator(5);
        NewUSN = "JYOT" + SerialNo;
        LOGGER.info("USN is :" + NewUSN);
        jwt = createAuth0JwsHMAC(clientId, clientsecret);
        LOGGER.info("Token is : " + jwt);


    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch Subscription for new usn")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02() {

        FetchNewUsnSubscription FetchNewUsnSubscriptionObj = new FetchNewUsnSubscription();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("usn", NewUSN);
        params.put("serviceName", "EDC");
        params.put("status", "ACTIVE");

        Response FetchNewUsnSubscriptionObjResp = SubscriptionPlanObj.fetchNewUsnSubscription(FetchNewUsnSubscriptionObj,headers,params);
        int StatusCode = FetchNewUsnSubscriptionObjResp.getStatusCode();
        Assert.assertTrue(FetchNewUsnSubscriptionObjResp.jsonPath().getString("statusMessage").contains("No data found."));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch Subscription for new usn with empty service name")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03() {

        FetchNewUsnSubscription FetchNewUsnSubscriptionObj = new FetchNewUsnSubscription();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("usn", NewUSN);
        params.put("serviceName", " ");
        params.put("status", "ACTIVE");

        Response FetchNewUsnSubscriptionObjResp = SubscriptionPlanObj.fetchNewUsnSubscription(FetchNewUsnSubscriptionObj,headers,params);
        int StatusCode = FetchNewUsnSubscriptionObjResp.getStatusCode();
        Assert.assertTrue(FetchNewUsnSubscriptionObjResp.jsonPath().getString("statusMessage").contains("Invalid Service | Service cannot be blank"));
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch Subscription for new usn with invalid service name")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04() {

        FetchNewUsnSubscription FetchNewUsnSubscriptionObj = new FetchNewUsnSubscription();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("usn", NewUSN);
        params.put("serviceName", "EDC1");
        params.put("status", "ACTIVE");

        Response FetchNewUsnSubscriptionObjResp = SubscriptionPlanObj.fetchNewUsnSubscription(FetchNewUsnSubscriptionObj,headers,params);
        int StatusCode = FetchNewUsnSubscriptionObjResp.getStatusCode();
        Assert.assertTrue(FetchNewUsnSubscriptionObjResp.jsonPath().getString("statusMessage").contains("Invalid Service | Service cannot be blank"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch Subscription for new usn with empty status")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05() {

        FetchNewUsnSubscription FetchNewUsnSubscriptionObj = new FetchNewUsnSubscription();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("usn", NewUSN);
        params.put("serviceName", "EDC");
        params.put("status", " ");

        Response FetchNewUsnSubscriptionObjResp = SubscriptionPlanObj.fetchNewUsnSubscription(FetchNewUsnSubscriptionObj,headers,params);
        int StatusCode = FetchNewUsnSubscriptionObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch Subscription for new usn with invalid status")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06() {

        FetchNewUsnSubscription FetchNewUsnSubscriptionObj = new FetchNewUsnSubscription();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("usn", NewUSN);
        params.put("serviceName", "EDC");
        params.put("status", "ACTIVE1");

        Response FetchNewUsnSubscriptionObjResp = SubscriptionPlanObj.fetchNewUsnSubscription(FetchNewUsnSubscriptionObj,headers,params);
        int StatusCode = FetchNewUsnSubscriptionObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch Subscription for new usn with empty usn")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07() {

        FetchNewUsnSubscription FetchNewUsnSubscriptionObj = new FetchNewUsnSubscription();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("usn", " ");
        params.put("serviceName", "EDC");
        params.put("status", "ACTIVE");

        Response FetchNewUsnSubscriptionObjResp = SubscriptionPlanObj.fetchNewUsnSubscription(FetchNewUsnSubscriptionObj,headers,params);
        int StatusCode = FetchNewUsnSubscriptionObjResp.getStatusCode();
        Assert.assertTrue(FetchNewUsnSubscriptionObjResp.jsonPath().getString("statusMessage").contains("Invalid USN format | USN cannot be blank"));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch Subscription for new usn with invalid usn")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08() {

        FetchNewUsnSubscription FetchNewUsnSubscriptionObj = new FetchNewUsnSubscription();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("usn", "@@");
        params.put("serviceName", "EDC");
        params.put("status", "ACTIVE");

        Response FetchNewUsnSubscriptionObjResp = SubscriptionPlanObj.fetchNewUsnSubscription(FetchNewUsnSubscriptionObj,headers,params);
        int StatusCode = FetchNewUsnSubscriptionObjResp.getStatusCode();
        Assert.assertTrue(FetchNewUsnSubscriptionObjResp.jsonPath().getString("statusMessage").contains("Invalid USN format | USN cannot be blank"));
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch Subscription for new usn with active status")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09() {

        FetchNewUsnSubscription FetchNewUsnSubscriptionObj = new FetchNewUsnSubscription();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("usn", "AERJEX2rjbke1");
        params.put("serviceName", "EDC");
        params.put("status", "ACTIVE");

        Response FetchNewUsnSubscriptionObjResp = SubscriptionPlanObj.fetchNewUsnSubscription(FetchNewUsnSubscriptionObj,headers,params);
        int StatusCode = FetchNewUsnSubscriptionObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch Subscription for new usn with inactive status")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10() {

        FetchNewUsnSubscription FetchNewUsnSubscriptionObj = new FetchNewUsnSubscription();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("usn", "AERJEX23makar1");
        params.put("serviceName", "EDC");
        params.put("status", "INACTIVE");

        Response FetchNewUsnSubscriptionObjResp = SubscriptionPlanObj.fetchNewUsnSubscription(FetchNewUsnSubscriptionObj,headers,params);
        int StatusCode = FetchNewUsnSubscriptionObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch Subscription for old usn")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11() {

        FetchOldUsnSubscription FetchOldUsnSubscriptionObj = new FetchOldUsnSubscription();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("usn", NewUSN);
        params.put("serviceName", "EDC");
        params.put("status", "ACTIVE");
        params.put("mid", "pvufVk98108581129485");

        Response FetchOldUsnSubscriptionObjResp = SubscriptionPlanObj.fetchOldUsnSubscription(FetchOldUsnSubscriptionObj,headers,params);
        int StatusCode = FetchOldUsnSubscriptionObjResp.getStatusCode();
        Assert.assertTrue(FetchOldUsnSubscriptionObjResp.jsonPath().getString("statusMessage").contains("No data found."));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch Subscription for old usn with empty mid")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12() {

        FetchOldUsnSubscription FetchOldUsnSubscriptionObj = new FetchOldUsnSubscription();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("usn", NewUSN);
        params.put("serviceName", "EDC");
        params.put("status", "ACTIVE");
        params.put("mid", "");

        Response FetchOldUsnSubscriptionObjResp = SubscriptionPlanObj.fetchOldUsnSubscription(FetchOldUsnSubscriptionObj,headers,params);
        int StatusCode = FetchOldUsnSubscriptionObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch Subscription for old usn with invalid mid")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13() {

        FetchOldUsnSubscription FetchOldUsnSubscriptionObj = new FetchOldUsnSubscription();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("usn", NewUSN);
        params.put("serviceName", "EDC");
        params.put("status", "ACTIVE");
        params.put("mid", "pvufVk981085811294851@");

        Response FetchOldUsnSubscriptionObjResp = SubscriptionPlanObj.fetchOldUsnSubscription(FetchOldUsnSubscriptionObj,headers,params);
        int StatusCode = FetchOldUsnSubscriptionObjResp.getStatusCode();
        Assert.assertTrue(FetchOldUsnSubscriptionObjResp.jsonPath().getString("statusMessage").contains("Invalid MID format"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Replace Subscription with inactive subscription")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_14() {
        ReplaceSubscriptionRequest replaceSubscriptionRequestObj = new ReplaceSubscriptionRequest(P.TESTDATA.get("ReplaceSubscriptionReqBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("usn", "AERJEXi5apr");
        body.put("newUsn", "AERJEXi5jb1");
        body.put("serviceName", "EDC");
        body.put("mid", "pvufVk98108581129485");
        body.put("serviceType", "Android");

        Response FetchOldUsnSubscriptionObjResp = SubscriptionPlanObj.replaceSubscription(replaceSubscriptionRequestObj,headers,body);
        int StatusCode = FetchOldUsnSubscriptionObjResp.getStatusCode();
        Assert.assertTrue(FetchOldUsnSubscriptionObjResp.jsonPath().getString("statusMessage").contains("Can't update inactive subscription"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Replace Subscription with invalid mid")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_15() {
        ReplaceSubscriptionRequest replaceSubscriptionRequestObj = new ReplaceSubscriptionRequest(P.TESTDATA.get("ReplaceSubscriptionReqBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("usn", "AERJEXi5apr");
        body.put("newUsn", "AERJEXi5jb1");
        body.put("serviceName", "EDC");
        body.put("mid", "pvufVk981085811294851");
        body.put("serviceType", "Android");

        Response FetchOldUsnSubscriptionObjResp = SubscriptionPlanObj.replaceSubscription(replaceSubscriptionRequestObj,headers,body);
        int StatusCode = FetchOldUsnSubscriptionObjResp.getStatusCode();
        Assert.assertTrue(FetchOldUsnSubscriptionObjResp.jsonPath().getString("statusMessage").contains("Subscription does not exist"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Replace Subscription with empty mid")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_16() {
        ReplaceSubscriptionRequest replaceSubscriptionRequestObj = new ReplaceSubscriptionRequest(P.TESTDATA.get("ReplaceSubscriptionReqBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("usn", "AERJEXi5apr");
        body.put("newUsn", "AERJEXi5jb1");
        body.put("serviceName", "EDC");
        body.put("mid", "");
        body.put("serviceType", "Android");

        Response FetchOldUsnSubscriptionObjResp = SubscriptionPlanObj.replaceSubscription(replaceSubscriptionRequestObj,headers,body);
        int StatusCode = FetchOldUsnSubscriptionObjResp.getStatusCode();
        Assert.assertTrue(FetchOldUsnSubscriptionObjResp.jsonPath().getString("statusMessage").contains("Invalid MID format"));
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Replace Subscription with empty service")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_17() {
        ReplaceSubscriptionRequest replaceSubscriptionRequestObj = new ReplaceSubscriptionRequest(P.TESTDATA.get("ReplaceSubscriptionReqBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("usn", "AERJEXi5apr");
        body.put("newUsn", "AERJEXi5jb1");
        body.put("serviceName", "");
        body.put("mid", "pvufVk98108581129485");
        body.put("serviceType", "Android");

        Response FetchOldUsnSubscriptionObjResp = SubscriptionPlanObj.replaceSubscription(replaceSubscriptionRequestObj,headers,body);
        int StatusCode = FetchOldUsnSubscriptionObjResp.getStatusCode();
        Assert.assertTrue(FetchOldUsnSubscriptionObjResp.jsonPath().getString("statusMessage").contains("Service cannot be blank"));
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Replace Subscription with invalid service")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_18() {
        ReplaceSubscriptionRequest replaceSubscriptionRequestObj = new ReplaceSubscriptionRequest(P.TESTDATA.get("ReplaceSubscriptionReqBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("usn", "AERJEXi5apr");
        body.put("newUsn", "AERJEXi5jb1");
        body.put("serviceName", "EDC1");
        body.put("mid", "pvufVk98108581129485");
        body.put("serviceType", "Android");

        Response FetchOldUsnSubscriptionObjResp = SubscriptionPlanObj.replaceSubscription(replaceSubscriptionRequestObj,headers,body);
        int StatusCode = FetchOldUsnSubscriptionObjResp.getStatusCode();
        Assert.assertTrue(FetchOldUsnSubscriptionObjResp.jsonPath().getString("statusMessage").contains("Invalid service."));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Replace Subscription with invalid service type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_19() {
        ReplaceSubscriptionRequest replaceSubscriptionRequestObj = new ReplaceSubscriptionRequest(P.TESTDATA.get("ReplaceSubscriptionReqBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("usn", "AERJEXi5apr");
        body.put("newUsn", "AERJEXi5jb1");
        body.put("serviceName", "EDC");
        body.put("mid", "pvufVk98108581129485");
        body.put("serviceType", "Android1");

        Response FetchOldUsnSubscriptionObjResp = SubscriptionPlanObj.replaceSubscription(replaceSubscriptionRequestObj,headers,body);
        int StatusCode = FetchOldUsnSubscriptionObjResp.getStatusCode();
        Assert.assertTrue(FetchOldUsnSubscriptionObjResp.jsonPath().getString("statusMessage").contains("Invalid service type."));
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Replace Subscription with empty service type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_20() {
        ReplaceSubscriptionRequest replaceSubscriptionRequestObj = new ReplaceSubscriptionRequest(P.TESTDATA.get("ReplaceSubscriptionReqBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("usn", "AERJEX23maa");
        body.put("newUsn", NewUSN);
        body.put("serviceName", "EDC");
        body.put("mid", "pvufVk98108581129485");
        body.put("serviceType", "");

        Response FetchOldUsnSubscriptionObjResp = SubscriptionPlanObj.replaceSubscription(replaceSubscriptionRequestObj,headers,body);
        int StatusCode = FetchOldUsnSubscriptionObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Replace Subscription with linux service type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_21() {
        ReplaceSubscriptionRequest replaceSubscriptionRequestObj = new ReplaceSubscriptionRequest(P.TESTDATA.get("ReplaceSubscriptionReqBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("usn", "AERJEXi5jb12314");
        body.put("newUsn", NewUSN);
        body.put("serviceName", "EDC");
        body.put("mid", "pvufVk98108581129485");
        body.put("serviceType", "Linux");

        Response FetchOldUsnSubscriptionObjResp = SubscriptionPlanObj.replaceSubscription(replaceSubscriptionRequestObj,headers,body);
        int StatusCode = FetchOldUsnSubscriptionObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Replace inactive Subscription")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_22() {
        ReplaceSubscriptionRequest replaceSubscriptionRequestObj = new ReplaceSubscriptionRequest(P.TESTDATA.get("ReplaceSubscriptionReqBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("usn", "AERJEXi5jb123");
        body.put("newUsn", NewUSN);
        body.put("serviceName", "EDC");
        body.put("mid", "pvufVk98108581129485");
        body.put("serviceType", "Android");

        Response FetchOldUsnSubscriptionObjResp = SubscriptionPlanObj.replaceSubscription(replaceSubscriptionRequestObj,headers,body);
        int StatusCode = FetchOldUsnSubscriptionObjResp.getStatusCode();
        Assert.assertTrue(FetchOldUsnSubscriptionObjResp.jsonPath().getString("statusMessage").contains("Can't update inactive subscription"));
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Replace Subscription with empty new usn")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_23() {
        ReplaceSubscriptionRequest replaceSubscriptionRequestObj = new ReplaceSubscriptionRequest(P.TESTDATA.get("ReplaceSubscriptionReqBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", clientId);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("usn", "AERJEXi5jb123");
        body.put("newUsn", "");
        body.put("serviceName", "EDC");
        body.put("mid", "pvufVk98108581129485");
        body.put("serviceType", "Android");

        Response FetchOldUsnSubscriptionObjResp = SubscriptionPlanObj.replaceSubscription(replaceSubscriptionRequestObj,headers,body);
        int StatusCode = FetchOldUsnSubscriptionObjResp.getStatusCode();
        Assert.assertTrue(FetchOldUsnSubscriptionObjResp.jsonPath().getString("statusMessage").contains("Invalid USN format"));
        Assert.assertEquals(StatusCode, 200);
    }
}
