package OCL.PGProfileUpdate.StoreCashBack;

import OCL.Subscription.FetchPlanSubscription;
import Services.CreateDeals.GetCreateLeadP4b;
import Services.StoreCashBack.StoreCashBackServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.assertions.CustomAssert;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class StoreCashBackAddAddress extends BaseMethod {


    private static final Logger LOGGER = LogManager.getLogger(FetchPlanSubscription.class);
    public String Token ;


    Response subfetchResponse = null;
    StoreCashBackServices getStoreCashBackObj = new StoreCashBackServices();
    GetCreateLeadP4b getCreateLeadP4b = new GetCreateLeadP4b();
    public static String solution="pg_profile_update";
    public static String entityType="INDIVIDUAL";
    public static String channel = "DIY_P4B_APP" ;
    public static String solutionSubType = "STORE_CASHBACK";
    public static String merchantMobileNumber = "6753223167";
    public static String mid = "BzKyKU70598839067380";
    public static String successMessage = "Request Submitted Successfully";
    String registerLead = "true";
    String triggerValue = "3";
    String leadId;
    String requestPath;
    String md5Key;
    String identifier;

    Map<String, String> headers = new HashMap<String, String>();
    String XMWCookie;


    @BeforeClass
    public void generateToken(){
        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
        XMWCookie=findXMWTokenforPanel("7771216290","paytm@123");
        LOGGER.info("Token for PG : " + Token);


    }
    @Test(priority = 2,description = "To validate that add new address api gives error when token is empty", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_001_EmptyToken() throws JSONException {

        headers.put("session_token", " ");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 401, "correct 401 api code recieved");

    }

    @Test(priority = 2,description = "To validate that add new address api gives error when token is invlaid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_002_InvalidTokens() throws JSONException {

        headers.put("session_token", "InvalidToken");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 401, "correct 401 api code recieved");

    }
    @Test(priority = 2,description = "To validate that add new address api gives error when token key not passed ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_003_EmptyTokenKeyValue() throws JSONException {


        //headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 401, "correct 401 api code recieved");

    }

    @Test(priority = 2,description = "To validate that add new address api gives error when no header is passed", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_004_NoHeaders() throws JSONException {

        Map<String,String> header =new HashMap<>();

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, header, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 401, "correct 401 api code recieved");

    }

    @Test(priority = 2,description = "To validate that add new address api gives error on empty MID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_005_EmptyMid() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", " ");

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct 400 api code recieved");

    }

    @Test(priority = 2,description = "To validate that add new address api gives error when mid is incorrect", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_006_InvalidToken() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "incorrectMID");

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct 400 api code recieved");

    }

    @Test(priority = 2,description = "To validate that add new address api gives error on sending incorrect MID key", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_007_InvalidToken() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("midIncorrect", mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct 400 api code recieved");

    }

    @Test(priority = 2,description = "To validate that add new address api gives error when Solution Sub type is invalid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_008_InvalidSolSubType() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");


        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", "Deals");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 500, "correct 500 api code recieved");

    }

    @Test(priority = 2,description = "To validate that add new address api gives error when channel is invalid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_009_InvalidChannelParam_BUGID_COP_19861() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");


        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", "invalid");
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "expects 400 api code ");

    }

    @Test(priority = 2,description = "To validate that add new address api gives error when entity type is invlaid entity trype", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_010_InvalidEntityType() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");


        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", "InvalidEntity");
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct 400 api code recieved");

    }

    @Test(priority = 2,description = "To validate that add new address api gives error when solution is invlaid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_011_Invalisolution() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");


        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "Invlaid");
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct 200 api code recieved");

    }

    @Test(priority = 2,description = "To validate that add new address api gives error when params are empty ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_012_EmptyHeader() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");


        Map<String, String> params = new HashMap<String, String>();


        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct 400 api code recieved");

    }

    @Test(priority = 2,description = "To validate that add new address api gives error when params are empty ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_013_incorrectHeader() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");


        Map<String, String> params = new HashMap<String, String>();


        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct 400 api code recieved");

    }

    @Test(priority = 2,description = "To validate that add new address api gives error on empty MID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_014_incorrectMid() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "GG123567800");

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct 400 api code recieved");

    }



    @Test(priority = 2,description = "To validate that add new address api gives error on empty MID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_015_blankMid() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "");

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct 400 api code recieved");

    }



    @Test(priority = 2,description = "To validate that add new address api gives error on empty MID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_0014_incorrectMid() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "GG123567800");

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct 400 api code recieved");

    }



    @Test(priority = 2,description = "To validate that add new address api gives error on empty MID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_0015_blankMid() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "");

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct 400 api code recieved");

    }

    @Test(priority = 2,description = "To validate that add new address api gives error on empty MID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_000015_blankMid() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "");

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct 400 api code recieved");

    }



    @Test(priority = 2,description = "To validate that add new address api gives error on empty MID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_00014_incorrectMid() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "GG123567800");

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct 400 api code recieved");

    }



    @Test(priority = 2,description = "To validate that add new address api gives error on empty MID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_00015_blankMid() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "");

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct 400 api code recieved");

    }


}
