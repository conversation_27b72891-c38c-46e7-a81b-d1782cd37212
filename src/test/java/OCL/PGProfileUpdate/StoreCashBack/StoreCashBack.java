package OCL.PGProfileUpdate.StoreCashBack;

import OCL.Subscription.FetchPlanSubscription;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Services.CreateDeals.GetCreateLeadP4b;
import Services.MechantService.MiddlewareServices;
import Services.StoreCashBack.StoreCashBackServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.assertions.CustomAssert;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

import static com.goldengate.common.BaseMethod.ApplicantToken;

public class StoreCashBack extends BaseMethod {


    private static final Logger LOGGER = LogManager.getLogger(FetchPlanSubscription.class);
    public String Token ;


    Response subfetchResponse = null;
    StoreCashBackServices getStoreCashBackObj = new StoreCashBackServices();
    GetCreateLeadP4b getCreateLeadP4b = new GetCreateLeadP4b();
    public static String solution="pg_profile_update";
    public static String entityType="PROPRIETORSHIP";
    public static String channel = "DIY_P4B_APP" ;
    public static String solutionSubType = "STORE_CASHBACK";
    public static String merchantMobileNumber = "8881777652";
    public static String mid = "onuEYg93462329067105";
    public static String successMessage = "Request Submitted Successfully";
    String registerLead = "true";
    String triggerValue = "3";
    String leadId;
    String requestPath;
    String md5Key;
    String identifier;

    Map<String, String> headers = new HashMap<String, String>();
    String XMWCookie;


    @BeforeClass
    public void generateToken(){
        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
        XMWCookie=findXMWTokenforPanel("7771216290","paytm@123");
        LOGGER.info("Token for PG : " + Token);
        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

    }


    @Test(priority = 1,description = "To validate that create Register lead fro store cashback is working sucessfully", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_001_RegisterLead() throws JSONException {

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("registerLead",registerLead);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,200);
        CustomAssert.assertEquals(subfetchResponse.jsonPath().getString("displayMessage"),successMessage,"Lead created sucessfully for store  cashback");
        leadId =subfetchResponse.jsonPath().getString("leadId");

    }


    @Test(priority = 2,description = "To validate that add new address api  for store cashback is working sucessfully", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_002_AddNewAddress() throws JSONException {

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddNewShopRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode,200,"correct 200 api code recieved");
        Assert.assertEquals(subfetchResponse.jsonPath().getString("displayMessage"),successMessage);
//        String leadIdfromresp =subfetchResponse.jsonPath().getString("leadId");
//        CustomAssert.assertEquals(leadIdfromresp,leadId,"successMessage");

    }

    @Test(priority = 3,description = "To validate that cashback details are added sucessfully", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_003_AddCashBackDetails() throws JSONException {

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddCashBackDeatilsRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode,200,"correct 200 api code recieved");
        Assert.assertEquals(subfetchResponse.jsonPath().getString("displayMessage"),successMessage);


    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_FetchTnc() throws JSONException {


        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,200);
        md5Key = subfetchResponse.jsonPath().getString("md5Key");
        identifier = subfetchResponse.jsonPath().getString("identifier");


    }

    @Test(priority = 5,description = "To validate that cashback details are added sucessfully", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_005_AddAgreement() throws JSONException {

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("MD_5_KEY",md5Key);
        body.put("TNC_IDENTIFIER",identifier);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsSubmitDetailsRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode,200,"correct 200 api code recieved");
        Assert.assertEquals(subfetchResponse.jsonPath().getString("displayMessage"),successMessage);


    }


    @Test(priority = 5,description = "To validate that data is navigated to panel", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_006_validatePanelData() throws JSONException {

        MiddlewareServices middlewareServicesObject = new MiddlewareServices();

        FetchLead v1FetchLeadObj = new FetchLead(leadId);

        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj,XMWCookie);

        System.out.println(v1FetchLeadResp.asString());
        Assert.assertEquals(subfetchResponse.jsonPath().getString("storeDealDetails.1[0]."),successMessage);


    }




}