package OCL.PGProfileUpdate.StoreCashBack;

import OCL.Subscription.FetchPlanSubscription;
import Services.CreateDeals.GetCreateLeadP4b;
import Services.StoreCashBack.StoreCashBackServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.assertions.CustomAssert;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class StoreCashBackRegisterLeadAPI extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(FetchPlanSubscription.class);
    public String Token;


    Response subfetchResponse = null;
    StoreCashBackServices getStoreCashBackObj = new StoreCashBackServices();
    GetCreateLeadP4b getCreateLeadP4b = new GetCreateLeadP4b();
    public static String solution="pg_profile_update";
    public static String entityType="INDIVIDUAL";
    public static String channel = "DIY_P4B_APP" ;
    public static String solutionSubType = "STORE_CASHBACK";
    public static String merchantMobileNumber = "5677567757";
    public static String mid = "QMkdEh06057310159464";
    public static String successMessage = "Request Submitted Successfully";
    String registerLead = "true";
    String triggerValue = "3";
    String leadId;
    String requestPath;
    String md5Key;
    String identifier;

    Map<String, String> headers = new HashMap<String, String>();
    String XMWCookie;


    @BeforeClass
    public void generateToken(){
        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
        LOGGER.info("Token for PG : " + Token);


    }


    @Test(priority = 1,description = "To validate that create Register lead thow error on empty token", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_001_WithoutToken() throws JSONException {

        headers.put("session_token", " " );
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("registerLead",registerLead);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);


    }

    @Test(priority = 1,description = "To validate that create Register lead fro store cashback is throwing error on entering invalid token", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_002_EmptyToken() throws JSONException {

        headers.put("session_token", "invalidtoken");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("registerLead",registerLead);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);

    }

    @Test(priority = 1,description = "To validate that create Register lead fro store cashback is throwing error on entering without token key value", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_003_EmptyToken() throws JSONException {

        //headers.put("session_token", "invalidtoken");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("registerLead",registerLead);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);

    }

    @Test(priority = 1,description = "To validate that create Register lead fro store cashback is throwing error on entering without headers", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_004_EmptyToken() throws JSONException {

        Map<String, String> headers_empty = new HashMap<String, String>();

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("registerLead",registerLead);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers_empty, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);


    }

    @Test(priority = 1,description = "To validate that create Register lead fro store cashback is throwing error on entering wmpty mid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_006_withoutParams() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid"," ");
        body.put("registerLead",registerLead);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To validate that create Register lead fro store cashback is throwing error on entering incorrect mid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_007_IncorrectMID() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid","IncorrectMID");
        body.put("registerLead",registerLead);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To validate that create Register lead fro store cashback is throwing error on not sending register lead empty", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_008_EmptyRegisterleadparam() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("registerLead"," ");

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To validate that create Register lead fro store cashback is throwing error on sending incorrect solution subtype ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_009_incorrectSolsubType() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", "incorrectsolutionsubtype");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("registerLead",registerLead);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To validate that create Register lead fro store cashback is throwing error on sending incorrect channel ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_010_incorrectChannel() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", "channelincorrect");
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("registerLead",registerLead);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To validate that create Register lead fro store cashback is throwing error on sending incorrect channel ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_011_emptyChannel() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", "");
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("registerLead",registerLead);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To validate that create Register lead fro store cashback is throwing error on sending incorrect solution subtype ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_012_emptySolsubType() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", "");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("registerLead",registerLead);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }
    @Test(priority = 1,description = "To validate that create Register lead fro store cashback is throwing error on entering incorrect mid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_013_emptyMID() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid","");
        body.put("registerLead",registerLead);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

}

