package OCL.PGProfileUpdate.StoreCashBack;

import OCL.Subscription.FetchPlanSubscription;

import Services.CreateDeals.GetCreateLeadP4b;

import Services.StoreCashBack.StoreCashBackServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.assertions.CustomAssert;
import com.paytm.apitools.util.annotations.Owner;

import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class StoreCashBackDetailsApi extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(FetchPlanSubscription.class);
    public String Token ;


    Response subfetchResponse = null;
    StoreCashBackServices getStoreCashBackObj = new StoreCashBackServices();
    public static String solution="pg_profile_update";
    public static String entityType="INDIVIDUAL";
    public static String channel = "DIY_P4B_APP" ;
    public static String solutionSubType = "STORE_CASHBACK";
    public static String merchantMobileNumber = "5677567757";
    public static String mid = "QMkdEh06057310159464";
    public static String successMessage = "Request Submitted Successfully";
    String requestPath;
    Map<String, String> headers = new HashMap<String, String>();
    String XMWCookie;


    @BeforeClass
    public void generateToken(){
        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
        XMWCookie=findXMWTokenforPanel("7771216290","paytm@123");
        LOGGER.info("Token for PG : " + Token);


    }


    @Test(priority = 3,description = "To validate that cashback details  api gives error when token is empty ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001() throws JSONException {

        headers.put("session_token", " ");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddCashBackDeatilsRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode,401,"correct 200 api code recieved");


    }

    @Test(priority = 3,description = "To validate that cashback details api gives error when token is invlaid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002() throws JSONException {

        headers.put("session_token", "InvalidToken");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddCashBackDeatilsRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode,401,"correct 200 api code recieved");



    }
    @Test(priority = 3,description = "To validate that cashback details api gives error when token key not passed ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003() throws JSONException {

        //headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddCashBackDeatilsRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode,401,"correct api code recieved");



    }
    @Test(priority = 3,description = "To validate that cashback details api gives error when no header is passed", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004() throws JSONException {

        Map<String,String> headernull = new HashMap<String,String>();

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddCashBackDeatilsRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headernull, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode,401,"correct 200 api code recieved");



    }
    @Test(priority = 3,description = "To validate that cashback details  api gives error on empty MID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid"," ");

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddCashBackDeatilsRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode,400,"correct  api code recieved");



    }
    @Test(priority = 3,description = "To validate that cashback details api gives error when mid is incorrect", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid","Invalid");

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddCashBackDeatilsRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode,400,"correct api code recieved");



    }
    @Test(priority = 3,description = "To validate that cashback details api gives error on sending incorrect MID key", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("midInvaldkey",mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddCashBackDeatilsRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode,400,"correct api code recieved");



    }
    @Test(priority = 3,description = "To validate that error is recieved when token is passed as null", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008() throws JSONException {

        try{

        headers.put("session_token", "null");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");


        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddCashBackDeatilsRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode,401,"correct api code recieved");

        }catch(Exception e){
            e.printStackTrace();
        }



    }
    @Test(priority = 3,description = "To validate that cashback details api gives error when Solution Sub type is invalid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_BUGID_COP_19861() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", "InvcalidsolutionSubType");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddCashBackDeatilsRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode,400,"correct 200 api code recieved");



    }



    @Test(priority = 2,description = "To validate that store details api gives error when channel is invalid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_0010() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");


        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", "invalid");
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddCashBackDeatilsRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "expects 400 api code ");

    }

    @Test(priority = 2,description = "To validate that add store details gives error when entity type is invlaid entity trype", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_011() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");


        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", solution);
        params.put("entityType", "InvalidEntity");
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddCashBackDeatilsRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct 400 api code recieved");

    }

    @Test(priority = 2,description = "To validate thatstore details api  gives error when solution is invlaid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_012() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");


        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "Invlaid");
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddCashBackDeatilsRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct api code recieved");

    }

    @Test(priority = 2,description = "To validate that store details api gives error when params are empty ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_013() throws JSONException {


        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");


        Map<String, String> params = new HashMap<String, String>();


        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/AddCashBackDeatilsRequest.json";
        subfetchResponse = getStoreCashBackObj.StoreCashBackResponse(requestPath, headers, body, params);

        int httpcode = subfetchResponse.getStatusCode();

        CustomAssert.assertEquals(httpcode, 400, "correct 400 api code recieved");

    }

}
