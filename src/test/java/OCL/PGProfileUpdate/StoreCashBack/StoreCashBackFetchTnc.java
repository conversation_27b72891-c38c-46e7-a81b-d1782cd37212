package OCL.PGProfileUpdate.StoreCashBack;


import OCL.Subscription.FetchPlanSubscription;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Services.CreateDeals.GetCreateLeadP4b;
import Services.MechantService.MiddlewareServices;
import Services.StoreCashBack.StoreCashBackServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.assertions.CustomAssert;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class StoreCashBackFetchTnc extends BaseMethod{


    private static final Logger LOGGER = LogManager.getLogger(FetchPlanSubscription.class);
    public String Token ;


    Response subfetchResponse = null;
    StoreCashBackServices getStoreCashBackObj = new StoreCashBackServices();
    GetCreateLeadP4b getCreateLeadP4b = new GetCreateLeadP4b();
    public static String solution="pg_profile_update";
    public static String entityType="PROPRIETORSHIP";
    public static String channel = "DIY_P4B_APP" ;
    public static String solutionSubType = "STORE_CASHBACK";
    public static String merchantMobileNumber = "9439454358";
    public static String mid = "yrVUnP86754564543160";
    public static String successMessage = "Request Submitted Successfully";
    String registerLead = "true";
    String triggerValue = "3";
    String leadId;
    String requestPath;
    String md5Key;
    String identifier;


    String XMWCookie;


    @BeforeClass
    public void generateToken(){
        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
        XMWCookie=findXMWTokenforPanel("7771216290","paytm@123");
        LOGGER.info("Token for PG : " + Token);


    }

    @Test(priority = 1,description = "To validate that create Register lead fro store cashback is working sucessfully", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_001_GenerateLead() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");



        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("registerLead",registerLead);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,200);
        CustomAssert.assertEquals(subfetchResponse.jsonPath().getString("displayMessage"),successMessage,"Lead created sucessfully for store  cashback");
        leadId =subfetchResponse.jsonPath().getString("leadId");

    }



    @Test(priority = 4,description = "To check that fetch TNC for cash back store  throw error when token is invalid ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", "Invalid");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }

    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow thorw eror when token is of different env ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", "01ee0f34-2978-5fa4-8a79-f9eb9829847d");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,200);
        md5Key = subfetchResponse.jsonPath().getString("md5Key");
        identifier = subfetchResponse.jsonPath().getString("identifier");


    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow throw error when  valid Token is passed with space ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token+" ");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow  when valid token is passed with space in start", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", " "+Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow throw error when speial charatcer is added to valid param", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token+ "@");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow thorw error when valid token is passed with special character at start ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", "@"+Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow throw error when valid token is passed with multiple special characters ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token+"##$%#%$#$%#$%");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow thorw wrror when special characters are passed as token ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", "&^%^&%&%&%%^%&&%^&%%%&^");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow when invalid chacater only token is passed ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", "InvalidToken");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow throw error when token is not passed", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", " ");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store flow throw error when token key is not passed ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put(" ", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow throw error when token key value is not passed", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        //headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow throw error when header key is not pased in api", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

//        headers.put("session_token", Token);
//        headers.put("version","7.3.0");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("accept", "*/*");
//        headers.put("Accept-Language", "en");
//        headers.put("Content-Type", "application/json");
//        headers.put("androidId", "AashitAndroid");
//        headers.put("browserName", "chrome");
//        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow throw error when lead id is invalid ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", "Invalid");

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow throw error when lead id is passed empty", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", " ");

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow throw error when lead key is not passed", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_0016() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put(" ", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow  throw error when params are not passed", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
       // params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);



    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow work fine when all fields are valid ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_018() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,200);
        md5Key = subfetchResponse.jsonPath().getString("md5Key");
        identifier = subfetchResponse.jsonPath().getString("identifier");


    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow get md5Key key not null when all fields are valid ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_0019() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,200);
        md5Key = subfetchResponse.jsonPath().getString("md5Key");
        identifier = subfetchResponse.jsonPath().getString("identifier");
        Assert.assertNotNull(md5Key);


    }


    @Test(priority = 4,description = "To check that fetch TNC for cash back store  flow given identifier not null", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_020() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        subfetchResponse= getStoreCashBackObj.StoreCashBackResponse(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,200);
        md5Key = subfetchResponse.jsonPath().getString("md5Key");
        identifier = subfetchResponse.jsonPath().getString("identifier");
        Assert.assertNotNull(identifier);


    }


}
