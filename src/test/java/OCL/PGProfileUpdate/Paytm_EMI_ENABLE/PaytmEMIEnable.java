package OCL.PGProfileUpdate.Paytm_EMI_ENABLE;

import OCL.Subscription.FetchPlanSubscription;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Services.InstrumentsEnableDisable.InstrumentEnableDisableServices;
import Services.MechantService.MiddlewareServices;
import Services.StoreCashBack.StoreCashBackServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.assertions.CustomAssert;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class PaytmEMIEnable extends BaseMethod {


    private static final Logger LOGGER = LogManager.getLogger(FetchPlanSubscription.class);
    public String Token ;


    Response subfetchResponse = null;
    StoreCashBackServices getStoreCashBackObj = new StoreCashBackServices();
    InstrumentEnableDisableServices instrumentEnableDisableServices= new InstrumentEnableDisableServices();

    public static String solution="pg_profile_update";
    public static String entityType="INDIVIDUAL";
    public static String channel = "DIY_P4B_APP";
    public static String solutionSubType = "INSTRUMENT_ENABLE";
    public static String solutionTypeLevel3 = "PAYTM_EMI";
    public static String merchantMobileNumber = "7060074461";
    public static String mid = "PTMEMI93318032185388";
    public static String successMessage = "Update Request Submitted Successfully";
    public static String isInstrumentEnableDisable = "true";
    String registerLead = "true";
    String triggerValue = "3";
    String leadId;
    String requestPath;
    String md5Key;
    String identifier;

    Map<String, String> headers = new HashMap<String, String>();
    String XMWCookie;


    @BeforeClass
    public void generateToken(){
        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
        XMWCookie=findXMWTokenforPanel("7771216290","paytm@123");
        LOGGER.info("Token for PG : " + Token);
        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");


    }


    @Test(priority = 1,description = "To validate that we are able to enable EMI paytm instrument ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_001_EnablePaytmEMI() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);
        params.put("solutionTypeLevel3", solutionTypeLevel3);


        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("isInstrumentEnableDisable",isInstrumentEnableDisable);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
        subfetchResponse= instrumentEnableDisableServices.InstrumentEnableDisableServices(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,200);
        CustomAssert.assertEquals(subfetchResponse.jsonPath().getString("displayMessage"),successMessage,"Lead created sucessfully for emi instrument enable");
        leadId =subfetchResponse.jsonPath().getString("leadId");

    }

    @Test(priority = 1,description = "To validate that we are able to enable EMI paytm instrument is giving correct response ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_001_EnablePaytmEMI_validateResponse() throws JSONException {

//        headers.put("session_token", Token);
//        headers.put("version","7.3.0");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("accept", "*/*");
//        headers.put("Accept-Language", "en");
//        headers.put("Content-Type", "application/json");
//        headers.put("androidId", "AashitAndroid");
//        headers.put("browserName", "chrome");
//        headers.put("browserVersion", "4.6.3");
//
//        Map<String,String> params=new HashMap<String,String>();
//        params.put("solution", solution);
//        params.put("entityType", entityType);
//        params.put("channel", channel);
//        params.put("solutionSubType", solutionSubType);
//        params.put("solutionTypeLevel3", solutionTypeLevel3);
//
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("mid",mid);
//        body.put("isInstrumentEnableDisable",isInstrumentEnableDisable);
//
//        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
//        subfetchResponse= instrumentEnableDisableServices.InstrumentEnableDisableServices(headers,body,params);
//
//        int httpcode = subfetchResponse.getStatusCode();

 //
        Assert.assertEquals(subfetchResponse.jsonPath().getString("displayMessage"),successMessage,"Lead created sucessfully for emi instrument enable");
        Assert.assertEquals(subfetchResponse.jsonPath().getString("statusCode"),"200","200");
        Assert.assertEquals(subfetchResponse.jsonPath().getString("leadId"),leadId,"lead id is correct");
        Assert.assertEquals(subfetchResponse.jsonPath().getString("nameMatchBelowThreshold"),"false","statsu is 200");
        Assert.assertEquals(subfetchResponse.jsonPath().getString("docUploadRequired"),"false","docUploadRequired is false");
        //leadId =subfetchResponse.jsonPath().getString("leadId");

    }



    @Test(priority = 1,description = "To validate that we are getting correct error message when enable lead is in progress", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_002_EnablePaytmEMI() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);
        params.put("solutionTypeLevel3", solutionTypeLevel3);


        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("isInstrumentEnableDisable",isInstrumentEnableDisable);

        requestPath = "MerchantService/V1/profile/update/StoreCashBack/StoreCashBackRequest.json";
        subfetchResponse= instrumentEnableDisableServices.InstrumentEnableDisableServices(headers,body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);
        CustomAssert.assertEquals(subfetchResponse.jsonPath().getString("displayMessage"),"Your details could not be saved. We already have a application in process, please continue with same.","correct message recieved");
        //leadId =subfetchResponse.jsonPath().getString("leadId");

    }


    @Test(priority = 1,description = "To check that lead is submitted sucessfully", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_validatePanelData() throws JSONException {

        MiddlewareServices middlewareServicesObject = new MiddlewareServices();

        FetchLead v1FetchLeadObj = new FetchLead(leadId);

        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj,XMWCookie);

        System.out.println(v1FetchLeadResp.asString());
        // Assert.assertEquals(subfetchResponse.jsonPath().getString("storeDealDetails.1[0]."),successMessage);

        Assert.assertEquals(v1FetchLeadResp.jsonPath().getString("leadDetails.businessEntityDetails.solutionTypeLevel2"),solutionSubType);
        Assert.assertEquals(v1FetchLeadResp.jsonPath().getString("leadDetails.businessEntityDetails.solution"),solution);
        Assert.assertEquals(v1FetchLeadResp.jsonPath().getString("leadDetails.businessEntityDetails.solutionTypeLevel3"),solutionTypeLevel3);
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.businessEntityDetails.category"));
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.businessEntityDetails.subCategory"));
        //Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.leadInfo.source"),source);
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.businessOwnerDetailList.mobileNumber"),merchantMobileNumber);
        Assert.assertEquals(v1FetchLeadResp.jsonPath().getString("leadDetails.leadInfo.leadNumber"),leadId);


//
//        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.StoreEmail"),email);
//        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.storeNumber"),storeNumber);
//        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.storeDisplayName"),storeDisplayName);
//        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.pgMid"),mid);
//        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.leadStatus"),"Approved");
//        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.shopId"),shopId);
//        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.instoreCategory"),instoreCategory);
//        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.businesslat"),businesslat);
//        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.businessLong"),businessLong);




    }

    @Test(priority = 1,description = "To check that lead is submitted sucessfully and timeline is correctly saved at panel", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_ValidateTimelineDetails() throws JSONException {

        MiddlewareServices middlewareServicesObject = new MiddlewareServices();

        FetchLead v1FetchLeadObj = new FetchLead(leadId);

        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj,XMWCookie);

        System.out.println(v1FetchLeadResp.asString());
        // Assert.assertEquals(subfetchResponse.jsonPath().getString("storeDealDetails.1[0]."),successMessage);

        //validating lead created stage in timeline
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].subStage"),"LEAD_CREATED");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].stage"),"GG");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].isActive"),"false");


        //validating Document uploaded stage in timeline
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].subStage"),"LEAD_POSTED");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].stage"),"GG");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].isActive"),"false");



        //validating lead posted stage in timeline
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("timelineDetail[1].subStage"),"LEAD_POSTED");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[1].stage"),"GG");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[1].isActive"),"false");

        //validating NOTIFICATION_SUCCESS stage in timeline
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].subStage"),"NOTIFICATION_SUCCESS");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].stage"),"NOTIFICATION");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].isActive"),"true");

        //validating MAQUETTE_FRAUD_CHECK_SUCCESS stage in timeline
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].subStage"),"MAQUETTE_FRAUD_CHECK_SUCCESS");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].stage"),"MAQUETTE_FRAUD_CHECK");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].isActive"),"true");

        //validating PG_CALL_BACK_SUCCESS stage in timeline
        CustomAssert.assertEquals(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].subStage"),"PG_CALL_BACK_SUCCESS","PG_CALL_BACK_SUCCESS");
        CustomAssert.assertEquals(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].stage"),"PG_CALLBACK","PG callback stage is correctly processed");
        CustomAssert.assertEquals(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].isActive"),"true","");


        //validating LEAD_SUCCESSFULLY_CLOSED stage in timeline
        CustomAssert.assertEquals(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].subStage"),"LEAD_SUCCESSFULLY_CLOSED","LEAD_SUCCESSFULLY_CLOSED");
        CustomAssert.assertEquals(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].stage"),"LEAD_CLOSED","lead is closed");
        CustomAssert.assertEquals(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].isActive"),"true","");
    }


    @Test(priority = 1,description = "To check that lead is submitted sucessfully and timeline is correctly saved in store info", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_ValidateStoreInfo() throws JSONException {

        MiddlewareServices middlewareServicesObject = new MiddlewareServices();

        FetchLead v1FetchLeadObj = new FetchLead(leadId);

        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj,XMWCookie);

        System.out.println(v1FetchLeadResp.asString());
        // Assert.assertEquals(subfetchResponse.jsonPath().getString("storeDealDetails.1[0]."),successMessage);

        //validating lead created stage in timeline
//        CustomAssert.assertEquals(v1FetchLeadResp.jsonPath().getString("leadDetails.businessOwnerDetailList.mobileNumber"),merchantMobileNumber,"correct mobile number is saved");
        CustomAssert.assertEquals(v1FetchLeadResp.jsonPath().getString("leadDetails.storeInfo.uuid"),leadId,"correct lead id");


    }








}