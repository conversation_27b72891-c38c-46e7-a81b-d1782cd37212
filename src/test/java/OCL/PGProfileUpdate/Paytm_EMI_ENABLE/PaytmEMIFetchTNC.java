package OCL.PGProfileUpdate.Paytm_EMI_ENABLE;

import OCL.Individual.ProfileUpdate.FlowMerchantReactivation;
import Request.MerchantService.v1.profile.update.Commissiontncs;
import Request.oAuth.oAuthWormhole.CreateUser;
import Services.MechantService.MiddlewareServices;
import Services.PG.PGServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class PaytmEMIFetchTNC extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(FlowMerchantReactivation.class);
    //MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    PGServices pgServicesObject = new PGServices();
    public String Token ;

    public static String ApplicantToken = "";
    public static String MID = "";
    public static String MobileNo = "";
    public static String App1CustId = "";
    public static String LeadStatus = "";
    public static String requestID = "";
    PGServices createMerchant50k = new PGServices();
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    public static String LeadId = "";
    public static String merchantMobileNumber = "9879879879";
    public static String mid = "Salon083765602579633";
    Map<String, String> headers = new HashMap<String, String>();

    @BeforeClass
    public void generateToken(){
        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
        XMWCookie=findXMWTokenforPanel("7771216290","paytm@123");
        LOGGER.info("Token for PG : " + Token);
        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");


    }


    @Test(priority = 0, description = "Create Applicant on Oauth", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_CreateApplicantOauth() {
        //generating new Number
        Utilities accObj = new Utilities();
        MobileNo = accObj.randomMobileNumberGenerator();
        LOGGER.info("New Number is : " + MobileNo);
        CreateUser OauthObj = new CreateUser();
        OauthObj.setHeader("Content-Type", "application/json");
        OauthObj.getProperties().setProperty("mobile", MobileNo);
        OauthObj.getProperties().setProperty("loginPassword", "paytm@123");
        Response OauthResp = OauthObj.callAPI();
        int StatusCode = OauthResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }




    @Test(priority = 0, description = "Get Commision T&C", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC01_GetCommisonWithEmptySession() {
        Commissiontncs CommissiontncsObj = new Commissiontncs();

        CommissiontncsObj.setHeader("Content-Type", "application/json");
        CommissiontncsObj.setHeader("session_token",Token);

        CommissiontncsObj.addFormParameter("solution", "pg_profile_update");
        CommissiontncsObj.addFormParameter("entityType","INDIVIDUAL");
        CommissiontncsObj.addFormParameter("mid", MID);
        CommissiontncsObj.addFormParameter("channel", "UMP_WEB");
        CommissiontncsObj.addFormParameter("solutionSubType", "INSTRUMENT_ENABLE");
        CommissiontncsObj.addFormParameter("solutionTypeLevel3", "PAYTM_EMI");

        Response CommissiontncsResp = CommissiontncsObj.callAPI();
        int StatusCode = CommissiontncsResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
}
