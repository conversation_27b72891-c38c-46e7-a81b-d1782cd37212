package OCL.PGProfileUpdate.DisplayNameUpdate;

import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class DisplayNameUpdateNegativeScenario extends BaseMethod {


    String Token;
    String MerchantNum = "6753767037";
    String requestPath;
    Response fetchResponse = null;
    Services.PgProfileUpdate.DisplayNameUpdate displayNameUpdate = new Services.PgProfileUpdate.DisplayNameUpdate();
    Map<String, String> headers = new HashMap<String, String>();
    public static String solution = "pg_profile_update";
    public static String solutionSubType = "DISPLAY_NAME_UPDATE";
    public static String entityType = "INDIVIDUAL";
    public static String channel = "DIY_P4B_APP";
    public static String mid = "qxkeqr00140205274882";
    public static String successMessage = "Request Submitted Successfully";
    public static String displayName = "Pizza Box new displayName";

    @BeforeClass
    public void BeforeOperatiosn() {

        Token = ApplicantToken(MerchantNum, "paytm@123");
        XMWCookie = findXMWTokenforPanel("7771216290", "paytm@123");

        headers.put("session_token", Token);
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

    }
    @Test(priority = 1, description = "To validate that create lead without display name must throw error", groups = {"Regression"})
    public void TC1_CreateLeadWithoutDisplayName() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 400);
    }

    @Test(priority = 1, description = "To validate that create lead without mid must throw error", groups = {"Regression"})
    public void TC2_CreateLeadWithoutMid() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", "");
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", displayName);

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 400);
    }

    @Test(priority = 1, description = "To validate that create lead without solution must throw error", groups = {"Regression"})
    public void TC3_CreateLeadWithoutSolution() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", "");
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", displayName);

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 400);
    }

    @Test(priority = 1, description = "To validate that create lead without solution sub type must throw error", groups = {"Regression"})
    public void TC4_CreateLeadWithoutSolutionSubType() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", "");

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", displayName);

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 400);
    }

    @Test(priority = 1, description = "To validate that create lead without solution sub type must throw error", groups = {"Regression"})
    public void TC5_CreateLeadWithoutToken() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", "");

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", displayName);

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 400);
    }

}
