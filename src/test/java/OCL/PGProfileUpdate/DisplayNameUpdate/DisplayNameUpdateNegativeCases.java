package OCL.PGProfileUpdate.DisplayNameUpdate;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import TestingLogics.RestAssuredRequestUtil;
import com.amazonaws.services.cloudfront.model.Headers;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;


public class DisplayNameUpdateNegativeCases extends BaseMethod{

    String Token;
    String MerchantNum = "6753767037";
    String requestPath;
    Response response;
    RestAssuredRequestUtil request;
    String requestBody;

  //  String api_ep = "https://goldengate-staging7.paytm.com/MerchantService/v1/profile/update" ;
    public static final String API_ep1 = "/MerchantService/v1/profile/update/";


    Response fetchResponse = null;
    Services.PgProfileUpdate.DisplayNameUpdate displayNameUpdate = new Services.PgProfileUpdate.DisplayNameUpdate();
    Map<String, String> headers = new HashMap<String, String>();
    public static String solution = "pg_profile_update";
    public static String solutionSubType = "DISPLAY_NAME_UPDATE";
    public static String entityType = "INDIVIDUAL";
    public static String channel = "DIY_P4B_APP";
    public static String mid = "qxkeqr00140205274882";
    public static String successMessage = "Request Submitted Successfully";
    public static String displayName = "Pizza Box new displayName";

    @BeforeClass
    public void BeforeOperatiosn() {

        Token = ApplicantToken(MerchantNum, "paytm@123");
        XMWCookie = findXMWTokenforPanel("7771216290", "paytm@123");

        headers.put("session_token", Token);
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

    }

    @Test(priority = 1, description = "To validate that create lead with session_token empty must throw error", groups = {"Regression"})
    public void TC1_CreateLeadWithSession_tokenEmpty() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", "");

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 401);

    }

    @Test(priority = 2, description = "To validate that create lead with Session_token not passed", groups = {"Regression"})
    public void TC2_CreateLeadWithSession_tokenNotPassed() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 401);

    }


    @Test(priority = 3, description = "To validate that create lead with invalid session token", groups = {"Regression"})
    public void TC3_CreateLeadWithDisplayNameEmpty() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 4, description = "To validate that create lead with display name empty must throw error", groups = {"Regression"})
    public void TC4_CreateLeadWithDisplayNameEmpty() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 5, description = "To validate that create lead with display name not passed", groups = {"Regression"})
    public void TC5_CreateLeadWithoutDisplayName() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 6, description = "To validate that create lead with display name passed other than string", groups = {"Regression"})
    public void TC6_CreateLeadWithoutDisplayEmpty() throws Exception  {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "123*#");


        System.out.println("\nWorking fine 1 \n");

        /*
        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = DisplayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);
*/
       // response = request.postRequest(String.format(ApiEndPoint.API_V1_ADMIN_Location), headers, params, body);
        response = request.postRequest_withParam(API_ep1, headers, params, body);

        System.out.println("\n working this \n");
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);

    }

    @Test(priority = 7, description = "To validate that create lead with pgmid emoty", groups = {"Regression"})
    public void TC7_CreateLeadWithPgmidEmpty() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", "");
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ronit");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 8, description = "To validate that create lead with pgmid not Passed", groups = {"Regression"})
    public void TC8_CreateLeadWithPgmidNotPassed() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        //body.put("mid", "");
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ronit");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 9, description = "To validate that create lead with pgmid is wrong", groups = {"Regression"})
    public void TC9_CreateLeadWithPgmidWrong() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", "abc");
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ronit");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }


    @Test(priority = 10, description = "To validate that create lead with displayNameManuallyChanged emoty", groups = {"Regression"})
    public void TC10_CreateLeadWithDisplayNameManuallyChangedEmpty() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "");
        body.put("displayName", "Ronit");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 11, description = "To validate that create lead with displayNameManuallyChanged not Passed", groups = {"Regression"})
    public void TC11_CreateLeadWithDisplayNameManuallyChangedNotPassed() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", "");
        //body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ronit");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 12, description = "To validate that create lead with displayNameManuallyChanged is wrong", groups = {"Regression"})
    public void TC12_CreateLeadWithDisplayNameManuallyChangedWrong() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "123");
        body.put("displayName", "Ronit");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 13, description = "To validate that create lead with solution emoty", groups = {"Regression"})
    public void TC13_CreateLeadWithSolutionEmpty() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", "");
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

    /*
        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "123");
        body.put("displayName", "Ronit");


         */

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 14, description = "To validate that create lead with solution not Passed", groups = {"Regression"})
    public void TC14_CreateLeadWithSolutionNotPassed() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

       // params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

      /*
        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "123");
        body.put("displayName", "Ronit");


         */

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 15, description = "To validate that create lead with solution is wrong", groups = {"Regression"})
    public void TC15_CreateLeadWithSolutionWrong() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution +"i");
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

       /*
        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "123");
        body.put("displayName", "Ronit");


         */

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }


    @Test(priority = 16, description = "To validate that create lead with channel emoty", groups = {"Regression"})
    public void TC16_CreateLeadWithChannelEmpty() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", "");
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

     /*
        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "123");
        body.put("displayName", "Ronit");


         */

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");



        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 17, description = "To validate that create lead with channel not Passed", groups = {"Regression"})
    public void TC17_CreateLeadWithChannelNotPassed() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
       // params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

       /*
        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "123");
        body.put("displayName", "Ronit");


         */

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");



        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 18, description = "To validate that create lead with Channel is wrong", groups = {"Regression"})
    public void TC18_CreateLeadWithChannelWrong() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel+"i");
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

       /*
        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "123");
        body.put("displayName", "Ronit");


         */

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 19, description = "To validate that create lead with EntityType emoty", groups = {"Regression"})
    public void TC19_CreateLeadWithEntityTypeEmpty() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", "");
        params.put("solutionSubType", solutionSubType);

        /*
        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "123");
        body.put("displayName", "Ronit");


         */

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");



        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 20, description = "To validate that create lead with EntityType not Passed", groups = {"Regression"})
    public void TC20_CreateLeadWithEntityTypeNotPassed() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
       // params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

      /*
        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "123");
        body.put("displayName", "Ronit");


         */

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");



        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 21, description = "To validate that create lead with EntityType is wrong", groups = {"Regression"})
    public void TC21_CreateLeadWithEntityTypeWrong() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType+"i");
        params.put("solutionSubType", solutionSubType);
/*
        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "123");
        body.put("displayName", "Ronit");


         */

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");



        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 22, description = "To validate that create lead with SolutionSubType emoty", groups = {"Regression"})
    public void TC22_CreateLeadWithSolutionSubTypeEmpty() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", "");

        /*
        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "123");
        body.put("displayName", "Ronit");


         */

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");



        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 23, description = "To validate that create lead with SolutionSubType not Passed", groups = {"Regression"})
    public void TC23_CreateLeadWithSolutionSubTypeNotPassed() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        //params.put("solutionSubType", solutionSubType);

       /*
        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "123");
        body.put("displayName", "Ronit");


         */

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 24, description = "To validate that create lead with SolutionSubType is wrong", groups = {"Regression"})
    public void TC24_CreateLeadWithSolutionSubTypeWrong() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType+"i");

        /*
        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "123");
        body.put("displayName", "Ronit");


         */

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");

        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 25, description = "To validate that create lead with channel emoty", groups = {"Regression"})
    public void TC25_CreateLeadWithChannelEmpty() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", "");
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

     /*
        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "123");
        body.put("displayName", "Ronit");


         */

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");



        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 26, description = "To validate that create lead with channel not Passed", groups = {"Regression"})
    public void TC26_CreateLeadWithChannelNotPassed() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        // params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

       /*
        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "123");
        body.put("displayName", "Ronit");


         */

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");



        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 27, description = "To validate that create lead with Channel is wrong", groups = {"Regression"})
    public void TC27_CreateLeadWithChannelWrong() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel+"i");
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

       /*
        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "123");
        body.put("displayName", "Ronit");


         */

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 400);

    }

    @Test(priority = 28, description = "To validate that create lead with invalid Session_token not passed", groups = {"Regression"})
    public void TC28_CreateLeadWith_invalid_Session_token() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token + "i");

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", "Ron");


        System.out.println("\nWorking fine 1 \n");

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpCode = fetchResponse.getStatusCode();
        System.out.println("\n Working fine 2");

        Assert.assertEquals(httpCode, 401);

    }




}
