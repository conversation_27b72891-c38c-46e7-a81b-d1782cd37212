package OCL.PGProfileUpdate.DisplayNameUpdate;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class DisplayNameUpdate extends BaseMethod {

    String Token;
    String MerchantNum = "6753767037";
    String requestPath;
    String leadId;
    Response fetchResponse = null;
    String DocPath = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";
    Services.PgProfileUpdate.DisplayNameUpdate displayNameUpdate = new Services.PgProfileUpdate.DisplayNameUpdate();
    Map<String, String> headers = new HashMap<String, String>();
    public static String solution = "pg_profile_update";
    public static String solutionSubType = "DISPLAY_NAME_UPDATE";
    public static String entityType = "INDIVIDUAL";
    public static String channel = "DIY_P4B_APP";
    public static String mid = "qxkeqr00140205274882";
    public static String successMessage = "Request Submitted Successfully";
    public static String displayName = "Pizza Box new displayName";

    @BeforeClass
    public void BeforeOperatiosn() {

        Token = ApplicantToken(MerchantNum, "paytm@123");
        XMWCookie = findXMWTokenforPanel("7771216290", "paytm@123");

        headers.put("session_token", Token);
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

    }

    @Test(priority = 1, description = "To validate that display name update flow is working successfully", groups = {"Regression"})
    public void TC1_displayNameUpdateCreateLead() {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", mid);
        body.put("displayNameManuallyChanged", "true");
        body.put("displayName", displayName);

        requestPath = "MerchantService/v1/profile/update/DisplayNameUpdateRequest.json";
        fetchResponse = displayNameUpdate.displayNameUpdate(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 200);
        leadId = fetchResponse.jsonPath().getString("leadId");
    }

    @Test(priority = 1, description = "To check fetch doc status api is working", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC2_fetchDocStatus() throws JSONException, Exception {

        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);
        headers.put("channel", channel);


        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", "INDIVIDUAL");
        params.put("leadId", leadId);
        params.put("solutionSubType", solutionSubType);

        Response response = displayNameUpdate.fetchDocStatus(headers, params);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 1, description = "To check that upload document is working", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC3_uploadBusinessProofDocument() throws JSONException, Exception {
        String endPoint = P.API.get("businessProofDocument");
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);
        headers.put("channel", channel);


        params.put("solutionType", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("docType", "businessProofDocument");
        params.put("docProvided", "registrationProof_displayName");
        params.put("solutionTypeLevel2", solutionSubType);

        File uploadFile = new File(DocPath);
        Response resp2 = UploadDocInAPI(uploadFile, params, headers, endPoint);

        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }

    public Response UploadDocInAPI(File uploadFile, Map<String, String> params, Map<String, String> header, String endPoint) throws Exception {

        Response resp = null;
        String baseURI = P.API.get("api_url");

        RequestSpecification spec;

        try {
            spec = new RequestSpecBuilder().setBaseUri(baseURI).build();
            resp = RestAssured.given().multiPart(uploadFile).spec(spec).relaxedHTTPSValidation().queryParams(params).headers(header)
                    .post(endPoint);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resp;
    }


}
