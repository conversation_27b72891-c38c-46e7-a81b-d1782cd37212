package OCL.PGProfileUpdate.BankDetailUpdate;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.Revisit.LoanIntentfetchDocumentDetails;
import Services.AddBankCreateLead;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class BankDetailRevampV3 extends BaseMethod {

    String Token;
    Utilities util = new Utilities();
    String MerchantNum = "**********";
    String requestPath;
    String leadId;
    String bankAccountNumber = util.generateRandomBankAccountNumber();
    boolean nameMatchStatus;
    String lastFourDigits;
    Map<String, String> headers = new HashMap<String, String>();
    Map<String, String> body = new HashMap<String, String>();
    Response fetchResponse = null;
    public static String solution = "pg_profile_update";
    public static String solutionSubType = "BANK_DETAIL_UPDATE";
    public static String entityType = "INDIVIDUAL";
    public static String channel = "DIY_P4B_APP";
    public static String mid = "EttpOq47766959619749";
    public static String successMessage = "Request Submitted Successfully";
    String custId;
    String BankStatementDMSID;
    String wfsidBankUpdate;
    String DocPath = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";
    AddBankCreateLead addBankCreateLead = new AddBankCreateLead();
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    @BeforeClass
    public void BeforeOperations() throws Exception {
        Token = ApplicantToken(MerchantNum, "paytm@123");


        DBConnection dbConnection = new DBConnection();
        dbConnection.UpdateQueryToCloseLead(MerchantNum, "pg_profile_update");

        headers.put("session_token", Token);
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");
    }

    @Test(priority = 1, description = "To validate that add bank is working successfully", groups = {"Regression"})
    public void CreateLead_BankSubmit() {
        Map<String, String> params = new HashMap<String, String>();

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);
        // Map<String, Object> bankDetailsBody = new HashMap<String, Object>();
        body.put("mid", mid);
        body.put("businessName", "ANMOL JAIN");
        body.put("authorisedSignatory", "ANMOL JAIN");
        body.put("action", "BANK_SUBMIT");
        body.put("flowVersion", "BANK_DETAIL_UPDATE_V3");
        body.put("bankAccountNumber", bankAccountNumber);
        body.put("ifsc", "ICIC0001070");
        body.put("bankAccountHolderName", "Anmol jain");
        requestPath = "MerchantService/V1/profile/update/createLeadBankUpdateV3.json";
        fetchResponse = addBankCreateLead.addbankCreateLead(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();

        if (httpcode == 400 && fetchResponse.jsonPath().getString("displayMessage").equalsIgnoreCase("Oops! Something went wrong. Please try again later.")) {
            CreateLead_BankSubmit();
        } else {
            Assert.assertEquals(httpcode, 200);
            leadId = fetchResponse.jsonPath().getString("leadId");
        }
        lastFourDigits = bankAccountNumber.substring(bankAccountNumber.length() - 4);

    }

    @Test(priority = 2, description = "To validate that add bank is working successfully", groups = {"Regression"})
    public void CreateLead_BankConfirm() {
        Map<String, String> params = new HashMap<String, String>();

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);
        body.put("mid", mid);
        body.put("action", "BANK_CONFIRM");
        body.put("flowVersion", "BANK_DETAIL_UPDATE_V3");
        body.put("leadId", leadId);
        requestPath = "MerchantService/V1/profile/update/createLeadBankConfirm.json";
        fetchResponse = addBankCreateLead.addbankCreateLead(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();


        if (httpcode == 400 && fetchResponse.jsonPath().getString("displayMessage").equalsIgnoreCase("Oops! Something went wrong. Please try again later.")) {
            CreateLead_BankConfirm();
        } else {
            Assert.assertEquals(httpcode, 200);
            if (fetchResponse.jsonPath().getString("nameMatchSuccess").equalsIgnoreCase("true")) {
                nameMatchStatus = true;
            } else {
                nameMatchStatus = false;
            }

        }

    }


    //Required when name match is false
    @Test(priority = 3, description = "To check that upload document is working", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void uploadBankProof() throws JSONException, Exception {
        if (nameMatchStatus) {
            System.out.println("Name Match is true");
        } else {
            String endPoint = P.API.get("UpdateBankProof");
            Map<String, String> params = new HashMap<String, String>();
            headers.put("Content-Type", "multipart/form-data");
            headers.put("session_token", Token);
            headers.put("channel", channel);
            params.put("solutionType", solution);
            params.put("channel", channel);
            params.put("entityType", entityType);
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "1");
            params.put("docType", "bankProof");
            params.put("docProvided", "BankStatement");
            params.put("solutionTypeLevel2", solutionSubType);
            File uploadFile = new File(DocPath);
            Response resp2 = UploadDocInAPI(uploadFile, params, headers, endPoint);
            int statusCode2 = resp2.getStatusCode();
            Assert.assertEquals(statusCode2, 200);
        }
    }

    public Response UploadDocInAPI(File uploadFile, Map<String, String> params, Map<String, String> header, String endPoint) throws Exception {
        Response resp = null;
        String baseURI = P.API.get("api_url");
        RequestSpecification spec;
        try {
            spec = new RequestSpecBuilder().setBaseUri(baseURI).build();
            resp = RestAssured.given().multiPart(uploadFile).spec(spec).relaxedHTTPSValidation().queryParams(params).headers(header)
                    .post(endPoint);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resp;
    }

    @Test(priority = 4, description = "To validate that add bank is working successfully", groups = {"Regression"})
    public void CreateLead_AuthSuccess() {
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);
        body.put("mid", mid);
        body.put("action", "AUTH_SUCCESS");
        body.put("flowVersion", "BANK_DETAIL_UPDATE_V3");
        body.put("leadId", leadId);
        requestPath = "MerchantService/V1/profile/update/createLeadBankConfirm.json";
        fetchResponse = addBankCreateLead.addbankCreateLead(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();

        if (httpcode == 400 && fetchResponse.jsonPath().getString("displayMessage").equalsIgnoreCase("Oops! Something went wrong. Please try again later.")) {
            CreateLead_AuthSuccess();
        }else
            Assert.assertEquals(httpcode, 200);

    }

    @Test(priority = 5, description = "To validate that add bank is working successfully", groups = {"Regression"})
    public void bankUpdateFetchDoc() {
        custId = GetResourceOwnerId(MerchantNum, "paytm@123");

        this.Token = BaseMethod.ApplicantToken(MerchantNum, "paytm@123");
        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();

        Map<String, String> headers = new HashMap<>();
        headers.put("session_token", Token); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "multipart/json");
        headers.put("version", "7.2.8"); // No change
        headers.put("deviceidentifier", "Xiaomi-RedmiNote9Pro-efc28fc97cede738"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solution", "diy_mco");
        queryParams.put("entityType", entityType);
        queryParams.put("leadId", leadId);
        queryParams.put("merchantCustId", custId);
        queryParams.put("channel", "DIY_P4B_APP");
        queryParams.put("solutionSubType", solutionSubType);

        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj, headers, queryParams);
        BankStatementDMSID = respObj.path("uploadedDocDetailsSet[0].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
        System.out.println(BankStatementDMSID);
    }

    @Test(priority = 6, description = "To validate that add bank is working successfully", groups = {"Regression"})
    public void bankUpdateQC() throws Exception {
        DBConnection dbConnectionObj = new DBConnection();
        int Ubmid = dbConnectionObj.getUserBusinessMappingId(MerchantNum, "pg_profile_update");
        dbConnectionObj.assignAgentViaDB("1152", Ubmid);
        Long bankUpdateWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);
        //Save value of bankUpdateWorkflowStatusId in string
        wfsidBankUpdate = String.valueOf(bankUpdateWorkflowStatusId);

        requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadBankUpdate.json";
        EditLead EditLeadObj = new EditLead(leadId, requestPath);
        EditLeadObj.getProperties().setProperty("uuidBankStatementPhoto", BankStatementDMSID);
        EditLeadObj.getProperties().setProperty("workflowStatusId", wfsidBankUpdate);
        EditLeadObj.getProperties().setProperty("reEnterAccountNumberLastFour", lastFourDigits);
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("action", "SUBMIT");
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("Cookie", XMWCookie);
        Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);

    }

}
