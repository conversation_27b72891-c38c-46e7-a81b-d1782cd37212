package OCL.PGProfileUpdate.BankDetailUpdate;
import Services.AddBankCreateLead;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import java.io.File;
import java.util.HashMap;
import java.util.Map;
public class BankDetailUpdateNegativeCases extends BaseMethod {
    String Token;
    String MerchantNum = "**********";
    String requestPath;
    Utilities util = new Utilities();
    String leadId;
    String bankAccountNumber = util.generateRandomBankAccountNumber();
    Map<String, String> headers = new HashMap<String, String>();
    Map<String, String> body = new HashMap<String, String>();
    Response fetchResponse = null;
    public static String solution = "pg_profile_update";
    public static String solutionSubType = "BANK_DETAIL_UPDATE";
    public static String entityType = "ASSOCIATION_OF_PERSONS";
    public static String channel = "DIY_P4B_APP";
    public static String mid = "XPwnjs55738647089267";
    public static String successMessage = "Request Submitted Successfully";
    String DocPath = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";
    AddBankCreateLead addBankCreateLead = new AddBankCreateLead();
    @BeforeClass
    public void BeforeOperations() {
        Token = ApplicantToken(MerchantNum, "paytm@123");
        XMWCookie = findXMWTokenforPanel("**********", "paytm@123");
        headers.put("session_token", Token);
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");
    }
    @Test(priority = 1, description = "To validate that create lead must give 401, when token is not passed", groups = {"Regression"})
    public void TC1_addBankCreateLeadWithoutToken() {
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", "");
        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);
        body.put("bankName", "ICICI bank LIMITED");
        body.put("bankAccountNumber", bankAccountNumber);
        body.put("ifsc", "ICIC0001645");
        body.put("bankAccountHolderName", "TOUCH WOOD LIMITED");
        body.put("mid", mid);
        body.put("newBankDetailFlow", "true");
        body.put("authorisedSignatory", "ANMOL JAIN");
        requestPath = "MerchantService/v1/profile/update/AddBankCreateLeadRequest.json";
        fetchResponse = addBankCreateLead.addbankCreateLead(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 401);
    }
    @Test(priority = 1, description = "To validate that create lead must throw error when MID is not passed", groups = {"Regression"})
    public void TC2_addBankCreateLeadWithoutMid() {
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);
        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);
        body.put("bankName", "ICICI bank LIMITED");
        body.put("bankAccountNumber", bankAccountNumber);
        body.put("ifsc", "ICIC0001645");
        body.put("bankAccountHolderName", "TOUCH WOOD LIMITED");
        body.put("mid", "");
        body.put("newBankDetailFlow", "true");
        body.put("authorisedSignatory", "ANMOL JAIN");
        requestPath = "MerchantService/v1/profile/update/AddBankCreateLeadRequest.json";
        fetchResponse = addBankCreateLead.addbankCreateLead(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 400);
    }
    @Test(priority = 1, description = "To validate that create lead must throw error when bank Account Number is not passed", groups = {"Regression"})
    public void TC3_addBankCreateLeadWithoutBankAccountNumber() {
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);
        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);
        body.put("bankName", "ICICI bank LIMITED");
        body.put("bankAccountNumber", "");
        body.put("ifsc", "ICIC0001645");
        body.put("bankAccountHolderName", "TOUCH WOOD LIMITED");
        body.put("mid", mid);
        body.put("newBankDetailFlow", "true");
        body.put("authorisedSignatory", "ANMOL JAIN");
        requestPath = "MerchantService/v1/profile/update/AddBankCreateLeadRequest.json";
        fetchResponse = addBankCreateLead.addbankCreateLead(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 400);
    }
    @Test(priority = 1, description = "To validate that create lead must throw error when bank details are not passed", groups = {"Regression"})
    public void TC4_addBankCreateLeadWithoutBankDetails() {
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);
        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);
        body.put("mid", mid);
        body.put("bankDetails", "");
        body.put("newBankDetailFlow", "true");
        body.put("authorisedSignatory", "ANMOL JAIN");
        requestPath = "MerchantService/v1/profile/update/AddBankCreateLeadRequest.json";
        fetchResponse = addBankCreateLead.addbankCreateLead(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 400);
    }
    @Test(priority = 1, description = "To check that upload document must throw error Without LeadId", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC5_uploadBankProofWithoutLeadId() throws JSONException, Exception {
        String endPoint = P.API.get("UpdateBankProof");
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "multipart/form-data");
        headers.put("session_token", Token);
        headers.put("channel", channel);
        params.put("solutionType", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionLeadId", "");
        params.put("pageNo", "1");
        params.put("docType", "bankProof");
        params.put("docProvided", "BankStatement");
        params.put("solutionTypeLevel2", solutionSubType);
        File uploadFile = new File(DocPath);
        Response resp2 = UploadDocInAPI(uploadFile, params, headers, endPoint);
        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }
    @Test(priority = 1, description = "To check that upload document must throw error Without solution Sub Type", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC6_uploadBankProofWithoutSolutionSubType() throws JSONException, Exception {
        String endPoint = P.API.get("UpdateBankProof");
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "multipart/form-data");
        headers.put("session_token", Token);
        headers.put("channel", channel);
        params.put("solutionType", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("docType", "bankProof");
        params.put("docProvided", "BankStatement");
        params.put("solutionTypeLevel2", "");
        File uploadFile = new File(DocPath);
        Response resp2 = UploadDocInAPI(uploadFile, params, headers, endPoint);
        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 400);
    }
    @Test(priority = 1, description = "To check that upload document must throw error Without entity Type", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC7_uploadBankProofWithoutEntityType() throws JSONException, Exception {
        String endPoint = P.API.get("UpdateBankProof");
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "multipart/form-data");
        headers.put("session_token", Token);
        headers.put("channel", channel);
        params.put("solutionType", solution);
        params.put("channel", channel);
        params.put("entityType", "");
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("docType", "bankProof");
        params.put("docProvided", "BankStatement");
        params.put("solutionTypeLevel2", solutionSubType);
        File uploadFile = new File(DocPath);
        Response resp2 = UploadDocInAPI(uploadFile, params, headers, endPoint);
        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 400);
    }
    @Test(priority = 1, description = "To check that upload document must throw error Without Token", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC8_uploadBankProofWithoutToken() throws JSONException, Exception {
        String endPoint = P.API.get("UpdateBankProof");
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "multipart/form-data");
        headers.put("session_token", "");
        headers.put("channel", channel);
        params.put("solutionType", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("docType", "bankProof");
        params.put("docProvided", "BankStatement");
        params.put("solutionTypeLevel2", solutionSubType);
        File uploadFile = new File(DocPath);
        Response resp2 = UploadDocInAPI(uploadFile, params, headers, endPoint);
        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 401);
    }
    @Test(priority = 1, description = "To check that upload document must throw error Without doc Type", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC9_uploadBankProofWithoutDocType() throws JSONException, Exception {
        String endPoint = P.API.get("UpdateBankProof");
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "multipart/form-data");
        headers.put("session_token", Token);
        headers.put("channel", channel);
        params.put("solutionType", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("docType", "");
        params.put("docProvided", "BankStatement");
        params.put("solutionTypeLevel2", solutionSubType);
        File uploadFile = new File(DocPath);
        Response resp2 = UploadDocInAPI(uploadFile, params, headers, endPoint);
        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }
    @Test(priority = 1, description = "To check that upload document must throw error Without doc Provided", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC10_uploadBankProofWithoutDocProvided() throws JSONException, Exception {
        String endPoint = P.API.get("UpdateBankProof");
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "multipart/form-data");
        headers.put("session_token", Token);
        headers.put("channel", channel);
        params.put("solutionType", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("docType", "bankProof");
        params.put("docProvided", "");
        params.put("solutionTypeLevel2", solutionSubType);
        File uploadFile = new File(DocPath);
        Response resp2 = UploadDocInAPI(uploadFile, params, headers, endPoint);
        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 400);
    }
    public Response UploadDocInAPI(File uploadFile, Map<String, String> params, Map<String, String> header, String endPoint) throws Exception {
        Response resp = null;
        String baseURI = P.API.get("api_url");
        RequestSpecification spec;
        try {
            spec = new RequestSpecBuilder().setBaseUri(baseURI).build();
            resp = RestAssured.given().multiPart(uploadFile).spec(spec).relaxedHTTPSValidation().queryParams(params).headers(header)
                    .post(endPoint);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resp;
    }
}

