package OCL.PGProfileUpdate.BankDetailUpdate;
import Services.AddBankCreateLead;
import Services.DBConnection.DBConnection;
import Services.Utilities.Utilities;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import java.io.File;
import java.util.HashMap;
import java.util.Map;
public class BankDetailUpdate extends BaseMethod {
    String Token;
    Utilities util = new Utilities();
    String MerchantNum = "**********";
    String requestPath;
    String leadId;
    String bankAccountNumber = util.generateRandomBankAccountNumber();
    String nameMatchStatus;
    Map<String, String> headers = new HashMap<String, String>();
    Map<String, String> body = new HashMap<String, String>();
    Response fetchResponse = null;
    public static String solution = "pg_profile_update";
    public static String solutionSubType = "BANK_DETAIL_UPDATE";
    public static String entityType = "INDIVIDUAL";
    public static String channel = "DIY_P4B_APP";
    public static String mid = "UsXLqD30348530846580";
    public static String successMessage = "Request Submitted Successfully";
    String DocPath = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";
    AddBankCreateLead addBankCreateLead = new AddBankCreateLead();
    @BeforeClass
    public void BeforeOperations() throws Exception {
        Token = ApplicantToken(MerchantNum, "paytm@123");
        //XMWCookie = findXMWTokenforPanel("**********", "paytm@123");


        DBConnection dbConnection = new DBConnection();
        dbConnection.UpdateQueryToCloseLead(MerchantNum, "pg_profile_update");

        headers.put("session_token", Token);
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");
    }
    @Test(priority = 1, description = "To validate that add bank is working successfully", groups = {"Regression"})
    public void TC1_addBankCreateLead() {
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);
        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);
        // Map<String, Object> bankDetailsBody = new HashMap<String, Object>();
        body.put("mid", mid);
        body.put("businessName", "ANMOL JAIN");
        //   body.put("bankDetails", bankDetailsBody);
        body.put("newBankDetailFlow", "true");
        body.put("authorisedSignatory", "ANMOL JAIN");
        body.put("bankName", "ICICI bank LIMITED");
        body.put("bankAccountNumber", bankAccountNumber);
        body.put("ifsc", "ICIC0001645");
        body.put("bankAccountHolderName", "TOUCH WOOD LIMITED");
        requestPath = "MerchantService/v1/profile/update/AddBankCreateLeadRequest.json";
        fetchResponse = addBankCreateLead.addbankCreateLead(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();

        if (httpcode != 200) {
            TC1_addBankCreateLead();
        } else {
            Assert.assertEquals(httpcode, 200);
            leadId = fetchResponse.jsonPath().getString("leadId");
            nameMatchStatus = fetchResponse.jsonPath().getString("nameMatchSuccess");
            System.out.println(leadId);
        }


    }
    //Required when name match is false
    @Test(priority = 1, description = "To check that upload document is working", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC2_uploadBankProof() throws JSONException, Exception {
        String endPoint = P.API.get("UpdateBankProof");
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "multipart/form-data");
        headers.put("session_token", Token);
        headers.put("channel", channel);
        params.put("solutionType", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("docType", "bankProof");
        params.put("docProvided", "BankStatement");
        params.put("solutionTypeLevel2", solutionSubType);
        File uploadFile = new File(DocPath);
        Response resp2 = UploadDocInAPI(uploadFile, params, headers, endPoint);
        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }
    public Response UploadDocInAPI(File uploadFile, Map<String, String> params, Map<String, String> header, String endPoint) throws Exception {
        Response resp = null;
        String baseURI = P.API.get("api_url");
        RequestSpecification spec;
        try {
            spec = new RequestSpecBuilder().setBaseUri(baseURI).build();
            resp = RestAssured.given().multiPart(uploadFile).spec(spec).relaxedHTTPSValidation().queryParams(params).headers(header)
                    .post(endPoint);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resp;
    }
    public static void main( String[] args )
    {
        System.out.println(generateKYCJWTToken());
    }
    public static String generateKYCJWTToken() {
        try {
            Algorithm buildAlgorithm = Algorithm.HMAC256("89CD7A268CDDB1D8995DE9EE1B5A85E394B498527BEBBF154A59DFE8DC21341D");
            JWTCreator.Builder builder = JWT.create().withClaim("kyc_profile_id", "1520342039000").withClaim("ts", System.currentTimeMillis());
            return "Bearer " + builder.sign(buildAlgorithm);
        } catch (Exception e) {
            return null;
        }
    }
}

