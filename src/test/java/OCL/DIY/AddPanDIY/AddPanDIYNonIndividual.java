package OCL.DIY.AddPanDIY;

import Services.AddPanDIY.AddPanDiy;
import Services.AddPanDIY.AddPanDiyNonIndividual;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/*
public class AddPanDIYNonIndividual extends BaseMethod {


    String Token;
    String MerchantNum = "6753767037";
    String requestPath;
    String leadId;
    Map<String, String> headers = new HashMap<String, String>();
    Response fetchResponse = null;
    public static String solution = "pg_profile_update";
    public static String solutionSubType = "ADD_PAN_DIY";
    public static String entityType = "INDIVIDUAL";
    public static String channel = "DIY_P4B_APP";
    public static String mid = "qxkeqr00140205274882";
    public static String successMessage = "Request Submitted Successfully";
    public static String pan = "**********";

    AddPanDiy addPanDIY = new AddPanDiy();
    AddPanDiyNonIndividual addPanDiyNonIndividual = new AddPanDiyNonIndividual();
    String DocPath = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";


    @BeforeClass
    public void BeforeOperatiosn() {

        Token = ApplicantToken(MerchantNum, "paytm@123");
        XMWCookie = findXMWTokenforPanel("7771216290", "paytm@123");

        headers.put("session_token", Token);
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

    }

    @Test(priority = 1, description = "To validate that add pan flow(for Non Individual PAN) is working successfully", groups = {"Regression"})
    public void TC1_addPanFlowForNonIndividualMerchant() {

        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", mid);
        body.put("pan", pan);
        body.put("nameAsPerPan", "TOUCH WOOD LIMITED");
        body.put("dob", "01/12/1999");
        body.put("fatherName", "xzyx");

        requestPath = "MerchantService/v1/profile/update/addPanRequest.json";
        fetchResponse = addPanDIY.addPanDIY(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 200);
        leadId = fetchResponse.jsonPath().getString("leadId");
    }

    @Test(priority = 1, description = "To validate that add bank is working successfully", groups = {"Regression"})
    public void TC2_addBankForNonIndividualPan() {

        Map<String, String> params = new HashMap<String, String>();
        Map<String, Object> body = new HashMap<String, Object>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);
        Map<String, String> bankDetailsBody = new HashMap<String, String>();
        bankDetailsBody.put("bankName", "ICICI bank LIMITED");
        bankDetailsBody.put("bankAccountNumber", "bankAccountNumber");
        bankDetailsBody.put("ifsc", "ICIC0001645");
        bankDetailsBody.put("bankAccountHolderName", "TOUCH WOOD LIMITED");

        body.put("bankDetails", bankDetailsBody);
        body.put("newBankDetailFlow", "true");
        body.put("authorisedSignatory", "ANMOL JAIN");
        body.put("businessName", "TOUCH WOOD LIMITED");
        body.put("businessType", "BUSINESS");
        body.put("finalDataSubmit", "true");

        requestPath = "MerchantService/v1/profile/update/AddBankPanRequest.json.json";
        fetchResponse = addPanDiyNonIndividual.addPanDIYNonIndividual(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 200);
        leadId = fetchResponse.jsonPath().getString("leadId");
    }


    @Test(priority = 1, description = "To validate final data submit is working successfully", groups = {"Regression"})
    public void TC3_finalDataSubmit() {

        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", mid);
        body.put("finalDataSubmit", "true");

        requestPath = "MerchantService/v1/profile/update/finalDataSubmit.json";
        fetchResponse = addPanDIY.addPanDIY(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 200);

    }

    @Test(priority = 1, description = "To check that upload document is working", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC4_uploadCOIDocument() throws JSONException, Exception {
        String endPoint = P.API.get("coi");
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);
        headers.put("channel", channel);


        params.put("solutionType", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("docType", "coi");
        params.put("docProvided", "COI");
        params.put("solutionTypeLevel2", solutionSubType);

        File uploadFile = new File(DocPath);
        Response resp2 = UploadDocInAPI(uploadFile, params, headers, endPoint);

        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }

    @Test(priority = 1, description = "To check that upload document is working", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC5_uploadPOIWBDocument() throws JSONException, Exception {
        String endPoint = P.API.get("coi");
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);
        headers.put("channel", channel);


        params.put("solutionType", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("docType", "POIAWB");
        params.put("docProvided", "RentalAgreement_poiawb");
        params.put("solutionTypeLevel2", solutionSubType);

        File uploadFile = new File(DocPath);
        Response resp2 = UploadDocInAPI(uploadFile, params, headers, endPoint);

        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }

    @Test(priority = 1, description = "To check that upload document is working", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC6_uploadPOAWTNDocument() throws JSONException, Exception {
        String endPoint = P.API.get("coi");
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);
        headers.put("channel", channel);


        params.put("solutionType", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("docType", "POAWTN");
        params.put("docProvided", "franchiseeAgreement_poawtn");
        params.put("solutionTypeLevel2", solutionSubType);

        File uploadFile = new File(DocPath);
        Response resp2 = UploadDocInAPI(uploadFile, params, headers, endPoint);

        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }

    @Test(priority = 1, description = "To check that upload document is working", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC7_uploadBusinessProof() throws JSONException, Exception {
        String endPoint = P.API.get("coi");
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);
        headers.put("channel", channel);


        params.put("solutionType", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("docType", "businessProof");
        params.put("docProvided", "accountStatement_business");
        params.put("solutionTypeLevel2", solutionSubType);

        File uploadFile = new File(DocPath);
        Response resp2 = UploadDocInAPI(uploadFile, params, headers, endPoint);

        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }

    @Test(priority = 1, description = "To check that upload document is working", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC8_uploadBankProof() throws JSONException, Exception {
        String endPoint = P.API.get("coi");
        Map<String, String> params = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);
        headers.put("channel", channel);


        params.put("solutionType", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("docType", "bankProof");
        params.put("docProvided", "BankStatement");
        params.put("solutionTypeLevel2", solutionSubType);

        File uploadFile = new File(DocPath);
        Response resp2 = UploadDocInAPI(uploadFile, params, headers, endPoint);

        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }

    public Response UploadDocInAPI(File uploadFile, Map<String, String> params, Map<String, String> header, String endPoint) throws Exception {

        Response resp = null;
        String baseURI = P.API.get("api_url");

        RequestSpecification spec;

        try {
            spec = new RequestSpecBuilder().setBaseUri(baseURI).build();
            resp = RestAssured.given().multiPart(uploadFile).spec(spec).relaxedHTTPSValidation().queryParams(params).headers(header)
                    .post(endPoint);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resp;
    }


}
 */