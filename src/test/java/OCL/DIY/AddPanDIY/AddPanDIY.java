package OCL.DIY.AddPanDIY;


import Services.AddPanDIY.AddPanDiy;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
/*
public class AddPanDIY extends BaseMethod {
    String Token;
    String MerchantNum = "";
    String requestPath;
    String leadId;
    Map<String, String> headers = new HashMap<String, String>();
    Response fetchResponse = null;
    public static String solution = "pg_profile_update";
    public static String solutionSubType = "ADD_PAN_DIY";
    public static String entityType = "INDIVIDUAL";
    public static String channel = "DIY_P4B_APP";
    public static String mid = "LweNrV66518835034894";
    public static String successMessage = "Request Submitted Successfully";
    public static String pan = "**********";

    AddPanDiy addPanDIYIndividual = new AddPanDiy();

    @BeforeClass
    public void BeforeOperatiosn() {

        Token = ApplicantToken(MerchantNum, "paytm@123");
        XMWCookie = findXMWTokenforPanel("7771216290", "paytm@123");

        headers.put("session_token", Token);
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


    @Test(priority = 1, description = "To validate that add pan flow(for Individual PAN) is working sucessfully", groups = {"Regression"})
    public void TC1_addPanFlowForIndividualMerchant() {

        Map<String, String> params = new HashMap<String, String>();
        Map<String, String> body = new HashMap<String, String>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionSubType", solutionSubType);

        body.put("mid", mid);
        body.put("pan", pan);
        body.put("nameAsPerPan", "TOUCH WOOD LIMITED");
        body.put("dob", "01/12/1999");
        body.put("fatherName", "xzyx");

        requestPath = "MerchantService/v1/profile/update/addPanRequest.json";
        fetchResponse = addPanDIYIndividual.addPanDIY(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 200);
        leadId = fetchResponse.jsonPath().getString("leadId");
    }


}
*/
