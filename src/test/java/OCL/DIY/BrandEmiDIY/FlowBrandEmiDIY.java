package OCL.DIY.BrandEmiDIY;

import Request.MerchantService.v1.brandEmi.getAllBrands.GetAllBrands;
import Request.MerchantService.v1.brandEmi.getAllBrands.dealer.validate.ValidateDealer;
import Request.MerchantService.v1.profile.update.lead.Status;
import Request.MerchantService.v1.sdMerchant.CreateLeadDIYCashAtPos;
import Request.MerchantService.v1.sdMerchant.lead.FetchLeadBrandEMIDIY;
import Request.MerchantService.v2.edc.diyUpgradePlans.CheckEligibilityDIY;
import Request.MerchantService.v2.edc.plans.fetchPlanEdcDIY;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowBrandEmiDIY extends BaseMethod
{
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowBrandEmiDIY.class);


    public static String merchantMobileNumber = "5555505151";
    public static String MerchantToken = "";
    public static String mid = "MPYnOd08861029683358";
    public static String channel = "DIY_P4B_APP";
    public static String solutionType = "diy_upgrade_merchant_plan";
    public static String planType = "brand_emi_activation";
    public static String custId = "1001844592";
    public static String leadId="";
    public static String totalLeads="";


    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void merchantLoginEMIDIY() throws Exception
    {
        MerchantToken = ApplicantToken(merchantMobileNumber, "paytm@123");
        establishConnectiontoServer(MerchantToken,5);
        LOGGER.info("Merchant token for EDC DIY : " + MerchantToken);
        LOGGER.info(" Inside DB execution to reset lead : ");
        DBConnection.UpdateQueryToCloseLeadsolnlevel2(merchantMobileNumber,"diy_upgrade_merchant_plan","brand_emi_activation");
        DBConnection.UpdateQueryToCloseLeadsolnlevel2(merchantMobileNumber,"brand_emi","brand_emi_activation");

      /*  waitForLoad(3000);
        TestBase testBase = new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '" + merchantMobileNumber + "' and status = '0' and solution_type='diy_upgrade_merchant_plan' and solution_type_level_2='brand_emi_activation';");
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '" + merchantMobileNumber + "' and status = '0' and solution_type='brand_emi' and solution_type_level_2='brand_emi_activation';");

        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " + UpdateRes); */

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_CheckEligibilityforBrandEmiDIYWithoutpassingCustID()  {
//        establishConnectiontoServer(MerchantToken,5);

        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_CheckEligibilityforBrandEmiDIYWithoutpassingMID()  {

        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", custId);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_CheckEligibilityforBrandEmiDIYWithoutpassingSolutionType()  {

        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", mid);
        queryParams.put("custId", custId);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_CheckEligibilityforBrandEmiDIYWithoutpassingChannel()  {

        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("custId", custId);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_CheckEligibilityforBrandEmiDIYWithoutpassingPlanType()  {

        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("custID", custId);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_CheckEligibilityforBrandEmiDIYWithInvalidCustidPassed()  {

        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", "10008568751");
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_CheckEligibilityforBrandEmiDIYWithInvalidMidPassed()  {

        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", custId);
        queryParams.put("mid", "pXXIQZ346652458523041");
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_CheckEligibilityforBrandEmiDIYWithInvalidSolutionTypePassed()  {

        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", custId);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", "upgrade_merchant_plan1");
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_CheckEligibilityforBrandEmiDIYWithInvalidChannelPassed()  {

        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", custId);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", "DIY_P4B_APP1");
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_CheckEligibilityforBrandEmiDIYWithInvalidPlanTypePassed()  {
//        establishConnectiontoServer(MerchantToken,5);

        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", custId);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", "brand_emi_activation1");

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 500);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11_CheckEligibilityforBrandEmiDIYWithEmptyCustIDPassed()  {
//        establishConnectiontoServer(MerchantToken,5);

        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", " ");
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12_CheckEligibilityforBrandEmiDIYWithEmptyMIDPassed()  {
//        establishConnectiontoServer(MerchantToken,5);

        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", custId);
        queryParams.put("mid", " ");
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13_CheckEligibilityforBrandEmiDIYWithEmptyPlanTypePassed()  {
//        establishConnectiontoServer(MerchantToken,5);

        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", custId);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", " ");

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 500);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_14_CheckEligibilityforBrandEmiDIYWithEmptyChannelPassed()  {

        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", custId);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_15_CheckEligibilityforBrandEmiDIYWithEmptySolutionTypePassed()  {

        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", custId);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_16_CheckEligibilityforBrandEmiDIYWithAllParametersPassed()  {

        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", custId);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get all brands for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_17_GetAllBrandsWithInvalidMIDPassed()  {

        GetAllBrands  GetAllBrandsObj =new GetAllBrands();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", "rlsCxU892286399198791");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
      Response  getallBrandsResp=middlewareServicesObject.GetAllBrandsDIY(GetAllBrandsObj,queryParams,headers);
        int StatusCode = getallBrandsResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get all brands for brand emi DIY",dependsOnMethods = "TC_16_CheckEligibilityforBrandEmiDIYWithAllParametersPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_18_GetAllBrandsWithEmptyMIDPassed()  {
        GetAllBrands  GetAllBrandsObj =new GetAllBrands();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", " ");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response  getallBrandsResp=middlewareServicesObject.GetAllBrandsDIY(GetAllBrandsObj,queryParams,headers);
        int StatusCode = getallBrandsResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Get all brands for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_19_GetAllBrandsWithCorrectMIDPassed()  {

        GetAllBrands  GetAllBrandsObj =new GetAllBrands();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", "eZICJC53961448945989");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response  getallBrandsResp=middlewareServicesObject.GetAllBrandsDIY(GetAllBrandsObj,queryParams,headers);
        int StatusCode = getallBrandsResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Validate dealer for brand emi DIY",dependsOnMethods = "TC_19_GetAllBrandsWithCorrectMIDPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_20_ValidateDealerWithInCorrectmidPassed()
    {
        ValidateDealer ValidateDealerObj =new ValidateDealer();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", "gdkYvU21469265374505111");
        queryParams.put("brandId", "124197");
        queryParams.put("dealerCode", "8901");
        queryParams.put("custId", "1001736611");

        Response  ValidateDealerResp=middlewareServicesObject.ValidateDealerBrandsDIY(ValidateDealerObj,queryParams,MerchantToken);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Validate dealer for brand emi DIY",dependsOnMethods = "TC_19_GetAllBrandsWithCorrectMIDPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_21_ValidateDealerWithInCorrectDealerCodePassed()
    {
        ValidateDealer ValidateDealerObj =new ValidateDealer();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", "gdkYvU21469265374505");
        queryParams.put("brandId", "124197");
        queryParams.put("dealerCode", "890111");
        queryParams.put("custId", "1001736611");

        Response  ValidateDealerResp=middlewareServicesObject.ValidateDealerBrandsDIY(ValidateDealerObj,queryParams,MerchantToken);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Validate dealer for brand emi DIY",dependsOnMethods = "TC_19_GetAllBrandsWithCorrectMIDPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_22_ValidateDealerWithInCorrectBrandIdPassed()
    {
        ValidateDealer ValidateDealerObj =new ValidateDealer();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", "gdkYvU21469265374505");
        queryParams.put("brandId", "1241971");
        queryParams.put("dealerCode", "8901");
        queryParams.put("custId", "1001736611");

        Response  ValidateDealerResp=middlewareServicesObject.ValidateDealerBrandsDIY(ValidateDealerObj,queryParams,MerchantToken);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Validate dealer for brand emi DIY",dependsOnMethods = "TC_19_GetAllBrandsWithCorrectMIDPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_23_ValidateDealerWithInCorrectCustIdPassed()
    {
        ValidateDealer ValidateDealerObj =new ValidateDealer();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", "gdkYvU21469265374505");
        queryParams.put("brandId", "124197");
        queryParams.put("dealerCode", "8901");
        queryParams.put("custId", "100173661111");

        Response  ValidateDealerResp=middlewareServicesObject.ValidateDealerBrandsDIY(ValidateDealerObj,queryParams,MerchantToken);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Validate dealer for brand emi DIY",dependsOnMethods = "TC_19_GetAllBrandsWithCorrectMIDPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_24_ValidateDealerWithoutTokenPassed()
    {
        ValidateDealer ValidateDealerObj =new ValidateDealer();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", "gdkYvU21469265374505");
        queryParams.put("brandId", "124197");
        queryParams.put("dealerCode", "8901");
        queryParams.put("custId", "1001736611");

        Response  ValidateDealerResp=middlewareServicesObject.ValidateDealerBrandsDIY(ValidateDealerObj,queryParams," ");
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 401);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Validate dealer for brand emi DIY",dependsOnMethods = "TC_19_GetAllBrandsWithCorrectMIDPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_25_ValidateDealerWithNullMidPassed()
    {
        ValidateDealer ValidateDealerObj =new ValidateDealer();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", " ");
        queryParams.put("brandId", "124197");
        queryParams.put("dealerCode", "8901");
        queryParams.put("custId", "1001736611");

        Response  ValidateDealerResp=middlewareServicesObject.ValidateDealerBrandsDIY(ValidateDealerObj,queryParams,MerchantToken);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Validate dealer for brand emi DIY",dependsOnMethods = "TC_19_GetAllBrandsWithCorrectMIDPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_26_ValidateDealerWithNullBrandIDPassed()
    {
        ValidateDealer ValidateDealerObj =new ValidateDealer();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", "gdkYvU21469265374505");
        queryParams.put("brandId", " ");
        queryParams.put("dealerCode", "8901");
        queryParams.put("custId", "1001736611");

        Response  ValidateDealerResp=middlewareServicesObject.ValidateDealerBrandsDIY(ValidateDealerObj,queryParams,MerchantToken);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Validate dealer for brand emi DIY",dependsOnMethods = "TC_19_GetAllBrandsWithCorrectMIDPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_27_ValidateDealerWithNullDealerCodePassed()
    {
        ValidateDealer ValidateDealerObj =new ValidateDealer();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", "gdkYvU21469265374505");
        queryParams.put("brandId", "124197");
        queryParams.put("dealerCode", " ");
        queryParams.put("custId", "1001736611");

        Response  ValidateDealerResp=middlewareServicesObject.ValidateDealerBrandsDIY(ValidateDealerObj,queryParams,MerchantToken);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Validate dealer for brand emi DIY",dependsOnMethods = "TC_19_GetAllBrandsWithCorrectMIDPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_28_ValidateDealerWithNullCustIdPassed()
    {
        ValidateDealer ValidateDealerObj =new ValidateDealer();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", "gdkYvU21469265374505");
        queryParams.put("brandId", "124197");
        queryParams.put("dealerCode", "8901");
        queryParams.put("custId", " ");

        Response  ValidateDealerResp=middlewareServicesObject.ValidateDealerBrandsDIY(ValidateDealerObj,queryParams,MerchantToken);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Validate dealer for brand emi DIY",dependsOnMethods = "TC_19_GetAllBrandsWithCorrectMIDPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_29_ValidateDealerWithAllParamsNullPassed()
    {
        ValidateDealer ValidateDealerObj =new ValidateDealer();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", " ");
        queryParams.put("brandId", " ");
        queryParams.put("dealerCode", " ");
        queryParams.put("custId", " ");

        Response  ValidateDealerResp=middlewareServicesObject.ValidateDealerBrandsDIY(ValidateDealerObj,queryParams,MerchantToken);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Validate dealer for brand emi DIY",dependsOnMethods = "TC_19_GetAllBrandsWithCorrectMIDPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_30_ValidateDealerWithCorrectDealerPassed()
    {
        ValidateDealer ValidateDealerObj =new ValidateDealer();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", "gdkYvU21469265374505");
        queryParams.put("brandId", "124197");
        queryParams.put("dealerCode", "8901");
        queryParams.put("custId", "1001736611");

        Response  ValidateDealerResp=middlewareServicesObject.ValidateDealerBrandsDIY(ValidateDealerObj,queryParams,MerchantToken);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch plan for brand emi DIY",dependsOnMethods = "TC_30_ValidateDealerWithCorrectDealerPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_31_FetchPlanEDCDIYWithEmptySolutionType()
    {
        fetchPlanEdcDIY fetchPlanEdcDIYObj =new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", " ");
        queryParams.put("planType", planType);
        queryParams.put("mid", mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);

        Response  ValidateDealerResp=middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEdcDIYObj,queryParams,headers);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch plan for brand emi DIY",dependsOnMethods = "TC_30_ValidateDealerWithCorrectDealerPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_32_FetchPlanEDCDIYWithEmptyPlanType()
    {
        fetchPlanEdcDIY fetchPlanEdcDIYObj =new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", solutionType);
        queryParams.put("planType", " ");
        queryParams.put("mid", mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);

        Response  ValidateDealerResp=middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEdcDIYObj,queryParams,headers);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch plan for brand emi DIY",dependsOnMethods = "TC_30_ValidateDealerWithCorrectDealerPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_33_FetchPlanEDCDIYWithEmptyMID()
    {
        fetchPlanEdcDIY fetchPlanEdcDIYObj =new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", solutionType);
        queryParams.put("planType", planType);
        queryParams.put("mid", " ");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);

        Response  ValidateDealerResp=middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEdcDIYObj,queryParams,headers);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch plan for brand emi DIY",dependsOnMethods = "TC_30_ValidateDealerWithCorrectDealerPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_34_FetchPlanEDCDIYWithEmptyToken()
    {
        fetchPlanEdcDIY fetchPlanEdcDIYObj =new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", solutionType);
        queryParams.put("planType", planType);
        queryParams.put("mid", mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", " ");

        Response  ValidateDealerResp=middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEdcDIYObj,queryParams,headers);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 401);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch plan for brand emi DIY",dependsOnMethods = "TC_30_ValidateDealerWithCorrectDealerPassed")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_35_FetchPlanEDCDIY()
    {
        fetchPlanEdcDIY fetchPlanEdcDIYObj =new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", solutionType);
        queryParams.put("planType", planType);
        queryParams.put("mid", mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);

        Response  ValidateDealerResp=middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEdcDIYObj,queryParams,headers);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Create lead for brand emi DIY",dependsOnMethods = "TC_35_FetchPlanEDCDIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_36_CreateLeadBrandEMIDIYWithInvalidSolution()
    {
        CreateLeadDIYCashAtPos CreateLeadDIYCashAtPosObj =new CreateLeadDIYCashAtPos(P.TESTDATA.get("CreateLeadBrandEMIDIY"));
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "diy_upgrade_merchant_plan1");
        queryParams.put("solutionTypeLevel2", planType);
        queryParams.put("channel", channel);
        queryParams.put("entityType","INDIVIDUAL");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("PG_MID", mid);
        body.put("brandId", "2583");
        body.put("dealerCode", "601");
        body.put("autoApproved", "true");


        Response  ValidateDealerResp=middlewareServicesObject.CreateLeadCashAtPosDIY(CreateLeadDIYCashAtPosObj,queryParams,headers,body);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Create lead for brand emi DIY",dependsOnMethods = "TC_36_CreateLeadBrandEMIDIYWithInvalidSolution")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_37_CreateLeadBrandEMIDIYWithInvalidChannel()
    {
        CreateLeadDIYCashAtPos CreateLeadDIYCashAtPosObj =new CreateLeadDIYCashAtPos(P.TESTDATA.get("CreateLeadBrandEMIDIY"));
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", solutionType);
        queryParams.put("solutionTypeLevel2", planType);
        queryParams.put("channel", "DIY_P4B_APP1");
        queryParams.put("entityType","INDIVIDUAL");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("PG_MID", mid);
        body.put("brandId", "2583");
        body.put("dealerCode", "601");
        body.put("autoApproved", "true");


        Response  ValidateDealerResp=middlewareServicesObject.CreateLeadCashAtPosDIY(CreateLeadDIYCashAtPosObj,queryParams,headers,body);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Create lead for brand emi DIY",dependsOnMethods = "TC_37_CreateLeadBrandEMIDIYWithInvalidChannel")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_38_CreateLeadBrandEMIDIYWithInvalidSolutionTypeLevel2()
    {
        CreateLeadDIYCashAtPos CreateLeadDIYCashAtPosObj =new CreateLeadDIYCashAtPos(P.TESTDATA.get("CreateLeadBrandEMIDIY"));
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", solutionType);
        queryParams.put("solutionTypeLevel2", "brand_emi_activation1");
        queryParams.put("channel", channel);
        queryParams.put("entityType","INDIVIDUAL");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("PG_MID", mid);
        body.put("brandId", "2583");
        body.put("dealerCode", "601");
        body.put("autoApproved", "true");


        Response  ValidateDealerResp=middlewareServicesObject.CreateLeadCashAtPosDIY(CreateLeadDIYCashAtPosObj,queryParams,headers,body);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Create lead for brand emi DIY",dependsOnMethods = "TC_38_CreateLeadBrandEMIDIYWithInvalidSolutionTypeLevel2")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_39_CreateLeadBrandEMIDIYWithInvalidEntity()
    {
        CreateLeadDIYCashAtPos CreateLeadDIYCashAtPosObj =new CreateLeadDIYCashAtPos(P.TESTDATA.get("CreateLeadBrandEMIDIY"));
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", solutionType);
        queryParams.put("solutionTypeLevel2", planType);
        queryParams.put("channel", channel);
        queryParams.put("entityType","PROPRIETORSHIP");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("PG_MID", mid);
        body.put("brandId", "2583");
        body.put("dealerCode", "601");
        body.put("autoApproved", "true");


        Response  ValidateDealerResp=middlewareServicesObject.CreateLeadCashAtPosDIY(CreateLeadDIYCashAtPosObj,queryParams,headers,body);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }


    @Test(priority = 1, groups = {"Regression"}, description = "Create lead for brand emi DIY",dependsOnMethods = "TC_39_CreateLeadBrandEMIDIYWithInvalidEntity")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_40_CreateLeadBrandEMIDIY()
    {
        CreateLeadDIYCashAtPos CreateLeadDIYCashAtPosObj =new CreateLeadDIYCashAtPos(P.TESTDATA.get("CreateLeadBrandEMIDIY"));
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", solutionType);
        queryParams.put("solutionTypeLevel2", planType);
        queryParams.put("channel", channel);
        queryParams.put("entityType","INDIVIDUAL");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("PG_MID", mid);
        body.put("brandId", "2583");
        body.put("dealerCode", "601");
        body.put("autoApproved", "true");


        Response  ValidateDealerResp=middlewareServicesObject.CreateLeadCashAtPosDIY(CreateLeadDIYCashAtPosObj,queryParams,headers,body);
        leadId= ValidateDealerResp.jsonPath().getString("leadId");
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch plan for Brand emi DIY with empty solution Type",dependsOnMethods = "TC_40_CreateLeadBrandEMIDIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_041_fetchPlanforBrandEMIDIYWithEmptySolutionType() {
        fetchPlanEdcDIY fetchPlanEDCDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("planType", planType);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", " ");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEdcDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEDCDIYObj, queryParams, headers);
        int StatusCode = fetchplanEdcDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch plan for Brand emi DIY with invalid mid",dependsOnMethods = "TC_041_fetchPlanforBrandEMIDIYWithEmptySolutionType")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_042_fetchPlanforBrandEMIDIYWithInvalidmid() {
        fetchPlanEdcDIY fetchPlanEDCDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("planType", planType);
        queryParams.put("mid", "gdkYvU214692653745051");
        queryParams.put("solutionType", solutionType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEdcDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEDCDIYObj, queryParams, headers);
        int StatusCode = fetchplanEdcDIYResponse.getStatusCode();
//        Assert.assertEquals(StatusCode, 500);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch plan for Brand emi DIY with invalid plan Type",dependsOnMethods = "TC_042_fetchPlanforBrandEMIDIYWithInvalidmid")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_043_fetchPlanforBrandEMIDIYWithInvalidPlanType() {
        fetchPlanEdcDIY fetchPlanEDCDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("planType", "brand_emi_activation1");
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEdcDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEDCDIYObj, queryParams, headers);
        int StatusCode = fetchplanEdcDIYResponse.getStatusCode();
//        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch plan for Brand emi DIY",dependsOnMethods = "TC_043_fetchPlanforBrandEMIDIYWithInvalidPlanType")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_044_fetchPlanforBrandEMIDIY() {
        fetchPlanEdcDIY fetchPlanEDCDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("planType", planType);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEdcDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEDCDIYObj, queryParams, headers);
        int StatusCode = fetchplanEdcDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch lead status for Brand emi DIY",dependsOnMethods = "TC_044_fetchPlanforBrandEMIDIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_045_fetchLeadStatusBrandEMIDIY() {
        Status StatusObj = new Status(P.TESTDATA.get("BrandEMILeadStatus"));
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionSubType", planType);
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("solution", solutionType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        Response fetchplanEdcDIYResponse = middlewareServicesObject.leadStatusBrandEMIDIY(StatusObj, queryParams, headers,body);
        int StatusCode = fetchplanEdcDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get all parent lead leads for brand emi DIY",dependsOnMethods = "TC_045_fetchLeadStatusBrandEMIDIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_46_FetchParentLeadBrandEMIDIY()
    {
        FetchLeadBrandEMIDIY FetchLeadBrandEMIDIYObj =new FetchLeadBrandEMIDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", solutionType);
        queryParams.put("all", "true");
        queryParams.put("channel", channel);
        queryParams.put("leadid",leadId);
        queryParams.put("entityType","INDIVIDUAL");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);

        Response  ValidateDealerResp=middlewareServicesObject.FetchleadBrandEmiDIY(FetchLeadBrandEMIDIYObj,queryParams,headers);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get all parent lead leads for brand emi DIY",dependsOnMethods = "TC_46_FetchParentLeadBrandEMIDIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_47_FetchChildLeadBrandEMIDIY()
    {
        FetchLeadBrandEMIDIY FetchLeadBrandEMIDIYObj =new FetchLeadBrandEMIDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "diy_brand_emi");
        queryParams.put("all", "true");
        queryParams.put("channel", channel);
        queryParams.put("leadid",leadId);
        queryParams.put("entityType","INDIVIDUAL");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);

        Response  ValidateDealerResp=middlewareServicesObject.FetchleadBrandEmiDIY(FetchLeadBrandEMIDIYObj,queryParams,headers);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get all parent lead leads for brand emi DIY",dependsOnMethods = "TC_47_FetchChildLeadBrandEMIDIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_48_FetchParentLeadBrandEMIDIYWithInvalidSolution()
    {
        FetchLeadBrandEMIDIY FetchLeadBrandEMIDIYObj =new FetchLeadBrandEMIDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "diy_upgrade_merchant_plan1");
        queryParams.put("all", "true");
        queryParams.put("channel", channel);
        queryParams.put("leadid",leadId);
        queryParams.put("entityType","INDIVIDUAL");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);

        Response  ValidateDealerResp=middlewareServicesObject.FetchleadBrandEmiDIY(FetchLeadBrandEMIDIYObj,queryParams,headers);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get all parent lead leads for brand emi DIY",dependsOnMethods = "TC_48_FetchParentLeadBrandEMIDIYWithInvalidSolution")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_49_FetchParentLeadBrandEMIDIYWithoutParamAll()
    {
        FetchLeadBrandEMIDIY FetchLeadBrandEMIDIYObj =new FetchLeadBrandEMIDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("leadid",leadId);
        queryParams.put("entityType","INDIVIDUAL");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);

        Response  ValidateDealerResp=middlewareServicesObject.FetchleadBrandEmiDIY(FetchLeadBrandEMIDIYObj,queryParams,headers);
        int StatusCode = ValidateDealerResp.getStatusCode();
//        Assert.assertEquals(StatusCode, 500);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get all parent lead leads for brand emi DIY",dependsOnMethods = "TC_49_FetchParentLeadBrandEMIDIYWithoutParamAll")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_50_FetchNoOfLeadsBrandEMIDIY()
    {
        FetchLeadBrandEMIDIY FetchLeadBrandEMIDIYObj =new FetchLeadBrandEMIDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("leadid",leadId);
        queryParams.put("all", "true");
        queryParams.put("entityType","INDIVIDUAL");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);

        Response  ValidateDealerResp=middlewareServicesObject.FetchleadBrandEmiDIY(FetchLeadBrandEMIDIYObj,queryParams,headers);
        totalLeads=ValidateDealerResp.jsonPath().getString("noOfLeads");
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        LOGGER.info("Total no. of leads are : "+totalLeads);

    }



}
