package OCL.DIY.BrandEmiDIY;

import Request.EOS.DeleteDealer;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class DealerDelete extends BaseMethod {
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(DealerDelete.class);

    @Test(priority = 0, description = "API RESPONSE WITH ALL VALID DETAILS", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_DeleteDealerForBrandEMI() throws Exception{

        DeleteDealer deleteDealer = new DeleteDealer();
        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("sub","BrandEmi");
        jwtParams.put("brandId","23580");
        String Auth=generateJwtTokenEOS("yQhDXG9zr8yXqPUDEU", "P4BIN004445",jwtParams);

        Map<String, String> body = new HashMap<String, String>();
        body.put("auth",Auth);
        body.put("clientId", "P4BIN004445");
        body.put("reqTimestamp", "1694172940599");
        body.put("brandId", "23580");
        body.put("dealerCode", "as233t");
        body.put("mid", "BuqNYm35873337823127");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response respObj = middlewareServicesObject.deleteDealerMiddlewareService(deleteDealer,body,headers);
        LOGGER.info("API RESPONSE WITH ALL VALID DETAILS " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);
    }

    @Test(priority = 0, description = "API Response without token", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_DeleteDealerForBrandEMI() throws Exception{

        DeleteDealer deleteDealer = new DeleteDealer();
        Map<String, String> body = new HashMap<String, String>();
        body.put("auth","");
        body.put("clientId", "P4BIN004445");
        body.put("reqTimestamp", "1694172940599");
        body.put("brandId", "23580");
        body.put("dealerCode", "as233t");
        body.put("mid", "BuqNYm35873337823127");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response respObj = middlewareServicesObject.deleteDealerMiddlewareService(deleteDealer,body,headers);
        LOGGER.info("API Response without token " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 500);
    }

    @Test(priority = 0, description = "API Response without clientID", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_DeleteDealerForBrandEMI() throws Exception{

        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("sub","BrandEmi");
        jwtParams.put("brandId","23580");
        String Auth=generateJwtTokenEOS("yQhDXG9zr8yXqPUDEU", "P4BIN004445",jwtParams);

        DeleteDealer deleteDealer = new DeleteDealer();
        Map<String, String> body = new HashMap<String, String>();
        body.put("auth",Auth);
        body.put("clientId", "");
        body.put("reqTimestamp", "1694172940599");
        body.put("brandId", "23580");
        body.put("dealerCode", "as233t");
        body.put("mid", "BuqNYm35873337823127");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response respObj = middlewareServicesObject.deleteDealerMiddlewareService(deleteDealer,body,headers);
        LOGGER.info("API Response without clientID " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 500);
    }

    @Test(priority = 0, description = "API Response without Timestamp", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_DeleteDealerForBrandEMI() throws Exception{

        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("sub","BrandEmi");
        jwtParams.put("brandId","23580");
        String Auth=generateJwtTokenEOS("yQhDXG9zr8yXqPUDEU", "P4BIN004445",jwtParams);

        DeleteDealer deleteDealer = new DeleteDealer();
        Map<String, String> body = new HashMap<String, String>();
        body.put("auth",Auth);
        body.put("clientId", "P4BIN004445");
        body.put("reqTimestamp", "");
        body.put("brandId", "23580");
        body.put("dealerCode", "as233t");
        body.put("mid", "BuqNYm35873337823127");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response respObj = middlewareServicesObject.deleteDealerMiddlewareService(deleteDealer,body,headers);
        LOGGER.info("API Response without clientID " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 500);
    }

    @Test(priority = 0, description = "API Response without BrandID", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_DeleteDealerForBrandEMI() throws Exception{

        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("sub","BrandEmi");
        jwtParams.put("brandId","23580");
        String Auth=generateJwtTokenEOS("yQhDXG9zr8yXqPUDEU", "P4BIN004445",jwtParams);

        DeleteDealer deleteDealer = new DeleteDealer();
        Map<String, String> body = new HashMap<String, String>();
        body.put("auth",Auth);
        body.put("clientId", "P4BIN004445");
        body.put("reqTimestamp", "1694172940599");
        body.put("brandId", "");
        body.put("dealerCode", "as233t");
        body.put("mid", "BuqNYm35873337823127");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response respObj = middlewareServicesObject.deleteDealerMiddlewareService(deleteDealer,body,headers);
        LOGGER.info("API Response without BrandID " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 500);
    }

    @Test(priority = 0, description = "API Response without MID", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_DeleteDealerForBrandEMI() throws Exception{

        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("sub","BrandEmi");
        jwtParams.put("brandId","23580");
        String Auth=generateJwtTokenEOS("yQhDXG9zr8yXqPUDEU", "P4BIN004445",jwtParams);

        DeleteDealer deleteDealer = new DeleteDealer();
        Map<String, String> body = new HashMap<String, String>();
        body.put("auth",Auth);
        body.put("clientId", "P4BIN004445");
        body.put("reqTimestamp", "1694172940599");
        body.put("brandId", "23580");
        body.put("dealerCode", "as233t");
        body.put("mid", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response respObj = middlewareServicesObject.deleteDealerMiddlewareService(deleteDealer,body,headers);
        LOGGER.info("API Response without MID " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);
    }

    @Test(priority = 0, description = "API Response without Dealercode", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_DeleteDealerForBrandEMI() throws Exception{
        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("sub","BrandEmi");
        jwtParams.put("brandId","23580");
        String Auth=generateJwtTokenEOS("yQhDXG9zr8yXqPUDEU", "P4BIN004445",jwtParams);

        DeleteDealer deleteDealer = new DeleteDealer();
        Map<String, String> body = new HashMap<String, String>();
        body.put("auth",Auth);
        body.put("clientId", "P4BIN004445");
        body.put("reqTimestamp", "1694172940599");
        body.put("brandId", "23580");
        body.put("dealerCode", "");
        body.put("mid", "BuqNYm35873337823127");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response respObj = middlewareServicesObject.deleteDealerMiddlewareService(deleteDealer,body,headers);
        LOGGER.info("API Response without Dealercode " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);
    }
    @Test(priority = 0, description = "API Response without Dealercode and MID", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_DeleteDealerForBrandEMI() throws Exception{

        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("sub","BrandEmi");
        jwtParams.put("brandId","23580");
        String Auth=generateJwtTokenEOS("yQhDXG9zr8yXqPUDEU", "P4BIN004445",jwtParams);

        DeleteDealer deleteDealer = new DeleteDealer();
        Map<String, String> body = new HashMap<String, String>();
        body.put("auth",Auth);
        body.put("clientId", "P4BIN004445");
        body.put("reqTimestamp", "1694172940599");
        body.put("brandId", "23580");
        body.put("dealerCode", "");
        body.put("mid", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response respObj = middlewareServicesObject.deleteDealerMiddlewareService(deleteDealer,body,headers);
        LOGGER.info("API Response without Dealercode and MID " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);
    }

    @Test(priority = 0, description = "API Response with invalid dealer code", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_DeleteDealerForBrandEMI() throws Exception{

        DeleteDealer deleteDealer = new DeleteDealer();
        Map<String, String> body = new HashMap<String, String>();
        body.put("auth","asdder");
        body.put("clientId", "P4BIN004445");
        body.put("reqTimestamp", "1694172940599");
        body.put("brandId", "23580");
        body.put("dealerCode", "3455");
        body.put("mid", "BuqNYm35873337823127");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response respObj = null;
        try {
            respObj = middlewareServicesObject.deleteDealerMiddlewareService(deleteDealer,body,headers);

        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {
            LOGGER.info("API Resposne with invalid token" + respObj.statusCode());
            Assert.assertEquals(respObj.statusCode(), 200);
        }
    }


}
