package OCL.DIY.BrandEmiDIY;

import Request.CIF.BrandActiveInKyb;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.v1.brandEmi.getAllBrands.GetAllBrands;
import Request.MerchantService.v1.brandEmi.getAllBrands.dealer.validate.ValidateDealer;
import Request.MerchantService.v1.sdMerchant.CreateLeadDIYCashAtPos;
import Request.MerchantService.v1.sdMerchant.lead.FetchLeadBrandEMIDIY;
import Request.MerchantService.v2.edc.diyUpgradePlans.CheckEligibilityDIY;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FlowBrandEmiWithDocUploadDIY extends BaseMethod {
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowBrandEmiDIY.class);


    public static String merchantMobileNumber = "5555505151";
    public static String MerchantToken = "";
    public static String mid = "MPYnOd08861029683358";
    public static String channel = "DIY_P4B_APP";
    public static String solutionType = "diy_upgrade_merchant_plan";
    public static String planType = "brand_emi_activation";
    public static String custId = "1001844592";
    public static String ParentleadId = "";
    public static String childLeadID = "";
    public static String ChildleadStage = "";
    public static String ParentleadStage = "";
    public static String cookie="";
    public static String PG_REQ_ID="";
    public static List<String> activeBrandList;
    public static List<String> activeBrandIDList;
    public static String BrandID="2583";
    public static String DealerCode="101";



    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void merchantLoginEMIDIY() throws Exception {
        MerchantToken = ApplicantToken(merchantMobileNumber, "paytm@123");
        establishConnectiontoServer(MerchantToken,5);
        LOGGER.info("Merchant token for EDC DIY : " + MerchantToken);
        LOGGER.info(" Inside DB execution to reset lead : ");
        waitForLoad(3000);
        DBConnection.UpdateQueryToCloseLeadsolnlevel2(merchantMobileNumber,"diy_upgrade_merchant_plan","brand_emi_activation");
        DBConnection.UpdateQueryToCloseLeadsolnlevel2(merchantMobileNumber,"brand_emi","brand_emi_activation");

        /*TestBase testBase = new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '" + merchantMobileNumber + "' and status = '0' and solution_type='diy_upgrade_merchant_plan' and solution_type_level_2='brand_emi_activation';");
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '" + merchantMobileNumber + "' and status = '0' and solution_type='brand_emi' and solution_type_level_2='brand_emi_activation';");

        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " + UpdateRes); */

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Check eligibility for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_CheckEligibilityforBrandEmiDIYWithAllParametersPassed() {
        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", custId);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get all brands for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_GetAllBrandsWithCorrectMIDPassed() {
        GetAllBrands GetAllBrandsObj = new GetAllBrands();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", mid);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response getallBrandsResp = middlewareServicesObject.GetAllBrandsDIY(GetAllBrandsObj, queryParams, headers);
        int StatusCode = getallBrandsResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Validate dealer for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_ValidateDealerWithInCorrectDealerPassed() {
        ValidateDealer ValidateDealerObj = new ValidateDealer();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", mid);
        queryParams.put("brandId", "2583");
        queryParams.put("dealerCode", "4011");
        queryParams.put("custId", custId);

        Response ValidateDealerResp = middlewareServicesObject.ValidateDealerBrandsDIY(ValidateDealerObj, queryParams, MerchantToken);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 1, groups = {"Regression"}, description = "Validate dealer for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_ValidateDealerWithCorrectDealerPassed() {
        ValidateDealer ValidateDealerObj = new ValidateDealer();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", mid);
        queryParams.put("brandId", "2583");
        queryParams.put("dealerCode", "601");
        queryParams.put("custId", custId);

        Response ValidateDealerResp = middlewareServicesObject.ValidateDealerBrandsDIY(ValidateDealerObj, queryParams, MerchantToken);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get Active brand list from kyb")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_GetActiveBrandListFromKyb() {
        BrandActiveInKyb BrandActiveInKybObj = new BrandActiveInKyb();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("pgMid", mid);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE2OTM1NDY5MjA3MTMiLCJjdXN0X2lkIjoiMTUyMDM0MjAzOTAwMCIsImNsaWVudF9pZCI6IkdHLU9FLXN0YWdpbmcifQ.BtqEX/Yci3YLy10/O4GAjEbSmTHcECqqVEI1iRmxHPM");
        Response BrandActiveInKybObjResp = middlewareServicesObject.GetActiveBrandListFromKyb(BrandActiveInKybObj, queryParams, headers);
        activeBrandList=BrandActiveInKybObjResp.jsonPath().getJsonObject("response.brandName.brandName");
        activeBrandIDList=BrandActiveInKybObjResp.jsonPath().getJsonObject("response.brandName.brandId");
        LOGGER.info("Active Brand list is :" +activeBrandList);
        LOGGER.info("Active Brand ID list is :" +activeBrandIDList);
        int StatusCode = BrandActiveInKybObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }


    @Test(priority = 1, groups = {"Regression"}, description = "Create lead for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_CreateLeadBrandEMIDIY() {
        if(!BrandID.equals(activeBrandIDList) || !BrandID.equals(" ")) {
            CreateLeadDIYCashAtPos CreateLeadDIYCashAtPosObj = new CreateLeadDIYCashAtPos(P.TESTDATA.get("CreateLeadBrandEMIDIY"));
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", solutionType);
            queryParams.put("solutionTypeLevel2", planType);
            queryParams.put("channel", channel);
            queryParams.put("entityType", "INDIVIDUAL");

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("session_token", MerchantToken);

            Map<String, String> body = new HashMap<String, String>();
            body.put("PG_MID", mid);
            body.put("brandId", BrandID);
            body.put("dealerCode", DealerCode);
            body.put("autoApproved", "true");


            Response ValidateDealerResp = middlewareServicesObject.CreateLeadCashAtPosDIY(CreateLeadDIYCashAtPosObj, queryParams, headers, body);
            ParentleadId = ValidateDealerResp.jsonPath().getString("leadId");
            LOGGER.info("Parent lead id is :"+ParentleadId);
            int StatusCode = ValidateDealerResp.getStatusCode();
            Assert.assertEquals(StatusCode, 200);
        }
        else
        {
            LOGGER.info("Brand is already active  or Brand id is null:" +BrandID );
            LOGGER.info("Brand is already active :" +activeBrandList );
        }

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get all parent lead leads for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_FetchParentLeadBrandEMIDIY()
    {
        FetchLeadBrandEMIDIY FetchLeadBrandEMIDIYObj =new FetchLeadBrandEMIDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", solutionType);
        queryParams.put("all", "true");
        queryParams.put("channel", channel);
        queryParams.put("leadid",ParentleadId);
        queryParams.put("entityType","INDIVIDUAL");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);

        Response  ValidateDealerResp=middlewareServicesObject.FetchleadBrandEmiDIY(FetchLeadBrandEMIDIYObj,queryParams,headers);
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get all child lead leads for brand emi DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_FetchChildLeadBrandEMIDIY()
    {
        FetchLeadBrandEMIDIY FetchLeadBrandEMIDIYObj =new FetchLeadBrandEMIDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "diy_brand_emi");
        queryParams.put("all", "true");
        queryParams.put("channel", channel);
        queryParams.put("leadid",ParentleadId);
        queryParams.put("entityType","INDIVIDUAL");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);

        Response  ValidateDealerResp=middlewareServicesObject.FetchleadBrandEmiDIY(FetchLeadBrandEMIDIYObj,queryParams,headers);
        childLeadID = ValidateDealerResp.jsonPath().getJsonObject("allLeads[0].leadId").toString();
        int StatusCode = ValidateDealerResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        LOGGER.info("Child lead id is :" +childLeadID);

    }

    @Test(priority = 1, description = "Fetch lead status for brand emi diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_FetchStatusLeadBrandEMIDiy()
    {
        waitForLoad(10000);
        FetchLead v1FetchLeadObj=new FetchLead(childLeadID);
        cookie=findXMWTokenforPanel("7771216290","paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, cookie);
        ChildleadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        PG_REQ_ID=v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.additionalDetails.pgReferenceId").toString();
        System.out.println("PG Request ID is "+ PG_REQ_ID);
        System.out.println("Child lead stage is "+ ChildleadStage);
    }

    @Test(priority = 1, description = "PG Callback for Brand EMI DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_ManualPGCallback()
    {
        if (ChildleadStage.equals("BRAND_ACTIVATION_PENDING"))
        {
            ManualPgCallBack(custId, PG_REQ_ID, mid);

        }
        else
        {
         LOGGER.info("Lead is not in correct stage for manual callback");
        }
    }

    @Test(priority = 1, description = "Fetch lead status for brand emi diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_FetchChildLeadStagefromPanelBrandEMIDiy()
    {
        waitForLoad(5000);
        FetchLead v1FetchLeadObj=new FetchLead(childLeadID);
        cookie=findXMWTokenforPanel("7771216290","paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, cookie);
        ChildleadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Child Lead stage is "+ ChildleadStage);
        if(ChildleadStage.equals("LEAD_SUCCESSFULLY_CLOSED"))
        {
            LOGGER.info("Child Lead closed successfully");
        }
        else
        {
            LOGGER.info("Lead not closed yet");
        }
    }

    @Test(priority = 1, description = "Fetch lead status for brand emi diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_FetchParentLeadStagefromPanelBrandEMIDiy()
    {
        waitForLoad(5000);
        FetchLead v1FetchLeadObj=new FetchLead(ParentleadId);
        cookie=findXMWTokenforPanel("7771216290","paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, cookie);
        ParentleadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Parent Lead stage is "+ ParentleadStage);

        if(ParentleadStage.equals("LEAD_SUCCESSFULLY_CLOSED"))
        {
            LOGGER.info("Parent Lead closed successfully");
        }
        else
        {
            LOGGER.info("Lead not closed yet");
        }
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get Active brand list from kyb")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_CheckBrandActiveORNotInKyb() {
        BrandActiveInKyb BrandActiveInKybObj = new BrandActiveInKyb();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("pgMid", mid);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE2OTM1NDY5MjA3MTMiLCJjdXN0X2lkIjoiMTUyMDM0MjAzOTAwMCIsImNsaWVudF9pZCI6IkdHLU9FLXN0YWdpbmcifQ.BtqEX/Yci3YLy10/O4GAjEbSmTHcECqqVEI1iRmxHPM");
        Response BrandActiveInKybObjResp = middlewareServicesObject.GetActiveBrandListFromKyb(BrandActiveInKybObj, queryParams, headers);
        activeBrandList=BrandActiveInKybObjResp.jsonPath().getJsonObject("response.brandName.brandName");
        activeBrandIDList=BrandActiveInKybObjResp.jsonPath().getJsonObject("response.brandName.brandId");
        LOGGER.info("Active Brand list is :" +activeBrandList);
        LOGGER.info("Active Brand ID list is :" +activeBrandIDList);
        int StatusCode = BrandActiveInKybObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

}
