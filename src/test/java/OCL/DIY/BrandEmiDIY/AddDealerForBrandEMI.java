package OCL.DIY.BrandEmiDIY;

import Request.EOS.AddDealer;
import Request.EOS.GetBrandDealer;
import Request.EOS.GetBrandList;
import Services.MechantService.MiddlewareServices;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class AddDealerForBrandEMI
{
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(AddDealerForBrandEMI.class);
    public static String mid = "ADfyin30849684056371";

    @Test(priority = 0, groups = {"Regression"}, description = "Add dealer for brand emi ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_AddDealerForBrandEMI() {
        AddDealer AddDealerObj = new AddDealer();
        Map<String, String> body = new HashMap<String, String>();
        body.put("brandId", "18260");
        body.put("dealerCode", "40101");
        body.put("mid", mid);
        body.put("shopType", "Sports  shop");
        body.put("stateCode", "2022");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response AddDealerObjResponse = middlewareServicesObject.AddDealerBrandEmi(AddDealerObj, headers,body);
        int StatusCode = AddDealerObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Add Same dealer for brand emi ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_AddSameDealerForBrandEMI() {
        AddDealer AddDealerObj = new AddDealer();
        Map<String, String> body = new HashMap<String, String>();
        body.put("brandId", "18260");
        body.put("dealerCode", "40101");
        body.put("mid", mid);
        body.put("shopType", "Sports  shop");
        body.put("stateCode", "2022");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response AddDealerObjResponse = middlewareServicesObject.AddDealerBrandEmi(AddDealerObj, headers,body);
        int StatusCode = AddDealerObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Find Brand Dealer data ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_FindBrandDealerData() {
        GetBrandDealer GetBrandDealerObj = new GetBrandDealer();
        Map<String, String> body = new HashMap<String, String>();
        body.put("brandId", "18260");
        body.put("dealerCode", "40101");
        body.put("mid", mid);
        body.put("shopType", "Sports  shop");
        body.put("stateCode", "2022");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response GetBrandDealerObjResponse = middlewareServicesObject.GetBrandDealerBrandEmi(GetBrandDealerObj, headers,body);
        int StatusCode = GetBrandDealerObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Add dealer for different brand")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_AddDealerforDiffBrand() {
        AddDealer AddDealerObj = new AddDealer();
        Map<String, String> body = new HashMap<String, String>();
        body.put("brandId", "182601");
        body.put("dealerCode", "40101");
        body.put("mid", mid);
        body.put("shopType", "Sports  shop");
        body.put("stateCode", "2022");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response AddDealerObjResponse = middlewareServicesObject.AddDealerBrandEmi(AddDealerObj, headers,body);
        int StatusCode = AddDealerObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Add dealer for empty mid")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_AddDealerforEmptyMid() {
        AddDealer AddDealerObj = new AddDealer();
        Map<String, String> body = new HashMap<String, String>();
        body.put("brandId", "18260");
        body.put("dealerCode", "40101");
        body.put("mid", "");
        body.put("shopType", "Sports  shop");
        body.put("stateCode", "2022");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response AddDealerObjResponse = middlewareServicesObject.AddDealerBrandEmi(AddDealerObj, headers,body);
        int StatusCode = AddDealerObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Add dealer for empty dealer code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_AddDealerforEmptyDealerCode() {
        AddDealer AddDealerObj = new AddDealer();
        Map<String, String> body = new HashMap<String, String>();
        body.put("brandId", "18260");
        body.put("dealerCode", "");
        body.put("mid", mid);
        body.put("shopType", "Sports  shop");
        body.put("stateCode", "2022");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response AddDealerObjResponse = middlewareServicesObject.AddDealerBrandEmi(AddDealerObj, headers,body);
        int StatusCode = AddDealerObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Add dealer for empty shop type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_AddDealerforEmptyShopType() {
        AddDealer AddDealerObj = new AddDealer();
        Map<String, String> body = new HashMap<String, String>();
        body.put("brandId", "18260");
        body.put("dealerCode", "40101");
        body.put("mid", mid);
        body.put("shopType", "");
        body.put("stateCode", "2022");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response AddDealerObjResponse = middlewareServicesObject.AddDealerBrandEmi(AddDealerObj, headers,body);
        int StatusCode = AddDealerObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Add dealer for empty state code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_AddDealerforEmptyStateCode() {
        AddDealer AddDealerObj = new AddDealer();
        Map<String, String> body = new HashMap<String, String>();
        body.put("brandId", "18260");
        body.put("dealerCode", "40101");
        body.put("mid",mid);
        body.put("shopType", "Sports  shop");
        body.put("stateCode", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response AddDealerObjResponse = middlewareServicesObject.AddDealerBrandEmi(AddDealerObj, headers,body);
        int StatusCode = AddDealerObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch all brand list")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_GetAllBrandList() {
        GetBrandList GetBrandListObj = new GetBrandList();
        Map<String, String> body = new HashMap<String, String>();
        body.put("clientId","P4BIN004445");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response GetBrandListObjResp = middlewareServicesObject.getBrandList(GetBrandListObj, headers,body);
        int StatusCode = GetBrandListObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch all brand list with invalid client id")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_GetAllBrandListWithInvalidClientId() {
        GetBrandList GetBrandListObj = new GetBrandList();
        Map<String, String> body = new HashMap<String, String>();
        body.put("clientId","P4BIN0044451");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response GetBrandListObjResp = middlewareServicesObject.getBrandList(GetBrandListObj, headers,body);
        int StatusCode = GetBrandListObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }


}
