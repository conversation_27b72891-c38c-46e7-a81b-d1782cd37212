package OCL.DIY.EMIOfferingDIY;

import Request.MerchantService.oe.V1.Payment.Order.NotifyCallback;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.v1.sdMerchant.AcceptTermsAndConditionsEdcDIY;
import Request.MerchantService.v2.edc.diyUpgradePlans.CheckEligibilityDIY;
import Request.MerchantService.v2.edc.plans.fetchPlanEdcDIY;
import Request.MerchantService.v2.edc.validateOrder.validateOrderEdcDIY;
import Request.OMS.AuthorizeOMS;
import Request.PG.CreateTerminalInPG;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.OMS.CheckoutAuthorizeOMS;
import Services.Utilities.TestBase;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowEMIOfferingDIY extends BaseMethod
{
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowEMIOfferingDIY.class);

    public static String MerchantToken = "";
    public static String PGToken = "";
    public static String mid = "WCFQAy48942049887909";
    public static String channel = "DIY_P4B_APP";
    public static String solutionType = "diy_upgrade_merchant_plan";
    public static String OrderId = "";
    public static String merchantMobileNumber = "7773330158";
    public static String productId = "";
    public static String custId = "1000858292";
    public static String price = "";
    public static String edcDIYLead="";
    public static String edcPlanId = "61";
    public  static String access_token="";
    public static String leadStage="";
    public static String leadId = "3ef6cc79-fa3a-4e3b-8e5b-2174262887dc";
    public static String item_id="";
    public static  String planType="EMI offering";
    public static String edcRentalCharge="";

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void merchantLoginEMIDIY() throws Exception
    {
        MerchantToken = ApplicantToken(merchantMobileNumber, "paytm@123");
        establishConnectiontoServer(MerchantToken,5);
        LOGGER.info("Merchant token for EDC DIY : " + MerchantToken);
        LOGGER.info(" Inside DB execution to reset lead : ");
        waitForLoad(3000);
        DBConnection.UpdateQueryToCloseLeadsolnlevel2(merchantMobileNumber,"diy_upgrade_merchant_plan","EMI offering");

        /*TestBase testBase = new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '" + merchantMobileNumber + "' and status = '0' and solution_type='diy_upgrade_merchant_plan' and solution_type_level_2='EMI offering';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " + UpdateRes); */

    }
    @Test(priority = 0, groups = {"Regression"}, description = "Create terminal for Emi offering DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createTerminalForEMIOfferingDIY() {
        CreateTerminalInPG CreateTerminalInPGObj=new CreateTerminalInPG(P.TESTDATA.get("CreateTerminalOnPGRequestBody"));
        Map<String, String> body = new HashMap<String, String>();
        Integer SerialNo = Utilities.randomNumberGenerator(5);
       String MapSerialNo = "JYOT"+SerialNo;
        body.put("serialNo",MapSerialNo);
        body.put("mid", mid);
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("modelName", "A910");
        Map<String, String> headers = new HashMap<String, String>();
        PGToken = ApplicantToken("9891497839", "paytm@123");
        headers.put("x-sso-token", PGToken);
        Response CreateTerminalResponse = middlewareServicesObject.CreateTerminalInPGMethod(CreateTerminalInPGObj, headers, body);
        int StatusCode = CreateTerminalResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch plan for Emi offering DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchPlanforEMIOfferingDIY() {
        CreateTerminalInPG v1=new CreateTerminalInPG(P.TESTDATA.get("CreateTerminalOnPGRequestBody"));

        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("planType", planType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        if(StatusCode !=200)
        {
            Response fetchplanEMIOfferingDIYResponse1 = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
            int StatusCode1 = fetchplanEMIOfferingDIYResponse1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }

    }


    @Test(priority = 2, groups = {"Regression"}, description = "Fetch product id for EMI offering DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchProductIdforEMIOfferingDIY() {
        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("planType", planType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        if(StatusCode !=200)
        {
            Response fetchplanEMIOfferingDIYResponse1 = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
            int StatusCode1 = fetchplanEMIOfferingDIYResponse1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }

        productId=fetchplanEMIOfferingDIYResponse.jsonPath().getJsonObject("productId").toString();
        System.out.println("Product id is " + productId);

    }
    @Test(priority = 3, groups = {"Regression"}, description = "Fetch price amount for EMI offering DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchtotalPriceforEMIOfferingDIY() {
        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("planType", planType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        price= fetchplanEMIOfferingDIYResponse.jsonPath().getJsonObject("totalPayablePrice").toString();
        System.out.println("Total Price is " + price);
    }

    @Test(priority = 4, groups = {"Regression"}, description = "Fetch rental price for EMI offering DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchRentalpriceforEMIOfferingDIY() {
        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("planType", planType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        edcRentalCharge = fetchplanEMIOfferingDIYResponse.jsonPath().getJsonObject("rentalCharge").toString();
        System.out.println("EDC rental charge is " + edcRentalCharge);
    }

    @Test(priority = 5, groups = {"Regression"}, description = "Accept terms & conditions for Emi offering DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AcceptTermsAndConditionsEMIOfferingDIY() {
        AcceptTermsAndConditionsEdcDIY AcceptTermsAndConditionsEdcDIYObj = new AcceptTermsAndConditionsEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType", "PROPRIETORSHIP");
        queryParams.put("solution", "diy_map_edc");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response AcceptTermsAndConditionsEdcDIYResponse = middlewareServicesObject.AcceptTermsAndConditionsEdcDIY(AcceptTermsAndConditionsEdcDIYObj, queryParams, headers);
        int StatusCode = AcceptTermsAndConditionsEdcDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 6, groups = {"Regression"}, description = "Check eligibility for Emi offering DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void CheckEligibilityforEMIOfferingDIY()
    {
        CheckEligibilityDIY  CheckEligibilityDIYObj= new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", custId);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);

    }

    @Test(priority = 7, description = "invalid price for emi offering diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidPriceforEmiOfferingDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderEMIOfferingDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("customerId", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("price", "1234");
        body.put("customer_id", custId);
        body.put("planType",planType);

        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);
        String ExpectedMsg = "{\\\"errorMessageCode\\\":\\\"400\\\",\\\"errorMessage\\\":\\\"Invalid order, Price mismatch!, price in request = 11801.0, plan price = 1180.0\\\"}";
        String ActualMsg = respObj.jsonPath().getJsonObject("error_info");
        Assert.assertFalse(ExpectedMsg.contains(ActualMsg));

    }

    @Test(priority = 8, description = "invalid mid for emi offering diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidmidforEmiOfferingDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderEMIOfferingDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<>();
        body.put("mid", "pXXIQZ346652458523041");
        body.put("customerId", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("price", price);
        body.put("customer_id", custId);
        body.put("planType",planType);

        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);
        String ExpectedMsg = "{\\\"errorMessageCode\\\":\\\"400\\\",\\\"errorMessage\\\":\\\"Invalid order, No EDC Machine is active or mapped for this merchant MID currently. Please map machine first to offer EMI services to this merchant account\\\"}";
        String ActualMsg = respObj.jsonPath().getJsonObject("error_info");
        Assert.assertFalse(ExpectedMsg.contains(ActualMsg));

    }

    @Test(priority = 9, description = "invalid plan type for emi offering diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidPlanTypeforEmiOfferingDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderEMIOfferingDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("customerId", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("price", price);
        body.put("customer_id", custId);
        body.put("planType","EMI offering1");

        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);
        String ExpectedMsg = "{\\\"errorMessageCode\\\":\\\"400\\\",\\\"errorMessage\\\":\\\"Invalid order, No active device plan found against plan type =  EMI offering1\\\"}";
        String ActualMsg = respObj.jsonPath().getJsonObject("error_info");
        Assert.assertFalse(ExpectedMsg.contains(ActualMsg));

    }

    @Test(priority = 10, description = "invalid productId for emi offering diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidProductIdforEmiOfferingDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderEMIOfferingDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("customerId", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", "12020193151");
        body.put("edcPlanId", edcPlanId);
        body.put("price", price);
        body.put("customer_id", custId);
        body.put("planType",planType);

        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);
        String ExpectedMsg = "{\\\"errorMessageCode\\\":\\\"400\\\",\\\"errorMessage\\\":\\\"Invalid order, Invalid product id = 1202019315, found against plan type =  EMI offering\\\"}";
        String ActualMsg = respObj.jsonPath().getJsonObject("error_info");
        Assert.assertFalse(ExpectedMsg.contains(ActualMsg));

    }

    @Test(priority = 11, description = "invalid solution type for emi offering diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidSolutionTypeforEmiOfferingDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderEMIOfferingDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("customerId", custId);
        body.put("solutionType", "diy_upgrade_merchant_plan1");
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("price", price);
        body.put("customer_id", custId);
        body.put("planType",planType);

        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(500,statusCode);
        String ExpectedMsg = "{\\\"errorMessageCode\\\":\\\"500\\\",\\\"errorMessage\\\":\\\"Internal Server Error, Please try again after sometime.\\\"}";
        String ActualMsg = respObj.jsonPath().getJsonObject("error_info");
        Assert.assertFalse(ExpectedMsg.contains(ActualMsg));

    }

    @Test(priority = 12, groups = {"Regression"}, description = "Failed to fetch plan for Emi offering DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void failedToFetchedPlansForEMIOfferingDIY() {
        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("planType", "EMI offering1");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }

    @Test(priority = 13, groups = {"Regression"}, description = "Failed to fetch plan for Emi offering DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidMidInFetchPlanForEMIOfferingDIY() {
        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", "xeAMyW616142310899651");
        queryParams.put("solutionType", solutionType);
        queryParams.put("planType", planType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }

    @Test(priority = 14, groups = {"Regression"}, description = "Failed to fetch plan for Emi offering DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidSolutionTypeInFetchPlanForEMIOfferingDIY() {
        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", "diy_upgrade_merchant_plan1");
        queryParams.put("planType", planType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }

    @Test(priority = 15, groups = {"Regression"}, description = "Invalid custid in check eligibility for Emi offering DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidCustIdInCheckEligibilityforEMIOfferingDIY()
    {
        CheckEligibilityDIY  CheckEligibilityDIYObj= new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", "100094189011");
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        String ExpectedMsg="Mid does not belong to given customer";
        String ActualMsg=CheckEligibilityDIYObjResponse.jsonPath().getJsonObject("displayMessage");
        Assert.assertTrue(ExpectedMsg.contains(ActualMsg));
    }

    @Test(priority = 16, groups = {"Regression"}, description = "Invalid mid in check eligibility for Emi offering DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidMIdInCheckEligibilityforEMIOfferingDIY()
    {
        CheckEligibilityDIY  CheckEligibilityDIYObj= new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", custId);
        queryParams.put("mid", "xeAMyW616142310899651111");
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        String ExpectedMsg="Failed to fetch mids for custId";
        String ActualMsg=CheckEligibilityDIYObjResponse.jsonPath().getJsonObject("displayMessage");
        Assert.assertFalse(ExpectedMsg.contains(ActualMsg));
    }

    @Test(priority = 17, description = "Validate order for emi offering diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void validateOrderforEMIOfferingDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderEMIOfferingDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");


        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("customerId", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("price", price);
        body.put("customer_id", custId);
        body.put("planType",planType);

        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);


    }

    @Test(priority = 18, description = "Create access token for emi offering diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createAccessToken()
    {

        AuthorizeOMS authorizeOMSObj=new AuthorizeOMS();
        CheckoutAuthorizeOMS checkoutObj=new CheckoutAuthorizeOMS();
        access_token= checkoutObj.CheckoutAuthorizeviaOMS(authorizeOMSObj).jsonPath().getJsonObject("access_token");
        System.out.println("Access token is " +access_token);

    }

    @Test(priority = 19, description = "Create QR for emi offering diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createQRforEmiofferingDiy()
    {


        OrderId=createQRviaOMS(leadId,merchantMobileNumber,MerchantToken);
        System.out.println("Order id is " + OrderId);


    }
    @Test(priority = 20, description = "Fetch payment status for emi offering diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchPaymentStatusforEmiofferingDiy()
    {
        item_id=fetchOrderviaOMS(OrderId);
        System.out.println("Item id is " + item_id);


    }

    @Test(priority = 21, description = "Notify Order Callback & create lead for Emi Offering diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void CreateLeadEmiofferingDiy() {
        NotifyCallback createLeadEdcDIY = new NotifyCallback(P.TESTDATA.get("EMIOfferingDIYOrderNotify"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("order_id", OrderId);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        Map<String, String> body = new HashMap<>();
        body.put("order_id", OrderId);
        body.put("mid", mid);
        body.put("customer_id", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("customerId",custId);
        body.put("item_id",item_id);

        Response respObj = middlewareServicesObject.OrderNotify(createLeadEdcDIY, queryParams, headers, body);
        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);
        edcDIYLead=respObj.jsonPath().getJsonObject("leadId").toString();
        System.out.println("EDC DIY lead id is " +edcDIYLead);


    }

    @Test(priority = 22, description = "Notify Order Callback for Emi offering", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void CallbackinprogressEmiofferingDiy() {
        NotifyCallback createLeadEdcDIY = new NotifyCallback(P.TESTDATA.get("EMIOfferingDIYOrderNotify"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("order_id", OrderId);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        Map<String, String> body = new HashMap<>();
        body.put("order_id", OrderId);
        body.put("mid", mid);
        body.put("customer_id", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("customerId",custId);
        body.put("item_id",item_id);

        Response respObj = middlewareServicesObject.OrderNotify(createLeadEdcDIY, queryParams, headers, body);
        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);



    }


    @Test(priority = 23, description = "fetch lead for Emioffering diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchLeadEmiofferingDiy()
    {
        FetchLead v1FetchLeadObj=new FetchLead(edcDIYLead);
        XMWCookie=findXMWTokenforPanel("7771216290","paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
        leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Lead stage is "+ leadStage);
    }

    @Test(priority = 24, description = "fetch lead status for Emioffering diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchStatusLeadEmiofferingDiy()
    {
        waitForLoad(20000);
        FetchLead v1FetchLeadObj=new FetchLead(edcDIYLead);
        XMWCookie=findXMWTokenforPanel("7771216290","paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
        leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Lead stage is "+ leadStage);
    }



}
