package OCL.DIY.MapEdcDIY;

import Request.MerchantService.oe.V1.Payment.Order.NotifyCallback;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.v1.sdMerchant.AcceptTermsAndConditionsEdcDIY;
import Request.MerchantService.v2.edc.plans.fetchPlanEdcDIY;
import Request.MerchantService.v2.edc.validateOrder.validateOrderEdcDIY;
import Request.OMS.AuthorizeOMS;
import Services.MechantService.MiddlewareServices;
import Services.OMS.CheckoutAuthorizeOMS;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FlowMapEdcDIY extends BaseMethod {

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowMapEdcDIY.class);

    public static String MerchantToken = "";
    public static String mid = "MPYnOd08861029683358";
    public static String channel = "DIY_P4B_APP";
    public static String solutionType = "diy_map_edc";
    public static String OrderId = "";
    public static String merchantMobileNumber = "5555505151";
    public static String productId = "";
    public static String custId = "1001844592";
    public static String price = "";
    public static String edcDIYLead="";
    public static String edcPlanId = "";
    public  static String access_token="";
    public static String leadStage="";
    public static String leadId = "3ef6cc79-fa3a-4e3b-8e5b-2174262887dc";
    public static String item_id="";

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void merchantLoginDIY() throws Exception {
         MerchantToken = ApplicantToken(merchantMobileNumber, "paytm@123");
        establishConnectiontoServer(merchantMobileNumber,5);

        LOGGER.info("Merchant token for EDC DIY : " + MerchantToken);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch plan for EDC DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchPlanforEDCDIY() {
        fetchPlanEdcDIY fetchPlanEDCDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEdcDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEDCDIYObj, queryParams, headers);
        int StatusCode = fetchplanEdcDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 2, groups = {"Regression"}, description = "Fetch product id for EDC DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchProductIdforEDCDIY() {
        fetchPlanEdcDIY fetchPlanEDCDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEdcDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEDCDIYObj, queryParams, headers);
        int StatusCode = fetchplanEdcDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }
    @Test(priority = 3, groups = {"Regression"}, description = "Fetch price amount for EDC DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchPriceforEDCDIY() {
        fetchPlanEdcDIY fetchPlanEDCDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEdcDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEDCDIYObj, queryParams, headers);
        int StatusCode = fetchplanEdcDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 4, groups = {"Regression"}, description = "Fetch edc plan id for EDC DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchEdcPlanIdforEDCDIY() {
        fetchPlanEdcDIY fetchPlanEDCDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEdcDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEDCDIYObj, queryParams, headers);
        int StatusCode = fetchplanEdcDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 5, groups = {"Regression"}, description = "Accept terms & conditions for EDC DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AcceptTermsAndConditionsEdcDIY() {
        AcceptTermsAndConditionsEdcDIY AcceptTermsAndConditionsEdcDIYObj = new AcceptTermsAndConditionsEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType", "PROPRIETORSHIP");
        queryParams.put("solution", "diy_map_edc");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response AcceptTermsAndConditionsEdcDIYResponse = middlewareServicesObject.AcceptTermsAndConditionsEdcDIY(AcceptTermsAndConditionsEdcDIYObj, queryParams, headers);
        int StatusCode = AcceptTermsAndConditionsEdcDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 6, description = "invalid price for edc diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidPriceforEdcDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderEdcDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("customerId", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("price", "1234");
        body.put("customer_id", custId);

        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);
        String ExpectedMsg = "Order validation failed!";
        String ActualMsg = respObj.jsonPath().getJsonObject("error_title");
        Assert.assertTrue(ExpectedMsg.contains(ActualMsg));

    }
    @Test(priority = 7, description = "invalid plan id for edc diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidPlanIdforEdcDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderEdcDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("customerId", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", "21");
        body.put("price", price);
        body.put("customer_id", custId);

        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);
        String ExpectedMsg = "There seems to be some issue with order processing. Please try after some time.";
        String ActualMsg = respObj.jsonPath().getJsonObject("error");
        Assert.assertTrue(ExpectedMsg.contains(ActualMsg));

    }

    @Test(priority = 8, description = "Validate order for edc diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void validateOrderforEdcDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderEdcDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");


        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("customerId", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("price", price);
        body.put("customer_id", custId);

        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);


    }
    @Test(priority = 9, description = "Create access token for edc diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createAccessToken()
    {

        AuthorizeOMS authorizeOMSObj=new AuthorizeOMS();
        CheckoutAuthorizeOMS checkoutObj=new CheckoutAuthorizeOMS();
         access_token= checkoutObj.CheckoutAuthorizeviaOMS(authorizeOMSObj).jsonPath().getJsonObject("access_token");
        System.out.println("Access token is " +access_token);

    }

    @Test(priority = 10, description = "Create QR for edc diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createQRforEdcDiy()
    {


       OrderId=createQRviaOMS(leadId,merchantMobileNumber,MerchantToken);
        System.out.println("Order id is " + OrderId);


    }
    @Test(priority = 11, description = "Fetch payment status for edc diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchPaymentStatusforEdcDiy()
    {
        item_id=fetchOrderviaOMS(OrderId);
        System.out.println("Item id is " + item_id);


    }

    @Test(priority = 12, description = "Notify Order Callback & create lead for edc diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void CreateLeadEdcDiy() {
        NotifyCallback createLeadEdcDIY = new NotifyCallback(P.TESTDATA.get("EdcDiyOrderNotify"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("order_id", OrderId);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        //   headers.put("Host","<calculated when request is sent>");
        //  headers.put("Content-Length","<calculated when request is sent>");
        Map<String, String> body = new HashMap<>();
        body.put("order_id", OrderId);
        body.put("mid", mid);
        body.put("customer_id", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("customerId",custId);
        body.put("item_id",item_id);

        Response respObj = middlewareServicesObject.OrderNotify(createLeadEdcDIY, queryParams, headers, body);
        int statusCode = respObj.getStatusCode();
         Assert.assertEquals(500,statusCode);



    }

    @Test(priority = 13, description = "Notify Order Callback ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void CallbackinprogressEdcDiy() {
        NotifyCallback createLeadEdcDIY = new NotifyCallback(P.TESTDATA.get("EdcDiyOrderNotify"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("order_id", OrderId);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        Map<String, String> body = new HashMap<>();
        body.put("order_id", OrderId);
        body.put("mid", mid);
        body.put("customer_id", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("customerId",custId);
        body.put("item_id",item_id);

        Response respObj = middlewareServicesObject.OrderNotify(createLeadEdcDIY, queryParams, headers, body);
        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(500,statusCode);



    }


    @Test(priority = 14, description = "fetch lead for edc diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchLeadEdcDiy()
    {
        FetchLead v1FetchLeadObj=new FetchLead(edcDIYLead);
        XMWCookie=findXMWTokenforPanel("7771216290","paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
        leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Lead stage is "+ leadStage);
    }

    @Test(priority = 15, description = "fetch lead status for edc diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchStatusLeadEdcDiy()
    {
        waitForLoad(20000);
        FetchLead v1FetchLeadObj=new FetchLead(edcDIYLead);
        XMWCookie=findXMWTokenforPanel("7771216290","paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
        leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Lead stage is "+ leadStage);
    }


}
