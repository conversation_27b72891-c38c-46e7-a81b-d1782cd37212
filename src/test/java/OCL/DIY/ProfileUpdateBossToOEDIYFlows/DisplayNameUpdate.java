package OCL.DIY.ProfileUpdateBossToOEDIYFlows;

import OCL.DIY.MCO.CreateLead;
import OCL.DIY.MerchantLimitUpgrade.DiyUpgrade;
import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.Revisit.LoanIntentfetchDocumentDetails;
import Services.DBConnection.DBConnection;
import Services.DIYMerchantUpgradeLimit.FetchScreenDetailsUpgradeLimit;
import Services.DIYProfileUpdate.BossToOeLeadUpdate;
import Services.DIYProfileUpdate.FetchDocStatus;
import Services.DIYProfileUpdate.FetchSuggestedNames;
import Services.DIYProfileUpdate.GSTUpdate;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class DisplayNameUpdate extends BaseMethod {

    private static final String MOBILE_NUMBER = "**********";

    private static final String CHANNEL = "DIY_P4B_APP";
    private static final String ENTITY_TYPE = "PROPRIETORSHIP";
    private static final String SOLUTION_TYPE_LEVEL_2 = "display_name_update";
    private static final String SOLUTION = "diy_mco";
    private static final String MID = "TKzhYj13094554733523";
    private static final String PAN = "**********";

    private static final String GSTIN = "07" + PAN + "1Z1";
    private static final String DOC_PATH = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";

    private String token;
    private String requestPath;
    private Response response;
    private String leadId;
    String custId;
    String nameAsPerPan;

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private Map<String, String> headers = new HashMap<>();
    private GSTUpdate gstUpdate = new GSTUpdate();
    private BossToOeLeadUpdate bossToOeLeadUpdate = new BossToOeLeadUpdate();
    FetchSuggestedNames FetchSuggestedNames = new FetchSuggestedNames();
    DiyUpgrade diyUpgrade = new DiyUpgrade();
    CreateLead createLead = new CreateLead();
    FetchScreenDetailsUpgradeLimit fetchScreenDetailsUpgradeLimit = new FetchScreenDetailsUpgradeLimit();
    FetchDocStatus fetchDocStatus = new FetchDocStatus();

    @BeforeClass
    public void setup() throws Exception {
        // Generate session token and setup headers
        token = ApplicantToken(MOBILE_NUMBER, "paytm@123");
        XMWCookie = findXMWTokenforPanel("7771216290", "paytm@123");

        DBConnection dbConnection = new DBConnection();
        dbConnection.UpdateQueryToCloseLead(MOBILE_NUMBER, "diy_mco");

        // Set common headers for all requests
        headers.put("session_token", token);
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");
    }

    private Map<String, String> getCommonParams() {
        Map<String, String> params = new HashMap<>();
        params.put("solution", SOLUTION);
        params.put("channel", CHANNEL);
        params.put("entityType", ENTITY_TYPE);
        params.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);

        return params;
    }


    @Test(priority = 1, description = "To check that create limit upgrade lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createLead() throws JSONException {
        Map<String, String> params = getCommonParams();
        Map<String, String> body = new HashMap<>();

        body.put("mid", MID);
        body.put("closeOpenLead", "true");

        requestPath = "MerchantService/V1/sdMerchant/lead/CreateLeadMerchantLimitUpgradeRequest.json";
        response = gstUpdate.GSTUpdate(requestPath, headers, params, body);

        if (response.getStatusCode() == 200) {
            leadId = response.jsonPath().getString("leadId");

        } else //recall the method if lead is not created
        {
            createLead();
        }

    }


    @Test(priority = 2, description = "Enter non-individual PAN manually", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void aadhaarUpdateAndMerchantSelfie() throws Exception {
        Response responseBody = FetchScreenDetails(leadId, token, ENTITY_TYPE, SOLUTION_TYPE_LEVEL_2);
        boolean AadhaarDigilockerRequired = diyUpgrade.checkNextScreenDetails(responseBody, "AADHAAR_DIGILOCKER");

        if (AadhaarDigilockerRequired) {
            createLead.UpdateLeadAadhaar(leadId, token);
            createLead.Upload_AadhaarDoc(leadId, token);
            createLead.Upload_MerchantSelfie(leadId, token);
        }

    }


    @Test(priority = 3, description = "Enter non-individual PAN manually", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UpdateBank() throws Exception {
        Response responseBody = FetchScreenDetails(leadId, token, ENTITY_TYPE, SOLUTION_TYPE_LEVEL_2);
        boolean BankRequired = diyUpgrade.checkNextScreenDetails(responseBody, "BANK_DETAILS");
        if (BankRequired) {
            createLead.SubmitBankLead(leadId, token);
            Response response = FetchDocStatus(leadId, token, ENTITY_TYPE, SOLUTION_TYPE_LEVEL_2, SOLUTION);
            boolean isDocRequired = checkDocStatus(response, "BANK_DETAILS");
            if (isDocRequired) {

                String endPoint = P.API.get("UploadBankProof");

                Map<String, String> params = new HashMap<String, String>();
                params.put("docType", "bankProof");
                params.put("docProvided", "BankStatement");
                params.put("entityType", ENTITY_TYPE);
                params.put("solutionType", "diy_mco");
                params.put("channel", "DIY_P4B_APP");
                params.put("solutionLeadId", leadId);
                params.put("pageNo", "1");

                Map<String, String> header = new HashMap<String, String>();
                header.put("Content-Type", "multipart/form-data");
                header.put("session_token", token);


                File uploadFile = new File(DOC_PATH);

                Response resp = createLead.UploadDocInAPI(uploadFile, params, header, endPoint);
            }
        }

    }

    @Test(priority = 4, description = "Enter non-individual PAN manually", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchDisplayName() throws Exception {
        Map<String, String> params = getCommonParams();
        params.put("leadId", leadId);
        params.put("entityType", ENTITY_TYPE);
        params.put("solutionType", SOLUTION);
        params.put("mid", MID);
        params.put("type", "DISPLAY_NAME");
        params.put("channel", CHANNEL);
        params.put("solutionSubType", SOLUTION_TYPE_LEVEL_2);

        requestPath = "MerchantService/V1/profile/update/fetchSuggestedName.json";
        response = FetchSuggestedNames.FetchSuggestedNames(requestPath, headers, params);

        System.out.println("response123 " + response.asString());
        String responseString = response.asString();

        JSONObject jsonResponse = new JSONObject(responseString);

        // Extract the "suggestedDisplayNames" array
        JSONArray suggestedDisplayNames = jsonResponse.getJSONArray("suggestedDisplayNames");

        for (int i = 0; i < suggestedDisplayNames.length(); i++) {
            JSONObject displayNameObject = suggestedDisplayNames.getJSONObject(i);

            if ("pan".equals(displayNameObject.getString("nameType"))) {
                nameAsPerPan = displayNameObject.getString("displayName");
                break;
            }
        }

    }

    @Test(priority = 5, description = "Submit lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateDisplayName() throws JSONException {
        Map<String, String> params = getCommonParams();
        params.put("mid", MID);
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<>();
        body.put("partialSave", "false");
        body.put("DISPLAY_NAME", nameAsPerPan);
        body.put("DISPLAY_NAME_TYPE", "bank");

        requestPath = "MerchantService/V1/sdMerchant/lead/updateLead/updateDisplayName.json";
        response = gstUpdate.GSTUpdate(requestPath, headers, params, body);

    }


    @Test(priority = 6, description = "Submit lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void SubmitLead() throws JSONException {
        headers.put("Content-Type", "application/json");
        Map<String, String> params = getCommonParams();
        params.put("leadId", leadId);

        Map<String, Object> body = new HashMap<>();
        body.put("partialSave", "false");
        body.put("IS_AGREEMENT_ACCEPTED", "true");

        requestPath = "MerchantService/V1/sdMerchant/lead/updateLead/LimitUpgradeSubmitLeadRequest.json";
        response = bossToOeLeadUpdate.bossToOeLeadUpdate(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
    }

//    @Test(priority = 7, description = "", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void RegisteredAddressUpdateLeadFetchDocStatus() throws Exception {
//

//        custId = GetResourceOwnerId(MOBILE_NUMBER, "paytm@123");
////        mid = FetchMID(custId);
//
//        //    this.token = BaseMethod.ApplicantToken(MOBILE_NUMBER, "paytm@123");
//        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();
//
//        Map<String, String> headers = new HashMap<>();
//        headers.put("session_token", token); // Updated from first list
//        headers.put("accept", "application/json, text/plain, */*"); // No change
//        headers.put("content-type", "multipart/json");
//        headers.put("version", "7.2.8"); // No change
//        headers.put("deviceidentifier", "Xiaomi-RedmiNote9Pro-efc28fc97cede738"); // Updated from first list
//        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
//        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged
//
//        Map<String, String> queryParams = new HashMap<>();
//        queryParams.put("solution", "diy_mco");
//        queryParams.put("entityType", "PROPRIETORSHIP");
//        queryParams.put("leadId", leadId);
//        queryParams.put("merchantCustId", custId);
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "registered_address_update");
//
//        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj, headers, queryParams);

//        shopInsidePhotoDMSID = respObj.path("uploadedDocDetailsSet[0].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
//        AadharPhotoDMSID2 = respObj.path("uploadedDocDetailsSet[4].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['2']");
//        AadharPhotoDMSID1 = respObj.path("uploadedDocDetailsSet[4].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");   //   BankStatementDMSID = respObj.path("uploadedDocDetailsSet[3].uploadedDocs[0].uuid");
//        BusinessOwnerPhotoDMSID = respObj.path("uploadedDocDetailsSet[3].uploadedDocs[0].uuid");
//        shopFrontPhotoDMSID = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
//        PanphotoDMSID = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].uuid");
//        qrStickerPhotoDMSID = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
//
//        System.out.println(BusinessOwnerPhotoDMSID + AadharPhotoDMSID1 + AadharPhotoDMSID2 + PanphotoDMSID + BankStatementDMSID + shopFrontPhotoDMSID + shopInsidePhotoDMSID + qrStickerPhotoDMSID);
//
//    }
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "Registered Address update QC", priority = 8, dependsOnMethods = "RegisteredAddressUpdateLeadFetchDocStatus")
//    public void RegisteredAddressUpdateQC() throws Exception {
//

//
//        DBConnection dbConnectionObj = new DBConnection();
//        int Ubmid = dbConnectionObj.getUserBusinessMappingId(MOBILE_NUMBER, "diy_mco", "registered_address_update");
//        dbConnectionObj.assignAgentViaDB("1152", Ubmid);
//        Long billingAddressUpdateWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);
//        //Save value of billingAddressUpdateWorkflowStatusId in string
//        wfsidBillingAddressUpdate = String.valueOf(billingAddressUpdateWorkflowStatusId);
//
//        requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadBossToOeProp.json";
//        EditLead EditLeadObj = new EditLead(leadId, requestPath);
//        EditLeadObj.getProperties().setProperty("uuidAadharPhoto1", AadharPhotoDMSID1);
//        EditLeadObj.getProperties().setProperty("uuidAadharPhoto2", AadharPhotoDMSID2);
//        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto", BusinessOwnerPhotoDMSID);
//        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto1", BusinessOwnerPhotoDMSID);
//        //   EditLeadObj.getProperties().setProperty("uuidPanPhoto", PanphotoDMSID);
//        //      EditLeadObj.getProperties().setProperty("uuidBankStatementPhoto", BankStatementDMSID);
//        EditLeadObj.getProperties().setProperty("uuidShopFrontPhoto", shopFrontPhotoDMSID);
//        EditLeadObj.getProperties().setProperty("uuidShopFrontPhoto1", shopFrontPhotoDMSID);
//        EditLeadObj.getProperties().setProperty("uuidShopInsidePhoto1", shopInsidePhotoDMSID);
//        EditLeadObj.getProperties().setProperty("uuidShopInsidePhoto", shopInsidePhotoDMSID);
//        EditLeadObj.getProperties().setProperty("uuidQrStickerPhoto1", qrStickerPhotoDMSID);
//        EditLeadObj.getProperties().setProperty("uuidQrStickerPhoto", qrStickerPhotoDMSID);
//        //   EditLeadObj.getProperties().setProperty("uuidBusinessProof", businessProofDMSID);
//        EditLeadObj.getProperties().setProperty("workflowStatusId", wfsidBillingAddressUpdate);
//        Map<String, String> queryParams = new HashMap<>();
//        queryParams.put("action", "SUBMIT");
//        Map<String, String> headers = new HashMap<>();
//        headers.put("Host", "goldengate-staging6.paytm.com");
//        headers.put("content-type", "application/json; charset=UTF-8");
//        headers.put("Cookie", XMWCookie);
//        Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);
//
//    }
//    }


    public Response FetchScreenDetails(String leadId, String Token, String entityType, String solutionTypeLevel2) throws JSONException {

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<>();
        params.put("leadId", leadId);
        params.put("fetchStrategy", "SCREEN_DETAILS");
        params.put("entityType", entityType);
        params.put("solutionTypeLevel2", solutionTypeLevel2);


        requestPath = "MerchantService/V1/sdMerchant/lead/fetchScreenDetailsUpgradeLimitRequest.json";
        response = fetchScreenDetailsUpgradeLimit.FetchScreenDetailsUpgradeLimit(requestPath, headers, params);

        int httpcode = response.getStatusCode();
        Assert.assertEquals(httpcode, 200);
        Response responseBody = response;
        System.out.println("responseBody " + responseBody.asString());
        return responseBody;
    }

    public Response FetchDocStatus(String leadId, String Token, String entityType, String solutionTypeLevel2, String solutionType) throws JSONException {

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<>();
        params.put("leadId", leadId);
        params.put("solution", solutionType);
        params.put("entityType", entityType);
        params.put("solutionTypeLevel2", solutionTypeLevel2);


        requestPath = "MerchantService/V2/UpgradeMid/Doc/Status/DocStatusRequest.json";
        response = fetchDocStatus.FetchDocumentStatus(requestPath, headers, params);

        int httpcode = response.getStatusCode();
        Assert.assertEquals(httpcode, 200);
        Response responseBody = response;
        System.out.println("responseBody " + responseBody.asString());
        return responseBody;
    }


    public boolean checkDocStatus(Response FetchDocStatus, String docType) {
        // Convert the response body to a JSONObject
        JsonPath jsonPath = FetchDocStatus.jsonPath();

        boolean isScreenPresent = jsonPath.getList("nextScreenDetails.docType")
                .stream()
                .anyMatch(screen -> docType.equals(screen));

        return isScreenPresent;

    }
}
