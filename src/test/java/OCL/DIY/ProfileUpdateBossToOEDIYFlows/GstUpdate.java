package OCL.DIY.ProfileUpdateBossToOEDIYFlows;

import OCL.DIY.MCO.CreateLead;
import OCL.DIY.MerchantLimitUpgrade.FetchScreenDetail;
import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.Revisit.LoanIntentfetchDocumentDetails;
import Services.DBConnection.DBConnection;
import Services.DIYMerchantUpgradeLimit.FetchScreenDetailsUpgradeLimit;
import Services.DIYProfileUpdate.BossToOeLeadUpdate;
import Services.DIYProfileUpdate.GSTUpdate;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class GstUpdate extends BaseMethod {


    // Test constants
    Utilities Util = new Utilities();
    String num2 = Util.randomMobileNumberGenerator();
    String num = num2.replaceFirst("5", "9");

    private static final String DOC_PATH = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";
    private static final String SOLUTION = "diy_mco";
    private static final String ENTITY_TYPE = "PROPRIETORSHIP";
    private static final String CHANNEL = "DIY_P4B_APP";
    private static final String SUCCESS_MESSAGE = "Request Submitted Successfully";
    private static final String SOLUTION_TYPE_LEVEL_2 = "gst_update";
    private static final String SOURCE = "DIY_P4B_APP";
    String PAN;
    String MID;
    String GSTIN = "10" + PAN + "1Z1";

    // Instance variables
    private String token;
    private String leadId;
    private String requestPath;
    private Response fetchResponse;

    private GSTUpdate gstUpdate = new GSTUpdate();
    private BossToOeLeadUpdate bossToOeLeadUpdate = new BossToOeLeadUpdate();
    private Map<String, String> headers = new HashMap<>();
    CreateLead createLead = new CreateLead();
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    String custId;
    String wfsid;
    String wfsidGstUpdate;
    String BusinessOwnerPhotoDMSID = "";
    String AadharPhotoDMSID1 = "";
    String AadharPhotoDMSID2 = "";
    String PanphotoDMSID = "";
    String BankStatementDMSID = "";
    String shopFrontPhotoDMSID = "";
    String shopInsidePhotoDMSID = "";
    String qrStickerPhotoDMSID = "";
    String businessProofDMSID = "";
    FetchScreenDetail fetchScreenDetail = new FetchScreenDetail();
    FetchScreenDetailsUpgradeLimit fetchScreenDetailsUpgradeLimit = new FetchScreenDetailsUpgradeLimit();

    @BeforeClass
    public void setup() throws Exception {
        // Generate token and set necessary headers
        if (Util.createNewAuthUser(num, "Paytm@123")) {
            token = BaseMethod.ApplicantToken(num, "Paytm@123");
        } else {
            Assert.assertFalse(false, "Number was not registered at Oauth end");
        }

        // Set common headers for all requests
        headers.put("session_token", token);
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");
    }


    private Map<String, String> getCommonParams() {
        Map<String, String> params = new HashMap<>();
        params.put("solution", SOLUTION);
        params.put("channel", CHANNEL);
        params.put("entityType", ENTITY_TYPE);
        params.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);

        return params;
    }

    @Test(priority = 1, description = "To create DIY MCO Retail lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createLeadDiyMCO() throws Exception {
        leadId = createLead.createLead(token);
        createLead.GetStarted(leadId, token);
        createLead.AadhaarOcr(leadId, token);
        createLead.UpdateLeadAadhaar(leadId, token);
        createLead.Upload_AadhaarDoc(leadId, token);
        createLead.Upload_MerchantSelfie(leadId, token);
        PAN = createLead.UpdateLeadPan(leadId, token);
        createLead.Upload_panDocument(leadId, token);
        createLead.UpdateBusinessCat(leadId, token);
        createLead.UpdatePennyDrop(leadId, token);
        createLead.SubmitBankLead(leadId, token);
        createLead.UpdateShopAddress(leadId, token);
        createLead.SubmitTheLead(leadId, token);

        custId = GetResourceOwnerId(num, "Paytm@123");
    }

    @Test(priority = 2, description = "", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void DiyMcoLeadFetchDocStatus() throws Exception {

        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();
        Map<String, String> headers = new HashMap<>();
        headers.put("session_token", token); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("version", "7.2.8"); // No change
        headers.put("deviceidentifier", "Xiaomi-RedmiNote9Pro-efc28fc97cede738"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solution", "diy_mco");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);
        queryParams.put("merchantCustId", custId);
        queryParams.put("channel", "DIY_P4B_APP");


        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj, headers, queryParams);

        Assert.assertEquals(respObj.getStatusCode(), 200);
        BusinessOwnerPhotoDMSID = respObj.path("uploadedDocDetailsSet[0].uploadedDocs[0].uuid");
        AadharPhotoDMSID1 = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
        AadharPhotoDMSID2 = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['2']");
        PanphotoDMSID = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].uuid");
        System.out.println(BusinessOwnerPhotoDMSID + AadharPhotoDMSID1 + AadharPhotoDMSID2 + PanphotoDMSID);

    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "DIY MCO Retail Onboarding QC", priority = 3, dependsOnMethods = "DiyMcoLeadFetchDocStatus")
    public void DiyMcoRetailOnboardingLeadQC() throws Exception {

        DBConnection dbConnectionObj = new DBConnection();
        int Ubmid = dbConnectionObj.getUserBusinessMappingId(num, "diy_mco");
        dbConnectionObj.assignAgentViaDB("1152", Ubmid);
        Long DiyMCOIndividualWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);
        wfsid = String.valueOf(DiyMCOIndividualWorkflowStatusId);

        requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadMcoIndividualWithPan.json";
        EditLead EditLeadObj = new EditLead(leadId, requestPath);
        EditLeadObj.getProperties().setProperty("uuidAadharPhoto1", AadharPhotoDMSID1);
        EditLeadObj.getProperties().setProperty("uuidAadharPhoto2", AadharPhotoDMSID2);
        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto", BusinessOwnerPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto1", BusinessOwnerPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidPanPhoto", PanphotoDMSID);
        EditLeadObj.getProperties().setProperty("workflowStatusId", wfsid);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("action", "SUBMIT");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("Cookie", XMWCookie);

        Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);

        Thread.sleep(120000); // Wait for 3 minutes
    }


    @Test(priority = 4, description = "add channel callback", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addChannelCallback() throws Exception {


        Thread.sleep(90000); // Wait
        Response addchannel = middlewareServicesObject.addChannelCallback(custId, leadId);

    }


    // Test for creating a GST update lead
    @Test(priority = 5, description = "Check that create limit upgrade lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createLeadGstUpdate() throws JSONException, InterruptedException {
        Thread.sleep(200000); // Wait for QR_MAPPING_SUCCESS

        MID = FetchMID(custId);

        Map<String, String> params = getCommonParams();
        Map<String, String> body = new HashMap<>();
        body.put("mid", MID);
        body.put("closeOpenLead", "true");

        requestPath = "MerchantService/v1/sdMerchant/lead/CreateLeadMerchantLimitUpgradeRequest.json";
        fetchResponse = gstUpdate.GSTUpdate(requestPath, headers, params, body);

        Assert.assertEquals(fetchResponse.getStatusCode(), 200);
        this.leadId = fetchResponse.jsonPath().getString("leadId");
    }

    @Test(priority = 6, description = "Check that create limit upgrade lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateAadhaarAndMerchantSelfie() throws Exception {


        Response rs = fetchScreenDetail.fetchScreenDetails(token, "SCREEN_DETAILS", "gst_update", leadId);
        if (rs.getBody().asString().contains("\"eligibleScreen\": \"AADHAR_PAN_MATCH_FALSE_SCREEN\".")) {

            createLead.UpdateLeadAadhaar(leadId, token);
            createLead.Upload_AadhaarDoc(leadId, token);
            createLead.Upload_MerchantSelfie(leadId, token);

        }
        }

        // Test for entering non-individual PAN manually
        @Test(priority = 6, description = "Enter non-individual PAN manually", groups = {"Regression"})
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void testEnterNonIndividualPanManually () throws JSONException {
            Map<String, String> params = getCommonParams();
            params.put("leadId", leadId);

            Map<String, Object> body = new HashMap<>();
            body.put("partialSave", "true");
            body.put("pan", PAN);
            body.put("gstin", GSTIN);
            body.put("PAN", PAN);
            body.put("BUSINESS_ENTITY", ENTITY_TYPE);
            body.put("KYB_BUSINESS_ID", "");

            requestPath = "MerchantService/v1/sdMerchant/lead/updatePanLeadRequest.json";
            fetchResponse = bossToOeLeadUpdate.bossToOeLeadUpdate(requestPath, headers, params, body);

            Assert.assertEquals(fetchResponse.getStatusCode(), 200);
        }


        @Test(priority = 7, description = "Upload Document", groups = {"Regression"})
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        @DataProvider(name = "documentData")
        public Object[][] documentData () {
            return new Object[][]{
                    //   {"POIAWB", "certificateOfPractice_poiawb"},
                    //   {"coi", "COI"},
                    {"businessProof", "GSTCertificate"},
                    //   {"POAWTN", "franchiseeAgreement_poawtn"},
                    {"shopInnerPhoto", "shopInnerPhoto"},
                    {"qrStickerPhoto", "qrSticker1"},
                    {"shopFrontPhoto", "shopFrontPhoto"},
                    {"bankProof", "BankStatement"}
//                {"pan", "pan"}

            };
        }

        @Test(priority = 8, description = "Upload Documents Dynamically", dataProvider = "documentData", groups = {"Regression"})
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void uploadDocument (String docType, String docProvided) throws Exception {
            String endPoint = P.API.get("UploadDoc");
            headers.put("Content-Type", "multipart/form-data");

            Map<String, String> params = new HashMap<>();
            params.put("solutionType", SOLUTION);
            params.put("channel", CHANNEL);
            params.put("entityType", ENTITY_TYPE);
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "1");
            params.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
            params.put("docType", docType);
            params.put("docProvided", docProvided);

            File uploadFile = new File(DOC_PATH);
            Response resp = UploadDocInAPI(uploadFile, params, headers, endPoint);

            int statusCode = resp.getStatusCode();
            Assert.assertEquals(statusCode, 200);
        }


        @Test(priority = 9, description = "Submit the lead", groups = {"Regression"})
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void testSubmitLead () throws JSONException {
            headers.put("Content-Type", "application/json");
            Map<String, String> params = getCommonParams();
            params.put("leadId", leadId);

            Map<String, Object> body = new HashMap<>();
            body.put("partialSave", "false");
            body.put("IS_AGREEMENT_ACCEPTED", "true");

            requestPath = "MerchantService/V1/sdMerchant/lead/updateLead/LimitUpgradeSubmitLeadRequest.json";
            fetchResponse = bossToOeLeadUpdate.bossToOeLeadUpdate(requestPath, headers, params, body);

            Assert.assertEquals(fetchResponse.getStatusCode(), 200);
        }


        @Test(priority = 10, description = "", groups = {"Regression"})
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void gstUpdateLeadFetchDocStatus () throws Exception {

            LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();
            Map<String, String> headers = new HashMap<>();
            headers.put("session_token", token); // Updated from first list
            headers.put("accept", "application/json, text/plain, */*"); // No change
            headers.put("content-type", "multipart/json");
            headers.put("version", "7.2.8"); // No change
            headers.put("deviceidentifier", "Xiaomi-RedmiNote9Pro-efc28fc97cede738"); // Updated from first list
            headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
            headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solution", "diy_mco");
            queryParams.put("entityType", "PROPRIETORSHIP");
            queryParams.put("leadId", leadId);
            queryParams.put("merchantCustId", custId);
            queryParams.put("channel", "DIY_P4B_APP");
            queryParams.put("solutionTypeLevel2", "business_name_update");

            Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj, headers, queryParams);

            shopInsidePhotoDMSID = respObj.path("uploadedDocDetailsSet[0].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
            AadharPhotoDMSID2 = respObj.path("uploadedDocDetailsSet[4].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['2']");
            AadharPhotoDMSID1 = respObj.path("uploadedDocDetailsSet[4].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
            //   BankStatementDMSID = respObj.path("uploadedDocDetailsSet[3].uploadedDocs[0].uuid");
            BusinessOwnerPhotoDMSID = respObj.path("uploadedDocDetailsSet[3].uploadedDocs[0].uuid");
            shopFrontPhotoDMSID = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
            PanphotoDMSID = respObj.path("uploadedDocDetailsSet[5].uploadedDocs[0].uuid");
            qrStickerPhotoDMSID = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");

            System.out.println(BusinessOwnerPhotoDMSID + AadharPhotoDMSID1 + AadharPhotoDMSID2 + PanphotoDMSID + BankStatementDMSID + shopFrontPhotoDMSID + shopInsidePhotoDMSID + qrStickerPhotoDMSID);

        }

        @Owner(emailId = "<EMAIL>", isAutomated = true)
        @Test(description = "GST Update QC", priority = 11, dependsOnMethods = "gstUpdateLeadFetchDocStatus")
        public void GstUpdateQC () throws Exception {

            DBConnection dbConnectionObj = new DBConnection();
            int Ubmid = dbConnectionObj.getUserBusinessMappingId(num, "diy_mco", SOLUTION_TYPE_LEVEL_2);
            dbConnectionObj.assignAgentViaDB("1152", Ubmid);
            Long gstUpdateWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);

            //Save value of businessNameUpdateWorkflowStatusId in string
            wfsidGstUpdate = String.valueOf(gstUpdateWorkflowStatusId);

            requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadBossToOeProp.json";
            EditLead EditLeadObj = new EditLead(leadId, requestPath);
            EditLeadObj.getProperties().setProperty("uuidAadharPhoto1", AadharPhotoDMSID1);
            EditLeadObj.getProperties().setProperty("uuidAadharPhoto2", AadharPhotoDMSID2);
            EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto", BusinessOwnerPhotoDMSID);
            EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto1", BusinessOwnerPhotoDMSID);
            EditLeadObj.getProperties().setProperty("uuidPanPhoto", PanphotoDMSID);
            //      EditLeadObj.getProperties().setProperty("uuidBankStatementPhoto", BankStatementDMSID);
            EditLeadObj.getProperties().setProperty("uuidShopFrontPhoto", shopFrontPhotoDMSID);
            EditLeadObj.getProperties().setProperty("uuidShopInsidePhoto", shopInsidePhotoDMSID);
            EditLeadObj.getProperties().setProperty("uuidQrStickerPhoto1", qrStickerPhotoDMSID);
            //   EditLeadObj.getProperties().setProperty("uuidBusinessProof", businessProofDMSID);
            EditLeadObj.getProperties().setProperty("workflowStatusId", wfsidGstUpdate);
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("action", "SUBMIT");
            Map<String, String> headers = new HashMap<>();
            headers.put("Host", "goldengate-staging6.paytm.com");
            headers.put("content-type", "application/json; charset=UTF-8");
            headers.put("Cookie", XMWCookie);
            Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);

        }


        // Helper method to upload documents to the API
        public Response UploadDocInAPI (File
        uploadFile, Map < String, String > params, Map < String, String > header, String endPoint) throws Exception {
            Response resp = null;
            String baseURI = P.API.get("api_url");
            RequestSpecification spec;
            try {
                spec = new RequestSpecBuilder().setBaseUri(baseURI).build();
                resp = RestAssured.given().multiPart(uploadFile).spec(spec).relaxedHTTPSValidation().queryParams(params).headers(header)
                        .post(endPoint);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return resp;
        }
    public Response FetchScreenDetails(String leadId) throws JSONException {
        Map<String, String> headers = new HashMap<>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", token);

        Map<String, String> params = new HashMap<>();
        //  params.put("solution", "diy_mco");
        params.put("leadId", leadId);
        params.put("fetchStrategy", "SCREEN_DETAILS");
        params.put("entityType", "INDIVIDUAL");


        requestPath = "MerchantService/V1/sdMerchant/lead/fetchScreenDetailsUpgradeLimitRequest.json";
        fetchResponse = fetchScreenDetailsUpgradeLimit.FetchScreenDetailsUpgradeLimit(requestPath, headers, params);

        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 200);
        Response responseBody = fetchResponse;
        return responseBody;
    }


    public boolean checkNextScreenDetails(Response checkNextScreenResponse, String screenName) {
        // Convert the response body to a JSONObject
        JsonPath jsonPath = checkNextScreenResponse.jsonPath();

        // Check if the "nextScreenDetails" array contains the value "AADHAAR_DIGILOCKER" for "eligibleScreen"
        boolean isScreenPresent = jsonPath.getList("nextScreenDetails.eligibleScreen")
                .stream()
                .anyMatch(screen -> screenName.equals(screen));

        return isScreenPresent;

    }
    }