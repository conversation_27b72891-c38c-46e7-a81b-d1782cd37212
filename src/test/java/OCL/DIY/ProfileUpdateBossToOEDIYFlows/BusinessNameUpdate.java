package OCL.DIY.ProfileUpdateBossToOEDIYFlows;

import OCL.DIY.MCO.CreateLead;
import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.Revisit.LoanIntentfetchDocumentDetails;
import Services.DBConnection.DBConnection;
import Services.DIYMerchantUpgradeLimit.FetchScreenDetailsUpgradeLimit;
import Services.DIYProfileUpdate.BossToOeLeadUpdate;
import Services.DIYProfileUpdate.GSTUpdate;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;

import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class BusinessNameUpdate extends BaseMethod {


    // Constants for configuration
    private static final String MOBILE_NUMBER = "**********";
    private static final String CHANNEL = "DIY_P4B_APP";
    private static final String ENTITY_TYPE = "PROPRIETORSHIP";
    private static final String SOLUTION_TYPE_LEVEL_2 = "business_name_update";
    private static final String SOLUTION = "diy_mco";
    private static final String MID = "LSlgAK42019998045135";
    private static final String PAN = "**********";
    private static final String GSTIN = "07" + PAN + "1Z1";
    private static final String DOC_PATH = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";

    private String token;
    private String requestPath;
    private Response response;
    private String leadId;
    String wfsidBillingAddressUpdate;
    String custId;
    String BusinessOwnerPhotoDMSID = "";
    String AadharPhotoDMSID1 = "";
    String AadharPhotoDMSID2 = "";
    String PanphotoDMSID = "";
    String BankStatementDMSID = "";
    String shopFrontPhotoDMSID = "";
    String shopInsidePhotoDMSID = "";
    String qrStickerPhotoDMSID = "";
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private Map<String, String> headers = new HashMap<>();
    private GSTUpdate gstUpdate = new GSTUpdate();
    private BossToOeLeadUpdate bossToOeLeadUpdate = new BossToOeLeadUpdate();
    CreateLead createLead = new CreateLead();
    FetchScreenDetailsUpgradeLimit fetchScreenDetailsUpgradeLimit = new FetchScreenDetailsUpgradeLimit();

    @BeforeClass
    public void setup() throws Exception {
        // Generate session token and setup headers
        token = ApplicantToken(MOBILE_NUMBER, "Paytm@123");
        XMWCookie = findXMWTokenforPanel("**********", "paytm@123");

        DBConnection dbConnection = new DBConnection();
        dbConnection.UpdateQueryToCloseLead(MOBILE_NUMBER, "diy_mco");

        // Set common headers for all requests
        headers.put("session_token", token);
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");
    }

    private Map<String, String> getCommonParams() {
        Map<String, String> params = new HashMap<>();
        params.put("solution", SOLUTION);
        params.put("channel", CHANNEL);
        params.put("entityType", ENTITY_TYPE);
        params.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);

        return params;
    }


    @Test(priority = 1, description = "To check that create limit upgrade lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createLead() throws JSONException {
        Map<String, String> params = getCommonParams();
        Map<String, String> body = new HashMap<>();

        body.put("mid", MID);
        body.put("closeOpenLead", "true");

        requestPath = "MerchantService/v1/sdMerchant/lead/CreateLeadMerchantLimitUpgradeRequest.json";
        response = gstUpdate.GSTUpdate(requestPath, headers, params, body);
        if (response.getStatusCode() == 200) {
            leadId = response.jsonPath().getString("leadId");

        } else //recall the method if lead is not created
        {
            createLead();
        }

    }


    @Test(priority = 2, description = "Enter non-individual PAN manually", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void enterAadharAndMerchantSelfie() throws Exception {
        createLead.UpdateLeadAadhaar(leadId, token);
        createLead.Upload_AadhaarDoc(leadId, token);
        createLead.Upload_MerchantSelfie(leadId, token);
    }

    @Test(priority = 3, description = "Enter non-individual PAN manually", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void enterNonIndividualPanManually() throws JSONException {
        Map<String, String> params = getCommonParams();
        params.put("leadId", leadId);

        Map<String, Object> body = new HashMap<>();
        body.put("partialSave", "true");
        body.put("pan", PAN);
        body.put("gstin", GSTIN);
        body.put("PAN", PAN);
        body.put("BUSINESS_ENTITY", ENTITY_TYPE);
        body.put("KYB_BUSINESS_ID", "");

        requestPath = "MerchantService/v1/sdMerchant/lead/updatePanLeadRequest.json";
        response = bossToOeLeadUpdate.bossToOeLeadUpdate(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
    }

    // Document upload with dynamic data provider
    @Test(priority = 4, description = "Upload Document", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @DataProvider(name = "documentData")
    public Object[][] documentData() {
        return new Object[][]{
                //      {"POIAWB", "certificateOfPractice_poiawb"},
                //     {"coi", "COI"},
                {"businessProof", "GSTCertificate"},
                //     {"POAWTN", "franchiseeAgreement_poawtn"},
                {"shopInnerPhoto", "shopInnerPhoto"},
                {"qrStickerPhoto", "qrSticker1"},
                {"shopFrontPhoto", "shopFrontPhoto"},
                {"bankProof", "BankStatement"}
//                {"pan", "pan"}
        };
    }

    @Test(priority = 5, description = "Upload Documents Dynamically", dataProvider = "documentData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void uploadDocument(String docType, String docProvided) throws Exception {
        String endPoint = P.API.get("UploadDoc");
        headers.put("Content-Type", "multipart/form-data");

        File uploadFile = new File(DOC_PATH);

        Map<String, String> params = new HashMap<>();
        params.put("solutionType", SOLUTION);
        params.put("channel", CHANNEL);
        params.put("entityType", ENTITY_TYPE);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        params.put("docType", docType);
        params.put("docProvided", docProvided);

        response = uploadDocumentToAPI(uploadFile, params, endPoint);
        Assert.assertEquals(response.getStatusCode(), 200);
    }

    // Helper method to upload documents to API
    private Response uploadDocumentToAPI(File uploadFile, Map<String, String> params, String endPoint) {
        String baseURI = P.API.get("api_url");
        RequestSpecification spec = new RequestSpecBuilder().setBaseUri(baseURI).build();

        try {
            return RestAssured.given()
                    .multiPart(uploadFile)
                    .spec(spec)
                    .relaxedHTTPSValidation()
                    .queryParams(params)
                    .headers(headers)
                    .post(endPoint);
        } catch (Exception e) {
            return null;
        }
    }

    @Test(priority = 6, description = "Submit lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void SubmitLead() throws JSONException {
        headers.put("Content-Type", "application/json");
        Map<String, String> params = getCommonParams();
        params.put("leadId", leadId);

        Map<String, Object> body = new HashMap<>();
        body.put("partialSave", "false");
        body.put("IS_AGREEMENT_ACCEPTED", "true");

        requestPath = "MerchantService/V1/sdMerchant/lead/updateLead/LimitUpgradeSubmitLeadRequest.json";
        response = bossToOeLeadUpdate.bossToOeLeadUpdate(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
    }

    public String createLead(String mid, String newToken) {

        Map<String, String> params = getCommonParams();
        Map<String, String> headers = new HashMap<>();
        headers.put("session_token", newToken);

        Map<String, String> body = new HashMap<>();

        body.put("mid", mid);
        body.put("closeOpenLead", "true");

        requestPath = "MerchantService/v1/sdMerchant/lead/CreateLeadMerchantLimitUpgradeRequest.json";
        response = gstUpdate.GSTUpdate(requestPath, headers, params, body);

        if (response.getStatusCode() == 200) {
            leadId = response.jsonPath().getString("leadId");
            return leadId;
        } else //recall the method if lead is not created
        {
            return createLead(mid, newToken);
        }
    }


    public void enterNonIndividualPanManually(String pan, String leadId, String newToken) {
        Map<String, String> params = getCommonParams();
        params.put("leadId", leadId);
        Map<String, String> headers = new HashMap<>();
        headers.put("session_token", newToken);

        Map<String, Object> body = new HashMap<>();
        body.put("partialSave", "true");
        body.put("pan", pan);
        String gstin = "07" + pan + "1Z1";
        body.put("gstin", gstin);
        body.put("PAN", pan);
        body.put("BUSINESS_ENTITY", ENTITY_TYPE);
        body.put("KYB_BUSINESS_ID", "");

        String requestPath = "MerchantService/v1/sdMerchant/lead/updatePanLeadRequest.json";
        Response response = bossToOeLeadUpdate.bossToOeLeadUpdate(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
    }


    public Object[][] documentDataAlternative() {
        return new Object[][]{
                {"Aadhaar", "AadhaarDocument"},
                // {"PAN", "PanDocument"},
                // {"BusinessProof", "BusinessProofDocument"},
                {"ShopFront", "ShopFrontPhoto"},
                {"ShopInside", "ShopInsidePhoto"},
                {"QrSticker", "QrStickerPhoto"}
        };
    }

    public void uploadDocument(String docType, String docProvided, String leadId, String newToken) throws Exception {
        String endPoint = P.API.get("UploadDoc");
        headers.put("Content-Type", "multipart/form-data");
        headers.put("session_token", newToken);

        File uploadFile = new File(DOC_PATH);

        Map<String, String> params = new HashMap<>();
        params.put("solutionType", SOLUTION);
        params.put("channel", CHANNEL);
        params.put("entityType", ENTITY_TYPE);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        params.put("docType", docType);
        params.put("docProvided", docProvided);

        response = uploadDocumentToAPI(uploadFile, params, endPoint);
        Assert.assertEquals(response.getStatusCode(), 200);
    }

    public void callDataProviderAndUploadDocument(String leadId, String newToken) throws Exception {
        // Instantiate the class and access the data provider method
        Object[][] data = documentDataAlternative(); // Directly call the data provider method

        // Loop through the data and call uploadDocument for each document
        for (Object[] row : data) {
            String docType = (String) row[0];
            String docProvided = (String) row[1];
            uploadDocument(docType, docProvided, leadId, newToken);
        }


    }

    public void SubmitLead(String leadId, String newToken) {

        headers.put("Content-Type", "application/json");
        headers.put("channel", "DIY_PAYTM_APP");
        headers.put("session_token", newToken);

        Map<String, String> params = getCommonParams();
        params.put("leadId", leadId);

        Map<String, Object> body = new HashMap<>();
        body.put("partialSave", false);
        body.put("IS_AGREEMENT_ACCEPTED", true);

        requestPath = "MerchantService/V1/sdMerchant/lead/updateLead/LimitUpgradeSubmitLeadRequest.json";
        response = bossToOeLeadUpdate.bossToOeLeadUpdate(requestPath, headers, params, body);

    }

    @Test(priority = 7, description = "", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void BusinessNameUpdateLeadFetchDocStatus() throws Exception {

        custId = GetResourceOwnerId(MOBILE_NUMBER, "paytm@123");

        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();

        Map<String, String> headers = new HashMap<>();
        headers.put("session_token", token); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "multipart/json");
        headers.put("version", "7.2.8"); // No change
        headers.put("deviceidentifier", "Xiaomi-RedmiNote9Pro-efc28fc97cede738"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solution", "diy_mco");
        queryParams.put("entityType", "PROPRIETORSHIP");
        queryParams.put("leadId", leadId);
        queryParams.put("merchantCustId", custId);
        queryParams.put("channel", "DIY_P4B_APP");
        queryParams.put("solutionTypeLevel2", "registered_address_update");

        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj, headers, queryParams);

        shopInsidePhotoDMSID = respObj.path("uploadedDocDetailsSet[0].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
        AadharPhotoDMSID2 = respObj.path("uploadedDocDetailsSet[4].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['2']");
        AadharPhotoDMSID1 = respObj.path("uploadedDocDetailsSet[4].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");   //   BankStatementDMSID = respObj.path("uploadedDocDetailsSet[3].uploadedDocs[0].uuid");
        BusinessOwnerPhotoDMSID = respObj.path("uploadedDocDetailsSet[3].uploadedDocs[0].uuid");
        shopFrontPhotoDMSID = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
        PanphotoDMSID = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].uuid");
        qrStickerPhotoDMSID = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");

        System.out.println(BusinessOwnerPhotoDMSID + AadharPhotoDMSID1 + AadharPhotoDMSID2 + PanphotoDMSID + BankStatementDMSID + shopFrontPhotoDMSID + shopInsidePhotoDMSID + qrStickerPhotoDMSID);

    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Business name update QC", priority = 8, dependsOnMethods = "BusinessNameUpdateLeadFetchDocStatus")
    public void BusinessNameUpdateQC() throws Exception {

        DBConnection dbConnectionObj = new DBConnection();
        int Ubmid = dbConnectionObj.getUserBusinessMappingId(MOBILE_NUMBER, "diy_mco", SOLUTION_TYPE_LEVEL_2);
        dbConnectionObj.assignAgentViaDB("1152", Ubmid);
        Long billingAddressUpdateWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);
        wfsidBillingAddressUpdate = String.valueOf(billingAddressUpdateWorkflowStatusId);

        requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadBossToOeProp.json";
        EditLead EditLeadObj = new EditLead(leadId, requestPath);
        EditLeadObj.getProperties().setProperty("uuidAadharPhoto1", AadharPhotoDMSID1);
        EditLeadObj.getProperties().setProperty("uuidAadharPhoto2", AadharPhotoDMSID2);
        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto", BusinessOwnerPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto1", BusinessOwnerPhotoDMSID);
        //   EditLeadObj.getProperties().setProperty("uuidPanPhoto", PanphotoDMSID);
        //      EditLeadObj.getProperties().setProperty("uuidBankStatementPhoto", BankStatementDMSID);
        EditLeadObj.getProperties().setProperty("uuidShopFrontPhoto", shopFrontPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidShopFrontPhoto1", shopFrontPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidShopInsidePhoto1", shopInsidePhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidShopInsidePhoto", shopInsidePhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidQrStickerPhoto1", qrStickerPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidQrStickerPhoto", qrStickerPhotoDMSID);
        //   EditLeadObj.getProperties().setProperty("uuidBusinessProof", businessProofDMSID);
        EditLeadObj.getProperties().setProperty("workflowStatusId", wfsidBillingAddressUpdate);
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("action", "SUBMIT");
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("Cookie", XMWCookie);
        Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);

    }

    public Response FetchScreenDetails(String leadId) throws JSONException {
        Map<String, String> headers = new HashMap<>();

        headers.put("Content-Type", "application/json");
        headers.put("session_token", token);

        Map<String, String> params = new HashMap<>();
        //  params.put("solution", "diy_mco");
        params.put("leadId", leadId);
        params.put("fetchStrategy", "SCREEN_DETAILS");
        params.put("entityType", "INDIVIDUAL");


        requestPath = "MerchantService/V1/sdMerchant/lead/fetchScreenDetailsUpgradeLimitRequest.json";
        response = fetchScreenDetailsUpgradeLimit.FetchScreenDetailsUpgradeLimit(requestPath, headers, params);

        int httpcode = response.getStatusCode();
        Assert.assertEquals(httpcode, 200);
        Response responseBody = response;
        return responseBody;
    }


    public boolean checkNextScreenDetails(Response checkNextScreenResponse, String screenName) {
        // Convert the response body to a JSONObject
        JsonPath jsonPath = checkNextScreenResponse.jsonPath();

        // Check if the "nextScreenDetails" array contains the value "AADHAAR_DIGILOCKER" for "eligibleScreen"
        boolean isScreenPresent = jsonPath.getList("nextScreenDetails.eligibleScreen")
                .stream()
                .anyMatch(screen -> screenName.equals(screen));

        return isScreenPresent;

    }

}
