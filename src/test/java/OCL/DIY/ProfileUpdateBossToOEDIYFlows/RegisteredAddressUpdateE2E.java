package OCL.DIY.ProfileUpdateBossToOEDIYFlows;

import OCL.DIY.MCO.CreateLead;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

public class RegisteredAddressUpdateE2E extends BaseMethod{
    Utilities Util = new Utilities();
    String num2 = Util.randomMobileNumberGenerator();
    String num = num2.replaceFirst("5", "9");
    String mobileNumber;
    String newToken;
    String leadId;
    String PAN;
    String custId;
    String mid;
    String wfsid;
    String wfsidBillingAddressUpdate;
    String requestPath = "";
    public String DocumetRequestDeserialised = "";
    String BusinessOwnerPhotoDMSID = "";
    String AadharPhotoDMSID1 = "";
    String AadharPhotoDMSID2 = "";
    String PanphotoDMSID = "";
    String BankStatementDMSID = "";
    String shopFrontPhotoDMSID = "";
    String shopInsidePhotoDMSID = "";
    String qrStickerPhotoDMSID = "";
    String businessProofDMSID = "";
    public static String ApplicantToken = "";
    CreateLead createLead = new CreateLead();
    BillingAddressUpdate billingAddressUpdate = new BillingAddressUpdate();
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    @BeforeClass
    public void BeforeOperation() {
        if (Util.createNewAuthUser(num, "Paytm@123")) {
            newToken = BaseMethod.ApplicantToken(num, "Paytm@123");
            mobileNumber = num;
        } else {
            Assert.assertFalse(false, "Number was not registered at Oauth end");
        }
    }

    @Test(priority = 1, description = "To create DIY MCO Retail lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createLead() throws Exception {
        leadId = createLead.createLead(newToken);
        createLead.GetStarted(leadId, newToken);
        createLead.AadhaarOcr(leadId, newToken);
        createLead.UpdateLeadAadhaar(leadId, newToken);
        createLead.Upload_AadhaarDoc(leadId, newToken);
        createLead.Upload_MerchantSelfie(leadId, newToken);
        PAN = createLead.UpdateLeadPan(leadId, newToken);
        createLead.Upload_panDocument(leadId, newToken);
        createLead.UpdateBusinessCat(leadId, newToken);
        createLead.UpdatePennyDrop(leadId, newToken);
        createLead.SubmitBankLead(leadId, newToken);
        createLead.UpdateShopAddress(leadId, newToken);
        createLead.SubmitTheLead(leadId, newToken);

        custId = GetResourceOwnerId(mobileNumber, "Paytm@123");

    }



}
