package OCL.DIY.ProfileUpdateBossToOEDIYFlows;

import OCL.DIY.MCO.CreateLead;
import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.Revisit.LoanIntentfetchDocumentDetails;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import java.util.HashMap;
import java.util.Map;


public class BillingAddressUpdateE2E extends BaseMethod {

    Utilities Util = new Utilities();
    String num2 = Util.randomMobileNumberGenerator();
    String num = num2.replaceFirst("5", "9");
    String mobileNumber;
    String newToken;
    String leadId;
    String PAN;
    String custId;
    String mid;
    String wfsid;
    String wfsidBillingAddressUpdate;
    String requestPath = "";
    String BusinessOwnerPhotoDMSID = "";
    String AadharPhotoDMSID1 = "";
    String AadharPhotoDMSID2 = "";
    String PanphotoDMSID = "";
    String BankStatementDMSID = "";
    String shopFrontPhotoDMSID = "";
    String shopInsidePhotoDMSID = "";
    String qrStickerPhotoDMSID = "";
    String businessProofDMSID = "";
    public static String ApplicantToken = "";
    CreateLead createLead = new CreateLead();
    BillingAddressUpdate billingAddressUpdate = new BillingAddressUpdate();
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    @BeforeClass
    public void BeforeOperation() {
        if (Util.createNewAuthUser(num, "Paytm@123")) {
            newToken = BaseMethod.ApplicantToken(num, "Paytm@123");
            mobileNumber = num;
        } else {
            Assert.assertFalse(false, "Number was not registered at Oauth end");
        }
    }

    @Test(priority = 1, description = "To create DIY MCO Retail lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createLead() throws Exception {
        leadId = createLead.createLead(newToken);
        createLead.GetStarted(leadId, newToken);
        createLead.AadhaarOcr(leadId, newToken);
        createLead.UpdateLeadAadhaar(leadId, newToken);
        createLead.Upload_AadhaarDoc(leadId, newToken);
        createLead.Upload_MerchantSelfie(leadId, newToken);
        PAN = createLead.UpdateLeadPan(leadId, newToken);
        createLead.Upload_panDocument(leadId, newToken);
        createLead.UpdateBusinessCat(leadId, newToken);
        createLead.UpdatePennyDrop(leadId, newToken);
        createLead.SubmitBankLead(leadId, newToken);
        createLead.UpdateShopAddress(leadId, newToken);
        createLead.SubmitTheLead(leadId, newToken);

        custId = GetResourceOwnerId(mobileNumber, "Paytm@123");

    }

    @Test(priority = 2, description = "", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void DiyMcoLeadFetchDocStatus() throws Exception {

        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();
        Map<String, String> headers = new HashMap<>();

        headers.put("session_token", newToken); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("version", "7.2.8"); // No change
        headers.put("deviceidentifier", "Xiaomi-RedmiNote9Pro-efc28fc97cede738"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solution", "diy_mco");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);
        queryParams.put("merchantCustId", custId);
        queryParams.put("channel", "DIY_P4B_APP");

        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj, headers, queryParams);

        Assert.assertEquals(respObj.getStatusCode(), 200);
        BusinessOwnerPhotoDMSID = respObj.path("uploadedDocDetailsSet[0].uploadedDocs[0].uuid");
        AadharPhotoDMSID1 = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
        AadharPhotoDMSID2 = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['2']");
        PanphotoDMSID = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].uuid");
        System.out.println(BusinessOwnerPhotoDMSID + AadharPhotoDMSID1 + AadharPhotoDMSID2 + PanphotoDMSID);

    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "DIY MCO Retail Onboarding QC", priority = 3, dependsOnMethods = "DiyMcoLeadFetchDocStatus")
    public void DiyMcoRetailOnboardingLeadQC() throws Exception {

        DBConnection dbConnectionObj = new DBConnection();
        int Ubmid = dbConnectionObj.getUserBusinessMappingId(mobileNumber, "diy_mco");
        dbConnectionObj.assignAgentViaDB("1152", Ubmid);
        Long DiyMCOIndividualWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);

        //Save value of DiyMCOIndividualWorkflowStatusId in string
        wfsid = String.valueOf(DiyMCOIndividualWorkflowStatusId);

        requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadMcoIndividualWithPan.json";
        EditLead EditLeadObj = new EditLead(leadId, requestPath);
        EditLeadObj.getProperties().setProperty("uuidAadharPhoto1", AadharPhotoDMSID1);
        EditLeadObj.getProperties().setProperty("uuidAadharPhoto2", AadharPhotoDMSID2);
        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto", BusinessOwnerPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto1", BusinessOwnerPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidPanPhoto", PanphotoDMSID);
        EditLeadObj.getProperties().setProperty("workflowStatusId", wfsid);
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("action", "SUBMIT");
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("Cookie", XMWCookie);
        Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);

        Thread.sleep(120000); // Wait for 3 minutes
    }


    @Test(priority = 4, description = "add channel callback", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addChannelCall() throws Exception {

        Thread.sleep(80000); // Wait for 2 minutes
        Response addchannel = middlewareServicesObject.addChannelCallback(custId, leadId);

    }


    @Test(priority = 5, description = "To create Billing Address lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createLeadBillingAddressUpdate() throws Exception {
        Thread.sleep(200000); // Wait for QR_MAPPING_SUCCESS
        mid = FetchMID(custId);
        this.newToken = BaseMethod.ApplicantToken(mobileNumber, "Paytm@123");
        this.leadId = billingAddressUpdate.createLead(mid, newToken);
        createLead.UpdateLeadAadhaar(leadId, newToken);
        createLead.Upload_AadhaarDoc(leadId, newToken);
        createLead.Upload_MerchantSelfie(leadId, newToken);
        billingAddressUpdate.enterNonIndividualPanManually(PAN, leadId, newToken);
        billingAddressUpdate.callDataProviderAndUploadDocument(leadId, newToken);
        billingAddressUpdate.SubmitLead(leadId, newToken);

    }


    @Test(priority = 6, description = "", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void BillingAddressUpdateLeadFetchDocStatus() throws Exception {
        
        this.newToken = BaseMethod.ApplicantToken(mobileNumber, "Paytm@123");
        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();

        Map<String, String> headers = new HashMap<>();
        headers.put("session_token", newToken); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "multipart/json");
        headers.put("version", "7.2.8"); // No change
        headers.put("deviceidentifier", "Xiaomi-RedmiNote9Pro-efc28fc97cede738"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solution", "diy_mco");
        queryParams.put("entityType", "PROPRIETORSHIP");
        queryParams.put("leadId", leadId);
        queryParams.put("merchantCustId", custId);
        queryParams.put("channel", "DIY_P4B_APP");
        queryParams.put("solutionTypeLevel2", "billing_address_update");

        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj, headers, queryParams);

        shopInsidePhotoDMSID = respObj.path("uploadedDocDetailsSet[0].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
        AadharPhotoDMSID2 = respObj.path("uploadedDocDetailsSet[4].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['2']");
        AadharPhotoDMSID1 = respObj.path("uploadedDocDetailsSet[4].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");   //   BankStatementDMSID = respObj.path("uploadedDocDetailsSet[3].uploadedDocs[0].uuid");
        BusinessOwnerPhotoDMSID = respObj.path("uploadedDocDetailsSet[3].uploadedDocs[0].uuid");
        shopFrontPhotoDMSID = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
        PanphotoDMSID = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].uuid");
        qrStickerPhotoDMSID = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");

        System.out.println(BusinessOwnerPhotoDMSID + AadharPhotoDMSID1 + AadharPhotoDMSID2 + PanphotoDMSID + BankStatementDMSID + shopFrontPhotoDMSID + shopInsidePhotoDMSID + qrStickerPhotoDMSID);

    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "DIY MCO Retail Onboarding QC", priority = 7, dependsOnMethods = "BillingAddressUpdateLeadFetchDocStatus")
    public void BillingAddressUpdateQC() throws Exception {


        DBConnection dbConnectionObj = new DBConnection();
        int Ubmid = dbConnectionObj.getUserBusinessMappingId(mobileNumber, "diy_mco", "billing_address_update");
        dbConnectionObj.assignAgentViaDB("1152", Ubmid);
        Long billingAddressUpdateWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);
        //Save value of billingAddressUpdateWorkflowStatusId in string
        wfsidBillingAddressUpdate = String.valueOf(billingAddressUpdateWorkflowStatusId);

        requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadBossToOeProp.json";
        EditLead EditLeadObj = new EditLead(leadId, requestPath);
        EditLeadObj.getProperties().setProperty("uuidAadharPhoto1", AadharPhotoDMSID1);
        EditLeadObj.getProperties().setProperty("uuidAadharPhoto2", AadharPhotoDMSID2);
        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto", BusinessOwnerPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto1", BusinessOwnerPhotoDMSID);
        //   EditLeadObj.getProperties().setProperty("uuidPanPhoto", PanphotoDMSID);
        //      EditLeadObj.getProperties().setProperty("uuidBankStatementPhoto", BankStatementDMSID);
        EditLeadObj.getProperties().setProperty("uuidShopFrontPhoto", shopFrontPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidShopFrontPhoto1", shopFrontPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidShopInsidePhoto1", shopInsidePhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidShopInsidePhoto", shopInsidePhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidQrStickerPhoto1", qrStickerPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidQrStickerPhoto", qrStickerPhotoDMSID);
        //   EditLeadObj.getProperties().setProperty("uuidBusinessProof", businessProofDMSID);
        EditLeadObj.getProperties().setProperty("workflowStatusId", wfsidBillingAddressUpdate);
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("action", "SUBMIT");
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("Cookie", XMWCookie);
        Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);

    }

}
