package OCL.DIY.ProfileUpdateBossToOEDIYFlows;

import OCL.CommonOnboarding.FlowCommonOnboardingIndividualWithForm60;
import OCL.DIY.MCO.CreateLead;
import OCL.DIY.MerchantLimitUpgrade.DiyUpgrade;
import Request.MerchantService.v1.sdMerchant.Lead_fetch;
import Request.MerchantService.v3.merchant.mid.Mobile;
import Services.DIYProfileUpdate.BossToOeLeadUpdate;
import Services.DIYProfileUpdate.GSTUpdate;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class PanUpdate extends BaseMethod {


    private FlowCommonOnboardingIndividualWithForm60 flowCommonOnboardingIndividualWithForm60;


    // Constants for configuration
    private static final String CHANNEL = "DIY_P4B_APP";
    private static final String ENTITY_TYPE = "INDIVIDUAL";
    private static final String SOLUTION_TYPE_LEVEL_2 = "pan_addition";
    private static final String SOLUTION = "diy_mco";
    Utilities Util = new Utilities();

    String PAN = Util.randomIndividualPANValueGenerator();

    private static final String DOC_PATH = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";

    private String token;
    private String requestPath;
    private Response response;
    private String leadId;
    private String custId;
    private String mobile;
    private String mid;
    private Map<String, String> headers = new HashMap<>();
    private GSTUpdate gstUpdate = new GSTUpdate();
    private BossToOeLeadUpdate bossToOeLeadUpdate = new BossToOeLeadUpdate();
    DisplayNameUpdate displayNameUpdate = new DisplayNameUpdate();
    DiyUpgrade diyUpgrade = new DiyUpgrade();
    CreateLead createLead = new CreateLead();
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    @BeforeClass
    public void setup() {
        // Set common headers for all requests
        token = ApplicantToken(mobile, "Paytm@123");
        XMWCookie = findXMWTokenforPanel("7771216290", "paytm@123");

        headers.put("session_token", token);
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");
    }

    private Map<String, String> getCommonParams() {
        Map<String, String> params = new HashMap<>();
        params.put("solution", SOLUTION);
        params.put("channel", CHANNEL);
        params.put("entityType", ENTITY_TYPE);
        params.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);

        return params;
    }


    @Test(priority = 1, description = "To check that create limit upgrade lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void executeForm60Lead() throws Exception {

        flowCommonOnboardingIndividualWithForm60 = new FlowCommonOnboardingIndividualWithForm60();
        //return mobile in BeforeSuiteLogin
        flowCommonOnboardingIndividualWithForm60.BeforeSuiteLogin();
        flowCommonOnboardingIndividualWithForm60.AgentLogin();
        flowCommonOnboardingIndividualWithForm60.TC01_Create_Lead();
//        custId = GetResourceOwnerId(mobileNumber, "Paytm@123");
        flowCommonOnboardingIndividualWithForm60.TC02_SelectProductContext();
        flowCommonOnboardingIndividualWithForm60.TC03_SendOtp();
        flowCommonOnboardingIndividualWithForm60.TC04_ValidateOTP();
        flowCommonOnboardingIndividualWithForm60.TC05_AddBankDetail();
        flowCommonOnboardingIndividualWithForm60.TC06_ConfirmBerau();
        flowCommonOnboardingIndividualWithForm60.TC07_AADHAR_OCR_INITIATED();
        flowCommonOnboardingIndividualWithForm60.TC08_SubmitAadharOCRDetails();
        flowCommonOnboardingIndividualWithForm60.TC09_skipPanDetail();
        flowCommonOnboardingIndividualWithForm60.TC09_SubmitForm60Detail();
        flowCommonOnboardingIndividualWithForm60.TC11_uploadBankProofDocument();
        flowCommonOnboardingIndividualWithForm60.TC12_UploadBusinessOwnerPhoto();
        flowCommonOnboardingIndividualWithForm60.TC13_MERCHANT_PHOTO_SKIPPED_AFTER_EXHAUSTING_ATTEMPTS();
        flowCommonOnboardingIndividualWithForm60.TC14_qna_submit();
        flowCommonOnboardingIndividualWithForm60.TC15_SegmentSubSegmentSubmit();
        flowCommonOnboardingIndividualWithForm60.TC16_UAC_Submit();
        flowCommonOnboardingIndividualWithForm60.TC17_FetchLeadDetail();
        flowCommonOnboardingIndividualWithForm60.TC18_RBDScreen_Submit();
        flowCommonOnboardingIndividualWithForm60.TC19_SendOtpforAgreement();
        flowCommonOnboardingIndividualWithForm60.TC20_ValidateOtpforAgreement();
        flowCommonOnboardingIndividualWithForm60.TC21_UploadShopPhoto();
        flowCommonOnboardingIndividualWithForm60.TC22_UploadAadharPhoto2();
        flowCommonOnboardingIndividualWithForm60.TC22_UploadAadharPhoto1();
        flowCommonOnboardingIndividualWithForm60.TC24_McoLeadSubmitAfterUploadingAllDocs();
        flowCommonOnboardingIndividualWithForm60.TC25_McoLeadFetchDocStatus();
        flowCommonOnboardingIndividualWithForm60.TC26_McoIndividualLeadQC();

    }

    @Test(priority = 2, description = "To check that create limit upgrade lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createLead() throws JSONException, InterruptedException {
        Thread.sleep(200000); // Wait for mid to be created

        setup();
        mid = FetchMID(custId);

        Map<String, String> params = getCommonParams();
        Map<String, String> body = new HashMap<>();

        body.put("mid", mid);
        body.put("closeOpenLead", "true");

        requestPath = "MerchantService/v1/sdMerchant/lead/CreateLeadMerchantLimitUpgradeRequest.json";
        response = gstUpdate.GSTUpdate(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
        leadId = response.jsonPath().getString("leadId");
    }


    @Test(priority = 3, description = "Enter non-individual PAN manually", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void aadhaarUpdateAndMerchantSelfie() throws Exception {
        Response responseBody = displayNameUpdate.FetchScreenDetails(leadId, token, ENTITY_TYPE, SOLUTION_TYPE_LEVEL_2);
        boolean AadhaarDigilockerRequired = diyUpgrade.checkNextScreenDetails(responseBody, "AADHAAR_DIGILOCKER");

        if (AadhaarDigilockerRequired) {
            createLead.UpdateLeadAadhaar(leadId, token);
            createLead.Upload_AadhaarDoc(leadId, token);
            createLead.Upload_MerchantSelfie(leadId, token);
        }

    }

    @Test(priority = 4, description = "Enter non-individual PAN manually", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UpdateBank() throws Exception {
        Response responseBody = displayNameUpdate.FetchScreenDetails(leadId, token, ENTITY_TYPE, SOLUTION_TYPE_LEVEL_2);
        boolean BankRequired = diyUpgrade.checkNextScreenDetails(responseBody, "BANK_DETAILS");
        if (BankRequired) {
            createLead.SubmitBankLead(leadId, token);
            Response response = displayNameUpdate.FetchDocStatus(leadId, token, ENTITY_TYPE, SOLUTION_TYPE_LEVEL_2, SOLUTION);
            boolean isDocRequired = displayNameUpdate.checkDocStatus(response, "BANK_DETAILS");
            if (isDocRequired) {
                String endPoint = P.API.get("UploadBankProof");

                Map<String, String> params = new HashMap<String, String>();
                params.put("docType", "bankProof");
                params.put("docProvided", "BankStatement");
                params.put("entityType", ENTITY_TYPE);
                params.put("solutionType", "diy_mco");
                params.put("channel", "DIY_P4B_APP");
                params.put("solutionLeadId", leadId);
                params.put("pageNo", "1");

                Map<String, String> header = new HashMap<String, String>();
                header.put("Content-Type", "multipart/form-data");
                header.put("session_token", token);


                File uploadFile = new File(DOC_PATH);

                Response resp = createLead.UploadDocInAPI(uploadFile, params, header, endPoint);
            }
        }

    }

    @Test(priority = 5, description = "Enter non-individual PAN manually", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void enterIndividualPanManually() throws JSONException {
        Map<String, String> params = getCommonParams();
        params.put("leadId", leadId);

        Map<String, Object> body = new HashMap<>();
        body.put("partialSave", "true");
        body.put("pan", PAN);
        body.put("gstin", "");
        body.put("PAN", PAN);
        body.put("BUSINESS_ENTITY", ENTITY_TYPE);
        body.put("KYB_BUSINESS_ID", "");

        requestPath = "MerchantService/v1/sdMerchant/lead/updatePanLeadRequest.json";
        response = bossToOeLeadUpdate.bossToOeLeadUpdate(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
    }


    @Test(priority = 6, description = "Enter non-individual PAN manually", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UploadPanDocument() throws Exception {

        Response response = displayNameUpdate.FetchDocStatus(leadId, token, ENTITY_TYPE, SOLUTION_TYPE_LEVEL_2, SOLUTION);
        boolean isDocRequired = displayNameUpdate.checkDocStatus(response, "pan");
        if (isDocRequired) {
            String endPoint = P.API.get("UploadBankProof");

            Map<String, String> params = new HashMap<String, String>();
            params.put("docType", "pan");
            params.put("docProvided", "pan");
            params.put("entityType", ENTITY_TYPE);
            params.put("solutionType", "diy_mco");
            params.put("channel", "DIY_P4B_APP");
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "1");

            Map<String, String> header = new HashMap<String, String>();
            header.put("Content-Type", "multipart/form-data");
            header.put("session_token", token);

            File uploadFile = new File(DOC_PATH);

            Response resp = createLead.UploadDocInAPI(uploadFile, params, header, endPoint);
        }


    }

    @Test(priority = 7, description = "Submit lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void SubmitLead() throws JSONException {
        headers.put("Content-Type", "application/json");
        Map<String, String> params = getCommonParams();
        params.put("leadId", leadId);

        Map<String, Object> body = new HashMap<>();
        body.put("partialSave", "false");
        body.put("IS_AGREEMENT_ACCEPTED", "true");

        requestPath = "MerchantService/V1/sdMerchant/lead/updateLead/LimitUpgradeSubmitLeadRequest.json";
        response = bossToOeLeadUpdate.bossToOeLeadUpdate(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
    }

    @Test(priority = 8, description = "To check that create limit upgrade lead", groups = {"Regression"})
    @Owner(emailId = "", isAutomated = true)
    public void checkLeadStatus() throws JSONException {
        Lead_fetch fetchLead = new Lead_fetch();

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", ENTITY_TYPE);
        queryParams.put("channel", "DIY_P4B_APP");
        queryParams.put("leadId", leadId);
        queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("fetchLeadStatusOnly", "true");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", token);
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("client", "androidapp");
        headers.put("latitude", "28.9342");
        headers.put("longitude", "72.48543");
        headers.put("androidId", "AashitAndroidId");

        Response responseObject = middlewareServicesObject.FetchLead(fetchLead, queryParams, headers);

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(200, statusCode);
    }

    @Test(priority = 9, description = "To check that create limit upgrade lead", groups = {"Regression"})
    @Owner(emailId = "", isAutomated = true)
    public void checkLeadDetails() throws JSONException {
        Lead_fetch fetchLead = new Lead_fetch();

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", ENTITY_TYPE);
        queryParams.put("channel", "DIY_P4B_APP");
        queryParams.put("leadId", leadId);
        queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", token);
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("client", "androidapp");
        headers.put("latitude", "28.9342");
        headers.put("longitude", "72.48543");
        headers.put("androidId", "AashitAndroidId");

        Response responseObject = middlewareServicesObject.FetchLead(fetchLead, queryParams, headers);

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(200, statusCode);
    }
}
