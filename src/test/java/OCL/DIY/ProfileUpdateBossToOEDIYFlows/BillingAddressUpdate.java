package OCL.DIY.ProfileUpdateBossToOEDIYFlows;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.Revisit.LoanIntentfetchDocumentDetails;
import Services.DBConnection.DBConnection;
import Services.DIYMCO.getStarted;
import Services.DIYMerchantUpgradeLimit.FetchScreenDetailsUpgradeLimit;
import Services.DIYProfileUpdate.BossToOeLeadUpdate;
import Services.DIYProfileUpdate.GSTUpdate;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;

import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class BillingAddressUpdate extends BaseMethod {


    // Constants for configuration
    private static final String MOBILE_NUMBER = "**********";
    private static final String CHANNEL = "DIY_P4B_APP";
    private static final String ENTITY_TYPE = "PROPRIETORSHIP";
    private static final String SOLUTION_TYPE_LEVEL_2 = "billing_address_update";
    private static final String SOLUTION = "diy_mco";
    private static final String MID = "vUoknj22987249712309";
    private static final String PAN = "**********";
    private static final String GSTIN = "07" + PAN + "1Z1";
    String wfsidBillingAddressUpdate;
    String custId;
    String BusinessOwnerPhotoDMSID = "";
    String AadharPhotoDMSID1 = "";
    String AadharPhotoDMSID2 = "";
    String PanphotoDMSID = "";
    String BankStatementDMSID = "";
    String shopFrontPhotoDMSID = "";
    String shopInsidePhotoDMSID = "";
    String qrStickerPhotoDMSID = "";
    Utilities util = new Utilities();
    String Aadhaar = util.ValidAadhaar();
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final String DOC_PATH = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";

    private String token;
    private String requestPath;
    private Response response;
    private String leadId;
    private Map<String, String> headers = new HashMap<>();
    private GSTUpdate gstUpdate = new GSTUpdate();
    private BossToOeLeadUpdate bossToOeLeadUpdate = new BossToOeLeadUpdate();
    getStarted gs = new getStarted();
    FetchScreenDetailsUpgradeLimit fetchScreenDetailsUpgradeLimit = new FetchScreenDetailsUpgradeLimit();


    @BeforeClass
    public void setup() throws Exception {
        // Generate session token and setup headers
        token = ApplicantToken(MOBILE_NUMBER, "paytm@123");
        XMWCookie = findXMWTokenforPanel("7771216290", "paytm@123");

        DBConnection dbConnection = new DBConnection();
        dbConnection.UpdateQueryToCloseLeadsolnlevel2(MOBILE_NUMBER, "diy_mco", "billing_address_update");


        // Set common headers for all requests
        headers.put("session_token", token);
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");
    }

    private Map<String, String> getCommonParams() {
        Map<String, String> params = new HashMap<>();
        params.put("solution", SOLUTION);
        params.put("channel", CHANNEL);
        params.put("entityType", ENTITY_TYPE);
        params.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);

        return params;
    }

    @Test(priority = 1, description = "To check that create limit upgrade lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createLead() throws JSONException {
        Map<String, String> params = getCommonParams();
        Map<String, String> body = new HashMap<>();

        body.put("mid", MID);
        body.put("closeOpenLead", "true");

        requestPath = "MerchantService/v1/sdMerchant/lead/CreateLeadMerchantLimitUpgradeRequest.json";
        response = gstUpdate.GSTUpdate(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);

        if (response.getStatusCode() == 200) {
            leadId = response.jsonPath().getString("leadId");

        } else //recall the method if lead is not created
        {
            createLead();
        }
    }

    @Test(priority = 2, description = "To check that create limit upgrade lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addAadhaar() throws JSONException {

        Response responseBody = super.FetchScreenDetails(leadId, token, ENTITY_TYPE, SOLUTION_TYPE_LEVEL_2, "SCREEN_DETAILS");
        boolean AadhaarDigilockerRequired = super.checkNextScreenDetails(responseBody, "AADHAAR_DIGILOCKER");

        if (AadhaarDigilockerRequired) {
            Map<String, String> params = new HashMap<String, String>();
            params.put("solution", "diy_mco");
            params.put("entityType", ENTITY_TYPE);
            params.put("channel", "DIY_P4B_APP");
            params.put("leadId", leadId);

            Map<String, String> header = new HashMap<String, String>();
            header.put("Content-Type", "application/json");
            header.put("session_token", token);
            header.put("androidId", "AashitAndroid");
            header.put("browserName", "chrome");
            header.put("browserVersion", "4.6.3");
            header.put("cache-control", "no-cache");
            header.put("ipAddress", "************");
            header.put("latitude", "28.32");
            header.put("longitude", "77.213");
            header.put("imei", "1234");
            header.put("channel", "DIY_P4B_APP");

            Map<String, String> body = new HashMap<String, String>();
            body.put("aadharNumber", Aadhaar);
            body.put("partialSave", "true");
            body.put("AADHAR_OCR_DONE", "true");
            body.put("AADHAR_NO_MISMATCHED", "false");
            body.put("AADHAR_NO_NOT_READABLE", "false");
            body.put("GENDER", "Female");
            body.put("MERCHANT_DOB", "12/27/1988");
            body.put("NAME_AS_PER_AADHAR", "TOUCH_WOOD_LIMITED");
            body.put("userAddressCity", "Jind");
            body.put("userAddressLandMark", "ANMOL JAIN");
            body.put("userAddressLine1", "9187");
            body.put("userAddressLine2", "Ahxhshj");
            body.put("userAddressLine3", "Sjjsiskso");
            body.put("userAddressPincode", "126102");
            body.put("userAddressState", "Haryana");


            requestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

            response = gs.getStartedResponse(requestPath, header, params, body);

            int statusCode = response.getStatusCode();
            Assert.assertEquals(statusCode, 200);
        }
    }

    @Test(priority = 3, description = "To check that create limit upgrade lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void uploadAadhaarPhoto() throws JSONException, Exception {

        Response responseBody = super.FetchScreenDetails(leadId, token, ENTITY_TYPE, SOLUTION_TYPE_LEVEL_2, "SCREEN_DETAILS");
        boolean AadhaarDigilockerRequired = super.checkNextScreenDetails(responseBody, "AADHAAR_DIGILOCKER");

        if (AadhaarDigilockerRequired) {
            String endPoint = P.API.get("UploadAadhaarDoc");
            headers.put("Content-Type", "multipart/form-data");

            Map<String, String> params = new HashMap<String, String>();
            params.put("docType", "poi");
            params.put("docProvided", "aadhaar");
            params.put("entityType", ENTITY_TYPE);
            params.put("solutionType", "diy_mco");
            params.put("channel", "DIY_P4B_APP");
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "1");


            File uploadFile = new File(DOC_PATH);

            Response resp = uploadDocumentToAPI(uploadFile, params, endPoint);

            int statusCode = resp.getStatusCode();
            Assert.assertEquals(statusCode, 200);

            //uploading page 2 of aadhar
            params.put("pageNo", "2");
            Response resp2 = uploadDocumentToAPI(uploadFile, params, endPoint);
            int statusCode2 = resp2.getStatusCode();
            Assert.assertEquals(statusCode2, 200);
        }
    }

    @Test(priority = 4, description = "Enter non-individual PAN manually", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void enterNonIndividualPanManually() throws JSONException {
        Map<String, String> params = getCommonParams();
        params.put("leadId", leadId);

        Map<String, Object> body = new HashMap<>();
        body.put("partialSave", "true");
        body.put("pan", PAN);
        body.put("gstin", GSTIN);
        body.put("PAN", PAN);
        body.put("BUSINESS_ENTITY", ENTITY_TYPE);
        body.put("KYB_BUSINESS_ID", "");

        requestPath = "MerchantService/v1/sdMerchant/lead/updatePanLeadRequest.json";
        response = bossToOeLeadUpdate.bossToOeLeadUpdate(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
    }

    @Test(priority = 5, description = "Upload business proof document", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void uploadBusinessProof() throws Exception {
        // Check screen details first
        Response screenDetailsResponse = super.FetchScreenDetails(leadId, token, ENTITY_TYPE, SOLUTION_TYPE_LEVEL_2, "SCREEN_DETAILS");
        Assert.assertNotNull(screenDetailsResponse, "Screen details response should not be null");
        Assert.assertEquals(screenDetailsResponse.getStatusCode(), 200, "Failed to fetch screen details");
        
        // Verify PAN document upload screen
        boolean panDocumentRequired = super.checkNextScreenDetails(screenDetailsResponse, "SCAN_PAN");
        Assert.assertTrue(panDocumentRequired, "PAN document upload screen not found");
        
        // Only proceed with upload if PAN document is required
        if (panDocumentRequired) {
            uploadDocument("businessProof", "GSTCertificate");
        } else {
            System.out.println("Skipping business proof upload as PAN document upload is not required");
        }
    }

    // Document upload with dynamic data provider
    @Test(priority = 6, description = "Upload Document", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @DataProvider(name = "documentData")
    public Object[][] documentData() {

        if (ENTITY_TYPE.equalsIgnoreCase("PRIVATE_LIMITED") || ENTITY_TYPE.equalsIgnoreCase("PUBLIC_LIMITED")) {
            return new Object[][]{
                    {"POIAWB", "certificateOfPractice_poiawb"},
                    {"coi", "COI"},
                    {"POAWTN", "franchiseeAgreement_poawtn"}
            };
        }

        return new Object[][]{
                {"shopInnerPhoto", "shopInnerPhoto"},
                {"qrStickerPhoto", "qrSticker1"},
                {"shopFrontPhoto", "shopFrontPhoto"},
                {"bankProof", "BankStatement"}

        };
    }

    @Test(priority = 7, description = "Upload Documents Dynamically", dataProvider = "documentData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void uploadDocument(String docType, String docProvided) throws Exception {
        String endPoint = P.API.get("UploadDoc");
        headers.put("Content-Type", "multipart/form-data");

        File uploadFile = new File(DOC_PATH);

        Map<String, String> params = new HashMap<>();
        params.put("solutionType", SOLUTION);
        params.put("channel", CHANNEL);
        params.put("entityType", ENTITY_TYPE);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        params.put("docType", docType);
        params.put("docProvided", docProvided);

        response = uploadDocumentToAPI(uploadFile, params, endPoint);
        Assert.assertEquals(response.getStatusCode(), 200);
    }

    // Helper method to upload documents to API
    private Response uploadDocumentToAPI(File uploadFile, Map<String, String> params, String endPoint) {
        String baseURI = P.API.get("api_url");
        RequestSpecification spec = new RequestSpecBuilder().setBaseUri(baseURI).build();

        try {
            return RestAssured.given()
                    .multiPart(uploadFile)
                    .spec(spec)
                    .relaxedHTTPSValidation()
                    .queryParams(params)
                    .headers(headers)
                    .post(endPoint);
        } catch (Exception e) {
            return null;
        }
    }

    @Test(priority = 8, description = "Submit lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void SubmitLead() throws JSONException {
        headers.put("Content-Type", "application/json");
        headers.put("channel", "DIY_PAYTM_APP");

        Map<String, String> params = getCommonParams();

        params.put("leadId", leadId);

        Map<String, Object> body = new HashMap<>();
        body.put("partialSave", false);
        body.put("IS_AGREEMENT_ACCEPTED", true);

        requestPath = "MerchantService/V1/sdMerchant/lead/updateLead/LimitUpgradeSubmitLeadRequest.json";
        response = bossToOeLeadUpdate.bossToOeLeadUpdate(requestPath, headers, params, body);


        Assert.assertEquals(response.getStatusCode(), 200);
    }

    public String createLead(String mid, String newToken) throws JSONException {
        Map<String, String> params = getCommonParams();
        Map<String, String> headers = new HashMap<>();
        headers.put("session_token", newToken);

        Map<String, String> body = new HashMap<>();

        body.put("mid", mid);
        body.put("closeOpenLead", "true");

        requestPath = "MerchantService/v1/sdMerchant/lead/CreateLeadMerchantLimitUpgradeRequest.json";
        response = gstUpdate.GSTUpdate(requestPath, headers, params, body);

        if (response.getStatusCode() == 200) {
            leadId = response.jsonPath().getString("leadId");
            return leadId;
        } else //recall the method if lead is not created
        {
            return createLead(mid, newToken);
        }

    }

    public void enterNonIndividualPanManually(String PAN, String leadId, String newToken) throws JSONException {
        Map<String, String> params = getCommonParams();
        params.put("leadId", leadId);
        Map<String, String> headers = new HashMap<>();
        headers.put("session_token", newToken);

        Map<String, Object> body = new HashMap<>();
        body.put("partialSave", "true");
        body.put("pan", PAN);
        String GSTIN = "07" + PAN + "1Z1";
        body.put("gstin", GSTIN);
        body.put("PAN", PAN);
        body.put("BUSINESS_ENTITY", ENTITY_TYPE);
        body.put("KYB_BUSINESS_ID", "");

        requestPath = "MerchantService/v1/sdMerchant/lead/updatePanLeadRequest.json";
        response = bossToOeLeadUpdate.bossToOeLeadUpdate(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
    }


    @DataProvider(name = "documentDataAlternative")
    public Object[][] documentDataAlternative() {

        if (ENTITY_TYPE.equalsIgnoreCase("PRIVATE_LIMITED") || ENTITY_TYPE.equalsIgnoreCase("PUBLIC_LIMITED")) {
            return new Object[][]{
                    {"POIAWB", "certificateOfPractice_poiawb"},
                    {"coi", "COI"},
                    {"POAWTN", "franchiseeAgreement_poawtn"}
            };
        }

        return new Object[][]{
                {"businessProof", "GSTCertificate"},
                {"shopInnerPhoto", "shopInnerPhoto"},
                {"qrStickerPhoto", "qrSticker1"},
                {"shopFrontPhoto", "shopFrontPhoto"},
                {"bankProof", "BankStatement"}
                //   {"pan", "pan"}

        };
    }

    public void uploadDocument(String docType, String docProvided, String leadId, String newToken) throws Exception {
        String endPoint = P.API.get("UploadDoc");
        headers.put("Content-Type", "multipart/form-data");
        headers.put("session_token", newToken);


        File uploadFile = new File(DOC_PATH);

        Map<String, String> params = new HashMap<>();
        params.put("solutionType", SOLUTION);
        params.put("channel", CHANNEL);
        params.put("entityType", ENTITY_TYPE);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        params.put("docType", docType);
        params.put("docProvided", docProvided);

        response = uploadDocumentToAPI(uploadFile, params, endPoint);
        Assert.assertEquals(response.getStatusCode(), 200);
    }

    public void callDataProviderAndUploadDocument(String leadId, String token) throws Exception {
        // Instantiate the class and access the data provider method
        Object[][] data = documentDataAlternative(); // Directly call the data provider method

        // Loop through the data and call uploadDocument for each document
        for (Object[] row : data) {
            String docType = (String) row[0];
            String docProvided = (String) row[1];

            // Call the uploadDocument method with each set of data
            uploadDocument(docType, docProvided, leadId, token);
        }
    }

    public void SubmitLead(String leadId, String newToken) throws JSONException {
        headers.put("Content-Type", "application/json");
        headers.put("channel", "DIY_PAYTM_APP");
        headers.put("session_token", newToken);

        Map<String, String> params = getCommonParams();

        params.put("leadId", leadId);

        Map<String, Object> body = new HashMap<>();
        body.put("partialSave", false);
        body.put("IS_AGREEMENT_ACCEPTED", true);

        requestPath = "MerchantService/V1/sdMerchant/lead/updateLead/LimitUpgradeSubmitLeadRequest.json";
        response = bossToOeLeadUpdate.bossToOeLeadUpdate(requestPath, headers, params, body);


        Assert.assertEquals(response.getStatusCode(), 200);
    }

    @Test(priority = 9, description = "", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void BillingAddressUpdateLeadFetchDocStatus() throws Exception {

        custId = GetResourceOwnerId(MOBILE_NUMBER, "paytm@123");

        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();

        Map<String, String> headers = new HashMap<>();
        headers.put("session_token", token); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "multipart/json");
        headers.put("version", "7.2.8"); // No change
        headers.put("deviceidentifier", "Xiaomi-RedmiNote9Pro-efc28fc97cede738"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solution", "diy_mco");
        queryParams.put("entityType", "PROPRIETORSHIP");
        queryParams.put("leadId", leadId);
        queryParams.put("merchantCustId", custId);
        queryParams.put("channel", "DIY_P4B_APP");
        queryParams.put("solutionTypeLevel2", "registered_address_update");

        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj, headers, queryParams);

        shopInsidePhotoDMSID = respObj.path("uploadedDocDetailsSet[0].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
//        AadharPhotoDMSID2 = respObj.path("uploadedDocDetailsSet[4].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['2']");
//        AadharPhotoDMSID1 = respObj.path("uploadedDocDetailsSet[4].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");   //   BankStatementDMSID = respObj.path("uploadedDocDetailsSet[3].uploadedDocs[0].uuid");
        BankStatementDMSID = respObj.path("uploadedDocDetailsSet[3].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
        shopFrontPhotoDMSID = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
//        PanphotoDMSID = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].uuid");
        qrStickerPhotoDMSID = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");

        System.out.println(BankStatementDMSID + shopFrontPhotoDMSID + shopInsidePhotoDMSID + qrStickerPhotoDMSID);

    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Billing Address update QC", priority = 10, dependsOnMethods = "BillingAddressUpdateLeadFetchDocStatus")
    public void BillingAddressUpdateQC() throws Exception {

        DBConnection dbConnectionObj = new DBConnection();
        int Ubmid = dbConnectionObj.getUserBusinessMappingId(MOBILE_NUMBER, "diy_mco", SOLUTION_TYPE_LEVEL_2);
        dbConnectionObj.assignAgentViaDB("1152", Ubmid);
        Long billingAddressUpdateWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);
        wfsidBillingAddressUpdate = String.valueOf(billingAddressUpdateWorkflowStatusId);

        requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadDiyProp.json";
        EditLead EditLeadObj = new EditLead(leadId, requestPath);
        EditLeadObj.getProperties().setProperty("uuidBankStatementPhoto", BankStatementDMSID);
        EditLeadObj.getProperties().setProperty("uuidShopFrontPhoto", shopFrontPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidShopFrontPhoto1", shopFrontPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidShopInsidePhoto1", shopInsidePhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidShopInsidePhoto", shopInsidePhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidQrStickerPhoto1", qrStickerPhotoDMSID);
        EditLeadObj.getProperties().setProperty("uuidQrStickerPhoto", qrStickerPhotoDMSID);
        //   EditLeadObj.getProperties().setProperty("uuidBusinessProof", businessProofDMSID);
        EditLeadObj.getProperties().setProperty("workflowStatusId", wfsidBillingAddressUpdate);
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("action", "SUBMIT");
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("Cookie", XMWCookie);
        Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);

    }
}
