package OCL.DIY.EDC;

import Request.DIYEDC.FetchMerchantDocDetails;
import Request.DIYEDC.FetchMid;
import Request.MerchantService.v3.MID;
import com.paytm.apitools.util.annotations.Owner;
import org.testng.Assert;
import org.testng.annotations.Test;

import Services.DIYEDC.Createlead1;
import Services.DIYEDC.FetchMerchantDocdetails;
import Services.DIYEDC.Fetchmid;

import Services.Utilities.Utilities;
import Services.MechantService.MiddlewareServices;

import java.io.File;
import java.util.Scanner;
import com.goldengate.common.BaseMethod;
import com.amazonaws.services.dynamodbv2.xspec.S;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import java.util.HashMap;
import java.util.Map;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import Services.Utilities.Utilities;
import org.w3c.dom.ls.LSInput;

public class CreateLead {

    String RequestPath;
    String leadId;
    String MID;
    String Token;
    Response response = null;
    Scanner sc = new Scanner(System.in);
    String Number ="8888701027";
    Createlead1 X = new Createlead1();
    FetchMerchantDocdetails Y = new FetchMerchantDocdetails();
    Fetchmid Z = new Fetchmid();
    Utilities Util = new Utilities();
    MiddlewareServices MS = new MiddlewareServices();
    String Aadhaar = Util.ValidAadhaar();

    String DocPath = "src/test/resources/MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/DocImage.png";


    @BeforeClass
    public void BeforeOperatiosn(){
        Token = BaseMethod.ApplicantToken(Number, "paytm@123");

//        if(Util.createNewAuthUser(Number,"paytm@123")){
//            Token = BaseMethod.ApplicantToken(Number, "paytm@123");
//        }
//        else{
//            Assert.assertFalse(false,"Number was not registered at Outh end");
//        }
    }



    @Test(priority = 1, description = "Fetches the existing doc details of the merchant", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_FetchMid() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("searchBy", "phoneNo");
        params.put("searchValue", Number);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Accept", "application/json");
//        header.put("Accept-Language", "en-GB,en-US;q=0.9,en;q=0.8");
//        header.put("Connection", "keep-alive");
//        header.put("Cookie", "BOSS_SESSION=bc1006fd-516f-4c62-ae50-4c15c4d7bb36");
        header.put("x-client-token", "eyJhbGciOiJIUzUxMiJ9.eyJpYXQiOjE2OTM1NzU0NTQsImNsaWVudC1pZCI6Ijg1YzJlN2UzLTJmMjItNDYxYy04NTRjLTVmNzdkOWY1MzJiZSJ9.Kw6zKtUv8Gs123O8FA8irgBKTFUFu2foOyfaB2tqITje49St41uJGO6LaLSyg5cy9Do6AWdb0k4APF81h-u62w");
        header.put("x-client-id","85c2e7e3-2f22-461c-854c-5f77d9f532behhzuHzBlYMh7d/b1nyvZDHYy2vAp23FGqJsglpDpItrT9p7XN2BnPMRtkMu5lict0HnLPod9vxUPbVuem3yrgA==");
//        header.put("Sec-Fetch-Dest", "empty");
//        header.put("Sec-Fetch-Mode", "cors");
//        header.put("Sec-Fetch-Site", "same-origin");
//        header.put("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36");
//        header.put("sec-ch-ua", "\"Not?A_Brand\";v=\"8\", \"Chromium\";v=\"108\", \"Google Chrome\";v=\"108\"");
//        header.put("sec-ch-ua-mobile", "?0");
//        header.put("sec-ch-ua-platform", "\"macOS\"");

//        MID = MS.v3FetchMID()
//        response = Z.FetchmidResponse(header, params);
        response = Z.FetchmidResponse(header, params);

//        MID= response.jsonPath().getString("mid");
        MID = response.getBody().print();
//        System.out.println(MID);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 1, description = "Fetches the existing doc details of the merchant", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_FetchMerchantDocDetails() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("mid", MID);
        params.put("solution", "diy_mco");
        params.put("solutionTypeLevel2","diy_edc");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);

        RequestPath = "MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/FetchMerchantDocDeatils_DIYEDCRequest.json";

        response = Y.FetchMerchantDocdetailsResponse(RequestPath, header, params);

        leadId = response.jsonPath().getString("leadId");
        System.out.println(leadId);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 2, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_CreateLead1() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionTypeLevel2","diy_edc");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        System.out.println("enter Merchants number");
        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("devicePid", "9234791539");



        RequestPath = "MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/CreateLead_DIYMCORequest.json";

        response = X.CreateleadResponse(RequestPath,header, params, body);

        leadId = response.jsonPath().getString("leadId");
        System.out.println(leadId);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 2, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_AadhaarDeatils() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadid",leadId);
        params.put("solutionTypeLevel2","diy_edc");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_PAYTM_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("aadharNumber", Aadhaar);
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "true");
        body.put("AADHAR_NO_MISMATCHED", "false");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Female");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");
        body.put("userAddressLine1", "9187");
        body.put("userAddressLine2", "Ahxhshj");
        body.put("userAddressLine3", "Sjjsiskso");
        body.put("userAddressPincode", "126102");
        body.put("userAddressState", "Haryana");


        RequestPath = "MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/AadhaarDetails_DIYEDCRequest.json";

        response = X.CreateleadResponse(RequestPath,header, params, body);

        leadId = response.jsonPath().getString("leadId");
        System.out.println(leadId);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 2, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_EnterPANManually() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadid",leadId);
        params.put("solutionTypeLevel2","diy_edc");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_PAYTM_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("partialSave", "true");
        body.put("pan","**********");
        body.put("pan","**********");
        body.put("BUSINESS_ENTITY","INDIVIDUAL");

        RequestPath = "MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/EnterPANManually_DIYEDCRequest.json";

        response = X.CreateleadResponse(RequestPath,header, params, body);

        leadId = response.jsonPath().getString("leadId");
        System.out.println(leadId);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }
    @Test(priority = 2, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_EnterGSTINManually() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadid",leadId);
        params.put("solutionTypeLevel2","diy_edc");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_PAYTM_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("partialSave", "true");
        body.put("gstin","09EPSPD0245Q1Z6");
        body.put("pan","**********");
        body.put("pan","**********");
        body.put("BUSINESS_ENTITY","INDIVIDUAL");
        body.put("GSTIN_ENTERED_MANUALLY", "true");


        RequestPath = "MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/AadhaarDetails_DIYEDCRequest.json";

        response = X.CreateleadResponse(RequestPath,header, params, body);

        leadId = response.jsonPath().getString("leadId");
        System.out.println(leadId);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 2, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_UpdateCatSubcat() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadid",leadId);
        params.put("solutionTypeLevel2","diy_edc");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_PAYTM_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("SEGMENT","Education");
        body.put("SUB_SEGMENT","School");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/UpdateCatSubcat_DIYEDCRequest.json";

        response = X.CreateleadResponse(RequestPath,header, params, body);

        leadId = response.jsonPath().getString("leadId");
        System.out.println(leadId);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 2, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_BankDetailsUpdate() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadid",leadId);
        params.put("solutionTypeLevel2","diy_edc");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_PAYTM_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails","false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber","**************");
        body.put("bankName","ICICI bank");
        body.put("ifsc","ICIC0004145");

        RequestPath = "MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/BankDetailsUpdate_DIYEDCRequest.json";

        response = X.CreateleadResponse(RequestPath,header, params, body);

        leadId = response.jsonPath().getString("leadId");
        System.out.println(leadId);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 2, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_TurnoverDeclaration() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadid",leadId);
        params.put("solutionTypeLevel2","diy_edc");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_PAYTM_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("partialSave", "true");
        body.put("TURNOVER_DECLARATION","BIG");

        RequestPath = "MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/TurnoverDeclaration_DIYEDCRequest.json";

        response = X.CreateleadResponse(RequestPath,header, params, body);

        leadId = response.jsonPath().getString("leadId");
        System.out.println(leadId);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 2, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_ReviewBusinessDetails() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadid",leadId);
        params.put("solutionTypeLevel2","diy_edc");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_PAYTM_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("partialSave", "false");

        RequestPath = "MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/ReviewBussinessDetails_DIYEDCRequest.json";

        response = X.CreateleadResponse(RequestPath,header, params, body);

        leadId = response.jsonPath().getString("leadId");
        System.out.println(leadId);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 2, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_LeadReviewOrderDetails() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadid",leadId);
        params.put("solutionTypeLevel2","diy_edc");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_PAYTM_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("partialSave", "false");
        body.put("IS_AGREEMENT_ACCEPTED", "true");


        RequestPath = "MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/ReviewOrderDetails_DIYEDCRequest.json";

        response = X.CreateleadResponse(RequestPath,header, params, body);

        leadId = response.jsonPath().getString("leadId");
        System.out.println(leadId);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 5, description = "Upload aadhar ocr", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_Upload_AadharDoc() throws Exception {

        String endPoint = P.API.get("UploadAadhaarDoc1");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "poi");
        params.put("docProvided","aadhaar");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType","diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId",leadId);
        params.put("pageNo","1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", Token);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        //uploading page 2 of aadhar
        params.put("pageNo","2");
        Response resp2 = UploadDocInAPI(uploadFile,params,header,endPoint);
        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }

    @Test(priority = 5, description = "Upload Business doc", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_Upload_BusinessDoc() throws Exception {

        String endPoint = P.API.get("UploadBusinessDoc");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "businessProof1");
        params.put("docProvided","certificateOfPractice");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType","diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId",leadId);
        params.put("pageNo","1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", Token);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }
    @Test(priority = 5, description = "Upload Bank Proof", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_Upload_BankProof() throws Exception {

        String endPoint = P.API.get("BankProof");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "bankProof");
        params.put("docProvided","BankStatement");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType","diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId",leadId);
        params.put("pageNo","1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", Token);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }
    @Test(priority = 5, description = "Upload Bank Proof", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_Upload_POAWTN_Doc() throws Exception {

        String endPoint = P.API.get("UploadDoc");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "POAWTN");
        params.put("docProvided","franchiseeAgreement_poawtn");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType","diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId",leadId);
        params.put("pageNo","1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", Token);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 5, description = "Upload Bank Proof", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_Upload_Patnarship_Deed_Doc() throws Exception {

        String endPoint = P.API.get("UploadDoc");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "partnership_deed");
        params.put("docProvided","PartnershipDeed");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType","diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId",leadId);
        params.put("pageNo","1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", Token);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 5, description = "Upload Bank Proof", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_Upload_Trust_Deed_Doc() throws Exception {

        String endPoint = P.API.get("UploadDoc");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "trust_deed");
        params.put("docProvided","TrustDeed");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType","diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId",leadId);
        params.put("pageNo","1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", Token);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 5, description = "Upload Bank Proof", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_Upload_POIAWB_Doc() throws Exception {

        String endPoint = P.API.get("UploadDoc");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "POIAWB");
        params.put("docProvided","certificateOfPractice_poiawb");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType","diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId",leadId);
        params.put("pageNo","1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", Token);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 5, description = "Upload Bank Proof", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_Upload_License_Proof() throws Exception {

        String endPoint = P.API.get("UploadDoc");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "licenseProof");
        params.put("docProvided","governmentIssuedLicense");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType","diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId",leadId);
        params.put("pageNo","1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", Token);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 5, description = "Upload Bank Proof", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_Upload_COI_Doc() throws Exception {

        String endPoint = P.API.get("UploadDoc");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "coi");
        params.put("docProvided","COI");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType","diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId",leadId);
        params.put("pageNo","1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", Token);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }
    @Test(priority = 5, description = "Upload Bank Proof", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_Upload_Doc() throws Exception {

        String endPoint = P.API.get("UploadDoc");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "coi");
        params.put("docProvided","COI");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType","diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId",leadId);
        params.put("pageNo","1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", Token);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }









    public Response UploadDocInAPI(File uploadFile, Map<String, String> params,Map<String, String> header,String endPoint) throws Exception{

        Response resp = null;
        String baseURI = P.API.get("api_url");

        RequestSpecification spec;

        try {
            spec = new RequestSpecBuilder().setBaseUri(baseURI).build();
            resp = RestAssured.given().multiPart(uploadFile).spec(spec).relaxedHTTPSValidation().queryParams(params).headers(header)
                    .post(endPoint);
        }
        catch (Exception e){
            e.printStackTrace();
        }
        return resp;

    }





}


//    @Test(priority = 1, description = "Fetches the existing doc details of the merchant", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC001_FetchMerchantDocDetails() {
//
//        Map<String, String> params = new HashMap<String, String>();
//        params.put("mid", MID);
//        params.put("solution", "diy_mco");
//        params.put("solutionTypeLevel2","diy_edc");
//
//        Map<String, String> header = new HashMap<String, String>();
//        header.put("Content-Type", "application/json");
//        header.put("session_token", Token);
//
//        RequestPath = "MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/FetchMerchantDocDeatils_DIYEDCRequest.json";
//
//        response = Y.FetchMerchantDocdetailsResponse(RequestPath, header, params);
//
//        leadId = response.jsonPath().getString("leadId");
//        System.out.println(leadId);
//        int statusCode = response.getStatusCode();
//        Assert.assertEquals(statusCode, 200);
//
//    }
//
//    @Test(priority = 2, description = "Creation of a lead", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC001_CreateLead1() {
//
//        Map<String, String> params = new HashMap<String, String>();
//        params.put("solution", "diy_mco");
//        params.put("entityType", "INDIVIDUAL");
//        params.put("channel", "DIY_P4B_APP");
//        params.put("solutionTypeLevel2","diy_edc");
//
//        Map<String, String> header = new HashMap<String, String>();
//        header.put("Content-Type", "application/json");
//        header.put("session_token", Token);
//        header.put("androidId", "AashitAndroid");
//        header.put("browserName", "chrome");
//        header.put("browserVersion", "4.6.3");
//        header.put("cache-control", "no-cache");
//        header.put("ipAddress", "************");
//        header.put("latitude", "28.32");
//        header.put("longitude", "77.213");
//        header.put("imei", "1234");
//        header.put("channel", "DIY_P4B_APP");
//
//        System.out.println("enter Merchants number");
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("mid", MID);
//        body.put("devicePid", "9234791539");
//
//
//
//        RequestPath = "MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/CreateLead_DIYMCORequest.json";
//
//        response = X.CreateleadResponse(RequestPath,header, params, body);
//
//        leadId = response.jsonPath().getString("leadId");
//        System.out.println(leadId);
//        int statusCode = response.getStatusCode();
//        Assert.assertEquals(statusCode, 200);
//
//    }
//
//    @Test(priority = 2, description = "Creation of a lead", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC001_AadhaarDeatils() {
//
//        Map<String, String> params = new HashMap<String, String>();
//        params.put("solution", "diy_mco");
//        params.put("entityType", "INDIVIDUAL");
//        params.put("channel", "DIY_P4B_APP");
//        params.put("leadid",leadId);
//        params.put("solutionTypeLevel2","diy_edc");
//
//        Map<String, String> header = new HashMap<String, String>();
//        header.put("Content-Type", "application/json");
//        header.put("session_token", Token);
//        header.put("androidId", "AashitAndroid");
//        header.put("browserName", "chrome");
//        header.put("browserVersion", "4.6.3");
//        header.put("cache-control", "no-cache");
//        header.put("ipAddress", "************");
//        header.put("latitude", "28.32");
//        header.put("longitude", "77.213");
//        header.put("imei", "1234");
//        header.put("channel", "DIY_PAYTM_APP");
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("aadharNumber", Aadhaar);
//        body.put("partialSave", "true");
//        body.put("AADHAR_OCR_DONE", "true");
//        body.put("AADHAR_NO_MISMATCHED", "false");
//        body.put("AADHAR_NO_NOT_READABLE", "false");
//        body.put("GENDER", "Female");
//        body.put("MERCHANT_DOB", "12/27/1988");
//        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
//        body.put("userAddressCity", "Jind");
//        body.put("userAddressLandMark", "ANMOL JAIN");
//        body.put("userAddressLine1", "9187");
//        body.put("userAddressLine2", "Ahxhshj");
//        body.put("userAddressLine3", "Sjjsiskso");
//        body.put("userAddressPincode", "126102");
//        body.put("userAddressState", "Haryana");
//
//
//        RequestPath = "MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/AadhaarDetails_DIYEDCRequest.json";
//
//        response = X.CreateleadResponse(RequestPath,header, params, body);
//
//        leadId = response.jsonPath().getString("leadId");
//        System.out.println(leadId);
//        int statusCode = response.getStatusCode();
//        Assert.assertEquals(statusCode, 200);
//
//    }


