package OCL.DIY.OnlineMerchantIndigoOnboarding;

import Request.MerchantService.v1.upgradeMid.lead.ValidateBankDetailsOnline;
import Request.oAuth.oAuthWormhole.CreateUser;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class TestOnlineMerchantV1IndigoProprietor extends BaseMethod {

    String sessionToken = "";
    String agentPassword = "paytm@123";
    public static String bankaccountholderName;
    public static String bankdetailsUuid;
    public static boolean namematchStatus = true;
    public static final String leadID = "";
    public static String solID;
    public static String CustId = "";
    public static String MID = "";

    public static final String SOLUTION = "online_merchant";
    public static final String SOLUTION_TYPE_LEVEL_2 = "CLIX";
    public static final String SOLUTION_TYPE_LEVEL_3 = "Unsecured_Short_term_Loan_Simplified";
    public static final String ENTITY_TYPE = "INDIVIDUAL";
    public static final String CHANNEL = "UMP_WEB";
    public static String PAN = "";
    public static final String DOB = "1989-04-21";
    public static final String EMAIL = "";
    public static final String ISSUER = "OE";
    public static final String CLIENT_ID = "LMS";
    public static String MOBILE = "";
    public static String BUSINESSPAN = "";
    public static String EntityTypeUL = "PROPRIETORSHIP";
    public static final String BUSINESSNAME = "RONIT ARYA";
    public static final String CATEGORY = "BFSI";
    public static final String SUBCATEGORY = "Loans";
    public static final String BANKNAME = "ICICI Bank";
    public static String BANKACCOUNTNUMBER ;
    public static final String IFSC = "Icic0006622";
    public static final boolean indigoDisabled = false;
    public static String regPan;
    public static boolean lessThan40kTurnover = false;
    public static boolean exemptedFromGST = false;
    public static final String UNREG_GSTIN = "";
    public static final String REG_GSTIN = "";
    public static String gstin = "";
    public static String retailRelatedBusinessUuid = "";
    public static String adhaarHash = "";
    public static final String nameAsPerAdhaar = "TOUCH WOOD LIMITED";
    public static final boolean adhaarVerified = true;
    public static final String shopRelatedBusinessUuid = "";

    private static final Logger LOGGER = LogManager.getLogger(TestOnlineMerchantV1IndigoProprietor.class);
    private static final Utilities UtilObj = null;

    Map<String, String> commonHeaders;

    @BeforeClass()
    public void intitializeInputData() throws IOException {

		Utilities accObj = new Utilities();
		MOBILE = accObj.randomMobileNumberGenerator();
		LOGGER.info("New Number is : " + MOBILE);
		adhaarHash = accObj.randomMobileNumberGenerator();
		BANKACCOUNTNUMBER = accObj.randomMobileNumberGenerator();
		LOGGER.info("New aadhar is : " + adhaarHash);
		//LOGGER.info(" Before Suite Method for Agent Login ");
		CreateUser OauthObj = new CreateUser();
		OauthObj.setHeader("Content-Type", "application/json");
		OauthObj.getProperties().setProperty("mobile", MOBILE);
		OauthObj.getProperties().setProperty("loginPassword", "paytm@123");
		Response OauthResp = OauthObj.callAPI();

		sessionToken = ApplicantToken(MOBILE, agentPassword);
		//LOGGER.info("Applicant Token for Lending : " + sessionToken);
		commonHeaders = setcommonHeaders();
		LOGGER.info("Entity is : " + EntityTypeUL);
		regPan = accObj.randomPublicPANValueGenerator();
		LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
		PAN = accObj.randomIndividualPANValueGenerator();
		LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
		gstin = "09" + PAN + "1Z1";

	}


    private Map<String, String> setcommonHeaders() {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        return headers;
    }

    MiddlewareServices middlewareServiceObject = new MiddlewareServices();


    @Test(priority = 0, groups = {"Regression"}, description = "Create Lead Indigo")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0001_RegisteredPan() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        //	queryParams.put("entityType", EntityTypeUL);
        queryParams.put("channel", CHANNEL);
        queryParams.put("mobile", MOBILE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("businessPan", regPan);
        body.put("businessName", BUSINESSNAME);
        body.put("leadId", "");
        body.put("kybBusinessId", "");
        body.put("solutionLeadId", "");
        body.put("indigoDisabled", indigoDisabled);

        Response responseObject = middlewareServiceObject.onlinecreateLeadIndigo(queryParams, headers, body);

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Need to Pass EntityType in Request");


    }


    @Test(priority = 0, groups = {"Regression"}, description = "Create Lead Indigo")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0002_postCreateLead() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("channel", CHANNEL);
        queryParams.put("mobile", MOBILE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("businessPan", PAN);
        body.put("businessName", BUSINESSNAME);
        body.put("leadId", "");
        body.put("kybBusinessId", "");
        body.put("solutionLeadId", "");
        body.put("indigoDisabled", indigoDisabled);

        Response responseObject = middlewareServiceObject.onlinecreateLeadIndigo(queryParams, headers, body);


        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);
        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        solID = responseObject.jsonPath().getString("solutionLeadId");
        EntityTypeUL = responseObject.jsonPath().getString("entityType");





    }
    @Test(priority = 0, groups = {"Regression"}, description = "Update Business for online merchant lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0003_postUpdateBusiness() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        queryParams.put("solutionLeadId", solID);
        queryParams.put("channel", CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("name", BUSINESSNAME);
        body.put("category", CATEGORY);
        body.put("subCategory", SUBCATEGORY);


        Response responseObject = middlewareServiceObject.onlineUpdateBusiness(queryParams, headers, body);

        // Status Code Validation
        //middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);

        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);

        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 204);

    }
    @Test(priority = 0, groups = {"Regression"}, description = "Update Additonal Details for online merchant lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0004_postUpdateAdditionalDetailsGSTIN() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        queryParams.put("channel", CHANNEL);


        Map<String, String> headers =  commonHeaders;


        Map<String, Object> body = new HashMap<String, Object>();

        body.put("solutionLeadId", solID);
        body.put("lessThan40kTurnover", lessThan40kTurnover);
        body.put("exemptedFromGST", exemptedFromGST);
        body.put("gstin", gstin);

        Response responseObject = middlewareServiceObject.onlineUpdateAddtionalDetailsGSTIndigoOnboarding(queryParams, headers, body);


        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);

        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        retailRelatedBusinessUuid = responseObject.jsonPath().getString("retailRelatedBusinessUuid");

    }


    @Test(priority = 0, groups = {"Regression"}, description = "Fetch lead Data")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0005_fetchLeadData() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);

        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        queryParams.put("solutionLeadId", solID);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;


        Response responseObject = middlewareServiceObject.onlineFetchLeadData(queryParams, headers);

        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);


        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        EntityTypeUL = responseObject.jsonPath().getString("solution.solutionAdditionalInfo.ENTITY_TYPE");

        CustId = responseObject.jsonPath().getString("custId");

    }


    @Test(priority = 0, groups = {"Regression"}, description = "Update Additonal Details for online merchant lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0006_postUpdateAdditionalDetailsAdhaar() {


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        queryParams.put("channel", CHANNEL);


        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();

        body.put("solutionLeadId", solID);
        body.put("adhaarHash", adhaarHash);
        body.put("nameAsPerAdhaar", nameAsPerAdhaar);
        body.put("adhaarVerified", adhaarVerified);
        body.put("shopRelatedBusinessUuid", retailRelatedBusinessUuid);

        Response responseObject = middlewareServiceObject.onlineUpdateAddtionalDetailsAdhaarIndigoOnboarding(queryParams, headers, body);


        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);

        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }


    @Test(priority = 0, groups = {"Regression"}, description = "Update Additonal Details for online merchant lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0007_postUpdateAdditionalDetailsAddress() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);

        queryParams.put("channel", CHANNEL);


        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;


        Map<String, Object> body = new HashMap<String, Object>();

        body.put("addressSubType", "CORRESPONDENCE");
        body.put("addressType", "Office Purp");
        body.put("addressUuid", "UIDADD");
        body.put("city", "Noida");
        body.put("country", "India");
        body.put("displayMessage", "VICTORY");
        body.put("landmark", "Near Sash Building");
        body.put("latitude", 28.09);
        body.put("line1", "F 9201");
        body.put("line2", "Sector 26");
        body.put("line3", "NAveen Okhla");
        body.put("longitude", 27.19);
        body.put("pincode", 201301);
        body.put("refId", "BuckMouth");
        body.put("residentialStatus", "Redi");

        body.put("state", "Uttar Pradesh");
        body.put("statusCode", 0);
        body.put("title", "Haryana shop");

        body.put("displayName", "Concer Disp");
        body.put("gstinExemptedCategory", "");
        body.put("leadId", "");


        body.put("addressSubType1", "Registered");
        body.put("addressType1", "My Commerce");
        body.put("addressUuid1", "RDXROID");
        body.put("city1", "Central Delhi");
        body.put("country1", "India");
        body.put("displayMessage1", "NItyaro");
        body.put("landmark1", "Near Sash Building");
        body.put("latitude1", 22.7);
        body.put("line11", "Denmar");
        body.put("line21", "MINE");
        body.put("line31", "LINE3 For Add");
        body.put("longitude1", 28.6);
        body.put("pincode1", 110011);
        body.put("refId1", "FEDA");
        body.put("residentialStatus1", "True");

        body.put("state1", "Delhi");
        body.put("statusCode1", 0);
        body.put("title1", "NASDH");

        //	body.put("retailStore", "false");
        body.put("shopRelatedBusinessUuid", retailRelatedBusinessUuid);
        body.put("email", "<EMAIL>");
        body.put("solutionLeadId", solID);
        //	body.put("appUrl", "https://google.com");
        //	body.put("websiteUrl", "https://google.com");
        body.put("businessProofNotRequired", "false");


        Response responseObject = middlewareServiceObject.onlineUpdateAddtionalDetailsAddressIndigoOnboarding(queryParams, headers, body);


        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);

        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);


    }





    @Test(priority = 0, groups = {"Regression"}, description = "Validate bank details for online merchant lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC0008_postValidateBankDetailsTest() {
        ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);

        queryParams.put("channel", CHANNEL);


        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();

        body.put("bankName", BANKNAME);
        body.put("bankAccountNumber", BANKACCOUNTNUMBER);
        body.put("ifsc", IFSC);
        body.put("bankAccountHolderName", "Sarvagya Dixit");
        body.put("beneficiaryName", "");


        Response responseObject = middlewareServiceObject.onlineMerchantValidateBank(queryParams, headers, body);

        validateBankDetailsResponseObject.setRefId(responseObject.jsonPath().getString("refId"));
        validateBankDetailsResponseObject.setStatusCode(responseObject.jsonPath().getString("statusCode"));
        validateBankDetailsResponseObject.setDisplayMessage(responseObject.jsonPath().getString("displayMessage"));
        validateBankDetailsResponseObject.setBankAccountHolderName(responseObject.jsonPath().getString("bankAccountHolderName"));
        validateBankDetailsResponseObject.setBankDetailsUuid(responseObject.jsonPath().getString("bankDetailsUuid"));
        validateBankDetailsResponseObject.setNameMatchStatus(responseObject.jsonPath().getString("nameMatchStatus"));
        int statusCode = responseObject.getStatusCode();
        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
        Assert.assertEquals(statusCode, 200);


        bankaccountholderName = responseObject.jsonPath().getString("bankAccountHolderName");
        bankdetailsUuid = responseObject.jsonPath().getString("bankDetailsUuid");
        // namematchStatus =responseObject.jsonPath().getChar("nameMatchStatus");

        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
    }


    @Test(priority = 0, groups = {"Regression"}, description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC0009_postUpdateBankDetailsTest() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        //queryParams.put("mobile", MOBILE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();

        body.put("bankName", BANKNAME);
        body.put("bankAccountNumber", BANKACCOUNTNUMBER);
        body.put("ifsc", IFSC);
        body.put("bankAccountHolderName", bankaccountholderName);
        body.put("beneficiaryName", "");
        body.put("bankDetailsUuid", bankdetailsUuid);
        body.put("nameMatchStatus", namematchStatus);
        //body.put("leadId", leadID);
        body.put("SolutionLeadId", solID);


        Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);


    }


    @Test(priority = 0, groups = {"Regression"}, description = "Update Tnc Online merchant ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC0010_postTncUpdate() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        queryParams.put("leadId", leadID);
        queryParams.put("solutionLeadId", solID);
        queryParams.put("channel", CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;


        Response responseObject = middlewareServiceObject.onlineMerchantUpdateTnc(queryParams, headers);

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        String displayMessage = responseObject.jsonPath().getString("displayMessage");
        Assert.assertEquals(displayMessage, "Tnc saved, workflow updated.");

    }

    @Test(priority = 0, description = "Get Cookie for OE Panel", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0011_PG_CALLBACK_MANUAL() throws SQLException, JsonProcessingException {
        // XMWToken=XMWCookie;

        //   LOGGER.info("XMW token is :"+XMWToken);

        LOGGER.info("Hitting Callback for MID");
        MID = PG_CallBack_Insatnt50K(CustId);
    }

    @Test(groups = {"Regression"}, description = "fetch lead status")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0011_fetchLeadStatus() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        queryParams.put("channel", CHANNEL);
        queryParams.put("solutionLeadId", solID);

        Map<String, String> headers = commonHeaders;

        Response responseObject = middlewareServiceObject.onlineFetchLead(queryParams, headers);
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        Assert.assertEquals(responseObject.jsonPath().getString("leadStatus"), "LEAD_SUBMITTED");
        Assert.assertEquals(responseObject.jsonPath().getString("leadSubStatus"), "BUSINESS_REGISTRATION_PENDING");
        Assert.assertEquals(responseObject.jsonPath().getString("childLeadStatus"), "LEAD_NOT_PRESENT");
        Assert.assertEquals(responseObject.jsonPath().getString("childLeadSubStatus"), "LEAD_NOT_PRESENT");

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch lead Data")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0012_fetchLeadData() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        queryParams.put("channel", CHANNEL);
        queryParams.put("solutionLeadId", solID);

        Map<String, String> headers = commonHeaders;
        Response responseObject = middlewareServiceObject.onlineFetchLeadData(queryParams, headers);

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), "BUSINESS_REGISTRATION_PENDING");
        Assert.assertEquals(responseObject.jsonPath().getString("workflowVersion"), "V3");
        Assert.assertEquals(responseObject.jsonPath().getString("complianceStatus"), "PENDING");
    }

    @Test(groups = {"Regression"}, description = "payments lead status")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0013_paymentsLeadStatus() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("channel", CHANNEL);

        Map<String, String> headers = commonHeaders;


        Response responseObject = middlewareServiceObject.onlinePaymentsLead(queryParams, headers);

        String solName = responseObject.jsonPath().get("solutions[0].solName").toString();
        String leadStatus = responseObject.jsonPath().get("solutions[0].leadStatus").toString();
        String leadSubStatus = responseObject.jsonPath().get("solutions[0].leadSubStatus").toString();

        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        Assert.assertEquals(solName, "online_merchant");
        Assert.assertEquals(leadStatus, "LEAD_SUBMITTED");
        Assert.assertEquals(leadSubStatus, "BUSINESS_REGISTRATION_PENDING");

    }


}