package OCL.DIY.OnlineMerchantIndigoOnboarding;

import Request.MerchantService.v1.upgradeMid.lead.ValidateBankDetailsOnline;
import Request.oAuth.oAuthWormhole.CreateUser;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class TestOnlineMerchantIndigoErrorMessages extends BaseMethod {

    public static final String leadID = "";
    public static final String SOLUTION = "online_merchant";
    public static final String SOLUTION_TYPE_LEVEL_2 = "CLIX";
    public static final String SOLUTION_TYPE_LEVEL_3 = "Unsecured_Short_term_Loan_Simplified";
    public static final String ENTITY_TYPE = "INDIVIDUAL";
    public static final String CHANNEL = "UMP_WEB";
    public static final String DOB = "1989-04-21";
    public static final String EMAIL = "";
    public static final String ISSUER = "OE";
    public static final String CLIENT_ID = "LMS";
    public static final String WORKFLOW_VERSION = "V2";
    public static final String BUSINESSNAME = "RONIT ARYA";
    public static final String CATEGORY = "BFSI";
    public static final String SUBCATEGORY = "Loans";
    public static final String BANKNAME = "ICICI Bank";
    public static final String IFSC = "ICICI006622";
    public static final boolean indigoDisabled = false;
    public static final String UNREG_GSTIN = "";
    public static final String REG_GSTIN = "";
    public static final String adhaarHash = "************";
    public static final String nameAsPerAdhaar = "TOUCH WOOD LIMITED";
    public static final boolean adhaarVerified = true;
    public static final String shopRelatedBusinessUuid = "";
    private static final Logger LOGGER = LogManager.getLogger(TestOnlineMerchantV1IndigoProprietor.class);
    private static final Utilities UtilObj = new Utilities();
    public static String bankaccountholderName;
    public static String bankdetailsUuid;
    public static boolean namematchStatus = true;
    public static String solID;
    public static String CustId = "";
    public static String MID = "";
    public static String PAN = "";
    public static String MOBILE = "";
    public static String BUSINESSPAN = "";
    public static String EntityTypeUL = "TRUST";
    public static String BANKACCOUNTNUMBER ;
    public static String gstin = "";
    public static String retailRelatedBusinessUuid = "";
    String sessionToken = "";
    String agentPassword = "paytm@123";
    Map<String, String> commonHeaders;
    MiddlewareServices middlewareServiceObject = new MiddlewareServices();

    @BeforeClass()
    public void intitializeInputData() throws IOException {
        MOBILE = UtilObj.randomMobileNumberGenerator();
        LOGGER.info("New Number is : " + MOBILE);
        CreateUser OauthObj = new CreateUser();
        OauthObj.setHeader("Content-Type", "application/json");
        OauthObj.getProperties().setProperty("mobile", MOBILE);
        OauthObj.getProperties().setProperty("loginPassword", "paytm@123");
        Response OauthResp = OauthObj.callAPI();
        sessionToken = ApplicantToken(MOBILE, agentPassword);
        commonHeaders = setcommonHeaders(sessionToken);
        LOGGER.info("Entity is : " + EntityTypeUL);
        PAN = UtilObj.randomTrustPANValueGenerator();
        BANKACCOUNTNUMBER= String.valueOf(Utilities.randomNumberGenerator(10));
        LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
        gstin = "09" + PAN + "1Z1";
    }

    private Map<String, String> setcommonHeaders(String sessionToken) {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        return headers;
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Create Lead Indigo")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0001_CreateLead() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("channel", CHANNEL);
        queryParams.put("mobile", MOBILE);
        Map<String, String> headers=commonHeaders;
        Map<String, Object> body = new HashMap<String, Object>();
        body.put("businessPan", PAN);
        body.put("businessName", BUSINESSNAME);
        body.put("leadId", "");
        body.put("kybBusinessId", "");
        body.put("solutionLeadId", "");
        body.put("indigoDisabled", indigoDisabled);

        Response responseObject = middlewareServiceObject.onlinecreateLeadIndigo(queryParams, headers, body);
        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        solID = responseObject.jsonPath().getString("solutionLeadId");

    }

    @Test(groups = {"Regression"}, description = "Create Lead Indigo with same mobile and same pan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0002_CreateLeadWithSameMobileAndDiffPAN() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("channel", CHANNEL);
        queryParams.put("mobile", MOBILE);

        Map<String, String> headers=commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("businessPan", PAN);
        body.put("businessName", BUSINESSNAME);
        body.put("leadId", "");
        body.put("kybBusinessId", "");
        body.put("solutionLeadId", "");
        body.put("indigoDisabled", indigoDisabled);

        Response responseObject = middlewareServiceObject.onlinecreateLeadIndigo(queryParams, headers, body);
        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Application already exists with Mobile No."));
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Please continue with the same or login with a new account."));
        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"Lead is already present.");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionLeadId"),solID);

    }

    @Test(groups = {"Regression"}, description = "Create Indigo lead with same mobile but diff pan")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0003_CreateLeadWithSameMobileAndDiffPAN() {

       String PAN1 = UtilObj.randomTrustPANValueGenerator();
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("channel", CHANNEL);
        queryParams.put("mobile", MOBILE);

        Map<String, String> headers=commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("businessPan", PAN1);
        body.put("businessName", BUSINESSNAME);
        body.put("leadId", "");
        body.put("kybBusinessId", "");
        body.put("solutionLeadId", "");
        body.put("indigoDisabled", indigoDisabled);

        Response responseObject = middlewareServiceObject.onlinecreateLeadIndigo(queryParams, headers, body);
        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Application already exists with Mobile No."));
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Please continue with the same or login with a new account."));
        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"Lead is already present.");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionLeadId"),solID);
    }

    @Test(groups = {"Regression"}, description = "Create Indigo lead with same mobile but diff entity")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0004_CreateLeadWithSameMobileAndDiffEntity() {

        String PAN1 = UtilObj.randomPartnershipPANValueGenerator();
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("channel", CHANNEL);
        queryParams.put("mobile", MOBILE);

        Map<String, String> headers=commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("businessPan", PAN1);
        body.put("businessName", BUSINESSNAME);
        body.put("leadId", "");
        body.put("kybBusinessId", "");
        body.put("solutionLeadId", "");
        body.put("indigoDisabled", indigoDisabled);

        Response responseObject = middlewareServiceObject.onlinecreateLeadIndigo(queryParams, headers, body);
        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Another application already in progress with Mobile No."));
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Entity type TRUST. Please continue with the same or login with a new account."));
        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"Your details could not be saved. Lead already exist with different EntityType, please continue with same.");
    }

    @Test(groups = {"Regression"}, description = "Create Indigo lead with same PAN but diff Mob")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0005_CreateLeadWithSamePANAndDiffMob() {
        String MOBILE1 = UtilObj.randomMobileNumberGenerator();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("channel", CHANNEL);
        queryParams.put("mobile", MOBILE1);
        CreateUser OauthObj = new CreateUser();
        OauthObj.setHeader("Content-Type", "application/json");
        OauthObj.getProperties().setProperty("mobile", MOBILE1);
        OauthObj.getProperties().setProperty("loginPassword", "paytm@123");
        Response OauthResp = OauthObj.callAPI();
        String localSessionToken = ApplicantToken(MOBILE1, agentPassword);
        Map<String, String> headers=setcommonHeaders(localSessionToken);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("businessPan", PAN);
        body.put("businessName", BUSINESSNAME);
        body.put("leadId", "");
        body.put("kybBusinessId", "");
        body.put("solutionLeadId", "");
        body.put("indigoDisabled", indigoDisabled);

        Response responseObject = middlewareServiceObject.onlinecreateLeadIndigo(queryParams, headers, body);
        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 500);
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Another Application already in progress with PAN against Mobile Number:"));
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Please continue with the same or provide a new PAN"));
        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"Your details could not be saved. Lead already exist with same PAN, please continue with same.");
    }

    @Test(priority = 0,groups = {"Regression"},description = "Update Business")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0006_postUpdateBusiness() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        queryParams.put("solutionLeadId", solID);
        queryParams.put("channel", CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("name", BUSINESSNAME);
        body.put("category", CATEGORY);
        body.put("subCategory", SUBCATEGORY);

        Response responseObject = middlewareServiceObject.onlineUpdateBusiness(queryParams, headers, body);

        String responseBody  =responseObject.getBody().asString();
        System.out.println("Response Body is:" +responseBody);

        //Validation
        int statusCode=responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 204);
    }

    @Test(priority = 0,groups = {"Regression"},description = "Update Additonal Details[gstin]")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0007_postUpdateAdditionalDetailsGSTIN() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        queryParams.put("channel", CHANNEL);
        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();

        body.put("solutionLeadId", solID);
        body.put("lessThan40kTurnover", false);
        body.put("exemptedFromGST", false);
        body.put("gstin", gstin);

        Response responseObject = middlewareServiceObject.onlineUpdateAddtionalDetailsGSTIndigoOnboarding(queryParams, headers, body);


        String responseBody  =responseObject.getBody().asString();
        System.out.println("Response Body is:" +responseBody);

        int statusCode=responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        retailRelatedBusinessUuid =responseObject.jsonPath().getString("retailRelatedBusinessUuid");

    }


    @Test(priority = 0,groups = {"Regression"},description = "Validate bank details")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void  TC0008_postValidateBankDetailsTest()
    {
        BANKACCOUNTNUMBER = MOBILE;
        ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);

        queryParams.put("channel", CHANNEL);
        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;
        Map<String, Object> body = new HashMap<String, Object>();
        body.put("bankName", BANKNAME);
        body.put("bankAccountNumber", BANKACCOUNTNUMBER);
        body.put("ifsc", IFSC);
        body.put("bankAccountHolderName", "testNameMatch");
        body.put("beneficiaryName", "");

        Response responseObject = middlewareServiceObject.onlineMerchantValidateBank(queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("IFSC Code "+IFSC+" is invalid. Please enter valid IFSC to continue"));

    }
}