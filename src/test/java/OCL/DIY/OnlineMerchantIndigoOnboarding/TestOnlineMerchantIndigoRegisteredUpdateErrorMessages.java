package OCL.DIY.OnlineMerchantIndigoOnboarding;

import Request.oAuth.oAuthWormhole.CreateUser;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

	
	public class TestOnlineMerchantIndigoRegisteredUpdateErrorMessages extends BaseMethod {
		
		String sessionToken = "";
		String leadId = "";
		String custId = "";
		String agentNumber = "";
		String agentPassword = "paytm@123";
		String token = "";
		String uuid = "";
		String ckycStage = "";
		String loanOffered = "";
		String maxLoanAmount = "";
		String authorisedMonthlyLimit = "";
		String stage = "";
		String code = "";
		String tncName = "";
		String url = "";
		String uniqueIdentifier = "";
		String md5 = "";
		String codeSanctionLetter = "";
		String tncNameSanctionLetter = "";
		String urlSanctionLetter = "";
		String uniqueIdentifierSanctionLetter = "";
		String md5SanctionLetter = "";
		
		
		public static String bankaccountholderName;
		public static String bankdetailsUuid;
		public static boolean namematchStatus = true;
		public static final String leadID = "";
		public static String solID;
		public static String CustId= "";
		public static String MID= "";
		public static String UUID= "";
		public static final String SOLUTION = "online_merchant";
		public static final String SOLUTION_TYPE_LEVEL_2 = "CLIX";
		public static final String SOLUTION_TYPE_LEVEL_3 = "Unsecured_Short_term_Loan_Simplified";
		public static final String ENTITY_TYPE = "INDIVIDUAL";
		public static final String CHANNEL = "UMP_WEB";
		public static String PAN = "";
		public static String citizenship = "INDIA";
		public static final String DOB = "1989-04-21";
		public static final String EMAIL = "";
		public static final String ISSUER = "OE";
		public static final String CLIENT_ID = "LMS";
		public static final String WORKFLOW_VERSION = "V2";
		public static  String MOBILE = "";
		public static  String BUSINESSPAN="";
		public static  String EntityTypeUL = "PUBLIC_LIMITED";
		public static final String BUSINESSNAME="RONIT ARYA";
		public static final String CATEGORY="BFSI";
		public static final String SUBCATEGORY="Loans";
		public static final String BANKNAME="ICICI Bank";
		public static String BANKACCOUNTNUMBER="************";
		public static final String IFSC="Icic0006622";
		public static final boolean indigoDisabled = false;
		 public	static String regPan;
		 public static boolean lessThan40kTurnover = false;
		 public static boolean exemptedFromGST = false;
		 public static final String UNREG_GSTIN = "";
		 public static final String REG_GSTIN = "";
		 public static String gstin = "";
		 public static String retailRelatedBusinessUuid = "";
		 public  static String adhaarHash = "";
		 public static final String nameAsPerAdhaar = "TOUCH WOOD LIMITED";
		 public static final boolean adhaarVerified = true;
		 public static final String shopRelatedBusinessUuid = "";
		 public static String actionType="UPDATE";
		 public static String ownerType= "APPLICANT";
		 
		 
		private static final Logger LOGGER = LogManager.getLogger(TestOnlineMerchantV1IndigoProprietor.class);
		private static final Utilities UtilObj = null;
		
		Map<String, String> commonHeaders;
		
		@BeforeClass()
		public void intitializeInputData() throws IOException {

			Utilities accObj = new Utilities();
			MOBILE = accObj.randomMobileNumberGenerator();
			LOGGER.info("New Number is : " + MOBILE);
			adhaarHash = accObj.randomMobileNumberGenerator();
			LOGGER.info("New Number is : " + adhaarHash);
			//LOGGER.info(" Before Suite Method for Agent Login ");
			CreateUser OauthObj = new CreateUser();
			OauthObj.setHeader("Content-Type","application/json");
			OauthObj.getProperties().setProperty("mobile",MOBILE);
			OauthObj.getProperties().setProperty("loginPassword","paytm@123");
			Response OauthResp =  OauthObj.callAPI();

			sessionToken = ApplicantToken(MOBILE, agentPassword);
			//LOGGER.info("Applicant Token for Lending : " + sessionToken);
			commonHeaders = setcommonHeaders();

			switch (EntityTypeUL) {
				case "PUBLIC_LIMITED":
				case "PRIVATE_LIMITED": {
					LOGGER.info("Entity is : " + EntityTypeUL);
					Utilities UtilObj = new Utilities();
					PAN = UtilObj.randomPublicPANValueGenerator();
				  regPan = PAN;
					LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
					
					gstin = "07"+PAN+"1Z1"; 
					
					
					
					
					break;
				}
				case "PROPRIETORSHIP": {
					LOGGER.info("Entity is : " + EntityTypeUL);
					Utilities UtilObj = new Utilities();
					PAN = UtilObj.randomIndividualPANValueGenerator();
					LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
					
					gstin = "09"+PAN+"1Z1"; 
					
					//UNREG_GSTIN = "09"+PAN+"1Z1";

					break;
				}
				case "TRUST": {
					LOGGER.info("Entity is : " + EntityTypeUL);
					Utilities UtilObj = new Utilities();
					PAN = UtilObj.randomTrustPANValueGenerator();
					LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
					gstin = "09"+PAN+"1Z1"; 

					break;
				}
				case "SOCIETY_ASSOCIATION_CLUB": {
					LOGGER.info("Entity is : " + EntityTypeUL);
					Utilities UtilObj = new Utilities();
					PAN = UtilObj.randomSocietyPANValueGenerator();
					LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
					gstin = "09"+PAN+"1Z1"; 

					break;
				}
				case "PARTNERSHIP": {
					LOGGER.info("Entity is : " + EntityTypeUL);
					Utilities UtilObj = new Utilities();
					PAN = UtilObj.randomPartnershipPANValueGenerator();
					LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
					gstin = "09"+PAN+"1Z1"; 

					break;
				}
				case "HINDU_UNDIVIDED_FAMILY": {
					LOGGER.info("Entity is : " + EntityTypeUL);
					Utilities UtilObj = new Utilities();
					PAN = UtilObj.randomHUFPANValueGenerator();
					LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
					gstin = "09"+PAN+"1Z1"; 

					break;
				}
			}

		}
		
		

		private Map<String, String> setcommonHeaders() {
		

			Map<String, String> headers = new HashMap<String, String>();
			headers.put("session_token", sessionToken);
			headers.put("Content-Type", "application/json");
			headers.put("version", "7.3.0");
			headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

			return headers;
		}

		MiddlewareServices middlewareServiceObject =new MiddlewareServices();
		
		
		@Test(priority = 0,groups = {"Regression"},description = "Create Registered Lead")
	    @Owner(emailId = "<EMAIL>",isAutomated = true)	
	    public void TC0001_CreateLead() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			
			queryParams.put("solution", SOLUTION);
		    queryParams.put("entityType", EntityTypeUL);
			queryParams.put("channel", CHANNEL);
			queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("businessPan", regPan);
			body.put("businessName", BUSINESSNAME);
			body.put("leadId", "");
			body.put("kybBusinessId", "");
			body.put("solutionLeadId", "");
			body.put("indigoDisabled", indigoDisabled);
			
			Response responseObject = middlewareServiceObject.onlinecreateLeadIndigo(queryParams, headers, body);
			
			String responseBody=responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			
			solID =responseObject.jsonPath().getString("solutionLeadId");
			EntityTypeUL =responseObject.jsonPath().getString("entityType");
			
			// Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 200);
			
			}
		
		
		@Test(priority = 0,groups = {"Regression"},description = "Create Registered Lead")
	    @Owner(emailId = "<EMAIL>",isAutomated = true)	
	    public void TC0002_CreateLead() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			
			queryParams.put("solution", SOLUTION);
		    queryParams.put("entityType", "PUBLIC_LIMITED");
			queryParams.put("channel", CHANNEL);
			queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("businessPan", regPan);
			body.put("businessName", BUSINESSNAME);
			body.put("leadId", "");
			body.put("kybBusinessId", "");
			body.put("solutionLeadId", "");
			body.put("indigoDisabled", indigoDisabled);
			
			Response responseObject = middlewareServiceObject.onlinecreateLeadIndigo(queryParams, headers, body);
			
			String responseBody=responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			
			solID =responseObject.jsonPath().getString("solutionLeadId");
			EntityTypeUL =responseObject.jsonPath().getString("entityType");
			
			// Validation
			
			
			String newMobile=MOBILE.substring(MOBILE.length()-4);
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400);
			 Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Application already exists with Mobile No. XXXXXX" + newMobile + ". Please continue with the same or login with a new account.");

			    Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"), "Lead is already present.");

			
		}
			}
		
		    	

		


