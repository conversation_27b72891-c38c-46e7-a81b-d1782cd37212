package OCL.DIY.UpgradeMidNegative;

import Request.MerchantService.v1.upgradeMid.lead.ValidateBankDetailsOnline;
import Request.oAuth.oAuthWormhole.CreateUser;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class TestUpgradeMidNegativeV1 extends BaseMethod {
	
	String sessionToken = "";
	String sessionToken1= "";
	String leadId = "";
	String custId = "";
	String agentNumber = "";
	String agentPassword = "paytm@123";
	String token = "";
	String uuid = "";
	String ckycStage = "";
	String loanOffered = "";
	String maxLoanAmount = "";
	String authorisedMonthlyLimit = "";
	String stage = "";
	String code = "";
	String tncName = "";
	String url = "";
	String uniqueIdentifier = "";
	String md5 = "";
	String codeSanctionLetter = "";
	String tncNameSanctionLetter = "";
	String urlSanctionLetter = "";
	String uniqueIdentifierSanctionLetter = "";
	String md5SanctionLetter = "";
	
	public static String bankaccountholderName;
	public static String bankdetailsUuid;
	public static boolean namematchStatus = true;
	public static String leadID;
	public static String solID;
	
	public static final String SOLUTION = "upgrade_mid";
	public static final String SOLUTION_TYPE_LEVEL_2 = "CLIX";
	public static final String SOLUTION_TYPE_LEVEL_3 = "Unsecured_Short_term_Loan_Simplified";
	public static final String ENTITY_TYPE = "PUBLIC_LIMITED";
	public static final String CHANNEL = "UMP_WEB";
	public static String PAN = "";
	public static final String DOB = "1989-04-21";
	public static final String EMAIL = "";
	public static final String ISSUER = "OE";
	public static final String CLIENT_ID = "LMS";
	public static final String WORKFLOW_VERSION = "V2";
	public static  String MOBILE = "";
	public static String MOBILE1 = ""; //for existing pan case
	public static  String BUSINESSPAN="";
	public static  String EntityTypeUL = "PUBLIC_LIMITED";
	public static final String BUSINESSNAME="TestBeneficiary";
	public static final String CATEGORY="BFSI";
	public static final String SUBCATEGORY="Loans";
	public static final String BANKNAME="ICICI Bank";
	public static String BANKACCOUNTNUMBER="************";
	public static final String IFSC="Icic0006622";
	private static final Logger LOGGER = LogManager.getLogger(TestUpgradeMidNegativeV1.class);
	
	Map<String, String> commonHeaders;
	Map<String, String> commonHeaders1;
	
	@BeforeClass()
	public void intitializeInputData() throws IOException {

		Utilities accObj = new Utilities();
		MOBILE = accObj.randomMobileNumberGenerator();
		LOGGER.info("New Number is : " + MOBILE);
		//LOGGER.info(" Before Suite Method for Agent Login ");
		CreateUser OauthObj = new CreateUser();
		OauthObj.setHeader("Content-Type","application/json");
		OauthObj.getProperties().setProperty("mobile",MOBILE);
		OauthObj.getProperties().setProperty("loginPassword","paytm@123");
		Response OauthResp =  OauthObj.callAPI();

		sessionToken = ApplicantToken(MOBILE, agentPassword);
		sessionToken1="a1fa9232-6bbd-4483-b31a-fd683ea26500";
		//LOGGER.info("Applicant Token for Lending : " + sessionToken);
		commonHeaders = setcommonHeaders();

		switch (EntityTypeUL) {
			case "PUBLIC_LIMITED":
			case "PRIVATE_LIMITED": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomPublicPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
			case "PROPRIETORSHIP": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomIndividualPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
			case "TRUST": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomTrustPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
			case "SOCIETY_ASSOCIATION_CLUB": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomSocietyPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
			case "PARTNERSHIP": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomPartnershipPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
			case "HINDU_UNDIVIDED_FAMILY": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomHUFPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
		}

	}
	//vishav:-For Different pan case
	 Utilities UtilObj = new Utilities();
     String PAN1 = UtilObj.randomIndividualPANValueGenerator();
    


	private Map<String, String> setcommonHeaders() {
	

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json");
		headers.put("version", "7.3.0");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

		return headers;
	}
	
	private Map<String, String> setcommonHeaders1() {
		

		Map<String, String> headers1 = new HashMap<String, String>();
		headers1.put("session_token", sessionToken1);
		headers1.put("Content-Type", "application/json");
		headers1.put("version", "7.3.0");
		headers1.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

		return headers1;
	}


	MiddlewareServices middlewareServiceObject =new MiddlewareServices();
	
	@Test(priority = 1,groups = {"Regression"},description = "Create upgrade mid lead")
//  @Owner(emailId = "<EMAIL>",isAutomated = true)
  public void TC0001_differentPAN_entity() {
		//entity type is proprietorship but pan is for public/private limited
		
		Map<String, String> queryParams = new HashMap<String, String>();
		
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	
		//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("channel", CHANNEL);
		//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		queryParams.put("mobile", MOBILE);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		
		body.put("businessPan", PAN1);
		body.put("businessName", BUSINESSNAME);
		body.put("leadId", "");
		body.put("kybBusinessId", "");
		body.put("solutionLeadId", "");
		
		Response responseObject = middlewareServiceObject.onlinecreateLead(queryParams, headers, body);
		
	// Status Code Validation	
		//middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);
		
		String responseBody  =responseObject.getBody().asString();
		System.out.println("Response Body is:" +responseBody);
		
		leadID =responseObject.jsonPath().getString("leadId");
		solID =responseObject.jsonPath().getString("solutionLeadId");
		
		// Status Code Validation
		int statusCode=responseObject.getStatusCode();
		Assert.assertEquals(statusCode, 400);
		
		
	//Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
  Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Please enter a valid PAN");
   //middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);
   
  
		
		}
	
	
	@Test(priority = 10,groups = {"Regression"},description = "Create upgrade mid lead")
	  //  @Owner(emailId = "<EMAIL>",isAutomated = true)
	    public void TC0002_postCreateLead() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("businessPan", PAN);
			body.put("businessName", BUSINESSNAME);
			body.put("leadId", "");
			body.put("kybBusinessId", "");
			body.put("solutionLeadId", "");
			
			Response responseObject = middlewareServiceObject.onlinecreateLead(queryParams, headers, body);
			
		// Status Code Validation	
			middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);
			
			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			leadID =responseObject.jsonPath().getString("leadId");
			solID =responseObject.jsonPath().getString("solutionLeadId");
			
			// Status Code Validation
			//int statusCode=responseObject.getStatusCode();
			//Assert.assertEquals(statusCode, 400);
			
			
	
	     
	    
			
			}
	//Today vishu
	@Test(priority = 2,groups = {"Regression"},description = "Create upgrade mid lead")
	  //  @Owner(emailId = "<EMAIL>",isAutomated = true)
	
	 public void TC0003_no_businessName(){
			
			Map<String, String> queryParams = new HashMap<String, String>();
			
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("businessPan", PAN);
			body.put("leadId", "");
			body.put("kybBusinessId", "");
			body.put("solutionLeadId", "");
			
			Response responseObject = middlewareServiceObject.onlinecreateLead(queryParams, headers, body);
			

			
			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			leadID =responseObject.jsonPath().getString("leadId");
			solID =responseObject.jsonPath().getString("solutionLeadId");
			
			// Status Code Validation
			//int statusCode=responseObject.getStatusCode();
			//Assert.assertEquals(responseObject.getStatusCode(), 400);
			//or
			
			int statusCode=responseObject.getStatusCode();
			if(statusCode==400)
			{
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please enter valid Business Name and try again.");
			}
		}
	
	/* @Test(priority = 11,groups = {"Regression"},description = "Create upgrade mid lead")
	  //  @Owner(emailId = "<EMAIL>",isAutomated = true)
	
	 public void TC0004_ExistingPan(){
		
		Utilities accObj = new Utilities();
		MOBILE1 = accObj.randomMobileNumberGenerator();
		
		String sessionToken2 = ApplicantToken(MOBILE1, agentPassword);
			
			Map<String, String> queryParams = new HashMap<String, String>();
			
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			queryParams.put("mobile", MOBILE1);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("businessPan", "**********");
			body.put("businessName", BUSINESSNAME);
			body.put("leadId", "");
			body.put("kybBusinessId", "");
			body.put("solutionLeadId", "");
			
			Response responseObject = middlewareServiceObject.onlinecreateLead(queryParams, headers, body);
			

			
			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			leadID =responseObject.jsonPath().getString("leadId");
			solID =responseObject.jsonPath().getString("solutionLeadId");
			
			// Status Code Validation
			//int statusCode=responseObject.getStatusCode();
			//Assert.assertEquals(responseObject.getStatusCode(), 400);
			//or
			
			int statusCode=responseObject.getStatusCode();
			//if(statusCode==500)
			//{
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Lead already exist with same PAN, please continue with same.");
			//}
		}   */
	
	
	//@Test(priority = 10,groups = {"Regression"},description = "Create upgrade mid lead")
	  //  @Owner(emailId = "<EMAIL>",isAutomated = true)
	
	  public void TC0005_ExistingMobile(){
			
			Map<String, String> queryParams = new HashMap<String, String>();
			
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
		    headers = commonHeaders;
		 
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("businessPan", PAN);
			body.put("businessName", BUSINESSNAME);
			body.put("leadId", "");
			body.put("kybBusinessId", "");
			body.put("solutionLeadId", "");
			
			Response responseObject = middlewareServiceObject.onlinecreateLead(queryParams, headers, body);
			

			
			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			leadID =responseObject.jsonPath().getString("leadId");
			solID =responseObject.jsonPath().getString("solutionLeadId");
		
			
			int statusCode=responseObject.getStatusCode();
			//if(statusCode==500)
			//{
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead is already present.");
			//}
		}  
		
	
	
	@Test(priority = 4,groups = {"Regression"},description = "Create upgrade mid lead")
	  //  @Owner(emailId = "<EMAIL>",isAutomated = true)
	    public void TC0006_negative_wrong_entityType() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", "vv");
		
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("businessPan", PAN);
			body.put("businessName", BUSINESSNAME);
			body.put("leadId", "");
			body.put("kybBusinessId", "");
			body.put("solutionLeadId", "");
			
			Response responseObject = middlewareServiceObject.onlinecreateLead(queryParams, headers, body);
			
			
			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			leadID =responseObject.jsonPath().getString("leadId");
			solID =responseObject.jsonPath().getString("solutionLeadId");
			
			// Status Code Validation
			//int statusCode=responseObject.getStatusCode();
			//Assert.assertEquals(statusCode, 500);
			
			int statusCode=responseObject.getStatusCode();
			if(statusCode==500)
			{
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again.");
			}
				
			}
	
	
	
	@Test(priority = 6,groups = {"Regression"},description = "Create upgrade mid lead")
	  //  @Owner(emailId = "<EMAIL>",isAutomated = true)
	    public void TC0007_negative_no_entityType() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", "");
		
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("businessPan", PAN);
			body.put("businessName", BUSINESSNAME);
			body.put("leadId", "");
			body.put("kybBusinessId", "");
			body.put("solutionLeadId", "");
			
			Response responseObject = middlewareServiceObject.onlinecreateLead(queryParams, headers, body);
			
			
			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			leadID =responseObject.jsonPath().getString("leadId");
			solID =responseObject.jsonPath().getString("solutionLeadId");
			
			// Status Code Validation
			//int statusCode=responseObject.getStatusCode();
			//Assert.assertEquals(statusCode, 500);
			
			int statusCode=responseObject.getStatusCode();
			if(statusCode==500)
			{
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again.");
			}
				
			}
	
	@Test(priority = 7,groups = {"Regression"},description = "Create upgrade mid lead")
	  //  @Owner(emailId = "<EMAIL>",isAutomated = true)
	    public void TC0008_negative_no_channel() {
		//should work fine if channel is not passed
			
			Map<String, String> queryParams = new HashMap<String, String>();
			
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", "");
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("businessPan", PAN);
			body.put("businessName", BUSINESSNAME);
			body.put("leadId", "");
			body.put("kybBusinessId", "");
			body.put("solutionLeadId", "");
			
			Response responseObject = middlewareServiceObject.onlinecreateLead(queryParams, headers, body);
			
			
			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			leadID =responseObject.jsonPath().getString("leadId");
			solID =responseObject.jsonPath().getString("solutionLeadId");
			
			// Status Code Validation
			//int statusCode=responseObject.getStatusCode();
			//Assert.assertEquals(statusCode, 500);
			
			int statusCode=responseObject.getStatusCode();
			if(statusCode==500)
			{
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again.");
			}
				
			}
	
	@Test(priority = 8,groups = {"Regression"},description = "Create upgrade mid lead")
	  //  @Owner(emailId = "<EMAIL>",isAutomated = true)
	    public void TC0009_negative_no_mobile() {
		//should work fine if channel is not passed
			
			Map<String, String> queryParams = new HashMap<String, String>();
			
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			queryParams.put("mobile", "");
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("businessPan", PAN);
			body.put("businessName", BUSINESSNAME);
			body.put("leadId", "");
			body.put("kybBusinessId", "");
			body.put("solutionLeadId", "");
			
			Response responseObject = middlewareServiceObject.onlinecreateLead(queryParams, headers, body);
			
			
			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			leadID =responseObject.jsonPath().getString("leadId");
			solID =responseObject.jsonPath().getString("solutionLeadId");
			
			int statusCode=responseObject.getStatusCode();
			if(statusCode==500)
			{
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Request Validation failed for create lead request");
			}
				
			}
	
	
	
	//update business API
	
	
	@Test(priority = 18,groups = {"Regression"},description = "Update Business for upgrade mid lead")
  //  @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0010_positiveUpdateBusiness() {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	    queryParams.put("solutionLeadId", solID);
		//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("channel", CHANNEL);
		//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		//queryParams.put("mobile", MOBILE);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("name", BUSINESSNAME);
		body.put("category", CATEGORY);
		body.put("subCategory", SUBCATEGORY);
	
		
		Response responseObject = middlewareServiceObject.onlineUpdateBusiness(queryParams, headers, body);
		
	// Status Code Validation	
		//middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);
		
		String responseBody  =responseObject.getBody().asString();
		System.out.println("Response Body is:" +responseBody);
		
		// Status Code Validation
		int statusCode=responseObject.getStatusCode();
		Assert.assertEquals(statusCode, 204);
		
	// Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
     //  Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead is already present.");

		
		}
	
	
	@Test(priority = 16,groups = {"Regression"},description = "Update Business for upgrade mid lead")
    //@Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0011_no_solutionId() {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	    queryParams.put("solutionLeadId", "");
		//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("channel", CHANNEL);
		//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		//queryParams.put("mobile", MOBILE);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("name", BUSINESSNAME);
		body.put("category", CATEGORY);
		body.put("subCategory", SUBCATEGORY);
	
		
		Response responseObject = middlewareServiceObject.onlineUpdateBusiness(queryParams, headers, body);
		
	// Status Code Validation	
		//middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);
		
		String responseBody  =responseObject.getBody().asString();
		System.out.println("Response Body is:" +responseBody);
		
		// Status Code Validation
		int statusCode=responseObject.getStatusCode();
		Assert.assertEquals(statusCode, 204);
		
		
		}
	
	
	@Test(priority = 13,groups = {"Regression"},description = "Update Business for upgrade mid lead")
  //  @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0012_no_category() {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	    queryParams.put("solutionLeadId", "solID");
		//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("channel", CHANNEL);
		//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		//queryParams.put("mobile", MOBILE);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("name", BUSINESSNAME);
		body.put("category", "");
		body.put("subCategory", SUBCATEGORY);
	
		
		Response responseObject = middlewareServiceObject.onlineUpdateBusiness(queryParams, headers, body);
		

		
		String responseBody  =responseObject.getBody().asString();
		System.out.println("Response Body is:" +responseBody);
		
		// Status Code Validation
		int statusCode=responseObject.getStatusCode();
		Assert.assertEquals(statusCode, 400);
		
		
		}
	
	@Test(priority = 14,groups = {"Regression"},description = "Update Business for upgrade mid lead")
  //  @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0013_no_Subcategory() {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	    queryParams.put("solutionLeadId", "solID");
		//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("channel", CHANNEL);
		//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		//queryParams.put("mobile", MOBILE);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("name", BUSINESSNAME);
		body.put("category", CATEGORY);
		body.put("subCategory", "");
	
		
		Response responseObject = middlewareServiceObject.onlineUpdateBusiness(queryParams, headers, body);
		

		
		String responseBody  =responseObject.getBody().asString();
		System.out.println("Response Body is:" +responseBody);
		
		// Status Code Validation
		int statusCode=responseObject.getStatusCode();
		Assert.assertEquals(statusCode, 400);
		
		
		}
	
	 /* @Test(priority = 12,groups = {"Regression"},description = "Update Business for upgrade mid lead")
   // @Owner(emailId = "<EMAIL>",isAutomated = true)
	
	 public void TC0014_no_channel() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		    queryParams.put("solutionLeadId", "solID");
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", "Vishav");
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("name", BUSINESSNAME);
			body.put("category", CATEGORY);
			body.put("subCategory", SUBCATEGORY);
		
			
			Response responseObject = middlewareServiceObject.onlineUpdateBusiness(queryParams, headers, body);
			

			
			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 204);
			
			
			}  */
	 
	 
		
	 
	// update additional details API
	
	
	/*@Test(priority = 19,groups = {"Regression"},description = "Update Business for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0015_missing_registered_address() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400); 
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Registered Address is empty in request");
			
				
		}*/
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0015_missing_Email() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
		
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400); 
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please enter valid Email ID and try again.");
				
		}
	
	/*@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0016_missing_businessProofNotRequired() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 200); 
				
		}*/ 
	//This is giving 200 on postman but here 400 why??
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0016_passing_businessProofNotRequired_as_true() {
		
		//  "businessProofNotRequired":"true" is only possible for proprietorship as per response in postman//
		// But here we are using public_limited therefore it should give error
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "true");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400); 
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Business Proof Document is optional only for Proprietors company type");
				
		}
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0017_Invalid_Pincode_in_registeredAddress() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 1100);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400); 
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid pincode");
				
		}
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0018_Invalid_pincode_in_correspondenceAddress() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400); 
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid pincode");
				
		}
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0019_invalid_pincode_in_both_addresses() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400); 
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid pincode");
				
		}
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0020_invalid_email() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "8000000002@yopm");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 200); 
				
		}
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0021_passing_empty_email() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400); 
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please enter valid Email ID and try again.");
				
		}
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0022_missing_displayName() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Display Name is Mandatory");
				
		}
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0023_missing_city() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		  
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400); 
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "City is null or empty");
				
		}
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0024_missing_country() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
		
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 200); 
		
				
		}
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0025_missing_address_line1_correspondence() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400); 
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "shopAddress is null or empty");
				
		}
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0026_missing_address_line2_correspondence() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
		
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 200); 
				
		}
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0027_missingAddress_line3_correspondence() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
		
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400);
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "areaOfEnrollment is null or empty");
				
		}
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0028_missing_address_line1_registered() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
		
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400);
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "shopAddress is null or empty"); 
				
		}
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0029_missing_address_line2_registered() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
		
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 200); 
				
		}
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0030_missing_address_line3_registered() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
		
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400); 
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "areaOfEnrollment is null or empty"); 
				
		}
	
	
	
	
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0031_missing_sol_id() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");

			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 200); 
				
		}
	
	
	
	
	 
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0032_postUpdateAdditionalDetails1() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 200); 
				
		}
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0033_pasing_appURL() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");
			body.put("appUrl","https://callbackUrltrue.com");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400); 
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Upgrade mid doesnot support online flows."); 
				
		}
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0034_pasing_webURL() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");
			body.put("websiteUrl","https://callbackUrltrue.com");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400); 
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Upgrade mid doesnot support online flows."); 
				
		}
	
	@Test(priority = 19,groups = {"Regression"},description = "Update additional details for upgrade mid lead")
	 //   @Owner(emailId = "<EMAIL>",isAutomated = true)
		

	    public void TC0035_pasing_Both_web_appURL() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		  //  queryParams.put("solutionLeadId", solID);
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("addressSubType", "CORRESPONDENCE");
			body.put("addressType", "Office Purp");
			body.put("addressUuid", "UIDADD");
		    body.put("city", "Noida");
			body.put("country", "India");
			body.put("displayMessage", "VICTORY");
			body.put("landmark", "Near Sash Building");
			body.put("latitude", 28.09);
			body.put("line1", "F 9201");
			body.put("line2", "Sector 26");
			body.put("line3", "NAveen Okhla");
			body.put("longitude", 27.19);
			body.put("pincode", 201301);
			body.put("refId", "BuckMouth");
			body.put("residentialStatus", "Redi");
		
			body.put("state", "Uttar Pradesh");
			body.put("statusCode", 0);
			body.put("title", "Haryana shop");
			
			body.put("displayName", "Concer Disp");
			body.put("gstinExemptedCategory", "");
			body.put("leadId", "");

		
			body.put("addressSubType1", "Registered");
			body.put("addressType1", "My Commerce");
			body.put("addressUuid1", "RDXROID");
		    body.put("city1", "Central Delhi");
			body.put("country1", "India");
			body.put("displayMessage1", "NItyaro");
			body.put("landmark1", "Near Sash Building");
			body.put("latitude1", 22.7);
			body.put("line11", "Denmar");
			body.put("line21", "MINE");
			body.put("line31", "LINE3 For Add");
			body.put("longitude1", 28.6);
			body.put("pincode1", 110011);
			body.put("refId1", "FEDA");
			body.put("residentialStatus1", "True");
		
			body.put("state1", "Delhi");
			body.put("statusCode1", 0);
			body.put("title1", "NASDH");
			
			body.put("retailStore", "false");
			body.put("shopRelatedBusinessUuid", "string");
			body.put("email", "<EMAIL>");
			body.put("solutionLeadId", solID);
			body.put("businessProofNotRequired", "false");
			body.put("appUrl","https://callbackUrltrue.com");
			body.put("websiteUrl","https://callbackUrltrue.com");



			Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
			

			String responseBody  =responseObject.getBody().asString();
			System.out.println("Response Body is:" +responseBody);
			
			// Status Code Validation
			int statusCode=responseObject.getStatusCode();
			Assert.assertEquals(statusCode, 400); 
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Upgrade mid doesnot support online flows."); 
				
		}
	
	
	// >------Validate bank details-------<
	
	@Test(priority = 19,groups = {"Regression"},description = "Validate bank details for upgrade mid lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0036_missing_EntityType()
	    {
			BANKACCOUNTNUMBER = MOBILE;
			ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();

		  Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", "");
		   // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("bankName", BANKNAME);
			body.put("bankAccountNumber", BANKACCOUNTNUMBER);
			body.put("ifsc", IFSC);
		    body.put("bankAccountHolderName", "");
		    body.put("beneficiaryName", "");
		
	
		  
	        Response responseObject = middlewareServiceObject.onlineMerchantValidateBank(queryParams, headers, body);
	        
	       validateBankDetailsResponseObject.setRefId(responseObject.jsonPath().getString("refId"));
	        validateBankDetailsResponseObject.setStatusCode(responseObject.jsonPath().getString("statusCode"));
	      validateBankDetailsResponseObject.setDisplayMessage(responseObject.jsonPath().getString("displayMessage"));
	       validateBankDetailsResponseObject.setBankAccountHolderName(responseObject.jsonPath().getString("bankAccountHolderName"));
	      validateBankDetailsResponseObject.setBankDetailsUuid(responseObject.jsonPath().getString("bankDetailsUuid"));
	      validateBankDetailsResponseObject.setNameMatchStatus(responseObject.jsonPath().getString("nameMatchStatus"));
	        int statusCode = responseObject.getStatusCode();
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	        Assert.assertEquals(statusCode, 500);
	        
	        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again."); 
	        
	   
	        
	        bankaccountholderName =responseObject.jsonPath().getString("bankAccountHolderName");
	        bankdetailsUuid =responseObject.jsonPath().getString("bankDetailsUuid");
	       // namematchStatus =responseObject.jsonPath().getChar("nameMatchStatus");
			
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	    }
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Validate bank details for upgrade mid lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0037_wrong_entityType()
	    // wrong entity type w.r.t one which was used while creating lead
	    {
			BANKACCOUNTNUMBER = MOBILE;
			ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();

		  Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", "PROPRIETORSHIP");
		   // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("bankName", BANKNAME);
			body.put("bankAccountNumber", BANKACCOUNTNUMBER);
			body.put("ifsc", IFSC);
		    body.put("bankAccountHolderName", "");
		    body.put("beneficiaryName", "");
		
	
		  
	        Response responseObject = middlewareServiceObject.onlineMerchantValidateBank(queryParams, headers, body);
	        
	       validateBankDetailsResponseObject.setRefId(responseObject.jsonPath().getString("refId"));
	        validateBankDetailsResponseObject.setStatusCode(responseObject.jsonPath().getString("statusCode"));
	      validateBankDetailsResponseObject.setDisplayMessage(responseObject.jsonPath().getString("displayMessage"));
	       validateBankDetailsResponseObject.setBankAccountHolderName(responseObject.jsonPath().getString("bankAccountHolderName"));
	      validateBankDetailsResponseObject.setBankDetailsUuid(responseObject.jsonPath().getString("bankDetailsUuid"));
	      validateBankDetailsResponseObject.setNameMatchStatus(responseObject.jsonPath().getString("nameMatchStatus"));
	        int statusCode = responseObject.getStatusCode();
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	        Assert.assertEquals(statusCode, 404);
	        
	        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again."); 
	   
	        
	        bankaccountholderName =responseObject.jsonPath().getString("bankAccountHolderName");
	        bankdetailsUuid =responseObject.jsonPath().getString("bankDetailsUuid");
	       // namematchStatus =responseObject.jsonPath().getChar("nameMatchStatus");
			
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	    }
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Validate bank details for upgrade mid lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0038_existing_accountNo()
	    {
			BANKACCOUNTNUMBER = MOBILE;
			ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();

		  Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		   // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("bankName", BANKNAME);
			body.put("bankAccountNumber", "************");
			body.put("ifsc", IFSC);
		    body.put("bankAccountHolderName", "");
		    body.put("beneficiaryName", "");
		
	
		  
	        Response responseObject = middlewareServiceObject.onlineMerchantValidateBank(queryParams, headers, body);
	        
	       validateBankDetailsResponseObject.setRefId(responseObject.jsonPath().getString("refId"));
	        validateBankDetailsResponseObject.setStatusCode(responseObject.jsonPath().getString("statusCode"));
	      validateBankDetailsResponseObject.setDisplayMessage(responseObject.jsonPath().getString("displayMessage"));
	       validateBankDetailsResponseObject.setBankAccountHolderName(responseObject.jsonPath().getString("bankAccountHolderName"));
	      validateBankDetailsResponseObject.setBankDetailsUuid(responseObject.jsonPath().getString("bankDetailsUuid"));
	      validateBankDetailsResponseObject.setNameMatchStatus(responseObject.jsonPath().getString("nameMatchStatus"));
	        int statusCode = responseObject.getStatusCode();
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	        Assert.assertEquals(statusCode, 400);
	        
	      //  Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "The Bank you have entered is already associated with another mobile number ending with XXXXXX665 Please use / upgrade the old account OR provide a new bank details"); 
	        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"This bank account is already registered with us. Please enter different bank details.");     
	   
	        
	        bankaccountholderName =responseObject.jsonPath().getString("bankAccountHolderName");
	        bankdetailsUuid =responseObject.jsonPath().getString("bankDetailsUuid");
	       // namematchStatus =responseObject.jsonPath().getChar("nameMatchStatus");
			
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	    }
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Validate bank details for upgrade mid lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0039_Invalid_IFSC_Code()
	    {
			BANKACCOUNTNUMBER = MOBILE;
			ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();

		  Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		   // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("bankName", BANKNAME);
			body.put("bankAccountNumber", BANKACCOUNTNUMBER);
			body.put("ifsc", "Icic000662");
		    body.put("bankAccountHolderName", "");
		    body.put("beneficiaryName", "");
		
	
		  
	        Response responseObject = middlewareServiceObject.onlineMerchantValidateBank(queryParams, headers, body);
	        
	       validateBankDetailsResponseObject.setRefId(responseObject.jsonPath().getString("refId"));
	        validateBankDetailsResponseObject.setStatusCode(responseObject.jsonPath().getString("statusCode"));
	      validateBankDetailsResponseObject.setDisplayMessage(responseObject.jsonPath().getString("displayMessage"));
	       validateBankDetailsResponseObject.setBankAccountHolderName(responseObject.jsonPath().getString("bankAccountHolderName"));
	      validateBankDetailsResponseObject.setBankDetailsUuid(responseObject.jsonPath().getString("bankDetailsUuid"));
	      validateBankDetailsResponseObject.setNameMatchStatus(responseObject.jsonPath().getString("nameMatchStatus"));
	        int statusCode = responseObject.getStatusCode();
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	  //      Assert.assertEquals(statusCode, 500); initially 500 used to come but now 200
	        Assert.assertEquals(statusCode, 200);
	        
	     //   Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "We could not verify this bank account. Please enter different bank details to continue "); 
	        
	        
	        
	   
	        
	        bankaccountholderName =responseObject.jsonPath().getString("bankAccountHolderName");
	        bankdetailsUuid =responseObject.jsonPath().getString("bankDetailsUuid");
	       // namematchStatus =responseObject.jsonPath().getChar("nameMatchStatus");
			
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	    }
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Validate bank details for upgrade mid lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0040_missing_IFSC_Code()
	    {
			BANKACCOUNTNUMBER = MOBILE;
			ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();

		  Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		   // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("bankName", BANKNAME);
			body.put("bankAccountNumber", BANKACCOUNTNUMBER);
			body.put("ifsc", "");
		    body.put("bankAccountHolderName", "");
		    body.put("beneficiaryName", "");
		
	
		  
	        Response responseObject = middlewareServiceObject.onlineMerchantValidateBank(queryParams, headers, body);
	        
	       validateBankDetailsResponseObject.setRefId(responseObject.jsonPath().getString("refId"));
	        validateBankDetailsResponseObject.setStatusCode(responseObject.jsonPath().getString("statusCode"));
	      validateBankDetailsResponseObject.setDisplayMessage(responseObject.jsonPath().getString("displayMessage"));
	       validateBankDetailsResponseObject.setBankAccountHolderName(responseObject.jsonPath().getString("bankAccountHolderName"));
	      validateBankDetailsResponseObject.setBankDetailsUuid(responseObject.jsonPath().getString("bankDetailsUuid"));
	      validateBankDetailsResponseObject.setNameMatchStatus(responseObject.jsonPath().getString("nameMatchStatus"));
	        int statusCode = responseObject.getStatusCode();
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	        Assert.assertEquals(statusCode, 400);
	        
	        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again."); 
	        
	   
	        
	        bankaccountholderName =responseObject.jsonPath().getString("bankAccountHolderName");
	        bankdetailsUuid =responseObject.jsonPath().getString("bankDetailsUuid");
	       // namematchStatus =responseObject.jsonPath().getChar("nameMatchStatus");
			
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	    }
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Validate bank details for upgrade mid lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0041_missing_bankAccountNumber()
	    {
			BANKACCOUNTNUMBER = MOBILE;
			ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();

		  Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		   // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("bankName", BANKNAME);
			body.put("bankAccountNumber", "");
			body.put("ifsc", IFSC);
		    body.put("bankAccountHolderName", "");
		    body.put("beneficiaryName", "");
		
	
		  
	        Response responseObject = middlewareServiceObject.onlineMerchantValidateBank(queryParams, headers, body);
	        
	       validateBankDetailsResponseObject.setRefId(responseObject.jsonPath().getString("refId"));
	        validateBankDetailsResponseObject.setStatusCode(responseObject.jsonPath().getString("statusCode"));
	      validateBankDetailsResponseObject.setDisplayMessage(responseObject.jsonPath().getString("displayMessage"));
	       validateBankDetailsResponseObject.setBankAccountHolderName(responseObject.jsonPath().getString("bankAccountHolderName"));
	      validateBankDetailsResponseObject.setBankDetailsUuid(responseObject.jsonPath().getString("bankDetailsUuid"));
	      validateBankDetailsResponseObject.setNameMatchStatus(responseObject.jsonPath().getString("nameMatchStatus"));
	        int statusCode = responseObject.getStatusCode();
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	        Assert.assertEquals(statusCode, 400);
	        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again."); 
	   
	        
	        bankaccountholderName =responseObject.jsonPath().getString("bankAccountHolderName");
	        bankdetailsUuid =responseObject.jsonPath().getString("bankDetailsUuid");
	       // namematchStatus =responseObject.jsonPath().getChar("nameMatchStatus");
			
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	    }
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Validate bank details for upgrade mid lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0042_missing_beneficiaryName_in_body()
	    {
			BANKACCOUNTNUMBER = MOBILE;
			ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();

		  Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		   // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("bankName", BANKNAME);
			body.put("bankAccountNumber", BANKACCOUNTNUMBER);
			body.put("ifsc", IFSC);
		    body.put("bankAccountHolderName", "");
		    
		
	
		  
	        Response responseObject = middlewareServiceObject.onlineMerchantValidateBank(queryParams, headers, body);
	        
	       validateBankDetailsResponseObject.setRefId(responseObject.jsonPath().getString("refId"));
	        validateBankDetailsResponseObject.setStatusCode(responseObject.jsonPath().getString("statusCode"));
	      validateBankDetailsResponseObject.setDisplayMessage(responseObject.jsonPath().getString("displayMessage"));
	       validateBankDetailsResponseObject.setBankAccountHolderName(responseObject.jsonPath().getString("bankAccountHolderName"));
	      validateBankDetailsResponseObject.setBankDetailsUuid(responseObject.jsonPath().getString("bankDetailsUuid"));
	      validateBankDetailsResponseObject.setNameMatchStatus(responseObject.jsonPath().getString("nameMatchStatus"));
	        int statusCode = responseObject.getStatusCode();
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	        Assert.assertEquals(statusCode, 200);
	        
	   
	        
	        bankaccountholderName =responseObject.jsonPath().getString("bankAccountHolderName");
	        bankdetailsUuid =responseObject.jsonPath().getString("bankDetailsUuid");
	       // namematchStatus =responseObject.jsonPath().getChar("nameMatchStatus");
			
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	    }
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Validate bank details for upgrade mid lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0043_wrong_solution()
	    {
			BANKACCOUNTNUMBER = MOBILE;
			ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();

		  Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", "online_merchant");
			queryParams.put("entityType", EntityTypeUL);
		   // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("bankName", BANKNAME);
			body.put("bankAccountNumber", BANKACCOUNTNUMBER);
			body.put("ifsc", IFSC);
		    body.put("bankAccountHolderName", "");
		    body.put("beneficiaryName", "");
		
	
		  
	        Response responseObject = middlewareServiceObject.onlineMerchantValidateBank(queryParams, headers, body);
	        
	       validateBankDetailsResponseObject.setRefId(responseObject.jsonPath().getString("refId"));
	        validateBankDetailsResponseObject.setStatusCode(responseObject.jsonPath().getString("statusCode"));
	      validateBankDetailsResponseObject.setDisplayMessage(responseObject.jsonPath().getString("displayMessage"));
	       validateBankDetailsResponseObject.setBankAccountHolderName(responseObject.jsonPath().getString("bankAccountHolderName"));
	      validateBankDetailsResponseObject.setBankDetailsUuid(responseObject.jsonPath().getString("bankDetailsUuid"));
	      validateBankDetailsResponseObject.setNameMatchStatus(responseObject.jsonPath().getString("nameMatchStatus"));
	        int statusCode = responseObject.getStatusCode();
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	        Assert.assertEquals(statusCode, 404);
	        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again."); 
	        
	   
	        
	        bankaccountholderName =responseObject.jsonPath().getString("bankAccountHolderName");
	        bankdetailsUuid =responseObject.jsonPath().getString("bankDetailsUuid");
	       // namematchStatus =responseObject.jsonPath().getChar("nameMatchStatus");
			
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	    }
	
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Validate bank details for upgrade mid lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0044_postValidateBankDetailsTest()
	    {
			BANKACCOUNTNUMBER = MOBILE;
			ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();

		  Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		   // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("bankName", BANKNAME);
			body.put("bankAccountNumber", BANKACCOUNTNUMBER);
			body.put("ifsc", IFSC);
		    body.put("bankAccountHolderName", "");
		    body.put("beneficiaryName", "");
		
	
		  
	        Response responseObject = middlewareServiceObject.onlineMerchantValidateBank(queryParams, headers, body);
	        
	       validateBankDetailsResponseObject.setRefId(responseObject.jsonPath().getString("refId"));
	        validateBankDetailsResponseObject.setStatusCode(responseObject.jsonPath().getString("statusCode"));
	      validateBankDetailsResponseObject.setDisplayMessage(responseObject.jsonPath().getString("displayMessage"));
	       validateBankDetailsResponseObject.setBankAccountHolderName(responseObject.jsonPath().getString("bankAccountHolderName"));
	      validateBankDetailsResponseObject.setBankDetailsUuid(responseObject.jsonPath().getString("bankDetailsUuid"));
	      validateBankDetailsResponseObject.setNameMatchStatus(responseObject.jsonPath().getString("nameMatchStatus"));
	        int statusCode = responseObject.getStatusCode();
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	        Assert.assertEquals(statusCode, 200);
	        
	   
	        
	        bankaccountholderName =responseObject.jsonPath().getString("bankAccountHolderName");
	        bankdetailsUuid =responseObject.jsonPath().getString("bankDetailsUuid");
	       // namematchStatus =responseObject.jsonPath().getChar("nameMatchStatus");
			
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	    }
	
	

	
	//updateBankDetails API
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0045_missing_bank_acc_num()
	    {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	  
		queryParams.put("channel", CHANNEL);

		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		
		body.put("bankName", BANKNAME);
		body.put("bankAccountNumber", "");
		body.put("ifsc", IFSC);
	    body.put("bankAccountHolderName", bankaccountholderName );
	    body.put("beneficiaryName", "");
	    body.put("bankDetailsUuid", bankdetailsUuid);
	    body.put("nameMatchStatus", namematchStatus);
	    body.put("leadId", leadID);
	    body.put("SolutionLeadId", solID);
	
		
	    Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);
		
	    int statusCode = responseObject.getStatusCode();
	    Assert.assertEquals(statusCode, 400);
		
	    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again."); 

	    }
	
	@Test(priority = 19,groups = {"Regression"},description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0046_invalid_bank_acc_num()
	    {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	  
		queryParams.put("channel", CHANNEL);

		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		
		body.put("bankName", BANKNAME);
		body.put("bankAccountNumber", "*********");
		body.put("ifsc", IFSC);
	    body.put("bankAccountHolderName", bankaccountholderName );
	    body.put("beneficiaryName", "");
	    body.put("bankDetailsUuid", bankdetailsUuid);
	    body.put("nameMatchStatus", namematchStatus);
	    body.put("leadId", leadID);
	    body.put("SolutionLeadId", solID);
	
		
	    Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);
		
	    int statusCode = responseObject.getStatusCode();
	    Assert.assertEquals(statusCode, 500);
		
	    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again."); 

	    }
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0047_missing_IFSC()
	    {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	  
		queryParams.put("channel", CHANNEL);

		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		
		body.put("bankName", BANKNAME);
		body.put("bankAccountNumber", BANKACCOUNTNUMBER);
		body.put("ifsc", "");
	    body.put("bankAccountHolderName", bankaccountholderName );
	    body.put("beneficiaryName", "");
	    body.put("bankDetailsUuid", bankdetailsUuid);
	    body.put("nameMatchStatus", namematchStatus);
	    body.put("leadId", leadID);
	    body.put("SolutionLeadId", solID);
	
		
	    Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);
		
	    int statusCode = responseObject.getStatusCode();
	    Assert.assertEquals(statusCode, 400);
		
	    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again."); 

	    }
	
	@Test(priority = 19,groups = {"Regression"},description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0048_invalid_IFSC()
	    {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	  
		queryParams.put("channel", CHANNEL);

		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		
		body.put("bankName", BANKNAME);
		body.put("bankAccountNumber", BANKACCOUNTNUMBER);
		body.put("ifsc", "Ici11111111");
	    body.put("bankAccountHolderName", bankaccountholderName );
	    body.put("beneficiaryName", "");
	    body.put("bankDetailsUuid", bankdetailsUuid);
	    body.put("nameMatchStatus", namematchStatus);
	    body.put("leadId", leadID);
	    body.put("SolutionLeadId", solID);
	
		
	    Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);
		
	    int statusCode = responseObject.getStatusCode();
	    Assert.assertEquals(statusCode, 500);
		
	    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again."); 

	    }
	
	@Test(priority = 19,groups = {"Regression"},description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0049_missing_AccountHolderName()
	    {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	  
		queryParams.put("channel", CHANNEL);

		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		
		body.put("bankName", BANKNAME);
		body.put("bankAccountNumber", BANKACCOUNTNUMBER);
		body.put("ifsc", "Icic0006622");
	    body.put("bankAccountHolderName", "" );
	    body.put("beneficiaryName", "");
	    body.put("bankDetailsUuid", bankdetailsUuid);
	    body.put("nameMatchStatus", namematchStatus);
	    body.put("leadId", leadID);
	    body.put("SolutionLeadId", solID);
	
		
	    Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);
		
	    int statusCode = responseObject.getStatusCode();
	    Assert.assertEquals(statusCode, 500);
		
	    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again."); 

	    }
	
	@Test(priority = 19,groups = {"Regression"},description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0050_invalid_accountHolderName()
	    {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	  
		queryParams.put("channel", CHANNEL);

		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		
		body.put("bankName", BANKNAME);
		body.put("bankAccountNumber", BANKACCOUNTNUMBER);
		body.put("ifsc", "Icic0006622");
	    body.put("bankAccountHolderName", "Vishav" );
	    body.put("beneficiaryName", "");
	    body.put("bankDetailsUuid", bankdetailsUuid);
	    body.put("nameMatchStatus", namematchStatus);
	    body.put("leadId", leadID);
	    body.put("SolutionLeadId", solID);
	
		
	    Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);
		
	    int statusCode = responseObject.getStatusCode();
	    Assert.assertEquals(statusCode, 500);
		
	    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again."); 

	    }
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0051_missing_bankDetailsUUID()
	    {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);

		queryParams.put("channel", CHANNEL);

		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		
		body.put("bankName", BANKNAME);
		body.put("bankAccountNumber", BANKACCOUNTNUMBER);
		body.put("ifsc", IFSC);
	    body.put("bankAccountHolderName", bankaccountholderName );
	    body.put("beneficiaryName", "");
	    body.put("bankDetailsUuid", "");
	    body.put("nameMatchStatus", namematchStatus);
	    body.put("leadId", leadID);
	    body.put("SolutionLeadId", solID);
	
		
	    Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);
		
	    int statusCode = responseObject.getStatusCode();
	    Assert.assertEquals(statusCode, 500);
		
	    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again."); 
 }
	
	@Test(priority = 19,groups = {"Regression"},description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0052_invalid_bankDetailsUUID()
	    {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);

		queryParams.put("channel", CHANNEL);

		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		
		body.put("bankName", BANKNAME);
		body.put("bankAccountNumber", BANKACCOUNTNUMBER);
		body.put("ifsc", IFSC);
	    body.put("bankAccountHolderName", bankaccountholderName );
	    body.put("beneficiaryName", "");
	    body.put("bankDetailsUuid", "454433490aaaaabbbb");
	    body.put("nameMatchStatus", namematchStatus);
	    body.put("leadId", leadID);
	    body.put("SolutionLeadId", solID);
	
		
	    Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);
		
	    int statusCode = responseObject.getStatusCode();
	    Assert.assertEquals(statusCode, 500);
		
	    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again."); 

	    }
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0053_empty_nameMatchStatus()
	    {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	 
		queryParams.put("channel", CHANNEL);
	
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		
		body.put("bankName", BANKNAME);
		body.put("bankAccountNumber", BANKACCOUNTNUMBER);
		body.put("ifsc", IFSC);
	    body.put("bankAccountHolderName", bankaccountholderName );
	    body.put("beneficiaryName", "");
	    body.put("bankDetailsUuid", bankdetailsUuid);
	    body.put("nameMatchStatus", "");
	    body.put("leadId", leadID);
	    body.put("SolutionLeadId", solID);
	
		
	    Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);
		
	    int statusCode = responseObject.getStatusCode();
 Assert.assertEquals(statusCode, 400);
		
		
		}
	
	@Test(priority = 19,groups = {"Regression"},description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0054_invalid_nameMatchStatus()
	    {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	 
		queryParams.put("channel", CHANNEL);
	
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		
		body.put("bankName", BANKNAME);
		body.put("bankAccountNumber", BANKACCOUNTNUMBER);
		body.put("ifsc", IFSC);
	    body.put("bankAccountHolderName", bankaccountholderName );
	    body.put("beneficiaryName", "");
	    body.put("bankDetailsUuid", bankdetailsUuid);
	    body.put("nameMatchStatus", "vv");
	    body.put("leadId", leadID);
	    body.put("SolutionLeadId", solID);
	
		
	    Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);
		
	    int statusCode = responseObject.getStatusCode();
 Assert.assertEquals(statusCode, 400);
		
	    }
	
	@Test(priority = 19,groups = {"Regression"},description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0055_invalid_entity()
	    {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", "Vishav");
	 
		queryParams.put("channel", CHANNEL);
	
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		
		body.put("bankName", BANKNAME);
		body.put("bankAccountNumber", BANKACCOUNTNUMBER);
		body.put("ifsc", IFSC);
	    body.put("bankAccountHolderName", bankaccountholderName );
	    body.put("beneficiaryName", "");
	    body.put("bankDetailsUuid", bankdetailsUuid);
	    body.put("nameMatchStatus", namematchStatus);
	    body.put("leadId", leadID);
	    body.put("SolutionLeadId", solID);
	
		
	    Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);
		
	    int statusCode = responseObject.getStatusCode();
	    Assert.assertEquals(statusCode, 500);
	    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again."); 
		


	    }
	
	@Test(priority = 19,groups = {"Regression"},description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0056_missing_entity()
	    {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", "");
	
		queryParams.put("channel", CHANNEL);
		
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		
		body.put("bankName", BANKNAME);
		body.put("bankAccountNumber", BANKACCOUNTNUMBER);
		body.put("ifsc", IFSC);
	    body.put("bankAccountHolderName", bankaccountholderName );
	    body.put("beneficiaryName", "");
	    body.put("bankDetailsUuid", bankdetailsUuid);
	    body.put("nameMatchStatus", namematchStatus);
	    body.put("leadId", leadID);
	    body.put("SolutionLeadId", solID);
	
		
	    Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);
		
	    int statusCode = responseObject.getStatusCode();
	    Assert.assertEquals(statusCode, 500);
	    
	    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Your details could not be saved. Please refresh the page and try again."); 
		

	    }
	
	
	
	@Test(priority = 19,groups = {"Regression"},description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0057_postUpdateBankDetailsTest()
	    {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	   // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
		//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("channel", CHANNEL);
		//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		//queryParams.put("mobile", MOBILE);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		
		body.put("bankName", BANKNAME);
		body.put("bankAccountNumber", BANKACCOUNTNUMBER);
		body.put("ifsc", IFSC);
	    body.put("bankAccountHolderName", bankaccountholderName );
	    body.put("beneficiaryName", "");
	    body.put("bankDetailsUuid", bankdetailsUuid);
	    body.put("nameMatchStatus", namematchStatus);
	    body.put("leadId", leadID);
	    body.put("SolutionLeadId", solID);
	
		
	    Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);
		
	    int statusCode = responseObject.getStatusCode();
	    Assert.assertEquals(statusCode, 200);
		
		

	    }
}