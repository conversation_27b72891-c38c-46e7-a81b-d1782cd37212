package OCL.DIY.CashAtPosDIY;

import Request.MerchantService.oe.V1.Payment.Order.NotifyCallback;
import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.v1.sdMerchant.AcceptTermsAndConditionsEdcDIY;
import Request.MerchantService.v1.sdMerchant.CreateLeadDIYCashAtPos;
import Request.MerchantService.v1.upgradeMid.doc.status.FetchDocumentDIYCashAtPos;
import Request.MerchantService.v1.upgradeMid.doc.status.UploadDocCashAtPosDIY;
import Request.MerchantService.v2.edc.diyUpgradePlans.CheckEligibilityDIY;
import Request.MerchantService.v2.edc.plans.fetchPlanEdcDIY;
import Request.MerchantService.v2.edc.validateOrder.validateOrderEdcDIY;
import Request.OMS.AuthorizeOMS;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.OMS.CheckoutAuthorizeOMS;
import Services.Utilities.TestBase;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowCashAtPosDIY extends BaseMethod {
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowCashAtPosDIY.class);

    public static String MerchantToken = "";
    public static String mid = "MPYnOd08861029683358";
    public static String channel = "DIY_P4B_APP";
    public static String solutionType = "diy_upgrade_merchant_plan";
    public static String OrderId = "";
    public static String merchantMobileNumber = "5555505151";
    public static String productId = "";
    public static String custId = "1001844592";
    public static String price = "";
    public static String edcDIYLead = "";
    public static String edcPlanId = "61";
    public static String access_token = "";
    public static String leadStage = "";
    public static String leadId = "3ef6cc79-fa3a-4e3b-8e5b-2174262887dc";
    public static String item_id = "";
    public static String planType = "Cash at POS";
    public static String edcRentalCharge = "";
    public static String kybId="A0myoe9ig4qp426";
    public static String version="4.6.9";
    public static String PaymentStatus="";
    public static String cookie="";
    public static String OePanelDocStatus = "";
    public static String RejectionReason = "";
    public static String DocumetRequestDeserialised = "";
    public static String WorkFlowId="";
    public static String UBMid="";
    public static String docId="";
    public static String pgRequestID="";
    public static String WorkFlowStatusIdForCurrentNode="";
    public static String ActiveWorkFlowStatus="";


    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void merchantLoginEMIDIY() throws Exception
    {
        MerchantToken = ApplicantToken(merchantMobileNumber, "paytm@123");
        establishConnectiontoServer(MerchantToken,5);
        LOGGER.info("Merchant token for EDC DIY : " + MerchantToken);
        LOGGER.info(" Inside DB execution to reset lead : ");
        waitForLoad(3000);
        DBConnection.UpdateQueryToCloseLeadsolnlevel2(merchantMobileNumber,"diy_upgrade_merchant_plan","Cash at POS");

        /*TestBase testBase = new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '" + merchantMobileNumber + "' and status = '0' and solution_type='diy_upgrade_merchant_plan' and solution_type_level_2='Cash at POS';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " + UpdateRes); */

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch plan for Cash at pos  DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchPlanforCashAtPosDIY() {
        fetchPlanEdcDIY fetchPlanCashAtPosDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("planType", planType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanCashAtPosDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 2, groups = {"Regression"}, description = "Fetch product id for Cash at Pos DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchProductIdforCashAtPosDIY() {
        fetchPlanEdcDIY fetchPlanCashAtPosDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("planType", planType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanCashAtPosDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        productId = fetchplanEMIOfferingDIYResponse.jsonPath().getJsonObject("productId").toString();
        System.out.println("Product id is " + productId);

    }

    @Test(priority = 3, groups = {"Regression"}, description = "Fetch rental amount for Cash At Pos DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchRentalAmountforCashAtPosDIY() {
        fetchPlanEdcDIY fetchPlanCashAtPosDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("planType", planType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanCashAtPosDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        edcRentalCharge = fetchplanEMIOfferingDIYResponse.jsonPath().getJsonObject("rentalAmount").toString();
        System.out.println("EDC rental amount is " + edcRentalCharge);
    }

    @Test(priority = 4, groups = {"Regression"}, description = "Accept terms & conditions for cash at pos  DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AcceptTermsAndConditionsCashAtPosDIY() {
        AcceptTermsAndConditionsEdcDIY AcceptTermsAndConditionsEdcDIYObj = new AcceptTermsAndConditionsEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType", "individual");
        queryParams.put("solution", "diy_map_edc");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response AcceptTermsAndConditionsEdcDIYResponse = middlewareServicesObject.AcceptTermsAndConditionsEdcDIY(AcceptTermsAndConditionsEdcDIYObj, queryParams, headers);
        int StatusCode = AcceptTermsAndConditionsEdcDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 5, groups = {"Regression"}, description = "Check eligibility for Cash at pos DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void CheckEligibilityforCashAtPosDIY() {
        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", custId);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);

        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);

    }

    @Test(priority = 6, groups = {"Regression"}, description = "Check eligibility for Cash at pos DIY with invalid mid")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void CheckEligibilityWithInvalidMIDforCashAtPosDIY() {
        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", custId);
        queryParams.put("mid", "xeAMyW616142310899651");
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);


        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        String ExpectedErrorMsg = "Mid does not belong to given customer";
        String ActualErrorMsg = CheckEligibilityDIYObjResponse.jsonPath().getJsonObject("displayMessage").toString();
        Assert.assertTrue(ExpectedErrorMsg.contains(ActualErrorMsg));
    }

    @Test(priority = 7, groups = {"Regression"}, description = "Check eligibility for Cash at pos DIY with invalid CustId")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void CheckEligibilityWithInvalidCustIdforCashAtPosDIY() {
        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", "10009418901");
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", planType);


        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        String ExpectedErrorMsg = "Mid does not belong to given customer";
        String ActualErrorMsg = CheckEligibilityDIYObjResponse.jsonPath().getJsonObject("displayMessage").toString();
        Assert.assertTrue(ExpectedErrorMsg.contains(ActualErrorMsg));
    }

    @Test(priority = 8, groups = {"Regression"}, description = "Check eligibility for Cash at pos DIY with invalid plantype")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void CheckEligibilityWithInvalidPlanTypeforCashAtPosDIY() {
        CheckEligibilityDIY CheckEligibilityDIYObj = new CheckEligibilityDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("custId", custId);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("channel", channel);
        queryParams.put("planType", "Cash at POS1");


        Response CheckEligibilityDIYObjResponse = middlewareServicesObject.checkEligibilityDIY(CheckEligibilityDIYObj, queryParams);
        int StatusCode = CheckEligibilityDIYObjResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 500);
    }

    @Test(priority = 9, description = "invalid price for cash at pos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidPriceforCashAtPosDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderCashAtPosDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("customerId", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("price", "1234");
        body.put("customer_id", custId);
        body.put("planType", planType);

        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200, statusCode);
        String ExpectedMsg = "{\\\"errorMessageCode\\\":\\\"400\\\",\\\"errorMessage\\\":\\\"Invalid order, Price mismatch!, price in request = 1234.0, plan price = 58.0\\\"}";
        String ActualMsg = respObj.jsonPath().getJsonObject("error_info").toString();
        Assert.assertFalse(ExpectedMsg.contains(ActualMsg));

    }

    @Test(priority = 10, description = "invalid mid for Cash At pos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidmidforCashAtPosDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderCashAtPosDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<>();
        body.put("mid", "WCFQAy4894204988790911");
        body.put("customerId", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("price", edcRentalCharge);
        body.put("customer_id", custId);
        body.put("planType", planType);

        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200, statusCode);


    }

    @Test(priority = 11, description = "validate order for Cash At pos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void validateOrderforCashAtPosDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderCashAtPosDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("customerId", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("price", edcRentalCharge);
        body.put("customer_id", custId);
        body.put("planType", planType);

        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200, statusCode);


    }

    @Test(priority = 12, description = "create lead for Cash At pos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createLeadforCashAtPosDiy() {
        CreateLeadDIYCashAtPos CreateLeadDIYCashAtPosObj = new CreateLeadDIYCashAtPos(P.TESTDATA.get("CreateLeadCashAtPosDIY"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", MerchantToken);


        Map<String, String> body = new HashMap<>();
        body.put("mobile", merchantMobileNumber);
        body.put("kybId", kybId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", solutionType);
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("latitude", "28.4542");
        queryParams.put("channel", channel);
        queryParams.put("version", "3.5.1");
        queryParams.put("longitude", "72.1242");
        queryParams.put("solutionTypeLevel2", "Cash%20at%20POS");

        Response respObj=middlewareServicesObject.CreateLeadCashAtPosDIY(CreateLeadDIYCashAtPosObj,queryParams,headers,body);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200, statusCode);
    }

    @Test(priority = 13, description = "fetch leadid for Cash At pos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchLeadforCashAtPosDiy() {
        CreateLeadDIYCashAtPos CreateLeadDIYCashAtPosObj = new CreateLeadDIYCashAtPos(P.TESTDATA.get("CreateLeadCashAtPosDIY"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", MerchantToken);


        Map<String, String> body = new HashMap<>();
        body.put("mobile", merchantMobileNumber);
        body.put("kybId", kybId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", solutionType);
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("latitude", "28.4542");
        queryParams.put("channel", channel);
        queryParams.put("version", "3.5.1");
        queryParams.put("longitude", "72.1242");
        queryParams.put("solutionTypeLevel2", "Cash%20at%20POS");

        Response respObj=middlewareServicesObject.CreateLeadCashAtPosDIY(CreateLeadDIYCashAtPosObj,queryParams,headers,body);
  edcDIYLead=respObj.jsonPath().getJsonObject("leadId");
  System.out.println("Cash at pos lead id is " + edcDIYLead);
        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200, statusCode);
    }

    @Test(priority = 14, description = "fetch document status for Cash At pos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchDocumentStatusforCashAtPosDiy()
    {
        FetchDocumentDIYCashAtPos FetchDocumentDIYCashAtPosObj = new FetchDocumentDIYCashAtPos();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", MerchantToken);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", solutionType);
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", edcDIYLead);
        queryParams.put("channel", channel);
        queryParams.put("solutionLeadId", edcDIYLead);


        Response respObj=middlewareServicesObject.FetchDocumentStatusCashAtPosDIY(FetchDocumentDIYCashAtPosObj,queryParams,headers);
        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200, statusCode);
    }

    @Test(priority = 15, description = "Upload Doc for Cash At pos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UploadDocforCashAtPosDiy()
    {
        UploadDocCashAtPosDIY UploadDocCashAtPosDIYObj = new UploadDocCashAtPosDIY();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", MerchantToken);



        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", solutionType);
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("docType", "activity_proof");
        queryParams.put("channel", channel);
        queryParams.put("type", "png");
        queryParams.put("docProvided", "TrustDeed");
        queryParams.put("solutionTypeLevel2", "Cash%20at%20POS");
        queryParams.put("solutionLeadId", edcDIYLead);

        Response respObj=middlewareServicesObject.UploadDocCashAtPosDIY(UploadDocCashAtPosDIYObj,queryParams,headers);

    }


    @Test(priority = 16, description = "Create access token for cash at pos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createAccessToken()
    {

        AuthorizeOMS authorizeOMSObj=new AuthorizeOMS();
        CheckoutAuthorizeOMS checkoutObj=new CheckoutAuthorizeOMS();
        access_token= checkoutObj.CheckoutAuthorizeviaOMS(authorizeOMSObj).jsonPath().getJsonObject("access_token");
        System.out.println("Access token is " +access_token);

    }

    @Test(priority = 17, description = "Create QR for cash at pos diy", groups = {"Regression"},dependsOnMethods = "createAccessToken")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createQRforCashAtPosDiy()
    {


        OrderId=createQRviaOMS(edcDIYLead,merchantMobileNumber,MerchantToken);
        System.out.println("Order id is " + OrderId);


    }
    @Test(priority = 18, description = "Fetch payment status for Cash At Pos diy", groups = {"Regression"},dependsOnMethods = "createQRforCashAtPosDiy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchPaymentStatusforCashAtPosDiy()
    {
        item_id=fetchOrderviaOMS(OrderId);
        System.out.println("Item id is " + item_id);


    }

    @Test(priority = 19, description = "Notify Order Callback & payment done for Cash At pos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void PaymentCashAtPosDiy() {
        NotifyCallback createLeadEdcDIY = new NotifyCallback(P.TESTDATA.get("CashAtPosOrderNotify"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("order_id", OrderId);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<>();
        body.put("order_id", OrderId);
        body.put("mid", mid);
        body.put("customer_id", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("customerId",custId);

        Response respObj = middlewareServicesObject.OrderNotify(createLeadEdcDIY, queryParams, headers, body);
        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);
        String expectedMsg="Order generated successfully";
        String actualMsg=respObj.jsonPath().getJsonObject("displayMessage").toString();
        Assert.assertTrue(actualMsg.contains(expectedMsg));


    }
    @Test(priority = 20, description = "fetch lead status for cash at pos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchStatusLeadCashAtPosDiy()
    {
        waitForLoad(5000);
        FetchLead v1FetchLeadObj=new FetchLead(edcDIYLead);
        cookie=findXMWTokenforPanel("7771216290","paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, cookie);
        leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Lead stage is "+ leadStage);
    }

    @Test(priority = 22, description = "assign agent  for cash at pos diy", groups = {"Regression"},dependsOnMethods = {"insertQueryForOMSJob"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void assignAgentForQC() throws SQLException
    {
        if(leadStage.equals("QC_ACTION_PENDING"))
        {
            /*TestBase testBase = new TestBase();
            DbName = DbStagingSprint;
            testBase.assignAgentviaDB(merchantMobileNumber,"diy_upgrade_merchant_plan","Cash at POS");
            int UpdateRes = TestBase.UpdateQueryResult;
            LOGGER.info("These are Updated Row/s : " + UpdateRes); */
        }
        else
        {
           LOGGER.info("Lead is not in QC stage");
        }
    }


    @Test(priority = 21,description = "Fetch Lead Details on Panel",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void FetchDocidviaPanel() throws SQLException
    {
       /* TestBase testBase = new TestBase();
        DbName = DbStagingSprint;
        UBMid= testBase.getUbmIdForUpgradeMerchantPlan(merchantMobileNumber,"diy_upgrade_merchant_plan","Cash at POS");
        LOGGER.info("ubm id is : "+UBMid);
        WorkFlowId=testBase.executequeryForWorkFlowId(UBMid,"480");
        LOGGER.info("Workflow id is : "+WorkFlowId);*/

            FetchLead v1FetchLeadObj=new FetchLead(edcDIYLead);
            cookie=findXMWTokenforPanel("7771216290","paytm@123");
            Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, cookie);
           docId= v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.documents[0].uuid").toString();
          LOGGER.info("Doc uuid is : "+docId);



    }

    @Test(priority = 22,description = "Fetch Lead Details on Panel",groups = {"Regression"},dependsOnMethods = {"FetchDocidviaPanel"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void insertQueryForOMSJob() throws SQLException
    {
      /*  TestBase testBase = new TestBase();
        DbName = DbStagingSprint;
         testBase.insertQueryInWorkflowStatus(UBMid,"1140");
        testBase.updateWorkflowStatus(WorkFlowId);
        ActiveWorkFlowStatus=testBase.ActiveWorkflowStatus(UBMid);
        LOGGER.info(" Active workflow status is :"+ActiveWorkFlowStatus);
        testBase.insertJob(UBMid,"'AgentAllocationJob'"); */
        fetchStatusLeadCashAtPosDiy();

    }


    @Test(priority = 23,description = "qc rejected of cash at pos lead form OE Panel",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void QCRejectionForCashAtPos() throws SQLException
    {

        EditLead v1EditLeadObj = new EditLead(edcDIYLead, P.TESTDATA.get("SubmitMerchantCashATPosRejected"));
       /* TestBase testBase = new TestBase();
        DbName = DbStagingSprint;
        WorkFlowId=testBase.executequeryForWorkFlowId(UBMid,"181");
        LOGGER.info("Workflow id is : "+WorkFlowId);
        v1EditLeadObj.getProperties().setProperty("uuid", docId);
        v1EditLeadObj.getProperties().setProperty("uuids", docId);
        v1EditLeadObj.getProperties().setProperty("workflowStatusId", WorkFlowId);

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "7771216290", "1001647902", cookie, "application/json");

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String LeadStage = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
        Assert.assertTrue(LeadStage.contains("QC_REJECTED")); */


    }

    @Test(priority = 24, description = "Upload Doc  after QC rejection for Cash At pos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UploadDocAfterQCRejectionforCashAtPosDiy()
    {
        UploadDocCashAtPosDIY UploadDocCashAtPosDIYObj = new UploadDocCashAtPosDIY();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", MerchantToken);



        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", solutionType);
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("docType", "activity_proof");
        queryParams.put("channel", channel);
        queryParams.put("type", "png");
        queryParams.put("docProvided", "TrustDeed");
        queryParams.put("solutionTypeLevel2", "Cash%20at%20POS");
        queryParams.put("solutionLeadId", edcDIYLead);

        Response respObj=middlewareServicesObject.UploadDocCashAtPosDIY(UploadDocCashAtPosDIYObj,queryParams,headers);

    }
    @Test(priority = 25,description = "Fetch Lead Details on Panel",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void FetchDocidviaPanelAfterAgainDocUpload() throws SQLException
    {
        /*TestBase testBase = new TestBase();
        DbName = DbStagingSprint;
        UBMid= testBase.getUbmIdForUpgradeMerchantPlan(merchantMobileNumber,"diy_upgrade_merchant_plan","Cash at POS");
        WorkFlowId=testBase.executequeryForWorkFlowId(UBMid,"181");*/

        FetchLead v1FetchLeadObj=new FetchLead(edcDIYLead);
        cookie=findXMWTokenforPanel("7771216290","paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, cookie);
        docId= v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.documents[0].uuid").toString();
        LOGGER.info("New Doc uuid after doc upload is : "+docId);



    }

    @Test(priority = 26,description = "qc pass of cash at pos lead form OE Panel",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void QCApprovedForCashAtPos() throws SQLException
    {
        assignAgentForQC();
        EditLead v1EditLeadObj = new EditLead(edcDIYLead, P.TESTDATA.get("SubmitMerchantCashATPosApproved"));

        v1EditLeadObj.getProperties().setProperty("uuid", docId);
        v1EditLeadObj.getProperties().setProperty("uuids", docId);
        v1EditLeadObj.getProperties().setProperty("workflowStatusId", WorkFlowId);

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "7771216290", "1001647902", cookie, "application/json");

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String LeadStage = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
        Assert.assertTrue(LeadStage.contains("PANEL_SUCCESS"));


    }
    @Test(priority = 27, description = "fetch lead status for cash at pos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchStatusLeadCashAtPosDiyAfterQC()
    {
        waitForLoad(5000);
        FetchLead v1FetchLeadObj=new FetchLead(edcDIYLead);
        cookie=findXMWTokenforPanel("7771216290","paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, cookie);
        leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Lead stage is "+ leadStage);
    }

    @Test(priority = 28, description = "Fetch Lead Details Cash at pos Lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void DIYCashAtPosFetchLeadDetailsForPgReferenceId() {
        if (leadStage.equals("PANEL_SUCCESS")) {
            Response resObjGetMerchant = FetchPanelLeadviaLeadID(edcDIYLead);
            pgRequestID = resObjGetMerchant.jsonPath().getJsonObject("leadDetails.additionalDetails.pgReferenceId").toString();
            LOGGER.info("PG Request id is : " + pgRequestID);
        } else {
            LOGGER.info("Lead is not in correct stage");
        }

    }

    @Test(priority = 29, description = "PG Callback for cash at pos")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void ManualPGCallbackForCashAtPos() {
        ManualPgCallBack(custId, pgRequestID, mid);

    }

    @Test(priority = 30, description = "fetch lead status for cash at pos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchStatusLeadCashAtPosDiyAfterPGCallback()
    {
        waitForLoad(5000);
        FetchLead v1FetchLeadObj=new FetchLead(edcDIYLead);
        cookie=findXMWTokenforPanel("7771216290","paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, cookie);
        leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Lead stage is "+ leadStage);
    }
}