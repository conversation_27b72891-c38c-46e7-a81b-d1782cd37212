package OCL.DIY.MCO;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.Revisit.LoanIntentfetchDocumentDetails;
import Services.DBConnection.DBConnection;
import Services.DIYMCO.CreationOfLead;
import Services.DIYMCO.getStarted;
import Services.DIYProfileUpdate.BossToOeLeadUpdate;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class DiyMcoCreateLeadV3 extends BaseMethod {
    private static final String DOC_PATH = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";
    private static final String SOLUTION = "diy_mco";
    private static final String ENTITY_TYPE = "INDIVIDUAL";
    private static final String CHANNEL = "DIY_P4B_APP";
    
    private String token;
    private String requestPath;
    private String leadId;
    private String custId;
    private String qcAgentsToken = "";
    private String panPhotoDMSID = "";
    private String bankPhotoDMSID = "";
    
    private final CreationOfLead creationOfLead = new CreationOfLead();
    private final Utilities util = new Utilities();
    private final BossToOeLeadUpdate bossToOeLeadUpdate = new BossToOeLeadUpdate();
    private final MiddlewareServices middlewareServices = new MiddlewareServices();
    private final getStarted getStarted = new getStarted();
    
    private String mobileNumber;
    private String pan;

    @BeforeClass
    public void beforeOperation() {
        mobileNumber = util.randomMobileNumberGenerator().replaceFirst("5", "9");
        pan = util.randomIndividualPANValueGenerator();
        
        if (util.createNewAuthUser(mobileNumber, "Paytm@123")) {
            token = BaseMethod.ApplicantToken(mobileNumber, "Paytm@123");
        } else {
            Assert.fail("Number was not registered at Oauth end");
        }
    }

    private Map<String, String> getCommonHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", token);
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");
        headers.put("cache-control", "no-cache");
        headers.put("ipAddress", "************");
        headers.put("latitude", "28.32");
        headers.put("longitude", "77.213");
        headers.put("imei", "1234");
        headers.put("channel", CHANNEL);
        return headers;
    }

    private Map<String, String> getCommonParams() {
        Map<String, String> params = new HashMap<>();
        params.put("solution", SOLUTION);
        params.put("entityType", ENTITY_TYPE);
        params.put("channel", CHANNEL);
        params.put("leadId", leadId);
        return params;
    }

    @Test(priority = 1, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createLead() throws JSONException {
        Map<String, String> params = new HashMap<>();
        params.put("solution", SOLUTION);
        params.put("entityType", ENTITY_TYPE);
        params.put("channel", CHANNEL);

        Map<String, String> headers = getCommonHeaders();
        Map<String, String> body = new HashMap<>();
        body.put("isKycConsentCaptured", "true");

        requestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreateRequest.json";
        Response response = creationOfLead.CreationOfLeadResponseWithParam(requestPath, headers, params, body);

        int statusCode = response.getStatusCode();
        String displayMessage = response.jsonPath().getString("displayMessage");
        if ((statusCode == 400 && displayMessage.contains("Please relogin. You are already onboarded as a Paytm Merchant.")) || 
            (statusCode == 500 && displayMessage.contains("We are unable to process your request. Please try again after sometime to continue."))) {
            createLead();
        } else {
            Assert.assertEquals(statusCode, 200);
            leadId = response.jsonPath().getString("leadId");
        }
    }

    @Test(priority = 2, description = "Get Started of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getStarted() {
        Map<String, String> params = getCommonParams();
        Map<String, String> headers = getCommonHeaders();
        
        Map<String, String> body = new HashMap<>();
        body.put("WHATSAPP_NOTIFICATION_CONSENT", "true");
        body.put("REFERRER_CODE", "");
        body.put("MERCHANT_EMAIL", "<EMAIL>");
        body.put("partialSave", "true");

        requestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOGetStartedRequest.json";
        Response response = getStarted.getStartedResponse(requestPath, headers, params, body);

        Assert.assertTrue(response.getStatusCode() == 200);
    }

    @Test(priority = 3, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateBusinessCat() {
        Map<String, String> params = getCommonParams();
        Map<String, String> headers = getCommonHeaders();

        Map<String, String> body = new HashMap<>();
        body.put("SEGMENT", "Food");
        body.put("SUB_SEGMENT", "Restaurant");
        body.put("partialSave", "true");

        requestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateBussinessCatRequest.json";
        Response response = getStarted.getStartedResponse(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
    }

    @Test(priority = 4, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updatePennyDrop() {
        Map<String, String> params = getCommonParams();
        Map<String, String> headers = getCommonHeaders();

        Map<String, String> body = new HashMap<>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", mobileNumber);
        body.put("bankName", "ICICI bank limited");
        body.put("ifsc", "ICIC0001070");

        requestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";
        Response response = getStarted.getStartedResponse(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
    }

    @Test(priority = 5, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void submitBankLead() {
        Map<String, String> params = getCommonParams();
        Map<String, String> headers = getCommonHeaders();

        Map<String, String> body = new HashMap<>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", mobileNumber);
        body.put("bankName", "ICICI bank limited");
        body.put("ifsc", "ICIC0001070");

        requestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";
        Response response = getStarted.getStartedResponse(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
    }

    @Test(priority = 6, description = "Enter non-individual PAN manually", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void enterIndividualPanManually() throws JSONException {
        Map<String, String> params = getCommonParams();
        Map<String, String> headers = getCommonHeaders();

        Map<String, String> body = new HashMap<>();
        body.put("partialSave", "true");
        body.put("pan", pan);
        body.put("gstin", "");
        body.put("PAN", pan);
        body.put("BUSINESS_ENTITY", "INDIVIDUAL");
        body.put("KYB_BUSINESS_ID", "");

        requestPath = "MerchantService/v1/sdMerchant/lead/updatePanLeadRequest.json";
        Response response = getStarted.getStartedResponse(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
    }

    @Test(priority = 7, description = "Upload Merchant Selfie", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void uploadPanDocument() throws Exception {
        // Wait for lead to be ready
        Thread.sleep(2000);
        
        // Fetch screen details
        Response screenDetailsResponse = super.FetchScreenDetails(leadId, token, ENTITY_TYPE, null, "SCREEN_DETAILS");
        
        // Verify PAN document upload screen
        boolean panDocumentRequired = super.checkNextScreenDetails(screenDetailsResponse, "SCAN_PAN");
if (panDocumentRequired) {
    

        // Get upload endpoint
        String endPoint = P.API.get("UploadMerchantSelfie");
        
        // Prepare request parameters
        Map<String, String> params = new HashMap<>();
        params.put("docType", "pan");
        params.put("docProvided", "pan");
        params.put("entityType", ENTITY_TYPE);
        params.put("solutionType", SOLUTION);
        params.put("channel", CHANNEL);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");

        // Prepare request headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "multipart/form-data");
        headers.put("session_token", token);

        // Verify upload file exists
        File uploadFile = new File(DOC_PATH);
        
        // Upload document
        Response uploadResponse = uploadDocumentToAPI(uploadFile, params, headers, endPoint);
}
         }

    @Test(priority = 8, description = "To check that upload document is working", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void uploadBankProofDocument() throws JSONException, Exception {
        // Wait for previous step to complete
        Thread.sleep(2000);
        
        // Fetch screen details
        Response screenDetailsResponse = super.FetchScreenDetails(leadId, token, ENTITY_TYPE, null, "SCREEN_DETAILS");
         // Verify bank proof upload screen
        boolean bankProofRequired = super.checkNextScreenDetails(screenDetailsResponse, "BANK_NAME_MATCH_FALSE_SCREEN");

        if (bankProofRequired) {
            
        
        // Get upload endpoint
        String endPoint = P.API.get("UpdateBankProof");
        
        // Prepare request parameters
        Map<String, String> params = new HashMap<>();
        params.put("solutionType", SOLUTION);
        params.put("channel", CHANNEL);
        params.put("entityType", ENTITY_TYPE);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("docType", "bankProof");
        params.put("docProvided", "BankStatement");

        // Prepare request headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "multipart/form-data");
        headers.put("session_token", token);
        headers.put("channel", CHANNEL);

        // Verify upload file exists
        File uploadFile = new File(DOC_PATH);
       
        // Upload document
        Response uploadResponse = uploadDocumentToAPI(uploadFile, params, headers, endPoint);
       
        // Verify response body
        String uploadResponseBody = uploadResponse.getBody().asString();
        }
        }

    @Test(priority = 9, description = "updates the lead by adding shop address", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateShopAddress() {
        Map<String, String> params = getCommonParams();
        Map<String, String> headers = getCommonHeaders();

        Map<String, String> body = new HashMap<>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "28.550667");
        body.put("longitudeOfShopVehicle", "77.268952");
        body.put("partialSave", "true");
        body.put("pincode", "180012");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LAT", "29.3222676");
        body.put("MAP_MY_INDIA_LONG", "76.3059143");

        requestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";
        Response response = getStarted.getStartedResponse(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
    }

    @Test(priority = 10, description = "Submit The Lead / business review ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void submitTheLead() {
        Map<String, String> params = getCommonParams();
        Map<String, String> headers = getCommonHeaders();

        Map<String, Object> body = new HashMap<>();
        body.put("partialSave", false);
        body.put("IS_AGREEMENT_ACCEPTED", true);

        requestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitTheLeadRequest.json";
        Response response = bossToOeLeadUpdate.bossToOeLeadUpdate(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
    }

    private Response uploadDocumentToAPI(File uploadFile, Map<String, String> params, Map<String, String> headers, String endPoint) {
        String baseURI = P.API.get("api_url");
        Assert.assertNotNull(baseURI, "API base URL should not be null");
        
        try {
            // Validate input parameters
            Assert.assertNotNull(uploadFile, "Upload file should not be null");
            Assert.assertTrue(uploadFile.exists(), "Upload file does not exist: " + uploadFile.getAbsolutePath());
            Assert.assertNotNull(params, "Request parameters should not be null");
            Assert.assertNotNull(headers, "Request headers should not be null");
            Assert.assertNotNull(endPoint, "API endpoint should not be null");
            
            // Build request specification
            RequestSpecification spec = new RequestSpecBuilder()
                .setBaseUri(baseURI)
                .build();
            
            // Make the API call
            Response response = RestAssured.given()
                .multiPart(uploadFile)
                .spec(spec)
                .relaxedHTTPSValidation()
                .queryParams(params)
                .headers(headers)
                .post(endPoint);
            
            // Validate response
            Assert.assertNotNull(response, "API response should not be null");
            Assert.assertNotNull(response.getBody(), "Response body should not be null");
            
            // Log response for debugging
            System.out.println("Document upload response: " + response.getBody().asString());
            
            return response;
        } catch (Exception e) {
            String errorMessage = String.format("Failed to upload document: %s. File: %s, Endpoint: %s", 
                e.getMessage(), uploadFile.getAbsolutePath(), endPoint);
            System.err.println(errorMessage);
            Assert.fail(errorMessage);
            return null;
        }
    }

    @Test(priority = 11, description = "", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void DiyMcoLeadFetchDocStatus() throws Exception {
        custId = GetResourceOwnerId(mobileNumber, "Paytm@123");

        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();
        Map<String, String> headers = new HashMap<>();

        headers.put("session_token", token);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("version", "7.2.8");
        headers.put("deviceidentifier", "Xiaomi-RedmiNote9Pro-efc28fc97cede738");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solution", "diy_mco");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);
        queryParams.put("merchantCustId", custId);
        queryParams.put("channel", "DIY_P4B_APP");

        Response respObj = middlewareServices.revisitLoanIntentFetchDoc(obj, headers, queryParams);

        Assert.assertEquals(respObj.getStatusCode(), 200);
        panPhotoDMSID = respObj.path("uploadedDocDetailsSet[0].uploadedDocs[0].uuid");
        bankPhotoDMSID = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
        System.out.println("Bank Photo DMSID: " + bankPhotoDMSID + ", PAN Photo DMSID: " + panPhotoDMSID);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "DIY MCO Retail Onboarding QC", priority = 12, dependsOnMethods = "DiyMcoLeadFetchDocStatus")
    public void DiyMcoRetailOnboardingLeadQC() throws Exception {
        DBConnection dbConnectionObj = new DBConnection();

        int Ubmid = dbConnectionObj.getUserBusinessMappingId(mobileNumber, "diy_mco");

        dbConnectionObj.assignAgentViaDB("1152", Ubmid);

        Long DiyMCOIndividualWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);

        String wfsid = String.valueOf(DiyMCOIndividualWorkflowStatusId);

        requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadDiyMco.json";
        EditLead EditLeadObj = new EditLead(leadId, requestPath);

        EditLeadObj.getProperties().setProperty("uuidPanPhoto", panPhotoDMSID);
        EditLeadObj.getProperties().setProperty("workflowStatusId", wfsid);
        EditLeadObj.getProperties().setProperty("uuidBankPhoto", bankPhotoDMSID);
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("action", "SUBMIT");
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("session_token", qcAgentsToken);
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("Cookie", XMWCookie);
        Response QCMCOLead = middlewareServices.v1EditLeadOEMco(EditLeadObj, queryParams, headers);

        Thread.sleep(120000);
    }

    @Test(priority = 13, description = "add channel callback", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addChannelCallback() throws Exception {
        Thread.sleep(80000);
        Response addchannel = middlewareServices.addChannelCallback(custId, leadId);
    }

    @Test(priority = 14, description = "Test invalid PAN number format", groups = {"Negative"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInvalidPanNumber() throws JSONException {
        Map<String, String> params = getCommonParams();
        Map<String, String> headers = getCommonHeaders();

        Map<String, String> body = new HashMap<>();
        body.put("partialSave", "true");
        body.put("pan", "INVALID123"); // Invalid PAN format
        body.put("gstin", "");
        body.put("PAN", "INVALID123");
        body.put("BUSINESS_ENTITY", "INDIVIDUAL");
        body.put("KYB_BUSINESS_ID", "");

        requestPath = "MerchantService/v1/sdMerchant/lead/updatePanLeadRequest.json";
        Response response = getStarted.getStartedResponse(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 400);
    }

    @Test(priority = 15, description = "Test invalid bank details", groups = {"Negative"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInvalidBankDetails() {
        Map<String, String> params = getCommonParams();
        Map<String, String> headers = getCommonHeaders();

        Map<String, String> body = new HashMap<>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", "****************"); // Invalid account number
        body.put("bankName", "Invalid Bank");
        body.put("ifsc", "INVALID123"); // Invalid IFSC

        requestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";
        Response response = getStarted.getStartedResponse(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 400);
    }

    @Test(priority = 16, description = "Test invalid shop address", groups = {"Negative"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInvalidShopAddress() {
        Map<String, String> params = getCommonParams();
        Map<String, String> headers = getCommonHeaders();

        Map<String, String> body = new HashMap<>();
        body.put("areaOfEnrollment", ""); // Empty area
        body.put("cityOfEnrollment", "");
        body.put("landmark", "");
        body.put("latitudeOfShopVehicle", "invalid"); // Invalid latitude
        body.put("longitudeOfShopVehicle", "invalid"); // Invalid longitude
        body.put("partialSave", "true");
        body.put("pincode", "123"); // Invalid pincode
        body.put("shopAddress", "");
        body.put("state", "");

        requestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";
        Response response = getStarted.getStartedResponse(requestPath, headers, params, body);

    }

    @Test(priority = 17, description = "Test invalid session token", groups = {"Negative"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInvalidSessionToken() {
        Map<String, String> params = getCommonParams();
        Map<String, String> headers = getCommonHeaders();
        headers.put("session_token", "invalid_token"); // Invalid session token

        Map<String, String> body = new HashMap<>();
        body.put("partialSave", "true");

        requestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOGetStartedRequest.json";
        Response response = getStarted.getStartedResponse(requestPath, headers, params, body);

        Assert.assertEquals(response.getStatusCode(), 401);
    }

    @Test(priority = 18, description = "Test invalid document upload", groups = {"Negative"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInvalidDocumentUpload() throws Exception {
        Map<String, String> params = getCommonParams();
        Map<String, String> headers = getCommonHeaders();
        headers.put("Content-Type", "multipart/form-data");

        // Create an empty file for testing
        File emptyFile = new File("empty.txt");
        emptyFile.createNewFile();

        String endPoint = P.API.get("UploadMerchantSelfie");
        Response uploadResponse = uploadDocumentToAPI(emptyFile, params, headers, endPoint);

        Assert.assertEquals(uploadResponse.getStatusCode(), 400);

        // Clean up
        emptyFile.delete();
    }

}


