package OCL.DIY.MCO;

import Services.DIYMCO.CreationOfLead;
import Services.DIYMCO.getStarted;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.assertions.CustomAssert;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class DiyMcoNegFlows {

    String merchantMobileNumber = "8888600239";
    String aadharDocPath = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/aadharOcrImage.png";
    String aadhardDocPathIncorrect ="src/test/resources";
    String endPointInvalid = "/v1/profile/update/ocr/doc65";
    String Token;
    String RequestPath;
    String leadId;
    CreationOfLead COF = new CreationOfLead();

    Utilities Util = new Utilities();
    String num2  = Util.randomMobileNumberGenerator();
    String num = num2.replaceFirst("5","7");

    String invalidToken ="invalid";




    getStarted gs = new getStarted();;
    Map<String, String> header = new HashMap<String, String>();
    Response response = null;
    @BeforeClass
    public void BeforeOperatiosn(){
        if(Util.createNewAuthUser(num,"paytm@123")){
            Token = BaseMethod.ApplicantToken(num, "paytm@123");
        }
        else{
            Assert.assertFalse(false,"Number was not registered at Outh end");
        }
    }


    @Test(priority = 1, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_createLead() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("isKycConsentCaptured", "true");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreateRequest.json";

        response = COF.CreationOfLeadResponseWithParam(RequestPath,header, params, body);

        leadId = response.jsonPath().getString("leadId");
        System.out.println(leadId);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }



    @Test(priority = 3, description = "In Upload aadhar ocr validate that for  empty headers api is throwing error", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_AadharOcr() throws Exception {

        String endPoint = P.API.get("UploadAadhaarOcr");
        Map<String, String> params = new HashMap<String, String>();
        params.put("fileType", "AADHAR");

        Map<String, String> header_ocr = new HashMap<String, String>();


        File uploadFile = new File(aadharDocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header_ocr,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }

    @Test(priority = 3, description = "on Upload aadhar error is recieved when token is incorrect ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC002_AadharOcr() throws Exception {

        String endPoint = P.API.get("UploadAadhaarOcr");
        Map<String, String> params = new HashMap<String, String>();
        params.put("fileType", "AADHAR");
        params.put("leadId",leadId);

        Map<String, String> header_ocr = new HashMap<String, String>();
        header_ocr.put("Content-Type", "multipart/form-data");
        header_ocr.put("session_token", invalidToken);

        File uploadFile = new File(aadharDocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header_ocr,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }

    @Test(priority = 3, description = "In Upload aadhar ocr validate that invalid api endpoint  is throwing error", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_AadharOcr() throws Exception {

        Map<String, String> params = new HashMap<String, String>();
        params.put("fileType", "AADHAR");
        params.put("leadId",leadId);

        Map<String, String> header_ocr = new HashMap<String, String>();
        header_ocr.put("Content-Type", "multipart/form-data");
        header_ocr.put("session_token", Token);

        File uploadFile = new File(aadharDocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header_ocr,endPointInvalid);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 405);
    }

    @Test(priority = 3, description = "In Upload aadhar ocr validate that invalid Contnetn type for file upload Api  is throwing error", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_AadharOcr() throws Exception {

        try {

            String endPoint = P.API.get("UploadAadhaarOcr");
            Map<String, String> params = new HashMap<String, String>();
            params.put("fileTypes", "AADHAR");
            params.put("leadId", leadId);

            Map<String, String> header_ocr = new HashMap<String, String>();
            header_ocr.put("Content-Type", "text/plain");
            header_ocr.put("session_token", Token);

            File uploadFile = new File(aadharDocPath);

            Response resp = UploadDocInAPI(uploadFile, params, header_ocr, endPoint);
            System.out.print(resp.getBody().asString());

        }catch(Exception e){
            CustomAssert.assertTrue(true ,"invalid Content type selected for file upload "+ e.getMessage());
        }
    }


    @Test(priority = 3, description = "In Upload aadhar ocr on empty token api is throwing error", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_AadharOcr() throws Exception {

        String endPoint = P.API.get("UploadAadhaarOcr");
        Map<String, String> params = new HashMap<String, String>();
        params.put("fileType", "AADHAR");
        params.put("leadId",leadId);

        Map<String, String> header_ocr = new HashMap<String, String>();
        header_ocr.put("Content-Type", "multipart/form-data");
        header_ocr.put("session_token", " ");

        File uploadFile = new File(aadharDocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header_ocr,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }



    @Test(priority = 3, description = "In Upload aadhar ocr on empty header for Content-Type  api is throwing error", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_AadharOcr() throws Exception {

        try {

            String endPoint = P.API.get("UploadAadhaarOcr");
            Map<String, String> params = new HashMap<String, String>();
            params.put("fileType", "AADHAR");
            params.put("leadId", leadId);

            Map<String, String> header_ocr = new HashMap<String, String>();
            header_ocr.put("Content-Type", " ");
            header_ocr.put("session_token", Token);

            File uploadFile = new File(aadharDocPath);

            Response resp = UploadDocInAPI(uploadFile, params, header_ocr, endPoint);
            System.out.print(resp.getBody().asString());

        }catch(Exception e){

            CustomAssert.assertTrue(true ,"on entering empty content type we are getting error ");
        }

    }


    @Test(priority = 3, description = "In Upload aadhar ocr on sending incorrect aadhar doc path", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_AadharOcr() throws Exception {

        try{

            String endPoint = P.API.get("UploadAadhaarOcr");
            Map<String, String> params = new HashMap<String, String>();
            params.put("fileType","AADHAR" );
            params.put("leadId",leadId);

            Map<String, String> header_ocr = new HashMap<String, String>();
            header_ocr.put("Content-Type", "multipart/form-data");
            header_ocr.put("session_token", Token);

            File uploadFile = new File(aadhardDocPathIncorrect);

            Response resp = UploadDocInAPI(uploadFile,params,header_ocr,endPoint);
            System.out.print(resp.getBody().asString());

        }catch(Exception e){

            CustomAssert.assertTrue(true ,"on entering invalid aadhar doc we are getting error ");
        }
    }



    public Response UploadDocInAPI(File uploadFile, Map<String, String> params,Map<String, String> header,String endPoint) throws Exception{

        Response resp = null;
        String baseURI = P.API.get("api_url");

        RequestSpecification spec;

        try {
            spec = new RequestSpecBuilder().setBaseUri(baseURI).build();
            resp = RestAssured.given().multiPart(uploadFile).spec(spec).relaxedHTTPSValidation().queryParams(params).headers(header)
                    .post(endPoint);
        }
        catch (Exception e){
            e.printStackTrace();
        }
        return resp;

    }

}