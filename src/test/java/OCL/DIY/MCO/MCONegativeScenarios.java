package OCL.DIY.MCO;
import Services.DIYMCO.CreationOfLead;
import Services.DIYMCO.getStarted;
import Services.DIYMCO.aadhaarUpdate;
import Services.DIYMCO.FetchCatSubCat;
import Services.LendingService.LendingBaseAPI;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.amazonaws.services.dynamodbv2.xspec.S;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.http.Header;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.apache.poi.hssf.record.HeaderRecord;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import Services.Utilities.Utilities;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class MCONegativeScenarios {

    String merchantMobileNumber = "8164646496";
    String inToken = "63767419sdfghj40";
    String DocPath = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";
//    String Token;
    String RequestPath;
    String leadId;
    String TLeadId = "4d81041e-6009-4ade-808f-2b9499771182";
    String IvLeadId = "4d81041e-6009-4ade-808f-2b9499771189";
    CreationOfLead COF = new CreationOfLead();


    String VleadId = "bde30a0c-ac31-4234-9f82-e8a22dda0232";
    String InVleadId = "bde30a0c-ac31-4234-9f82-e8a22dda0233";

    String InvAadhaar = "5274";

    String Token = "a4e9da56-c9f2-433f-bf69-5287c1133200";




    Utilities Util = new Utilities();
    String num2  = Util.randomMobileNumberGenerator();
    String num = num2.replaceFirst("5","9");

    Boolean Number9;
    String Aadhaar = Util.ValidAadhaar();
    String BankAccNo = Util.generateRandomBankAccountNumber();




    getStarted gs = new getStarted();
    aadhaarUpdate au = new aadhaarUpdate();
    FetchCatSubCat FC = new FetchCatSubCat();
    Map<String, String> header = new HashMap<String, String>();
    Response response = null;
//    @BeforeClass
//    public void BeforeOperatiosn(){
//        if(Util.createNewAuthUser(merchantMobileNumber,"paytm@123")){
//            Token = BaseMethod.ApplicantToken(num, "paytm@123");
//        }
//        else{
//            Assert.assertFalse(false,"Number was not registered at Outh end");
//        }
//    }


    @Test(priority = 1, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_createLead() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("isKycConsentCaptured", "true");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreateRequest.json";

        response = COF.CreationOfLeadResponseWithParam(RequestPath,header, params, body);

        leadId = response.jsonPath().getString("leadId");
        System.out.println(leadId);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 1, description = "Creation of a lead with an invalid token", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_createLeadInvalidSessionT() {


        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token",inToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("isKycConsentCaptured", "true");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreateRequest.json";

        response = COF.CreationOfLeadResponseWithParam(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 401);

    }
    @Test(priority = 1, description = "Creation of a lead Without param Entity", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_createLeadWithoutEntity() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("channel", "DIY_P4B_APP");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("isKycConsentCaptured", "true");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreateRequest.json";

        response = COF.CreationOfLeadResponseWithParam(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);

    }
    @Test(priority = 1, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_createLeadWithIncorrectParam() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIIDUAL");
        params.put("channel", "DIY_P4B_APP");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("isKycConsentCaptured", "true");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreateRequest.json";

        response = COF.CreationOfLeadResponseWithParam(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);

    }
    @Test(priority = 1, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_createLeadWithMissingHeader() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("isKycConsentCaptured", "true");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreateRequest.json";

        response = COF.CreationOfLeadResponseWithParam(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }

    @Test(priority = 1, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_createLeadWithoutChannel() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("isKycConsentCaptured", "true");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreateRequest.json";

        response = COF.CreationOfLeadResponseWithParam(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);

    }
    @Test(priority = 1, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_createLeadWithInvalidEndpoint() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("isKycConsentCaptured", "true");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreateRequest.json";

        response = COF.CreationOfLeadResponseWithParam(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }

    @Test(priority = 1, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_createLeadNoBody() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("isKycConsentCaptured", "true");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreateRequest.json";

        response = COF.CreationOfLeadResponseWithParam(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);

    }
    @Test(priority = 2, description = "Get Started of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_GetStarted() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("WHATSAPP_NOTIFICATION_CONSENT", "true");
        body.put("REFERRER_CODE", "");
        body.put("MERCHANT_EMAIL", "<EMAIL>");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOGetStartedRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 2, description = "Get Started of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_GetStartedInvalidT() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",TLeadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", inToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("WHATSAPP_NOTIFICATION_CONSENT", "true");
        body.put("REFERRER_CODE", "");
        body.put("MERCHANT_EMAIL", "<EMAIL>");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOGetStartedRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 2, description = "Get Started of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_GetStartedInvalidLeadId() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",IvLeadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("WHATSAPP_NOTIFICATION_CONSENT", "true");
        body.put("REFERRER_CODE", "");
        body.put("MERCHANT_EMAIL", "<EMAIL>");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOGetStartedRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 2, description = "Get Started of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_GetStartedWithMissingParam() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",IvLeadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("WHATSAPP_NOTIFICATION_CONSENT", "true");
        body.put("REFERRER_CODE", "");
        body.put("MERCHANT_EMAIL", "<EMAIL>");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOGetStartedRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 2, description = "Get Started of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_GetStartedWithMissingSolution() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("entityType", "PUBLIC_LIMITED");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",IvLeadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("WHATSAPP_NOTIFICATION_CONSENT", "true");
        body.put("REFERRER_CODE", "");
        body.put("MERCHANT_EMAIL", "<EMAIL>");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOGetStartedRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 2, description = "Get Started of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_GetStartedWithDiffEndPoint() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",IvLeadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("WHATSAPP_NOTIFICATION_CONSENT", "true");
        body.put("REFERRER_CODE", "");
        body.put("MERCHANT_EMAIL", "<EMAIL>");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOGetStartedRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 2, description = "Get Started of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_GetStartedMissingBody() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",IvLeadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("WHATSAPP_NOTIFICATION_CONSENT", "true");
        body.put("REFERRER_CODE", "");
        body.put("MERCHANT_EMAIL", "<EMAIL>");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOGetStartedRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 2, description = "Get Started of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_GetStartedWithNoBody() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",IvLeadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("WHATSAPP_NOTIFICATION_CONSENT", "true");
        body.put("REFERRER_CODE", "");
        body.put("MERCHANT_EMAIL", "<EMAIL>");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOGetStartedRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 2, description = "Get Started of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_GetStartedWithMissingHeader() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",IvLeadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("WHATSAPP_NOTIFICATION_CONSENT", "true");
        body.put("REFERRER_CODE", "");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOGetStartedRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }


    @Test(priority = 3, description = "Upload aadhar ocr", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_AadharOcr() throws Exception {

        String endPoint = P.API.get("UploadAadhaarOcr");
        Map<String, String> params = new HashMap<String, String>();
        params.put("fileType", "AADHAR");
        params.put("leadId",leadId);

        Map<String, String> header_ocr = new HashMap<String, String>();
        header_ocr.put("Content-Type", "multipart/form-data");
        header_ocr.put("session_token", Token);

        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header_ocr,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 4, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_UpdateLeadAadhaar() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("aadharNumber", Aadhaar);
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "true");
        body.put("AADHAR_NO_MISMATCHED", "false");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Female");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");
        body.put("userAddressLine1", "9187");
        body.put("userAddressLine2", "Ahxhshj");
        body.put("userAddressLine3", "Sjjsiskso");
        body.put("userAddressPincode", "126102");
        body.put("userAddressState", "Haryana");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 4, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_UpdateLeadAadhaarWithInvalidSToken() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", inToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("aadharNumber", Aadhaar);
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "true");
        body.put("AADHAR_NO_MISMATCHED", "false");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Female");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");
        body.put("userAddressLine1", "9187");
        body.put("userAddressLine2", "Ahxhshj");
        body.put("userAddressLine3", "Sjjsiskso");
        body.put("userAddressPincode", "126102");
        body.put("userAddressState", "Haryana");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

        response = gs.getStartedResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 4, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_UpdateLeadAadhaarWithInvalidLeadId() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", IvLeadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("aadharNumber", Aadhaar);
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "true");
        body.put("AADHAR_NO_MISMATCHED", "false");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Female");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");
        body.put("userAddressLine1", "9187");
        body.put("userAddressLine2", "Ahxhshj");
        body.put("userAddressLine3", "Sjjsiskso");
        body.put("userAddressPincode", "126102");
        body.put("userAddressState", "Haryana");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

        response = au.aadhaarUpdateResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        String Error = response.getStatusLine();
        Assert.assertEquals(statusCode, 500);
        System.out.println(Error);
    }
    @Test(priority = 4, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_UpdateLeadAadhaarWithMissingEntity() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", inToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("aadharNumber", Aadhaar);
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "true");
        body.put("AADHAR_NO_MISMATCHED", "false");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Female");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");
        body.put("userAddressLine1", "9187");
        body.put("userAddressLine2", "Ahxhshj");
        body.put("userAddressLine3", "Sjjsiskso");
        body.put("userAddressPincode", "126102");
        body.put("userAddressState", "Haryana");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

        response = au.aadhaarUpdateResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 4, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_UpdateLeadAadhaarWithInCorrectAadhar() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", inToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("aadharNumber", InvAadhaar);
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "true");
        body.put("AADHAR_NO_MISMATCHED", "false");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Female");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");
        body.put("userAddressLine1", "9187");
        body.put("userAddressLine2", "Ahxhshj");
        body.put("userAddressLine3", "Sjjsiskso");
        body.put("userAddressPincode", "126102");
        body.put("userAddressState", "Haryana");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

        response = au.aadhaarUpdateResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 4, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_UpdateLeadAadhaarWithMissingAadhaar() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", inToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "true");
        body.put("AADHAR_NO_MISMATCHED", "false");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Female");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");
        body.put("userAddressLine1", "9187");
        body.put("userAddressLine2", "Ahxhshj");
        body.put("userAddressLine3", "Sjjsiskso");
        body.put("userAddressPincode", "126102");
        body.put("userAddressState", "Haryana");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

        response = au.aadhaarUpdateResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 4, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_UpdateLeadAadhaarWithInvalidAddress() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("aadharNumber", Aadhaar);
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "true");
        body.put("AADHAR_NO_MISMATCHED", "false");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Female");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");
        body.put("userAddressLine1", "9187");
        body.put("userAddressLine2", "Ahxhshj");
        body.put("userAddressLine3", "Sjjsiskso");
        body.put("userAddressPincode", "1265443102");
        body.put("userAddressState", "Harrryana");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

        response = au.aadhaarUpdateResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 4, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_UpdateLeadAadhaarWithNoaddress() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("aadharNumber", Aadhaar);
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "true");
        body.put("AADHAR_NO_MISMATCHED", "false");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Female");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

        response = au.aadhaarUpdateResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 4, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_UpdateLeadAadhaarWithIncorrectParams() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_Mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_GG_APP");
        params.put("leadId", VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("aadharNumber", Aadhaar);
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "true");
        body.put("AADHAR_NO_MISMATCHED", "false");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Female");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");
        body.put("userAddressLine1", "9187");
        body.put("userAddressLine2", "Ahxhshj");
        body.put("userAddressLine3", "Sjjsiskso");
        body.put("userAddressPincode", "126102");
        body.put("userAddressState", "Haryana");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

        response = au.aadhaarUpdateResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 4, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_UpdateLeadAadhaarWithIncorrectheader() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "14592.14535.45550.1455");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("aadharNumber", Aadhaar);
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "true");
        body.put("AADHAR_NO_MISMATCHED", "false");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Female");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");
        body.put("userAddressLine1", "9187");
        body.put("userAddressLine2", "Ahxhshj");
        body.put("userAddressLine3", "Sjjsiskso");
        body.put("userAddressPincode", "126102");
        body.put("userAddressState", "Haryana");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

        response = au.aadhaarUpdateResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        String errorR   = String.valueOf(response.getStatusLine());
        System.out.println(errorR);
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 4, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_UpdateLeadAadhaarWithIncorrectBody() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("aadharNumber", Aadhaar);
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "false");
        body.put("AADHAR_NO_MISMATCHED", "true");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Femboy");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");
        body.put("userAddressLine1", "9187");
        body.put("userAddressLine2", "Ahxhshj");
        body.put("userAddressLine3", "Sjjsiskso");
        body.put("userAddressPincode", "126102");
        body.put("userAddressState", "Haryana");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

        response = au.aadhaarUpdateResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 4, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_UpdateLeadAadhaarWithMissingParams() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("leadId", VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("aadharNumber", Aadhaar);
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "true");
        body.put("AADHAR_NO_MISMATCHED", "false");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Female");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");
        body.put("userAddressLine1", "9187");
        body.put("userAddressLine2", "Ahxhshj");
        body.put("userAddressLine3", "Sjjsiskso");
        body.put("userAddressPincode", "126102");
        body.put("userAddressState", "Haryana");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

        response = au.aadhaarUpdateResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 4, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_UpdateLeadAadhaarWithMissingHeader() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("aadharNumber", Aadhaar);
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "true");
        body.put("AADHAR_NO_MISMATCHED", "false");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Female");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");
        body.put("userAddressLine1", "9187");
        body.put("userAddressLine2", "Ahxhshj");
        body.put("userAddressLine3", "Sjjsiskso");
        body.put("userAddressPincode", "126102");
        body.put("userAddressState", "Haryana");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

        response = au.aadhaarUpdateResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 4, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_UpdateLeadAadhaarWithMissingBody() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("aadharNumber", Aadhaar);
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "true");
        body.put("AADHAR_NO_MISMATCHED", "false");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Female");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");
        body.put("userAddressLine1", "9187");
        body.put("userAddressLine2", "Ahxhshj");
        body.put("userAddressLine3", "Sjjsiskso");
        body.put("userAddressState", "Haryana");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

        response = au.aadhaarUpdateResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 5, description = "Upload aadhar ocr", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_Upload_AadharDoc() throws Exception {

        String endPoint = P.API.get("UploadAadhaarDoc");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "poi");
        params.put("docProvided","aadhaar");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType","diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId",leadId);
        params.put("pageNo","1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", Token);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        //uploading page 2 of aadhar
        params.put("pageNo","2");
        Response resp2 = UploadDocInAPI(uploadFile,params,header,endPoint);
        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }
    @Test(priority = 6, description = "Upload Merchant Selfie", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_Upload_MerchantSelfie() throws Exception {

        String endPoint = P.API.get("UploadMerchantSelfie");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "businessOwnerPhoto");
        params.put("docProvided","businessOwnerPhoto");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType","diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId",leadId);
        params.put("pageNo","1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", Token);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        //uploading page 2 of aadhar
        params.put("pageNo","2");
        Response resp2 = UploadDocInAPI(uploadFile,params,header,endPoint);
        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }
    @Test(priority = 7, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_UpdateBussinessCat() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");


        Map<String, String> body = new HashMap<String, String>();
        body.put("SEGMENT", "Food");
        body.put("SUB_SEGMENT", "Restaurant");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateBussinessCatRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 7, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_UpdateBussinessCatWithInvalidSToken() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", inToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");


        Map<String, String> body = new HashMap<String, String>();
        body.put("SEGMENT", "Food");
        body.put("SUB_SEGMENT", "Restaurant");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateBussinessCatRequest.json";

        response = gs.getStartedResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 7, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_UpdateBussinessCatWithInvalidLeadId() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",InVleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");


        Map<String, String> body = new HashMap<String, String>();
        body.put("SEGMENT", "Food");
        body.put("SUB_SEGMENT", "Restaurant");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateBussinessCatRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 7, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_UpdateBussinessCatWithMissingParams() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");


        Map<String, String> body = new HashMap<String, String>();
        body.put("SEGMENT", "Food");
        body.put("SUB_SEGMENT", "Restaurant");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateBussinessCatRequest.json";

        response = gs.getStartedResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 7, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_UpdateBussinessCatWithMissingHeader() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");


        Map<String, String> body = new HashMap<String, String>();
        body.put("SEGMENT", "Food");
        body.put("SUB_SEGMENT", "Restaurant");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateBussinessCatRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 7, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_UpdateBussinessCatWithMissingBody() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");


        Map<String, String> body = new HashMap<String, String>();
        body.put("SEGMENT", "Food");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateBussinessCatRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 7, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_UpdateBussinessCatWithDifferentEntity() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "PUBLIC_LIMITED");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");


        Map<String, String> body = new HashMap<String, String>();
        body.put("SEGMENT", "Food");
        body.put("SUB_SEGMENT", "Restaurant");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateBussinessCatRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 7, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_UpdateBussinessCatWithInvalidSegment() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");


        Map<String, String> body = new HashMap<String, String>();
        body.put("SEGMENT", "Fooooooood");
        body.put("SUB_SEGMENT", "Restaurant");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateBussinessCatRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 7, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_UpdateBussinessCatWithInvalidSubSegment() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");


        Map<String, String> body = new HashMap<String, String>();
        body.put("SEGMENT", "Food");
        body.put("SUB_SEGMENT", "Restttaurantaurant");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateBussinessCatRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 7, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_UpdateBussinessCatWithInvalidIP() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "19342.13345.3440.13445");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");


        Map<String, String> body = new HashMap<String, String>();
        body.put("SEGMENT", "Food");
        body.put("SUB_SEGMENT", "Restaurant");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateBussinessCatRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 7, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_UpdateBussinessCatWithNoRequest() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");


        Map<String, String> body = new HashMap<String, String>();
        body.put("SEGMENT", "Food");
        body.put("SUB_SEGMENT", "Restaurant");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateBussinessCatRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 8, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdatePennyDrop() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC000543");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 8, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdatePennyDropWithInvalidSToken() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", inToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC000543");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 8, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdatePennyDropWithInvalidLeadId() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",InVleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC000543");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 8, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdatePennyDropWithMissingParams() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC000543");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 8, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdatePennyDropWithMissingHeader() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC000543");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 8, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdatePennyDropWithMissingBody() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("ifsc", "ICIC000543");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 8, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdatePennyDropWithInvalidEntity() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIIUYIDVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC000543");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 8, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdatePennyDropInvalidCacheControl() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-----cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC000543");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 8, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdatePennyDropInvalidBankAccount() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", "3467343sffs437483747384783874");
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC000543");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 8, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdatePennyDropWithExistingBankAccount() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", "***************");
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC000543");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 8, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdatePennyDropWithInvalidBankName() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICJDKSKDI bank");
        body.put("ifsc", "ICIC000543");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 8, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdatePennyDropWithInvalidIFSC() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICAXISIC90349543");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 8, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdatePennyDropWithIncorrectBankName() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "AXIS bank");
        body.put("ifsc", "ICIC000543");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 8, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdatePennyDropWithIncorrectIFSC() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "AXIS000543");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 9, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitBankLead() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC0004145");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 9, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitBankLeadWithInvalidSToken() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", inToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC0004145");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 9, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitBankLeadWithInvalidLeadId() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",InVleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC0004145");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 9, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitBankLeadWithMissingParams() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC0004145");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 9, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitBankLeadWithMissingHeader() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC0004145");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 9, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitBankLeadWithMissingBody() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC0004145");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 9, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitBankLeadWithInvalidEntity() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVVVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC0004145");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 9, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitBankLeadWithInvalidImei() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "*************,4658b4uf6rd4");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC0004145");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 9, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitBankLeadWithIncorrectBankAccount() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", "***************");
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC0004145");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 9, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitBankLeadWithIncorrectBankName() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "AXIS bank");
        body.put("ifsc", "ICIC0004145");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 9, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitBankLeadWithIncorrectIFSC() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "HDFC0004145");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 9, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitBankLeadWithInvalidBankAccount() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", "567234932756931275639127551923875193257913752937285");
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC0004145");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 9, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitBankLeadWithInvalidBankName() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICAXISICI bank");
        body.put("ifsc", "ICIC0004145");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 9, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitBankLeadWithInvalidIFSC() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICHDFCIC0004145");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 9, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitBankLeadWithNoRequest() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC0004145");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    //    @Test(priority = 10, description = "Upload Bank Proof", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC007_Upload_BankProof() throws Exception {
//
//        String endPoint = P.API.get("UploadBankProof");
//        Map<String, String> params = new HashMap<String, String>();
//        params.put("docType", "bankProof");
//        params.put("docProvided","BankStatement");
//        params.put("entityType", "INDIVIDUAL");
//        params.put("solutionType","diy_mco");
//        params.put("channel", "DIY_P4B_APP");
//        params.put("solutionLeadId",leadId);
//        params.put("pageNo","1");
//
//        Map<String, String> header = new HashMap<String, String>();
//        header.put("Content-Type", "multipart/form-data");
//        header.put("session_token", Token);
//
//
//        File uploadFile = new File(DocPath);
//
//        Response resp = UploadDocInAPI(uploadFile,params,header,endPoint);
//        System.out.print(resp.getBody().asString());
//
//        int statusCode = resp.getStatusCode();
//        Assert.assertEquals(statusCode, 200);
//
//        //uploading page 2 of aadhar
//        params.put("pageNo","2");
//        Response resp2 = UploadDocInAPI(uploadFile,params,header,endPoint);
//        int statusCode2 = resp2.getStatusCode();
//        Assert.assertEquals(statusCode2, 200);
//    }
    @Test(priority = 11, description = "updates the lead by adding shop address", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdateShopAddress() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "28.550667");
        body.put("longitudeOfShopVehicle", "77.268952");
        body.put("partialSave", "true");
        body.put("pincode", "180012");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LAT", "29.3222676");
        body.put("MAP_MY_INDIA_LONG", "76.3059143");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 11, description = "updates the lead by adding shop address", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdateShopAddressWithInvalidSToken() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", InVleadId);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "28.550667");
        body.put("longitudeOfShopVehicle", "77.268952");
        body.put("partialSave", "true");
        body.put("pincode", "180012");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LAT", "29.3222676");
        body.put("MAP_MY_INDIA_LONG", "76.3059143");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 11, description = "updates the lead by adding shop address", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdateShopAddressWithInvalidLeadId() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",InVleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "28.550667");
        body.put("longitudeOfShopVehicle", "77.268952");
        body.put("partialSave", "true");
        body.put("pincode", "180012");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LAT", "29.3222676");
        body.put("MAP_MY_INDIA_LONG", "76.3059143");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 11, description = "updates the lead by adding shop address", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdateShopAddressWithMissingParams() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "28.550667");
        body.put("longitudeOfShopVehicle", "77.268952");
        body.put("partialSave", "true");
        body.put("pincode", "180012");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LAT", "29.3222676");
        body.put("MAP_MY_INDIA_LONG", "76.3059143");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 11, description = "updates the lead by adding shop address", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdateShopAddressWithMissingHeader() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "28.550667");
        body.put("longitudeOfShopVehicle", "77.268952");
        body.put("partialSave", "true");
        body.put("pincode", "180012");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LAT", "29.3222676");
        body.put("MAP_MY_INDIA_LONG", "76.3059143");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 11, description = "updates the lead by adding shop address", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdateShopAddressWithMissingBody() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "28.550667");
        body.put("longitudeOfShopVehicle", "77.268952");
        body.put("partialSave", "true");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LONG", "76.3059143");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 11, description = "updates the lead by adding shop address", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdateShopAddressWithInvalidEntity() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVVIIDDUUAALL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "28.550667");
        body.put("longitudeOfShopVehicle", "77.268952");
        body.put("partialSave", "true");
        body.put("pincode", "180012");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LAT", "29.3222676");
        body.put("MAP_MY_INDIA_LONG", "76.3059143");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 11, description = "updates the lead by adding shop address", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdateShopAddressWithInvalidIp() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "100092.135.0.15");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "28.550667");
        body.put("longitudeOfShopVehicle", "77.268952");
        body.put("partialSave", "true");
        body.put("pincode", "180012");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LAT", "29.3222676");
        body.put("MAP_MY_INDIA_LONG", "76.3059143");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 11, description = "updates the lead by adding shop address", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdateShopAddressWithInvalidCity() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jaaaaaammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "28.550667");
        body.put("longitudeOfShopVehicle", "77.268952");
        body.put("partialSave", "true");
        body.put("pincode", "180012");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LAT", "29.3222676");
        body.put("MAP_MY_INDIA_LONG", "76.3059143");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 11, description = "updates the lead by adding shop address", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdateShopAddressWithInvalidPinCode() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "28.550667");
        body.put("longitudeOfShopVehicle", "77.268952");
        body.put("partialSave", "true");
        body.put("pincode", "1800443412");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LAT", "29.3222676");
        body.put("MAP_MY_INDIA_LONG", "76.3059143");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 11, description = "updates the lead by adding shop address", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdateShopAddressWithInvalidLAT() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "28.550667");
        body.put("longitudeOfShopVehicle", "77.268952");
        body.put("partialSave", "true");
        body.put("pincode", "180012");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LAT", "2000gf009.3222676");
        body.put("MAP_MY_INDIA_LONG", "76.3059143");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 11, description = "updates the lead by adding shop address", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdateShopAddressWithInvalidLONG() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "28.550667");
        body.put("longitudeOfShopVehicle", "737.268952");
        body.put("partialSave", "true");
        body.put("pincode", "180012");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LAT", "29.3222676");
        body.put("MAP_MY_INDIA_LONG", "7dffd6.3059143");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 11, description = "updates the lead by adding shop address", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdateShopAddressWithInvalidLONGOfShopV() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "28fgdf.550667");
        body.put("longitudeOfShopVehicle", "737.268952");
        body.put("partialSave", "true");
        body.put("pincode", "180012");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LAT", "29.3222676");
        body.put("MAP_MY_INDIA_LONG", "7dffd6.3059143");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 11, description = "updates the lead by adding shop address", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdateShopAddressWithInvalidLATOfShopV() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "2dfdf45358.550667");
        body.put("longitudeOfShopVehicle", "737.268952");
        body.put("partialSave", "true");
        body.put("pincode", "180012");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LAT", "29.3222676");
        body.put("MAP_MY_INDIA_LONG", "7dffd6.3059143");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 12, description = "Submit The Lead / business review ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitTheLead() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("partialSave", "false");
        body.put("IS_AGREEMENT_ACCEPTED", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitTheLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 12, description = "Submit The Lead / business review ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitTheLeadWithInvalidSToken() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", inToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("partialSave", "false");
        body.put("IS_AGREEMENT_ACCEPTED", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitTheLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 12, description = "Submit The Lead / business review ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitTheLeadWithInvalidLeadId() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",InVleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("partialSave", "false");
        body.put("IS_AGREEMENT_ACCEPTED", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitTheLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 12, description = "Submit The Lead / business review ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitTheLeadWithMissingParams() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("partialSave", "false");
        body.put("IS_AGREEMENT_ACCEPTED", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitTheLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 12, description = "Submit The Lead / business review ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitTheLeadWithMissingHeader() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("partialSave", "false");
        body.put("IS_AGREEMENT_ACCEPTED", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitTheLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 12, description = "Submit The Lead / business review ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitTheLeadWithMissingBody() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("partialSave", "false");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitTheLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 12, description = "Submit The Lead / business review ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitTheLeadWithInvalidEntity() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIvvvvVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("partialSave", "false");
        body.put("IS_AGREEMENT_ACCEPTED", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitTheLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 12, description = "Submit The Lead / business review ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitTheLeadWithInvalidCache() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-2323--2424-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("partialSave", "false");
        body.put("IS_AGREEMENT_ACCEPTED", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitTheLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 12, description = "Submit The Lead / business review ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitTheLeadWithNoRequest() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("partialSave", "false");
        body.put("IS_AGREEMENT_ACCEPTED", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitTheLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 12, description = "Submit The Lead / business review ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_SubmitTheLeadWhereAgreementAccepetedIsFalse() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId",VleadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("partialSave", "false");
        body.put("IS_AGREEMENT_ACCEPTED", "false");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitTheLeadRequest.json";

        response = gs.getStartedResponse(RequestPath,header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }



    public Response UploadDocInAPI(File uploadFile, Map<String, String> params,Map<String, String> header,String endPoint) throws Exception{

        Response resp = null;
        String baseURI = P.API.get("api_url");

        RequestSpecification spec;

        try {
            spec = new RequestSpecBuilder().setBaseUri(baseURI).build();
            resp = RestAssured.given().multiPart(uploadFile).spec(spec).relaxedHTTPSValidation().queryParams(params).headers(header)
                    .post(endPoint);
        }
        catch (Exception e){
            e.printStackTrace();
        }
        return resp;

    }

}
