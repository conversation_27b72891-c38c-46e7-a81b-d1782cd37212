package OCL.DIY.MCO;

import Request.DIYMCO.FetchCat;
import Request.MerchantService.v2.lending.dataUpdate.CKYCCallBack;
import Services.DIYMCO.FetchCatSubCat;
import Services.Subscription.SubscriptionPlan;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.util.HashMap;
import java.util.Map;

import static com.goldengate.common.BaseMethod.createAuth0JwsHMAC;

public class Fetch_Category extends FetchCatSubCat{

    public static String clientid= "oe-subscription-client";
    public static String mid = "DcFcoJ97348642540712";
//    public static long merchantMobileNumber = "9983886781";
    String successMessage = "SUCCESS";
    String merchantMobileNumber = "8247842174";
    String Token;

    @Test(priority = 1,description = "Fetches the category based on the key ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchCategoryBasedOnKey()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token","ed729696-87ba-4cb9-bc10-49a6e9ae7400");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);

        int statusCode = response.getStatusCode();
       // Assert.assertEquals(statusCode, 200);

        String displayMessage = response.jsonPath().getString("displayMessage");
        Assert.assertEquals(response.jsonPath().getString("displayMessage"),successMessage);
    }

    @Test(priority = 1,description = "checking if the given session_token is valid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void sessionTokenCheck1()
    {

        Token = BaseMethod.ApplicantToken(merchantMobileNumber, "paytm@123");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);
    }

    @Test(priority = 1,description = "Fetches the category based on the key ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchCategoryBasedOnKey2()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token","ed729696-87ba-4cb9-bc10-49a6e9ae7400");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String displayMessage = response.jsonPath().getString("displayMessage");
        Assert.assertEquals(response.jsonPath().getString("displayMessage"),successMessage);
    }

    @Test(priority = 1,description = "checking if the given session_token is valid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void sessionTokenCheck2()
    {

        Token = BaseMethod.ApplicantToken(merchantMobileNumber, "paytm@123");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);
    }

    @Test(priority = 1,description = "Fetches the category based on the key ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchCategoryBasedOnKey3()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token","ed729696-87ba-4cb9-bc10-49a6e9ae7400");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String displayMessage = response.jsonPath().getString("displayMessage");
        Assert.assertEquals(response.jsonPath().getString("displayMessage"),successMessage);
    }

    @Test(priority = 1,description = "checking if the given session_token is valid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void sessionTokenCheck4()
    {

        Token = BaseMethod.ApplicantToken(merchantMobileNumber, "paytm@123");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);
    }

    @Test(priority = 1,description = "Fetches the category based on the key ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchCategoryBasedOnKey4()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token","ed729696-87ba-4cb9-bc10-49a6e9ae7400");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String displayMessage = response.jsonPath().getString("displayMessage");
        Assert.assertEquals(response.jsonPath().getString("displayMessage"),successMessage);
    }

    @Test(priority = 1,description = "checking if the given session_token is valid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void sessionTokenCheck26()
    {

        Token = BaseMethod.ApplicantToken(merchantMobileNumber, "paytm@123");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);
    }

    @Test(priority = 1,description = "Fetches the category based on the key ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchCategoryBasedOnKey5()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token","ed729696-87ba-4cb9-bc10-49a6e9ae7400");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String displayMessage = response.jsonPath().getString("displayMessage");
        Assert.assertEquals(response.jsonPath().getString("displayMessage"),successMessage);
    }

    @Test(priority = 1,description = "checking if the given session_token is valid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void sessionTokenCheck6()
    {

        Token = BaseMethod.ApplicantToken(merchantMobileNumber, "paytm@123");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);
    }

    @Test(priority = 1,description = "Fetches the category based on the key ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchCategoryBasedOnKey7()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token","ed729696-87ba-4cb9-bc10-49a6e9ae7400");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String displayMessage = response.jsonPath().getString("displayMessage");
        Assert.assertEquals(response.jsonPath().getString("displayMessage"),successMessage);
    }

    @Test(priority = 1,description = "checking if the given session_token is valid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void sessionTokenCheck8()
    {

        Token = BaseMethod.ApplicantToken(merchantMobileNumber, "paytm@123");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);
    }

    @Test(priority = 1,description = "Fetches the category based on the key ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchCategoryBasedOnKey9()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token","ed729696-87ba-4cb9-bc10-49a6e9ae7400");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String displayMessage = response.jsonPath().getString("displayMessage");
        Assert.assertEquals(response.jsonPath().getString("displayMessage"),successMessage);
    }

    @Test(priority = 1,description = "checking if the given session_token is valid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void sessionTokenCheck()
    {

        Token = BaseMethod.ApplicantToken(merchantMobileNumber, "paytm@123");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);
    }

    @Test(priority = 1,description = "Fetches the category based on the key ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchCategoryBasedOnKey10()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token","ed729696-87ba-4cb9-bc10-49a6e9ae7400");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String displayMessage = response.jsonPath().getString("displayMessage");
        Assert.assertEquals(response.jsonPath().getString("displayMessage"),successMessage);
    }

    @Test(priority = 1,description = "checking if the given session_token is valid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void sessionTokenCheck11()
    {

        Token = BaseMethod.ApplicantToken(merchantMobileNumber, "paytm@123");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);
    }
    @Test(priority = 1,description = "Fetches the category based on the key ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchCategoryBasedOnKey12()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token","ed729696-87ba-4cb9-bc10-49a6e9ae7400");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String displayMessage = response.jsonPath().getString("displayMessage");
        Assert.assertEquals(response.jsonPath().getString("displayMessage"),successMessage);
    }

    @Test(priority = 1,description = "checking if the given session_token is valid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void sessionTokenCheck13()
    {

        Token = BaseMethod.ApplicantToken(merchantMobileNumber, "paytm@123");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);
    }
    @Test(priority = 1,description = "Fetches the category based on the key ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchCategoryBasedOnKey14()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token","ed729696-87ba-4cb9-bc10-49a6e9ae7400");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String displayMessage = response.jsonPath().getString("displayMessage");
        Assert.assertEquals(response.jsonPath().getString("displayMessage"),successMessage);
    }

    @Test(priority = 1,description = "checking if the given session_token is valid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void sessionTokenCheck15()
    {

        Token = BaseMethod.ApplicantToken(merchantMobileNumber, "paytm@123");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);
    }
    @Test(priority = 1,description = "Fetches the category based on the key ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchCategoryBasedOnKey16()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token","ed729696-87ba-4cb9-bc10-49a6e9ae7400");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String displayMessage = response.jsonPath().getString("displayMessage");
        Assert.assertEquals(response.jsonPath().getString("displayMessage"),successMessage);
    }

    @Test(priority = 1,description = "checking if the given session_token is valid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void sessionTokenCheck17()
    {

        Token = BaseMethod.ApplicantToken(merchantMobileNumber, "paytm@123");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);
    }
    @Test(priority = 1,description = "Fetches the category based on the key ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchCategoryBasedOnKey18()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token","ed729696-87ba-4cb9-bc10-49a6e9ae7400");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String displayMessage = response.jsonPath().getString("displayMessage");
        Assert.assertEquals(response.jsonPath().getString("displayMessage"),successMessage);
    }

    @Test(priority = 1,description = "checking if the given session_token is valid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void sessionTokenCheck19()
    {

        Token = BaseMethod.ApplicantToken(merchantMobileNumber, "paytm@123");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);
    }
    @Test(priority = 1,description = "Fetches the category based on the key ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchCategoryBasedOnKey20()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token","ed729696-87ba-4cb9-bc10-49a6e9ae7400");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String displayMessage = response.jsonPath().getString("displayMessage");
        Assert.assertEquals(response.jsonPath().getString("displayMessage"),successMessage);
    }

    @Test(priority = 1,description = "checking if the given session_token is valid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void sessionTokenCheck21()
    {

        Token = BaseMethod.ApplicantToken(merchantMobileNumber, "paytm@123");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);
    }

    @Test(priority = 1,description = "Fetches the category based on the key ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchCategoryBasedOnKey22()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token","ed729696-87ba-4cb9-bc10-49a6e9ae7400");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String displayMessage = response.jsonPath().getString("displayMessage");
        Assert.assertEquals(response.jsonPath().getString("displayMessage"),successMessage);
    }

    @Test(priority = 1,description = "checking if the given session_token is valid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void sessionTokenCheck23()
    {

        Token = BaseMethod.ApplicantToken(merchantMobileNumber, "paytm@123");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);
    }

    @Test(priority = 1,description = "Fetches the category based on the key ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchCategoryBasedOnKey24()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token","ed729696-87ba-4cb9-bc10-49a6e9ae7400");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String displayMessage = response.jsonPath().getString("displayMessage");
        Assert.assertEquals(response.jsonPath().getString("displayMessage"),successMessage);
    }

    @Test(priority = 1,description = "checking if the given session_token is valid", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void sessionTokenCheck25()
    {

        Token = BaseMethod.ApplicantToken(merchantMobileNumber, "paytm@123");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution","diy_mco");
        params.put("entityType","INDIVIDUAL");

        Map<String, String> body = new HashMap<String, String>();

        Response response = FetchCatSubCatResponse(headers,params);
    }





}
