package OCL.DIY.MCO;


import Services.DIYMCO.CreationOfLead;
import Services.DIYMCO.getStarted;
import Services.DIYProfileUpdate.BossToOeLeadUpdate;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;


import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class CreateLead extends BaseMethod {
    String DocPath = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";
    String Token;
    String RequestPath;
    String leadId;
    String custId;
    String mid;
    String QCAgentsToken = "";
    String BusinessOwnerPhotoDMSID = "";
    String AadharPhotoDMSID1 = "";
    String AadharPhotoDMSID2 = "";
    String PanphotoDMSID = "";
    CreationOfLead COF = new CreationOfLead();

    Utilities Util = new Utilities();
    String pan = Util.randomIndividualPANValueGenerator();
    String num2 = Util.randomMobileNumberGenerator();
    String num = num2.replaceFirst("5", "9");
    String Aadhaar = Util.ValidAadhaar();
    String BankAccNo = Util.generateRandomBankAccountNumber();
    String PAN = Util.randomIndividualPANValueGenerator();

    private BossToOeLeadUpdate bossToOeLeadUpdate = new BossToOeLeadUpdate();

    getStarted gs = new getStarted();
    ;
    Map<String, String> header = new HashMap<String, String>();
    Response response = null;

    @BeforeClass
    public void BeforeOperation() {
        if (Util.createNewAuthUser(num, "Paytm@123")) {
            Token = BaseMethod.ApplicantToken(num, "Paytm@123");
        } else {
            Assert.assertFalse(false, "Number was not registered at Oauth end");
        }
    }


    @Test(priority = 1, description = "Creation of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createLead() throws JSONException {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("isKycConsentCaptured", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreateRequest.json";

        response = COF.CreationOfLeadResponseWithParam(RequestPath, header, params, body);


        int statusCode = response.getStatusCode();
        String displayMessage = response.jsonPath().getString("displayMessage");
        if ((statusCode == 400 && displayMessage.contains("Please relogin. You are already onboarded as a Paytm Merchant.")) || (statusCode == 500 && displayMessage.contains("We are unable to process your request. Please try again after sometime to continue."))) {
            createLead();
        } else {
            Assert.assertEquals(statusCode, 200);
            leadId = response.jsonPath().getString("leadId");
        }
    }


    @Test(priority = 2, description = "Get Started of a lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void GetStarted() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("WHATSAPP_NOTIFICATION_CONSENT", "true");
        body.put("REFERRER_CODE", "");
        body.put("MERCHANT_EMAIL", "<EMAIL>");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOGetStartedRequest.json";

        response = gs.getStartedResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 3, description = "Upload aadhar ocr", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AadhaarOcr() throws Exception {

        String endPoint = P.API.get("UploadAadhaarOcr");
        Map<String, String> params = new HashMap<String, String>();
        params.put("fileType", "AADHAR");
        params.put("leadId", leadId);

        Map<String, String> header_ocr = new HashMap<String, String>();
        header_ocr.put("Content-Type", "multipart/form-data");
        header_ocr.put("session_token", Token);

        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile, params, header_ocr, endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 4, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UpdateLeadAadhaar() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("aadharNumber", Aadhaar);
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "true");
        body.put("AADHAR_NO_MISMATCHED", "false");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Female");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");
        body.put("userAddressLine1", "9187");
        body.put("userAddressLine2", "Ahxhshj");
        body.put("userAddressLine3", "Sjjsiskso");
        body.put("userAddressPincode", "126102");
        body.put("userAddressState", "Haryana");
        System.out.println("Aadhar Number: " + Aadhaar);

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

        response = gs.getStartedResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 5, description = "Upload aadhar ocr", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void Upload_AadhaarDoc() throws Exception {

        String endPoint = P.API.get("UploadAadhaarDoc");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "poi");
        params.put("docProvided", "aadhaar");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType", "diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", Token);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile, params, header, endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        //uploading page 2 of aadhar
        params.put("pageNo", "2");
        Response resp2 = UploadDocInAPI(uploadFile, params, header, endPoint);
        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }

    @Test(priority = 6, description = "Upload Merchant Selfie", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void Upload_MerchantSelfie() throws Exception {

        String endPoint = P.API.get("UploadMerchantSelfie");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "businessOwnerPhoto");
        params.put("docProvided", "businessOwnerPhoto");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType", "diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", Token);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile, params, header, endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        //uploading page 2 of aadhar
        params.put("pageNo", "2");
        Response resp2 = UploadDocInAPI(uploadFile, params, header, endPoint);
        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }




    @Test(priority = 7, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UpdateBusinessCat() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");


        Map<String, String> body = new HashMap<String, String>();
        body.put("SEGMENT", "Food");
        body.put("SUB_SEGMENT", "Restaurant");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateBussinessCatRequest.json";

        response = gs.getStartedResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 8, description = "Update aadhaar lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UpdatePennyDrop() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC000543");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";

        response = gs.getStartedResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 9, description = "submit the updated lead containing bank details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void SubmitBankLead() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC0004145");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 10, description = "Upload Bank Proof", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_Upload_BankProof() throws Exception {

        String endPoint = P.API.get("UploadBankProof");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "bankProof");
        params.put("docProvided","BankStatement");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType","diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId",leadId);
        params.put("pageNo","1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", Token);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile,params,header,endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        //uploading page 2 of aadhar
        params.put("pageNo","2");
        Response resp2 = UploadDocInAPI(uploadFile,params,header,endPoint);
        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }


    @Test(priority = 11, description = "Enter non-individual PAN manually", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void enterIndividualPanManually() throws JSONException {
        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<>();
        body.put("partialSave", "true");
        body.put("pan", PAN);
        body.put("gstin", "");
        body.put("PAN", PAN);
        body.put("BUSINESS_ENTITY", "INDIVIDUAL");
        body.put("KYB_BUSINESS_ID", "");

        RequestPath = "MerchantService/v1/sdMerchant/lead/updatePanLeadRequest.json";
        response = gs.getStartedResponse(RequestPath, header, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
    }

    @Test(priority = 12, description = "Upload Merchant Selfie", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void Upload_panDocument() throws Exception {

        String endPoint = P.API.get("UploadMerchantSelfie");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "pan");
        params.put("docProvided", "pan");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType", "diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", Token);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile, params, header, endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }


    @Test(priority = 13, description = "updates the lead by adding shop address", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UpdateShopAddress() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "28.550667");
        body.put("longitudeOfShopVehicle", "77.268952");
        body.put("partialSave", "true");
        body.put("pincode", "180012");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LAT", "29.3222676");
        body.put("MAP_MY_INDIA_LONG", "76.3059143");
//        body.put("areaOfEnrollment", "Trikuta Outer Road");
//        body.put("cityOfEnrollment", "Punjab");
//        body.put("landmark", "lol");
//        body.put("latitudeOfShopVehicle", "31.92064");
//        body.put("longitudeOfShopVehicle", "75.47558");
//        body.put("partialSave", "true");
//        body.put("pincode", "143528");
//        body.put("shopAddress", "Gurdaspur");
//        body.put("state", "Punjab");
//        body.put("MAP_MY_INDIA_LAT", "31.92064");
//        body.put("MAP_MY_INDIA_LONG", "75.47558");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";

        response = gs.getStartedResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 14, description = "Submit The Lead / business review ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void SubmitTheLead() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", Token);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("partialSave", false);
        body.put("IS_AGREEMENT_ACCEPTED", true);

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitTheLeadRequest.json";
        response = bossToOeLeadUpdate.bossToOeLeadUpdate(RequestPath, header, params, body);


        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }


    public Response UploadDocInAPI(File uploadFile, Map<String, String> params, Map<String, String> header, String endPoint) throws Exception {

        Response resp = null;
        String baseURI = P.API.get("api_url");

        RequestSpecification spec;

        try {
            spec = new RequestSpecBuilder().setBaseUri(baseURI).build();
            resp = RestAssured.given().multiPart(uploadFile).spec(spec).relaxedHTTPSValidation().queryParams(params).headers(header)
                    .post(endPoint);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resp;

    }


    public String createLead(String newToken) throws JSONException {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", newToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("isKycConsentCaptured", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreateRequest.json";

        response = COF.CreationOfLeadResponseWithParam(RequestPath, header, params, body);

        leadId = response.jsonPath().getString("leadId");

        if (response.getStatusCode() == 200) {
            leadId = response.jsonPath().getString("leadId");
            return leadId;
        } else //recall the method if lead is not created
        {
            return createLead(newToken);
        }

    }

    public void GetStarted(String newLeadId, String newToken) {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", newLeadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("session_token", newToken);
        header.put("Content-Type", "application/json");
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("WHATSAPP_NOTIFICATION_CONSENT", "true");
        body.put("REFERRER_CODE", "");
        body.put("MERCHANT_EMAIL", "<EMAIL>");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOGetStartedRequest.json";

        response = gs.getStartedResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    public void AadhaarOcr(String newLeadId, String newToken) throws Exception {

        String endPoint = P.API.get("UploadAadhaarOcr");
        Map<String, String> params = new HashMap<String, String>();
        params.put("fileType", "AADHAR");
        params.put("leadId", newLeadId);

        Map<String, String> header_ocr = new HashMap<String, String>();
        header_ocr.put("Content-Type", "multipart/form-data");
        header_ocr.put("session_token", newToken);

        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile, params, header_ocr, endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    public void UpdateLeadAadhaar(String newLeadId, String newToken) {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", newLeadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", newToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<String, String>();
        body.put("aadharNumber", Aadhaar);
        body.put("partialSave", "true");
        body.put("AADHAR_OCR_DONE", "true");
        body.put("AADHAR_NO_MISMATCHED", "false");
        body.put("AADHAR_NO_NOT_READABLE", "false");
        body.put("GENDER", "Female");
        body.put("MERCHANT_DOB", "12/27/1988");
        body.put("NAME_AS_PER_AADHAR", "ANMOL JAIN");
        body.put("userAddressCity", "Jind");
        body.put("userAddressLandMark", "ANMOL JAIN");
        body.put("userAddressLine1", "9187");
        body.put("userAddressLine2", "Ahxhshj");
        body.put("userAddressLine3", "Sjjsiskso");
        body.put("userAddressPincode", "126102");
        body.put("userAddressState", "Haryana");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";

        response = gs.getStartedResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    public void Upload_AadhaarDoc(String leadId, String newToken) throws Exception {

        String endPoint = P.API.get("UploadAadhaarDoc");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "poi");
        params.put("docProvided", "aadhaar");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType", "diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", newToken);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile, params, header, endPoint);

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        //uploading page 2 of aadhar
        params.put("pageNo", "2");
        Response resp2 = UploadDocInAPI(uploadFile, params, header, endPoint);
        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }


    public void Upload_MerchantSelfie(String leadId, String newToken) throws Exception {

        String endPoint = P.API.get("UploadMerchantSelfie");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "businessOwnerPhoto");
        params.put("docProvided", "businessOwnerPhoto");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType", "diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", newToken);


        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile, params, header, endPoint);
        System.out.print(resp.getBody().asString());

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        //uploading page 2 of aadhar
        params.put("pageNo", "2");
        Response resp2 = UploadDocInAPI(uploadFile, params, header, endPoint);
        int statusCode2 = resp2.getStatusCode();
        Assert.assertEquals(statusCode2, 200);
    }

    public String UpdateLeadPan(String leadId, String newToken) {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", newToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");

        Map<String, String> body = new HashMap<>();
        body.put("partialSave", "true");
        body.put("pan", PAN);
        body.put("gstin", "");
        body.put("PAN", PAN);
        body.put("BUSINESS_ENTITY", "INDIVIDUAL");
        body.put("KYB_BUSINESS_ID", "");

        RequestPath = "MerchantService/v1/sdMerchant/lead/updatePanLeadRequest.json";
        response = gs.getStartedResponse(RequestPath, header, params, body);

        Assert.assertEquals(response.getStatusCode(), 200);
        return PAN;
    }


    public void Upload_panDocument(String leadId, String newToken) throws Exception {

        String endPoint = P.API.get("UploadMerchantSelfie");
        Map<String, String> params = new HashMap<String, String>();
        params.put("docType", "pan");
        params.put("docProvided", "pan");
        params.put("entityType", "INDIVIDUAL");
        params.put("solutionType", "diy_mco");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "multipart/form-data");
        header.put("session_token", newToken);

        File uploadFile = new File(DocPath);

        Response resp = UploadDocInAPI(uploadFile, params, header, endPoint);

        int statusCode = resp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }


    public void UpdateBusinessCat(String newLeadId, String newToken) {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", newLeadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", newToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");
        header.put("channel", "DIY_P4B_APP");


        Map<String, String> body = new HashMap<String, String>();
        body.put("SEGMENT", "Gas and Petrol");
        body.put("SUB_SEGMENT", "HPCL Pump");
        body.put("partialSave", "true");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateBussinessCatRequest.json";

        response = gs.getStartedResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }


    public void UpdatePennyDrop(String newLeadId, String newToken) {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", newLeadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", newToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "true");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC0001070");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdatePennyDropRequest.json";

        response = gs.getStartedResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }


    public void SubmitBankLead(String newLeadId, String newToken) {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", newLeadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", newToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("onlyValidateBankDetails", "false");
        body.put("partialSave", "true");
        body.put("bankAccountNumber", BankAccNo);
        body.put("bankName", "ICICI bank");
        body.put("ifsc", "ICIC0001070");


        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitUpdatedBankDetailsLeadRequest.json";

        response = gs.getStartedResponse(RequestPath, header, params, body);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    public void UpdateShopAddress(String newLeadId, String newToken) {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", newLeadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", newToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, String> body = new HashMap<String, String>();
        body.put("areaOfEnrollment", "Trikuta Outer Road");
        body.put("cityOfEnrollment", "Jammu");
        body.put("landmark", "lol");
        body.put("latitudeOfShopVehicle", "28.550667");
        body.put("longitudeOfShopVehicle", "77.268952");
        body.put("partialSave", "true");
        body.put("pincode", "180012");
        body.put("shopAddress", "F1");
        body.put("state", "Jammu and Kashmir");
        body.put("MAP_MY_INDIA_LAT", "29.3222676");
        body.put("MAP_MY_INDIA_LONG", "76.3059143");

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAddShopAddressRequest.json";

        response = gs.getStartedResponse(RequestPath, header, params, body);

    }


    public void SubmitTheLead(String leadId, String newToken) {

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "diy_mco");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("leadId", leadId);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("session_token", newToken);
        header.put("androidId", "AashitAndroid");
        header.put("browserName", "chrome");
        header.put("browserVersion", "4.6.3");
        header.put("cache-control", "no-cache");
        header.put("ipAddress", "************");
        header.put("latitude", "28.32");
        header.put("longitude", "77.213");
        header.put("imei", "1234");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("partialSave", false);
        body.put("IS_AGREEMENT_ACCEPTED", true);

        RequestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOSubmitTheLeadRequest.json";
        response = bossToOeLeadUpdate.bossToOeLeadUpdate(RequestPath, header, params, body);
    }

//    @Test(priority = 14, description = "", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void DiyMcoLeadFetchDocStatus() throws Exception {
//        custId = GetResourceOwnerId(num, "Paytm@123");
//
//        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();
//        Map<String, String> headers = new HashMap<>();
//
//        headers.put("session_token", Token); // Updated from first list
//        headers.put("accept", "application/json, text/plain, */*"); // No change
//        headers.put("content-type", "application/json; charset=UTF-8");
//        headers.put("version", "7.2.8"); // No change
//        headers.put("deviceidentifier", "Xiaomi-RedmiNote9Pro-efc28fc97cede738"); // Updated from first list
//        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
//        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged
//
//        Map<String, String> queryParams = new HashMap<>();
//        queryParams.put("solution", "diy_mco");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("leadId", leadId);
//        queryParams.put("merchantCustId", custId);
//        queryParams.put("channel", "DIY_P4B_APP");
//
////
////        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj, headers, queryParams);
////
////        Assert.assertEquals(respObj.getStatusCode(), 200);
////        BusinessOwnerPhotoDMSID = respObj.path("uploadedDocDetailsSet[0].uploadedDocs[0].uuid");
////        AadharPhotoDMSID1 = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
////        AadharPhotoDMSID2 = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['2']");
////        PanphotoDMSID = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].uuid");
////        System.out.println(BusinessOwnerPhotoDMSID + AadharPhotoDMSID1 + AadharPhotoDMSID2 + PanphotoDMSID);
//
//    }


//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "DIY MCO Retail Onboarding QC", priority = 14, dependsOnMethods = "DiyMcoLeadFetchDocStatus")
//    public void DiyMcoRetailOnboardingLeadQC() throws Exception {
//
//        DBConnection dbConnectionObj = new DBConnection();
//
//        int Ubmid = dbConnectionObj.getUserBusinessMappingId(num, "diy_mco");
//
//        dbConnectionObj.assignAgentViaDB("1152", Ubmid);
//
//
//        Long DiyMCOIndividualWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);
//
//
//        //Save value of DiyMCOIndividualWorkflowStatusId in string
//        String wfsid = String.valueOf(DiyMCOIndividualWorkflowStatusId);
//
//        RequestPath = "MerchantServiceOEPanelV1EditLead/EditLeadMcoIndividualWithPan.json";
//        EditLead EditLeadObj = new EditLead(leadId, RequestPath);
////        EditLeadObj.getProperties().setProperty("uuidAadharPhoto1", AadharPhotoDMSID1);
////        EditLeadObj.getProperties().setProperty("uuidAadharPhoto2", AadharPhotoDMSID2);
////        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto", BusinessOwnerPhotoDMSID);
////        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto1", BusinessOwnerPhotoDMSID);
////        EditLeadObj.getProperties().setProperty("uuidPanPhoto", PanphotoDMSID);
////        EditLeadObj.getProperties().setProperty("workflowStatusId", wfsid);
////        Map<String, String> queryParams = new HashMap<>();
////        queryParams.put("action", "SUBMIT");
////        Map<String, String> headers = new HashMap<>();
////        headers.put("Host", "goldengate-staging6.paytm.com");
////        headers.put("session_token", QCAgentsToken);
////        headers.put("content-type", "application/json; charset=UTF-8");
////        headers.put("Cookie", XMWCookie);
////        Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);
//
//        Thread.sleep(120000); // Wait for 3 minutes
//    }

//    @Test(priority = 15, description = "add channel callback", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void addChannelCallback() throws Exception {
//        // Add channel callback
//        Map<String, String> headers = new HashMap<>();
//        headers.put("content-type", "application/json");
//
//        Algorithm algorithm = Algorithm.HMAC256("e943ccc0-efc5-47e4-a701-bf9f3956bdaf");
//        // Get current timestamp in the local timezone
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
//        LocalDateTime now = LocalDateTime.now();  // System's default local time
//        String formattedTimestamp = now.format(formatter);
//
//        // Build the JWT token with claims
//        String jwtToken = JWT.create()
//                .withClaim("timestamp", formattedTimestamp)
//                .withClaim("clientId", "OE_INTERNAL")
//                .withClaim("iss", "OE")
//                .sign(algorithm);
//
//        // Step 3: Add JWT token to the headers
//        headers.put("Authorization", jwtToken);
//
//        Map<String, String> body = new HashMap<>();
//        body.put("mid", mid);
//        body.put("leadId", leadId);
//        body.put("bankName", "PTYES");
//        body.put("responseCode", "000");
//        body.put("responseMessage", "SUCCESS");
//
////        requestPath = "MerchantService/v1/boss/AddChannelCallback.json";
////        response = channelCallback.addChannelCallback(requestPath, headers, body);
////        Thread.sleep(40000); // Wait for 2 minutes
//
//    }

}
