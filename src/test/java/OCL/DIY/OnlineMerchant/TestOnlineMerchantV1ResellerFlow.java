package OCL.DIY.OnlineMerchant;


import Request.MerchantService.v1.upgradeMid.lead.ValidateBankDetailsOnline;
import Request.oAuth.oAuthWormhole.CreateUser;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;


public class TestOnlineMerchantV1ResellerFlow extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(TestOnlineMerchantV1ResellerFlow.class);
    MiddlewareServices middlewareServiceObject = new MiddlewareServices();
    String sessionToken = "";
    String leadId = "";
    String agentPassword = "paytm@123";
    public static String bankaccountholderName;
    public static String bankdetailsUuid;
    public static boolean namematchStatus = true;
    public static String leadID;
    public static String solID;
    public static String CustId = "";
    public static String MID = "";

    public static final String SOLUTION = "online_merchant";
    public static final String SOLUTION_TYPE_LEVEL_2 = "CLIX";
    public static final String SOLUTION_TYPE_LEVEL_3 = "Unsecured_Short_term_Loan_Simplified";
    public static final String ENTITY_TYPE = "INDIVIDUAL";
    public static final String CHANNEL = "UMP_WEB";
    public static String PAN = "";
    public static final String DOB = "1989-04-21";
    public static final String EMAIL = "";
    public static final String ISSUER = "OE";
    public static final String CLIENT_ID = "LMS";
    public static final String WORKFLOW_VERSION = "V2";

    public static String MOBILE = "";
    public static String BUSINESSPAN = "";
    public static String EntityTypeUL = "PROPRIETORSHIP";
    public static final String BUSINESSNAME = "TestBeneficiary";
    public static final String CATEGORY = "BFSI";
    public static final String SUBCATEGORY = "Loans";
    public static final String BANKNAME = "ICICI Bank";
    public static String BANKACCOUNTNUMBER = "************";

    public static final String IFSC = "Icic0006622";

    Map<String, String> commonHeaders;

    @BeforeClass()
    public void intitializeInputData() throws IOException {

        Utilities accObj = new Utilities();
        MOBILE = accObj.randomMobileNumberGenerator();
        LOGGER.info("New Number is : " + MOBILE);
        //LOGGER.info(" Before Suite Method for Agent Login ");
        CreateUser OauthObj = new CreateUser();
        OauthObj.setHeader("Content-Type", "application/json");
        OauthObj.getProperties().setProperty("mobile", MOBILE);
        OauthObj.getProperties().setProperty("loginPassword", "paytm@123");
        Response OauthResp = OauthObj.callAPI();

        sessionToken = ApplicantToken(MOBILE, agentPassword);
        //LOGGER.info("Applicant Token for Lending : " + sessionToken);
        commonHeaders = setcommonHeaders();

        switch (EntityTypeUL) {
            case "PUBLIC_LIMITED":
            case "PRIVATE_LIMITED": {
                LOGGER.info("Entity is : " + EntityTypeUL);
                Utilities UtilObj = new Utilities();
                PAN = UtilObj.randomPublicPANValueGenerator();
                LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

                break;
            }
            case "PROPRIETORSHIP": {
                LOGGER.info("Entity is : " + EntityTypeUL);
                Utilities UtilObj = new Utilities();
                PAN = UtilObj.randomIndividualPANValueGenerator();
                LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

                break;
            }
            case "TRUST": {
                LOGGER.info("Entity is : " + EntityTypeUL);
                Utilities UtilObj = new Utilities();
                PAN = UtilObj.randomTrustPANValueGenerator();
                LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

                break;
            }
            case "SOCIETY_ASSOCIATION_CLUB": {
                LOGGER.info("Entity is : " + EntityTypeUL);
                Utilities UtilObj = new Utilities();
                PAN = UtilObj.randomSocietyPANValueGenerator();
                LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

                break;
            }
            case "PARTNERSHIP": {
                LOGGER.info("Entity is : " + EntityTypeUL);
                Utilities UtilObj = new Utilities();
                PAN = UtilObj.randomPartnershipPANValueGenerator();
                LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

                break;
            }
            case "HINDU_UNDIVIDED_FAMILY": {
                LOGGER.info("Entity is : " + EntityTypeUL);
                Utilities UtilObj = new Utilities();
                PAN = UtilObj.randomHUFPANValueGenerator();
                LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

                break;
            }
        }

    }

    private Map<String, String> setcommonHeaders() {


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        return headers;
    }


    @Test(priority = 0, groups = {"Regression"}, description = "Fetch all business")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0001_fetchAllBusiness() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("channel", CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;
        Response responseObject = middlewareServiceObject.onlineFetchAllBusiness(queryParams, headers);

        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);


        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        Assert.assertEquals(responseObject.jsonPath().getString("businesses"), "[]");

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Fetch lead Data")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0002_fetchLeadData() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        queryParams.put("channel", CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;


        Response responseObject = middlewareServiceObject.onlineFetchLeadData(queryParams, headers);

        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data Not present for customer");

    }


    @Test(priority = 0, groups = {"Regression"}, description = "Create online merchant lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0003_postCreateLead() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);

        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        queryParams.put("mobile", MOBILE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("businessPan", PAN);
        body.put("businessName", BUSINESSNAME);
        body.put("leadId", "");
        body.put("kybBusinessId", "");
        body.put("solutionLeadId", "");
        body.put("resellerId", "testpr41287262755637");
        body.put("referralCode", "ICICI");

        Response responseObject = middlewareServiceObject.onlineResellercreateLead(queryParams, headers, body);

        // Status Code Validation
        //middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);

        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);

        leadID = responseObject.jsonPath().getString("leadId");
        solID = responseObject.jsonPath().getString("solutionLeadId");

        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadID);
        Assert.assertEquals(responseObject.jsonPath().getString("solutionLeadId"), solID);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Create online merchant lead for existing lead ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0004_postCreateLead() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);

        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        queryParams.put("mobile", MOBILE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("businessPan", PAN);
        body.put("businessName", BUSINESSNAME);
        body.put("leadId", "");
        body.put("kybBusinessId", "");
        body.put("solutionLeadId", "");
        body.put("resellerId", "testpr41287262755637");
        body.put("referralCode", "ICICI");

        Response responseObject = middlewareServiceObject.onlineResellercreateLead(queryParams, headers, body);

        // Status Code Validation
        //middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);

        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 400);

        // Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead already present with same reseller.");


    }

    // Hitting fetch lead data to check reseller id
    @Test(priority = 0, groups = {"Regression"}, description = "Fetch lead Data")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0005_fetchLeadData() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);

        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        queryParams.put("solutionLeadId", solID);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;


        Response responseObject = middlewareServiceObject.onlineFetchLeadData(queryParams, headers);

        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);

        //String solName = responseObject.jsonPath().get("solutions[0].solName").toString();
        //String leadStatus = responseObject.jsonPath().get("solutions[0].leadStatus").toString();
        //String leadSubStatus = responseObject.jsonPath().get("solution.solutionAdditionalInfo").toString();

        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.RESELLER_ID"), "testpr41287262755637");

    }


    @Test(priority = 0, groups = {"Regression"}, description = "payments lead status")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0006_paymentsLeadStatus() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        //queryParams.put("entityType", EntityTypeUL);

        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        //	queryParams.put("solutionLeadId", solID);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;


        Response responseObject = middlewareServiceObject.onlinePaymentsLead(queryParams, headers);

        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);

        String solutionTypeLevel3 = responseObject.jsonPath().get("solutions[0].solutionTypeLevel3").toString();

        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        Assert.assertEquals(solutionTypeLevel3, "testpr41287262755637");

    }


    @Test(priority = 0, groups = {"Regression"}, description = "Update Business for online merchant lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0007_postUpdateBusiness() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        queryParams.put("solutionLeadId", solID);
        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        //queryParams.put("mobile", MOBILE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("name", BUSINESSNAME);
        body.put("category", CATEGORY);
        body.put("subCategory", SUBCATEGORY);


        Response responseObject = middlewareServiceObject.onlineUpdateBusiness(queryParams, headers, body);

        // Status Code Validation
        //middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);

        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);

        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 204);

        // Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
        //  Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead is already present.");


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Update Additonal Details for online merchant lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0008_postUpdateAdditionalDetails() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        queryParams.put("solutionLeadId", solID);
        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        //queryParams.put("mobile", MOBILE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;


        Map<String, Object> body = new HashMap<String, Object>();

        body.put("addressSubType", "CORRESPONDENCE");
        body.put("addressType", "Office Purp");
        body.put("addressUuid", "UIDADD");
        body.put("city", "Noida");
        body.put("country", "India");
        body.put("displayMessage", "VICTORY");
        body.put("landmark", "Near Sash Building");
        body.put("latitude", 28.09);
        body.put("line1", "F 9201");
        body.put("line2", "Sector 26");
        body.put("line3", "NAveen Okhla");
        body.put("longitude", 27.19);
        body.put("pincode", 201301);
        body.put("refId", "BuckMouth");
        body.put("residentialStatus", "Redi");

        body.put("state", "Uttar Pradesh");
        body.put("statusCode", 0);
        body.put("title", "Haryana shop");

        body.put("displayName", "Concer Disp");
        body.put("gstinExemptedCategory", "");
        body.put("leadId", "");


        body.put("addressSubType1", "Registered");
        body.put("addressType1", "My Commerce");
        body.put("addressUuid1", "RDXROID");
        body.put("city1", "Central Delhi");
        body.put("country1", "India");
        body.put("displayMessage1", "NItyaro");
        body.put("landmark1", "Near Sash Building");
        body.put("latitude1", 22.7);
        body.put("line11", "Denmar");
        body.put("line21", "MINE");
        body.put("line31", "LINE3 For Add");
        body.put("longitude1", 28.6);
        body.put("pincode1", 110011);
        body.put("refId1", "FEDA");
        body.put("residentialStatus1", "True");

        body.put("state1", "Delhi");
        body.put("statusCode1", 0);
        body.put("title1", "NASDH");

        body.put("retailStore", "false");
        body.put("shopRelatedBusinessUuid", "string");
        body.put("email", "<EMAIL>");
        body.put("solutionLeadId", solID);
        body.put("appUrl", "https://google.com");
        body.put("websiteUrl", "https://google.com");
        body.put("businessProofNotRequired", "false");


        Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);


        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);

        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);


    }


    @Test(priority = 0, groups = {"Regression"}, description = "Validate bank details for online merchant lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC0009_postValidateBankDetailsTest() {
        BANKACCOUNTNUMBER = MOBILE;
        ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        //queryParams.put("mobile", MOBILE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();

        body.put("bankName", BANKNAME);
        body.put("bankAccountNumber", BANKACCOUNTNUMBER);
        body.put("ifsc", IFSC);
        body.put("bankAccountHolderName", "");
        body.put("beneficiaryName", "");


        Response responseObject = middlewareServiceObject.onlineMerchantValidateBank(queryParams, headers, body);

        validateBankDetailsResponseObject.setRefId(responseObject.jsonPath().getString("refId"));
        validateBankDetailsResponseObject.setStatusCode(responseObject.jsonPath().getString("statusCode"));
        validateBankDetailsResponseObject.setDisplayMessage(responseObject.jsonPath().getString("displayMessage"));
        validateBankDetailsResponseObject.setBankAccountHolderName(responseObject.jsonPath().getString("bankAccountHolderName"));
        validateBankDetailsResponseObject.setBankDetailsUuid(responseObject.jsonPath().getString("bankDetailsUuid"));
        validateBankDetailsResponseObject.setNameMatchStatus(responseObject.jsonPath().getString("nameMatchStatus"));
        int statusCode = responseObject.getStatusCode();
        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
        Assert.assertEquals(statusCode, 200);


        bankaccountholderName = responseObject.jsonPath().getString("bankAccountHolderName");
        bankdetailsUuid = responseObject.jsonPath().getString("bankDetailsUuid");
        // namematchStatus =responseObject.jsonPath().getChar("nameMatchStatus");

        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
    }


    @Test(priority = 0, groups = {"Regression"}, description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC0010_postUpdateBankDetailsTest() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        //queryParams.put("mobile", MOBILE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();

        body.put("bankName", BANKNAME);
        body.put("bankAccountNumber", BANKACCOUNTNUMBER);
        body.put("ifsc", IFSC);
        body.put("bankAccountHolderName", bankaccountholderName);
        body.put("beneficiaryName", "");
        body.put("bankDetailsUuid", bankdetailsUuid);

        body.put("leadId", leadID);
        body.put("solutionLeadId", solID);
        body.put("nameMatchStatus", namematchStatus);


        Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Update Tnc Online merchant ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC0011_postTncUpdate() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        queryParams.put("leadId", leadID);
        queryParams.put("solutionLeadId", solID);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        //queryParams.put("mobile", MOBILE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Response responseObject = middlewareServiceObject.onlineMerchantUpdateTnc(queryParams, headers);

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        //   String displayMessage = responseObject.jsonPath().getString("displayMessage");
        //   Assert.assertEquals(displayMessage, "Tnc saved, workflow updated.");


        if (responseObject.getStatusCode() == 200) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Tnc saved, workflow updated.");
            leadId = responseObject.jsonPath().getString("leadId");
        } else {
            log.info(" Inside Else condition for Document Upload ");
            for (int i = 0; i <= 5; ++i)

                LOGGER.info("Try to hit the API again");
            responseObject = middlewareServiceObject.onlineMerchantUpdateTnc(queryParams, headers);
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead successfully created.");
            leadId = responseObject.jsonPath().getString("leadId");
            if (responseObject.getStatusCode() == 200) {

                LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
                Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Tnc saved, workflow updated.");
                leadId = responseObject.jsonPath().getString("leadId");
            } else {

                LOGGER.info("post Tnc is throwing error ");
            }

        }

    }

    @Test(priority = 0, description = "Get Cust ID for mobile number", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0012_PositiveCustID() throws SQLException, JsonProcessingException {


        LOGGER.info("Hitting FetchUserDetails for custid");
        CustId = FetchUserDetails(MOBILE, "mobileNo");
    }


    @Test(priority = 0, description = "Get Cookie for OE Panel", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0013_PositiveGetOEPanelCookieUnlimited() throws SQLException, JsonProcessingException {
        //XMWToken=XMWCookie;

        //LOGGER.info("XMW token is :"+XMWToken);

        LOGGER.info("Hitting PG Callback for  MID");
        DbName = DbStaging6;
        MID = PG_CallBack_Insatnt50K(CustId);
    }

    //Multiple reseller flow (lead using different reseller MID on same mobile number)
    @Test(priority = 0, groups = {"Regression"}, description = "Create online merchant lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0014_postCreateLead() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);

        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        queryParams.put("mobile", MOBILE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("businessPan", PAN);
        body.put("businessName", BUSINESSNAME);
        body.put("leadId", "");
        body.put("kybBusinessId", "");
        body.put("solutionLeadId", "");
        body.put("resellerId", "LaivBr79100886656552");
        body.put("referralCode", "test001");

        Response responseObject = middlewareServiceObject.onlineResellercreateLead(queryParams, headers, body);

        // Status Code Validation
        //middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);

        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);

        leadID = responseObject.jsonPath().getString("leadId");
        solID = responseObject.jsonPath().getString("solutionLeadId");

        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadID);
        Assert.assertEquals(responseObject.jsonPath().getString("solutionLeadId"), solID);


    }

    //Multiple reseller flow (lead using same reseller MID as previous on same mobile number. Should give error)
    @Test(priority = 0, groups = {"Regression"}, description = "Create online merchant lead for existing lead ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0015_postCreateLead() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);

        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        queryParams.put("mobile", MOBILE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("businessPan", PAN);
        body.put("businessName", BUSINESSNAME);
        body.put("leadId", "");
        body.put("kybBusinessId", "");
        body.put("solutionLeadId", "");
        body.put("resellerId", "LaivBr79100886656552");
        body.put("referralCode", "test001");

        Response responseObject = middlewareServiceObject.onlineResellercreateLead(queryParams, headers, body);
        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead already present with same reseller.");

    }

    // Hitting fetch lead data to check reseller id
    @Test(priority = 0, groups = {"Regression"}, description = "Fetch lead Data")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0016_fetchLeadData() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);

        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        queryParams.put("solutionLeadId", solID);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;


        Response responseObject = middlewareServiceObject.onlineFetchLeadData(queryParams, headers);

        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);

        //String solName = responseObject.jsonPath().get("solutions[0].solName").toString();
        //String leadStatus = responseObject.jsonPath().get("solutions[0].leadStatus").toString();
        //String leadSubStatus = responseObject.jsonPath().get("solution.solutionAdditionalInfo").toString();

        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.RESELLER_ID"), "LaivBr79100886656552");

    }

    @Test(priority = 0, groups = {"Regression"}, description = "payments lead status")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0017_paymentsLeadStatus() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        //queryParams.put("entityType", EntityTypeUL);

        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        //	queryParams.put("solutionLeadId", solID);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;


        Response responseObject = middlewareServiceObject.onlinePaymentsLead(queryParams, headers);

        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);

        String solutionTypeLevel3 = responseObject.jsonPath().get("solutions[1].solutionTypeLevel3").toString();

        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        // Checking if both resellers are coming in response of Payment lead status API
        Assert.assertEquals(responseObject.jsonPath().get("solutions[0].solutionTypeLevel3"), "testpr41287262755637");
        Assert.assertEquals(solutionTypeLevel3, "LaivBr79100886656552");

    }


    @Test(priority = 0, groups = {"Regression"}, description = "Update Business for online merchant lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0018_postUpdateBusiness() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        queryParams.put("solutionLeadId", solID);
        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        //queryParams.put("mobile", MOBILE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("name", BUSINESSNAME);
        body.put("category", CATEGORY);
        body.put("subCategory", SUBCATEGORY);


        Response responseObject = middlewareServiceObject.onlineUpdateBusiness(queryParams, headers, body);

        // Status Code Validation
        //middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);

        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);

        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 204);

        // Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
        //  Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead is already present.");


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Update Additonal Details for online merchant lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0019_postUpdateAdditionalDetails() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        queryParams.put("solutionLeadId", solID);
        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        //queryParams.put("mobile", MOBILE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;


        Map<String, Object> body = new HashMap<String, Object>();

        body.put("addressSubType", "CORRESPONDENCE");
        body.put("addressType", "Office Purp");
        body.put("addressUuid", "UIDADD");
        body.put("city", "Noida");
        body.put("country", "India");
        body.put("displayMessage", "VICTORY");
        body.put("landmark", "Near Sash Building");
        body.put("latitude", 28.09);
        body.put("line1", "F 9201");
        body.put("line2", "Sector 26");
        body.put("line3", "NAveen Okhla");
        body.put("longitude", 27.19);
        body.put("pincode", 201301);
        body.put("refId", "BuckMouth");
        body.put("residentialStatus", "Redi");

        body.put("state", "Uttar Pradesh");
        body.put("statusCode", 0);
        body.put("title", "Haryana shop");

        body.put("displayName", "Concer Disp");
        body.put("gstinExemptedCategory", "");
        body.put("leadId", "");


        body.put("addressSubType1", "Registered");
        body.put("addressType1", "My Commerce");
        body.put("addressUuid1", "RDXROID");
        body.put("city1", "Central Delhi");
        body.put("country1", "India");
        body.put("displayMessage1", "NItyaro");
        body.put("landmark1", "Near Sash Building");
        body.put("latitude1", 22.7);
        body.put("line11", "Denmar");
        body.put("line21", "MINE");
        body.put("line31", "LINE3 For Add");
        body.put("longitude1", 28.6);
        body.put("pincode1", 110011);
        body.put("refId1", "FEDA");
        body.put("residentialStatus1", "True");

        body.put("state1", "Delhi");
        body.put("statusCode1", 0);
        body.put("title1", "NASDH");

        body.put("retailStore", "false");
        body.put("shopRelatedBusinessUuid", "string");
        body.put("email", "<EMAIL>");
        body.put("solutionLeadId", solID);
        body.put("appUrl", "https://google.com");
        body.put("websiteUrl", "https://google.com");
        body.put("businessProofNotRequired", "false");


        Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);


        String responseBody = responseObject.getBody().asString();
        System.out.println("Response Body is:" + responseBody);

        // Status Code Validation
        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);


    }



    //not passing solution lead id when creating lead with different reseller on same mobile num
    @Test(priority = 0, groups = {"Regression"}, description = "Validate bank details for online merchant lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC0021_postValidateBankDetailsTest() {
        BANKACCOUNTNUMBER = MOBILE;
        ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        //queryParams.put("mobile", MOBILE);
        //queryParams.put("solutionLeadId", solID);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();

        body.put("bankName", BANKNAME);
        body.put("bankAccountNumber", BANKACCOUNTNUMBER);
        body.put("ifsc", IFSC);
        body.put("bankAccountHolderName", "");
        body.put("beneficiaryName", "");


        Response responseObject = middlewareServiceObject.onlineMerchantValidateBank(queryParams, headers, body);

        validateBankDetailsResponseObject.setRefId(responseObject.jsonPath().getString("refId"));
        validateBankDetailsResponseObject.setStatusCode(responseObject.jsonPath().getString("statusCode"));
        validateBankDetailsResponseObject.setDisplayMessage(responseObject.jsonPath().getString("displayMessage"));
        validateBankDetailsResponseObject.setBankAccountHolderName(responseObject.jsonPath().getString("bankAccountHolderName"));
        validateBankDetailsResponseObject.setBankDetailsUuid(responseObject.jsonPath().getString("bankDetailsUuid"));
        validateBankDetailsResponseObject.setNameMatchStatus(responseObject.jsonPath().getString("nameMatchStatus"));
        int statusCode = responseObject.getStatusCode();
        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
        Assert.assertEquals(statusCode, 500);


        bankaccountholderName = responseObject.jsonPath().getString("bankAccountHolderName");
        bankdetailsUuid = responseObject.jsonPath().getString("bankDetailsUuid");
        // namematchStatus =responseObject.jsonPath().getChar("nameMatchStatus");

        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
    }


    @Test(priority = 0, groups = {"Regression"}, description = "Validate bank details for online merchant lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC0022_postValidateBankDetailsTest() {
        BANKACCOUNTNUMBER = MOBILE;
        ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        //queryParams.put("mobile", MOBILE);
        queryParams.put("solutionLeadId", solID);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();

        body.put("bankName", BANKNAME);
        body.put("bankAccountNumber", BANKACCOUNTNUMBER);
        body.put("ifsc", IFSC);
        body.put("bankAccountHolderName", "");
        body.put("beneficiaryName", "");


        Response responseObject = middlewareServiceObject.onlineMerchantValidateBank(queryParams, headers, body);

        validateBankDetailsResponseObject.setRefId(responseObject.jsonPath().getString("refId"));
        validateBankDetailsResponseObject.setStatusCode(responseObject.jsonPath().getString("statusCode"));
        validateBankDetailsResponseObject.setDisplayMessage(responseObject.jsonPath().getString("displayMessage"));
        validateBankDetailsResponseObject.setBankAccountHolderName(responseObject.jsonPath().getString("bankAccountHolderName"));
        validateBankDetailsResponseObject.setBankDetailsUuid(responseObject.jsonPath().getString("bankDetailsUuid"));
        validateBankDetailsResponseObject.setNameMatchStatus(responseObject.jsonPath().getString("nameMatchStatus"));
        int statusCode = responseObject.getStatusCode();
        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
        Assert.assertEquals(statusCode, 200);


        bankaccountholderName = responseObject.jsonPath().getString("bankAccountHolderName");
        bankdetailsUuid = responseObject.jsonPath().getString("bankDetailsUuid");
        // namematchStatus =responseObject.jsonPath().getChar("nameMatchStatus");

        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
    }


    @Test(priority = 0, groups = {"Regression"}, description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC0023_postUpdateBankDetailsTest() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        //queryParams.put("mobile", MOBILE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Map<String, Object> body = new HashMap<String, Object>();

        body.put("bankName", BANKNAME);
        body.put("bankAccountNumber", BANKACCOUNTNUMBER);
        body.put("ifsc", IFSC);
        body.put("bankAccountHolderName", bankaccountholderName);
        body.put("beneficiaryName", "");
        body.put("bankDetailsUuid", bankdetailsUuid);
        body.put("leadId", leadID);
        body.put("solutionLeadId", solID);

        body.put("nameMatchStatus", namematchStatus);

        Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);


    }

    @Test(priority = 0, groups = {"Regression"}, description = "Update Tnc Online merchant ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC0024_postTncUpdate() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", SOLUTION);
        queryParams.put("entityType", EntityTypeUL);
        queryParams.put("leadId", leadID);
        queryParams.put("solutionLeadId", solID);
        queryParams.put("channel", CHANNEL);
        //queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
        //queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
        //queryParams.put("mobile", MOBILE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = commonHeaders;

        Response responseObject = middlewareServiceObject.onlineMerchantUpdateTnc(queryParams, headers);

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        //   String displayMessage = responseObject.jsonPath().getString("displayMessage");
        //   Assert.assertEquals(displayMessage, "Tnc saved, workflow updated.");


        if (responseObject.getStatusCode() == 200) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Tnc saved, workflow updated.");
            leadId = responseObject.jsonPath().getString("leadId");
        } else {
            log.info(" Inside Else condition for Document Upload ");
            for (int i = 0; i <= 5; ++i)

                LOGGER.info("Try to hit the API again");
            responseObject = middlewareServiceObject.onlineMerchantUpdateTnc(queryParams, headers);
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead successfully created.");
            leadId = responseObject.jsonPath().getString("leadId");
            if (responseObject.getStatusCode() == 200) {

                LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
                Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Tnc saved, workflow updated.");
                leadId = responseObject.jsonPath().getString("leadId");
            } else {

                LOGGER.info("post Tnc is throwing error ");
            }

        }

    }

    @Test(priority = 0, description = "Get Cust ID for mobile number", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0025_PositiveCustID() throws SQLException, JsonProcessingException {


        LOGGER.info("Hitting FetchUserDetails for custid");
        CustId = FetchUserDetails(MOBILE, "mobileNo");
    }


    @Test(priority = 0, description = "Get Cookie for OE Panel", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0026_PositiveGetOEPanelCookieUnlimited() throws SQLException, JsonProcessingException {
        //XMWToken=XMWCookie;

        //LOGGER.info("XMW token is :"+XMWToken);

        LOGGER.info("Hitting PG Callback for  MID");
        DbName = DbStaging6;
        MID = PG_CallBack_Insatnt50K(CustId);
    }


}