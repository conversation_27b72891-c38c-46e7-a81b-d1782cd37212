package OCL.DIY.OnlineMerchant;


import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.upgradeMid.lead.ValidateBankDetailsOnline;
import Request.oAuth.oAuthWormhole.CreateUser;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;



public class TestOnlineMerchantV1ResellerFlowCC extends BaseMethod {

	private static final Logger LOGGER = LogManager.getLogger(TestOnlineMerchantV1.class);
	MiddlewareServices middlewareServiceObject =new MiddlewareServices();
	 
	ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();
	
	String sessionToken = "";
	String leadId = "";
	String custId = "";
	String agentNumber = "";
	String agentPassword = "paytm@123";
	String token = "";
	String uuid = "";
	String ckycStage = "";
	String loanOffered = "";
	String maxLoanAmount = "";
	String authorisedMonthlyLimit = "";
	String stage = "";
	String code = "";
	String tncName = "";
	String url = "";
	String uniqueIdentifier = "";
	String md5 = "";
	String codeSanctionLetter = "";
	String tncNameSanctionLetter = "";
	String urlSanctionLetter = "";
	String uniqueIdentifierSanctionLetter = "";
	String md5SanctionLetter = "";
	
	public static String bankaccountholderName;
	public static String bankdetailsUuid;
	public static boolean namematchStatus = false;
	public static String leadID;
	public static String solID;
	public static String CustId= "";
	public static String MID= "";
	 public static String OePanelDocStatus = "APPROVED";
	 public static String RejectionReason = null;
	 public static String DocumetRequestDeserialised = "";
	 public static String LeadStagePanel = "";
	  public static String WorkFlowId = "";
	
	public static final String SOLUTION = "online_merchant";
	public static final String SOLUTION_TYPE_LEVEL_2 = "CLIX";
	public static final String SOLUTION_TYPE_LEVEL_3 = "Unsecured_Short_term_Loan_Simplified";
	public static final String ENTITY_TYPE = "INDIVIDUAL";
	public static final String CHANNEL = "UMP_WEB";
	public static String PAN = "";
	public static final String DOB = "1989-04-21";
	public static final String EMAIL = "";
	public static final String ISSUER = "OE";
	public static final String CLIENT_ID = "LMS";
	public static final String WORKFLOW_VERSION = "V2";
	public static String XMWToken="";
	public static  String MOBILE = "";
	public static  String BUSINESSPAN="";
	public static  String EntityTypeUL = "PUBLIC_LIMITED";
	public static final String BUSINESSNAME="TestBeneficiary";
	public static final String CATEGORY="BFSI";
	public static final String SUBCATEGORY="Loans";
	public static final String BANKNAME="ICICI Bank";
	public static String BANKACCOUNTNUMBER="************";

	public static final String IFSC="Icic0006622";

	Map<String, String> commonHeaders;
	
	
	
    @BeforeClass()
	public void intitializeInputData() throws IOException {

		Utilities accObj = new Utilities();
		MOBILE = accObj.randomMobileNumberGenerator();
		LOGGER.info("New Number is : " + MOBILE);
		//LOGGER.info(" Before Suite Method for Agent Login ");
		CreateUser OauthObj = new CreateUser();
		OauthObj.setHeader("Content-Type","application/json");
		OauthObj.getProperties().setProperty("mobile",MOBILE);
		OauthObj.getProperties().setProperty("loginPassword","paytm@123");
		Response OauthResp =  OauthObj.callAPI();

		sessionToken = ApplicantToken(MOBILE, agentPassword);
		//LOGGER.info("Applicant Token for Lending : " + sessionToken);
		commonHeaders = setcommonHeaders();

		switch (EntityTypeUL) {
			case "PUBLIC_LIMITED":
			case "PRIVATE_LIMITED": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomPublicPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
			case "PROPRIETORSHIP": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomIndividualPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
			case "TRUST": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomTrustPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
			case "SOCIETY_ASSOCIATION_CLUB": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomSocietyPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
			case "PARTNERSHIP": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomPartnershipPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
			case "HINDU_UNDIVIDED_FAMILY": {
				LOGGER.info("Entity is : " + EntityTypeUL);
				Utilities UtilObj = new Utilities();
				PAN = UtilObj.randomHUFPANValueGenerator();
				LOGGER.info("PAN Number is for ORG Merchant : " + PAN);

				break;
			}
		}

	}

	private Map<String, String> setcommonHeaders() {
	

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json");
		headers.put("version", "7.3.0");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

		return headers;
	}
	
	

	@Test(priority = 0,groups = {"Regression"},description = "Create online merchant lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0001_postCreateLead() {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	
		//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("channel", CHANNEL);
		//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		queryParams.put("mobile", MOBILE);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("businessPan", PAN);
		body.put("businessName", BUSINESSNAME);
		body.put("leadId", "");
		body.put("kybBusinessId", "");
		body.put("solutionLeadId", "");
		body.put("resellerId", "LaivBr79100886656552");
		body.put("referralCode", "test001");
		
		Response responseObject = middlewareServiceObject.onlineResellercreateLead(queryParams, headers, body);
		
	// Status Code Validation	
		//middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);
		
		String responseBody  =responseObject.getBody().asString();
		System.out.println("Response Body is:" +responseBody);
		
		leadID =responseObject.jsonPath().getString("leadId");
		solID =responseObject.jsonPath().getString("solutionLeadId");
		
		// Status Code Validation
		int statusCode=responseObject.getStatusCode();
		Assert.assertEquals(statusCode, 200);
		
	    Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadID);
       Assert.assertEquals(responseObject.jsonPath().getString("solutionLeadId"), solID);

		
		}
	
	@Test(priority = 0,groups = {"Regression"},description = "Create online merchant lead for existing lead ")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0002_postCreateLead() {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	
		//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("channel", CHANNEL);
		//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		queryParams.put("mobile", MOBILE);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("businessPan", PAN);
		body.put("businessName", BUSINESSNAME);
		body.put("leadId", "");
		body.put("kybBusinessId", "");
		body.put("solutionLeadId", "");
		body.put("resellerId", "LaivBr79100886656552");
		body.put("referralCode", "test001");			
		
		Response responseObject = middlewareServiceObject.onlineResellercreateLead(queryParams, headers, body);
		
	// Status Code Validation	
		//middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);
		
		String responseBody  =responseObject.getBody().asString();
		System.out.println("Response Body is:" +responseBody);
		
		//leadID =responseObject.jsonPath().getString("leadId");
		//solID =responseObject.jsonPath().getString("solutionLeadId");
		
		// Status Code Validation
		int statusCode=responseObject.getStatusCode();
		Assert.assertEquals(statusCode, 400);
		
	// Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
       Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead already present with same reseller.");
       //Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadID);
       //Assert.assertEquals(responseObject.jsonPath().getString("solutionLeadId"), solID);
		
		}

	

	@Test(priority = 0,groups = {"Regression"},description = "Update Business for online merchant lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0003_postUpdateBusiness() {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	    queryParams.put("solutionLeadId", solID);
		//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("channel", CHANNEL);
		//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		//queryParams.put("mobile", MOBILE);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("name", BUSINESSNAME);
		body.put("category", CATEGORY);
		body.put("subCategory", SUBCATEGORY);
	
		
		Response responseObject = middlewareServiceObject.onlineUpdateBusiness(queryParams, headers, body);
		
	// Status Code Validation	
		//middlewareServiceObject.verifyResponseCodeAs200OK(responseObject);
		
		String responseBody  =responseObject.getBody().asString();
		System.out.println("Response Body is:" +responseBody);
		
		// Status Code Validation
		int statusCode=responseObject.getStatusCode();
		Assert.assertEquals(statusCode, 204);
		
	// Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
     //  Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead is already present.");

		
		}
	
	@Test(priority = 0,groups = {"Regression"},description = "Update Additonal Details for online merchant lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0004_postUpdateAdditionalDetails() {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	    queryParams.put("solutionLeadId", solID);
		//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("channel", CHANNEL);
		//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		//queryParams.put("mobile", MOBILE);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		
		Map<String, Object> body = new HashMap<String, Object>();
		
		body.put("addressSubType", "CORRESPONDENCE");
		body.put("addressType", "Office Purp");
		body.put("addressUuid", "UIDADD");
	    body.put("city", "Noida");
		body.put("country", "India");
		body.put("displayMessage", "VICTORY");
		body.put("landmark", "Near Sash Building");
		body.put("latitude", 28.09);
		body.put("line1", "F 9201");
		body.put("line2", "Sector 26");
		body.put("line3", "NAveen Okhla");
		body.put("longitude", 27.19);
		body.put("pincode", 201301);
		body.put("refId", "BuckMouth");
		body.put("residentialStatus", "Redi");
	
		body.put("state", "Uttar Pradesh");
		body.put("statusCode", 0);
		body.put("title", "Haryana shop");
		
		body.put("displayName", "Concer Disp");
		body.put("gstinExemptedCategory", "");
		body.put("leadId", "");

	
		body.put("addressSubType1", "Registered");
		body.put("addressType1", "My Commerce");
		body.put("addressUuid1", "RDXROID");
	    body.put("city1", "Central Delhi");
		body.put("country1", "India");
		body.put("displayMessage1", "NItyaro");
		body.put("landmark1", "Near Sash Building");
		body.put("latitude1", 22.7);
		body.put("line11", "Denmar");
		body.put("line21", "MINE");
		body.put("line31", "LINE3 For Add");
		body.put("longitude1", 28.6);
		body.put("pincode1", 110011);
		body.put("refId1", "FEDA");
		body.put("residentialStatus1", "True");
	
		body.put("state1", "Delhi");
		body.put("statusCode1", 0);
		body.put("title1", "NASDH");
		
		body.put("retailStore", "false");
		body.put("shopRelatedBusinessUuid", "string");
		body.put("email", "<EMAIL>");
		body.put("solutionLeadId", solID);
		body.put("appUrl", "https://google.com");
		body.put("websiteUrl", "https://google.com");
		body.put("businessProofNotRequired", "false");



		Response responseObject = middlewareServiceObject.onlineUpdateAdditonalDetails(queryParams, headers, body);
		

		String responseBody  =responseObject.getBody().asString();
		System.out.println("Response Body is:" +responseBody);
		
		// Status Code Validation
		int statusCode=responseObject.getStatusCode();
		Assert.assertEquals(statusCode, 200);
			
	
	   
	}
	

	@Test(priority = 0,groups = {"Regression"},description = "Validate bank details for online merchant lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0005_postValidateBankDetailsTest()
	    {
			BANKACCOUNTNUMBER = MOBILE;
			ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();

		  Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("entityType", EntityTypeUL);
		   // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
			//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
			queryParams.put("channel", CHANNEL);
			//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
			//queryParams.put("mobile", MOBILE);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;
			
			Map<String, Object> body = new HashMap<String, Object>();
			
			body.put("bankName", BANKNAME);
			body.put("bankAccountNumber", BANKACCOUNTNUMBER);
			body.put("ifsc", IFSC);
		    body.put("bankAccountHolderName", "");
		    body.put("beneficiaryName", "");
		
	
		  
	        Response responseObject = middlewareServiceObject.onlineMerchantValidateBank(queryParams, headers, body);
	        
	       validateBankDetailsResponseObject.setRefId(responseObject.jsonPath().getString("refId"));
	        validateBankDetailsResponseObject.setStatusCode(responseObject.jsonPath().getString("statusCode"));
	      validateBankDetailsResponseObject.setDisplayMessage(responseObject.jsonPath().getString("displayMessage"));
	       validateBankDetailsResponseObject.setBankAccountHolderName(responseObject.jsonPath().getString("bankAccountHolderName"));
	      validateBankDetailsResponseObject.setBankDetailsUuid(responseObject.jsonPath().getString("bankDetailsUuid"));
	      validateBankDetailsResponseObject.setNameMatchStatus(responseObject.jsonPath().getString("nameMatchStatus"));
	        int statusCode = responseObject.getStatusCode();
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	        Assert.assertEquals(statusCode, 200);
	        
	   
	        
	        bankaccountholderName =responseObject.jsonPath().getString("bankAccountHolderName");
	        bankdetailsUuid =responseObject.jsonPath().getString("bankDetailsUuid");
	       // namematchStatus =responseObject.jsonPath().getChar("nameMatchStatus");
			
	        // validateBankDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsSchema.json");
	    }
	

	@Test(priority = 0,groups = {"Regression"},description = "Update bank details for online merchant lead Name match true")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0006_postUpdateBankDetailsTest()
	    {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
	   // queryParams.put("solutionLeadId", "950e8bdb-7693-45c6-9911-632ad837c2c9");
		//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("channel", CHANNEL);
		//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		//queryParams.put("mobile", MOBILE);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		Map<String, Object> body = new HashMap<String, Object>();
		
		body.put("bankName", BANKNAME);
		body.put("bankAccountNumber", BANKACCOUNTNUMBER);
		body.put("ifsc", IFSC);
	    body.put("bankAccountHolderName", bankaccountholderName );
	    body.put("beneficiaryName", "");
	    body.put("bankDetailsUuid", bankdetailsUuid);
	    body.put("nameMatchStatus", namematchStatus);
	    body.put("leadId", leadID);
	    body.put("SolutionLeadId", solID);
	
		
	    Response responseObject = middlewareServiceObject.onlineMerchantUpdateBank(queryParams, headers, body);
		
	    int statusCode = responseObject.getStatusCode();
	    Assert.assertEquals(statusCode, 200);
		
		

	    }
	
	@Test(priority = 0,groups = {"Regression"},description = "Update Tnc Online merchant ")
    @Owner(emailId = "<EMAIL>",isAutomated = true)

	    public void  TC0007_postTncUpdate()
	    {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", EntityTypeUL);
		 queryParams.put("leadId", leadID);
		queryParams.put("solutionLeadId", solID);
	    queryParams.put("channel", CHANNEL);
		//queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		//queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		//queryParams.put("mobile", MOBILE);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;
		
		
		
		
	    Response responseObject = middlewareServiceObject.onlineMerchantUpdateTnc(queryParams, headers);
		
	    int statusCode = responseObject.getStatusCode();
	    Assert.assertEquals(statusCode, 200);
	//   String displayMessage = responseObject.jsonPath().getString("displayMessage");
	//   Assert.assertEquals(displayMessage, "Tnc saved, workflow updated.");
	   
	   
		if (responseObject.getStatusCode() == 200) {
			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Tnc saved, workflow updated.");
			leadId = responseObject.jsonPath().getString("leadId");
		}

		else {
			log.info(" Inside Else condition for Document Upload ");
			for (int i = 0; i <= 5; ++i)
				
			LOGGER.info("Try to hit the API again");
			responseObject = middlewareServiceObject.onlineMerchantUpdateTnc(queryParams, headers);
			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead successfully created.");
			leadId = responseObject.jsonPath().getString("leadId");
			if (responseObject.getStatusCode() == 200) {
				
				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Tnc saved, workflow updated.");
				leadId = responseObject.jsonPath().getString("leadId");
			}
			
			else {
				
				LOGGER.info("post Tnc is throwing error ");
			}
			
		}
	
	    }


	
	   @Test(priority = 0,groups = {"Regression"},description = "Upload cancel cheque document for lead ")
	    @Owner(emailId = "<EMAIL>",isAutomated = true)

		    public void  TC0008_postUploadCancelCheque() throws JsonProcessingException
		    {
			
		   File DocPath = new File(System.getProperty("user.dir")+"/PaytmImage.jpg");
	        LOGGER.info("File Path is : " + DocPath.getAbsolutePath());

	        Map<String, String> queryParams = new HashMap<String, String>();

	        queryParams.put("solution", SOLUTION);
	        queryParams.put("entityType", EntityTypeUL);
	        queryParams.put("leadId", leadID);
	        queryParams.put("solutionLeadId",solID);
	        queryParams.put("channel", CHANNEL);
	       // queryParams.put("solutionTypeLevel2","business_service");
	     
	    
	        Map<String, String> headerss = new HashMap<String, String>();

			headerss.put("session_token", sessionToken);
			headerss.put("version", "7.3.0");
			headerss.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
			headerss.put("Content-Type", "multipart/json");

	        Map<String, String> queryParamsUpload = new HashMap<String, String>();
	        queryParamsUpload.put("solutionType", SOLUTION);
	        queryParamsUpload.put("entityType", EntityTypeUL);
	        queryParamsUpload.put("solutionLeadId",solID);
	        queryParamsUpload.put("channel", CHANNEL);
	       

	        FetchUploadDiyDoc(queryParams,queryParamsUpload,headerss,DocPath);

	
	
       
		    }
	   
	   
	   
		@Test(priority = 0,description = "Get Cust ID for mobile number",groups = {"Regression"})
	    @Owner(emailId = "<EMAIL>",isAutomated = true)
	    public void TC0009_PositiveCustID() throws SQLException, JsonProcessingException {
	       

	        LOGGER.info("Hitting FetchUserDetails for custid");
	        CustId = FetchUserDetails(MOBILE,"mobileNo");
	    }
	   

	   
	   @Test(priority = 0,groups = {"Regression"},description = "FetchLeadPanelOnlinMerchant ")
	    @Owner(emailId = "<EMAIL>",isAutomated = true)

	   
	    public void TC0010_PositiveFetchLeadPanelOnlinMerchant()
	    {

	        try
	        {
	            Map <String,String> RequestPanel = new HashMap<>();
	            Map <String,String> ResponsePanel = new HashMap<>();

	            RequestPanel.put("docStatus","APPROVED");
	            RequestPanel.put("rejectionReason",null);
	            RequestPanel.put("leadId",solID);


	            ResponsePanel = FetchPanelLead(RequestPanel);

	            DocumetRequestDeserialised = ResponsePanel.get("DocumenstArray");
	            WorkFlowId = ResponsePanel.get("WorkFlowId");
	            LOGGER. info("Lead Stage is : " + ResponsePanel.get("LeadStage"));
	            LOGGER. info("WorkFlow ID Is: " + ResponsePanel.get("WorkFlowId"));
	        }
	        catch (Exception e)
	        {
	            LOGGER.info("Execption " + e);
	            LOGGER.info(" Line No. at : " + e.getStackTrace()[0].getLineNumber());
	        }
	    }
	   
	   
	   @Test(priority = 0,groups = {"Regression"},description = "Allocating agent for cancel cheque ")
	    @Owner(emailId = "<EMAIL>",isAutomated = true)

		    public void  TC0011_PostAllocateAgent() throws JsonProcessingException{
		   
		   LOGGER.info("Allocating agent for cancel cheque");
		   
		   ReallocatingAgent(solID, "1152");
		   
	   }
	   
	   
	   
	   
	   @Test(priority = 0,groups = {"Regression"},description = "Allocating agent for cancel cheque ")
	    @Owner(emailId = "<EMAIL>",isAutomated = true)

	   
	   public void TC0012_PositiveSubmitLeadPanel500K()
	    {
		    XMWToken=XMWCookie;

		     LOGGER.info("XMW token is :"+XMWToken);
	            EditLead v1EditLeadObj = new EditLead(solID, P.TESTDATA.get("EditLeadUpgradeMidCancelChequeApprove"));

//	            v1EditLeadObj.getProperties().setProperty("custId", CustId);
	     //       v1EditLeadObj.getProperties().setProperty("mobileNumber", MOBILE);
	            v1EditLeadObj.getProperties().setProperty("documents", DocumetRequestDeserialised);
	            v1EditLeadObj.getProperties().setProperty("reEnterAccountNumber", MOBILE);
	            v1EditLeadObj.getProperties().setProperty("workflowStatusId", WorkFlowId);

	            Response responseObject = middlewareServiceObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "**********", "**********", XMWToken, "application/json");

	            int statusCode = responseObject.getStatusCode();
	            Assert.assertEquals(statusCode, 200);

	            String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
	            Assert.assertTrue(LeadSatge.contains("PANEL_SUCCESS"));

	            //v1EditLeadObj.validateResponseAgainstJSONSchema("MerchantServiceOEPanelV1EditLead/EditLead500KSchema.json");


	    }
	   
	   

	   @Test(priority = 0,description = "Get Cookie for OE Panel",groups = {"Regression"})
	    @Owner(emailId = "<EMAIL>",isAutomated = true)
	    public void TC0013_PositiveGetOEPanelCookieUnlimited() throws SQLException, JsonProcessingException {
	       // XMWToken=XMWCookie;

	     //   LOGGER.info("XMW token is :"+XMWToken);

	        LOGGER.info("Hitting Callback for 100Rs MID");
		   DbName = DbStaging6;
	        MID = PG_CallBack_Insatnt50K(CustId);
	    } 
	   
}
	
