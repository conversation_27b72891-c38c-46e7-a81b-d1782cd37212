package OCL.DIY.MerchantLimitUpgrade;

import Services.DIYMerchantUpgradeLimit.FetchMerchantDocDetails;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;


import java.util.HashMap;
import java.util.Map;

public class FetchScreenDetail extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(FetchScreenDetail.class);
    FetchMerchantDocDetails fetchMerchantDocDetails = new FetchMerchantDocDetails();
    Response fetchResponse = null;
    public String Token;
    String leadId;
    public static String mid = "edqOXK74981235418402";
    public static String merchantMobileNumber = "8881222111";
    Map<String, String> headers = new HashMap<String, String>();
    Map<String, String> params = new HashMap<String, String>();
    String requestPath;

    @BeforeClass
    public void generateToken() {
        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
        LOGGER.info("Token: " + Token);
        headers.put("session_token", Token);
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

    }

    @Test(priority = 1, description = "To check if fetch merchant doc detail is working", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC1_fetchMerchantDocDetail_MerchantLimitUpgrade() throws JSONException {

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", "diy_mco");
        params.put("mid", mid);
        params.put("fetchStrategy", "FETCH_DIY_UPGRADE_LENDING_STRATEGY");
        params.put("solutionTypeLevel2", "diy_upgrade");


        requestPath = "MerchantService/v1/sdMerchant/lead/fetchMerchantDocDetailRequest.json";
        fetchResponse = fetchMerchantDocDetails.FetchMerchantDocDetails(requestPath, headers, params);

        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 200);
    }


    @Test(priority = 1, description = "To check if fetch merchant doc detail is working", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC2_fetchMerchantDocDetailForDocumentInQCReview_MerchantLimitUpgrade() throws JSONException {

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", "diy_mco");
        params.put("mid", mid);
        params.put("solutionTypeLevel2", "diy_upgrade");
        params.put("fetchStrategy", "FETCH_DIY_UPGRADE_LENDING_STRATEGY");
        requestPath = "MerchantService/v1/sdMerchant/lead/fetchMerchantDocDetail.json";
        fetchResponse = fetchMerchantDocDetails.FetchMerchantDocDetails(requestPath, headers, params);

        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 200);

    }

    @Test(priority = 1, description = "To check if fetch merchant doc detail is giving 400 when mid is not present", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC3_fetchMerchantDocDetail_MWithoutMid() throws JSONException {

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", "diy_mco");

        params.put("fetchStrategy", "FETCH_IN_QC_AADHAAR_PAN_GSTIN_STRATEGY");
        requestPath = "MerchantService/v1/sdMerchant/lead/fetchMerchantDocDetail.json";
        fetchResponse = fetchMerchantDocDetails.FetchMerchantDocDetails(requestPath, headers, params);

        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 400);

    }

    @Test(priority = 1, description = "To check if fetch merchant doc detail is giving 400 when solution is not present", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC4_fetchMerchantDocDetail_MWithoutSolution() throws JSONException {

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);


        params.put("fetchStrategy", "FETCH_IN_AADHAAR_PAN_GSTIN_STRATEGY");
        requestPath = "MerchantService/v1/sdMerchant/lead/fetchMerchantDocDetail.json";
        fetchResponse = fetchMerchantDocDetails.FetchMerchantDocDetails(requestPath, headers, params);

        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 400);

    }
    @Test(priority = 1, description = "To check if fetch merchant doc detail is giving 400 when solution is not present", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC5_fetchMerchantDocQCDetail_MWithoutSolution() throws JSONException {

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);


        params.put("fetchStrategy", "FETCH_IN_QC_AADHAAR_PAN_GSTIN_STRATEGY");
        requestPath = "MerchantService/v1/sdMerchant/lead/fetchMerchantDocDetail.json";
        fetchResponse = fetchMerchantDocDetails.FetchMerchantDocDetails(requestPath, headers, params);

        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 400);

    }

    public Response fetchScreenDetails(String Token, String fetchStrategy, String solutionTypeLevel2, String leadId) throws JSONException {

        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        params.put("solution", "diy_mco");
     //   params.put("mid", mid);
        params.put("fetchStrategy", fetchStrategy);
        params.put("solutionTypeLevel2", solutionTypeLevel2);
        params.put("leadId", leadId);


        requestPath = "MerchantService/v1/sdMerchant/lead/fetchMerchantDocDetailRequest.json";
        fetchResponse = fetchMerchantDocDetails.FetchMerchantDocDetails(requestPath, headers, params);

       return fetchResponse;
    }
}
