package OCL.DIY.MerchantLimitUpgrade;
import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.Revisit.LoanIntentfetchDocumentDetails;
import Services.DBConnection.DBConnection;
import Services.DIYMCO.getStarted;
import Services.DIYMerchantUpgradeLimit.CreateLeadMerchantUpgradeLimit;
import Services.DIYMerchantUpgradeLimit.FetchScreenDetailsUpgradeLimit;
import Services.DIYMerchantUpgradeLimit.UpdateLeadMerchantUpgradeLimit;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import java.io.File;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import java.util.HashMap;
import java.util.Map;
public class DiyReKYC extends BaseMethod {
    public String Token;
    String DocPath = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";
    getStarted gs = new getStarted();
    ;
    Response fetchResponse = null;
    public static String solution = "diy_mco";
    public static String entityType = "INDIVIDUAL";
    public static String channel = "DIY_P4B_APP";
    public static String merchantMobileNumber = "**********";
    public static String mid = "NYVQAQ14152020662046";
    public static String successMessage = "Request Submitted Successfully";
    public static String solutionTypeLevel2 = "diy_upgrade";
    public static String source = "DIY_P4B_APP";
    String closeOpenLeadValue = "true";
    Map<String, String> headers = new HashMap<String, String>();
    Map<String, String> body = new HashMap<String, String>();
    Map<String, String> params = new HashMap<String, String>();
    String leadId;
    String custId;
    String requestPath;
    Utilities util = new Utilities();
    String Aadhaar = util.ValidAadhaar();
    String BusinessOwnerPhotoDMSID;
    String AadharPhotoDMSID1;
    String AadharPhotoDMSID2;
    String BankStatementDMSID;
    String wfsidDiyUpdate;
    String BusinessProofDmsId;
    CreateLeadMerchantUpgradeLimit createLeadLimitUpgrade = new CreateLeadMerchantUpgradeLimit();
    UpdateLeadMerchantUpgradeLimit updateLeadMerchantUpgradeLimit = new UpdateLeadMerchantUpgradeLimit();
    FetchScreenDetailsUpgradeLimit fetchScreenDetailsUpgradeLimit = new FetchScreenDetailsUpgradeLimit();
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    String pan = "**********";
    @BeforeClass
    public void setup() throws Exception {
        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
        DBConnection dbConnection = new DBConnection();
        dbConnection.UpdateQueryToCloseLead(merchantMobileNumber, "diy_mco");
        headers.put("session_token", Token);
        headers.put("version", "7.3.0");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");
    }
    @Test(priority = 1, description = "To check that create limit upgrade lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createLead() throws JSONException {
        Map<String, String> params = new HashMap<String, String>();
        //params
        params.put("solution", "diy_mco");
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionTypeLevel2", solutionTypeLevel2);
        //headers
        headers.put("Content-Type", "application/json");
        headers.put("channel", channel);
        headers.put("session_token", Token);
        body.put("mid", mid);
        body.put("closeOpenLead", closeOpenLeadValue);
        body.put("leadContext", "REKYC_MERCHANT");
        requestPath = "MerchantService/V1/sdMerchant/lead/CreateLeadDiyRekycRequest.json";
        fetchResponse = createLeadLimitUpgrade.CreateLeadMerchantUpgradeLimit(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();
        if (httpcode == 200) {
            Assert.assertEquals(httpcode, 200);
            leadId = fetchResponse.jsonPath().getString("leadId");
        } else if (httpcode == 500 || httpcode == 400) {
            createLead();
        }
    }
    @Test(priority = 2, description = "To check that create limit upgrade lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addAadhaar() throws JSONException {
        Response responseBody = FetchScreenDetails(leadId);
        boolean AadhaarDigilockerRequired = checkNextScreenDetails(responseBody, "AADHAAR_DIGILOCKER");
        if (AadhaarDigilockerRequired) {
            Map<String, String> params = new HashMap<String, String>();
            params.put("solution", "diy_mco");
            params.put("entityType", entityType);
            params.put("channel", "DIY_P4B_APP");
            params.put("leadId", leadId);
            Map<String, String> header = new HashMap<String, String>();
            header.put("Content-Type", "application/json");
            header.put("session_token", Token);
            header.put("androidId", "AashitAndroid");
            header.put("browserName", "chrome");
            header.put("browserVersion", "4.6.3");
            header.put("cache-control", "no-cache");
            header.put("ipAddress", "************");
            header.put("latitude", "28.32");
            header.put("longitude", "77.213");
            header.put("imei", "1234");
            header.put("channel", "DIY_P4B_APP");
            Map<String, String> body = new HashMap<String, String>();
            body.put("aadharNumber", Aadhaar);
            body.put("partialSave", "true");
            body.put("AADHAR_OCR_DONE", "true");
            body.put("AADHAR_NO_MISMATCHED", "false");
            body.put("AADHAR_NO_NOT_READABLE", "false");
            body.put("GENDER", "Female");
            body.put("MERCHANT_DOB", "12/27/1988");
            body.put("NAME_AS_PER_AADHAR", "TOUCH_WOOD_LIMITED");
            body.put("userAddressCity", "Jind");
            body.put("userAddressLandMark", "ANMOL JAIN");
            body.put("userAddressLine1", "9187");
            body.put("userAddressLine2", "Ahxhshj");
            body.put("userAddressLine3", "Sjjsiskso");
            body.put("userAddressPincode", "126102");
            body.put("userAddressState", "Haryana");
            requestPath = "MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOUpdateLeadAadhaarRequest.json";
            fetchResponse = gs.getStartedResponse(requestPath, header, params, body);
            int statusCode = fetchResponse.getStatusCode();
            Assert.assertEquals(statusCode, 200);
        }
    }
    @Test(priority = 3, description = "To check that create limit upgrade lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void uploadAadhaarPhoto() throws JSONException, Exception {
        Response responseBody = FetchScreenDetails(leadId);
        boolean AadhaarDigilockerRequired = checkNextScreenDetails(responseBody, "AADHAAR_DIGILOCKER");
        if (AadhaarDigilockerRequired) {
            String endPoint = P.API.get("UploadAadhaarDoc");
            headers.put("Content-Type", "multipart/form-data");
            Map<String, String> params = new HashMap<String, String>();
            params.put("docType", "poi");
            params.put("docProvided", "aadhaar");
            params.put("entityType", entityType);
            params.put("solutionType", "diy_mco");
            params.put("channel", "DIY_P4B_APP");
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "1");
            File uploadFile = new File(DocPath);
            Response resp = uploadDocumentToAPI(uploadFile, params, endPoint);
            int statusCode = resp.getStatusCode();
            Assert.assertEquals(statusCode, 200);
            //uploading page 2 of aadhar
            params.put("pageNo", "2");
            Response resp2 = uploadDocumentToAPI(uploadFile, params, endPoint);
            int statusCode2 = resp2.getStatusCode();
            Assert.assertEquals(statusCode2, 200);
        }
    }
    @Test(priority = 4, description = "To check that create limit upgrade lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void uploadBusinessOwnerPhoto() throws JSONException, Exception {
        Response responseBody = FetchScreenDetails(leadId);
        if (checkNextScreenDetails(responseBody, "CLICK_MERCHANT_PHOTO")) {
            String endPoint = P.API.get("UploadDoc");
            Map<String, String> params = new HashMap<String, String>();
            headers.put("Content-Type", "multipart/form-data");
            params.put("solution", solution);
            params.put("channel", channel);
            params.put("entityType", entityType);
            params.put("solutionLeadId", leadId);
            params.put("pageNo", "1");
            params.put("docType", "businessOwnerPhoto");
            params.put("docProvided", "businessOwnerPhoto");
            params.put("solutionTypeLevel2", "diy_upgrade");
            File uploadFile = new File(DocPath);
            Response resp2 = uploadDocumentToAPI(uploadFile, params, endPoint);
            int statusCode2 = resp2.getStatusCode();
            Assert.assertEquals(statusCode2, 200);
        }
    }
    @Test(priority = 5, description = "To check that create limit upgrade lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void enterPanManually() throws JSONException {
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);
        headers.put("channel", channel);
        params.put("solution", "diy_mco");
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("leadId", leadId);
        body.put("partialSave", "true");
        body.put("pan", pan);
        body.put("gstin", "");
        body.put("PAN", pan);
        body.put("BUSINESS_ENTITY", entityType);
        body.put("KYB_BUSINESS_ID", "");
        requestPath = "MerchantService/V1/sdMerchant/lead/updatePanLeadRequest.json";
        fetchResponse = updateLeadMerchantUpgradeLimit.updateLeadMerchantUpgradeLimit(requestPath, headers, params, body);
        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 200);
    }
    @Test(priority = 6, description = "Upload Document", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void skipGSTProof() throws JSONException {
        if (entityType.equalsIgnoreCase("INIDVIDUAL")) {
            headers.put("Content-Type", "application/json");
            headers.put("session_token", Token);
            headers.put("channel", channel);
            params.put("solution", "diy_mco");
            params.put("channel", channel);
            params.put("entityType", entityType);
            params.put("leadId", leadId);
            params.put("solutionTypeLevel2", solutionTypeLevel2);
            body.put("partialSave", "true");
            body.put("IS_GST_SKIPPED", "true");
            requestPath = "MerchantService/V1/sdMerchant/lead/skipGstRequest.json";
            fetchResponse = updateLeadMerchantUpgradeLimit.updateLeadMerchantUpgradeLimit(requestPath, headers, params, body);
            int statusCode = fetchResponse.getStatusCode();
            Assert.assertEquals(statusCode, 200);
        }
    }
    @Test(priority = 7, description = "Upload Document", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void skipBusinessProof() throws JSONException {
        if (entityType.equalsIgnoreCase("INDIVIDUAL")) {
            headers.put("Content-Type", "application/json");
            headers.put("session_token", Token);
            headers.put("channel", channel);
            params.put("solution", "diy_mco");
            params.put("channel", channel);
            params.put("entityType", entityType);
            params.put("leadId", leadId);
            params.put("solutionTypeLevel2", solutionTypeLevel2);
            body.put("partialSave", "true");
            body.put("IS_BUSINESS_PROOF_SKIPPED", "true");
            requestPath = "MerchantService/V1/sdMerchant/lead/skipBusinessProofRequest.json";
            fetchResponse = updateLeadMerchantUpgradeLimit.updateLeadMerchantUpgradeLimit(requestPath, headers, params, body);
            int statusCode = fetchResponse.getStatusCode();
            Assert.assertEquals(statusCode, 200);
        }
    }
    @Test(priority = 8, description = "Upload Document", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void manuallyEnterAccountType() throws JSONException {
        if (entityType.equalsIgnoreCase("INDIVIDUAL")) {
            headers.put("Content-Type", "application/json");
            headers.put("session_token", Token);
            headers.put("channel", channel);
            params.put("solution", "diy_mco");
            params.put("channel", channel);
            params.put("entityType", entityType);
            params.put("leadId", leadId);
            params.put("solutionTypeLevel2", solutionTypeLevel2);
            body.put("partialSave", "true");
            body.put("USER_INPUT_ACCOUNT_TYPE", "Savings Account");
            requestPath = "MerchantService/V1/sdMerchant/lead/manuallySubmitAccountType.json";
            fetchResponse = updateLeadMerchantUpgradeLimit.updateLeadMerchantUpgradeLimit(requestPath, headers, params, body);
            int statusCode = fetchResponse.getStatusCode();
            Assert.assertEquals(statusCode, 200);
        }
    }
    @Test(priority = 9, description = "Upload Document", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @DataProvider(name = "documentData")
    public Object[][] documentData() {
        if (entityType.equalsIgnoreCase("PRIVATE_LIMITED") || entityType.equalsIgnoreCase("PUBLIC_LIMITED")) {
            return new Object[][]{
                    {"POIAWB", "certificateOfPractice_poiawb"},
                    {"coi", "COI"},
                    {"POAWTN", "franchiseeAgreement_poawtn"}
            };
        } else if (entityType.equalsIgnoreCase("INDIVIDUAL")) {
            return new Object[][]{
                    {"bankProof", "BankStatement"}
            };
        } else
            return new Object[][]{
                    {"businessProof", "GSTCertificate"},
                    {"shopInnerPhoto", "shopInnerPhoto"},
                    {"qrStickerPhoto", "qrSticker1"},
                    {"shopFrontPhoto", "shopFrontPhoto"},
                    {"bankProof", "BankStatement"}
            };
    }
    @Test(priority = 10, description = "Upload Documents Dynamically", dataProvider = "documentData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void uploadDocument(String docType, String docProvided) throws Exception {
        String endPoint = P.API.get("UploadDoc");
        headers.put("Content-Type", "multipart/form-data");
        File uploadFile = new File(DocPath);
        Map<String, String> params = new HashMap<>();
        params.put("solutionType", solution);
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("solutionLeadId", leadId);
        params.put("pageNo", "1");
        params.put("solutionTypeLevel2", solutionTypeLevel2);
        params.put("docType", docType);
        params.put("docProvided", docProvided);
        fetchResponse = uploadDocumentToAPI(uploadFile, params, endPoint);
        Assert.assertEquals(fetchResponse.getStatusCode(), 200);
    }
    @Test(priority = 11, description = "To check that upload document is working", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void SubmitLead() throws JSONException {
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);
        headers.put("channel", channel);
        params.put("solution", "diy_mco");
        params.put("channel", channel);
        params.put("entityType", entityType);
        params.put("leadId", leadId);
        body.put("partialSave", "false");
        body.put("IS_AGREEMENT_ACCEPTED", "true");
        requestPath = "MerchantService/V1/sdMerchant/lead/updateLead/LimitUpgradeSubmitLeadRequest.json";
        fetchResponse = updateLeadMerchantUpgradeLimit.updateLeadMerchantUpgradeLimit(requestPath, headers, params, body);
        int statusCode = fetchResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 12, description = "", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void diyUpgradeFetchDocStatus() throws Exception {
        custId = GetResourceOwnerId(merchantMobileNumber, "paytm@123");
        this.Token = BaseMethod.ApplicantToken(merchantMobileNumber, "paytm@123");
        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();
        Map<String, String> headers = new HashMap<>();
        headers.put("session_token", Token); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "multipart/json");
        headers.put("version", "7.2.8"); // No change
        headers.put("deviceidentifier", "Xiaomi-RedmiNote9Pro-efc28fc97cede738"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solution", "diy_mco");
        queryParams.put("entityType", entityType);
        queryParams.put("leadId", leadId);
        queryParams.put("merchantCustId", custId);
        queryParams.put("channel", "DIY_P4B_APP");
        queryParams.put("solutionTypeLevel2", solutionTypeLevel2);
        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj, headers, queryParams);
        BusinessOwnerPhotoDMSID = respObj.path("uploadedDocDetailsSet[0].uploadedDocs[0].uuid");
        AadharPhotoDMSID1 = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
        AadharPhotoDMSID2 = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['2']");
        BankStatementDMSID = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
        System.out.println(BankStatementDMSID + AadharPhotoDMSID1 + AadharPhotoDMSID2 );
    }
    @Test(description = "DIY MCO Retail Onboarding QC", priority = 13, dependsOnMethods = "diyUpgradeFetchDocStatus")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void DiyUpgradeQC() throws Exception {
        DBConnection dbConnectionObj = new DBConnection();
        int Ubmid = dbConnectionObj.getUserBusinessMappingId(merchantMobileNumber, "diy_mco", solutionTypeLevel2);
        dbConnectionObj.assignAgentViaDB("1152", Ubmid);
        Long diyUpgradeWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);
        //Save value of diyUpgradeWorkflowStatusId in string
        wfsidDiyUpdate = String.valueOf(diyUpgradeWorkflowStatusId);
        requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadDiyRekycIndividual.json";
        EditLead EditLeadObj = new EditLead(leadId, requestPath);
        EditLeadObj.getProperties().setProperty("uuidAadharFront", AadharPhotoDMSID1);
        EditLeadObj.getProperties().setProperty("uuidAadharBack", AadharPhotoDMSID2);
        EditLeadObj.getProperties().setProperty("uuidBankStatementPhoto", BankStatementDMSID);
        EditLeadObj.getProperties().setProperty("workflowStatusId", wfsidDiyUpdate);
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("action", "SUBMIT");
        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("Cookie", XMWCookie);
        Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);
    }
    private Response uploadDocumentToAPI(File uploadFile, Map<String, String> params, String endPoint) {
        String baseURI = P.API.get("api_url");
        RequestSpecification spec = new RequestSpecBuilder().setBaseUri(baseURI).build();
        try {
            return RestAssured.given()
                    .multiPart(uploadFile)
                    .spec(spec)
                    .relaxedHTTPSValidation()
                    .queryParams(params)
                    .headers(headers)
                    .post(endPoint);
        } catch (Exception e) {
            return null;
        }
    }
    public Response FetchScreenDetails(String leadId) throws JSONException {
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);
        //  params.put("solution", "diy_mco");
        params.put("leadId", leadId);
        params.put("fetchStrategy", "SCREEN_DETAILS");
        params.put("entityType", entityType);
        params.put("solutionTypeLevel2", solutionTypeLevel2);
        requestPath = "MerchantService/V1/sdMerchant/lead/fetchScreenDetailsUpgradeLimitRequest.json";
        fetchResponse = fetchScreenDetailsUpgradeLimit.FetchScreenDetailsUpgradeLimit(requestPath, headers, params);
        int httpcode = fetchResponse.getStatusCode();
        Assert.assertEquals(httpcode, 200);
        Response responseBody = fetchResponse;
        return responseBody;
    }
    public boolean checkNextScreenDetails(Response checkNextScreenResponse, String screenName) {
        // Convert the response body to a JSONObject
        JsonPath jsonPath = checkNextScreenResponse.jsonPath();
        // Check if the "nextScreenDetails" array contains the value "AADHAAR_DIGILOCKER" for "eligibleScreen"
        boolean isScreenPresent = jsonPath.getList("nextScreenDetails.eligibleScreen")
                .stream()
                .anyMatch(screen -> screenName.equals(screen));
        return isScreenPresent;
    }
}