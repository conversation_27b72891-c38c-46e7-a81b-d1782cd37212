package OCL.DIY.MposDIY;

import Request.MerchantService.oe.V1.Payment.Order.NotifyCallback;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.v1.Payments.leads.status.FetchLeadMposDIY;
import Request.MerchantService.v1.profile.Update;
import Request.MerchantService.v1.sdMerchant.AcceptTermsAndConditionsEdcDIY;
import Request.MerchantService.v1.sdMerchant.CreateLeadMposDIY;
import Request.MerchantService.v2.edc.plans.fetchPlanEdcDIY;
import Request.MerchantService.v2.edc.validateOrder.validateOrderEdcDIY;
import Request.OMS.AuthorizeOMS;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.OMS.CheckoutAuthorizeOMS;
import Services.Utilities.TestBase;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowMposDIY extends BaseMethod
{
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowMposDIY.class);

    public static String MerchantToken = "";
    public static String mid = "MPYnOd08861029683358";
    public static String channel = "DIY_P4B_APP";
    public static String solutionType = "mpos";
    public static String OrderId = "";
    public static String merchantMobileNumber = "5555505151";
    public static String productId = "";
    public static String custId = "1001844592";
    public static String price = "";
    public static String cookie="";
    public static String edcPlanId = "";
    public  static String access_token="";
    public static String leadStage="";
    public static String leadId = "";
    public static String item_id="";
    public static String MposRentalCharge="";
    public static String pgProfileLead="";
    public static String leadStagePGProfile="";

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void merchantLoginMposDIY() throws Exception
    {
        MerchantToken = ApplicantToken(merchantMobileNumber, "paytm@123");
        establishConnectiontoServer(MerchantToken,5);
        LOGGER.info("Merchant token for EDC DIY : " + MerchantToken);
        LOGGER.info(" Inside DB execution to reset lead : ");
        waitForLoad(3000);
        DBConnection.UpdateQueryToCloseLeadsolnlevel2(merchantMobileNumber,"brand_emi","mpos");
        /*TestBase testBase = new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '" + merchantMobileNumber + "' and status = '0' and solution_type='mpos';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " + UpdateRes); */

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch plan for mpos DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchPlanforMposDIY()
    {
        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("deviceSerialNumber", "OnePlus-GM1901-263ac034140468ee");
        queryParams.put("latitude", "29.9385826");
        queryParams.put("longitude", "77.5462696");
        queryParams.put("osType", "Android");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 2, groups = {"Regression"}, description = "Fetch plan for invalid mid for mpos DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchPlanforInvalidMidMposDIY()
    {
        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", "pXXIQZ34665245852304");
        queryParams.put("solutionType", solutionType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }


    @Test(priority = 3, groups = {"Regression"}, description = "Fetch product id for EMI offering DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchProductIdforEMIOfferingDIY() {
        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("deviceSerialNumber", "OnePlus-GM1901-263ac034140468ee");
        queryParams.put("latitude", "29.9385826");
        queryParams.put("longitude", "77.5462696");
        queryParams.put("osType", "Android");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        productId=fetchplanEMIOfferingDIYResponse.jsonPath().getJsonObject("productId").toString();
        System.out.println("Product id is " + productId);

    }

    @Test(priority = 4, groups = {"Regression"}, description = "Fetch price for mpos DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchPriceforMposDIY() {
        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("deviceSerialNumber", "OnePlus-GM1901-263ac034140468ee");
        queryParams.put("latitude", "29.9385826");
        queryParams.put("longitude", "77.5462696");
        queryParams.put("osType", "Android");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        price=fetchplanEMIOfferingDIYResponse.jsonPath().getJsonObject("price").toString();
        System.out.println("Price is " + price);

    }

    @Test(priority = 5, groups = {"Regression"}, description = "Fetch rental amount for mpos DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchRentalAmountforMposDIY() {
        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        queryParams.put("deviceSerialNumber", "OnePlus-GM1901-263ac034140468ee");
        queryParams.put("latitude", "29.9385826");
        queryParams.put("longitude", "77.5462696");
        queryParams.put("osType", "Android");
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        MposRentalCharge=fetchplanEMIOfferingDIYResponse.jsonPath().getJsonObject("rentalAmount").toString();
        System.out.println("Mpos Rental charge is " + MposRentalCharge);

    }

    @Test(priority = 6, groups = {"Regression"}, description = "Fetch mpos plan id for mpos DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchMposPlanIdforMposDIY() {
        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("deviceSerialNumber", "OnePlus-GM1901-263ac034140468ee");
        queryParams.put("latitude", "29.9385826");
        queryParams.put("longitude", "77.5462696");
        queryParams.put("osType", "Android");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        edcPlanId=fetchplanEMIOfferingDIYResponse.jsonPath().getJsonObject("id").toString();
        System.out.println("Mpos plan id is " + edcPlanId);

    }

    @Test(priority = 7, groups = {"Regression"}, description = "Fetch plan for invalid solution type for mpos DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchPlanforInvalidSolutionTypeMposDIY()
    {
        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", "mpos1");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }

    @Test(priority = 8, groups = {"Regression"}, description = "Fetch plan with additional params for mpos DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchPlanWithAdditionalParamsforMposDIY()
    {
        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("deviceSerialNumber", "OnePlus-GM1901-263ac034140468ee");
        queryParams.put("latitude", "29.9385826");
        queryParams.put("longitude", "77.5462696");
        queryParams.put("osType", "Android");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }
    @Test(priority = 8, groups = {"Regression"}, description = "Fetch plan with small merchant flag for mpos DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchPlanWithSmallMerchantFlagMposDIY()
    {
        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("deviceSerialNumber", "OnePlus-GM1901-263ac034140468ee");
        queryParams.put("latitude", "29.9385826");
        queryParams.put("longitude", "77.5462696");
        queryParams.put("osType", "Android");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
       String actual= fetchplanEMIOfferingDIYResponse.jsonPath().getJsonObject("smallMerchantDeclaration").toString();
      System.out.println("Small Merchant flag is :" +actual);
    }

    @Test(priority = 8, groups = {"Regression"}, description = "Fetch plan with small merchant flag for mpos DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchPlanWithSmallMerchantEditDisableFlagMposDIY()
    {
        fetchPlanEdcDIY fetchPlanEMIOfferingDIYObj = new fetchPlanEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("channel", channel);
        queryParams.put("mid", mid);
        queryParams.put("solutionType", solutionType);
        queryParams.put("deviceSerialNumber", "OnePlus-GM1901-263ac034140468ee");
        queryParams.put("latitude", "29.9385826");
        queryParams.put("longitude", "77.5462696");
        queryParams.put("osType", "Android");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response fetchplanEMIOfferingDIYResponse = middlewareServicesObject.fetchPlanEdcDIY(fetchPlanEMIOfferingDIYObj, queryParams, headers);
        int StatusCode = fetchplanEMIOfferingDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        String smallFlag= fetchplanEMIOfferingDIYResponse.jsonPath().getJsonObject("smallMerchantDeclarationEditDisable").toString();
        System.out.println("Small Merchant flag is :" +smallFlag);
    }

    @Test(priority = 9, groups = {"Regression"}, description = "Accept terms & conditions for mpos DIY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AcceptTermsAndConditionsEdcDIY()
    {
        AcceptTermsAndConditionsEdcDIY AcceptTermsAndConditionsEdcDIYObj = new AcceptTermsAndConditionsEdcDIY();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType", "individual");
        queryParams.put("solution", "mpos");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", MerchantToken);
        Response AcceptTermsAndConditionsEdcDIYResponse = middlewareServicesObject.AcceptTermsAndConditionsEdcDIY(AcceptTermsAndConditionsEdcDIYObj, queryParams, headers);
        int StatusCode = AcceptTermsAndConditionsEdcDIYResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 10, description = "Validate order for mpos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void validateOrderforMposDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderMposDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");


        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("customerId", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("price", price);
        body.put("customer_id", custId);

        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);

    }

    @Test(priority = 11, description = "invalid price for Mpos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidPriceforMposDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderMposDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("customerId", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("price", "1234");
        body.put("customer_id", custId);


        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);
        String ExpectedMsg = "\"Order validation failed!\"";
        String ActualMsg = respObj.jsonPath().getJsonObject("error_title").toString();
        Assert.assertTrue(ExpectedMsg.contains(ActualMsg));

    }

    @Test(priority = 12, description = "invalid plan id for Mpos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidPlanIdforMposDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderMposDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("customerId", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", "9");
        body.put("price", price);
        body.put("customer_id", custId);


        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);
        String ExpectedMsg = "\"Order validation failed!\"";
        String ActualMsg = respObj.jsonPath().getJsonObject("error_title").toString();
        Assert.assertTrue(ExpectedMsg.contains(ActualMsg));

    }


    @Test(priority = 13, description = "invalid productId for mpos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidProductIdforMposDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderMposDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("customerId", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", "12020193151");
        body.put("edcPlanId", edcPlanId);
        body.put("price", price);
        body.put("customer_id", custId);


        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);
        String ExpectedMsg = "\"Order validation failed!\"";
        String ActualMsg = respObj.jsonPath().getJsonObject("error_title").toString();
        Assert.assertTrue(ExpectedMsg.contains(ActualMsg));

    }

    @Test(priority = 14, description = "invalid solution type for Mpos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void invalidSolutionTypeforMposDiy() {
        validateOrderEdcDIY validateOrderforEdcDiyObj = new validateOrderEdcDIY(P.TESTDATA.get("validateOrderMposDIY"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("customerId", custId);
        body.put("solutionType", "mpos1");
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("price", price);
        body.put("customer_id", custId);


        Response respObj = middlewareServicesObject.validateOrderEdcDIY(validateOrderforEdcDiyObj, body, headers);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(500,statusCode);
        String ExpectedMsg = "\"Order validation failed!\"";
        String ActualMsg = respObj.jsonPath().getJsonObject("error_title").toString();
        Assert.assertTrue(ExpectedMsg.contains(ActualMsg));

    }
    @Test(priority = 15, description = "Create access token for mpos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createAccessTokenMposDIY()
    {

        AuthorizeOMS authorizeOMSObj=new AuthorizeOMS();
        CheckoutAuthorizeOMS checkoutObj=new CheckoutAuthorizeOMS();
        access_token= checkoutObj.CheckoutAuthorizeviaOMS(authorizeOMSObj).jsonPath().getJsonObject("access_token");
        System.out.println("Access token is " +access_token);

    }

    @Test(priority = 16, description = "Create QR for Mpos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createQRforMposDiy()
    {


        OrderId=createQRviaOMS(leadId,merchantMobileNumber,MerchantToken);
        System.out.println("Order id is " + OrderId);


    }
    @Test(priority = 17, description = "Fetch payment status for mpos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchPaymentStatusforMposDiy()
    {
        item_id=fetchOrderviaOMS(OrderId);
        System.out.println("Item id is " + item_id);


    }

    @Test(priority = 17, description = "fetch  lead before lead creation upfront status for Mpos diy", groups = {"Regression"},dependsOnMethods = {"fetchPaymentStatusforMposDiy"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchLeadMposDiyBeforeLeadCreation()
    {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "mpos");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "DIY_P4B_APP");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",MerchantToken);


        FetchLeadMposDIY FetchLeadMposDIYObj=new FetchLeadMposDIY();
        Response FetchLeadMposDIYObjResp = middlewareServicesObject.fetchLeadMposDIY(FetchLeadMposDIYObj,queryParams,headers );
        leadStage = FetchLeadMposDIYObjResp.jsonPath().getJsonObject("solutions.leadSubStatus").toString();
        System.out.println("Lead stage is :" +leadStage);
        int statusCode=FetchLeadMposDIYObjResp.getStatusCode();
        Assert.assertEquals(200,statusCode);
    }


    @Test(priority = 17, description = "create  lead upfront status for Mpos diy", groups = {"Regression"},dependsOnMethods = "fetchLeadMposDiyBeforeLeadCreation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createLeadMposDiy()
    {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "mpos");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "DIY_P4B_APP");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",MerchantToken);

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("isSmallMerchant", "false");
        body.put("deviceSerialNumber", "f0225c338c24411WQ");
        body.put("edcPlanId", edcPlanId);

        CreateLeadMposDIY CreateLeadMposDIYOBj=new CreateLeadMposDIY();
        Response v1FetchLeadResp = middlewareServicesObject.CreateLeadMosDIY(CreateLeadMposDIYOBj,queryParams,headers,body );
        leadId = v1FetchLeadResp.jsonPath().getJsonObject("leadId").toString();
        System.out.println("Lead id is "+ leadId);
        int statusCode=v1FetchLeadResp.getStatusCode();
        Assert.assertEquals(200,statusCode);
    }

    @Test(priority = 17, description = "fetch  lead after lead creation upfront status for Mpos diy", groups = {"Regression"},dependsOnMethods = "createLeadMposDiy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchLeadMposDiyAfterLeadCreation()
    {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "mpos");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "DIY_P4B_APP");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",MerchantToken);


        FetchLeadMposDIY FetchLeadMposDIYObj=new FetchLeadMposDIY();
        Response FetchLeadMposDIYObjResp = middlewareServicesObject.fetchLeadMposDIY(FetchLeadMposDIYObj,queryParams,headers );
        leadStage = FetchLeadMposDIYObjResp.jsonPath().getJsonObject("solutions.leadSubStatus").toString();
        System.out.println("Lead stage is :" +leadStage);
        int statusCode=FetchLeadMposDIYObjResp.getStatusCode();
        Assert.assertEquals(200,statusCode);
    }


    @Test(priority = 17, description = "create  lead upfront status for Mpos diy", groups = {"Regression"},dependsOnMethods = "createLeadMposDiy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void LeadAlreadyExistMposDiy()
    {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "mpos");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "DIY_P4B_APP");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",MerchantToken);

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("isSmallMerchant", "false");
        body.put("deviceSerialNumber", "f0225c338c24411WQ");
        body.put("edcPlanId", edcPlanId);

        CreateLeadMposDIY CreateLeadMposDIYOBj=new CreateLeadMposDIY();
        Response v1FetchLeadResp = middlewareServicesObject.CreateLeadMosDIY(CreateLeadMposDIYOBj,queryParams,headers,body );
        leadId = v1FetchLeadResp.jsonPath().getJsonObject("leadId").toString();
        String actual = v1FetchLeadResp.jsonPath().getJsonObject("isLeadAlreadyExists").toString();
        System.out.println("Lead id is "+ leadId);
        int statusCode=v1FetchLeadResp.getStatusCode();
        Assert.assertEquals(200,statusCode);
        String expected="true";
        Assert.assertTrue(actual.contains(expected));
    }

    @Test(priority = 18, description = "Notify Order Callback  for Mpos diy", groups = {"Regression"},dependsOnMethods = "LeadAlreadyExistMposDiy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void OrderNotifyMposDiyWithoutOrderID() {
        NotifyCallback createLeadEdcDIY = new NotifyCallback(P.TESTDATA.get("MposDIYOrderNotify"));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        Map<String, String> body = new HashMap<>();
        body.put("order_id", OrderId);
        body.put("mid", mid);
        body.put("customer_id", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("customerId",custId);
        body.put("item_id","13278527119");

        Response respObj = middlewareServicesObject.OrderNotify(createLeadEdcDIY, queryParams, headers, body);
        int statusCode = respObj.getStatusCode();
       Assert.assertEquals(200,statusCode);



    }

    @Test(priority = 18, description = "Notify Order Callback  for Mpos diy", groups = {"Regression"},dependsOnMethods = "OrderNotifyMposDiyWithoutOrderID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void OrderNotifyMposDiy() {
        NotifyCallback createLeadEdcDIY = new NotifyCallback(P.TESTDATA.get("MposDIYOrderNotify"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("order_id", OrderId);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        Map<String, String> body = new HashMap<>();
        body.put("order_id", OrderId);
        body.put("mid", mid);
        body.put("customer_id", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("customerId",custId);
        body.put("item_id","13278527119");

        Response respObj = middlewareServicesObject.OrderNotify(createLeadEdcDIY, queryParams, headers, body);
        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);



    }

    @Test(priority = 19, description = "Notify Order Callback for Mpos DIY", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void CallbackinprogressMposDiy() {
        NotifyCallback createLeadEdcDIY = new NotifyCallback(P.TESTDATA.get("MposDIYOrderNotify"));

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("order_id", OrderId);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        Map<String, String> body = new HashMap<>();
        body.put("order_id", OrderId);
        body.put("mid", mid);
        body.put("customer_id", custId);
        body.put("solutionType", solutionType);
        body.put("mobileNumber", merchantMobileNumber);
        body.put("product_id", productId);
        body.put("edcPlanId", edcPlanId);
        body.put("customerId",custId);
        body.put("item_id","13278527119");

        Response respObj = middlewareServicesObject.OrderNotify(createLeadEdcDIY, queryParams, headers, body);
        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);

    }

    @Test(priority = 19, description = "fetch  lead status after callback upfront status for Mpos diy", groups = {"Regression"},dependsOnMethods = "CallbackinprogressMposDiy")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchLeadMposDiyAfterCallback()
    {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "mpos");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "DIY_P4B_APP");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",MerchantToken);


        FetchLeadMposDIY FetchLeadMposDIYObj=new FetchLeadMposDIY();
        Response FetchLeadMposDIYObjResp = middlewareServicesObject.fetchLeadMposDIY(FetchLeadMposDIYObj,queryParams,headers );
        leadStage = FetchLeadMposDIYObjResp.jsonPath().getJsonObject("solutions.leadSubStatus").toString();
        System.out.println("Lead stage is :" +leadStage);
        int statusCode=FetchLeadMposDIYObjResp.getStatusCode();
        Assert.assertEquals(200,statusCode);
    }

    @Test(priority = 19, description = "fetch  lead status with invalid channel", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchLeadMposWithInvalidChannel()
    {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "mpos");
        queryParams.put("entityType", "INDIVIDUAL");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",MerchantToken);


        FetchLeadMposDIY FetchLeadMposDIYObj=new FetchLeadMposDIY();
        Response FetchLeadMposDIYObjResp = middlewareServicesObject.fetchLeadMposDIY(FetchLeadMposDIYObj,queryParams,headers );
//        leadStage = FetchLeadMposDIYObjResp.jsonPath().getJsonObject("solutions.leadSubStatus").toString();
      //  System.out.println("Lead stage is :" +leadStage);
       int statusCode=FetchLeadMposDIYObjResp.getStatusCode();
        Assert.assertEquals(400,statusCode);
    }

    @Test(priority = 20, description = "fetch lead for Mpos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchLeadMposDiy()
    {
        waitForLoad(5000);
        FetchLead v1FetchLeadObj=new FetchLead(leadId);
        cookie=findXMWTokenforPanel("7771216290","paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, cookie);
        leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Lead stage is "+ leadStage);
    }

    @Test(priority = 21, description = "fetch lead status for Mpos diy", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchStatusLeadMposDiy()
    {
        waitForLoad(10000);
        FetchLead v1FetchLeadObj=new FetchLead(leadId);
        cookie=findXMWTokenforPanel("7771216290","paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, cookie);
        leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Lead stage is "+ leadStage);
    }

    @Test(priority = 22, description = "Add gst for mpos", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AddGSTForMpos()
    {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "PROPRIETORSHIP");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionSubType", "ADD_GSTIN");
        queryParams.put("p2mEnable", "true");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",MerchantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("gstin","09FRTPT4567Y4ZU");

        Update addGstObj=new Update(P.TESTDATA.get("updateGST"));
        Response addGstResp = middlewareServicesObject.UpdateGSTMpos(addGstObj,queryParams,headers,body);
      pgProfileLead= addGstResp.jsonPath().getJsonObject("leadId").toString();
      System.out.println("PG profile lead id is :"+pgProfileLead);
   int statusCode=addGstResp.getStatusCode();
   Assert.assertEquals(200,statusCode);
        String actual=addGstResp.jsonPath().getJsonObject("nameMatchSuccess").toString();
        String expected="true";
        Assert.assertTrue(expected.contains(actual));
    }

    @Test(priority = 23, description = "Add gst for mpos", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void IsGSTValidForMpos()
    {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "PROPRIETORSHIP");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionSubType", "ADD_GSTIN");
        queryParams.put("p2mEnable", "true");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",MerchantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("gstin","09FRTPT4567Y4ZU");

        Update addGstObj=new Update(P.TESTDATA.get("updateGST"));
        Response addGstResp = middlewareServicesObject.UpdateGSTMpos(addGstObj,queryParams,headers,body);

    }

    @Test(priority = 24, description = "name match success for mpos", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void IsNameMatchSuccessForMpos()
    {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "PROPRIETORSHIP");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionSubType", "ADD_GSTIN");
        queryParams.put("p2mEnable", "true");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token",MerchantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("gstin","09FRTPT4567Y4ZU");

        Update addGstObj=new Update(P.TESTDATA.get("updateGST"));
        Response addGstResp = middlewareServicesObject.UpdateGSTMpos(addGstObj,queryParams,headers,body);

    }

    @Test(priority = 25, description = "fetch lead for pg profile  lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchLeadPgProfileLead()
    {
        waitForLoad(5000);
        FetchLead v1FetchLeadObj=new FetchLead(pgProfileLead);
        cookie=findXMWTokenforPanel("7771216290","paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, cookie);
        leadStagePGProfile = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Lead stage is "+ leadStagePGProfile);
    }

    @Test(priority = 26, description = "fetch lead for pg profile  lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void fetchLeadPgProfileLeadAfterMaquette()
    {
        waitForLoad(10000);
        FetchLead v1FetchLeadObj=new FetchLead(pgProfileLead);
        cookie=findXMWTokenforPanel("7771216290","paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, cookie);
        leadStagePGProfile = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Lead stage is "+ leadStagePGProfile);
    }

}
