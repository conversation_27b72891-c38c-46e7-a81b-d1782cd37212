package OCL.SHOPinsurance;

import Request.Shopinsurance.Embeddedshopinsurance;
import Request.Shopinsurance.ShopInsuranceConfirmPolicy;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class EDCShopInsuranceConfirmPolicy extends BaseMethod
{
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();

	private static final Logger LOGGER = LogManager.getLogger(EDCShopInsuranceConfirmPolicy.class);
	public static String clientId = "OE";
	public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";


	String sessiontoken;
	String agentvar = "8010630022";

	//sessiontoken = ApplicantToken(agentvar, "paytm@123");
	//String sessiontoken = AgentSessionToken("9891497839", "paytm@123");

	@Test(priority = 0,description = "Checking response of a validate policyID")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_001_StatusCode()
	{
		ShopInsuranceConfirmPolicy reqobj = new ShopInsuranceConfirmPolicy(P.TESTDATA.get("ConfirmpolicyRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("orderId", "100076043112");
		body.put("cartItemId", "1349569877796");
		body.put("source", "EDC");
		body.put("policyId", "1349569877796");
		body.put("policyHolderName", "TOUCH WOOD LIMITED");
		body.put("shopName", "Bhumi enterprises");
		body.put("policyHolderPhone", "9999111753");
		body.put("policyHolderEmail", "null");
		body.put("policyHolderAddress", "****iabad ,Chiranjiv Vihar ,Ansals Chiranjiv Vihar ,Ghaziabad ,Ghaziabad ,Meerut Division ,Ghaziabad,Uttar Pradesh,201002");
		body.put("city", "****iabad");
		body.put("pincode", "201002");
		body.put("category", "CDIT");
		body.put("subCategory", "Mobile and Tablets");



		Response respObj = MiddlewareServicesObject.ShopInsuranceConfirmPolicyMethod(reqobj,body,headers);
		Assert.assertEquals(respObj.statusCode(), 412);

	}

	@Test(priority = 0,description = "order ID is null")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_002_orderIDnull()
	{
		ShopInsuranceConfirmPolicy reqobj = new ShopInsuranceConfirmPolicy(P.TESTDATA.get("ConfirmpolicyRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("orderId", "");
		body.put("cartItemId", "1349569877796");
		body.put("source", "EDC");
		body.put("policyId", "1349569877796");
		body.put("policyHolderName", "TOUCH WOOD LIMITED");
		body.put("shopName", "Bhumi enterprises");
		body.put("policyHolderPhone", "9999111753");
		body.put("policyHolderEmail", "null");
		body.put("policyHolderAddress", "****iabad ,Chiranjiv Vihar ,Ansals Chiranjiv Vihar ,Ghaziabad ,Ghaziabad ,Meerut Division ,Ghaziabad,Uttar Pradesh,201002");
		body.put("city", "****iabad");
		body.put("pincode", "201002");
		body.put("category", "CDIT");
		body.put("subCategory", "Mobile and Tablets");



		Response respObj = MiddlewareServicesObject.ShopInsuranceConfirmPolicyMethod(reqobj,body,headers);
		Assert.assertEquals(respObj.statusCode(), 412);

	}


	@Test(priority = 0,description = "cartitemID and policyID are different")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_003_cartandpolicyIDaresame()
	{
		ShopInsuranceConfirmPolicy reqobj = new ShopInsuranceConfirmPolicy(P.TESTDATA.get("ConfirmpolicyRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("orderId", "100076043112");
		body.put("cartItemId", "1349569877796");
		body.put("source", "EDC");
		body.put("policyId", "1349569878896");
		body.put("policyHolderName", "TOUCH WOOD LIMITED");
		body.put("shopName", "Bhumi enterprises");
		body.put("policyHolderPhone", "9999111753");
		body.put("policyHolderEmail", "null");
		body.put("policyHolderAddress", "****iabad ,Chiranjiv Vihar ,Ansals Chiranjiv Vihar ,Ghaziabad ,Ghaziabad ,Meerut Division ,Ghaziabad,Uttar Pradesh,201002");
		body.put("city", "****iabad");
		body.put("pincode", "201002");
		body.put("category", "CDIT");
		body.put("subCategory", "Mobile and Tablets");



		Response respObj = MiddlewareServicesObject.ShopInsuranceConfirmPolicyMethod(reqobj,body,headers);
		Assert.assertEquals(respObj.statusCode(), 412);

	}



	@Test(priority = 0,description = "without source device")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_004_sourcedevicemiss()
	{
		ShopInsuranceConfirmPolicy reqobj = new ShopInsuranceConfirmPolicy(P.TESTDATA.get("ConfirmpolicyRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("orderId", "100076043112");
		body.put("cartItemId", "1349569877796");

		body.put("policyId", "1349569877796");
		body.put("policyHolderName", "TOUCH WOOD LIMITED");
		body.put("shopName", "Bhumi enterprises");
		body.put("policyHolderPhone", "9999111753");
		body.put("policyHolderEmail", "null");
		body.put("policyHolderAddress", "****iabad ,Chiranjiv Vihar ,Ansals Chiranjiv Vihar ,Ghaziabad ,Ghaziabad ,Meerut Division ,Ghaziabad,Uttar Pradesh,201002");
		body.put("city", "****iabad");
		body.put("pincode", "201002");
		body.put("category", "CDIT");
		body.put("subCategory", "Mobile and Tablets");



		Response respObj = MiddlewareServicesObject.ShopInsuranceConfirmPolicyMethod(reqobj,body,headers);
		Assert.assertEquals(respObj.statusCode(), 412);

	}


	@Test(priority = 0,description = "without policy information")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_005_withoutpolicyinfor()
	{
		ShopInsuranceConfirmPolicy reqobj = new ShopInsuranceConfirmPolicy(P.TESTDATA.get("ConfirmpolicyRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("orderId", "100076043112");
		body.put("cartItemId", "1349569877796");
		body.put("source", "EDC");
//		body.put("policyId", "1349569877796");
//		body.put("policyHolderName", "TOUCH WOOD LIMITED");
//		body.put("shopName", "Bhumi enterprises");
//		body.put("policyHolderPhone", "9999111753");
//		body.put("policyHolderEmail", "null");
//		body.put("policyHolderAddress", "****iabad ,Chiranjiv Vihar ,Ansals Chiranjiv Vihar ,Ghaziabad ,Ghaziabad ,Meerut Division ,Ghaziabad,Uttar Pradesh,201002");
//		body.put("city", "****iabad");
//		body.put("pincode", "201002");
//		body.put("category", "CDIT");
//		body.put("subCategory", "Mobile and Tablets");



		Response respObj = MiddlewareServicesObject.ShopInsuranceConfirmPolicyMethod(reqobj,body,headers);
		Assert.assertEquals(respObj.statusCode(), 412);

	}


	@Test(priority = 0,description = "access token is missing")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_006_withoutaccesstoken()
	{
		ShopInsuranceConfirmPolicy reqobj = new ShopInsuranceConfirmPolicy(P.TESTDATA.get("ConfirmpolicyRequest"));

		Map<String, String> headers = new HashMap<String, String>();

	//	headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("orderId", "100076043112");
		body.put("cartItemId", "1349569877796");
		body.put("source", "EDC");
		body.put("policyId", "1349569877796");
		body.put("policyHolderName", "TOUCH WOOD LIMITED");
		body.put("shopName", "Bhumi enterprises");
		body.put("policyHolderPhone", "9999111753");
		body.put("policyHolderEmail", "null");
		body.put("policyHolderAddress", "****iabad ,Chiranjiv Vihar ,Ansals Chiranjiv Vihar ,Ghaziabad ,Ghaziabad ,Meerut Division ,Ghaziabad,Uttar Pradesh,201002");
		body.put("city", "****iabad");
		body.put("pincode", "201002");
		body.put("category", "CDIT");
		body.put("subCategory", "Mobile and Tablets");



		Response respObj = MiddlewareServicesObject.ShopInsuranceConfirmPolicyMethod(reqobj,body,headers);
		Assert.assertEquals(respObj.statusCode(), 412);
		LOGGER.info("message :" + respObj.jsonPath().getString("error_message"));

	}



	@Test(priority = 0,description = "Checking response of a validate policyID")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_007_withoutaccesstoken()
	{
		ShopInsuranceConfirmPolicy reqobj = new ShopInsuranceConfirmPolicy(P.TESTDATA.get("ConfirmpolicyRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("orderId", "100076AS043112");
		body.put("cartItemId", "1349569877796");
		body.put("source", "EDC");
		body.put("policyId", "1349569877796");
		body.put("policyHolderName", "TOUCH WOOD LIMITED");
		body.put("shopName", "Bhumi enterprises");
		body.put("policyHolderPhone", "9999111753");
		body.put("policyHolderEmail", "null");
		body.put("policyHolderAddress", "****iabad ,Chiranjiv Vihar ,Ansals Chiranjiv Vihar ,Ghaziabad ,Ghaziabad ,Meerut Division ,Ghaziabad,Uttar Pradesh,201002");
		body.put("city", "****iabad");
		body.put("pincode", "201002");
		body.put("category", "CDIT");
		body.put("subCategory", "Mobile and Tablets");



		Response respObj = MiddlewareServicesObject.ShopInsuranceConfirmPolicyMethod(reqobj,body,headers);
		Assert.assertEquals(respObj.statusCode(), 412);
		LOGGER.info("message :" + respObj.jsonPath().getString("error_message"));

	}


	@Test(priority = 0,description = "adding email address")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_007_addmerchantEmail()
	{
		ShopInsuranceConfirmPolicy reqobj = new ShopInsuranceConfirmPolicy(P.TESTDATA.get("ConfirmpolicyRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("orderId", "100076AS043112");
		body.put("cartItemId", "1349569877796");
		body.put("source", "EDC");
		body.put("policyId", "1349569877796");
		body.put("policyHolderName", "TOUCH WOOD LIMITED");
		body.put("shopName", "Bhumi enterprises");
		body.put("policyHolderPhone", "9999111753");
		body.put("policyHolderEmail", "<EMAIL>");
		body.put("policyHolderAddress", "****iabad ,Chiranjiv Vihar ,Ansals Chiranjiv Vihar ,Ghaziabad ,Ghaziabad ,Meerut Division ,Ghaziabad,Uttar Pradesh,201002");
		body.put("city", "****iabad");
		body.put("pincode", "201002");
		body.put("category", "CDIT");
		body.put("subCategory", "Mobile and Tablets");



		Response respObj = MiddlewareServicesObject.ShopInsuranceConfirmPolicyMethod(reqobj,body,headers);
		Assert.assertEquals(respObj.statusCode(), 412);
		LOGGER.info("message :" + respObj.jsonPath().getString("error_message"));

	}



	@Test(priority = 0,description = "missing policy ID")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_008_missingPolicyID()
	{
		ShopInsuranceConfirmPolicy reqobj = new ShopInsuranceConfirmPolicy(P.TESTDATA.get("ConfirmpolicyRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("orderId", "100076AS043112");
		body.put("cartItemId", "1349569877796");
		body.put("source", "EDC");
//		body.put("policyId", "1349569877796");
		body.put("policyHolderName", "TOUCH WOOD LIMITED");
		body.put("shopName", "Bhumi enterprises");
		body.put("policyHolderPhone", "9999111753");
		body.put("policyHolderEmail", "<EMAIL>");
		body.put("policyHolderAddress", "****iabad ,Chiranjiv Vihar ,Ansals Chiranjiv Vihar ,Ghaziabad ,Ghaziabad ,Meerut Division ,Ghaziabad,Uttar Pradesh,201002");
		body.put("city", "****iabad");
		body.put("pincode", "201002");
		body.put("category", "CDIT");
		body.put("subCategory", "Mobile and Tablets");



		Response respObj = MiddlewareServicesObject.ShopInsuranceConfirmPolicyMethod(reqobj,body,headers);
		Assert.assertEquals(respObj.statusCode(), 412);
		LOGGER.info("message :" + respObj.jsonPath().getString("error_message"));

	}


	@Test(priority = 0,description = "missing policy ID")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_008_missingcartItemID()
	{
		ShopInsuranceConfirmPolicy reqobj = new ShopInsuranceConfirmPolicy(P.TESTDATA.get("ConfirmpolicyRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("orderId", "100076AS043112");
		body.put("cartItemId", "");
		body.put("source", "EDC");
		body.put("policyId", "1349569877796");
		body.put("policyHolderName", "TOUCH WOOD LIMITED");
		body.put("shopName", "Bhumi enterprises");
		body.put("policyHolderPhone", "9999111753");
		body.put("policyHolderEmail", "<EMAIL>");
		body.put("policyHolderAddress", "****iabad ,Chiranjiv Vihar ,Ansals Chiranjiv Vihar ,Ghaziabad ,Ghaziabad ,Meerut Division ,Ghaziabad,Uttar Pradesh,201002");
		body.put("city", "****iabad");
		body.put("pincode", "201002");
		body.put("category", "CDIT");
		body.put("subCategory", "Mobile and Tablets");



		Response respObj = MiddlewareServicesObject.ShopInsuranceConfirmPolicyMethod(reqobj,body,headers);
		Assert.assertEquals(respObj.statusCode(), 412);
		LOGGER.info("message :" + respObj.jsonPath().getString("error_message"));

	}
}
