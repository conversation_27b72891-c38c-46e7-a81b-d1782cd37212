package OCL.SHOPinsurance;

import Request.Shopinsurance.Embeddedshopinsurance;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class EligibilityShopInsurance extends BaseMethod
{
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();

	private static final Logger LOGGER = LogManager.getLogger(EligibilityShopInsurance.class);
	public static String clientId = "OE";
	public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";


	String sessiontoken;
	String agentvar = "8010630022";

	//sessiontoken = ApplicantToken(agentvar, "paytm@123");
	//String sessiontoken = AgentSessionToken("9891497839", "paytm@123");

	@Test(priority = 0,description = "Checking status code")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_001_StatusCode()
	{
		Embeddedshopinsurance reqobj = new Embeddedshopinsurance(P.TESTDATA.get("EmbeddedshopinsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("pincode", "201002");

		body.put("category", "Food");
		body.put("subCategory", "Foodcourt");
		body.put("source", "EDC");
		body.put("planType", "SHOP_INSURANCE");
		body.put("merchantId", "null");


		Response respObj = MiddlewareServicesObject.EmbeddedshopinsuranceMethod(reqobj, body,headers);
		Assert.assertEquals(respObj.statusCode(), 200);

	}

	@Test(priority = 0,description = "access token are missing in headers")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_002_withoutaccesstoken()
	{
		Embeddedshopinsurance reqobj = new Embeddedshopinsurance(P.TESTDATA.get("EmbeddedshopinsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		//headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("pincode", "201002");

		body.put("category", "Food");
		body.put("subCategory", "Foodcourt");
		body.put("source", "EDC");
		body.put("planType", "SHOP_INSURANCE");
		body.put("merchantId", "null");


		Response respObj = MiddlewareServicesObject.EmbeddedshopinsuranceMethod(reqobj, body,headers);
		Assert.assertEquals(respObj.statusCode(), 412);
		LOGGER.info("Error Message :" + respObj.jsonPath().getString("error_message"));

	}

	@Test(priority = 0,description = "Giving BlackListed Pincode")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_003_BlackListedPincode()
	{
		Embeddedshopinsurance reqobj = new Embeddedshopinsurance(P.TESTDATA.get("EmbeddedshopinsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("pincode", "173101");

		body.put("category", "Food");
		body.put("subCategory", "Foodcourt");
		body.put("source", "EDC");
		body.put("planType", "SHOP_INSURANCE");
		body.put("merchantId", "null");


		Response respObj = MiddlewareServicesObject.EmbeddedshopinsuranceMethod(reqobj, body,headers);
		Assert.assertEquals(respObj.statusCode(), 200);
		LOGGER.info("Status of Shop Insurance Eligibility :" + respObj.jsonPath().getString("status"));

	}

	@Test(priority = 0,description = "Missing Pincode")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_004_MissingPincode()
	{
		Embeddedshopinsurance reqobj = new Embeddedshopinsurance(P.TESTDATA.get("EmbeddedshopinsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("pincode", "");

		body.put("category", "Food");
		body.put("subCategory", "Foodcourt");
		body.put("source", "EDC");
		body.put("planType", "SHOP_INSURANCE");
		body.put("merchantId", "null");


		Response respObj = MiddlewareServicesObject.EmbeddedshopinsuranceMethod(reqobj, body,headers);
		Assert.assertEquals(respObj.statusCode(), 412);
		LOGGER.info("Error Message :" + respObj.jsonPath().getString("error_message"));

	}


	@Test(priority = 0,description = "Changing Source Device to soundbox")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_005_ChangingSource1()
	{
		Embeddedshopinsurance reqobj = new Embeddedshopinsurance(P.TESTDATA.get("EmbeddedshopinsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("pincode", "201002");

		body.put("category", "Food");
		body.put("subCategory", "Foodcourt");
		body.put("source", "SOUNDBOX");
		body.put("planType", "SHOP_INSURANCE");
		body.put("merchantId", "null");


		Response respObj = MiddlewareServicesObject.EmbeddedshopinsuranceMethod(reqobj, body,headers);
		Assert.assertEquals(respObj.statusCode(), 412);
		LOGGER.info("Error Message :" + respObj.jsonPath().getString("error_message"));

	}

	@Test(priority = 0,description = "Changing Source Device to card soundbox")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_006_ChangingSource2()
	{
		Embeddedshopinsurance reqobj = new Embeddedshopinsurance(P.TESTDATA.get("EmbeddedshopinsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("pincode", "201002");

		body.put("category", "Food");
		body.put("subCategory", "Foodcourt");
		body.put("source", "TAPNPAY");
		body.put("planType", "SHOP_INSURANCE");
		body.put("merchantId", "null");


		Response respObj = MiddlewareServicesObject.EmbeddedshopinsuranceMethod(reqobj, body,headers);
		Assert.assertEquals(respObj.statusCode(), 412);
		LOGGER.info("Error Message :" + respObj.jsonPath().getString("error_message"));

	}

	@Test(priority = 0,description = "Invalid subcategory of the merchant")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_007_invalidSubcategory()
	{
		Embeddedshopinsurance reqobj = new Embeddedshopinsurance(P.TESTDATA.get("EmbeddedshopinsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("pincode", "201002");

		body.put("category", "bfsi");
		body.put("subCategory", "lic");
		body.put("source", "EDC");
		body.put("planType", "SHOP_INSURANCE");
		body.put("merchantId", "null");


		Response respObj = MiddlewareServicesObject.EmbeddedshopinsuranceMethod(reqobj, body,headers);
		Assert.assertEquals(respObj.statusCode(), 200);
		LOGGER.info("Status of Shop Insurance Eligibility :" + respObj.jsonPath().getString("status"));

	}

	@Test(priority = 0,description = "Invalid subcategory of the merchant")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_008_invalidcategory()
	{
		Embeddedshopinsurance reqobj = new Embeddedshopinsurance(P.TESTDATA.get("EmbeddedshopinsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("pincode", "201002");

		body.put("category", "onpaytm");
		body.put("subCategory", "fastag");
		body.put("source", "EDC");
		body.put("planType", "SHOP_INSURANCE");
		body.put("merchantId", "null");


		Response respObj = MiddlewareServicesObject.EmbeddedshopinsuranceMethod(reqobj, body,headers);
		Assert.assertEquals(respObj.statusCode(), 200);
		LOGGER.info("Status of Shop Insurance Eligibility :" + respObj.jsonPath().getString("status"));

	}

	@Test(priority = 0,description = "Invalid subcategory without subcategory of the merchant")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_009_invalidcategory2()
	{
		Embeddedshopinsurance reqobj = new Embeddedshopinsurance(P.TESTDATA.get("EmbeddedshopinsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("pincode", "201002");

		body.put("category", "onpaytm");
		body.put("subCategory", "");
		body.put("source", "EDC");
		body.put("planType", "SHOP_INSURANCE");
		body.put("merchantId", "null");


		Response respObj = MiddlewareServicesObject.EmbeddedshopinsuranceMethod(reqobj, body,headers);
		Assert.assertEquals(respObj.statusCode(), 412);
		LOGGER.info("Error Message :" + respObj.jsonPath().getString("error_message"));

	}

	@Test(priority = 0,description = "change plan type")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_010_plantype()
	{
		Embeddedshopinsurance reqobj = new Embeddedshopinsurance(P.TESTDATA.get("EmbeddedshopinsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("pincode", "201002");

		body.put("category", "Food");
		body.put("subCategory", "Foodcourt");
		body.put("source", "EDC");
		body.put("planType", "BRAND_EMI");
		body.put("merchantId", "null");


		Response respObj = MiddlewareServicesObject.EmbeddedshopinsuranceMethod(reqobj, body,headers);
		Assert.assertEquals(respObj.statusCode(), 200);
		LOGGER.info("Status of Shop Insurance Eligibility :" + respObj.jsonPath().getString("status"));

	}


	@Test(priority = 0,description = "Providing merchant ID")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_011_merchantID()
	{
		Embeddedshopinsurance reqobj = new Embeddedshopinsurance(P.TESTDATA.get("EmbeddedshopinsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("pincode", "201002");

		body.put("category", "Food");
		body.put("subCategory", "Foodcourt");
		body.put("source", "EDC");
		body.put("planType", "SHOP_INSURANCE");
		body.put("merchantId", "QbKKuf71845080373634");


		Response respObj = MiddlewareServicesObject.EmbeddedshopinsuranceMethod(reqobj, body,headers);
		Assert.assertEquals(respObj.statusCode(), 200);
		LOGGER.info("Status of Shop Insurance Eligibility :" + respObj.jsonPath().getString("status"));

	}

	@Test(priority = 0,description = "providing all blacklisted category and pincode to Insurance")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_012_allblacklistedrequest()
	{
		Embeddedshopinsurance reqobj = new Embeddedshopinsurance(P.TESTDATA.get("EmbeddedshopinsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("pincode", "124507");

		body.put("category", "education");
		body.put("subCategory", "school");
		body.put("source", "EDC");
		body.put("planType", "SHOP_INSURANCE");
		body.put("merchantId", "QbKKuf71845080373634");


		Response respObj = MiddlewareServicesObject.EmbeddedshopinsuranceMethod(reqobj, body,headers);
		Assert.assertEquals(respObj.statusCode(), 200);
		LOGGER.info("Status of Shop Insurance Eligibility :" + respObj.jsonPath().getString("status"));

	}


	@Test(priority = 0,description = "Pincode blaclisted due to Flood")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_013_pincodeBlaclisted1()
	{
		Embeddedshopinsurance reqobj = new Embeddedshopinsurance(P.TESTDATA.get("EmbeddedshopinsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("pincode", "144514");

		body.put("category", "Food");
		body.put("subCategory", "Foodcourt");
		body.put("source", "EDC");
		body.put("planType", "SHOP_INSURANCE");
		body.put("merchantId", "null");


		Response respObj = MiddlewareServicesObject.EmbeddedshopinsuranceMethod(reqobj, body,headers);
		Assert.assertEquals(respObj.statusCode(), 200);
		LOGGER.info("Status of Shop Insurance Eligibility :" + respObj.jsonPath().getString("status"));

	}


	@Test(priority = 0,description = "Pincode blaclisted due to Landslide")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_014_pincodeBlaclisted2()
	{
		Embeddedshopinsurance reqobj = new Embeddedshopinsurance(P.TESTDATA.get("EmbeddedshopinsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("pincode", "177034");

		body.put("category", "Food");
		body.put("subCategory", "Foodcourt");
		body.put("source", "EDC");
		body.put("planType", "SHOP_INSURANCE");
		body.put("merchantId", "null");


		Response respObj = MiddlewareServicesObject.EmbeddedshopinsuranceMethod(reqobj, body,headers);
		Assert.assertEquals(respObj.statusCode(), 200);
		LOGGER.info("Status of Shop Insurance Eligibility :" + respObj.jsonPath().getString("status"));

	}

	@Test(priority = 0,description = "sending empty request to insurance ")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_015_sendingemptyrequest()
	{
		Embeddedshopinsurance reqobj = new Embeddedshopinsurance(P.TESTDATA.get("EmbeddedshopinsuranceRequest"));

		Map<String, String> headers = new HashMap<String, String>();

		headers.put("access_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvbmJvYXJkaW5nLWVuZ2luZS1zdGFnaW5nIn0.ScqsDViE42vpdDGeapZTHQHaM1cLxntmlD-vlyB9vs8");
		headers.put("Content-Type", "application/json");



		Map<String, String> body = new HashMap<String, String>();
		body.put("pincode", "");

		body.put("category", "");
		body.put("subCategory", "");
		body.put("source", "");
		body.put("planType", "");
		body.put("merchantId", "");


		Response respObj = MiddlewareServicesObject.EmbeddedshopinsuranceMethod(reqobj, body,headers);
		Assert.assertEquals(respObj.statusCode(), 412);
		LOGGER.info("Error Message :" + respObj.jsonPath().getString("error_message"));

	}



}
