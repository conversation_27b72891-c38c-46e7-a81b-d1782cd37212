package OCL.ManageAgentOEPanel;

import Request.MerchantService.oe.panel.v1.agent;
import Request.MerchantService.oe.panel.v1.userInfo;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import net.minidev.json.JSONArray;
import org.json.simple.JSONArray;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.AfterMethod;
import java.util.concurrent.TimeUnit;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ManageAgent extends BaseMethod {

    private static final Logger logger = LoggerFactory.getLogger(ManageAgent.class);

    private final MiddlewareServices middlewareServicesObject;
    private static String X_MW_Token;
    private agent agent;
    private String phoneNumber;
    private Map<String, String> headers;
    private static final String BASE_URL = "https://oe-staging6.paytm.com";

    public ManageAgent() {
        this.middlewareServicesObject = new MiddlewareServices();
        this.headers = new HashMap<>();
    }

    public static String repeatNew(String str, int count) {
        if (str == null) {
            throw new IllegalArgumentException("String cannot be null");
        }
        if (count < 0) {
            throw new IllegalArgumentException("Count cannot be negative");
        }

        StringBuilder sb = new StringBuilder(str.length() * count);
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    @BeforeMethod
    public void setup() {
        try {
            headers.clear();
            headers = new HashMap<>();
            headers.put("Accept", "application/json");
            headers.put("Referer", BASE_URL + "/updateAgent");
            headers.put("phonenumber", "7771216290");
            headers.put("ssoid", "1106992015");
            headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
            headers.put("Origin", BASE_URL);
            logger.debug("Headers initialized successfully");
        } catch (Exception e) {
            logger.error("Error in setup: {}", e.getMessage());
            throw new RuntimeException("Failed to setup test", e);
        }
    }

    @BeforeMethod
    public void initializeAgent() {
        try {
            agent = new agent();
            logger.debug("Agent initialized successfully");
        } catch (Exception e) {
            logger.error("Error initializing agent: {}", e.getMessage());
            throw new RuntimeException("Failed to initialize agent", e);
        }
    }

    private void validateResponse(Response response) {
        Objects.requireNonNull(response, "Response object cannot be null");
        Assert.assertNotNull(response.getStatusCode(), "Status code should not be null");
        
        logger.debug("Response Status Code: {}", response.getStatusCode());
        logger.debug("Response Body: {}", response.getBody().asString());
    }

    private String createJSONArray(String... values) {
        if (values == null || values.length == 0) {
            throw new IllegalArgumentException("Values cannot be null or empty");
        }
        
        try {
            JSONArray array = new JSONArray();
            for (String value : values) {
                if (value != null) {
                    array.add(value);
                }
            }
            return array.toJSONString();
        } catch (Exception e) {
            logger.error("Error creating JSON array: {}", e.getMessage());
            throw new RuntimeException("Failed to create JSON array", e);
        }
    }

    @Test(description = "fetch XMW token for OE panel", priority = 0)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getOE_XMW() {
        X_MW_Token = XMWCookie;
        System.out.println(X_MW_Token);
    }


    @Test(testName = "Search an Agent with empty phone number", priority=1, dependsOnMethods = "getOE_XMW")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void searchAgentEmptyNumber() {
        try {
            phoneNumber = "";
            userInfo getAgent = new userInfo(phoneNumber);
            headers.put("Cookie", Objects.requireNonNull(X_MW_Token, "X_MW_Token cannot be null"));

            Response responseObject = middlewareServicesObject.searchAgentDetails(getAgent, headers);
            validateResponse(responseObject);
            Assert.assertEquals(responseObject.getStatusCode(), 200);
            
            String errorMsg = responseObject.jsonPath().getString("errorMsg");
            Assert.assertNotNull(errorMsg, "Error message should not be null");
            Assert.assertTrue(errorMsg.contains("Mobile no. is not registered with Paytm"));
            
            logger.info("Search agent with empty number test completed successfully");
        } catch (Exception e) {
            logger.error("Error in searchAgentEmptyNumber: {}", e.getMessage());
            throw e;
        }
    }

    @Test(testName = "Search an Agent with Invalid number", priority=2, dependsOnMethods = "getOE_XMW")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void searchAgentInvalidNumber()
    {
        phoneNumber= "987Agh";
        userInfo getAgent= new userInfo(phoneNumber);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Response responseObject= middlewareServicesObject.searchAgentDetails(getAgent,headers);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        System.out.println("Status Code is " + StatusCode);
        String message= responseObject.jsonPath().getString("message");
        Assert.assertEquals(true,message.contains("User is not registered with Paytm"));
        //String errorCode= responseObject.jsonPath().getString("errorCode");
        //Assert.assertEquals(errorCode,201);

    }

    @Test(testName = "Search an Agent with special characters", priority = 2, dependsOnMethods = "getOE_XMW")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void searchAgentSpecialChars() {



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
//GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","^&%^&%^%VVHGVBHH*&&*((2");
        body.put("emailId","<EMAIL>");
        body.put("agentName","test agent");
        body.put("status","1");
        body.put("pincode","110035");
//body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(testName = "Search an Agent with extremely long number", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void searchAgentLongNumber() {



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
//GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","58858826637238783246746326");
        body.put("emailId","<EMAIL>");
        body.put("agentName","test agent");
        body.put("status","1");
        body.put("pincode","110035");
//body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 500);
    }

    @Test(testName = "Search an Agent with no token present in header", priority=3)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void searchAgentNoTokenHeader()
    {
        phoneNumber= "";
        userInfo getAgent= new userInfo(phoneNumber);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        //headers.put("Cookie",X_MW_Token);

        Response responseObject= middlewareServicesObject.searchAgentDetails(getAgent,headers);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 401);
        System.out.println("Status Code is " + StatusCode);

    }

    @Test(testName = "Search an Agent with only token present in request", priority=4, dependsOnMethods = "getOE_XMW")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void searchAgentPositiveOnlyTokenHeader()
    {
        phoneNumber= "8802825904";
        userInfo getAgent= new userInfo(phoneNumber);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Cookie",X_MW_Token);

        Response responseObject= middlewareServicesObject.searchAgentDetails(getAgent,headers);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);
       // getAgent.validateResponseAgainstJSONSchema("ManageAgent/searchAgent/searchAgentSchema.json");

    }

    @Test(testName = "Search an Agent for positive flow", priority=4, dependsOnMethods = "getOE_XMW")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void searchAgentPositive()
    {
        phoneNumber= "8010630022";
        userInfo getAgent= new userInfo(phoneNumber);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Response responseObject= middlewareServicesObject.searchAgentDetails(getAgent,headers);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);
      //  getAgent.validateResponseAgainstJSONSchema("ManageAgent/searchAgent/searchAgentSchema.json");

    }

    @Test(testName = "Add an Agent with No Roles", priority=2, dependsOnMethods = "getOE_XMW")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AddAgentWithNoRoles()
    {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie",X_MW_Token);

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
        GrpArray.add("hotel_group");

        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","7771110020");
        body.put("emailId","<EMAIL>");
        body.put("agentName","Nancy bansal");
        body.put("status","1");
        body.put("pincode","110035");
        body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());


        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400 );
        System.out.println("Status Code is " + StatusCode);
        Assert.assertEquals((responseObject.jsonPath().getString("message")),"No role provided: ");

    }

    /*
    @Test(testName= "Add a new Agent", priority = 2, dependsOnMethods = "getOE_XMW")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AddAgent()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);


// Set json array for keys
        JSONArray array=new JSONArray();
        array.add("edcPOSGroup");
        array.add("hotel_group");

        Map<String, String> body = new HashMap<String, String>();
        body.put("groups",array.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers, body);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);
        agent.validateResponseAgainstJSONSchema("ManageAgent/addAgent/addAgentResSchema.json");

    }
*/
    @Test(testName = "Update an existing Agent with No Roles", priority=2, dependsOnMethods = "getOE_XMW")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UpdateAgentWithNoRoles()
    {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie",X_MW_Token);

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
        GrpArray.add("hotel_group");
/*
        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");
*/
        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","7771110020");
        body.put("emailId","<EMAIL>");
        body.put("agentName","Nancy bansal");
        body.put("status","1");
        body.put("pincode","110035");
        body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());


        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400 );
        System.out.println("Status Code is " + StatusCode);
        Assert.assertEquals((responseObject.jsonPath().getString("message")),"No role provided: ");
    }

    @Test(testName = "Update an Agent without phone number", priority=2, dependsOnMethods = "getOE_XMW")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UpdateAgentWithoutMobile()
    {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
        GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");

        Map<String, String> body = new HashMap<String, String>();
        body.put("emailId","<EMAIL>");
        body.put("agentName","Nancy bansal");
        body.put("status","1");
        body.put("pincode","110035");
        body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400 );
        System.out.println("Status Code is " + StatusCode);
        Assert.assertEquals((responseObject.jsonPath().getString("message")),"Please check either mobile no or email id is empty");

    }

    @Test(testName = "Update an Agent with incorrect id", priority=2, dependsOnMethods = "getOE_XMW")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UpdateAgentWithIncorrectId()
    {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
        //GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName","test agent");
        body.put("status","1");
        body.put("pincode","110035");
        body.put("id","774676423564673545");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 500);
        System.out.println("Status Code is " + StatusCode);
    }

    @Test(testName = "Update an Agent", priority=2, dependsOnMethods = "getOE_XMW")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UpdateAgentWithPhone()
    {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
        GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>\n");
        body.put("agentName","Puneet Kumar");
        body.put("status","1");
        body.put("pincode","110035");
        body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }


    @Test(testName = "Update an Agent", priority=2, dependsOnMethods = "getOE_XMW")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UpdateAgent()
    {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
        //GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName","test agent");
        body.put("status","1");
        body.put("pincode","110035");
        //body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);
        //agent.validateResponseAgainstJSONSchema("ManageAgent/updateAgent/updateAgentResSchema.json");
        String emailId= responseObject.jsonPath().getString("user.emailId");
        Assert.assertEquals(emailId,"<EMAIL>");
        String agentName= responseObject.jsonPath().getString("user.agentName");
        Assert.assertEquals(agentName,"test agent");
        String status= responseObject.jsonPath().getString("user.status");
        Assert.assertEquals(status,"1");
        String pincode= responseObject.jsonPath().getString("user.address.pincode");
        Assert.assertEquals(pincode,"110035");
        String recordType= responseObject.jsonPath().getString("user.userAdditionalInfo.recordType");
        Assert.assertEquals(recordType,"Ambassador");
        String agentSubtype= responseObject.jsonPath().getString("user.userAdditionalInfo.agentSubtype");
        Assert.assertEquals(agentSubtype,"DIY P2P Merchant");
        String dateOfJoining= responseObject.jsonPath().getString("user.userAdditionalInfo.dateOfJoining");
        Assert.assertEquals(dateOfJoining,"2019-10-14 00:00:00");
     //   String groups= responseObject.jsonPath().getString("user.groups");
      //  Assert.assertEquals(groups,GrpArray.toJSONString());
     //   String roleList= responseObject.jsonPath().getString("user.roleList");
      //  Assert.assertEquals(roleList,rolesArray.toJSONString().contains(roleList));
    }

    @Test(testName = "Add Agent with invalid email format", priority = 2, dependsOnMethods = "getOE_XMW")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addAgentInvalidEmail() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
        //GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","sourabh1.singh@paytm");
        body.put("agentName","test agent");
        body.put("status","1");
        body.put("pincode","110035");
        //body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        System.out.println("Status Code is " + StatusCode);
    }

    @Test(testName = "Add Agent with future date of joining", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addAgentFutureDate() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
        //GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName","test agent");
        body.put("status","1");
        body.put("pincode","110035");
        //body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2029-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        System.out.println("Status Code is " + StatusCode);
    }

    @Test(testName = "Update Agent with invalid status code", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateAgentInvalidStatus() {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
        //GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName","test agent");
        body.put("status","112342424");
        body.put("pincode","110035");
        //body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);
    }

    @Test(testName = "Update Agent with invalid pincode", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateAgentInvalidPincode() {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
        //GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName","test agent");
        body.put("status","1");
        body.put("pincode","110ww035");
        //body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        System.out.println("Status Code is " + StatusCode);
    }

    @Test(testName = "Update Agent with duplicate email", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateAgentDuplicateEmail() {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
        //GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName","test agent");
        body.put("status","1");
        body.put("pincode","110035");
        //body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);
    }

    @Test(testName = "Search an Agent with SQL injection attempt", priority = 2, dependsOnMethods = "getOE_XMW")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void searchAgentSQLInjection() {
        phoneNumber = "1234' OR '1'='1";
        userInfo getAgent = new userInfo(phoneNumber);
        Response responseObject = middlewareServicesObject.searchAgentDetails(getAgent, headers);
        Assert.assertEquals(responseObject.getStatusCode(), 401);
    }

    @Test(testName = "Search an Agent with international format", priority = 2, dependsOnMethods = "getOE_XMW")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void searchAgentInternationalFormat() {
        phoneNumber = "+91-7771110020";
        userInfo getAgent = new userInfo(phoneNumber);
        Response responseObject = middlewareServicesObject.searchAgentDetails(getAgent, headers);
        Assert.assertEquals(responseObject.getStatusCode(), 401);
    }

    @Test(testName = "Add Agent with XSS attempt in name", priority = 2, dependsOnMethods = "getOE_XMW")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addAgentXSSAttempt() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
//GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName", "<script>alert('xss')</script>");
        body.put("status","1");
        body.put("pincode","110035");
//body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(testName = "Update Agent with malformed JSON in groups", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateAgentMalformedGroups() {



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("767676vvh");
//GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName","test agent");
        body.put("status","1");
        body.put("pincode","110035");
//body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(testName = "Add Agent with extremely long name", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addAgentLongName() {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
//GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");

        //create a string of 256 characters and add in agentName
        

        // String longAgentName = "a".repeat(256);
        String longAgentName = repeatNew("a", 256);
        body.put("agentName", longAgentName); // Assuming 255 is max length
        body.put("status","1");
        body.put("pincode","110035");
//body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 500);
    }

    @Test(testName = "Update Agent with invalid date format", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateAgentInvalidDateFormat() {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
//GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName","test agent");
        body.put("status","1");
        body.put("pincode","110035");
//body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining", "2023/12/31"); // Wrong
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);// format

    }

    @Test(testName = "Add Agent with invalid role combination", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addAgentInvalidRoles() {



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
//GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("yuyu");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName","test agent");
        body.put("status","1");
        body.put("pincode","110035");
//body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }



    @Test(testName = "Add Agent with invalid group names", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addAgentInvalidGroups() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("wrong_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName","test agent");
        body.put("status","1");
        body.put("pincode","110035");
//body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(testName = "Update Agent with unicode characters", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateAgentUnicodeChars() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
//GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName","测试用户");
        body.put("status","1");
        body.put("pincode","110035");
//body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 500);


    }

    // Additional Security Test Cases
    @Test(testName = "Search Agent with LDAP injection attempt", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void searchAgentLDAPInjection() {
        String phoneNumber = "*)(uid=*))(|(uid=*";
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", phoneNumber);
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
//GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName","test agent");
        body.put("status","1");
        body.put("pincode","110035");
//body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }

    @Test(testName = "Add Agent with HTML injection in email", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addAgentHTMLInjection() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
        //GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId", "<EMAIL><img src=x onerror=alert(1)>");
        body.put("agentName","test agent");
        body.put("status","1");
        body.put("pincode","110035");
        //body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        System.out.println("Status Code is " + StatusCode);




    }

    // Data Type and Format Validation
    @Test(testName = "Update Agent with numeric values as strings", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateAgentNumericAsString() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
//GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("emailId","<EMAIL>");
        body.put("agentName","test agent");
        body.put("phoneNumber", "\"5885882626\""); // Number as quoted string
        body.put("status", "\"1\"");

        body.put("pincode","110035");
//body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);




    }

    @Test(testName = "Add Agent with invalid JSON array format", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addAgentInvalidJSONArray() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName","test agent");
        body.put("status","1");
        body.put("pincode","110035");
//body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList", "[OE_ADMIN, OE_SUDO]"); // Invalid JSON array format

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }



    @Test(testName = "Add Agent with whitespace-only values", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addAgentWhitespaceValues() {



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
//GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName","   ");
        body.put("status","1");
        body.put("pincode","110035");
//body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(testName = "Add Agent with invalid date values", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addAgentInvalidDate() {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
        //GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName","test agent");
        body.put("status","1");
        body.put("pincode","110035");
        //body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining", "2023-13-32 25:61:61.0"); // Invalid month, day, hours, minutes, seconds

        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }


    @Test(testName = "Add Agent with null characters in strings", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void addAgentNullChars() {



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Accept", "application/json");
        headers.put("Referer", "https://oe-staging6.paytm.com/updateAgent");
        headers.put("phonenumber", "7771216290");
        headers.put("ssoid", "1106992015");
        headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36");
        headers.put("Origin", "https://oe-staging6.paytm.com");
        headers.put("Cookie",X_MW_Token);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("mode", "EMAIL");

        JSONArray GrpArray=new JSONArray();
        GrpArray.add("edcPOSGroup");
//GrpArray.add("hotel_group");

        JSONArray rolesArray=new JSONArray();
        rolesArray.add("OE_SUDO");
        rolesArray.add("OE_ADMIN");
        rolesArray.add("OE_MS");


        Map<String, String> body = new HashMap<String, String>();
        body.put("phoneNumber","5885882626");
        body.put("emailId","<EMAIL>");
        body.put("agentName","test\0agent");
        body.put("status","1");
        body.put("pincode","110035");
//body.put("inactiveReason","others");
        body.put("recordType","Ambassador");
        body.put("agentSubtype","DIY P2P Merchant");
        body.put("dateOfJoining","2019-10-14 00:00:00.0");
        body.put("groups",GrpArray.toJSONString());
        body.put("roleList",rolesArray.toJSONString());

        agent = new agent();
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers,body,queryParams);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    // Header Manipulation
    @Test(testName = "Search Agent with modified content-type", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void searchAgentInvalidContentType() {
        Map<String, String> modifiedHeaders = new HashMap<>(headers);
        modifiedHeaders.put("Content-Type", "text/plain");
      //  Response responseObject = middlewareServicesObject.searchAgentDetails(getAgent, modifiedHeaders);
      //  Assert.assertEquals(responseObject.getStatusCode(), 415); // Unsupported Media Type
    }

    @Test(testName = "Update Agent with missing required headers", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateAgentMissingHeaders() {
        Map<String, String> minimalHeaders = new HashMap<>();
        minimalHeaders.put("Cookie", X_MW_Token);
        Response responseObject = middlewareServicesObject.addAndUpdateAgentDetails(agent, minimalHeaders, new HashMap<>());
        Assert.assertEquals(responseObject.getStatusCode(), 400);
    }

    // Concurrent Access
    @Test(testName = "Update Agent with concurrent modifications", priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateAgentConcurrent() {
        Map<String, String> body = new HashMap<>();
        try {
            body.put("phoneNumber", "7771110020");
            body.put("agentName", "Test User");
            
            Response response1 = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers, body);
            validateResponse(response1);
            Assert.assertEquals(response1.getStatusCode(), 200);
            
            TimeUnit.MILLISECONDS.sleep(100);
            
            Response response2 = middlewareServicesObject.addAndUpdateAgentDetails(agent, headers, body);
            validateResponse(response2);
            Assert.assertTrue(response2.getStatusCode() == 409 || response2.getStatusCode() == 400);
            
            logger.info("Concurrent update test completed successfully");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("Concurrent test interrupted: {}", e.getMessage());
            throw new RuntimeException("Test interrupted", e);
        } catch (Exception e) {
            logger.error("Error in concurrent test: {}", e.getMessage());
            throw e;
        } finally {
            body.clear();
        }
    }


    // Rate Limiting

    @AfterMethod
    public void cleanup() {
        try {
            if (headers != null) {
                headers.clear();
            }
            if (agent != null) {
                // Add any necessary agent cleanup
            }
            logger.debug("Cleanup completed successfully");
        } catch (Exception e) {
            logger.error("Error in cleanup: {}", e.getMessage());
        }
    }

    private void validateAgentResponse(Response response) {
        validateResponse(response);
        if (response.getStatusCode() == 200) {
            Assert.assertNotNull(response.jsonPath().getString("user"), "User object should not be null");
            Assert.assertNotNull(response.jsonPath().getString("user.emailId"), "Email ID should not be null");
            Assert.assertNotNull(response.jsonPath().getString("user.agentName"), "Agent name should not be null");
        }
    }
}
