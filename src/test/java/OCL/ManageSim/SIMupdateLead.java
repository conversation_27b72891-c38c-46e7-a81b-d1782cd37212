package OCL.ManageSim;

import Request.managesim.FetchDevicesimdetails;
import Request.managesim.SimupdateLead;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class SIMupdateLead extends BaseMethod
{
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	private static final Logger LOGGER = LogManager.getLogger(SIMupdateLead.class);
	public static String clientId = "OE";
	public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";


	String sessiontoken = AgentSessionToken("9891497839", "paytm@123");
	String agentvar = "8010630022";//9891497839

	// sessiontoken = "b4b4506b-7903-47de-88df-9d6c8c955600";


	@Test(priority = 0,description = "Checking status code")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_001_simdeactivation()
	{
		SimupdateLead reqobj = new SimupdateLead(P.TESTDATA.get("SimupdateLeadRequest"));

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("X-SRC", "GGClient");
		headers.put("isBusyBoxFound", "false");
		headers.put("deviceName", "IV2201");
		headers.put("version", "5.2.3");
		headers.put("session_token", sessiontoken);
		headers.put("isDeviceRooted", "false");
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("osVersion", "13");
		headers.put("Content-Type", "application/json; charset=UTF-8");
		headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


		Map<String, String> body = new HashMap<String, String>();
		body.put("leadId", "56953700-de6f-41a3-b152-83b6370c4d45");
		body.put("deviceId", "M9CXN4F4C481");
		body.put("deviceCategory", "EDC");
		body.put("deviceType", "PAX");
		body.put("modelName", "A50");
		body.put("action", "DEACTIVATION");
		body.put("imsiNo", "404100567289578");
		body.put("simReused", "false");
		body.put("simDeActivationFailureReason", "No sim data found at TMS for this device id");
		body.put("simString", "8991102205672895780U");



		Response respObj = MiddlewareServicesObject.SimupdateLeadMethod(reqobj, headers, body);

		Assert.assertEquals(respObj.statusCode(), 200);


	}


	@Test(priority = 0,description = "invalid seession token")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_002_invalidsession()
	{
		SimupdateLead reqobj = new SimupdateLead(P.TESTDATA.get("SimupdateLeadRequest"));

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("X-SRC", "GGClient");
		headers.put("isBusyBoxFound", "false");
		headers.put("deviceName", "IV2201");
		headers.put("version", "5.2.3");
		headers.put("session_token", "sessiontoken");
		headers.put("isDeviceRooted", "false");
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("osVersion", "13");
		headers.put("Content-Type", "application/json; charset=UTF-8");
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


		Map<String, String> body = new HashMap<String, String>();
		body.put("leadId", "8a39f638-6615-4644-8c22-be8d29932a58");
		body.put("deviceId", "K6544KL83367");
		body.put("deviceCategory", "EDC");
		body.put("deviceType", "PAX");
		body.put("modelName", "A910");
		body.put("action", "DEACTIVATION");
		body.put("imsiNo", "");
		body.put("simReused", "false");
		body.put("simDeActivationFailureReason", "No sim data found at TMS for this device id");
		body.put("simString", "8991102205672895780U");


		Response respObj = MiddlewareServicesObject.SimupdateLeadMethod(reqobj, headers, body);
		Assert.assertEquals(respObj.statusCode(), 410);

	}


	@Test(priority = 0,description = "without device indentifier")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_003_withoutdeviceIdentifier()
	{
		SimupdateLead reqobj = new SimupdateLead(P.TESTDATA.get("SimupdateLeadRequest"));

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("X-SRC", "GGClient");
		headers.put("isBusyBoxFound", "false");
		headers.put("deviceName", "IV2201");
		headers.put("version", "5.2.3");
		headers.put("session_token", sessiontoken);
		headers.put("isDeviceRooted", "false");
		//headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("osVersion", "13");
		headers.put("Content-Type", "application/json; charset=UTF-8");
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


		Map<String, String> body = new HashMap<String, String>();
		body.put("leadId", "8fbcb295-ac4d-4796-a077-f2ce840f4143");
		body.put("deviceId", "M9CXN4F4C481");
		body.put("deviceCategory", "EDC");
		body.put("deviceType", "PAX");
		body.put("modelName", "A50");
		body.put("action", "DEACTIVATION");
		body.put("imsiNo", "404100567289578");
		body.put("simReused", "false");
		body.put("simDeActivationFailureReason", "No sim data found at TMS for this device id");
		body.put("simString", "8991102205672895780U");


		Response respObj = MiddlewareServicesObject.SimupdateLeadMethod(reqobj, headers, body);

		Assert.assertEquals(respObj.statusCode(), 410);


	}

	@Test(priority = 0,description = "SIM activation when old sim data is not present")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_004_SIM_ACTIVATION()
	{
		SimupdateLead reqobj = new SimupdateLead(P.TESTDATA.get("SimupdateLeadRequest"));

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("X-SRC", "GGClient");
		headers.put("isBusyBoxFound", "false");
		headers.put("deviceName", "IV2201");
		headers.put("version", "5.2.3");
		headers.put("session_token", sessiontoken);
		headers.put("isDeviceRooted", "false");
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("osVersion", "13");
		headers.put("Content-Type", "application/json; charset=UTF-8");
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


		Map<String, String> body = new HashMap<String, String>();
		body.put("leadId", "56953700-de6f-41a3-b152-83b6370c4d45");
		body.put("deviceId", "M9CXN4F4C481");
		body.put("deviceCategory", "EDC");
		body.put("deviceType", "PAX");
		body.put("modelName", "A50");
		body.put("action", "ACTIVATION");
		body.put("imsiNo", "404100567289578");
		body.put("simReused", "false");
		body.put("simDeActivationFailureReason", "No sim data found at TMS for this device id");
		body.put("simString", "8991102205672895780U");



		Response respObj = MiddlewareServicesObject.SimupdateLeadMethod(reqobj, headers, body);

		Assert.assertEquals(respObj.statusCode(), 500);


	}

	@Test(priority = 0,description = "SIM activation before deactivation")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_005_SIM_ACTIVATION_before_deactivation()
	{
		SimupdateLead reqobj = new SimupdateLead(P.TESTDATA.get("SimupdateLeadRequest"));

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("X-SRC", "GGClient");
		headers.put("isBusyBoxFound", "false");
		headers.put("deviceName", "IV2201");
		headers.put("version", "5.2.3");
		headers.put("session_token", sessiontoken);
		headers.put("isDeviceRooted", "false");
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("osVersion", "13");
		headers.put("Content-Type", "application/json; charset=UTF-8");
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


		Map<String, String> body = new HashMap<String, String>();
		body.put("leadId", "8fbcb295-ac4d-4796-a077-f2ce840f4143");
		body.put("deviceId", "M9CXN4F4C481");
		body.put("deviceCategory", "EDC");
		body.put("deviceType", "PAX");
		body.put("modelName", "A50");
		body.put("action", "ACTIVATION");
		body.put("imsiNo", "404100567289578");
		body.put("simReused", "TRUE");
		body.put("simDeActivationFailureReason", "");
		body.put("simString", "8991102205672895780U");

		Response respObj = MiddlewareServicesObject.SimupdateLeadMethod(reqobj, headers, body);

		Assert.assertEquals(respObj.statusCode(), 400);
		LOGGER.info("Message : " +  respObj.jsonPath().getString("displayMessage"));

	}

	@Test(priority = 0,description = "sim data in missing from request body")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_006_missing_details()
	{
		SimupdateLead reqobj = new SimupdateLead(P.TESTDATA.get("SimupdateLeadRequest"));

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("X-SRC", "GGClient");
		headers.put("isBusyBoxFound", "false");
		headers.put("deviceName", "IV2201");
		headers.put("version", "5.2.3");
		headers.put("session_token", sessiontoken);
		headers.put("isDeviceRooted", "false");
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("osVersion", "13");
		headers.put("Content-Type", "application/json; charset=UTF-8");
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


		Map<String, String> body = new HashMap<String, String>();
		body.put("leadId", "8fbcb295-ac4d-4796-a077-f2ce840f4143");
		body.put("deviceId", "M9CXN4F4C481");
		body.put("deviceCategory", "EDC");
		body.put("deviceType", "PAX");
		body.put("modelName", "A50");
		body.put("action", "ACTIVATION");
		body.put("imsiNo", "");
		body.put("simReused", "FALSE");
		body.put("simDeActivationFailureReason", "");
		body.put("simString", "");

		Response respObj = MiddlewareServicesObject.SimupdateLeadMethod(reqobj, headers, body);

		Assert.assertEquals(respObj.statusCode(), 400);
		LOGGER.info("Message : " +  respObj.jsonPath().getString("displayMessage"));

	}

	@Test(priority = 0,description = "EDC data in missing from request body")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_007_missing_machine_details()
	{
		SimupdateLead reqobj = new SimupdateLead(P.TESTDATA.get("SimupdateLeadRequest"));

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("X-SRC", "GGClient");
		headers.put("isBusyBoxFound", "false");
		headers.put("deviceName", "IV2201");
		headers.put("version", "5.2.3");
		headers.put("session_token", sessiontoken);
		headers.put("isDeviceRooted", "false");
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("osVersion", "13");
		headers.put("Content-Type", "application/json; charset=UTF-8");
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


		Map<String, String> body = new HashMap<String, String>();
		body.put("leadId", "8fbcb295-ac4d-4796-a077-f2ce840f4143");
//		body.put("deviceId", "M9CXN4F4C481");
//		body.put("deviceCategory", "EDC");
//		body.put("deviceType", "PAX");
		body.put("modelName", "A50");
		body.put("action", "DEACTIVATION");
		body.put("imsiNo", "404100567289578");
		body.put("simReused", "false");
		body.put("simDeActivationFailureReason", "No sim data found at TMS for this device id");
		body.put("simString", "8991102205672895780U");

		Response respObj = MiddlewareServicesObject.SimupdateLeadMethod(reqobj, headers, body);

		Assert.assertEquals(respObj.statusCode(), 400);
		LOGGER.info("Message : " +  respObj.jsonPath().getString("displayMessage"));

	}

	@Test(priority = 0,description = "wrong Lead")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_008_wrong_leadID()
	{
		SimupdateLead reqobj = new SimupdateLead(P.TESTDATA.get("SimupdateLeadRequest"));

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("X-SRC", "GGClient");
		headers.put("isBusyBoxFound", "false");
		headers.put("deviceName", "IV2201");
		headers.put("version", "5.2.3");
		headers.put("session_token", sessiontoken);
		headers.put("isDeviceRooted", "false");
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("osVersion", "13");
		headers.put("Content-Type", "application/json; charset=UTF-8");
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


		Map<String, String> body = new HashMap<String, String>();
		body.put("leadId", "8fbc-4796-a077-f2ce840f4143b295-ac4d");
		body.put("deviceId", "M9CXN4F4C481");
		body.put("deviceCategory", "EDC");
		body.put("deviceType", "PAX");
		body.put("modelName", "A50");
		body.put("action", "DEACTIVATION");
		body.put("imsiNo", "404100567289578");
		body.put("simReused", "false");
		body.put("simDeActivationFailureReason", "No sim data found at TMS for this device id");
		body.put("simString", "8991102205672895780U");

		Response respObj = MiddlewareServicesObject.SimupdateLeadMethod(reqobj, headers, body);

		Assert.assertEquals(respObj.statusCode(), 400);
		LOGGER.info("Message : " +  respObj.jsonPath().getString("displayMessage"));

	}

	@Test(priority = 0,description = "deactivation without data on TMS")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_009_deactivation()
	{
		SimupdateLead reqobj = new SimupdateLead(P.TESTDATA.get("SimupdateLeadRequest"));

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("X-SRC", "GGClient");
		headers.put("isBusyBoxFound", "false");
		headers.put("deviceName", "IV2201");
		headers.put("version", "5.2.3");
		headers.put("session_token", sessiontoken);
		headers.put("isDeviceRooted", "false");
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("osVersion", "13");
		headers.put("Content-Type", "application/json; charset=UTF-8");
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


		Map<String, String> body = new HashMap<String, String>();
		body.put("leadId", "8fbcb295-ac4d-4796-a077-f2ce840f4143");
		body.put("deviceId", "M9CXN4F4C481");
		body.put("deviceCategory", "EDC");
		body.put("deviceType", "PAX");
		body.put("modelName", "A50");
		body.put("action", "DEACTIVATION");
		body.put("imsiNo", "404100567289578");
		body.put("simReused", "false");
		body.put("simDeActivationFailureReason", "");
		body.put("simString", "8991102205672895780U");

		Response respObj = MiddlewareServicesObject.SimupdateLeadMethod(reqobj, headers, body);

		Assert.assertEquals(respObj.statusCode(), 400);
		LOGGER.info("Message : " +  respObj.jsonPath().getString("displayMessage"));

	}

	@Test(priority = 0,description = "deactivation without data on TMS")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_010_deactivation()
	{
		SimupdateLead reqobj = new SimupdateLead(P.TESTDATA.get("SimupdateLeadRequest"));

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("X-SRC", "GGClient");
		headers.put("isBusyBoxFound", "false");
		headers.put("deviceName", "IV2201");
		headers.put("version", "5.2.3");
		headers.put("session_token", sessiontoken);
		headers.put("isDeviceRooted", "false");
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("osVersion", "13");
		headers.put("Content-Type", "application/json; charset=UTF-8");
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


		Map<String, String> body = new HashMap<String, String>();
		body.put("leadId", "8fbcb295-ac4d-4796-a077-f2ce840f4143");
		body.put("deviceId", "M9CXN4F4C481");
		body.put("deviceCategory", "EDC");
		body.put("deviceType", "PAX");
		body.put("modelName", "A50");
		body.put("action", "");
		body.put("imsiNo", "404100567289578");
		body.put("simReused", "false");
		body.put("simDeActivationFailureReason", "");
		body.put("simString", "8991102205672895780U");

		//Response respObj = MiddlewareServicesObject.SimupdateLeadMethod(reqobj, headers, body);

		Response respObj = null;
		try {
			respObj = MiddlewareServicesObject.SimupdateLeadMethod(reqobj, headers, body);

		} catch (PatternSyntaxException e) {

		}
		if (respObj != null) {
			Assert.assertEquals(respObj.statusCode(), 400);
		}

	}
	@Test(priority = 0,description = "sim re-useded condiditong is missing")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_011_sim_reused_not_provided()
	{
		SimupdateLead reqobj = new SimupdateLead(P.TESTDATA.get("SimupdateLeadRequest"));

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("X-SRC", "GGClient");
		headers.put("isBusyBoxFound", "false");
		headers.put("deviceName", "IV2201");
		headers.put("version", "5.2.3");
		headers.put("session_token", sessiontoken);
		headers.put("isDeviceRooted", "false");
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("osVersion", "13");
		headers.put("Content-Type", "application/json; charset=UTF-8");
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


		Map<String, String> body = new HashMap<String, String>();
		body.put("leadId", "8fbcb295-ac4d-4796-a077-f2ce840f4143");
		body.put("deviceId", "M9CXN4F4C481");
		body.put("deviceCategory", "EDC");
		body.put("deviceType", "PAX");
		body.put("modelName", "A50");
		body.put("action", "DEACTIVATION");
		body.put("imsiNo", "404100567289578");
		body.put("simReused", "");
		body.put("simDeActivationFailureReason", "No sim data found at TMS for this device id");
		body.put("simString", "8991102205672895780U");

		Response respObj = MiddlewareServicesObject.SimupdateLeadMethod(reqobj, headers, body);

		Assert.assertEquals(respObj.statusCode(), 400);

	}

	@Test(priority = 0,description = "another sim")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_012_another_sim()
	{
		SimupdateLead reqobj = new SimupdateLead(P.TESTDATA.get("SimupdateLeadRequest"));

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("X-SRC", "GGClient");
		headers.put("isBusyBoxFound", "false");
		headers.put("deviceName", "IV2201");
		headers.put("version", "5.2.3");
		headers.put("session_token", sessiontoken);
		headers.put("isDeviceRooted", "false");
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("osVersion", "13");
		headers.put("Content-Type", "application/json; charset=UTF-8");
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


		Map<String, String> body = new HashMap<String, String>();
		body.put("leadId", "8fbcb295-ac4d-4796-a077-f2ce840f4143");
		body.put("deviceId", "M9CXN4F4C481");
		body.put("deviceCategory", "EDC");
		body.put("deviceType", "PAX");
		body.put("modelName", "A50");
		body.put("action", "DEACTIVATION");
		body.put("imsiNo", "404100567289578");
		body.put("simReused", "false");
		body.put("simDeActivationFailureReason", "No sim data found at TMS for this device id");
		body.put("simString", "8991102205676982816U");

		Response respObj = MiddlewareServicesObject.SimupdateLeadMethod(reqobj, headers, body);

		Assert.assertEquals(respObj.statusCode(), 400);

	}

}
