package OCL.ManageSim;

import Request.managesim.FetchDevicesimdetails;
import Request.managesim.SimupdateLead;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FetchDeviceSimDetails extends BaseMethod
{
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	private static final Logger LOGGER = LogManager.getLogger(FetchDeviceSimDetails.class);
	public static String clientId = "OE";
	public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";


	String sessiontoken = AgentSessionToken("9891497839", "paytm@123");
	String agentvar = "8010630022";//9891497839

	// sessiontoken = "b4b4506b-7903-47de-88df-9d6c8c955600";


	@Test(priority = 0,description = "Checking status code")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_001_StatusCode()
	{
		FetchDevicesimdetails reqobj = new FetchDevicesimdetails(P.TESTDATA.get("DeviceSimDetailsRequest"));

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("oldSerialNumber", "K6544KL83367");
		queryParams.put("leadId", "5615f6cc-b666-46d9-884a-2ac50680b76b");
		queryParams.put("oem", "PAX");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessiontoken);
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");

		Response respObj = MiddlewareServicesObject.FetchDevicesimdetailsMethod(reqobj, queryParams, headers);

		Assert.assertEquals(respObj.statusCode(), 200);
		LOGGER.info(" Message : " + respObj.jsonPath().getString("simDeActivationFailureReason"));

	}



	@Test(priority = 0,description = "without session token")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_002_withoutsessiontoken()
	{
		FetchDevicesimdetails reqobj = new FetchDevicesimdetails(P.TESTDATA.get("DeviceSimDetailsRequest"));

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("oldSerialNumber", "K6544KL83367");
		queryParams.put("leadId", "5615f6cc-b666-46d9-884a-2ac50680b76b");
		queryParams.put("oem", "PAX");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");

		Response respObj = MiddlewareServicesObject.FetchDevicesimdetailsMethod(reqobj, queryParams, headers);

		Assert.assertEquals(respObj.statusCode(), 401);

	}

	@Test(priority = 0,description = "invalid session token")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_003_invalidsessiontoken()
	{
		FetchDevicesimdetails reqobj = new FetchDevicesimdetails(P.TESTDATA.get("DeviceSimDetailsRequest"));

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("oldSerialNumber", "K6544KL83367");
		queryParams.put("leadId", "5615f6cc-b666-46d9-884a-2ac50680b76b");
		queryParams.put("oem", "PAX");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", "51402565-d70e-4f84000");
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");

		Response respObj = MiddlewareServicesObject.FetchDevicesimdetailsMethod(reqobj, queryParams, headers);

		Assert.assertEquals(respObj.statusCode(), 410);

	}



	@Test(priority = 0,description = "without device identifer")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_004_withoutdeviceIdentifier()
	{
		FetchDevicesimdetails reqobj = new FetchDevicesimdetails(P.TESTDATA.get("DeviceSimDetailsRequest"));

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("oldSerialNumber", "K6544KL83367");
		queryParams.put("leadId", "5615f6cc-b666-46d9-884a-2ac50680b76b");
		queryParams.put("oem", "PAX");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessiontoken);
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");

		Response respObj = MiddlewareServicesObject.FetchDevicesimdetailsMethod(reqobj, queryParams, headers);

		Assert.assertEquals(respObj.statusCode(), 200);

	}


	@Test(priority = 0,description = "without without lead ID")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_005_withoutLeadID()
	{
		FetchDevicesimdetails reqobj = new FetchDevicesimdetails(P.TESTDATA.get("DeviceSimDetailsRequest"));

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("oldSerialNumber", "K6544KL83367");
	//	queryParams.put("leadId", "5615f6cc-b666-46d9-884a-2ac50680b76b");
		queryParams.put("oem", "PAX");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessiontoken);
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");

		Response respObj = MiddlewareServicesObject.FetchDevicesimdetailsMethod(reqobj, queryParams, headers);

		Assert.assertEquals(respObj.statusCode(),400 );

	}

	@Test(priority = 0,description = "without serial number")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_006_withoutserialnumber()
	{
		FetchDevicesimdetails reqobj = new FetchDevicesimdetails(P.TESTDATA.get("DeviceSimDetailsRequest"));

		Map<String, String> queryParams = new HashMap<String, String>();
	//	queryParams.put("oldSerialNumber", "K6544KL83367");
		queryParams.put("leadId", "5615f6cc-b666-46d9-884a-2ac50680b76b");
		queryParams.put("oem", "PAX");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessiontoken);
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");

		Response respObj = MiddlewareServicesObject.FetchDevicesimdetailsMethod(reqobj, queryParams, headers);

		Assert.assertEquals(respObj.statusCode(), 400);

	}

	@Test(priority = 0,description = "without serial number")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_007_Invaliddeviceterminalnumber()
	{
		FetchDevicesimdetails reqobj = new FetchDevicesimdetails(P.TESTDATA.get("DeviceSimDetailsRequest"));

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("oldSerialNumber", "J4C7B4D3F34");
		queryParams.put("leadId", "5615f6cc-b666-46d9-884a-2ac50680b76b");
		queryParams.put("oem", "PAX");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessiontoken);
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");

		Response respObj = MiddlewareServicesObject.FetchDevicesimdetailsMethod(reqobj, queryParams, headers);

		Assert.assertEquals(respObj.statusCode(), 200);
		LOGGER.info(" Message : " + respObj.jsonPath().getString("simDeActivationFailureReason"));

	}

	@Test(priority = 0,description = "without edc Machine Make")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_008_withoutoem()
	{
		FetchDevicesimdetails reqobj = new FetchDevicesimdetails(P.TESTDATA.get("DeviceSimDetailsRequest"));

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("oldSerialNumber", "K6544KL83367");
		queryParams.put("leadId", "5615f6cc-b666-46d9-884a-2ac50680b76b");
	//	queryParams.put("oem", "PAX");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessiontoken);
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");

		Response respObj = MiddlewareServicesObject.FetchDevicesimdetailsMethod(reqobj, queryParams, headers);

		Assert.assertEquals(respObj.statusCode(), 200);
		LOGGER.info(" Message : " + respObj.jsonPath().getString("simDeActivationFailureReason"));

	}

	@Test(priority = 0,description = "different edc Machine Make")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_009_anotheoem()
	{
		FetchDevicesimdetails reqobj = new FetchDevicesimdetails(P.TESTDATA.get("DeviceSimDetailsRequest"));

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("oldSerialNumber", "K6544KL83367");
		queryParams.put("leadId", "5615f6cc-b666-46d9-884a-2ac50680b76b");
		queryParams.put("oem", "INGENICO");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessiontoken);
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");

		Response respObj = MiddlewareServicesObject.FetchDevicesimdetailsMethod(reqobj, queryParams, headers);

		Assert.assertEquals(respObj.statusCode(), 200);
		LOGGER.info(" Message : " + respObj.jsonPath().getString("simDeActivationFailureReason"));

	}

	@Test(priority = 0,description = "Different Lead of same flow")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_010_differentleadID1()
	{
		FetchDevicesimdetails reqobj = new FetchDevicesimdetails(P.TESTDATA.get("DeviceSimDetailsRequest"));

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("oldSerialNumber", "K6544KL83367");
		queryParams.put("leadId", "34e2d5fd-7fe1-4eb8-b70e-f90a05c8c213");
		queryParams.put("oem", "PAX");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessiontoken);
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");

		Response respObj = MiddlewareServicesObject.FetchDevicesimdetailsMethod(reqobj, queryParams, headers);

		Assert.assertEquals(respObj.statusCode(), 200);
		LOGGER.info(" Message : " + respObj.jsonPath().getString("simDeActivationFailureReason"));

	}

	@Test(priority = 0,description = "Different Lead of different slow")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_011_differentleadID2()
	{
		FetchDevicesimdetails reqobj = new FetchDevicesimdetails(P.TESTDATA.get("DeviceSimDetailsRequest"));

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("oldSerialNumber", "K6544KL83367");
		queryParams.put("leadId", "aa482da7-0faa-4757-94d0-28bcb21ba0a0");
		queryParams.put("oem", "PAX");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessiontoken);
		headers.put("deviceIdentifier", "OnePlus-IV2201-2fd53c7d86191a3a");

		Response respObj = MiddlewareServicesObject.FetchDevicesimdetailsMethod(reqobj, queryParams, headers);

		Assert.assertEquals(respObj.statusCode(), 200);

	}

}
