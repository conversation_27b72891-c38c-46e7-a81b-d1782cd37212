package OCL.MakerChecker.WorkflowEngine;

import Request.MakerChecker.WorkflowEngine.CreateWorkflow;
import Request.MakerChecker.WorkflowEngine.FetchWorkflow;
import Services.MakerChecker.MakerCheckerMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class CreateWorkflowTest extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(CreateWorkflowTest.class);
    MakerCheckerMiddlewareServices makerCheckerMiddlewareServices = new MakerCheckerMiddlewareServices();
    String jwt = MakerCheckerMiddlewareServices.generateJwtToken();

    public static String clientId = "BOSS";
    public static String Key = "827fd090-249d-4445-be67-3943c2f0c2a2";

    @Test(priority = 0, description = "Create Workflow")
    public void TC_01_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("useCase","SETTLEMENT_FREEZE");

        Map<String, String> body = new HashMap<String, String>();

        body.put("contextType","FILE");
        body.put("contextId","wyu27");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","FREEZE");
        body.put("currentStatus","UNFREEZE");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("freezeReason","LEA");
        body.put("freezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "null param")
    public void TC_02_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));

        /* --all the param,headers,body should be passed in hashmap-- */

        Map<String, String> queryParam = new HashMap<String, String>();

        Map<String, String> body = new HashMap<String, String>();

        JSONObject payload = new JSONObject();

        body.put("contextType","FILE");
        body.put("contextId","wyu27");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","FREEZE");
        body.put("currentStatus","UNFREEZE");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("freezeReason","LEA");
        body.put("freezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);

    }

    @Test(priority = 0, description = "without USE_CASE")
    public void TC_03_createworkflow() throws JSONException {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","FILE");
        body.put("contextId","SAMPLE_MERCHANT_BANK_DETAILS_EDIT1.csv");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("payload","");
        body.put("makerId","**********");
        body.put("makerEmail","<EMAIL>");
        body.put("boss_request_id","105");
        body.put("isEligibleForHistoricData","false");
        body.put("bulkSolutionType","OFFLINE");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);

    }

    @Test(priority = 0, description = "invalid USE_CASE")
    public void TC_04_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));

        /* --all the param,headers,body should be passed in hashmap-- */

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("useCase","DETAILS_UPDATE");

        Map<String, String> body = new HashMap<String, String>();

        body.put("contextType","FILE");
        body.put("contextId","SAMPLE_MERCHANT_BANK_DETAILS_EDIT1.csv");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("payload","");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);

    }

    @Test(priority = 0, description = "null contextType")
    public void TC_05_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));

        /* --all the param,headers,body should be passed in hashmap-- */

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("useCase","BIN_DETAILS_UPDATE");

        Map<String, String> body = new HashMap<String, String>();

        body.put("contextType","");
        body.put("contextId","wyu27");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","FREEZE");
        body.put("currentStatus","UNFREEZE");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("freezeReason","LEA");
        body.put("freezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");
        body.put("bulkSolutionType","OFFLINE");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);

    }

    @Test(priority = 0, description = "null contextId")
    public void TC_06_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));

        /* --all the param,headers,body should be passed in hashmap-- */

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("useCase","BIN_DETAILS_UPDATE");

        Map<String, String> body = new HashMap<String, String>();

        body.put("contextType","FILE");
        body.put("contextId","wyu27");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","");
        body.put("currentStatus","FREEZE");
        body.put("currentStatus","UNFREEZE");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("freezeReason","LEA");
        body.put("freezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);

    }

    @Test(priority = 0, description = "null channel")
    public void TC_07_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));

        /* --all the param,headers,body should be passed in hashmap-- */

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("useCase","BIN_DETAILS_UPDATE");

        Map<String, String> body = new HashMap<String, String>();

        body.put("contextType","FILE");
        body.put("contextId","wyu27");
        body.put("channel","");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","FREEZE");
        body.put("currentStatus","UNFREEZE");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("freezeReason","LEA");
        body.put("freezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");
        Map<String, String> headers = new HashMap<String, String>();

        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);

    }

    @Test(priority = 0, description = "without channel")
    public void TC_08_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","BIN_DETAILS_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","FILE");
        body.put("contextId","wyu27");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","FREEZE");
        body.put("currentStatus","UNFREEZE");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("freezeReason","LEA");
        body.put("freezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);

    }
    @Test(priority = 0, description = "without contextId")
    public void TC_09_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));

        /* --all the param,headers,body should be passed in hashmap-- */

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("useCase","BIN_DETAILS_UPDATE");

        Map<String, String> body = new HashMap<String, String>();

        body.put("contextType","FILE");
        body.put("contextId","");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","FREEZE");
        body.put("currentStatus","UNFREEZE");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("freezeReason","LEA");
        body.put("freezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);

    }
    @Test(priority = 0, description = "without Payload")
    public void TC_10_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("useCase","BIN_DETAILS_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","FILE");
        body.put("contextId","d3f63");
        body.put("channel","wdfq63547");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("makerId","**********");
        body.put("makerEmail","<EMAIL>");
        body.put("boss_request_id","105");
        body.put("isEligibleForHistoricData","false");
        body.put("bulkSolutionType","OFFLINE");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 0, description = "invalid jwt")
    public void TC_11_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","BIN_DETAILS_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","FILE");
        body.put("contextId","wyu27");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","FREEZE");
        body.put("currentStatus","UNFREEZE");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("freezeReason","LEA");
        body.put("freezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt","ashyagduiwdbxjabwdhjqwghj");

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 0, description = "without cookie-workflow should be created")
    public void TC_12_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","BIN_DETAILS_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","FILE");
        body.put("contextId","wyu27");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","FREEZE");
        body.put("currentStatus","UNFREEZE");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("freezeReason","LEA");
        body.put("freezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);
        headers.put("Cookie","");

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "Merchant solution type = Online")
    public void TC_13_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","SETTLEMENT_FREEZE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","FILE");
        body.put("contextId","wyu27");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","FREEZE");
        body.put("currentStatus","UNFREEZE");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("freezeReason","LEA");
        body.put("freezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","ONLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "Merchant solution type = Online")
    public void TC_14_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","SETTLEMENT_FREEZE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","FILE");
        body.put("contextId","wyu27");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","FREEZE");
        body.put("currentStatus","UNFREEZE");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("freezeReason","LEA");
        body.put("freezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","ONLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "SETTLEMENT_UNFREEZE")
    public void TC_15_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","SETTLEMENT_UNFREEZE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","FILE");
        body.put("contextId","wyu27");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","UNFREEZE");
        body.put("currentStatus","FREEZE");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "Bulk solution type workflow")
    public void TC_16_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","BULK_MERCHANT_BLOCK");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","FILE");
        body.put("contextId","SAMPLE_MERCHANT_BANK_DETAILS_EDIT1.csv");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","BLOCK");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("bulkSolutionType","OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "Bulk solution type-ONLINE")
    public void TC_17_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","BULK_MERCHANT_BLOCK");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","FILE");
        body.put("contextId","SAMPLE_MERCHANT_BANK_DETAILS_EDIT1.csv");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","BLOCK");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("bulkSolutionType","ONLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "Bulk solution type-ONLINE")
    public void TC_18_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","BULK_MERCHANT_BLOCK");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","FILE");
        body.put("contextId","SAMPLE_MERCHANT_BANK_DETAILS_EDIT1.csv");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","BLOCK");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("bulkSolutionType","ONLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "Context type too long - will throw sql exception")
    public void TC_19_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","SETTLEMENT_FREEZE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","Sample File To be Tested hgcashcascbasjkc");
        body.put("contextId","SAMPLE_MERCHANT_BANK_DETAILS_EDIT1.csv");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","BLOCK");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 0, description = "Channel - CASE_MANAGEMENT")
    public void TC_20_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","SETTLEMENT_FREEZE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","CASE_MANAGEMENT");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","BLOCK");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "Channel - RU_ADMIN_PANEL")
    public void TC_21_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","RU_PLATFORM_FEE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","RU_ADMIN_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","BLOCK");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "merchant solution type - CORPORATE")
    public void TC_22_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","MERCHANT_COMMISSION_ADD");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","BLOCK");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","CORPORATE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "merchant solution type - PPBL_ONLINE")
    public void TC_23_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","MERCHANT_COMMISSION_ADD");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","BLOCK");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","PPBL_ONLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "merchant solution type - PPBL_OFFLINE")
    public void TC_24_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","MERCHANT_COMMISSION_ADD");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","BLOCK");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","PPBL_OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "merchant solution type - PEPL")
    public void TC_25_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","MERCHANT_COMMISSION_ADD");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","BLOCK");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","PEPL");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "workflow created by non-super user")
    public void TC_26_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","SETTLEMENT_FREEZE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","BOSS_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","BLOCK");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "CASE_MANAGEMENT - UNFREEZE CASE")
    public void TC_27_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","SETTLEMENT_UNFREEZE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","CASE_MANAGEMENT");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","FREEZE");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","OFFLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "CASE_MANAGEMENT - UNFREEZE CASE - ONLINE ")
    public void TC_28_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","SETTLEMENT_UNFREEZE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","CASE_MANAGEMENT");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","FREEZE");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","ONLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "CASE_MANAGEMENT - FREEZE CASE - ONLINE ")
    public void TC_29_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","SETTLEMENT_FREEZE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","CASE_MANAGEMENT");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","FREEZE");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","ONLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "Usecase - INCENTIVE_APPROVAL")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_30_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","INCENTIVE_APPROVAL");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","BOSS");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","FREEZE");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","ONLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "UMP usecase - GV_PROGRAM_CREATE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_31_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","GV_PROGRAM_CREATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","UMP_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","ONLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "UMP usecase - GV_PROGRAM_UPDATE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_32_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","GV_PROGRAM_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","UMP_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","ONLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "UMP usecase - GV_ADD_MONEY")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_33_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","GV_ADD_MONEY");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","UMP_PANEL");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","ONLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "Gateway usecase - GATEWAY_CONFIG")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_34_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","GATEWAY_CONFIG");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","Gateway_config");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","ONLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "Gateway usecase - Incentive_approval")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_35_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","INCENTIVE_APPROVAL");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","Gateway_config");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","ONLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "Gateway usecase - INITIATE_REFUND")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_36_createworkflow()
    {
        CreateWorkflow createWorkflow = new CreateWorkflow(P.TESTDATA.get("CreateWorkflowRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","INITIATE_REFUND");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","qa8mid97505444272048");
        body.put("channel","Gateway_config");
        body.put("userContextType","mhd_agent");
        body.put("userContextId","123");
        body.put("currentStatus","");
        body.put("reason","MHD - Unhold basis KAM request");
        body.put("subReason","");
        body.put("unfreezeReason","LEA");
        body.put("unfreezeSubReason","SFIO");
        body.put("location","KOLKATA");
        body.put("remarks","testing");
        body.put("keys","DM114404013626");
        body.put("merchantSolutionType","ONLINE");
        body.put("merchantDisplayName","Link Service");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","**********");
        body.put("boss_request_id","1301");
        body.put("isEligibleForHistoricData","false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response CreateWorkflowResponse = makerCheckerMiddlewareServices.CreateWorkflowResponse(createWorkflow,headers,queryParam,body);

        int statusCode = CreateWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }




}

