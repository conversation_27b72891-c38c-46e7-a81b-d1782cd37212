package OCL.MakerChecker.WorkflowEngine;

import Request.MakerChecker.ReviewService.FetchRejectionReasons;
import Request.MakerChecker.WorkflowEngine.GetWorkflowDetail;
import Services.MakerChecker.MakerCheckerMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class GetWorkflowDetailsTest extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(GetWorkflowDetailsTest.class);
    MakerCheckerMiddlewareServices makerCheckerMiddlewareServices = new MakerCheckerMiddlewareServices();
    String jwt = MakerCheckerMiddlewareServices.generateJwtTokenForWorkflowCMSClient();

    @Test(priority = 0, description = "Get Workflow Detail")
    public void TC_01_GetWorkflowDetail(){

        GetWorkflowDetail getWorkflowDetail = new GetWorkflowDetail(P.TESTDATA.get("GetWorkflowDetailRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("workflowExecutionId","774");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("Content-Type","application/json");
        headers.put("jwt",jwt);

        Response getWorkflowDetailResponse = makerCheckerMiddlewareServices.getWorkflowDetailResponse(getWorkflowDetail,headers,queryParam);

        int statusCode = getWorkflowDetailResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 0, description = "null workflowExecutionId")
    public void TC_02_GetWorkflowDetail(){

        GetWorkflowDetail getWorkflowDetail = new GetWorkflowDetail(P.TESTDATA.get("GetWorkflowDetailRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("workflowExecutionId"," ");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("Content-Type","application/json");
        headers.put("jwt",jwt);

        Response getWorkflowDetailResponse = makerCheckerMiddlewareServices.getWorkflowDetailResponse(getWorkflowDetail,headers,queryParam);

        int statusCode = getWorkflowDetailResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }

    @Test(priority = 0, description = "Invalid Workflow Execution Id")
    public void TC_03_GetWorkflowDetail(){

        GetWorkflowDetail getWorkflowDetail = new GetWorkflowDetail(P.TESTDATA.get("GetWorkflowDetailRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("workflowExecutionId","774590");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("Content-Type","application/json");
        headers.put("jwt",jwt);

        Response getWorkflowDetailResponse = makerCheckerMiddlewareServices.getWorkflowDetailResponse(getWorkflowDetail,headers,queryParam);

        int statusCode = getWorkflowDetailResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 0, description = "null jwt")
    public void TC_04_GetWorkflowDetail()
    {

        GetWorkflowDetail getWorkflowDetail = new GetWorkflowDetail(P.TESTDATA.get("GetWorkflowDetailRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("workflowExecutionId","774");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("Content-Type","application/json");
        headers.put("jwt"," ");

        Response getWorkflowDetailResponse = makerCheckerMiddlewareServices.getWorkflowDetailResponse(getWorkflowDetail,headers,queryParam);

        int statusCode = getWorkflowDetailResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }

    @Test(priority = 0, description = "invalid jwt")
    public void TC_05_GetWorkflowDetail()
    {

        GetWorkflowDetail getWorkflowDetail = new GetWorkflowDetail(P.TESTDATA.get("GetWorkflowDetailRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("workflowExecutionId","774");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("Content-Type","application/json");
        headers.put("jwt","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJXRSIsImNsaWVudCI6IlJldmlldy1TZXJ2aWNlIiwiaWF0IjoxNjg5MTYyOTUyLCJ0aW1lc3RhbXAiOiIyMDIzLTA3LTEyVDE3OjI2OjI0LjI1MyswNTozMCJ9.d1zdcvXdRcJLEuShDbrhUQtQJo5Qs0aaXL1ngL-OYEo");

        Response getWorkflowDetailResponse = makerCheckerMiddlewareServices.getWorkflowDetailResponse(getWorkflowDetail,headers,queryParam);

        int statusCode = getWorkflowDetailResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 0, description = "without Cookie")
    public void TC_06_GetWorkflowDetail(){

        GetWorkflowDetail getWorkflowDetail = new GetWorkflowDetail(P.TESTDATA.get("GetWorkflowDetailRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("workflowExecutionId","774");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("jwt",jwt);

        Response getWorkflowDetailResponse = makerCheckerMiddlewareServices.getWorkflowDetailResponse(getWorkflowDetail,headers,queryParam);

        int statusCode = getWorkflowDetailResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }


}
