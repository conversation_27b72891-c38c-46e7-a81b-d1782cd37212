package OCL.MakerChecker.WorkflowEngine;

import Request.MakerChecker.WorkflowEngine.FetchWorkflow;
import Request.MakerChecker.WorkflowEngine.ReviewRequestCallback;
import Services.MakerChecker.MakerCheckerMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class ReviewRequestCallbackTest extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(ReviewRequestCallbackTest.class);
    MakerCheckerMiddlewareServices makerCheckerMiddlewareServices = new MakerCheckerMiddlewareServices();
    String jwt = MakerCheckerMiddlewareServices.generateJwtTokenForReviewCallbackToWorkflow();

    @Test(priority = 0, description = "Review Request Callback")
    public void TC_01_ReviewCallback() {
        ReviewRequestCallback reviewRequestCallback = new ReviewRequestCallback(P.TESTDATA.get("ReviewRequestCallbackRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> body = new HashMap<String, String>();

        body.put("workFlowExecutionId", "4048");
        body.put("rejectionReason", "");
        body.put("status", "APPROVED");
        body.put("stage", "L1");
        body.put("remarks", "null");
        body.put("custId", "1700620202");
        body.put("email", "<EMAIL>");
        body.put("timestamp", "2023-07-31 10:28:26.64");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", jwt);

        Response ReviewRequestCallbackResponse = makerCheckerMiddlewareServices.ReviewRequestCallbackResponse(reviewRequestCallback,headers,body);

        int statusCode = ReviewRequestCallbackResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "INVALID WorkflowId")
    public void TC_02_ReviewCallback() {
        ReviewRequestCallback reviewRequestCallback = new ReviewRequestCallback(P.TESTDATA.get("ReviewRequestCallbackRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> body = new HashMap<String, String>();

        body.put("workFlowExecutionId", "4600");
        body.put("rejectionReason", "");
        body.put("status", "APPROVED");
        body.put("stage", "L1");
        body.put("remarks", "null");
        body.put("custId", "1700620202");
        body.put("email", "<EMAIL>");
        body.put("timestamp", "2023-07-31 10:28:26.64");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", jwt);

        Response ReviewRequestCallbackResponse = makerCheckerMiddlewareServices.ReviewRequestCallbackResponse(reviewRequestCallback,headers,body);

        int statusCode = ReviewRequestCallbackResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400, "Unable to find any allowable node Id");
    }
    @Test(priority = 0, description = "WorkflowId-null")
    public void TC_03_ReviewCallback() {
        ReviewRequestCallback reviewRequestCallback = new ReviewRequestCallback(P.TESTDATA.get("ReviewRequestCallbackRequest"));
        Map<String, String> body = new HashMap<String, String>();

        body.put("workFlowExecutionId", "");
        body.put("rejectionReason", "");
        body.put("status", "APPROVED");
        body.put("stage", "L1");
        body.put("remarks", "null");
        body.put("custId", "1700620202");
        body.put("email", "<EMAIL>");
        body.put("timestamp", "2023-07-31 10:28:26.64");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", jwt);

        Response ReviewRequestCallbackResponse = makerCheckerMiddlewareServices.ReviewRequestCallbackResponse(reviewRequestCallback,headers,body);

        int statusCode = ReviewRequestCallbackResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400, "field 'workFlowExecutionId' is required");
    }
    @Test(priority = 0, description = "null-status")
    public void TC_04_ReviewCallback() {
        ReviewRequestCallback reviewRequestCallback = new ReviewRequestCallback(P.TESTDATA.get("ReviewRequestCallbackRequest"));
        Map<String, String> body = new HashMap<String, String>();

        body.put("workFlowExecutionId", "4598");
        body.put("rejectionReason", "");
        body.put("status", "");
        body.put("stage", "L1");
        body.put("remarks", "null");
        body.put("custId", "1700620202");
        body.put("email", "<EMAIL>");
        body.put("timestamp", "2023-07-31 10:28:26.64");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", jwt);

        Response ReviewRequestCallbackResponse = makerCheckerMiddlewareServices.ReviewRequestCallbackResponse(reviewRequestCallback,headers,body);

        int statusCode = ReviewRequestCallbackResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400, "field 'status' must not be blank");
    }
    @Test(priority = 0, description = "With Status = REJECTED")
    public void TC_05_ReviewCallback() {
        ReviewRequestCallback reviewRequestCallback = new ReviewRequestCallback(P.TESTDATA.get("ReviewRequestCallbackRequest"));
        Map<String, String> body = new HashMap<String, String>();

        body.put("workFlowExecutionId", "4047");
        body.put("rejectionReason", "");
        body.put("status", "REJECTED");
        body.put("stage", "L1");
        body.put("remarks", "null");
        body.put("custId", "1700620202");
        body.put("email", "<EMAIL>");
        body.put("timestamp", "2023-07-31 10:28:26.64");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", jwt);

        Response ReviewRequestCallbackResponse = makerCheckerMiddlewareServices.ReviewRequestCallbackResponse(reviewRequestCallback,headers,body);

        int statusCode = ReviewRequestCallbackResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200, "successfully updated");
    }
    @Test(priority = 0, description = "null-stage")
    public void TC_06_ReviewCallback() {
        ReviewRequestCallback reviewRequestCallback = new ReviewRequestCallback(P.TESTDATA.get("ReviewRequestCallbackRequest"));
        Map<String, String> body = new HashMap<String, String>();

        body.put("workFlowExecutionId", "4598");
        body.put("rejectionReason", "");
        body.put("status", "APPROVED");
        body.put("stage", "");
        body.put("remarks", "null");
        body.put("custId", "1700620202");
        body.put("email", "<EMAIL>");
        body.put("timestamp", "2023-07-31 10:28:26.64");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", jwt);

        Response ReviewRequestCallbackResponse = makerCheckerMiddlewareServices.ReviewRequestCallbackResponse(reviewRequestCallback,headers,body);

        int statusCode = ReviewRequestCallbackResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 0, description = "null-custId")
    public void TC_07_ReviewCallback() {
        ReviewRequestCallback reviewRequestCallback = new ReviewRequestCallback(P.TESTDATA.get("ReviewRequestCallbackRequest"));
        Map<String, String> body = new HashMap<String, String>();

        body.put("workFlowExecutionId", "4598");
        body.put("rejectionReason", "");
        body.put("status", "APPROVED");
        body.put("stage", "L1");
        body.put("remarks", "null");
        body.put("custId", "");
        body.put("email", "<EMAIL>");
        body.put("timestamp", "2023-07-31 10:28:26.64");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", jwt);

        Response ReviewRequestCallbackResponse = makerCheckerMiddlewareServices.ReviewRequestCallbackResponse(reviewRequestCallback,headers,body);

        int statusCode = ReviewRequestCallbackResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400, "field 'custId' must not be blank");
    }
    @Test(priority = 0, description = "null-")
    public void TC_08_ReviewCallback() {
        ReviewRequestCallback reviewRequestCallback = new ReviewRequestCallback(P.TESTDATA.get("ReviewRequestCallbackRequest"));
        Map<String, String> body = new HashMap<String, String>();

        body.put("workFlowExecutionId", "4598");
        body.put("rejectionReason", "");
        body.put("status", "APPROVED");
        body.put("stage", "L1");
        body.put("remarks", "null");
        body.put("custId", "");
        body.put("email", "<EMAIL>");
        body.put("timestamp", "2023-07-31 10:28:26.64");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", jwt);

        Response ReviewRequestCallbackResponse = makerCheckerMiddlewareServices.ReviewRequestCallbackResponse(reviewRequestCallback,headers,body);

        int statusCode = ReviewRequestCallbackResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400, "field 'custId' must not be blank");
    }
}
