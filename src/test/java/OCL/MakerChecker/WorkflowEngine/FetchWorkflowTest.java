package OCL.MakerChecker.WorkflowEngine;

import Request.MakerChecker.WorkflowEngine.FetchWorkflow;
import Services.MakerChecker.MakerCheckerMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FetchWorkflowTest extends BaseMethod {


    private static final Logger LOGGER = LogManager.getLogger(FetchWorkflowTest.class);
    MakerCheckerMiddlewareServices makerCheckerMiddlewareServices = new MakerCheckerMiddlewareServices();

    String jwt = MakerCheckerMiddlewareServices.generateJwtTokenForWorkflowBossClient();

    @Test(priority = 0, description = "Fetch Workflow details for workflow engine")
    public void TC_01_fetchworkflowdetails()
    {
        FetchWorkflow fetchWorkflow = new FetchWorkflow(P.TESTDATA.get("FetchWorkflowRequest"));

        /* --all the param,headers,body should be passed in hashmap-- */

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("pageNumber","0");
        queryParam.put("limit","10");


        Map<String, String> body = new HashMap<String, String>();

        body.put("contextType","MID");
        body.put("contextId","mid1231324");
        body.put("userContextType","mhd_agent");
        body.put("startDate","2023-04-25");
        body.put("EndDate","2023-04-26");
        body.put("fieldProjections","payload.changeList");
        body.put("useCaseList","freeze");
        body.put("statusList","0");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response fetchWorkflowResponse = makerCheckerMiddlewareServices.fetchWorkflowResponse(fetchWorkflow,headers,queryParam,body);

        int statusCode = fetchWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0 , description = "page-number_null")
    public void TC_02_blankpagenumber()
    {
        FetchWorkflow fetchWorkflow = new FetchWorkflow(P.TESTDATA.get("FetchWorkflowRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("limit","10");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","mid1231324");
        body.put("userContextType","mhd_agent");
        body.put("startDate","2023-04-25");
        body.put("EndDate","2023-04-26");
        body.put("fieldProjections","payload.changeList");
        body.put("useCaseList","freezed");
        body.put("statusList","0");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response fetchWorkflowResponse = makerCheckerMiddlewareServices.fetchWorkflowResponse(fetchWorkflow,headers,queryParam,body);

        int statusCode = fetchWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200, "Testcase Passed");


    }
    @Test(priority = 0 , description = "limit_null")
    public void TC_03_limitnull()
    {
        FetchWorkflow fetchWorkflow = new FetchWorkflow(P.TESTDATA.get("FetchWorkflowRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("pageNumber","0");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","mid1231324");
        body.put("userContextType","mhd_agent");
        body.put("startDate","2023-04-25");
        body.put("EndDate","2023-04-26");
        body.put("fieldProjections","payload.changeList");
        body.put("useCaseList","freezed");
        body.put("statusList","0");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response fetchWorkflowResponse = makerCheckerMiddlewareServices.fetchWorkflowResponse(fetchWorkflow,headers,queryParam,body);

        int statusCode = fetchWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200, "Testcase Passed");


    }
    @Test(priority = 0 , description = "without FieldProjection")
    public void TC_06_withoutFieldProjection()
    {
        FetchWorkflow fetchWorkflow = new FetchWorkflow(P.TESTDATA.get("FetchWorkflowRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("pageNumber","0");
        queryParam.put("limit","10");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","12351235");
        body.put("userContextType","mhd_agent");
        body.put("startDate","2023-04-25");
        body.put("EndDate","2023-04-26");
        body.put("useCaseList","hello");
        body.put("statusList","0");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt",jwt);

        Response fetchWorkflowResponse = makerCheckerMiddlewareServices.fetchWorkflowResponse(fetchWorkflow,headers,queryParam,body);

        int statusCode = fetchWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400, "invalid use case");

    }

    @Test(priority = 0 , description = "invalid jwt")
    public void TC_07_InvalidJWT()
    {
        FetchWorkflow fetchWorkflow = new FetchWorkflow(P.TESTDATA.get("FetchWorkflowRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("pageNumber","0");
        queryParam.put("limit","10");

        Map<String, String> body = new HashMap<String, String>();
        body.put("contextType","MID");
        body.put("contextId","12351235");
        body.put("userContextType","mhd_agent");
        body.put("startDate","2023-04-25");
        body.put("EndDate","2023-04-26");
        body.put("fieldProjections","payload.changeList");
        body.put("useCaseList","hello");
        body.put("statusList","0");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type","application/json");
        headers.put("jwt","afdsw547612uewfgdhfashf");

        Response fetchWorkflowResponse = makerCheckerMiddlewareServices.fetchWorkflowResponse(fetchWorkflow,headers,queryParam,body);

        int statusCode = fetchWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401, "User Authentication Failed");

    }

    @Test(priority = 0 , description = "without jwt")
    public void TC_08_InvalidUseCaseList()
    {
        FetchWorkflow fetchWorkflow = new FetchWorkflow(P.TESTDATA.get("FetchWorkflowRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("pageNumber","0");
        queryParam.put("limit","10");

        Map<String, String> body = new HashMap<String, String>();

        body.put("contextType","MID");
        body.put("contextId","12351235");
        body.put("userContextType","mhd_agent");
        body.put("startDate","2023-04-25");
        body.put("EndDate","2023-04-26");
        body.put("fieldProjections","payload.changeList");
        body.put("useCaseList","hello");
        body.put("statusList","0");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("Content-type","application/json");

        Response fetchWorkflowResponse = makerCheckerMiddlewareServices.fetchWorkflowResponse(fetchWorkflow,headers,queryParam,body);

        int statusCode = fetchWorkflowResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401, "User Authentication Failed");

    }

}


