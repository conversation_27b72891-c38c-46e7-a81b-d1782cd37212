package OCL.MakerChecker.ReviewService;

import Request.MakerChecker.ReviewService.DownloadDMSDoc;
import Services.MakerChecker.MakerCheckerMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

public class DownloadDMSDocTest extends BaseMethod {

    private static final Logger LOGGER = Logger.getLogger(String.valueOf(DownloadDMSDocTest.class));

    MakerCheckerMiddlewareServices makerCheckerMiddlewareServices = new MakerCheckerMiddlewareServices();

    String cookieVal= makerCheckerMiddlewareServices.getBossSessionToken("<EMAIL>","paytm@123");
    String url = "https://oe-staging5.paytm.com/checker/search";
    @Test(priority = 0, description = "Download DMS Doc")
    public void TC_01_DownloadDMSDoc(){

        DownloadDMSDoc downloadDMSDoc = new DownloadDMSDoc(P.TESTDATA.get("DownloadDMSDocRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","5");
        queryParam.put("stageId","5");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response dmsDocResponse = makerCheckerMiddlewareServices.dmsDocResponse(downloadDMSDoc,headers,queryParam);

        int statusCode = dmsDocResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200, "Test case Passed");
    }

    @Test(priority = 0, description = "null case Id")
    public void TC_02_nullcaseId(){

        DownloadDMSDoc downloadDMSDoc = new DownloadDMSDoc(P.TESTDATA.get("DownloadDMSDocRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("stageId","5");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response dmsDocResponse = makerCheckerMiddlewareServices.dmsDocResponse(downloadDMSDoc,headers,queryParam);

        int statusCode = dmsDocResponse.getStatusCode();
        Assert.assertEquals(statusCode, 500, "case Id should not be null");
    }

    @Test(priority = 0, description = "null case Id")
    public void TC_03_nullstageId(){

        DownloadDMSDoc downloadDMSDoc = new DownloadDMSDoc(P.TESTDATA.get("DownloadDMSDocRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","5");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response dmsDocResponse = makerCheckerMiddlewareServices.dmsDocResponse(downloadDMSDoc,headers,queryParam);

        int statusCode = dmsDocResponse.getStatusCode();
        Assert.assertEquals(statusCode, 500, "stage Id should not be null");
    }
}
