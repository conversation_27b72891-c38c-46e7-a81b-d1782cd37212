package OCL.MakerChecker.ReviewService;

import Request.MakerChecker.ReviewService.ApproveRejectCase;
import Request.MakerChecker.ReviewService.FetchSubGroupCheckers;
import Services.MakerChecker.MakerCheckerMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FetchSubGroupCheckersTest extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(FetchReviewStatusCountTest.class);
    MakerCheckerMiddlewareServices makerCheckerMiddlewareServices = new MakerCheckerMiddlewareServices();
    String Cookieval = "Path=/; OAUTH_JSESSIONID=FEFD2D4F44591CEC84D0AD4878E122ED; Path=/";
    String jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE2OTI2ODQ3NjU0MjQiLCJjdXN0X2lkIjoiMTEwNzE5OTQwNyIsImNsaWVudF9pZCI6InRlc3RjbGllbnQifQ.XACcqPmQRq0zSoIY0VS7nuDQxKAs47rALSEmbhdEinM";

    @Test(priority = 0, description = "Fetch Usecase - Maker Sub_Group")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "freeze_maker");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "sub_group");
        queryParam.put("roleAssociationValue", "ONLINE_RISK");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of maker's subgroup - ONLINE_RISK ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "Freeze_Kam_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "sub_group");
        queryParam.put("roleAssociationValue", "ONLINE_RISK");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of maker's subgroup - ONLINE_MHD ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "Freeze_Kam_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "sub_group");
        queryParam.put("roleAssociationValue", "ONLINE_MHD");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of maker's subgroup - OFFLINE_RISK ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "Freeze_Kam_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "sub_group");
        queryParam.put("roleAssociationValue", "OFFLINE_RISK");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of maker's subgroup - OFFLINE_MHD ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "Freeze_Kam_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "sub_group");
        queryParam.put("roleAssociationValue", "OFFLINE_MHD");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of maker's multiple subgroup but got the first one")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "Freeze_Kam_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "sub_group");
        queryParam.put("roleAssociationValue", "ONLINE_RISK");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of maker's role for BULK_MERCHANT_TIERED_COMMISSION_DETAILS_Maker")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "BULK_MERCHANT_TIERED_COMMISSION_DETAILS_Maker");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "sub_group");
        queryParam.put("roleAssociationValue", "ONLINE_RISK");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of maker's sub_group role for BULK_MERCHANT_TIERED_COMMISSION_DETAILS_Maker")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "BULK_MERCHANT_TIERED_COMMISSION_DETAILS_Kam_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "sub_group");
        queryParam.put("roleAssociationValue", "ONLINE_RISK");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of merchantSolutionType - OFFLINE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "Freeze_Kam_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "merchantSolutionType");
        queryParam.put("roleAssociationValue", "OFFLINE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of merchantSolutionType - ONLINE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "Freeze_Kam_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "merchantSolutionType");
        queryParam.put("roleAssociationValue", "ONLINE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of merchantSolutionType - CORPORATE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "Freeze_Kam_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "merchantSolutionType");
        queryParam.put("roleAssociationValue", "ONLINE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of merchantSolutionType - PPBL_ONLINE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "Freeze_Kam_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "merchantSolutionType");
        queryParam.put("roleAssociationValue", "PPBL_ONLINE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of merchantSolutionType - PPBL_OFFLINE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "Freeze_Kam_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "merchantSolutionType");
        queryParam.put("roleAssociationValue", "PPBL_OFFLINE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of merchantSolutionType - PEPL")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_14_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "Freeze_Kam_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "merchantSolutionType");
        queryParam.put("roleAssociationValue", "PEPL");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of bulkSolutionType - OFFLINE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_15_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "BULK_MERCHANT_BASIC_DETAILS_EDIT_KAM_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "bulkSolutionType");
        queryParam.put("roleAssociationValue", "OFFLINE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of bulkSolutionType - ONLINE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_16_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "BULK_MERCHANT_BASIC_DETAILS_EDIT_KAM_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "bulkSolutionType");
        queryParam.put("roleAssociationValue", "ONLINE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of bulkSolutionType - OFFLINE")

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_17_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "BULK_MERCHANT_BASIC_DETAILS_EDIT_KAM_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "bulkSolutionType");
        queryParam.put("roleAssociationValue", "OFFLINE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of bulkSolutionType - CORPORATE")

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_18_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "BULK_MERCHANT_BASIC_DETAILS_EDIT_KAM_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "bulkSolutionType");
        queryParam.put("roleAssociationValue", "OFFLINE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of bulkSolutionType - PPBL_ONLINE")

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_19_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "BULK_MERCHANT_BASIC_DETAILS_EDIT_KAM_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "bulkSolutionType");
        queryParam.put("roleAssociationValue", "PPBL_ONLINE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of bulkSolutionType - PPBL_OFFLINE")

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_20_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "BULK_MERCHANT_BASIC_DETAILS_EDIT_KAM_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "bulkSolutionType");
        queryParam.put("roleAssociationValue", "PPBL_OFFLINE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Checkers on bases of bulkSolutionType - PEPL")

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_21_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-service");
        queryParam.put("roleName", "BULK_MERCHANT_BASIC_DETAILS_EDIT_KAM_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "bulkSolutionType");
        queryParam.put("roleAssociationValue", "PEPL");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "Invalid contract name or role provided")

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_22_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-system");
        queryParam.put("roleName", "BULK_MERCHANT_BASIC_DETAILS_EDIT_KAM_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "bulkSolutionType");
        queryParam.put("roleAssociationValue", "PEPL");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }
    @Test(priority = 0, description = "fetch users on sub_team bases")

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_23_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-system");
        queryParam.put("roleName", "INCENTIVE_APPROVAL_KAM_L1");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "teamSubteam");
        queryParam.put("roleAssociationValue", "Semi-Organised_QR_Field_Sales");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }
    @Test(priority = 0, description = "fetch users on sub_team bases with invalid role")

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_24_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */


        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-system");
        queryParam.put("roleName", "BULK_MERCHANT_COMMISSION_CFRF_DETAILS_KAM_L2");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "teamSubteam");
        queryParam.put("roleAssociationValue", "Semi-Organised_QR_Field_Sales");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }
    @Test(priority = 0, description = "Return null users if sub_group is null")

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_25_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-system");
        queryParam.put("roleName", "freeze_maker");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "sub_group");
        queryParam.put("roleAssociationValue", "ONLINE_RISK_OPS");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }
    @Test(priority = 0, description = "Return null users if sub_group is invalid")

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_26_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-system");
        queryParam.put("roleName", "freeze_maker");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "sub_group");
        queryParam.put("roleAssociationValue", "ONLINE_RISK_OPS_ABC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token", jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie", Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }
    @Test(priority = 0, description = "invalid jwt provided ")

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_27_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-system");
        queryParam.put("roleName", "freeze_maker");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "sub_group");
        queryParam.put("roleAssociationValue", "ONLINE_RISK_OPS_ABC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token",jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie",Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 0, description = "null cookie provided ")

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_28_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-system");
        queryParam.put("roleName", "freeze_maker");
        queryParam.put("pageNumber", "1");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "sub_group");
        queryParam.put("roleAssociationValue", "ONLINE_RISK_OPS_ABC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token",jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie"," ");

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }
    @Test(priority = 0, description = "Invalid Pagenation ")

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_29_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-system");
        queryParam.put("roleName", "freeze_maker");
        queryParam.put("pageNumber", "0");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "sub_group");
        queryParam.put("roleAssociationValue", "ONLINE_RISK_OPS_ABC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token",jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie",Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }
    @Test(priority = 0, description = "Different team sub_group checkers ")

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_30_FetchSubGroupCheckers() {
        FetchSubGroupCheckers fetchSubGroupCheckers = new FetchSubGroupCheckers(P.TESTDATA.get("FetchSubGroupCheckersRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("contract", "review-system");
        queryParam.put("roleName", "freeze_maker");
        queryParam.put("pageNumber", "0");
        queryParam.put("pageSize", "50");
        queryParam.put("roleAssociationType", "sub_group");
        queryParam.put("roleAssociationValue", "ONLINE_ABC_RISK");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cache-control", "no-cache");
        headers.put("x-jwt-token",jwt);
        headers.put("X-KycApp-RequestID", "kyr");
        headers.put("Cookie",Cookieval);

        Response FetchSubGroupCheckersResponse = makerCheckerMiddlewareServices.FetchSubGroupCheckersResponse(fetchSubGroupCheckers, headers, queryParam);

        int statusCode = FetchSubGroupCheckersResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }


}
