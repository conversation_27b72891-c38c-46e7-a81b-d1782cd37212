package OCL.MakerChecker.ReviewService;

import Request.MakerChecker.ReviewService.FetchAllCases;
import Request.MakerChecker.ReviewService.FetchRejectionReasons;
import Services.MakerChecker.MakerCheckerMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FetchAllCasesTest extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(FetchAllCasesTest.class);

    MakerCheckerMiddlewareServices makerCheckerMiddlewareServices = new MakerCheckerMiddlewareServices();

    String cookieVal= makerCheckerMiddlewareServices.getBossSessionToken("<EMAIL>","paytm@123");

    final public static String url = "https://oe-staging5.paytm.com/search";

    @Test(priority = 0, description = "Fetch open cases")
    public void fetchAllCases(){

        FetchAllCases fetchAllCases = new FetchAllCases(P.TESTDATA.get("FetchAllCasesRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("pageNumber","1");
        queryParam.put("pageLimit","10");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Map<String, String> body = new HashMap<String, String>();
        body.put("useCase","FREEZE");
        body.put("status","APPROVED");
        body.put("contextKey","MID");
        body.put("contextValue","mid1231324");
        body.put("createdDateStart","2023-06-25");
        body.put("createdDateEnd","2023-06-26");

        Response fetchAllcasesResponse = makerCheckerMiddlewareServices.fetchAllcasesResponse(fetchAllCases,headers,queryParam,body);

        int statusCode = fetchAllcasesResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 0, description = "null page number")
    public void FC_01(){

        FetchAllCases fetchAllCases = new FetchAllCases(P.TESTDATA.get("FetchAllCasesRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("pageLimit","10");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Map<String, String> body = new HashMap<String, String>();

        body.put("useCase","FREEZE");
        body.put("status","APPROVED");
        body.put("contextKey","MID");
        body.put("contextValue","mid1231324");
        body.put("createdDateStart","2023-06-25");
        body.put("createdDateEnd","2023-06-26");

        Response fetchAllcasesResponse = makerCheckerMiddlewareServices.fetchAllcasesResponse(fetchAllCases,headers,queryParam,body);

        int statusCode = fetchAllcasesResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 0, description = "null page Limit")
    public void FC_02(){

        FetchAllCases fetchAllCases = new FetchAllCases(P.TESTDATA.get("FetchAllCasesRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("pageNumber","1");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Map<String, String> body = new HashMap<String, String>();

        body.put("useCase","FREEZE");
        body.put("status","APPROVED");
        body.put("contextKey","MID");
        body.put("contextValue","mid1231324");
        body.put("createdDateStart","2023-06-25");
        body.put("createdDateEnd","2023-06-26");

        Response fetchAllcasesResponse = makerCheckerMiddlewareServices.fetchAllcasesResponse(fetchAllCases,headers,queryParam,body);

        int statusCode = fetchAllcasesResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 0, description = "invalid page Limit")
    public void FC_03(){

        FetchAllCases fetchAllCases = new FetchAllCases(P.TESTDATA.get("FetchAllCasesRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("pageNumber","1");
        queryParam.put("pageLimit","100");


        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Map<String, String> body = new HashMap<String, String>();

        body.put("useCase","FREEZE");
        body.put("status","APPROVED");
        body.put("contextKey","MID");
        body.put("contextValue","mid1231324");
        body.put("createdDateStart","2023-06-25");
        body.put("createdDateEnd","2023-06-26");

        Response fetchAllcasesResponse = makerCheckerMiddlewareServices.fetchAllcasesResponse(fetchAllCases,headers,queryParam,body);

        int statusCode = fetchAllcasesResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "invalid mid")
    public void FC_05(){

        FetchAllCases fetchAllCases = new FetchAllCases(P.TESTDATA.get("FetchAllCasesRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("pageNumber","1");
        queryParam.put("pageLimit","100");


        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Map<String, String> body = new HashMap<String, String>();

        body.put("useCase","nullFREEZE");
        body.put("status","APPROVED");
        body.put("contextKey","MID");
        body.put("contextValue","ewbsuw1132");
        body.put("createdDateStart","2023-06-25");
        body.put("createdDateEnd","2023-06-26");

        Response fetchAllcasesResponse = makerCheckerMiddlewareServices.fetchAllcasesResponse(fetchAllCases,headers,queryParam,body);

        int statusCode = fetchAllcasesResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 0, description = "without body") // //for Empty request body all the cases will be returned which are allocated to the logged in user
    public void FC_06(){

        FetchAllCases fetchAllCases = new FetchAllCases(P.TESTDATA.get("FetchAllCasesRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("pageNumber","1");
        queryParam.put("pageLimit","10");


        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Map<String, String> body = new HashMap<>();

        Response fetchAllcasesResponse = makerCheckerMiddlewareServices.fetchAllcasesResponse(fetchAllCases,headers,queryParam,body);

        int statusCode = fetchAllcasesResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }


}
