package OCL.MakerChecker.ReviewService;

import Request.MakerChecker.ReviewService.ReviewPanelGetAllResources;
import Services.MakerChecker.MakerCheckerMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class ReviewPanelGetAllResourcesTest extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(ReviewPanelGetAllResourcesTest.class);
    MakerCheckerMiddlewareServices makerCheckerMiddlewareServices = new MakerCheckerMiddlewareServices();
    String cookieVal= makerCheckerMiddlewareServices.getBossSessionToken("<EMAIL>","paytm@123");
    final public static String url = "https://oe-staging5.paytm.com/checker/search";
    @Test(priority = 0, description = "get All Resources")
    public void TC_01_getallResources(){
        ReviewPanelGetAllResources reviewPanelGetAllResources = new ReviewPanelGetAllResources(P.TESTDATA.get("ReviewPanelGetAllResourcesRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("keys","TEXTBOX_LEN,USE_CASES,SEARCH_CONTEXT_KEYS,STATUSES,REVIEW_ACTIONS");
        queryParam.put("context","CHECKER");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response getallResourcesResponse = makerCheckerMiddlewareServices.reviewPanelGetAllResourcesResponse(reviewPanelGetAllResources,headers,queryParam);

        int statusCode = getallResourcesResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "null keys - return all")
    public void TC_02_getallResources(){
        ReviewPanelGetAllResources reviewPanelGetAllResources = new ReviewPanelGetAllResources(P.TESTDATA.get("GetallResourcesRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("keys","");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response getallResourcesResponse = makerCheckerMiddlewareServices.reviewPanelGetAllResourcesResponse(reviewPanelGetAllResources,headers,queryParam);

        int statusCode = getallResourcesResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "invalid keys")
    public void TC_03_getallResources(){
        ReviewPanelGetAllResources reviewPanelGetAllResources = new ReviewPanelGetAllResources(P.TESTDATA.get("GetallResourcesRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("keys","cxbascxgq");
        queryParam.put("context","CHECKER");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response getallResourcesResponse = makerCheckerMiddlewareServices.reviewPanelGetAllResourcesResponse(reviewPanelGetAllResources,headers,queryParam);

        int statusCode = getallResourcesResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 0, description = "CONTEXT-Maker")
    public void TC_04_getallResources(){
        ReviewPanelGetAllResources reviewPanelGetAllResources = new ReviewPanelGetAllResources(P.TESTDATA.get("GetallResourcesRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("keys","TEXTBOX_LEN,USE_CASES,SEARCH_CONTEXT_KEYS,STATUSES,REVIEW_ACTIONS");
        queryParam.put("context","MAKER");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response getallResourcesResponse = makerCheckerMiddlewareServices.reviewPanelGetAllResourcesResponse(reviewPanelGetAllResources,headers,queryParam);

        int statusCode = getallResourcesResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "invalid-token")
    public void TC_05_getallResources(){
        ReviewPanelGetAllResources reviewPanelGetAllResources = new ReviewPanelGetAllResources(P.TESTDATA.get("GetallResourcesRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("keys","TEXTBOX_LEN,USE_CASES,SEARCH_CONTEXT_KEYS,STATUSES,REVIEW_ACTIONS");
        queryParam.put("context","MAKER");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie","xsgvaxhjqgcjqgw");

        Response getallResourcesResponse = makerCheckerMiddlewareServices.reviewPanelGetAllResourcesResponse(reviewPanelGetAllResources,headers,queryParam);

        int statusCode = getallResourcesResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }

}
