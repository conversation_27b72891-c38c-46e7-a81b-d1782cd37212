package OCL.MakerChecker.ReviewService;

import Request.MakerChecker.ReviewService.FetchRejectionReasons;
import Request.MakerChecker.ReviewService.GetReviewCaseDetails;
import Services.MakerChecker.MakerCheckerMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class GetReviewCaseDetailsTest extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(GetReviewCaseDetailsTest.class);
    MakerCheckerMiddlewareServices makerCheckerMiddlewareServices = new MakerCheckerMiddlewareServices();
    String url = "https://oe-staging5.paytm.com/checker/search";
    //String cookieVal= makerCheckerMiddlewareServices.getBossSessionToken("<EMAIL>","paytm@123");
    String cookieVal = "BOSS_SESSION=a24f25a1-0b1b-4f13-a635-181879ed6063";
    @Test(priority = 0, description = "Fetch Case Details")
    public void TC_01_fetchCaseDetails(){
        GetReviewCaseDetails getReviewCaseDetails = new GetReviewCaseDetails(P.TESTDATA.get("GetReviewCaseDetailsRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","951");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Content-Type","application/json");
        headers.put("Cookie",cookieVal);

        Response GetReviewCaseDetailsResponse= makerCheckerMiddlewareServices.GetReviewCaseDetailsResponse(getReviewCaseDetails,headers,queryParam);
        int statusCode = GetReviewCaseDetailsResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "Fetch Case Details with null caseId")
    public void TC_02_fetchCaseDetails(){
        GetReviewCaseDetails getReviewCaseDetails = new GetReviewCaseDetails(P.TESTDATA.get("GetReviewCaseDetailsRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Content-Type","application/json");
        headers.put("Cookie",cookieVal);

        Response GetReviewCaseDetailsResponse= makerCheckerMiddlewareServices.GetReviewCaseDetailsResponse(getReviewCaseDetails,headers,queryParam);
        int statusCode = GetReviewCaseDetailsResponse.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 0, description = "null params")
    public void TC_03_fetchCaseDetails(){
        GetReviewCaseDetails getReviewCaseDetails = new GetReviewCaseDetails(P.TESTDATA.get("GetReviewCaseDetailsRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Content-Type","application/json");
        headers.put("Cookie",cookieVal);

        Response GetReviewCaseDetailsResponse= makerCheckerMiddlewareServices.GetReviewCaseDetailsResponse(getReviewCaseDetails,headers,queryParam);
        int statusCode = GetReviewCaseDetailsResponse.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 0, description = "invalid caseId")
    public void TC_04_fetchCaseDetails(){
        GetReviewCaseDetails getReviewCaseDetails = new GetReviewCaseDetails(P.TESTDATA.get("GetReviewCaseDetailsRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","1056782233");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Content-Type","application/json");
        headers.put("Cookie",cookieVal);

        Response GetReviewCaseDetailsResponse= makerCheckerMiddlewareServices.GetReviewCaseDetailsResponse(getReviewCaseDetails,headers,queryParam);
        int statusCode = GetReviewCaseDetailsResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 0, description = "invalid oe-origin")
    public void TC_06_fetchCaseDetails(){
        GetReviewCaseDetails getReviewCaseDetails = new GetReviewCaseDetails(P.TESTDATA.get("GetReviewCaseDetailsRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","951");
        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","RU_PANEL");
        headers.put("oe-origin-url",url);
        headers.put("Content-Type","application/json");
        headers.put("Cookie",cookieVal);

        Response GetReviewCaseDetailsResponse= makerCheckerMiddlewareServices.GetReviewCaseDetailsResponse(getReviewCaseDetails,headers,queryParam);
        int statusCode = GetReviewCaseDetailsResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 0, description = "context = MAKER")
    public void TC_07_fetchCaseDetails(){
        GetReviewCaseDetails getReviewCaseDetails = new GetReviewCaseDetails(P.TESTDATA.get("GetReviewCaseDetailsRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","951");
        queryParam.put("context","MAKER");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Content-Type","application/json");
        headers.put("Cookie",cookieVal);

        Response GetReviewCaseDetailsResponse= makerCheckerMiddlewareServices.GetReviewCaseDetailsResponse(getReviewCaseDetails,headers,queryParam);
        int statusCode = GetReviewCaseDetailsResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "context = CHECKER")
    public void TC_08_fetchCaseDetails(){
        GetReviewCaseDetails getReviewCaseDetails = new GetReviewCaseDetails(P.TESTDATA.get("GetReviewCaseDetailsRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","952");
        queryParam.put("context","CHECKER");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Content-Type","application/json");
        headers.put("Cookie",cookieVal);

        Response GetReviewCaseDetailsResponse= makerCheckerMiddlewareServices.GetReviewCaseDetailsResponse(getReviewCaseDetails,headers,queryParam);
        int statusCode = GetReviewCaseDetailsResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
}
