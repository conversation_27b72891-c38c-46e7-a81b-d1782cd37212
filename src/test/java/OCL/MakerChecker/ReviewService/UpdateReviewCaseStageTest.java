package OCL.MakerChecker.ReviewService;

import Request.MakerChecker.ReviewService.FetchRejectionReasons;
import Request.MakerChecker.ReviewService.UpdateReviewCaseStage;
import Services.MakerChecker.MakerCheckerMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class UpdateReviewCaseStageTest extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(FetchRejectionReasonsTest.class);
    MakerCheckerMiddlewareServices makerCheckerMiddlewareServices = new MakerCheckerMiddlewareServices();
    String jwt = MakerCheckerMiddlewareServices.generateJwtTokenForReviewService();
    @Test(priority = 0, description = "Update Reviewcase Stage")
    public void TC_01_updateReviewCaseStage(){
        UpdateReviewCaseStage updateReviewCaseStage = new UpdateReviewCaseStage(P.TESTDATA.get("UpdateReviewCaseStageRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","4347");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName", "VishalG");
        body.put("subReason", "");
        body.put("reason", "MHD - Unhold basis KAM request");
        body.put("currentStatus", "FREEZE");
        body.put("freezeReason","Risk");
        body.put("freezeSubReason", "Suspicious GMV");
        body.put("location", "Kolkata");
        body.put("remarks", "xyz");
        body.put("keys", "DM1144255171039");
        body.put("makerEmail", "<EMAIL>");
        body.put("isEligibleForHistoricData", "false");
        body.put("boss_request_id", "1444");
        body.put("merchantSolutionType", "ONLINE");
        body.put("makerId", "1700007197");
        body.put("workFlowExecutionId", "4048");
        body.put("stage", "L2");
        body.put("makerCustId", "1700007197");
        body.put("makerEmail", "<EMAIL>");
        body.put("mid","qa8mid97505444272048");
        body.put("contextValue","qa8mid97505444272048");
        body.put("source","BOSS_PANEL");

        Response UpdateReviewCaseStageResponse = makerCheckerMiddlewareServices.UpdateReviewCaseStageResponse(updateReviewCaseStage,headers,queryParam,body);
        int statusCode = UpdateReviewCaseStageResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "Update Reviewcase Stage - invalid case Id")
    public void TC_02_updateReviewCaseStage(){
        UpdateReviewCaseStage updateReviewCaseStage = new UpdateReviewCaseStage(P.TESTDATA.get("UpdateReviewCaseStageRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","4126");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName", "VishalG");
        body.put("subReason", "");
        body.put("reason", "MHD - Unhold basis KAM request");
        body.put("currentStatus", "FREEZE");
        body.put("freezeReason","Risk");
        body.put("freezeSubReason", "Suspicious GMV");
        body.put("location", "Kolkata");
        body.put("remarks", "xyz");
        body.put("keys", "DM1144255171039");
        body.put("makerEmail", "<EMAIL>");
        body.put("isEligibleForHistoricData", "false");
        body.put("boss_request_id", "1444");
        body.put("merchantSolutionType", "ONLINE");
        body.put("makerId", "1700007197");
        body.put("workFlowExecutionId", "4048");
        body.put("stage", "L2");
        body.put("makerCustId", "1700007197");
        body.put("makerEmail", "<EMAIL>");
        body.put("mid","qa8mid97505444272048");
        body.put("contextValue","qa8mid97505444272048");
        body.put("source","BOSS_PANEL");

        Response UpdateReviewCaseStageResponse = makerCheckerMiddlewareServices.UpdateReviewCaseStageResponse(updateReviewCaseStage,headers,queryParam,body);
        int statusCode = UpdateReviewCaseStageResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 0, description = "Update Reviewcase Stage - null case Id")
    public void TC_03_updateReviewCaseStage(){
        UpdateReviewCaseStage updateReviewCaseStage = new UpdateReviewCaseStage(P.TESTDATA.get("UpdateReviewCaseStageRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName", "VishalG");
        body.put("subReason", "");
        body.put("reason", "MHD - Unhold basis KAM request");
        body.put("currentStatus", "FREEZE");
        body.put("freezeReason","Risk");
        body.put("freezeSubReason", "Suspicious GMV");
        body.put("location", "Kolkata");
        body.put("remarks", "xyz");
        body.put("keys", "DM1144255171039");
        body.put("makerEmail", "<EMAIL>");
        body.put("isEligibleForHistoricData", "false");
        body.put("boss_request_id", "1444");
        body.put("merchantSolutionType", "ONLINE");
        body.put("makerId", "1700007197");
        body.put("workFlowExecutionId", "4048");
        body.put("stage", "");
        body.put("makerCustId", "1700007197");
        body.put("makerEmail", "<EMAIL>");
        body.put("mid","qa8mid97505444272048");
        body.put("contextValue","qa8mid97505444272048");
        body.put("source","BOSS_PANEL");

        Response UpdateReviewCaseStageResponse = makerCheckerMiddlewareServices.UpdateReviewCaseStageResponse(updateReviewCaseStage,headers,queryParam,body);
        int statusCode = UpdateReviewCaseStageResponse.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 0, description = "Update Reviewcase Stage - null stage")
    public void TC_04_updateReviewCaseStage(){
        UpdateReviewCaseStage updateReviewCaseStage = new UpdateReviewCaseStage(P.TESTDATA.get("UpdateReviewCaseStageRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","4224");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName", "VishalG");
        body.put("subReason", "");
        body.put("reason", "MHD - Unhold basis KAM request");
        body.put("currentStatus", "FREEZE");
        body.put("freezeReason","Risk");
        body.put("freezeSubReason", "Suspicious GMV");
        body.put("location", "Kolkata");
        body.put("remarks", "xyz");
        body.put("keys", "DM1144255171039");
        body.put("makerEmail", "<EMAIL>");
        body.put("isEligibleForHistoricData", "false");
        body.put("boss_request_id", "1444");
        body.put("merchantSolutionType", "ONLINE");
        body.put("makerId", "1700007197");
        body.put("workFlowExecutionId", "4048");
        body.put("makerCustId", "1700007197");
        body.put("makerEmail", "<EMAIL>");
        body.put("mid","qa8mid97505444272048");
        body.put("contextValue","qa8mid97505444272048");
        body.put("source","BOSS_PANEL");

        Response UpdateReviewCaseStageResponse = makerCheckerMiddlewareServices.UpdateReviewCaseStageResponse(updateReviewCaseStage,headers,queryParam,body);
        int statusCode = UpdateReviewCaseStageResponse.getStatusCode();
        Assert.assertEquals(statusCode, 412);
    }
    @Test(priority = 0, description = "Update Reviewcase Stage - invalid stage")
    public void TC_05_updateReviewCaseStage(){
        UpdateReviewCaseStage updateReviewCaseStage = new UpdateReviewCaseStage(P.TESTDATA.get("UpdateReviewCaseStageRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","4224");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName", "VishalG");
        body.put("subReason", "");
        body.put("reason", "MHD - Unhold basis KAM request");
        body.put("currentStatus", "FREEZE");
        body.put("freezeReason","Risk");
        body.put("freezeSubReason", "Suspicious GMV");
        body.put("location", "Kolkata");
        body.put("remarks", "xyz");
        body.put("keys", "DM1144255171039");
        body.put("makerEmail", "<EMAIL>");
        body.put("isEligibleForHistoricData", "false");
        body.put("boss_request_id", "1444");
        body.put("stage", "L8");
        body.put("merchantSolutionType", "ONLINE");
        body.put("makerId", "1700007197");
        body.put("workFlowExecutionId", "4048");
        body.put("makerCustId", "1700007197");
        body.put("makerEmail", "<EMAIL>");
        body.put("mid","qa8mid97505444272048");
        body.put("contextValue","qa8mid97505444272048");
        body.put("source","BOSS_PANEL");

        Response UpdateReviewCaseStageResponse = makerCheckerMiddlewareServices.UpdateReviewCaseStageResponse(updateReviewCaseStage,headers,queryParam,body);
        int statusCode = UpdateReviewCaseStageResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 0, description = "Update Reviewcase Stage - invalid stage")
    public void TC_06_updateReviewCaseStage(){
        UpdateReviewCaseStage updateReviewCaseStage = new UpdateReviewCaseStage(P.TESTDATA.get("UpdateReviewCaseStageRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","4224");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName", "VishalG");
        body.put("subReason", "");
        body.put("reason", "MHD - Unhold basis KAM request");
        body.put("currentStatus", "FREEZE");
        body.put("freezeReason","Risk");
        body.put("freezeSubReason", "Suspicious GMV");
        body.put("location", "Kolkata");
        body.put("remarks", "xyz");
        body.put("keys", "DM1144255171039");
        body.put("makerEmail", "<EMAIL>");
        body.put("isEligibleForHistoricData", "false");
        body.put("boss_request_id", "1444");
        body.put("stage", "L8");
        body.put("merchantSolutionType", "ONLINE");
        body.put("makerId", "1700007197");
        body.put("workFlowExecutionId", "4048");
        body.put("makerCustId", "1700007197");
        body.put("makerEmail", "<EMAIL>");
        body.put("mid","qa8mid97505444272048");
        body.put("contextValue","qa8mid97505444272048");
        body.put("source","BOSS_PANEL");

        Response UpdateReviewCaseStageResponse = makerCheckerMiddlewareServices.UpdateReviewCaseStageResponse(updateReviewCaseStage,headers,queryParam,body);
        int statusCode = UpdateReviewCaseStageResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 0, description = "Update Reviewcase Stage - case with wrong merchant soultion type")
    public void TC_07_updateReviewCaseStage(){
        UpdateReviewCaseStage updateReviewCaseStage = new UpdateReviewCaseStage(P.TESTDATA.get("UpdateReviewCaseStageRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","3769");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName", "VishalG");
        body.put("subReason", "");
        body.put("reason", "MHD - Unhold basis KAM request");
        body.put("currentStatus", "FREEZE");
        body.put("freezeReason","Risk");
        body.put("freezeSubReason", "Suspicious GMV");
        body.put("location", "Kolkata");
        body.put("remarks", "xyz");
        body.put("keys", "DM1144255171039");
        body.put("makerEmail", "<EMAIL>");
        body.put("isEligibleForHistoricData", "false");
        body.put("boss_request_id", "1444");
        body.put("stage", "L2");
        body.put("merchantSolutionType", "ONLINE");
        body.put("makerId", "1700007197");
        body.put("workFlowExecutionId", "4048");
        body.put("makerCustId", "1700007197");
        body.put("makerEmail", "<EMAIL>");
        body.put("mid","qa8mid97505444272048");
        body.put("contextValue","qa8mid97505444272048");
        body.put("source","BOSS_PANEL");

        Response UpdateReviewCaseStageResponse = makerCheckerMiddlewareServices.UpdateReviewCaseStageResponse(updateReviewCaseStage,headers,queryParam,body);
        int statusCode = UpdateReviewCaseStageResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 0, description = "Update Reviewcase Stage - invalid jwt")
    public void TC_08_updateReviewCaseStage(){
        UpdateReviewCaseStage updateReviewCaseStage = new UpdateReviewCaseStage(P.TESTDATA.get("UpdateReviewCaseStageRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","3769");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt","hgsahdgjgduqtydrsyadfghsavbdmsanbdc");

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName", "VishalG");
        body.put("subReason", "");
        body.put("reason", "MHD - Unhold basis KAM request");
        body.put("currentStatus", "FREEZE");
        body.put("freezeReason","Risk");
        body.put("freezeSubReason", "Suspicious GMV");
        body.put("location", "Kolkata");
        body.put("remarks", "xyz");
        body.put("keys", "DM1144255171039");
        body.put("makerEmail", "<EMAIL>");
        body.put("isEligibleForHistoricData", "false");
        body.put("boss_request_id", "1444");
        body.put("stage", "L2");
        body.put("merchantSolutionType", "ONLINE");
        body.put("makerId", "1700007197");
        body.put("workFlowExecutionId", "4048");
        body.put("makerCustId", "1700007197");
        body.put("makerEmail", "<EMAIL>");
        body.put("mid","qa8mid97505444272048");
        body.put("contextValue","qa8mid97505444272048");
        body.put("source","BOSS_PANEL");

        Response UpdateReviewCaseStageResponse = makerCheckerMiddlewareServices.UpdateReviewCaseStageResponse(updateReviewCaseStage,headers,queryParam,body);
        int statusCode = UpdateReviewCaseStageResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 0, description = "Update Reviewcase Stage - stage move to L3")
    public void TC_09_updateReviewCaseStage(){
        UpdateReviewCaseStage updateReviewCaseStage = new UpdateReviewCaseStage(P.TESTDATA.get("UpdateReviewCaseStageRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","4347");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt","hgsahdgjgduqtydrsyadfghsavbdmsanbdc");

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName", "VishalG");
        body.put("subReason", "");
        body.put("reason", "MHD - Unhold basis KAM request");
        body.put("currentStatus", "FREEZE");
        body.put("freezeReason","Risk");
        body.put("freezeSubReason", "Suspicious GMV");
        body.put("location", "Kolkata");
        body.put("remarks", "xyz");
        body.put("keys", "DM1144255171039");
        body.put("makerEmail", "<EMAIL>");
        body.put("isEligibleForHistoricData", "false");
        body.put("boss_request_id", "1444");
        body.put("stage", "L3");
        body.put("merchantSolutionType", "ONLINE");
        body.put("makerId", "1700007197");
        body.put("workFlowExecutionId", "4048");
        body.put("makerCustId", "1700007197");
        body.put("makerEmail", "<EMAIL>");
        body.put("mid","qa8mid97505444272048");
        body.put("contextValue","qa8mid97505444272048");
        body.put("source","BOSS_PANEL");

        Response UpdateReviewCaseStageResponse = makerCheckerMiddlewareServices.UpdateReviewCaseStageResponse(updateReviewCaseStage,headers,queryParam,body);
        int statusCode = UpdateReviewCaseStageResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 0, description = "Update Reviewcase Stage - restrict stage movement in case of null maker Id")
    public void TC_10_updateReviewCaseStage(){
        UpdateReviewCaseStage updateReviewCaseStage = new UpdateReviewCaseStage(P.TESTDATA.get("UpdateReviewCaseStageRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","4347");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName", "VishalG");
        body.put("subReason", "");
        body.put("reason", "MHD - Unhold basis KAM request");
        body.put("currentStatus", "FREEZE");
        body.put("freezeReason","Risk");
        body.put("freezeSubReason", "Suspicious GMV");
        body.put("location", "Kolkata");
        body.put("remarks", "xyz");
        body.put("keys", "DM1144255171039");
        body.put("makerEmail", "<EMAIL>");
        body.put("isEligibleForHistoricData", "false");
        body.put("boss_request_id", "1444");
        body.put("stage", "L3");
        body.put("merchantSolutionType", "ONLINE");
        body.put("makerId", "");
        body.put("workFlowExecutionId", "4048");
        body.put("makerCustId", "1700007197");
        body.put("makerEmail", "<EMAIL>");
        body.put("mid","qa8mid97505444272048");
        body.put("contextValue","qa8mid97505444272048");
        body.put("source","BOSS_PANEL");

        Response UpdateReviewCaseStageResponse = makerCheckerMiddlewareServices.UpdateReviewCaseStageResponse(updateReviewCaseStage,headers,queryParam,body);
        int statusCode = UpdateReviewCaseStageResponse.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 0, description = "Update Reviewcase Stage - restrict stage movement in case of missing maker Id")
    public void TC_11_updateReviewCaseStage(){
        UpdateReviewCaseStage updateReviewCaseStage = new UpdateReviewCaseStage(P.TESTDATA.get("UpdateReviewCaseStageRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","4347");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName", "VishalG");
        body.put("subReason", "");
        body.put("reason", "MHD - Unhold basis KAM request");
        body.put("currentStatus", "FREEZE");
        body.put("freezeReason","Risk");
        body.put("freezeSubReason", "Suspicious GMV");
        body.put("location", "Kolkata");
        body.put("remarks", "xyz");
        body.put("keys", "DM1144255171039");
        body.put("makerEmail", "<EMAIL>");
        body.put("isEligibleForHistoricData", "false");
        body.put("boss_request_id", "1444");
        body.put("stage", "L3");
        body.put("merchantSolutionType", "ONLINE");
        body.put("makerId", "");
        body.put("workFlowExecutionId", "4048");
        body.put("makerCustId", "1700007197");
        body.put("makerEmail", "<EMAIL>");
        body.put("mid","qa8mid97505444272048");
        body.put("contextValue","qa8mid97505444272048");
        body.put("source","BOSS_PANEL");

        Response UpdateReviewCaseStageResponse = makerCheckerMiddlewareServices.UpdateReviewCaseStageResponse(updateReviewCaseStage,headers,queryParam,body);
        int statusCode = UpdateReviewCaseStageResponse.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 0, description = "Update Reviewcase Stage - approved request updation")
    public void TC_12_updateReviewCaseStage(){
        UpdateReviewCaseStage updateReviewCaseStage = new UpdateReviewCaseStage(P.TESTDATA.get("UpdateReviewCaseStageRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","4362");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName", "VishalG");
        body.put("subReason", "");
        body.put("reason", "MHD - Unhold basis KAM request");
        body.put("currentStatus", "FREEZE");
        body.put("freezeReason","Risk");
        body.put("freezeSubReason", "Suspicious GMV");
        body.put("location", "Kolkata");
        body.put("remarks", "xyz");
        body.put("keys", "DM1144255171039");
        body.put("makerEmail", "<EMAIL>");
        body.put("isEligibleForHistoricData", "false");
        body.put("boss_request_id", "1444");
        body.put("stage", "L3");
        body.put("merchantSolutionType", "ONLINE");
        body.put("makerId", "1700007197");
        body.put("workFlowExecutionId", "4048");
        body.put("makerCustId", "1700007197");
        body.put("makerEmail", "<EMAIL>");
        body.put("mid","qa8mid97505444272048");
        body.put("contextValue","qa8mid97505444272048");
        body.put("source","BOSS_PANEL");

        Response UpdateReviewCaseStageResponse = makerCheckerMiddlewareServices.UpdateReviewCaseStageResponse(updateReviewCaseStage,headers,queryParam,body);
        int statusCode = UpdateReviewCaseStageResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "Update Reviewcase Stage - null params")
    public void TC_13_updateReviewCaseStage(){
        UpdateReviewCaseStage updateReviewCaseStage = new UpdateReviewCaseStage(P.TESTDATA.get("UpdateReviewCaseStageRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName", "VishalG");
        body.put("subReason", "");
        body.put("reason", "MHD - Unhold basis KAM request");
        body.put("currentStatus", "FREEZE");
        body.put("freezeReason","Risk");
        body.put("freezeSubReason", "Suspicious GMV");
        body.put("location", "Kolkata");
        body.put("remarks", "xyz");
        body.put("keys", "DM1144255171039");
        body.put("makerEmail", "<EMAIL>");
        body.put("isEligibleForHistoricData", "false");
        body.put("boss_request_id", "1444");
        body.put("stage", "L3");
        body.put("merchantSolutionType", "ONLINE");
        body.put("makerId", "1700007197");
        body.put("workFlowExecutionId", "4048");
        body.put("makerCustId", "1700007197");
        body.put("makerEmail", "<EMAIL>");
        body.put("mid","qa8mid97505444272048");
        body.put("contextValue","qa8mid97505444272048");
        body.put("source","BOSS_PANEL");

        Response UpdateReviewCaseStageResponse = makerCheckerMiddlewareServices.UpdateReviewCaseStageResponse(updateReviewCaseStage,headers,queryParam,body);
        int statusCode = UpdateReviewCaseStageResponse.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 0, description = "Update Reviewcase Stage - empty feilds")
    public void TC_14_updateReviewCaseStage(){
        UpdateReviewCaseStage updateReviewCaseStage = new UpdateReviewCaseStage(P.TESTDATA.get("UpdateReviewCaseStageRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseId","4362");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();

        Response UpdateReviewCaseStageResponse = makerCheckerMiddlewareServices.UpdateReviewCaseStageResponse(updateReviewCaseStage,headers,queryParam,body);
        int statusCode = UpdateReviewCaseStageResponse.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
}

