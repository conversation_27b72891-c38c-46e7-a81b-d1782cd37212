package OCL.MakerChecker.ReviewService;

import Request.MakerChecker.ReviewService.ApproveRejectCase;
import Request.MakerChecker.ReviewService.FetchReviewStatusCount;
import Services.MakerChecker.MakerCheckerMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FetchReviewStatusCountTest extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(FetchReviewStatusCountTest.class);
    MakerCheckerMiddlewareServices makerCheckerMiddlewareServices = new MakerCheckerMiddlewareServices();
    String Cookieval = "BOSS_SESSION=699d537f-0102-42fc-b37d-621071d45697";
    String url = "https://oe-staging14.paytm.com/checker/search";
    @Test(priority = 0, description = "Fetch status count for logged in user")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_FetchReviewStatusCount() {

        FetchReviewStatusCount fetchReviewStatusCount = new FetchReviewStatusCount(P.TESTDATA.get("FetchReviewStatusCountRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("context","CHECKER");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Oe-Origin","BOSS");
        headers.put("Oe-Origin-Url",url);
        headers.put("Cookie",Cookieval);

        Response FetchReviewStatusCountResponse = makerCheckerMiddlewareServices.FetchReviewStatusCountResponse(fetchReviewStatusCount,headers,queryParam);

        int statusCode = FetchReviewStatusCountResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }
    @Test(priority = 0, description = "Fetch status count for logged in user - MAKER")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_FetchReviewStatusCount() {

        FetchReviewStatusCount fetchReviewStatusCount = new FetchReviewStatusCount(P.TESTDATA.get("FetchReviewStatusCountRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("context","MAKER");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Oe-Origin","BOSS");
        headers.put("Oe-Origin-Url",url);
        headers.put("Cookie",Cookieval);

        Response FetchReviewStatusCountResponse = makerCheckerMiddlewareServices.FetchReviewStatusCountResponse(fetchReviewStatusCount,headers,queryParam);

        int statusCode = FetchReviewStatusCountResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }
    @Test(priority = 0, description = "Fetch status count for logged in user - with null context")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_FetchReviewStatusCount() {

        FetchReviewStatusCount fetchReviewStatusCount = new FetchReviewStatusCount(P.TESTDATA.get("FetchReviewStatusCountRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("context","CHECKER");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Oe-Origin","BOSS");
        headers.put("Oe-Origin-Url",url);
        headers.put("Cookie",Cookieval);

        Response FetchReviewStatusCountResponse = makerCheckerMiddlewareServices.FetchReviewStatusCountResponse(fetchReviewStatusCount,headers,queryParam);

        int statusCode = FetchReviewStatusCountResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }
    @Test(priority = 0, description = "Fetch status count for logged in user - with invalid Oe-origin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_FetchReviewStatusCount() {

        FetchReviewStatusCount fetchReviewStatusCount = new FetchReviewStatusCount(P.TESTDATA.get("FetchReviewStatusCountRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("context","CHECKER");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Oe-Origin","wqvxjhqwexgjq");
        headers.put("Oe-Origin-Url",url);
        headers.put("Cookie",Cookieval);

        Response FetchReviewStatusCountResponse = makerCheckerMiddlewareServices.FetchReviewStatusCountResponse(fetchReviewStatusCount,headers,queryParam);

        int statusCode = FetchReviewStatusCountResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401);

    }
    @Test(priority = 0, description = "Fetch status count for logged in user - with null Oe-origin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_FetchReviewStatusCount() {

        FetchReviewStatusCount fetchReviewStatusCount = new FetchReviewStatusCount(P.TESTDATA.get("FetchReviewStatusCountRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("context","CHECKER");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Oe-Origin","");
        headers.put("Oe-Origin-Url",url);
        headers.put("Cookie",Cookieval);

        Response FetchReviewStatusCountResponse = makerCheckerMiddlewareServices.FetchReviewStatusCountResponse(fetchReviewStatusCount,headers,queryParam);

        int statusCode = FetchReviewStatusCountResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 0, description = "Fetch status count for logged in user - with null Params")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_FetchReviewStatusCount() {

        FetchReviewStatusCount fetchReviewStatusCount = new FetchReviewStatusCount(P.TESTDATA.get("FetchReviewStatusCountRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Oe-Origin","");
        headers.put("Oe-Origin-Url",url);
        headers.put("Cookie",Cookieval);

        Response FetchReviewStatusCountResponse = makerCheckerMiddlewareServices.FetchReviewStatusCountResponse(fetchReviewStatusCount,headers,queryParam);

        int statusCode = FetchReviewStatusCountResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 0, description = "Fetch status count for logged in user - with invalid url")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_FetchReviewStatusCount() {

        FetchReviewStatusCount fetchReviewStatusCount = new FetchReviewStatusCount(P.TESTDATA.get("FetchReviewStatusCountRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Oe-Origin","BOSS");
        headers.put("Oe-Origin-Url","https://oe-staging5.paytm.com/checker/search");
        headers.put("Cookie",Cookieval);

        Response FetchReviewStatusCountResponse = makerCheckerMiddlewareServices.FetchReviewStatusCountResponse(fetchReviewStatusCount,headers,queryParam);

        int statusCode = FetchReviewStatusCountResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 0, description = "Fetch status count for logged in user - with invalid boss session")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_FetchReviewStatusCount() {

        FetchReviewStatusCount fetchReviewStatusCount = new FetchReviewStatusCount(P.TESTDATA.get("FetchReviewStatusCountRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Oe-Origin","BOSS");
        headers.put("Oe-Origin-Url",url);
        headers.put("Cookie","vadhjcsbjahhbsjck");

        Response FetchReviewStatusCountResponse = makerCheckerMiddlewareServices.FetchReviewStatusCountResponse(fetchReviewStatusCount,headers,queryParam);

        int statusCode = FetchReviewStatusCountResponse.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 0, description = "Fetch status count for logged in user - with null boss session")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_FetchReviewStatusCount() {

        FetchReviewStatusCount fetchReviewStatusCount = new FetchReviewStatusCount(P.TESTDATA.get("FetchReviewStatusCountRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Oe-Origin","BOSS");
        headers.put("Oe-Origin-Url",url);

        Response FetchReviewStatusCountResponse = makerCheckerMiddlewareServices.FetchReviewStatusCountResponse(fetchReviewStatusCount,headers,queryParam);

        int statusCode = FetchReviewStatusCountResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 0, description = "Fetch status count for logged in user - with null Oe-origin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_FetchReviewStatusCount() {

        FetchReviewStatusCount fetchReviewStatusCount = new FetchReviewStatusCount(P.TESTDATA.get("FetchReviewStatusCountRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Oe-Origin-Url",url);
        headers.put("Cookie",Cookieval);

        Response FetchReviewStatusCountResponse = makerCheckerMiddlewareServices.FetchReviewStatusCountResponse(fetchReviewStatusCount,headers,queryParam);

        int statusCode = FetchReviewStatusCountResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 0, description = "Fetch status count for logged in user - with null Oe-origin-url")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11_FetchReviewStatusCount() {

        FetchReviewStatusCount fetchReviewStatusCount = new FetchReviewStatusCount(P.TESTDATA.get("FetchReviewStatusCountRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Oe-origin","BOSS");
        headers.put("Cookie",Cookieval);

        Response FetchReviewStatusCountResponse = makerCheckerMiddlewareServices.FetchReviewStatusCountResponse(fetchReviewStatusCount,headers,queryParam);

        int statusCode = FetchReviewStatusCountResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 0, description = "Fetch status count for logged in user - TL status count")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12_FetchReviewStatusCount() {

        FetchReviewStatusCount fetchReviewStatusCount = new FetchReviewStatusCount(P.TESTDATA.get("FetchReviewStatusCountRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Oe-origin","BOSS");
        headers.put("Oe-Origin-Url",url);

        headers.put("Cookie","BOSS_SESSION=1a69c588-b7b9-4b53-acfe-0a0efcb5b3b1");

        Response FetchReviewStatusCountResponse = makerCheckerMiddlewareServices.FetchReviewStatusCountResponse(fetchReviewStatusCount,headers,queryParam);

        int statusCode = FetchReviewStatusCountResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
}


