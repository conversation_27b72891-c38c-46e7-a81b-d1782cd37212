package OCL.MakerChecker.ReviewService;

import OCL.MakerChecker.WorkflowEngine.CreateWorkflowTest;
import Request.MakerChecker.ReviewService.ApproveRejectCase;
import Request.MakerChecker.WorkflowEngine.CreateWorkflow;
import Services.MakerChecker.MakerCheckerMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class ApproveRejectCaseTest extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(ApproveRejectCaseTest.class);
    MakerCheckerMiddlewareServices makerCheckerMiddlewareServices = new MakerCheckerMiddlewareServices();
    //String cookieVal= makerCheckerMiddlewareServices.getBossSessionToken("<EMAIL>","paytm@123");
    String cookieVal="BOSS_SESSION=ffb385fe-8af7-4609-a79e-b96a1301b71b";
    String url = "https://oe-staging5.paytm.com/checker/search";

    @Test(priority = 0, description = "approve cases")
    public void TC_01_approve_reject()
    {
        ApproveRejectCase approveRejectCase = new ApproveRejectCase(P.TESTDATA.get("ApproveRejectCaseRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */

        Map<String, String> body = new HashMap<String, String>();

        body.put("caseId","1848");
        body.put("stageId","2171");
        body.put("action","APPROVE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Cookie",cookieVal);
        headers.put("Oe-Origin","BOSS");
        headers.put("Oe-Origin-Url",url);
        headers.put("Content-Type","application/json");


        Response approveRejectCaseResponse = makerCheckerMiddlewareServices.approveRejectCaseResponse(approveRejectCase,headers,body);

        int statusCode = approveRejectCaseResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }
    @Test(priority = 0, description = "null caseId")
    public void TC_02_approve_reject()
    {
        ApproveRejectCase approveRejectCase = new ApproveRejectCase(P.TESTDATA.get("ApproveRejectCaseRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */

        Map<String, String> body = new HashMap<String, String>();

        body.put("caseId","");
        body.put("stageId","2179");
        body.put("action","APPROVE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Cookie",cookieVal);
        headers.put("Oe-Origin","BOSS");
        headers.put("Oe-Origin-Url",url);
        headers.put("Content-Type","application/json");


        Response approveRejectCaseResponse = makerCheckerMiddlewareServices.approveRejectCaseResponse(approveRejectCase,headers,body);

        int statusCode = approveRejectCaseResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);

    }
    @Test(priority = 0, description = "null stageId")
    public void TC_03_approve_reject()
    {
        ApproveRejectCase approveRejectCase = new ApproveRejectCase(P.TESTDATA.get("ApproveRejectCaseRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */

        Map<String, String> body = new HashMap<String, String>();

        body.put("caseId","1856");
        body.put("stageId","");
        body.put("action","APPROVE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Cookie",cookieVal);
        headers.put("Oe-Origin","BOSS");
        headers.put("Oe-Origin-Url",url);
        headers.put("Content-Type","application/json");


        Response approveRejectCaseResponse = makerCheckerMiddlewareServices.approveRejectCaseResponse(approveRejectCase,headers,body);

        int statusCode = approveRejectCaseResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 0, description = "invalid caseId")
    public void TC_05_approve_reject()
    {
        ApproveRejectCase approveRejectCase = new ApproveRejectCase(P.TESTDATA.get("ApproveRejectCaseRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */

        Map<String, String> body = new HashMap<String, String>();

        body.put("caseId","2456");
        body.put("stageId","1234");
        body.put("action","APPROVE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Cookie",cookieVal);
        headers.put("Oe-Origin","BOSS");
        headers.put("Oe-Origin-Url",url);
        headers.put("Content-Type","application/json");


        Response approveRejectCaseResponse = makerCheckerMiddlewareServices.approveRejectCaseResponse(approveRejectCase,headers,body);

        int statusCode = approveRejectCaseResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 0, description = "invalid stageId")
    public void TC_06_approve_reject()
    {
        ApproveRejectCase approveRejectCase = new ApproveRejectCase(P.TESTDATA.get("ApproveRejectCaseRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */

        Map<String, String> body = new HashMap<String, String>();

        body.put("caseId","5321");
        body.put("stageId","1324");
        body.put("action","APPROVE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Cookie",cookieVal);
        headers.put("Oe-Origin","BOSS");
        headers.put("Oe-Origin-Url",url);
        headers.put("Content-Type","application/json");


        Response approveRejectCaseResponse = makerCheckerMiddlewareServices.approveRejectCaseResponse(approveRejectCase,headers,body);

        int statusCode = approveRejectCaseResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 0, description = "invalid session token")
    public void TC_07_approve_reject()
    {
        ApproveRejectCase approveRejectCase = new ApproveRejectCase(P.TESTDATA.get("ApproveRejectCaseRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */

        Map<String, String> body = new HashMap<String, String>();

        body.put("caseId","1234");
        body.put("stageId","3456");
        body.put("action","APPROVE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Cookie","BOSS_SESSION=be2c1c63-6519-475d-a16e-e5bd51591c1d");
        headers.put("Oe-Origin","BOSS");
        headers.put("Oe-Origin-Url",url);
        headers.put("Content-Type","application/json");


        Response approveRejectCaseResponse = makerCheckerMiddlewareServices.approveRejectCaseResponse(approveRejectCase,headers,body);

        int statusCode = approveRejectCaseResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
    @Test(priority = 0, description = "Bulk Approve")
    public void TC_08_approve_reject()
    {
        ApproveRejectCase approveRejectCase = new ApproveRejectCase(P.TESTDATA.get("ApproveRejectCaseRequest"));
        /* --all the param,headers,body should be passed in hashmap-- */

        Map<String, String> body = new HashMap<String, String>();

        body.put("caseId","4164");
        body.put("stageId","4511");
        body.put("caseId","4165");
        body.put("stageId","4512");
        body.put("action","APPROVE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Cookie",cookieVal);
        headers.put("Oe-Origin","BOSS");
        headers.put("Oe-Origin-Url",url);
        headers.put("Content-Type","application/json");


        Response approveRejectCaseResponse = makerCheckerMiddlewareServices.approveRejectCaseResponse(approveRejectCase,headers,body);

        int statusCode = approveRejectCaseResponse.getStatusCode();
        Assert.assertEquals(statusCode, 424);
    }
}
