package OCL.MakerChecker.ReviewService;

import Request.MakerChecker.ReviewService.CreateReviewCase;
import Request.MakerChecker.ReviewService.FetchAllCases;
import Services.MakerChecker.MakerCheckerMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class CreateReviewCaseTest extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(CreateReviewCaseTest.class);
    MakerCheckerMiddlewareServices makerCheckerMiddlewareServices = new MakerCheckerMiddlewareServices();

    String jwt = MakerCheckerMiddlewareServices.generateJwtTokenForReviewService();
    @Test(priority = 0, description = "Create Review Case")
    public void TC_01_CreateCase(){
        CreateReviewCase createReviewCase = new CreateReviewCase(P.TESTDATA.get("CreateReviewCaseRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","BIN_DETAILS_UPDATE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type","application/json");
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName","Aadi");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","1107228639");
        body.put("boss_request_id","16");
        body.put("stage","L1");
        body.put("mid","fdhh");
        body.put("abc","1234");
        body.put("source","BOSS_PANEL");
        body.put("contextKey","MID");
        body.put("contextValue","Testing521");
        body.put("workFlowExecutionId","1567");

        Response CreateReviewCaseResponse = makerCheckerMiddlewareServices.CreateReviewCaseResponse(createReviewCase,headers,queryParam,body);

        int statusCode = CreateReviewCaseResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0, description = "null meta")
    public void TC_02_CreateCase(){
        CreateReviewCase createReviewCase = new CreateReviewCase(P.TESTDATA.get("CreateReviewCaseRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","BIN_DETAILS_UPDATE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type","application/json");
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName","Aadi");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","1107228639");
        body.put("boss_request_id","16");
        body.put("mid","fdhh");
        body.put("abc","1234");
        body.put("source","BOSS_PANEL");
        body.put("contextKey","MID");
        body.put("contextValue","Testing521");
        body.put("workFlowExecutionId","1567");
        Response CreateReviewCaseResponse = makerCheckerMiddlewareServices.CreateReviewCaseResponse(createReviewCase,headers,queryParam,body);

        int statusCode = CreateReviewCaseResponse.getStatusCode();
        Assert.assertEquals(statusCode, 412);
    }
    @Test(priority = 0, description = "null stage in meta")
    public void TC_03_CreateCase(){
        CreateReviewCase createReviewCase = new CreateReviewCase(P.TESTDATA.get("CreateReviewCaseRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","BIN_DETAILS_UPDATE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type","application/json");
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName","Aadi");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","1107228639");
        body.put("boss_request_id","16");
        body.put("stage","");
        body.put("mid","fdhh");
        body.put("abc","1234");
        body.put("source","BOSS_PANEL");
        body.put("contextKey","MID");
        body.put("contextValue","Testing521");
        body.put("workFlowExecutionId","1567");

        Response CreateReviewCaseResponse = makerCheckerMiddlewareServices.CreateReviewCaseResponse(createReviewCase,headers,queryParam,body);

        int statusCode = CreateReviewCaseResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 0, description = "without Use-Case")
    public void TC_04_CreateCase(){
        CreateReviewCase createReviewCase = new CreateReviewCase(P.TESTDATA.get("CreateReviewCaseRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type","application/json");
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName","Aadi");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","1107228639");
        body.put("boss_request_id","16");
        body.put("stage","L1");
        body.put("mid","fdhh");
        body.put("abc","1234");
        body.put("source","BOSS_PANEL");
        body.put("contextKey","MID");
        body.put("contextValue","Testing521");
        body.put("workFlowExecutionId","1567");

        Response CreateReviewCaseResponse = makerCheckerMiddlewareServices.CreateReviewCaseResponse(createReviewCase,headers,queryParam,body);

        int statusCode = CreateReviewCaseResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 0, description = "invalid use-Case")
    public void TC_05_CreateCase(){
        CreateReviewCase createReviewCase = new CreateReviewCase(P.TESTDATA.get("CreateReviewCaseRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","ahcvhja");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type","application/json");
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName","Aadi");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","1107228639");
        body.put("boss_request_id","16");
        body.put("stage","L1");
        body.put("mid","fdhh");
        body.put("abc","1234");
        body.put("source","BOSS_PANEL");
        body.put("contextKey","MID");
        body.put("contextValue","Testing521");
        body.put("workFlowExecutionId","1567");

        Response CreateReviewCaseResponse = makerCheckerMiddlewareServices.CreateReviewCaseResponse(createReviewCase,headers,queryParam,body);

        int statusCode = CreateReviewCaseResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 0, description = "without source")
    public void TC_06_CreateCase(){
        CreateReviewCase createReviewCase = new CreateReviewCase(P.TESTDATA.get("CreateReviewCaseRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","BIN_DETAILS_UPDATE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type","application/json");
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName","Aadi");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","1107228639");
        body.put("boss_request_id","16");
        body.put("stage","L1");
        body.put("mid","fdhh");
        body.put("abc","1234");
        body.put("contextKey","MID");
        body.put("contextValue","Testing521");
        body.put("workFlowExecutionId","1567");
        Response CreateReviewCaseResponse = makerCheckerMiddlewareServices.CreateReviewCaseResponse(createReviewCase,headers,queryParam,body);

        int statusCode = CreateReviewCaseResponse.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 0, description = "without makerId")
    public void TC_07_CreateCase(){
        CreateReviewCase createReviewCase = new CreateReviewCase(P.TESTDATA.get("CreateReviewCaseRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","BIN_DETAILS_UPDATE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type","application/json");
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName","Aadi");
        body.put("makerEmail","<EMAIL>");
        body.put("boss_request_id","16");
        body.put("stage","L1");
        body.put("mid","fdhh");
        body.put("abc","1234");
        body.put("source","BOSS_PANEL");
        body.put("contextKey","MID");
        body.put("contextValue","Testing521");
        body.put("workFlowExecutionId","1567");

        Response CreateReviewCaseResponse = makerCheckerMiddlewareServices.CreateReviewCaseResponse(createReviewCase,headers,queryParam,body);

        int statusCode = CreateReviewCaseResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400);
    }
    @Test(priority = 0, description = "without workflowexecutionId")
    public void TC_08_CreateCase(){
        CreateReviewCase createReviewCase = new CreateReviewCase(P.TESTDATA.get("CreateReviewCaseRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","BIN_DETAILS_UPDATE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type","application/json");
        headers.put("jwt",jwt);

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName","Aadi");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","1107228639");
        body.put("boss_request_id","16");
        body.put("stage","L1");
        body.put("mid","fdhh");
        body.put("abc","1234");
        body.put("source","BOSS_PANEL");
        body.put("contextKey","MID");
        body.put("contextValue","Testing521");

        Response CreateReviewCaseResponse = makerCheckerMiddlewareServices.CreateReviewCaseResponse(createReviewCase,headers,queryParam,body);

        int statusCode = CreateReviewCaseResponse.getStatusCode();
        Assert.assertEquals(statusCode, 500);
    }
    @Test(priority = 0, description = "Invalid jwt")
    public void TC_09_CreateCase(){
        CreateReviewCase createReviewCase = new CreateReviewCase(P.TESTDATA.get("CreateReviewCaseRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("useCase","BIN_DETAILS_UPDATE");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type","application/json");
        headers.put("jwt","cgsajhfguwefuiyqewytfuaghdvjhsdbv");

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantDisplayName","Aadi");
        body.put("makerEmail","<EMAIL>");
        body.put("makerId","1107228639");
        body.put("boss_request_id","16");
        body.put("stage","L1");
        body.put("mid","fdhh");
        body.put("abc","1234");
        body.put("source","BOSS_PANEL");
        body.put("contextKey","MID");
        body.put("contextValue","Testing521");
        body.put("workFlowExecutionId","1567");

        Response CreateReviewCaseResponse = makerCheckerMiddlewareServices.CreateReviewCaseResponse(createReviewCase,headers,queryParam,body);

        int statusCode = CreateReviewCaseResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401);
    }
}
