package OCL.MakerChecker.ReviewService;

import Request.MakerChecker.ReviewService.FetchDMSDoc;
import Services.MakerChecker.MakerCheckerMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

public class FetchDMSDocTest extends BaseMethod {
    private static final Logger LOGGER = Logger.getLogger(String.valueOf(FetchDMSDocTest.class));
    MakerCheckerMiddlewareServices makerCheckerMiddlewareServices = new MakerCheckerMiddlewareServices();
    String cookieVal= makerCheckerMiddlewareServices.getBossSessionToken("<EMAIL>","paytm@123");
    String url = "https://oe-staging5.paytm.com/checker/search";
    //String cookieVal = "BOSS_SESSION=3b01fd53-4400-4c42-8d89-6b47b4bda50c";
    @Test(priority = 0, description = "Fetch DMS Doc")
    public void fetchDMSDoc(){

        FetchDMSDoc fetchDMSDoc = new FetchDMSDoc(P.TESTDATA.get("FetchDMSDocRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("caseId","1480");
        queryParam.put("stageId","1815");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response fetchDMSDocResponse = makerCheckerMiddlewareServices.fetchDMSDocResponse(fetchDMSDoc,headers,queryParam);

        int statusCode = fetchDMSDocResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200, "Test case Passed");
    }

    @Test(priority = 0, description = "null case Id")
    public void nullcaseId(){

        FetchDMSDoc fetchDMSDoc = new FetchDMSDoc(P.TESTDATA.get("FetchDMSDocRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("stageId","5");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response fetchDMSDocResponse = makerCheckerMiddlewareServices.fetchDMSDocResponse(fetchDMSDoc,headers,queryParam);

        int statusCode = fetchDMSDocResponse.getStatusCode();
        Assert.assertEquals(statusCode, 500, "Test case Failed");
    }

    @Test(priority = 0, description = "null stage Id")
    public void nullstageId(){

        FetchDMSDoc fetchDMSDoc = new FetchDMSDoc(P.TESTDATA.get("FetchDMSDocRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("caseId","5");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response fetchDMSDocResponse = makerCheckerMiddlewareServices.fetchDMSDocResponse(fetchDMSDoc,headers,queryParam);

        int statusCode = fetchDMSDocResponse.getStatusCode();
        Assert.assertEquals(statusCode, 500, "Test case Failed");
    }

    @Test(priority = 0, description = "invalid stage Id")
    public void invalidstageId(){

        FetchDMSDoc fetchDMSDoc = new FetchDMSDoc(P.TESTDATA.get("FetchDMSDocRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("caseId","5");
        queryParam.put("stageId","");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response fetchDMSDocResponse = makerCheckerMiddlewareServices.fetchDMSDocResponse(fetchDMSDoc,headers,queryParam);

        int statusCode = fetchDMSDocResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400, "Test case Failed");
    }

    @Test(priority = 0, description = "invalid case Id")
    public void invalidcaseId(){

        FetchDMSDoc fetchDMSDoc = new FetchDMSDoc(P.TESTDATA.get("FetchDMSDocRequest"));

        Map<String, String> queryParam = new HashMap<String, String>();

        queryParam.put("caseId","5");
        queryParam.put("stageId","1043");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response fetchDMSDocResponse = makerCheckerMiddlewareServices.fetchDMSDocResponse(fetchDMSDoc,headers,queryParam);

        int statusCode = fetchDMSDocResponse.getStatusCode();
        Assert.assertEquals(statusCode, 400, "Test case Failed");
    }
}
