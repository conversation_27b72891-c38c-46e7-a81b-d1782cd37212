package OCL.MakerChecker.ReviewService;

import Request.MakerChecker.ReviewService.FetchRejectionReasons;
import Services.MakerChecker.MakerCheckerMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FetchRejectionReasonsTest extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(FetchRejectionReasonsTest.class);
    MakerCheckerMiddlewareServices makerCheckerMiddlewareServices = new MakerCheckerMiddlewareServices();
    String cookieVal= makerCheckerMiddlewareServices.getBossSessionToken("<EMAIL>","paytm@123");
    final public static String url = "https://oe-staging5.paytm.com/search";
    @Test(priority = 0, description = "Fetch Rejection Reasons")
    public void TC_01_fetchRejectionReasons(){
        FetchRejectionReasons fetchRejectionReasons = new FetchRejectionReasons(P.TESTDATA.get("FetchRejectionReasonsRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseDefinitionId","31");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response fetchRejectionReasonsResponse = makerCheckerMiddlewareServices.fetchRejectionReasons(fetchRejectionReasons,headers,queryParam);
        int statusCode = fetchRejectionReasonsResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(priority = 0,description = "Without case-definition default reasons")
    public void TC_02_withoutcasedefinition(){

        FetchRejectionReasons fetchRejectionReasons = new FetchRejectionReasons(P.TESTDATA.get("FetchRejectionReasonsRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response fetchRejectionReasonsResponse = makerCheckerMiddlewareServices.fetchRejectionReasons(fetchRejectionReasons,headers,queryParam);
        int statusCode = fetchRejectionReasonsResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200, "Passed");
    }
    @Test(priority = 0,description = "with Feild")
    public void TC_03_Fields(){

        FetchRejectionReasons fetchRejectionReasons = new FetchRejectionReasons(P.TESTDATA.get("FetchRejectionReasonsRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseDefinitionId","5");
        queryParam.put("fields","pan");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("oe-origin","BOSS");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response fetchRejectionReasonsResponse = makerCheckerMiddlewareServices.fetchRejectionReasons(fetchRejectionReasons,headers,queryParam);
        int statusCode = fetchRejectionReasonsResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200, "Testcase Passed");
    }
    @Test(priority = 0,description = "null oe-origin")
    public void TC_04_nulloeorigin(){

        FetchRejectionReasons fetchRejectionReasons = new FetchRejectionReasons(P.TESTDATA.get("FetchRejectionReasonsRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseDefinitionId","5");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("oe-origin","");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response fetchRejectionReasonsResponse = makerCheckerMiddlewareServices.fetchRejectionReasons(fetchRejectionReasons,headers,queryParam);
        int statusCode = fetchRejectionReasonsResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401, "User Authentication Failed");
    }
    @Test(priority = 0,description = "invalid cookie")
    public void TC_05_invalidcookie(){

        FetchRejectionReasons fetchRejectionReasons = new FetchRejectionReasons(P.TESTDATA.get("FetchRejectionReasonsRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseDefinitionId","5");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("oe-origin","");
        headers.put("oe-origin-url",url);
        headers.put("Cookie","vcajhscyfcyafchasv635e675");

        Response fetchRejectionReasonsResponse = makerCheckerMiddlewareServices.fetchRejectionReasons(fetchRejectionReasons,headers,queryParam);
        int statusCode = fetchRejectionReasonsResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401, "User Authentication Failed");
    }
    @Test(priority = 0,description = "invalid OE-origin")
    public void TC_06_invalidorigin(){

        FetchRejectionReasons fetchRejectionReasons = new FetchRejectionReasons(P.TESTDATA.get("FetchRejectionReasonsRequest"));
        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("caseDefinitionId","5");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("oe-origin","wtqfdgh");
        headers.put("oe-origin-url",url);
        headers.put("Cookie",cookieVal);

        Response fetchRejectionReasonsResponse = makerCheckerMiddlewareServices.fetchRejectionReasons(fetchRejectionReasons,headers,queryParam);
        int statusCode = fetchRejectionReasonsResponse.getStatusCode();
        Assert.assertEquals(statusCode, 401, "User Authentication Failed");
    }
}
