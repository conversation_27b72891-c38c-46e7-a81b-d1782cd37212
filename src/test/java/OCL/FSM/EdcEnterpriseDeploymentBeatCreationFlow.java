package OCL.FSM;

import Request.FSM.EnterpriseBeatCreation;
import Services.FSM.FSMServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class EdcEnterpriseDeploymentBeatCreationFlow extends BaseMethod
{
    FSMServices FSMServicesObj=new FSMServices();
    private static final Logger LOGGER = LogManager.getLogger(FetchFseDetails.class);
    public static String jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE3MjEzNzYxODAwMDAiLCJreWNfcHJvZmlsZV9pZCI6ICIxNTYzODkwMDM1MDAiLCAiY3VzdF9pZCI6ICIxMDAyMzc2NTMyIn0.HsueqdZQmTizRa9asVBSVcvDBYziXnKwiRzdTGp5bKE";

//    public static String jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE2OTEwNjI1ODAwMDAiLCJreWNfcHJvZmlsZV9pZCI6IjE1NjM4OTAwMzUwMCJ9.5GKjje_N5HgaiJG-DW5SJ3pokXqhziTYzGWnAiowUCs";
    public static String generateTicketNumber() {
        // Generate a random UUID
        UUID uuid = UUID.randomUUID();
        // Convert UUID to a string without dashes
        String uniqueNumber = uuid.toString().replace("-", "");
        // Create the freshdesk ticket number
        String freshdeskTicketNumber = "freshdeskTicketNumber, \"" + uniqueNumber + "\"";
        return freshdeskTicketNumber;
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation for tag- EDC Enterprise Deployment and acquirer HFPP")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");


        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Enterprise Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
//        body.put("freshdeskTicketNumber", "58702395983380987756");
        body.put("freshdeskTicketNumber", generateTicketNumber());

        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "HFPP");
        body.put("tids", "176532");
        body.put("mbid", "86373");


        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with invalid tags ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Enterprise Deployment1");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "HFPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Tag is invalid."));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with empty tags")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "HFPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Tag is invalid."));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with invalid acquirer")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Enterprise Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "HFPP1");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Invalid Acquirer value."));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with empty acquirer")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Enterprise Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Invalid Acquirer value."));
        Assert.assertEquals(StatusCode, 400);
    }

}
