package OCL.FSM;

import Request.FSM.EnterpriseBeatCreation;
import Request.FSM.FetchFseDetail;
import Services.FSM.FSMServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class EnterpriseBeatCreationFlow extends BaseMethod
{
    FSMServices FSMServicesObj=new FSMServices();
    private static final Logger LOGGER = LogManager.getLogger(FetchFseDetails.class);

//    public static String jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE2OTEwNjI1ODAwMDAiLCJreWNfcHJvZmlsZV9pZCI6IjE1NjM4OTAwMzUwMCJ9.5GKjje_N5HgaiJG-DW5SJ3pokXqhziTYzGWnAiowUCs";
    public static String jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE3MjEzNzYxODAwMDAiLCJreWNfcHJvZmlsZV9pZCI6ICIxNTYzODkwMDM1MDAiLCAiY3VzdF9pZCI6ICIxMDAyMzc2NTMyIn0.HsueqdZQmTizRa9asVBSVcvDBYziXnKwiRzdTGp5bKE";


    public static String generateTicketNumber() {
        // Generate a random UUID
        UUID uuid = UUID.randomUUID();
        // Convert UUID to a string without dashes
        String uniqueNumber = uuid.toString().replace("-", "");
        // Create the freshdesk ticket number
        String freshdeskTicketNumber = "freshdeskTicketNumber, \"" + uniqueNumber + "\"";
        return freshdeskTicketNumber;
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation for tag-EDC Retail Channel Deployment and acquirer SBPP")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");


        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
//        body.put("freshdeskTicketNumber", "58702395983380987756");
        body.put("freshdeskTicketNumber", generateTicketNumber());

        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "176532");
        body.put("mbid", "86373");


        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with invalid reference type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid1");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Invalid reference Type Allowed values:pgmid"));
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with empty reference type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Mandatory field ReferenceType or ReferenceValue is missing from the request"));
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with invalid reference value")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg668523302958631");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("No Address found in ES, KYB, BOSS."));
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with empty reference value")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Mandatory field ReferenceType or ReferenceValue is missing from the request"));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with invalid tags ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment1");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Tag is invalid."));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with empty tags")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Tag is invalid."));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with invalid product")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC1");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Invalid values for Type of product in request"));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with empty product")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Mandatory field product is missing in request."));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with invalid product model")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A9101");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Invalid values for Type of product model in request"));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with empty product model")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Mandatory field type of product model is missing"));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with invalid ticket number ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741@");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Invalid value passed for field FreshdeskTicketNumber"));
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with empty ticket number ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Freshdesk Ticket Number cannot be null or empty."));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with invalid requestType  ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_14() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service1");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Mandatory field Priority is missing from the request"));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with empty requestType")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_15() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Mandatory field Priority is missing from the request"));
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with zero deviceCount")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_16() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "0");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("DeviceCount count can\u0027t be less than 1"));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with empty requestType")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_17() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", generateTicketNumber());

        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Bad Request"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with invalid acquirer")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_18() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP1");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Invalid Acquirer value."));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with empty acquirer")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_19() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Invalid Acquirer value."));
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with invalid tids")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_20() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", generateTicketNumber());

        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "172500@");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with empty tids")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_21() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("TIDs count does not match deviceCount"));
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with invalid mbid")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_22() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Retail Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", generateTicketNumber());

        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "SBPP");
        body.put("tids", "173500");
        body.put("mbid", "89751@");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

}
