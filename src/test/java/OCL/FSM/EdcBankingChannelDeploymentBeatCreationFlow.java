package OCL.FSM;

import Request.FSM.EnterpriseBeatCreation;
import Services.FSM.FSMServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class EdcBankingChannelDeploymentBeatCreationFlow extends BaseMethod
{
    FSMServices FSMServicesObj=new FSMServices();
    private static final Logger LOGGER = LogManager.getLogger(FetchFseDetails.class);

//    public static String jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.5GKjje_N5HgaiJG-DW5SJ3pokXqhziTYzGWnAiowUCs";
public static String jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************.HsueqdZQmTizRa9asVBSVcvDBYziXnKwiRzdTGp5bKE";

    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation for tag- EDC Banking Channel Deployment and acquirer AWDC")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");


        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Banking Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987756");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "AWDC");
        body.put("tids", "176532");
        body.put("mbid", "86373");


        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with invalid tags ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Banking Channel Deployment1");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "AWDC");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Tag is invalid."));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with empty tags")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "AWDC");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Tag is invalid."));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with invalid acquirer")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Banking Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "AWDC1");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Invalid Acquirer value."));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Enterprise beat creation with empty acquirer")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EnterpriseBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "Aggreg66852330295863");
        body.put("tags", "EDC Banking Channel Deployment");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910");
        body.put("freshdeskTicketNumber", "58702395983380987741");
        body.put("requestType", "fasttrack_service");
        body.put("deviceCount", "1");
        body.put("acquirer", "");
        body.put("tids", "172500");
        body.put("mbid", "89751");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Invalid Acquirer value."));
        Assert.assertEquals(StatusCode, 400);
    }
}
