package OCL.FSM;

import Request.FSM.EnterpriseBeatCreation;
import Services.FSM.FSMServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class EDCReplaceBeatCreationFlow extends BaseMethod
{
    FSMServices FSMServicesObj=new FSMServices();
    private static final Logger LOGGER = LogManager.getLogger(FetchFseDetails.class);

//    public static String jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE2OTEwNjI1ODAwMDAiLCJreWNfcHJvZmlsZV9pZCI6IjE1NjM4OTAwMzUwMCJ9.5GKjje_N5HgaiJG-DW5SJ3pokXqhziTYzGWnAiowUCs";
    public static String jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE3MjEzNzYxODAwMDAiLCJreWNfcHJvZmlsZV9pZCI6ICIxNTYzODkwMDM1MDAiLCAiY3VzdF9pZCI6ICIxMDAyMzc2NTMyIn0.HsueqdZQmTizRa9asVBSVcvDBYziXnKwiRzdTGp5bKE";

    public static String Ticketnumber1 = "";
    public static String Ticketnumber2 = "";
    @Test(priority = 0, groups = {"Regression"}, description = "Generate ticket number")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_00_GenerateTicketNumber() {
        Integer RandomDigit1 = Utilities.randomNumberGenerator(10);
        Integer RandomDigit2 = Utilities.randomNumberGenerator(10);
        Ticketnumber1 = "71909202" + RandomDigit1;
        Ticketnumber2= "71909202" + RandomDigit2;


    }

    @Test(priority = 1, groups = {"Regression"}, description = "EDC Replacement beat creation for tag- EDC Replacement")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EDCDeactivationBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");


        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "KyeiXd63386517982023");
        body.put("tags", "EDC Replacement");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "Android A910");
        body.put("freshdeskTicketNumber", Ticketnumber1);
        body.put("deviceSerialNumber", Ticketnumber2);

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "EDC Replacement beat creation for tag- EDC Replacement with invalid tag")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EDCDeactivationBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");


        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "KyeiXd63386517982023");
        body.put("tags", "EDC Replacement1");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "Android A910");
        body.put("freshdeskTicketNumber", "58702395986680987756");
        body.put("deviceSerialNumber", "MEGLD979JSQ");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Tag is invalid."));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "EDC Replacement beat creation for tag- EDC Replacement with empty tag")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EDCDeactivationBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");


        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "KyeiXd63386517982023");
        body.put("tags", " ");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "Android A910");
        body.put("freshdeskTicketNumber", "58702395986680987756");
        body.put("deviceSerialNumber", "MEGLD979JSQ");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Tag is invalid."));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "EDC Replacement beat creation for tag- EDC Replacement with invalid product model")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EDCDeactivationBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");


        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "KyeiXd63386517982023");
        body.put("tags", "EDC Replacement");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "A910@");
        body.put("freshdeskTicketNumber", "58702395986680987756");
        body.put("deviceSerialNumber", "MEGLD979JSQ");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Invalid values for Type of product model in request"));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "EDC Replacement beat creation for tag- EDC Replacement with empty product model")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EDCDeactivationBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");


        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "KyeiXd63386517982023");
        body.put("tags", "EDC Replacement");
        body.put("product", "EDC");
        body.put("typeOfProductModel", " ");
        body.put("freshdeskTicketNumber", "58702395986680987756");
        body.put("deviceSerialNumber", "MEGLD979JSQ");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Mandatory field type of product model is missing"));
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "EDC Replacement beat creation for tag- EDC Replacement with invalid serial number")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EDCDeactivationBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");


        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "KyeiXd63386517982023");
        body.put("tags", "EDC Replacement");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "Android A910");
        body.put("freshdeskTicketNumber", "58702395986680987756");
        body.put("deviceSerialNumber", "MEGLD979JSQ@");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Invalid value passed for field DeviceSerialNumber"));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "EDC Replacement beat creation for tag- EDC Replacement with empty serial number")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EDCDeactivationBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");


        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "KyeiXd63386517982023");
        body.put("tags", "EDC Replacement");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "Android A910");
        body.put("freshdeskTicketNumber", Ticketnumber2);
        body.put("deviceSerialNumber", " ");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
       // Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Mandatory field DSN is missing in request."));
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "EDC Replacement beat creation for tag- EDC Replacement with invalid referenceType")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EDCDeactivationBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");


        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pg mid");
        body.put("referenceValue", "KyeiXd63386517982023");
        body.put("tags", "EDC Replacement");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "Android A910");
        body.put("freshdeskTicketNumber", "58702395986680987756");
        body.put("deviceSerialNumber", "MEGLD979JSQ");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Invalid reference Type Allowed values:pgmid"));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "EDC Replacement beat creation for tag- EDC Replacement with empty referenceType")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EDCDeactivationBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");


        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", " ");
        body.put("referenceValue", "KyeiXd63386517982023");
        body.put("tags", "EDC Replacement");
        body.put("product", "EDC");
        body.put("typeOfProductModel", "Android A910");
        body.put("freshdeskTicketNumber", "58702395986680987756");
        body.put("deviceSerialNumber", "MEGLD979JSQ");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Mandatory field ReferenceType or ReferenceValue is missing from the request"));
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "EDC Replacement beat creation for tag- EDC Replacement with empty product")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10() {

        EnterpriseBeatCreation EnterpriseBeatCreationObj=new EnterpriseBeatCreation(P.TESTDATA.get("EDCDeactivationBeatCreationRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");


        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceValue", "KyeiXd63386517982023");
        body.put("tags", "EDC Replacement");
        body.put("product", " ");
        body.put("typeOfProductModel", "Android A910");
        body.put("freshdeskTicketNumber", "58702395986680987756");
        body.put("deviceSerialNumber", "MEGLD979JSQ");

        Response EnterpriseBeatCreationObjResp = FSMServicesObj.enterpriseBeatCreation(EnterpriseBeatCreationObj,headers,body);
        int StatusCode = EnterpriseBeatCreationObjResp.getStatusCode();
        Assert.assertTrue(EnterpriseBeatCreationObjResp.jsonPath().getString("error.errorMsg").contains("Mandatory field product is missing in request."));
        Assert.assertEquals(StatusCode, 400);
    }

}
