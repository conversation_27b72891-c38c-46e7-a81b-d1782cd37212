package OCL.FSM;

import Request.FSM.FetchBeatDetail;
import Request.FSM.FetchFseDetail;
import Request.FSM.FetchMerchantDetail;
import Request.FSM.UpdateTeamAndSubteam;
import Services.FSM.FSMServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FetchFseDetails extends BaseMethod
{
    FSMServices FSMServicesObj=new FSMServices();
    private static final Logger LOGGER = LogManager.getLogger(FetchFseDetails.class);

//    public static String jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE2OTEwNjI1ODAwMDAiLCJreWNfcHJvZmlsZV9pZCI6IjE1NjM4OTAwMzUwMCJ9.5GKjje_N5HgaiJG-DW5SJ3pokXqhziTYzGWnAiowUCs";
public static String jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE3MjEzNzYxODAwMDAiLCJreWNfcHJvZmlsZV9pZCI6ICIxNTYzODkwMDM1MDAiLCAiY3VzdF9pZCI6ICIxMDAyMzc2NTMyIn0.HsueqdZQmTizRa9asVBSVcvDBYziXnKwiRzdTGp5bKE";

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch FSE details from fsm ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_FetchFseDetailFromFSM() {

        FetchFseDetail FetchFseDetailObj=new FetchFseDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("uid", "1000875574");
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("userInfo", "1");
        params.put("hierarchy", "1");


        Response FetchFseDetailObjResp = FSMServicesObj.fetchFseDetail(FetchFseDetailObj,headers,params);
        int StatusCode = FetchFseDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch FSE details from fsm with empty user info ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_FetchFseDetailFromFSMWithEmptyUserInfo() {

        FetchFseDetail FetchFseDetailObj=new FetchFseDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("uid", "1000875574");
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("userInfo", " ");
        params.put("hierarchy", "1");


        Response FetchFseDetailObjResp = FSMServicesObj.fetchFseDetail(FetchFseDetailObj,headers,params);
        int StatusCode = FetchFseDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch FSE details from fsm with invalid user info ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_FetchFseDetailFromFSMWithEmptyUserInfo() {

        FetchFseDetail FetchFseDetailObj=new FetchFseDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("uid", "1000875574");
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("userInfo", "abc");
        params.put("hierarchy", "1");


        Response FetchFseDetailObjResp = FSMServicesObj.fetchFseDetail(FetchFseDetailObj,headers,params);
        int StatusCode = FetchFseDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch FSE details from fsm with empty hierarchy info ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_FetchFseDetailFromFSMWithEmptyhierarchyInfo() {

        FetchFseDetail FetchFseDetailObj=new FetchFseDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("uid", "1000875574");
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("userInfo", "1");
        params.put("hierarchy", " ");


        Response FetchFseDetailObjResp = FSMServicesObj.fetchFseDetail(FetchFseDetailObj,headers,params);
        int StatusCode = FetchFseDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch FSE details from fsm with invalid hierarchy info ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_FetchFseDetailFromFSMWithInvalidhierarchyInfo() {

        FetchFseDetail FetchFseDetailObj=new FetchFseDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("uid", "1000875574");
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("userInfo", "1");
        params.put("hierarchy", "abc");


        Response FetchFseDetailObjResp = FSMServicesObj.fetchFseDetail(FetchFseDetailObj,headers,params);
        int StatusCode = FetchFseDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch FSE details from fsm with empty uid info ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_FetchFseDetailFromFSMWithEmptyUid() {

        FetchFseDetail FetchFseDetailObj=new FetchFseDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("uid", " ");
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("userInfo", "1");
        params.put("hierarchy", "1");


        Response FetchFseDetailObjResp = FSMServicesObj.fetchFseDetail(FetchFseDetailObj,headers,params);
        int StatusCode = FetchFseDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch FSE details from fsm with invalid uid info ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_FetchFseDetailFromFSMWithInvalidUid() {

        FetchFseDetail FetchFseDetailObj=new FetchFseDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("uid", "abc");
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("userInfo", "1");
        params.put("hierarchy", "1");


        Response FetchFseDetailObjResp = FSMServicesObj.fetchFseDetail(FetchFseDetailObj,headers,params);
        int StatusCode = FetchFseDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch FSE details from fsm with empty jwt info ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_FetchFseDetailFromFSMWithEmptyJwt() {

        FetchFseDetail FetchFseDetailObj=new FetchFseDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", " ");
        headers.put("uid", "1000875574");
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("userInfo", "1");
        params.put("hierarchy", "1");


        Response FetchFseDetailObjResp = FSMServicesObj.fetchFseDetail(FetchFseDetailObj,headers,params);
        int StatusCode = FetchFseDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 401);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch FSE details from fsm with invalid jwt info ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_FetchFseDetailFromFSMWithInvalidJwt() {

        FetchFseDetail FetchFseDetailObj=new FetchFseDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt + "1");
        headers.put("uid", "1000875574");
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("userInfo", "1");
        params.put("hierarchy", "1");


        Response FetchFseDetailObjResp = FSMServicesObj.fetchFseDetail(FetchFseDetailObj,headers,params);
        int StatusCode = FetchFseDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 401);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch merchant details ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_FetchMerchantDetails() {

        FetchMerchantDetail FetchMerchantDetailObj=new FetchMerchantDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("custId", "1000875574");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pg_mid", "HySHnd27878673398759");
        body.put("req_params", "pg_mid");

        Response FetchMerchantDetailObjResp = FSMServicesObj.fetchMerchantDetail(FetchMerchantDetailObj,headers,params,body);
        int StatusCode = FetchMerchantDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch FSE details from fsm with empty mid info ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11_FetchFseDetailFromFSMWithEmptyMid() {

        FetchMerchantDetail FetchMerchantDetailObj=new FetchMerchantDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("custId", "1000875574");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pg_mid", " ");
        body.put("req_params", "pg_mid");


        Response FetchMerchantDetailObjResp = FSMServicesObj.fetchMerchantDetail(FetchMerchantDetailObj,headers,params,body);
        int StatusCode = FetchMerchantDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch FSE details from fsm with invalid mid info ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12_FetchFseDetailFromFSMWithInvalidMid() {

        FetchMerchantDetail FetchMerchantDetailObj=new FetchMerchantDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("custId", "1000875574");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pg_mid", "HySHnd278786733987591");
        body.put("req_params", "pg_mid");

        Response FetchMerchantDetailObjResp = FSMServicesObj.fetchMerchantDetail(FetchMerchantDetailObj,headers,params,body);
        int StatusCode = FetchMerchantDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch FSE details from fsm with empty custid info ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13_FetchFseDetailFromFSMWithEmptyCustid() {

        FetchMerchantDetail FetchMerchantDetailObj=new FetchMerchantDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("custId", "");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pg_mid", "HySHnd27878673398759");
        body.put("req_params", "pg_mid");


        Response FetchMerchantDetailObjResp = FSMServicesObj.fetchMerchantDetail(FetchMerchantDetailObj,headers,params,body);
        int StatusCode = FetchMerchantDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch FSE details from fsm with invalid custid info ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_14_FetchFseDetailFromFSMWithInvalidCustid() {

        FetchMerchantDetail FetchMerchantDetailObj=new FetchMerchantDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("custId", "10008755741");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pg_mid", "HySHnd27878673398759");
        body.put("req_params", "pg_mid");

        Response FetchMerchantDetailObjResp = FSMServicesObj.fetchMerchantDetail(FetchMerchantDetailObj,headers,params,body);
        int StatusCode = FetchMerchantDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch FSE details from fsm with empty RequestParam info ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_15_FetchFseDetailFromFSMWithEmptyRequestParam() {

        FetchMerchantDetail FetchMerchantDetailObj=new FetchMerchantDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("custId", "1000875574");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pg_mid", "HySHnd27878673398759");
        body.put("req_params", " ");


        Response FetchMerchantDetailObjResp = FSMServicesObj.fetchMerchantDetail(FetchMerchantDetailObj,headers,params,body);
        int StatusCode = FetchMerchantDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch FSE details from fsm with invalid RequestParam info ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_16_FetchFseDetailFromFSMWithInvalidRequestParam() {

        FetchMerchantDetail FetchMerchantDetailObj=new FetchMerchantDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("custId", "1000875574");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pg_mid", "HySHnd27878673398759");
        body.put("req_params", "pg_mid1");

        Response FetchMerchantDetailObjResp = FSMServicesObj.fetchMerchantDetail(FetchMerchantDetailObj,headers,params,body);
        int StatusCode = FetchMerchantDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch beat details from fsm ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_17_FetchBeatDetailFromFsm() {

        FetchBeatDetail FetchBeatDetailObj=new FetchBeatDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceId", "HySHnd27878673398759");
        body.put("agentEcode", "**********");
        body.put("solutionType", "revisit_merchant");

        Response FetchBeatDetailObjResp = FSMServicesObj.fetchBeatDetail(FetchBeatDetailObj,headers,body);
        int StatusCode = FetchBeatDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch beat details from fsm with invalid ref type ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_18_FetchBeatDetailFromFsmWithInvalidRefType() {

        FetchBeatDetail FetchBeatDetailObj=new FetchBeatDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid1");
        body.put("referenceId", "HySHnd27878673398759");
        body.put("agentEcode", "**********");
        body.put("solutionType", "revisit_merchant");

        Response FetchBeatDetailObjResp = FSMServicesObj.fetchBeatDetail(FetchBeatDetailObj,headers,body);
        int StatusCode = FetchBeatDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch beat details from fsm with empty ref type ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_19_FetchBeatDetailFromFsmWithEmptyRefType() {

        FetchBeatDetail FetchBeatDetailObj=new FetchBeatDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "");
        body.put("referenceId", "HySHnd27878673398759");
        body.put("agentEcode", "**********");
        body.put("solutionType", "revisit_merchant");

        Response FetchBeatDetailObjResp = FSMServicesObj.fetchBeatDetail(FetchBeatDetailObj,headers,body);
        int StatusCode = FetchBeatDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch beat details from fsm with null ref type ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_20_FetchBeatDetailFromFsmWithNullRefType() {

        FetchBeatDetail FetchBeatDetailObj=new FetchBeatDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "null");
        body.put("referenceId", "HySHnd27878673398759");
        body.put("agentEcode", "**********");
        body.put("solutionType", "revisit_merchant");

        Response FetchBeatDetailObjResp = FSMServicesObj.fetchBeatDetail(FetchBeatDetailObj,headers,body);
        int StatusCode = FetchBeatDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch beat details from fsm with empty refid  ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_21_FetchBeatDetailFromFsmWithEmptyRefid() {

        FetchBeatDetail FetchBeatDetailObj=new FetchBeatDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceId", "");
        body.put("agentEcode", "**********");
        body.put("solutionType", "revisit_merchant");

        Response FetchBeatDetailObjResp = FSMServicesObj.fetchBeatDetail(FetchBeatDetailObj,headers,body);
        int StatusCode = FetchBeatDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch beat details from fsm with invalid refid  ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_22_FetchBeatDetailFromFsmWithInvalidRefid() {

        FetchBeatDetail FetchBeatDetailObj=new FetchBeatDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceId", "abc");
        body.put("agentEcode", "**********");
        body.put("solutionType", "revisit_merchant");

        Response FetchBeatDetailObjResp = FSMServicesObj.fetchBeatDetail(FetchBeatDetailObj,headers,body);
        int StatusCode = FetchBeatDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch beat details from fsm with empty agent code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_23_FetchBeatDetailFromFsmWithEmptyAgentCode() {

        FetchBeatDetail FetchBeatDetailObj=new FetchBeatDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceId", "HySHnd27878673398759");
        body.put("agentEcode", "");
        body.put("solutionType", "revisit_merchant");

        Response FetchBeatDetailObjResp = FSMServicesObj.fetchBeatDetail(FetchBeatDetailObj,headers,body);
        int StatusCode = FetchBeatDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch beat details from fsm with invalid agent code  ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_24_FetchBeatDetailFromFsmWithInvalidAgentCode() {

        FetchBeatDetail FetchBeatDetailObj=new FetchBeatDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceId", "HySHnd27878673398759");
        body.put("agentEcode", "**********1");
        body.put("solutionType", "revisit_merchant");

        Response FetchBeatDetailObjResp = FSMServicesObj.fetchBeatDetail(FetchBeatDetailObj,headers,body);
        int StatusCode = FetchBeatDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch beat details from fsm with empty solution type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_25_FetchBeatDetailFromFsmWithEmptySolutionType() {

        FetchBeatDetail FetchBeatDetailObj=new FetchBeatDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceId", "HySHnd27878673398759");
        body.put("agentEcode", "**********");
        body.put("solutionType", "");

        Response FetchBeatDetailObjResp = FSMServicesObj.fetchBeatDetail(FetchBeatDetailObj,headers,body);
        int StatusCode = FetchBeatDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Fetch beat details from fsm with invalid solution type  ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_26_FetchBeatDetailFromFsmWithInvalidSolutionType() {

        FetchBeatDetail FetchBeatDetailObj=new FetchBeatDetail();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("referenceType", "pgmid");
        body.put("referenceId", "HySHnd27878673398759");
        body.put("agentEcode", "**********");
        body.put("solutionType", "revisit_merchant1");

        Response FetchBeatDetailObjResp = FSMServicesObj.fetchBeatDetail(FetchBeatDetailObj,headers,body);
        int StatusCode = FetchBeatDetailObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Update team and sub-team in fsm ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_27_UpdateTeamAndSubteam (){

        UpdateTeamAndSubteam UpdateTeamAndSubteamObj=new UpdateTeamAndSubteam();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("status", "ACTIVE");
        body.put("empCode", "**********");
        body.put("designation", "TL");
        body.put("agentType", "FSE");
        body.put("team", "Soundbox");
        body.put("subteam", "Device Service");
        body.put("department", "Soundbox");

        Response UpdateTeamAndSubteamObjResp = FSMServicesObj.updateTeamAndSubteam(UpdateTeamAndSubteamObj,headers,body);
        int StatusCode = UpdateTeamAndSubteamObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Update team and sub-team in fsm with invalid status")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_28_UpdateTeamAndSubteamWithInvalidStatus(){

        UpdateTeamAndSubteam UpdateTeamAndSubteamObj=new UpdateTeamAndSubteam();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("status", "ACTIVE1");
        body.put("empCode", "**********");
        body.put("designation", "TL");
        body.put("agentType", "FSE");
        body.put("team", "Soundbox");
        body.put("subteam", "Device Service");
        body.put("department", "Soundbox");

        Response UpdateTeamAndSubteamObjResp = FSMServicesObj.updateTeamAndSubteam(UpdateTeamAndSubteamObj,headers,body);
        int StatusCode = UpdateTeamAndSubteamObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Update team and sub-team in fsm with inactive status")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_29_UpdateTeamAndSubteamWithInactiveStatus(){

        UpdateTeamAndSubteam UpdateTeamAndSubteamObj=new UpdateTeamAndSubteam();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("status", "INACTIVE");
        body.put("empCode", "**********");
        body.put("designation", "TL");
        body.put("agentType", "FSE");
        body.put("team", "Soundbox");
        body.put("subteam", "Device Service");
        body.put("department", "Soundbox");

        Response UpdateTeamAndSubteamObjResp = FSMServicesObj.updateTeamAndSubteam(UpdateTeamAndSubteamObj,headers,body);
        int StatusCode = UpdateTeamAndSubteamObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Update team and sub-team in fsm with invalid empcode")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_30_UpdateTeamAndSubteamWithInvalidEmpCode(){

        UpdateTeamAndSubteam UpdateTeamAndSubteamObj=new UpdateTeamAndSubteam();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("status", "ACTIVE");
        body.put("empCode", "**********1");
        body.put("designation", "TL");
        body.put("agentType", "FSE");
        body.put("team", "Soundbox");
        body.put("subteam", "Device Service");
        body.put("department", "Soundbox");

        Response UpdateTeamAndSubteamObjResp = FSMServicesObj.updateTeamAndSubteam(UpdateTeamAndSubteamObj,headers,body);
        int StatusCode = UpdateTeamAndSubteamObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Update team and sub-team in fsm with empty empcode")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_31_UpdateTeamAndSubteamWithEmptyEmpCode(){

        UpdateTeamAndSubteam UpdateTeamAndSubteamObj=new UpdateTeamAndSubteam();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("status", "ACTIVE");
        body.put("empCode", "");
        body.put("designation", "TL");
        body.put("agentType", "FSE");
        body.put("team", "Soundbox");
        body.put("subteam", "Device Service");
        body.put("department", "Soundbox");

        Response UpdateTeamAndSubteamObjResp = FSMServicesObj.updateTeamAndSubteam(UpdateTeamAndSubteamObj,headers,body);
        int StatusCode = UpdateTeamAndSubteamObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Update team and sub-team in fsm with invalid designation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_32_UpdateTeamAndSubteamWithInvalidDesignation(){

        UpdateTeamAndSubteam UpdateTeamAndSubteamObj=new UpdateTeamAndSubteam();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("status", "ACTIVE");
        body.put("empCode", "**********");
        body.put("designation", "TL1");
        body.put("agentType", "FSE");
        body.put("team", "Soundbox");
        body.put("subteam", "Device Service");
        body.put("department", "Soundbox");

        Response UpdateTeamAndSubteamObjResp = FSMServicesObj.updateTeamAndSubteam(UpdateTeamAndSubteamObj,headers,body);
        int StatusCode = UpdateTeamAndSubteamObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Update team and sub-team in fsm with invalid agentType")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_33_UpdateTeamAndSubteamWithEmptyEmpCode(){

        UpdateTeamAndSubteam UpdateTeamAndSubteamObj=new UpdateTeamAndSubteam();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("status", "ACTIVE");
        body.put("empCode", "**********");
        body.put("designation", "FSE");
        body.put("agentType", "FSE1");
        body.put("team", "Soundbox");
        body.put("subteam", "Device Service");
        body.put("department", "Soundbox");

        Response UpdateTeamAndSubteamObjResp = FSMServicesObj.updateTeamAndSubteam(UpdateTeamAndSubteamObj,headers,body);
        int StatusCode = UpdateTeamAndSubteamObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Update team and sub-team in fsm with invalid team")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_34_UpdateTeamAndSubteamWithInvalidTeam(){

        UpdateTeamAndSubteam UpdateTeamAndSubteamObj=new UpdateTeamAndSubteam();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("status", "ACTIVE");
        body.put("empCode", "**********");
        body.put("designation", "TL");
        body.put("agentType", "FSE");
        body.put("team", "banking-offline1");
        body.put("subteam", "enterprise sales");
        body.put("department", "banking-offline");

        Response UpdateTeamAndSubteamObjResp = FSMServicesObj.updateTeamAndSubteam(UpdateTeamAndSubteamObj,headers,body);
        int StatusCode = UpdateTeamAndSubteamObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Update team and sub-team in fsm with invalid sub-team")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_35_UpdateTeamAndSubteamWithEmptyEmpCode(){

        UpdateTeamAndSubteam UpdateTeamAndSubteamObj=new UpdateTeamAndSubteam();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("status", "ACTIVE");
        body.put("empCode", "**********");
        body.put("designation", "FSE");
        body.put("agentType", "FSE");
        body.put("team", "banking-offline");
        body.put("subteam", "enterprise sales1");
        body.put("department", "banking-offline");

        Response UpdateTeamAndSubteamObjResp = FSMServicesObj.updateTeamAndSubteam(UpdateTeamAndSubteamObj,headers,body);
        int StatusCode = UpdateTeamAndSubteamObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Update team and sub-team in fsm with invalid department")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_36_UpdateTeamAndSubteamWithEmptyEmpCode(){

        UpdateTeamAndSubteam UpdateTeamAndSubteamObj=new UpdateTeamAndSubteam();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("status", "ACTIVE");
        body.put("empCode", "**********");
        body.put("designation", "TL");
        body.put("agentType", "FSE");
        body.put("team", "banking-offline");
        body.put("subteam", "enterprise sales");
        body.put("department", "banking-offline1");

        Response UpdateTeamAndSubteamObjResp = FSMServicesObj.updateTeamAndSubteam(UpdateTeamAndSubteamObj,headers,body);
        int StatusCode = UpdateTeamAndSubteamObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Update team and sub-team in fsm with empty team and sub-team")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_37_UpdateTeamAndSubteamWithEmptyTeamAndSubTeam(){

        UpdateTeamAndSubteam UpdateTeamAndSubteamObj=new UpdateTeamAndSubteam();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("Content-Type", "application/json");



        Map<String, String> body = new HashMap<String, String>();
        body.put("status", "ACTIVE");
        body.put("empCode", "**********");
        body.put("designation", "TL");
        body.put("agentType", "FSE");
        body.put("team", "");
        body.put("subteam", "");
        body.put("department", "");

        Response UpdateTeamAndSubteamObjResp = FSMServicesObj.updateTeamAndSubteam(UpdateTeamAndSubteamObj,headers,body);
        int StatusCode = UpdateTeamAndSubteamObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }



















}
