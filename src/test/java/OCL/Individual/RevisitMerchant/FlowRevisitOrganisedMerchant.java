package OCL.Individual.RevisitMerchant;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.QnA.FetchQnA;
import Request.MerchantService.v1.Revisit.RevisitOrganised;
import Request.MerchantService.v1.Revisit.RevisitSubmit;
import Services.MechantService.MiddlewareServices;
import Services.UAD.UADServices;
import Services.Wallet.WalletServices;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.util.*;

public class FlowRevisitOrganisedMerchant extends BaseMethod
{
    private static final Logger LOGGER = LogManager.getLogger(FlowRevisitOrganisedMerchant.class);

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    UADServices uadServicesObject = new UADServices();
    WalletServices walletServices = new WalletServices();

    public static String AgentToken = "";
    public static String CustId = "";
    public static String ShopReferenceId = "ENTP017111";
    public static  String version = "4.8.3";
    public static String ShopId ="";
    public static String UserMID = "";
    
    public static String OTP = "";
    public static String leadId = "";
    public static String KybContractId = "";
    public static String KybBusinessId = "";
    public static String KybSolution = "";
    public static String SolutionType = "";
    public static String FseRole = "";
    public static String FseSubRole = "";
    public static String Category = "";
    public static String SubSol = "";
    public static String OePanelDocStatus = "APPROVED";
    public static String RejectionReason = null;
    public static String DocumetRequestDeserialised = "";
    public static String WorkFlowId = "";
    public static List<String> docsToUpload = new ArrayList<>();



    @BeforeTest
    public void AgentLoginMapPos()
    {
        LOGGER.info("Before Organised Revisit Merchant Test, Agent Login");
        AgentToken = AgentSessionToken("8010630022","paytm@123");
       //AgentToken = "cbbd8444-6560-42be-832c-1886a6598000";
    }

    @Test(priority = 0,description = "Fetch Revisit Details for ShopId",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_FetchShopDetailsRevisitOrganised()
    {
        RevisitOrganised orgRev = new RevisitOrganised();

        Map<String,String > query = new HashMap<>();
        query.put("storeID",ShopReferenceId);

        Response organisedRevResp = middlewareServicesObject.RevisitOrgainsed(orgRev,query,version,AgentToken);

        Map SolAddMap = organisedRevResp.jsonPath().getMap("solutionAddressMap");

        Set solutionType =SolAddMap.keySet();
        LOGGER.info("Set of SolutionType : " +solutionType.toString());

        ArrayList getSolType = new ArrayList<>(solutionType);
        SolutionType = getSolType.get(0).toString();
        LOGGER.info("Solution Type is :" +SolutionType);

       List SolutionDetails = (List) SolAddMap.get(SolutionType);
        LOGGER.info("Solution Details as LIST :" +SolutionDetails);

        Map FinalDataExtract = (Map) SolutionDetails.get(0);

        CustId = organisedRevResp.jsonPath().getJsonObject("custId").toString();
        UserMID = FinalDataExtract.get("mid").toString(); //organisedRevResp.jsonPath().getJsonObject("solutionAddressMap.p2p_100k[0].mid").toString();
        ShopId = FinalDataExtract.get("shopId").toString(); //organisedRevResp.jsonPath().getJsonObject("solutionAddressMap.p2p_100k[0].shopId").toString();
        KybContractId = FinalDataExtract.get("kybContractId").toString(); //organisedRevResp.jsonPath().getJsonObject("solutionAddressMap.p2p_100k[0].kybContractId").toString();
        KybBusinessId = FinalDataExtract.get("kybBusinessId").toString(); //organisedRevResp.jsonPath().getJsonObject("solutionAddressMap.p2p_100k[0].kybBusinessId").toString();
        KybSolution = FinalDataExtract.get("kybSolution").toString(); //organisedRevResp.jsonPath().getJsonObject("solutionAddressMap.p2p_100k[0].kybSolution").toString();
        FseRole = FinalDataExtract.get("fseRole").toString();
        FseSubRole = FinalDataExtract.get("fseSubRole").toString();
        Category = FinalDataExtract.get("category").toString();
        SubSol = FinalDataExtract.get("solutionSubType").toString();


        LOGGER.info("\n CustId : " +CustId + "\n User MID : " +UserMID + "\n ShopId : " +ShopId + "\n KYB Contract ID : " + KybContractId
        + "\n KYB Business Id : " +KybBusinessId + "\n Kyb Solution : " + KybSolution + "\n FSE Role : " +FseRole + "\n Fse Sub Role : " +FseSubRole
        + "\n Category of Merchant : " +Category + "\n Sub Solution Type : " +SubSol);



    }


    @Test(priority = 0,description = "Fetch QnA for Organised Merchant",dependsOnMethods = "TC001_FetchShopDetailsRevisitOrganised",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_FetchQnaRevisitOrganised()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        query.put("entityType","INDIVIDUAL");
        query.put("solutionSubType",SubSol);
        query.put("category",Category);
        query.put("questionType","additional");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","organized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        int StatusCode = fetchQnAresp.getStatusCode();
        Assert.assertEquals(StatusCode,200);


    }

    @Test(priority = 0,description = "Submit Revisit Details for ShopId",dependsOnMethods = "TC002_FetchQnaRevisitOrganised",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_SubmitRevisitOrganised()
    {
        RevisitSubmit revisitSubmit = new RevisitSubmit(P.TESTDATA.get("OrganisedSubmit"));

        Map<String,String> query = new HashMap<>();

        Map<String,String> body = new HashMap<>();
        body.put("custId",CustId);
        body.put("storeReferenceID",ShopReferenceId);
        body.put("solutionRevisited",SolutionType);
        body.put("shopId",ShopId);
        body.put("mid",UserMID);
        body.put("category",Category);
        body.put("kybBusinessId",KybBusinessId);
        body.put("kybContractId",KybContractId);
        body.put("kybSolution",KybSolution);
        body.put("fseRole",FseRole);
        body.put("fseSubRole",FseSubRole);

        LOGGER.info("This is Request Body Map : " +body);

        Response submitRevisitRequest = middlewareServicesObject.RevisitSubmit(revisitSubmit,query,body,version,AgentToken);

        leadId = submitRevisitRequest.jsonPath().getJsonObject("leadId").toString();
        LOGGER.info("This is lead Id of Organised Revisit : " +leadId);
    }

    @Test(priority = 0,description = "Positive Fetch and Submit Documents for Revisit Organised",dependsOnMethods ="TC003_SubmitRevisitOrganised",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_FetchAndSubmitDocRevisitOrganised() throws JsonProcessingException
    {
        Map<String,String>queryDocUpload = new HashMap<>();
        queryDocUpload.put("type", "jpg");
        queryDocUpload.put("entityType", "INDIVIDUAL");
        queryDocUpload.put("solutionType", "revisit_merchant");
        queryDocUpload.put("merchantCustId", CustId);
        queryDocUpload.put("leadId", leadId);
        queryDocUpload.put("solutionTypeLevel2",SubSol);
        queryDocUpload.put("solutionTypeLevel3","organized");
        queryDocUpload.put("category",Category);
        queryDocUpload.put("fseRole",FseRole);
        queryDocUpload.put("fseSubRole",FseSubRole);

        Map<String,String>queryFetchDoc = new HashMap<>();
        queryFetchDoc.put("entityType", "INDIVIDUAL");
        queryFetchDoc.put("solution", "revisit_merchant");
        queryFetchDoc.put("leadId", leadId);
        queryFetchDoc.put("channel", "GG_APP");
        queryFetchDoc.put("merchantCustId", CustId);
        queryFetchDoc.put("solutionSubType",SubSol);
        queryFetchDoc.put("category",Category);
        queryFetchDoc.put("solutionTypeLevel3","organized");
        queryFetchDoc.put("fseRole",FseRole);
        queryFetchDoc.put("fseSubRole",FseSubRole);

        GgAppDynamicDocUpload(AgentToken,version,"",queryFetchDoc,queryDocUpload);

    }

    @Test(priority = 0,description = "Fetch Lead Details on Panel",groups = {"Regression"},dependsOnMethods = "TC004_FetchAndSubmitDocRevisitOrganised")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_PositiveFetchLeadPanelRevisitOrganised() throws JsonProcessingException
    {
        Map <String,String> RequestPanel = new HashMap<>();

        RequestPanel.put("docStatus",OePanelDocStatus);
        RequestPanel.put("rejectionReason",RejectionReason);
        RequestPanel.put("leadId",leadId);

        Map <String,String> ResponsePanel = FetchPanelLead(RequestPanel);

        DocumetRequestDeserialised = ResponsePanel.get("DocumenstArray");
        WorkFlowId = ResponsePanel.get("WorkFlowId");
        LOGGER. info("Lead Stage is : " + ResponsePanel.get("LeadStage"));

    }

    @Test(priority = 253,description = "Submit rejected lead from OE panel ",groups = {"Regression"},dependsOnMethods = "TC005_PositiveFetchLeadPanelRevisitOrganised")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC006_PositiveSubmitLeadPanel()
    {

        ReallocatingAgent(leadId,"1152");
        EditLead v1EditLeadObj=new EditLead(leadId, P.TESTDATA.get("EditLeadRevisitOrganised"));

        v1EditLeadObj.getProperties().setProperty("documents",DocumetRequestDeserialised);
        v1EditLeadObj.getProperties().setProperty("custId",CustId);
        v1EditLeadObj.getProperties().setProperty("pgMID",UserMID);
        v1EditLeadObj.getProperties().setProperty("kybShopReferenceId",ShopReferenceId);

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "9560526665", "1106992015", XMWCookie,"application/json");

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode,200);

        String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
        Assert.assertTrue(LeadSatge.contains("PANEL_SUCCESS"));
    }

}
