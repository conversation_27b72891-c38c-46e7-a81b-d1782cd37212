package OCL.Individual.RevisitMerchant;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.QnA.FetchQnA;
import Request.MerchantService.v1.Revisit.RevisitSubmit;
import Request.MerchantService.v1.Revisit.RevisitUnOrganised;
import Services.MechantService.MiddlewareServices;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.*;

public class FlowRevisitUnOrganisedMerchant extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(FlowRevisitUnOrganisedMerchant.class);

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    public static String AgentToken = "";
    public static String CustId = "";
    public static String MobileNo = "5555508413";
    public static  String version = "7.1.9";
    public static String ShopId ="";
    public static String UserMID = "";

    public static String OTP = "";
    public static String leadId = "";
    public static String KybContractId = "";
    public static String KybBusinessId = "";
    public static String KybSolution = "";
    public static String SolutionType = "";
    public static String FseRole = "";
    public static String FseSubRole = "";
    public static String Category = "";
    public static String SubSol = "";
    public static String OePanelDocStatus = "APPROVED";
    public static String RejectionReason = null;
    public static String DocumetRequestDeserialised = "";
    public static String WorkFlowId = "";
    public static String LeadStage = "";
    public static List<String> docsToUpload = new ArrayList<>();

    @BeforeMethod
    public void AgentLoginMapPos() throws Exception {
        LOGGER.info("Before Un-Organised Revisit Merchant Test, Agent Login");
        AgentToken = AgentSessionToken("8010630022","paytm@123");
        establishConnectiontoServer(AgentToken,5);
        LOGGER.info("Agent Session token : " +AgentToken);
    }

    @Test(description = "Fetch Revisit details for Inavlid Mobile")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_01_FetchShopForInvalidNumber()
    {
        RevisitUnOrganised unOrgRev = new RevisitUnOrganised("123456789");

        Map<String,String > query = new HashMap<>();
        Map<String,String > headers = new HashMap<>();

        headers.put("Content-Type", "application/json");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", AgentToken);
        headers.put("version", version);
        headers.put("appLanguage", "en");
        headers.put("deviceName", "CPH1859");
        headers.put("client", "androidapp");
        headers.put("imei", "869003037324211");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("isDeviceRooted", "false");
        headers.put("ipAddress", DeviceIP);
        headers.put("isLocationMocked", "false");
        headers.put("latitude", "28.5913173");
        headers.put("longitude", "77.3189828");
        headers.put("osVersion", "9");

        Response unOrganisedRevResp = middlewareServicesObject.RevisitUnOrgainsed(unOrgRev,query,version,AgentToken);

        Assert.assertTrue(unOrganisedRevResp.jsonPath().getString("displayMessage").contains("Enter a valid mobile number"));
    }

    @Test(description = "Fetch Revisit details for Empty Mobile")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_02_FetchShopForEmptyNumber()
    {
        RevisitUnOrganised unOrgRev = new RevisitUnOrganised("");

        Map<String,String > query = new HashMap<>();

        Response unOrganisedRevResp = middlewareServicesObject.RevisitUnOrgainsed(unOrgRev,query,version,AgentToken);

        Assert.assertEquals(unOrganisedRevResp.getStatusCode(),404);
    }

    @Test(description = "Fetch Revisit details for Empty Mobile")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_03_FetchShopForNonMerchantMobile()
    {
        RevisitUnOrganised unOrgRev = new RevisitUnOrganised("**********");

        Map<String,String > query = new HashMap<>();

        Response unOrganisedRevResp = middlewareServicesObject.RevisitUnOrgainsed(unOrgRev,query,version,AgentToken);
        System.out.println("Response Body:"+unOrganisedRevResp.body().prettyPrint());
        Assert.assertTrue(unOrganisedRevResp.jsonPath().getString("displayMessage").contains("Revisit flow is not available for this user as user don't have any MID/P2P merchant account"));
    }


    @Test(priority = 0,description = "Fetch Revisit Details for Mobile Number",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_04_FetchShopDetailsRevisitUnOrganised()
    {
        RevisitUnOrganised unOrgRev = new RevisitUnOrganised(MobileNo);

        Map<String,String > query = new HashMap<>();

        Response unOrganisedRevResp = middlewareServicesObject.RevisitUnOrgainsed(unOrgRev,query,version,AgentToken);
        System.out.println("Response Body:"+unOrganisedRevResp.getBody().prettyPrint());

        Map SolAddMap = unOrganisedRevResp.jsonPath().getMap("solutionAddressMap");

        Set solutionType =SolAddMap.keySet();
        LOGGER.info("Set of SolutionType : " +solutionType.toString());

        ArrayList getSolType = new ArrayList<>(solutionType);
        SolutionType = getSolType.get(0).toString();
        LOGGER.info("Solution Type is :" +SolutionType);

        List SolutionDetails = (List) SolAddMap.get(SolutionType);
        LOGGER.info("Solution Details as LIST :" +SolutionDetails);

        Map FinalDataExtract = (Map) SolutionDetails.get(0);

        CustId = unOrganisedRevResp.jsonPath().getJsonObject("custId").toString();
        UserMID = FinalDataExtract.get("mid").toString(); //organisedRevResp.jsonPath().getJsonObject("solutionAddressMap.p2p_100k[0].mid").toString();
        FseRole = FinalDataExtract.get("fseRole").toString();
        FseSubRole = FinalDataExtract.get("fseSubRole").toString();
        SubSol = FinalDataExtract.get("solutionSubType").toString();

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_01_FetchQnaRevisitUnOrganisedWithoutSolution()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        //query.put("solutionType","revisit_merchant");
        query.put("entityType","INDIVIDUAL");
        query.put("solutionSubType",SubSol);
        query.put("questionType","additional");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","unorganized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        int StatusCode = fetchQnAresp.getStatusCode();
        Assert.assertEquals(StatusCode,400);


    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_02_FetchQnaRevisitUnOrganisedInvalidSolution()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit");
        query.put("entityType","INDIVIDUAL");
        query.put("solutionSubType",SubSol);
        query.put("questionType","additional");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","unorganized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        int StatusCode = fetchQnAresp.getStatusCode();
        Assert.assertEquals(StatusCode,500);
        Assert.assertTrue(fetchQnAresp.jsonPath().getString("message").contains("Failed to fetch questions"));

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_03_FetchQnaRevisitUnOrganisedEmptySolution()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","");
        query.put("entityType","INDIVIDUAL");
        query.put("solutionSubType",SubSol);
        query.put("questionType","additional");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","unorganized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        int StatusCode = fetchQnAresp.getStatusCode();
        Assert.assertEquals(StatusCode,500);
        Assert.assertTrue(fetchQnAresp.jsonPath().getString("message").contains("Failed to fetch questions"));

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_04_FetchQnaRevisitUnOrganisedDifferentSolution()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","p2p_100k");
        query.put("entityType","INDIVIDUAL");
        query.put("solutionSubType",SubSol);
        query.put("questionType","additional");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","unorganized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        int StatusCode = fetchQnAresp.getStatusCode();
        Assert.assertEquals(StatusCode,200);


    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_05_FetchQnaRevisitUnOrganisedInvalidEntity()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        query.put("entityType","INDIV");
        query.put("solutionSubType",SubSol);
        query.put("questionType","additional");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","unorganized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        int StatusCode = fetchQnAresp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_06_FetchQnaRevisitUnOrganisedEmptyEntity()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        query.put("entityType","");
        query.put("solutionSubType",SubSol);
        query.put("questionType","additional");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","unorganized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        int StatusCode = fetchQnAresp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_07_FetchQnaRevisitUnOrganisedNoEntity()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        //query.put("entityType","");
        query.put("solutionSubType",SubSol);
        query.put("questionType","additional");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","unorganized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        int StatusCode = fetchQnAresp.getStatusCode();
        Assert.assertEquals(StatusCode,400);

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_08_FetchQnaRevisitUnOrganisedDifferentEntity()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        query.put("entityType","PUBLIC_LIMITED");
        query.put("solutionSubType",SubSol);
        query.put("questionType","additional");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","unorganized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        int StatusCode = fetchQnAresp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_09_FetchQnaRevisitUnOrganisedInvalidSubSol()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        query.put("entityType","INDIVIDUAL");
        query.put("solutionSubType","SubSol");
        query.put("questionType","additional");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","unorganized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        int StatusCode = fetchQnAresp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_10_FetchQnaRevisitUnOrganisedEmptySubSol()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        query.put("entityType","INDIVIDUAL");
        query.put("solutionSubType","");
        query.put("questionType","additional");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","unorganized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        int StatusCode = fetchQnAresp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_11_FetchQnaRevisitUnOrganisedNoSubSol()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        query.put("entityType","INDIVIDUAL");
        //query.put("solutionSubType","");
        query.put("questionType","additional");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","unorganized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);
        System.out.println(fetchQnAresp.getBody().prettyPrint());
        int StatusCode = fetchQnAresp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_12_FetchQnaRevisitUnOrganisedDifferentSubSol()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        query.put("entityType","INDIVIDUAL");
        query.put("solutionSubType","edc");
        query.put("questionType","additional");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","unorganized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        int StatusCode = fetchQnAresp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_13_FetchQnaRevisitUnOrganisedInvalidQuestionType()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        query.put("entityType","INDIVIDUAL");
        query.put("solutionSubType",SubSol);
        query.put("questionType","add");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","unorganized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        int StatusCode = fetchQnAresp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_14_FetchQnaRevisitUnOrganisedEmptyQuestionType()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        query.put("entityType","INDIVIDUAL");
        query.put("solutionSubType",SubSol);
        query.put("questionType","");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","unorganized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        int StatusCode = fetchQnAresp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_15_FetchQnaRevisitUnOrganisedNoQuestionType()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        query.put("entityType","INDIVIDUAL");
        query.put("solutionSubType",SubSol);
        //query.put("questionType","");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","unorganized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        //Assert.assertEquals(fetchQnAresp.getStatusCode(),200);

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_16_FetchQnaRevisitUnOrganisedInvalidSolLvl3()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        query.put("entityType","INDIVIDUAL");
        query.put("solutionSubType",SubSol);
        query.put("questionType","additional");
        query.put("solutionTypeLevel3","unorg");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);


        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        Assert.assertEquals(fetchQnAresp.getStatusCode(),200);

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_17_FetchQnaRevisitUnOrganisedEmptySolLvl3()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        query.put("entityType","INDIVIDUAL");
        query.put("solutionSubType",SubSol);
        query.put("questionType","additional");
        query.put("solutionTypeLevel3","");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);


        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        Assert.assertEquals(fetchQnAresp.getStatusCode(),200);

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_18_FetchQnaRevisitUnOrganisedDifferentSolLvl3()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        query.put("entityType","INDIVIDUAL");
        query.put("solutionSubType",SubSol);
        query.put("questionType","additional");
        query.put("solutionTypeLevel3","organised");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);


        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        Assert.assertEquals(fetchQnAresp.getStatusCode(),200);

    }

    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_19_FetchQnaRevisitUnOrganisedNoSolLvl3()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        query.put("entityType","INDIVIDUAL");
        query.put("solutionSubType",SubSol);
        query.put("questionType","additional");
        //query.put("solutionTypeLevel3","organised");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);


        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        Assert.assertEquals(fetchQnAresp.getStatusCode(),200);

    }



    @Test(priority = 1,description = "Fetch QnA for UnOrganised Merchant",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_20_FetchQnaRevisitUnOrganised()
    {
        FetchQnA fetchQnA = new FetchQnA();

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","revisit_merchant");
        query.put("entityType","INDIVIDUAL");
        query.put("solutionSubType",SubSol);
        query.put("questionType","additional");
        query.put("fseRole",FseRole);
        query.put("fseSubRole",FseSubRole);
        query.put("solutionTypeLevel3","unorganized");

        Response fetchQnAresp = middlewareServicesObject.v1FetchQnA(fetchQnA,query,AgentToken,version);

        int StatusCode = fetchQnAresp.getStatusCode();
        Assert.assertEquals(StatusCode,200);


    }

    @Test(priority = 2,description = "Submit Revisit Details for UnOrganised Merchant",dependsOnMethods = "TC002_20_FetchQnaRevisitUnOrganised",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_01_SubmitRevisitUnOrganisedEmptyBody()
    {
        RevisitSubmit revisitSubmit = new RevisitSubmit(P.TESTDATA.get(""));

        Map<String,String> query = new HashMap<>();

        Map<String,String> body = new HashMap<>();

        Response submitRevisitRequest = middlewareServicesObject.RevisitSubmit(revisitSubmit,query,body,version,AgentToken);

        Assert.assertEquals(submitRevisitRequest.getStatusCode(),400);

    }

    @Test(priority = 2,description = "Submit Revisit Details for UnOrganised Merchant",dependsOnMethods = "TC002_20_FetchQnaRevisitUnOrganised",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_02_SubmitRevisitUnOrganisedWrongBody()
    {
        RevisitSubmit revisitSubmit = new RevisitSubmit(P.TESTDATA.get("UnOrganisedSubmit"));

        Map<String,String> query = new HashMap<>();

        Map<String,String> body = new HashMap<>();

        Response submitRevisitRequest = middlewareServicesObject.RevisitSubmit(revisitSubmit,query,body,version,AgentToken);

        Assert.assertEquals(submitRevisitRequest.getStatusCode(),200);
        Assert.assertTrue(submitRevisitRequest.jsonPath().getString("message").contains("Something is not right and we have been notified"));

    }

    @Test(priority = 2,description = "Submit Revisit Details for UnOrganised Merchant",dependsOnMethods = "TC002_20_FetchQnaRevisitUnOrganised",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_03_SubmitRevisitUnOrganisedNoCustId()
    {
        RevisitSubmit revisitSubmit = new RevisitSubmit(P.TESTDATA.get("UnOrganisedSubmit"));

        Map<String,String> query = new HashMap<>();

        Map<String,String> body = new HashMap<>();
        //body.put("custId",CustId);
        body.put("mobileNumber",MobileNo);
        body.put("solutionRevisited",SolutionType);
        body.put("shopId",ShopId);
        body.put("mid",UserMID);
        body.put("category",Category);
        body.put("kybBusinessId",KybBusinessId);
        body.put("kybContractId",KybContractId);
        body.put("kybSolution",KybSolution);
        body.put("fseRole",FseRole);
        body.put("fseSubRole",FseSubRole);

        LOGGER.info("This is Request Body Map : " +body);

        Response submitRevisitRequest = middlewareServicesObject.RevisitSubmit(revisitSubmit,query,body,version,AgentToken);
        Assert.assertEquals(submitRevisitRequest.getStatusCode(),200);
        Assert.assertTrue(submitRevisitRequest.jsonPath().getString("message").contains("Something is not right and we have been notified"));

    }

    @Test(priority = 2,description = "Submit Revisit Details for UnOrganised Merchant",dependsOnMethods = "TC002_20_FetchQnaRevisitUnOrganised",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_04_SubmitRevisitUnOrganisedInvalidCustId()
    {
        RevisitSubmit revisitSubmit = new RevisitSubmit(P.TESTDATA.get("UnOrganisedSubmit"));

        Map<String,String> query = new HashMap<>();

        Map<String,String> body = new HashMap<>();
        body.put("custId","CustId");
        body.put("mobileNumber",MobileNo);
        body.put("solutionRevisited",SolutionType);
        body.put("shopId",ShopId);
        body.put("mid",UserMID);
        body.put("category",Category);
        body.put("kybBusinessId",KybBusinessId);
        body.put("kybContractId",KybContractId);
        body.put("kybSolution",KybSolution);
        body.put("fseRole",FseRole);
        body.put("fseSubRole",FseSubRole);

        LOGGER.info("This is Request Body Map : " +body);

        Response submitRevisitRequest = middlewareServicesObject.RevisitSubmit(revisitSubmit,query,body,version,AgentToken);
        Assert.assertEquals(submitRevisitRequest.getStatusCode(),400);

    }

    @Test(priority = 2,description = "Submit Revisit Details for UnOrganised Merchant",dependsOnMethods = "TC002_20_FetchQnaRevisitUnOrganised",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_04_SubmitRevisitUnOrganisedEmptyCustId()
    {
        RevisitSubmit revisitSubmit = new RevisitSubmit(P.TESTDATA.get("UnOrganisedSubmit"));

        Map<String,String> query = new HashMap<>();

        Map<String,String> body = new HashMap<>();
        body.put("custId","");
        body.put("mobileNumber",MobileNo);
        body.put("solutionRevisited",SolutionType);
        body.put("shopId",ShopId);
        body.put("mid",UserMID);
        body.put("category",Category);
        body.put("kybBusinessId",KybBusinessId);
        body.put("kybContractId",KybContractId);
        body.put("kybSolution",KybSolution);
        body.put("fseRole",FseRole);
        body.put("fseSubRole",FseSubRole);

        LOGGER.info("This is Request Body Map : " +body);

        Response submitRevisitRequest = middlewareServicesObject.RevisitSubmit(revisitSubmit,query,body,version,AgentToken);
        Assert.assertEquals(submitRevisitRequest.getStatusCode(),200);
        Assert.assertTrue(submitRevisitRequest.jsonPath().getString("message").contains("Something is not right and we have been notified"));

    }

    @Test(priority = 2,description = "Submit Revisit Details for UnOrganised Merchant",dependsOnMethods = "TC002_20_FetchQnaRevisitUnOrganised",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_05_SubmitRevisitUnOrganisedEmptyCustId()
    {
        RevisitSubmit revisitSubmit = new RevisitSubmit(P.TESTDATA.get("UnOrganisedSubmit"));

        Map<String,String> query = new HashMap<>();

        Map<String,String> body = new HashMap<>();
        body.put("custId","");
        body.put("mobileNumber",MobileNo);
        body.put("solutionRevisited",SolutionType);
        body.put("shopId",ShopId);
        body.put("mid",UserMID);
        body.put("category",Category);
        body.put("kybBusinessId",KybBusinessId);
        body.put("kybContractId",KybContractId);
        body.put("kybSolution",KybSolution);
        body.put("fseRole",FseRole);
        body.put("fseSubRole",FseSubRole);

        LOGGER.info("This is Request Body Map : " +body);

        Response submitRevisitRequest = middlewareServicesObject.RevisitSubmit(revisitSubmit,query,body,version,AgentToken);
        System.out.println("Response:"+submitRevisitRequest.getBody().prettyPrint());
        Assert.assertEquals(submitRevisitRequest.getStatusCode(),200);
        Assert.assertTrue(submitRevisitRequest.jsonPath().getString("message").contains("Something is not right and we have been notified"));

    }

    @Test(priority = 2,description = "Submit Revisit Details for UnOrganised Merchant",dependsOnMethods = "TC002_20_FetchQnaRevisitUnOrganised",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_06_SubmitRevisitUnOrganisedNoMobile()
    {
        RevisitSubmit revisitSubmit = new RevisitSubmit(P.TESTDATA.get("UnOrganisedSubmit"));

        Map<String,String> query = new HashMap<>();

        Map<String,String> body = new HashMap<>();
        body.put("custId",CustId);
        //body.put("mobileNumber",MobileNo);
        body.put("solutionRevisited",SolutionType);
        body.put("shopId",ShopId);
        body.put("mid",UserMID);
        body.put("kybBusinessId",KybBusinessId);
        body.put("kybContractId",KybContractId);
        body.put("kybSolution",KybSolution);
        body.put("fseRole",FseRole);
        body.put("fseSubRole",FseSubRole);

        LOGGER.info("This is Request Body Map : " +body);

        Response submitRevisitRequest = middlewareServicesObject.RevisitSubmit(revisitSubmit,query,body,version,AgentToken);
        Assert.assertEquals(submitRevisitRequest.getStatusCode(),200);
        Assert.assertTrue(submitRevisitRequest.jsonPath().getString("message").contains("Something is not right and we have been notified"));

    }

   /* @Test(priority = 0,description = "Submit Revisit Details for UnOrganised Merchant",dependsOnMethods = "TC002_20_FetchQnaRevisitUnOrganised",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_07_SubmitRevisitUnOrganisedInvalidMobile()
    {
        RevisitSubmit revisitSubmit = new RevisitSubmit(P.TESTDATA.get("UnOrganisedSubmit"));

        Map<String,String> query = new HashMap<>();

        Map<String,String> body = new HashMap<>();
        body.put("custId",CustId);
        body.put("mobileNumber","MobileNo");
        body.put("solutionRevisited",SolutionType);
        body.put("shopId",ShopId);
        body.put("mid",UserMID);
        body.put("kybBusinessId",KybBusinessId);
        body.put("kybContractId",KybContractId);
        body.put("kybSolution",KybSolution);
        body.put("fseRole",FseRole);
        body.put("fseSubRole",FseSubRole);

        LOGGER.info("This is Request Body Map : " +body);

        Response submitRevisitRequest = middlewareServicesObject.RevisitSubmit(revisitSubmit,query,body,version,AgentToken);

    }

    @Test(priority = 0,description = "Submit Revisit Details for UnOrganised Merchant",dependsOnMethods = "TC002_20_FetchQnaRevisitUnOrganised",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_08_SubmitRevisitUnOrganisedNoMid()
    {
        RevisitSubmit revisitSubmit = new RevisitSubmit(P.TESTDATA.get("UnOrganisedSubmit"));

        Map<String,String> query = new HashMap<>();

        Map<String,String> body = new HashMap<>();
        body.put("custId",CustId);
        body.put("mobileNumber",MobileNo);
        body.put("solutionRevisited",SolutionType);
        body.put("shopId",ShopId);
        //body.put("mid",UserMID);
        body.put("kybBusinessId",KybBusinessId);
        body.put("kybContractId",KybContractId);
        body.put("kybSolution",KybSolution);
        body.put("fseRole",FseRole);
        body.put("fseSubRole",FseSubRole);

        LOGGER.info("This is Request Body Map : " +body);

        Response submitRevisitRequest = middlewareServicesObject.RevisitSubmit(revisitSubmit,query,body,version,AgentToken);

    }

    @Test(priority = 0,description = "Submit Revisit Details for UnOrganised Merchant",dependsOnMethods = "TC002_20_FetchQnaRevisitUnOrganised",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_09_SubmitRevisitUnOrganisedEmptyMid()
    {
        RevisitSubmit revisitSubmit = new RevisitSubmit(P.TESTDATA.get("UnOrganisedSubmit"));

        Map<String,String> query = new HashMap<>();

        Map<String,String> body = new HashMap<>();
        body.put("custId",CustId);
        body.put("mobileNumber",MobileNo);
        body.put("solutionRevisited",SolutionType);
        body.put("shopId",ShopId);
        body.put("mid","");
        body.put("kybBusinessId",KybBusinessId);
        body.put("kybContractId",KybContractId);
        body.put("kybSolution",KybSolution);
        body.put("fseRole",FseRole);
        body.put("fseSubRole",FseSubRole);

        LOGGER.info("This is Request Body Map : " +body);

        Response submitRevisitRequest = middlewareServicesObject.RevisitSubmit(revisitSubmit,query,body,version,AgentToken);

    }
*/
    @Test(priority = 2,description = "Submit Revisit Details for ShopId",dependsOnMethods = "TC002_20_FetchQnaRevisitUnOrganised",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_07_SubmitRevisitUnOrganised()
    {
        RevisitSubmit revisitSubmit = new RevisitSubmit(P.TESTDATA.get("UnOrganisedSubmit"));

        Map<String,String> query = new HashMap<>();

        Map<String,String> body = new HashMap<>();
        body.put("custId",CustId);
        body.put("mobileNumber",MobileNo);
        body.put("solutionRevisited",SolutionType);
        body.put("shopId",ShopId);
        body.put("mid",UserMID);
        body.put("kybBusinessId",KybBusinessId);
        body.put("kybContractId",KybContractId);
        body.put("kybSolution",KybSolution);
        body.put("fseRole",FseRole);
        body.put("fseSubRole",FseSubRole);

        LOGGER.info("This is Request Body Map : " +body);
        System.out.println("This is Request Body Map : " +body);

        Response submitRevisitRequest = middlewareServicesObject.RevisitSubmit(revisitSubmit,query,body,version,AgentToken);
        System.out.println(submitRevisitRequest.getBody().prettyPrint());
        leadId = submitRevisitRequest.jsonPath().getJsonObject("leadId").toString();
        LOGGER.info("This is lead Id of Organised Revisit : " +leadId);
    }

    @Test(priority = 3,description = "Positive Fetch and Submit Documents for Revisit Organised",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_FetchAndSubmitDocRevisitUnOrganised() throws JsonProcessingException
    {
        Map<String,String>queryDocUpload = new HashMap<>();
        queryDocUpload.put("type", "jpg");
        queryDocUpload.put("entityType", "INDIVIDUAL");
        queryDocUpload.put("solutionType", "revisit_merchant");
        queryDocUpload.put("merchantCustId", CustId);
        queryDocUpload.put("leadId", leadId);
        queryDocUpload.put("solutionTypeLevel2",SubSol);
        queryDocUpload.put("solutionTypeLevel3","unorganized");
        queryDocUpload.put("fseRole",FseRole);
        queryDocUpload.put("fseSubRole",FseSubRole);

        Map<String,String>queryFetchDoc = new HashMap<>();
        queryFetchDoc.put("entityType", "INDIVIDUAL");
        queryFetchDoc.put("solution", "revisit_merchant");
        queryFetchDoc.put("leadId", leadId);
        queryFetchDoc.put("channel", "GG_APP");
        queryFetchDoc.put("merchantCustId", CustId);
        queryFetchDoc.put("solutionSubType",SubSol);
        queryFetchDoc.put("category",Category);
        queryFetchDoc.put("solutionTypeLevel3","unorganized");
        queryFetchDoc.put("fseRole",FseRole);
        queryFetchDoc.put("fseSubRole",FseSubRole);

        GgAppDynamicDocUpload(AgentToken,version,MobileNo,queryFetchDoc,queryDocUpload);

    }

//    @Test(priority = 4,description = "Fetch Lead Details on Panel",groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>",isAutomated = true)
//    public void TC005_PositiveFetchLeadPanelRevisitUnOrganised() throws JsonProcessingException
//    {
//        Map <String,String> RequestPanel = new HashMap<>();
//
//        RequestPanel.put("docStatus",OePanelDocStatus);
//        RequestPanel.put("rejectionReason",RejectionReason);
//        RequestPanel.put("leadId",leadId);
//
//        Map <String,String> ResponsePanel = FetchPanelLead(RequestPanel);
//
//        DocumetRequestDeserialised = ResponsePanel.get("DocumenstArray");
//        WorkFlowId = ResponsePanel.get("WorkFlowId");
//        LOGGER. info("Lead Stage is : " + ResponsePanel.get("LeadStage"));
//        LeadStage = ResponsePanel.get("LeadStage");
//
//    }
//    @Test(priority = 5,description = "Submit rejected lead from OE panel ",groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>",isAutomated = true)
//    public void TC006_PositiveSubmitLeadPanelUnOrganised()
//    {
//        if (LeadStage.equals("LEAD_SUCCESSFULLY_CLOSED"))
//        {
//            LOGGER.info("Lead is Already Closed");
//        }
//
//        else {
//            ReallocatingAgent(leadId, "1152");
//            EditLead v1EditLeadObj = new EditLead(leadId, P.TESTDATA.get("EditLeadRevisitUnOrganised"));
//
//            v1EditLeadObj.getProperties().setProperty("documents", DocumetRequestDeserialised);
//            v1EditLeadObj.getProperties().setProperty("custId", CustId);
//            v1EditLeadObj.getProperties().setProperty("pgMID", UserMID);
//
//            Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "9560526665", "1106992015", XMWCookie, "application/json");
//
//            int statusCode = responseObject.getStatusCode();
//            Assert.assertEquals(statusCode, 200);
//
//            String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
//            Assert.assertTrue(LeadSatge.contains("PANEL_SUCCESS"));
//        }
//    }

}