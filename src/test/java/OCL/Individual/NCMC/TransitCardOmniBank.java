package OCL.Individual.NCMC;
import java.util.Random;
import OCL.Individual.SoundBox.FlowSoundBox;
import Services.MechantService.MiddlewareServices;
import TestingLogics.TEST;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import Request.MerchantService.v1.NCMC.*;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import OCL.Individual.SoundBox.FlowSoundBox;
import TestingLogics.TEST;
import com.paytm.apitools.util.annotations.Owner;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import com.goldengate.common.BaseMethod;


import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

import java.util.Map;

public class TransitCardOmniBank extends BaseMethod {
    public String docType = "PAN";
    public String docNo = "**********";
    public String firstName = "Kartikeya";
    public String lastName = "Tiwari";

    public String sessionToken = "";
    public Response response;
    public String version = "5.3.1";
    public String workflowid = "";
    public String productId = "";
    public String cardPrice = "" ;
    public  String topupPrice = "";
    public String phone_number = "**********";
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
   // private static final org.apache.log4j.Logger LOGGER = org.apache.log4j.Logger.getLogger(FlowSoundBox.class);

    private static final org.apache.logging.log4j.Logger LOGGER = org.apache.logging.log4j.LogManager.getLogger(FlowSoundBox.class);
    TEST obj=new TEST();
    private String stateId = "";
    private String otp = "888888";
    private String lead_id = "";
    private String solutionType = "ncmc_card_issuance";
    private String entityType = "INDIVIDUAL";
    private String session_id = "";
    private String qrcode = "281005010101191UFVUOP1A0";
    private String cartOrderId = "";
    private String custId = "";

    @BeforeClass
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLoginSoundBox() {
         sessionToken = AgentSessionToken("8010630022", "paytm@123");
        LOGGER.info("Agent Token  for Soundbox : " + sessionToken);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Get Product Lists")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void get_product_list_ncmc_omni(){

        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        GetNcmcProducts productobj = new GetNcmcProducts();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua" , "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "IPgvY/VIvXuixQxUSE+KGkLqYO33HVJAPG90BA1kf1pbOihHvuMehK+0dfwUxJ0/B7J4w1RHDwmFqy3RLJqYuTI1VMY6Ti82v1VdZO76iu+Llkn748jBolUE1aqMro5n");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.43 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging14.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging14.paytm.com/");
        headers.put("accept-language" , "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        Response respobj = middlewareServicesObject.getNcmcproducts(productobj , headers);
        int statuscode = respobj.getStatusCode();
        Assert.assertEquals(statuscode , 200);
        productId = respobj.jsonPath().getString("productList[1].productId").toString();
        cardPrice = respobj.jsonPath().getString("productList[1].cardPrice").toString();
        topupPrice = respobj.jsonPath().getString("productList[1].topupPrice").toString();
        LOGGER.info(productId + "  "  +topupPrice+ "  "  + cardPrice);
    }

    @Test(priority = 2, groups = {"Regression"}, description = "Send OTP ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void send_ncmc_otp() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        // headers.put("sec-ch-ua" , "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        //  headers.put("x-mw-url-checksum", "Dq5LZe0mzumRUxZQ5DzN05uyI3/Jq7oXmtdeAG7jHV6ldx/oOgs4kbmV8Prfp+5tHg+2dMa2IEEow1W5TWtsN1f6EUUo7AJTlG8K5K0hLKdJblprIiDd8bL2owyi2CaZ");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        // headers.put("x-mw-checksum", "luA4xYSC2RO1folxHFHPO9oQvTNroUWu/7BW2lszIbT7w3uWO7jr4V1+OfpvAfUimK0wNfz9u+0OJPdM6pTsqIuF8Qia26HpHh3k0WVsVOLf0CUXCyCfA3afajCklzm9");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.144 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("phoneNumber", phone_number);
        body.put("productId", productId); // Use the productId obtained from the previous test
        body.put("cardAmount", cardPrice);
        body.put("topupAmount", topupPrice);
        body.put("cardType", "");
        body.put("issueNonPPBL", "false");
        ncmcsendotp obj = new ncmcsendotp();
        Response respobj2 = middlewareServicesObject.sendOTP(obj, headers, body);

        // Validate the response
        int statusCode = respobj2.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        productId = respobj2.jsonPath().getString("productDetails[0].productId").toString();
        cardPrice = respobj2.jsonPath().getString("productDetails[0].cardPrice").toString();
        topupPrice = respobj2.jsonPath().getString("productDetails[0].topupPrice").toString();
    }

    @Test(priority = 2, groups = {"Regression"}, description = "Send OTP ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void send_ncmc_otp_omni() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        // headers.put("sec-ch-ua" , "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        //  headers.put("x-mw-url-checksum", "Dq5LZe0mzumRUxZQ5DzN05uyI3/Jq7oXmtdeAG7jHV6ldx/oOgs4kbmV8Prfp+5tHg+2dMa2IEEow1W5TWtsN1f6EUUo7AJTlG8K5K0hLKdJblprIiDd8bL2owyi2CaZ");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        // headers.put("x-mw-checksum", "luA4xYSC2RO1folxHFHPO9oQvTNroUWu/7BW2lszIbT7w3uWO7jr4V1+OfpvAfUimK0wNfz9u+0OJPdM6pTsqIuF8Qia26HpHh3k0WVsVOLf0CUXCyCfA3afajCklzm9");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.144 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("phoneNumber", phone_number);
        body.put("productId", productId); // Use the productId obtained from the previous test
        body.put("cardAmount", cardPrice);
        body.put("topupAmount", topupPrice);
        body.put("cardType", "OMNI");
        body.put("issueNonPPBL", "true");
        ncmcsendotp obj = new ncmcsendotp();
        Response respobj2 = middlewareServicesObject.sendOTP(obj, headers, body);

        // Validate the response
        int statusCode = respobj2.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        stateId = respobj2.jsonPath().getString("stateId").toString();

    }

    @Test(priority = 3 , groups = {"Regression"}, description = "Validate NCMC OTP")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void validate_ncmc_otp_omni() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        //   headers.put("x-mw-url-checksum", "uvivt6CLrbmeSKJ3tIPJt3tXAQNHDif7GasH1WzUq7vPZzbSzzFex10KpdVPDfzD6DoAsjLvES8+AQ0wGi8TwQQu1XkWxm7kMZ+0g5XGFuTbVHzj3fiFXOSOgqHBB/uL");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        //  headers.put("x-mw-checksum", "KYZr4in6D+P9tGYhu9Yt6c+cCUWhystuFnGFT78DplElbldIW8lG8q1/wyS3V9Zryy3QKtNnAsKb1hZUYfqPMpUo7PrWq0+SzdYz+wq/8lHwT4W0YxdKB03+zhWzGcQt");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.144 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("phoneNumber", phone_number);
        body.put("workflowId", workflowid); // Use the workflowId obtained from the sendOTP method
        body.put("stateId", stateId); // Use the stateId obtained from the sendOTP method
        body.put("otp", otp);
        body.put("productId", productId);
        body.put("cardAmount", cardPrice);
        body.put("topupAmount", topupPrice);
        body.put("leadId", "");
        body.put("cardType", "OMNI");
        ncmcvalidateotp obj = new ncmcvalidateotp();
        Response respobj3 = middlewareServicesObject.validateOTP(obj , headers, body);

        // Validate the response
        int statusCode = respobj3.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        lead_id = respobj3.jsonPath().getString("leadId").toString();
        session_id = respobj3.jsonPath().getString("sessionId").toString();
        custId = respobj3.jsonPath().getString("custId").toString();

    }
    @Test(priority = 4 , groups = {"Regression"}, description = "Submit Kyc for Omni")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void submit_kyc_omni(){
        submitKyc obj = new submitKyc();
        Response objresp = middlewareServicesObject.submitKycomni(obj , sessionToken  , version, lead_id , session_id , docType , docNo , firstName , lastName);
        int StatusCode = objresp.statusCode();
        Assert.assertEquals(StatusCode , 200);
    }


}
