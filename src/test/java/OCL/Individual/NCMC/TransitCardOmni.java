package OCL.Individual.NCMC;
import Request.MerchantService.v1.NCMC.*;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import OCL.Individual.SoundBox.FlowSoundBox;
import TestingLogics.TEST;
import com.paytm.apitools.util.annotations.Owner;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import com.goldengate.common.BaseMethod;


import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;
// We are inheriting basemethod in order to use its methods too such as AgentSessionToken
public class TransitCardOmni extends BaseMethod {

    //Declaring a variable named as session token to take input the agent session token
    public String sessionToken = "eyJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiZGlyIn0..S56v2Yen2n_0ke93._y-GsZdI95eM5L2sXs2Pjtc6hHZHABqqzOjEDuOE60xdPjSgn9f0ntRGudjDNiUaeBtEqsLXr9KuS8jcdgRsWkN02Kj127rJJDRirvan3LLbyea2KLzxdjrnwfHBqIMSiv-PE7sV-gd2iCdXy_zivkf602g33_m9ylDwVK-iMQEUZQbFPG8d8-2xGoXTpAzdeNXdM1iSjwScEu-avwZbaMHtkHijezxQ0GxNnyMUFJghMJsrxu5t6_nQ9PzUPnbFAA00MYT06m_oABTwvy0.wDX7xgWY20vnGTEP8ZnYEQ7500";
    public Response response;
    public String version = "5.3.1";
    public String workflowid = "";
    public String productId = "";
    public Map<String,String> headers;
    public Map<String, String> body;
    public String cardPrice = "" ;
    public  String topupPrice = "";
    public String phone_number = "5759878063";
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
   // private static final org.apache.log4j.Logger LOGGER = org.apache.log4j.Logger.getLogger(FlowSoundBox.class);

    private static final org.apache.logging.log4j.Logger LOGGER = org.apache.logging.log4j.LogManager.getLogger(FlowSoundBox.class);

    TEST obj=new TEST();
    private String stateId = "";
    private String otp = "888888";
    private String lead_id = "";
    private String solutionType = "ncmc_card_issuance";
    private String entityType = "INDIVIDUAL";
    private String session_id = "";
    private String qrcode = "281005010101191UFVUOP1A0";
    private String cartOrderId = "";

    // we made this to call each an
    @BeforeClass
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLoginSoundBox() {
       // sessionToken = AgentSessionToken("7771216290", "paytm@123");
        LOGGER.info("Agent Token  for Soundbox : " + sessionToken);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get Product Lists")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void get_product_list_ncmc(){

        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        GetNcmcProducts productobj = new GetNcmcProducts();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua" , "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "IPgvY/VIvXuixQxUSE+KGkLqYO33HVJAPG90BA1kf1pbOihHvuMehK+0dfwUxJ0/B7J4w1RHDwmFqy3RLJqYuTI1VMY6Ti82v1VdZO76iu+Llkn748jBolUE1aqMro5n");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.43 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging14.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging14.paytm.com/");
        headers.put("accept-language" , "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        Response respobj = middlewareServicesObject.getNcmcproducts(productobj , headers);
        int statuscode = respobj.getStatusCode();
        Assert.assertEquals(statuscode , 200);
        productId = respobj.jsonPath().getString("productList[1].productId").toString();
        cardPrice = respobj.jsonPath().getString("productList[1].cardPrice").toString();
        topupPrice = respobj.jsonPath().getString("productList[1].topupPrice").toString();
        LOGGER.info(productId + "  "  +topupPrice+ "  "  + cardPrice);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get Product Lists")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void get_product_list_ncmc_sessiontokenempty(){

        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        GetNcmcProducts productobj = new GetNcmcProducts();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua" , "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "IPgvY/VIvXuixQxUSE+KGkLqYO33HVJAPG90BA1kf1pbOihHvuMehK+0dfwUxJ0/B7J4w1RHDwmFqy3RLJqYuTI1VMY6Ti82v1VdZO76iu+Llkn748jBolUE1aqMro5n");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", "");
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.43 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging14.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging14.paytm.com/");
        headers.put("accept-language" , "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        Response respobj = middlewareServicesObject.getNcmcproducts(productobj , headers);
        int statuscode = respobj.getStatusCode();
        Assert.assertEquals(statuscode , 401);
//        productId = respobj.jsonPath().getString("productList[0].productId").toString();
//        cardPrice = respobj.jsonPath().getString("productList[0].cardPrice").toString();
//        topupPrice = respobj.jsonPath().getString("productList[0].topupPrice").toString();
//        LOGGER.info(productId + "  "  +topupPrice+ "  "  + cardPrice);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get Product Lists")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void get_product_list_ncmc_version_old(){

        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        GetNcmcProducts productobj = new GetNcmcProducts();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua" , "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "IPgvY/VIvXuixQxUSE+KGkLqYO33HVJAPG90BA1kf1pbOihHvuMehK+0dfwUxJ0/B7J4w1RHDwmFqy3RLJqYuTI1VMY6Ti82v1VdZO76iu+Llkn748jBolUE1aqMro5n");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.43 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", "1.2.3");
        headers.put("origin", "https://oe-staging14.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging14.paytm.com/");
        headers.put("accept-language" , "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        Response respobj = middlewareServicesObject.getNcmcproducts(productobj , headers);
        int statuscode = respobj.getStatusCode();
        Assert.assertEquals(statuscode , 200);
//        productId = respobj.jsonPath().getString("productList[0].productId").toString();
//        cardPrice = respobj.jsonPath().getString("productList[0].cardPrice").toString();
//        topupPrice = respobj.jsonPath().getString("productList[0].topupPrice").toString();
//        LOGGER.info(productId + "  "  +topupPrice+ "  "  + cardPrice);
    }


    @Test(priority = 1, groups = {"Regression"}, description = "Get Product Lists")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void get_product_list_ncmc_checksum_missing(){

        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        GetNcmcProducts productobj = new GetNcmcProducts();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua" , "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "IPgvY/VIvXuixQxUSE+KGkLqYO33HVJAPG90BA1kf1pbOihHvuMehK+0dfwUxJ0/B7J4w1RHDwmFqy3RLJqYuTI1VMY6Ti82v1VdZO76iu+Llkn748jBolUE1aqMro5n");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.43 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging14.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging14.paytm.com/");
        headers.put("accept-language" , "en-IN,en-US;q=0.9,en;q=0.8");
      //  headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        Response respobj = middlewareServicesObject.getNcmcproducts(productobj , headers);
        int statuscode = respobj.getStatusCode();
        Assert.assertEquals(statuscode , 200);
//        productId = respobj.jsonPath().getString("productList[0].productId").toString();
//        cardPrice = respobj.jsonPath().getString("productList[0].cardPrice").toString();
//        topupPrice = respobj.jsonPath().getString("productList[0].topupPrice").toString();
//        LOGGER.info(productId + "  "  +topupPrice+ "  "  + cardPrice);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get Product Lists")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void get_product_list_ncmc_checksum_random(){

        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        GetNcmcProducts productobj = new GetNcmcProducts();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua" , "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "IPgvY/VIvXuixQxUSE+KGkLqYO33HVJAPG90BA1kf1pbOihHvuMehK+0dfwUxJ0/B7J4w1RHDwmFqy3RLJqYuTI1VMY6Ti82v1VdZO76iu+Llkn748jBolUE1aqMro5n");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.43 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging14.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging14.paytm.com/");
        headers.put("accept-language" , "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep12345");
        Response respobj = middlewareServicesObject.getNcmcproducts(productobj , headers);
        int statuscode = respobj.getStatusCode();
        Assert.assertEquals(statuscode , 200);
//        productId = respobj.jsonPath().getString("productList[1].productId").toString();
//        cardPrice = respobj.jsonPath().getString("productList[1].cardPrice").toString();
//        topupPrice = respobj.jsonPath().getString("productList[1].topupPrice").toString();
//        LOGGER.info(productId + "  "  +topupPrice+ "  "  + cardPrice);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Get Product Lists")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void get_product_list_ncmc_version_empty(){

        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        GetNcmcProducts productobj = new GetNcmcProducts();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua" , "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "IPgvY/VIvXuixQxUSE+KGkLqYO33HVJAPG90BA1kf1pbOihHvuMehK+0dfwUxJ0/B7J4w1RHDwmFqy3RLJqYuTI1VMY6Ti82v1VdZO76iu+Llkn748jBolUE1aqMro5n");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.43 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", "");
        headers.put("origin", "https://oe-staging14.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging14.paytm.com/");
        headers.put("accept-language" , "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        Response respobj = middlewareServicesObject.getNcmcproducts(productobj , headers);
        int statuscode = respobj.getStatusCode();
        Assert.assertEquals(statuscode , 200);
//        productId = respobj.jsonPath().getString("productList[1].productId").toString();
//        cardPrice = respobj.jsonPath().getString("productList[1].cardPrice").toString();
//        topupPrice = respobj.jsonPath().getString("productList[1].topupPrice").toString();
//        LOGGER.info(productId + "  "  +topupPrice+ "  "  + cardPrice);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Get Product Lists")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void get_product_list_ncmc_session_token_random(){

        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        GetNcmcProducts productobj = new GetNcmcProducts();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua" , "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "IPgvY/VIvXuixQxUSE+KGkLqYO33HVJAPG90BA1kf1pbOihHvuMehK+0dfwUxJ0/B7J4w1RHDwmFqy3RLJqYuTI1VMY6Ti82v1VdZO76iu+Llkn748jBolUE1aqMro5n");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", "random");
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.43 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging14.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging14.paytm.com/");
        headers.put("accept-language" , "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        Response respobj = middlewareServicesObject.getNcmcproducts(productobj , headers);
        int statuscode = respobj.getStatusCode();
        Assert.assertEquals(statuscode , 410);
//        productId = respobj.jsonPath().getString("productList[1].productId").toString();
//        cardPrice = respobj.jsonPath().getString("productList[1].cardPrice").toString();
//        topupPrice = respobj.jsonPath().getString("productList[1].topupPrice").toString();
//        LOGGER.info(productId + "  "  +topupPrice+ "  "  + cardPrice);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get Product Lists")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void get_product_list_ncmc_headers_empty(){

        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        GetNcmcProducts productobj = new GetNcmcProducts();
//        headers.put("Host" , "goldengate-staging5.paytm.com");
//        headers.put("sec-ch-ua" , "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
//        headers.put("devicemac", "60:6E:E8:D6:95:DB");
//        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
//        headers.put("client", "androidapp");
//        headers.put("x-mw-url-checksum", "IPgvY/VIvXuixQxUSE+KGkLqYO33HVJAPG90BA1kf1pbOihHvuMehK+0dfwUxJ0/B7J4w1RHDwmFqy3RLJqYuTI1VMY6Ti82v1VdZO76iu+Llkn748jBolUE1aqMro5n");
//        headers.put("androidid", "6db875100ed7d7a4");
//        headers.put("osversion", "10");
//        headers.put("x-src", "GGClient");
//        headers.put("devicemanufacturer", "Xiaomi");
//        headers.put("applanguage", "en");
//        headers.put("sec-ch-ua-platform", "\"Android\"");
//        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
//        headers.put("devicename", "M2004J19C");
//        headers.put("sec-ch-ua-mobile", "?1");
//        headers.put("ipaddress", "************");
//        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.43 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
//        headers.put("isbusyboxfound", "false");
//        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
//        headers.put("origin", "https://oe-staging14.paytm.com");
//        headers.put("x-requested-with", "com.paytm.goldengate.debug");
//        headers.put("sec-fetch-site" , "same-site");
//        headers.put("sec-fetch-mode" , "cors");
//        headers.put("sec-fetch-dest" , "empty");
//        headers.put("referer" , "https://oe-staging14.paytm.com/");
//        headers.put("accept-language" , "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        Response respobj = middlewareServicesObject.getNcmcproducts(productobj , headers);
        int statuscode = respobj.getStatusCode();
        Assert.assertEquals(statuscode , 410);
//        productId = respobj.jsonPath().getString("productList[1].productId").toString();
//        cardPrice = respobj.jsonPath().getString("productList[1].cardPrice").toString();
//        topupPrice = respobj.jsonPath().getString("productList[1].topupPrice").toString();
//        LOGGER.info(productId + "  "  +topupPrice+ "  "  + cardPrice);
    }





    @Test(priority = 2, groups = {"Regression"}, description = "Send OTP ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void send_ncmc_otp() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        // headers.put("sec-ch-ua" , "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        //  headers.put("x-mw-url-checksum", "Dq5LZe0mzumRUxZQ5DzN05uyI3/Jq7oXmtdeAG7jHV6ldx/oOgs4kbmV8Prfp+5tHg+2dMa2IEEow1W5TWtsN1f6EUUo7AJTlG8K5K0hLKdJblprIiDd8bL2owyi2CaZ");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        // headers.put("x-mw-checksum", "luA4xYSC2RO1folxHFHPO9oQvTNroUWu/7BW2lszIbT7w3uWO7jr4V1+OfpvAfUimK0wNfz9u+0OJPdM6pTsqIuF8Qia26HpHh3k0WVsVOLf0CUXCyCfA3afajCklzm9");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.144 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("phoneNumber", phone_number);
        body.put("productId", productId); // Use the productId obtained from the previous test
        body.put("cardAmount", cardPrice);
        body.put("topupAmount", topupPrice);
        body.put("cardType", "");
        body.put("issueNonPPBL", "false");
        ncmcsendotp obj = new ncmcsendotp();
        Response respobj2 = middlewareServicesObject.sendOTP(obj, headers, body);

        // Validate the response
        int statusCode = respobj2.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        workflowid = respobj2.jsonPath().getString("workflowId").toString();
        stateId = respobj2.jsonPath().getString("stateId").toString();
    }

    @Test(priority = 2, groups = {"Regression"}, description = "Send OTP ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void send_ncmc_otp_random_number() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        // headers.put("sec-ch-ua" , "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        //  headers.put("x-mw-url-checksum", "Dq5LZe0mzumRUxZQ5DzN05uyI3/Jq7oXmtdeAG7jHV6ldx/oOgs4kbmV8Prfp+5tHg+2dMa2IEEow1W5TWtsN1f6EUUo7AJTlG8K5K0hLKdJblprIiDd8bL2owyi2CaZ");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        // headers.put("x-mw-checksum", "luA4xYSC2RO1folxHFHPO9oQvTNroUWu/7BW2lszIbT7w3uWO7jr4V1+OfpvAfUimK0wNfz9u+0OJPdM6pTsqIuF8Qia26HpHh3k0WVsVOLf0CUXCyCfA3afajCklzm9");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.144 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("phoneNumber", "8858826212");
        body.put("productId", productId); // Use the productId obtained from the previous test
        body.put("cardAmount", cardPrice);
        body.put("topupAmount", topupPrice);
        body.put("cardType", "");
        body.put("issueNonPPBL", "false");
        ncmcsendotp obj = new ncmcsendotp();
        Response respobj2 = middlewareServicesObject.sendOTP(obj, headers, body);

        // Validate the response
        int statusCode = respobj2.getStatusCode();
        Assert.assertEquals(statusCode, 500);
//
//        workflowid = respobj2.jsonPath().getString("workflowId").toString();
//        stateId = respobj2.jsonPath().getString("stateId").toString();
    }

    @Test(priority = 2, groups = {"Regression"}, description = "Send OTP ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void send_ncmc_otp_version_failure() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        // headers.put("sec-ch-ua" , "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        //  headers.put("x-mw-url-checksum", "Dq5LZe0mzumRUxZQ5DzN05uyI3/Jq7oXmtdeAG7jHV6ldx/oOgs4kbmV8Prfp+5tHg+2dMa2IEEow1W5TWtsN1f6EUUo7AJTlG8K5K0hLKdJblprIiDd8bL2owyi2CaZ");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        // headers.put("x-mw-checksum", "luA4xYSC2RO1folxHFHPO9oQvTNroUWu/7BW2lszIbT7w3uWO7jr4V1+OfpvAfUimK0wNfz9u+0OJPdM6pTsqIuF8Qia26HpHh3k0WVsVOLf0CUXCyCfA3afajCklzm9");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.144 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", "1.0.2");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("phoneNumber", phone_number);
        body.put("productId", productId); // Use the productId obtained from the previous test
        body.put("cardAmount", cardPrice);
        body.put("topupAmount", topupPrice);
        body.put("cardType", "");
        body.put("issueNonPPBL", "false");
        ncmcsendotp obj = new ncmcsendotp();
        Response respobj2 = middlewareServicesObject.sendOTP(obj, headers, body);

        // Validate the response
        int statusCode = respobj2.getStatusCode();
        Assert.assertEquals(statusCode, 200);
//
//        workflowid = respobj2.jsonPath().getString("workflowId").toString();
//        stateId = respobj2.jsonPath().getString("stateId").toString();
    }

    @Test(priority = 2, groups = {"Regression"}, description = "Send OTP ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void send_ncmc_otp_phone_number_shorter_length() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        // headers.put("sec-ch-ua" , "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        //  headers.put("x-mw-url-checksum", "Dq5LZe0mzumRUxZQ5DzN05uyI3/Jq7oXmtdeAG7jHV6ldx/oOgs4kbmV8Prfp+5tHg+2dMa2IEEow1W5TWtsN1f6EUUo7AJTlG8K5K0hLKdJblprIiDd8bL2owyi2CaZ");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        // headers.put("x-mw-checksum", "luA4xYSC2RO1folxHFHPO9oQvTNroUWu/7BW2lszIbT7w3uWO7jr4V1+OfpvAfUimK0wNfz9u+0OJPdM6pTsqIuF8Qia26HpHh3k0WVsVOLf0CUXCyCfA3afajCklzm9");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.144 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("phoneNumber", "1234");
        body.put("productId", productId); // Use the productId obtained from the previous test
        body.put("cardAmount", cardPrice);
        body.put("topupAmount", topupPrice);
        body.put("cardType", "");
        body.put("issueNonPPBL", "false");
        ncmcsendotp obj = new ncmcsendotp();
        Response respobj2 = middlewareServicesObject.sendOTP(obj, headers, body);

        // Validate the response
        int statusCode = respobj2.getStatusCode();
        Assert.assertEquals(statusCode, 400);
//
//        workflowid = respobj2.jsonPath().getString("workflowId").toString();
//        stateId = respobj2.jsonPath().getString("stateId").toString();
    }
    @Test(priority = 2, groups = {"Regression"}, description = "Send OTP ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void send_ncmc_otp_phone_number_longer_length() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        // headers.put("sec-ch-ua" , "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        //  headers.put("x-mw-url-checksum", "Dq5LZe0mzumRUxZQ5DzN05uyI3/Jq7oXmtdeAG7jHV6ldx/oOgs4kbmV8Prfp+5tHg+2dMa2IEEow1W5TWtsN1f6EUUo7AJTlG8K5K0hLKdJblprIiDd8bL2owyi2CaZ");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        // headers.put("x-mw-checksum", "luA4xYSC2RO1folxHFHPO9oQvTNroUWu/7BW2lszIbT7w3uWO7jr4V1+OfpvAfUimK0wNfz9u+0OJPdM6pTsqIuF8Qia26HpHh3k0WVsVOLf0CUXCyCfA3afajCklzm9");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.144 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("phoneNumber", "12345678910");
        body.put("productId", productId); // Use the productId obtained from the previous test
        body.put("cardAmount", cardPrice);
        body.put("topupAmount", topupPrice);
        body.put("cardType", "");
        body.put("issueNonPPBL", "false");
        ncmcsendotp obj = new ncmcsendotp();
        Response respobj2 = middlewareServicesObject.sendOTP(obj, headers, body);

        // Validate the response
        int statusCode = respobj2.getStatusCode();
        Assert.assertEquals(statusCode, 400);

        workflowid = respobj2.jsonPath().getString("workflowId").toString();
        stateId = respobj2.jsonPath().getString("stateId").toString();
    }

    @Test(priority = 3 , groups = {"Regression"}, description = "Validate NCMC OTP")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void validate_ncmc_otp() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
     //   headers.put("x-mw-url-checksum", "uvivt6CLrbmeSKJ3tIPJt3tXAQNHDif7GasH1WzUq7vPZzbSzzFex10KpdVPDfzD6DoAsjLvES8+AQ0wGi8TwQQu1XkWxm7kMZ+0g5XGFuTbVHzj3fiFXOSOgqHBB/uL");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
      //  headers.put("x-mw-checksum", "KYZr4in6D+P9tGYhu9Yt6c+cCUWhystuFnGFT78DplElbldIW8lG8q1/wyS3V9Zryy3QKtNnAsKb1hZUYfqPMpUo7PrWq0+SzdYz+wq/8lHwT4W0YxdKB03+zhWzGcQt");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.144 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("phoneNumber", phone_number);
        body.put("workflowId", workflowid); // Use the workflowId obtained from the sendOTP method
        body.put("stateId", stateId); // Use the stateId obtained from the sendOTP method
        body.put("otp", otp);
        body.put("productId", productId);
        body.put("cardAmount", cardPrice);
        body.put("topupAmount", topupPrice);
        body.put("leadId", "");
        body.put("cardType", "");
        ncmcvalidateotp obj = new ncmcvalidateotp();
        Response respobj3 = middlewareServicesObject.validateOTP(obj , headers, body);

        // Validate the response
        int statusCode = respobj3.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        lead_id = respobj3.jsonPath().getString("leadId").toString();
        session_id = respobj3.jsonPath().getString("sessionId").toString();

    }


    @Test(priority = 3 , groups = {"Regression"}, description = "Validate NCMC OTP")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void validate_ncmc_otp_wrong_otp() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        //   headers.put("x-mw-url-checksum", "uvivt6CLrbmeSKJ3tIPJt3tXAQNHDif7GasH1WzUq7vPZzbSzzFex10KpdVPDfzD6DoAsjLvES8+AQ0wGi8TwQQu1XkWxm7kMZ+0g5XGFuTbVHzj3fiFXOSOgqHBB/uL");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        //  headers.put("x-mw-checksum", "KYZr4in6D+P9tGYhu9Yt6c+cCUWhystuFnGFT78DplElbldIW8lG8q1/wyS3V9Zryy3QKtNnAsKb1hZUYfqPMpUo7PrWq0+SzdYz+wq/8lHwT4W0YxdKB03+zhWzGcQt");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.144 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("phoneNumber", phone_number);
        body.put("workflowId", workflowid); // Use the workflowId obtained from the sendOTP method
        body.put("stateId", stateId); // Use the stateId obtained from the sendOTP method
        body.put("otp", "33333");
        body.put("productId", productId);
        body.put("cardAmount", cardPrice);
        body.put("topupAmount", topupPrice);
        body.put("leadId", "");
        body.put("cardType", "");
        ncmcvalidateotp obj = new ncmcvalidateotp();
        Response respobj3 = middlewareServicesObject.validateOTP(obj , headers, body);

        // Validate the response
        int statusCode = respobj3.getStatusCode();
        Assert.assertEquals(statusCode, 200);

//        lead_id = respobj3.jsonPath().getString("leadId").toString();
//        session_id = respobj3.jsonPath().getString("sessionId").toString();

    }

    @Test(priority = 4 , groups = {"Regression"}, description = "Fetch the Status of Lead ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void lead_data_ncmc(){
        lead_state obj = new lead_state(lead_id );
        Response lead_state_obj_resp = middlewareServicesObject.lead_state_ncmc(obj , solutionType , entityType , sessionToken ,  version);
        int Statuscode = lead_state_obj_resp.getStatusCode();
        Assert.assertEquals(Statuscode , 410);
    }

    @Test(priority = 4 , groups = {"Regression"}, description = "Fetch the Status of Lead ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void lead_data_ncmc_solution_type_missing(){
        lead_state obj = new lead_state(lead_id );
        Response lead_state_obj_resp = middlewareServicesObject.lead_state_ncmc(obj , "" , entityType , sessionToken ,  version);
        int Statuscode = lead_state_obj_resp.getStatusCode();
        Assert.assertEquals(Statuscode , 410);
    }
    @Test(priority = 4 , groups = {"Regression"}, description = "Fetch the Status of Lead ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void lead_data_ncmc_entity_type_missing(){
        lead_state obj = new lead_state(lead_id );
        Response lead_state_obj_resp = middlewareServicesObject.lead_state_ncmc(obj , solutionType , "" , sessionToken ,  version);
        int Statuscode = lead_state_obj_resp.getStatusCode();
        Assert.assertEquals(Statuscode , 410);
    }

    @Test(priority = 4 , groups = {"Regression"}, description = "Fetch the Status of Lead ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void lead_data_ncmc_session_token_missing(){
        lead_state obj = new lead_state(lead_id );
        Response lead_state_obj_resp = middlewareServicesObject.lead_state_ncmc(obj , solutionType , entityType , " " ,  version);
        int Statuscode = lead_state_obj_resp.getStatusCode();
        Assert.assertEquals(Statuscode , 401);
    }

    @Test(priority = 4 , groups = {"Regression"}, description = "Fetch the Status of Lead ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void lead_data_ncmc_older_version(){
        lead_state obj = new lead_state(lead_id );
        Response lead_state_obj_resp = middlewareServicesObject.lead_state_ncmc(obj , solutionType , entityType , sessionToken ,  "1.1.1");
        int Statuscode = lead_state_obj_resp.getStatusCode();
        Assert.assertEquals(Statuscode , 200);
    }

    @Test(priority = 5 , groups = {"Regression"}, description = "Create the QR Code ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void generate_qr_ncmc(){
        generate_qr_ncmc obj = new generate_qr_ncmc();
        Response generate_qr_ncmc_resp = middlewareServicesObject.generate_qr_ncmc(obj  , sessionToken ,  version , qrcode , lead_id , session_id);
        int Statuscode = generate_qr_ncmc_resp.getStatusCode();
        Assert.assertEquals(Statuscode , 200);
        cartOrderId = generate_qr_ncmc_resp.jsonPath().getString("paymentOrderId").toString();
    }
    @Test(priority = 5 , groups = {"Regression"}, description = "Create the QR Code ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void generate_qr_ncmc_sessionToken_missing(){
        generate_qr_ncmc obj = new generate_qr_ncmc();
        Response generate_qr_ncmc_resp = middlewareServicesObject.generate_qr_ncmc(obj  , " " ,  version , qrcode , lead_id , session_id);
        int Statuscode = generate_qr_ncmc_resp.getStatusCode();
        Assert.assertEquals(Statuscode , 401);
    }
    @Test(priority = 5 , groups = {"Regression"}, description = "Create the QR Code ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void generate_qr_ncmc_version_missing(){
        generate_qr_ncmc obj = new generate_qr_ncmc();
        Response generate_qr_ncmc_resp = middlewareServicesObject.generate_qr_ncmc(obj  , sessionToken ,  " " , qrcode , lead_id , session_id);
        int Statuscode = generate_qr_ncmc_resp.getStatusCode();
        Assert.assertEquals(Statuscode , 200);
    }
    @Test(priority = 5 , groups = {"Regression"}, description = "Create the QR Code ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void generate_qr_ncmc_qrcode_missing(){
        generate_qr_ncmc obj = new generate_qr_ncmc();
        Response generate_qr_ncmc_resp = middlewareServicesObject.generate_qr_ncmc(obj  , sessionToken ,  version , " " , lead_id , session_id);
        int Statuscode = generate_qr_ncmc_resp.getStatusCode();
        Assert.assertEquals(Statuscode , 400);
    }
    @Test(priority = 5 , groups = {"Regression"}, description = "Create the QR Code ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void generate_qr_ncmc_lead_id_missing(){
        generate_qr_ncmc obj = new generate_qr_ncmc();

        Response generate_qr_ncmc_resp = middlewareServicesObject.generate_qr_ncmc(obj  , sessionToken ,  version , qrcode , " " , session_id);

        int Statuscode = generate_qr_ncmc_resp.getStatusCode();

        Assert.assertEquals(Statuscode , 400);
    }
    @Test(priority = 5 , groups = {"Regression"}, description = "Create the QR Code ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void generate_qr_ncmc_sessionid_missing(){
        generate_qr_ncmc obj = new generate_qr_ncmc();

        Response generate_qr_ncmc_resp = middlewareServicesObject.generate_qr_ncmc(obj  , sessionToken ,  version , qrcode , lead_id , "");

        int Statuscode = generate_qr_ncmc_resp.getStatusCode();

        Assert.assertEquals(Statuscode , 400);
    }
    @Test(priority = 5 , groups = {"Regression"}, description = "Create the QR Code ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void generate_qr_ncmc_wrong_session_token(){
        generate_qr_ncmc obj = new generate_qr_ncmc();

        Response generate_qr_ncmc_resp = middlewareServicesObject.generate_qr_ncmc(obj  , "random" ,  version , qrcode , lead_id , session_id);

        int Statuscode = generate_qr_ncmc_resp.getStatusCode();

        Assert.assertEquals(Statuscode , 410);
    }
    @Test(priority = 5 , groups = {"Regression"}, description = "Create the QR Code ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void generate_qr_ncmc_older_version_configured(){
        generate_qr_ncmc obj = new generate_qr_ncmc();
        Response generate_qr_ncmc_resp = middlewareServicesObject.generate_qr_ncmc(obj  , sessionToken ,  "1.1.1" , qrcode , lead_id , session_id);
        int Statuscode = generate_qr_ncmc_resp.getStatusCode();
        Assert.assertEquals(Statuscode , 200);
    }

    @Test(priority = 5 , groups = {"Regression"}, description = "Create the QR Code ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void generate_qr_ncmc_wrong_qrcode(){
        generate_qr_ncmc obj = new generate_qr_ncmc();
        Response generate_qr_ncmc_resp = middlewareServicesObject.generate_qr_ncmc(obj  , sessionToken ,  version , "kjsbfckjxcn" , lead_id , session_id);
        int Statuscode = generate_qr_ncmc_resp.getStatusCode();
        Assert.assertEquals(Statuscode , 400);
    }
    @Test(priority = 5 , groups = {"Regression"}, description = "Create the QR Code ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void generate_qr_ncmc_random_lead_id(){
        generate_qr_ncmc obj = new generate_qr_ncmc();
        Response generate_qr_ncmc_resp = middlewareServicesObject.generate_qr_ncmc(obj  , sessionToken ,  version , qrcode , "random" , session_id);
        int Statuscode = generate_qr_ncmc_resp.getStatusCode();
        Assert.assertEquals(Statuscode , 400);
    }
    @Test(priority = 5 , groups = {"Regression"}, description = "Create the QR Code ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void generate_qr_ncmc_random_session_token(){
        generate_qr_ncmc obj = new generate_qr_ncmc();
        Response generate_qr_ncmc_resp = middlewareServicesObject.generate_qr_ncmc(obj  , sessionToken ,  version , qrcode , lead_id , "random ");
        int Statuscode = generate_qr_ncmc_resp.getStatusCode();
        Assert.assertEquals(Statuscode , 400);
        cartOrderId = generate_qr_ncmc_resp.jsonPath().getString("cartOrderId").toString();
    }


    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the Payment Status ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void fetch_payment_status(){
        fetch_payment_status obj = new fetch_payment_status();
        Response respobj = middlewareServicesObject.fetch_payment_status(obj , sessionToken ,  version , lead_id , cartOrderId);
        int Statuscode = respobj.getStatusCode();
        Assert.assertEquals(Statuscode , 200);
    }

    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the Payment Status ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void fetch_payment_status_sessiontoken_missing(){
        fetch_payment_status obj = new fetch_payment_status();
        Response respobj = middlewareServicesObject.fetch_payment_status(obj , "" ,  version , lead_id , cartOrderId);
        int Statuscode = respobj.getStatusCode();
        Assert.assertEquals(Statuscode , 401);
    }

    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the Payment Status ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void fetch_payment_status_session_token_random(){
        fetch_payment_status obj = new fetch_payment_status();
        Response respobj = middlewareServicesObject.fetch_payment_status(obj , "random" ,  version , lead_id , cartOrderId);
        int Statuscode = respobj.getStatusCode();
        Assert.assertEquals(Statuscode , 410);
    }

    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the Payment Status ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void fetch_payment_status_old_version(){
        fetch_payment_status obj = new fetch_payment_status();
        Response respobj = middlewareServicesObject.fetch_payment_status(obj , sessionToken ,  "1.1.1" , lead_id , cartOrderId);
        int Statuscode = respobj.getStatusCode();
        Assert.assertEquals(Statuscode , 200);
    }
    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the Payment Status ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void fetch_payment_status_version_empty(){
        fetch_payment_status obj = new fetch_payment_status();
        Response respobj = middlewareServicesObject.fetch_payment_status(obj , sessionToken ,  "" , lead_id , cartOrderId);
        int Statuscode = respobj.getStatusCode();
        Assert.assertEquals(Statuscode , 400);
    }
    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the Payment Status ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void fetch_payment_status_lead_id_empty(){
        fetch_payment_status obj = new fetch_payment_status();
        Response respobj = middlewareServicesObject.fetch_payment_status(obj , sessionToken ,  version , "" , cartOrderId);
        int Statuscode = respobj.getStatusCode();

        Assert.assertEquals(Statuscode , 400);
    }
    public void fetch_payment_status_lead_id_random(){
        fetch_payment_status obj = new fetch_payment_status();
        Response respobj = middlewareServicesObject.fetch_payment_status(obj , sessionToken ,  version , "random" , cartOrderId);
        int Statuscode = respobj.getStatusCode();

        Assert.assertEquals(Statuscode , 200);
    }
    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the Payment Status ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void fetch_payment_status_cartId_missing(){
        fetch_payment_status obj = new fetch_payment_status();
        Response respobj = middlewareServicesObject.fetch_payment_status(obj , sessionToken ,  version , lead_id , "");
        int Statuscode = respobj.getStatusCode();

        Assert.assertEquals(Statuscode , 400);
    }
    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the Payment Status ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void fetch_payment_status_cart_id_wrong(){
        fetch_payment_status obj = new fetch_payment_status();
        Response respobj = middlewareServicesObject.fetch_payment_status(obj , sessionToken ,  version , lead_id , "123445");
        int Statuscode = respobj.getStatusCode();

        Assert.assertEquals(Statuscode , 400);

    }
    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the Payment Status ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void fetch_payment_status_expired_cart_id(){
        fetch_payment_status obj = new fetch_payment_status();
        Response respobj = middlewareServicesObject.fetch_payment_status(obj , sessionToken ,  version , lead_id , "123456787654");
        int Statuscode = respobj.getStatusCode();

        Assert.assertEquals(Statuscode , 400);
    }
    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the Payment Status ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void fetch_payment_status_Omni_order_id(){
        fetch_payment_status obj = new fetch_payment_status();
        Response respobj = middlewareServicesObject.fetch_payment_status(obj , sessionToken ,  version , lead_id , "100076429797");
        int Statuscode = respobj.getStatusCode();

        Assert.assertEquals(Statuscode , 400);
    }


}
