package OCL.Individual.SoundBox.device;

import OCL.Individual.SoundBox.base.SoundBoxBaseTest;
import OCL.Individual.SoundBox.base.SoundBoxConstants;
import OCL.Individual.SoundBox.base.SoundBoxTestUtils;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * Test class for IoT Device functionality in SoundBox
 * Contains all tests related to IoT device retrieval and management (Priority 56-63)
 */
public class IotDeviceTests extends SoundBoxBaseTest {

    @Test(priority = 56, dependsOnMethods = {"OCL.Individual.SoundBox.device.DeviceValidationTests.verifyDeviceValidationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify IoT device retrieval with valid data")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyIotDeviceRetrievalWithValidData() {
        Response response = SoundBoxTestUtils.executeIotDeviceRetrieval(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.DEVICE_IDENTIFIER, true, 
            SoundBoxConstants.STATUS_OK);
        
        // Validate response contains device information
        String responseBody = response.getBody().asString();
        Assert.assertNotNull(responseBody, "Response body should not be null");
        LOGGER.info("IoT device retrieval successful");
        
        logTestCompletion("verifyIotDeviceRetrievalWithValidData", true);
    }

    @Test(priority = 57, dependsOnMethods = {"verifyIotDeviceRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify IoT device retrieval with missing agent token")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyIotDeviceRetrievalWithMissingAgentToken() {
        SoundBoxTestUtils.executeIotDeviceRetrievalWithCustomToken(middlewareServicesObject, 
            "", SoundBoxConstants.VERSION, SoundBoxConstants.DEVICE_IDENTIFIER, 
            SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyIotDeviceRetrievalWithMissingAgentToken", true);
    }

    @Test(priority = 58, dependsOnMethods = {"verifyIotDeviceRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify IoT device retrieval with invalid agent token")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyIotDeviceRetrievalWithInvalidAgentToken() {
        String invalidToken = "eyJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiZGlyIn0..invalid.token.value";
        
        SoundBoxTestUtils.executeIotDeviceRetrievalWithCustomToken(middlewareServicesObject, 
            invalidToken, SoundBoxConstants.VERSION, SoundBoxConstants.DEVICE_IDENTIFIER, 
            SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyIotDeviceRetrievalWithInvalidAgentToken", true);
    }

    @Test(priority = 59, dependsOnMethods = {"verifyIotDeviceRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify IoT device retrieval with missing device identifier")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyIotDeviceRetrievalWithMissingDeviceIdentifier() {
        SoundBoxTestUtils.executeIotDeviceRetrieval(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, "", true, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyIotDeviceRetrievalWithMissingDeviceIdentifier", true);
    }

    @Test(priority = 60, dependsOnMethods = {"verifyIotDeviceRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify IoT device retrieval with missing version")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyIotDeviceRetrievalWithMissingVersion() {
        SoundBoxTestUtils.executeIotDeviceRetrieval(middlewareServicesObject, 
            AgentToken, "", SoundBoxConstants.DEVICE_IDENTIFIER, true, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyIotDeviceRetrievalWithMissingVersion", true);
    }

    @Test(priority = 61, dependsOnMethods = {"verifyIotDeviceRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify IoT device retrieval with missing checksum")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyIotDeviceRetrievalWithMissingChecksum() {
        // Test without checksum header
        SoundBoxTestUtils.executeIotDeviceRetrieval(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.DEVICE_IDENTIFIER, false, 
            SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyIotDeviceRetrievalWithMissingChecksum", true);
    }

    @Test(priority = 62, dependsOnMethods = {"verifyIotDeviceRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify IoT device retrieval with empty checksum")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyIotDeviceRetrievalWithEmptyChecksum() {
        // Test with empty checksum value - using custom headers method
        SoundBoxTestUtils.executeIotDeviceRetrieval(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.DEVICE_IDENTIFIER, true, 
            SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyIotDeviceRetrievalWithEmptyChecksum", true);
    }

    @Test(priority = 63, dependsOnMethods = {"verifyIotDeviceRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_SMOKE}, 
          description = "Verify IoT device retrieval smoke test - quick validation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyIotDeviceRetrievalSmokeTest() {
        Response response = SoundBoxTestUtils.executeIotDeviceRetrieval(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.DEVICE_IDENTIFIER, true, 
            SoundBoxConstants.STATUS_OK);
        
        // Basic validation for smoke test
        Assert.assertNotNull(response.getBody(), "Response body should not be null");
        Assert.assertTrue(response.getTime() < 5000, "Response time should be less than 5 seconds");
        
        LOGGER.info("IoT device retrieval smoke test passed - Response time: {} ms", response.getTime());
        
        logTestCompletion("verifyIotDeviceRetrievalSmokeTest", true);
    }

    /**
     * Helper method to validate IoT device response structure
     * @param response API response
     */
    private void validateIotDeviceResponse(Response response) {
        String responseBody = response.getBody().asString();
        Assert.assertNotNull(responseBody, "Response body should not be null");
        
        // Additional validations can be added here based on expected response structure
        LOGGER.info("IoT device response validation completed");
    }

    /**
     * Helper method to test IoT device retrieval with retry logic
     * @param maxRetries Maximum number of retries
     */
    private void executeIotDeviceRetrievalWithRetry(int maxRetries) {
        int attempts = 0;
        Response response = null;
        
        while (attempts < maxRetries) {
            try {
                response = SoundBoxTestUtils.executeIotDeviceRetrieval(middlewareServicesObject, 
                    AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.DEVICE_IDENTIFIER, true, 
                    SoundBoxConstants.STATUS_OK);
                break; // Success, exit loop
            } catch (AssertionError e) {
                attempts++;
                if (attempts >= maxRetries) {
                    throw e; // Re-throw if max retries reached
                }
                LOGGER.warn("IoT device retrieval attempt {} failed, retrying...", attempts);
                // Re-login and retry
                AgentLoginSoundBox();
            }
        }
        
        Assert.assertNotNull(response, "Response should not be null after retries");
        validateIotDeviceResponse(response);
    }

    /**
     * Test IoT device retrieval with different device identifiers
     */
    @Test(priority = 64, dependsOnMethods = {"verifyIotDeviceRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify IoT device retrieval with different device identifiers")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyIotDeviceRetrievalWithDifferentDeviceIdentifiers() {
        // Test with different device identifier formats
        String[] deviceIdentifiers = {
            "Xiaomi-M2004J19C-131535fc93929702",
            "Samsung-Galaxy-123456789",
            "OnePlus-8T-987654321"
        };
        
        for (String deviceId : deviceIdentifiers) {
            try {
                SoundBoxTestUtils.executeIotDeviceRetrieval(middlewareServicesObject, 
                    AgentToken, SoundBoxConstants.VERSION, deviceId, true, SoundBoxConstants.STATUS_OK);
                LOGGER.info("IoT device retrieval successful for device: {}", deviceId);
            } catch (AssertionError e) {
                LOGGER.warn("IoT device retrieval failed for device: {} - {}", deviceId, e.getMessage());
                // Continue with next device identifier
            }
        }
        
        logTestCompletion("verifyIotDeviceRetrievalWithDifferentDeviceIdentifiers", true);
    }

    /**
     * Test IoT device retrieval with performance validation
     */
    @Test(priority = 65, dependsOnMethods = {"verifyIotDeviceRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify IoT device retrieval performance")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyIotDeviceRetrievalPerformance() {
        long startTime = System.currentTimeMillis();
        
        Response response = SoundBoxTestUtils.executeIotDeviceRetrieval(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.DEVICE_IDENTIFIER, true, 
            SoundBoxConstants.STATUS_OK);
        
        long endTime = System.currentTimeMillis();
        long responseTime = endTime - startTime;
        
        // Validate performance
        Assert.assertTrue(responseTime < 3000, 
            String.format("Response time should be less than 3 seconds, actual: %d ms", responseTime));
        
        LOGGER.info("IoT device retrieval performance test passed - Response time: {} ms", responseTime);
        
        logTestCompletion("verifyIotDeviceRetrievalPerformance", true);
    }
}
