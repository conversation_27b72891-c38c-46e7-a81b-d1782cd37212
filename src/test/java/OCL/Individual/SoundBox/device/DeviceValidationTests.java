package OCL.Individual.SoundBox.device;

import OCL.Individual.SoundBox.base.SoundBoxBaseTest;
import OCL.Individual.SoundBox.base.SoundBoxConstants;
import OCL.Individual.SoundBox.base.SoundBoxTestUtils;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * Test class for Device Validation functionality in SoundBox
 * Contains all tests related to device validation and QR scanning (Priority 33-46)
 */
public class DeviceValidationTests extends SoundBoxBaseTest {

    @Test(priority = 33, dependsOnMethods = {"OCL.Individual.SoundBox.content.YoutubeLinkTests.verifyYoutubeLinkFetchWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify device validation with valid data")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyDeviceValidationWithValidData() {
        Response response = SoundBoxTestUtils.executeDeviceValidation(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        // Extract reference ID from response
        String refId = response.jsonPath().getString("refId");
        Assert.assertNotNull(refId, "Reference ID should not be null in device validation response");
        LOGGER.info("Device validation successful - Reference ID: {}", refId);
        
        logTestCompletion("verifyDeviceValidationWithValidData", true);
    }

    @Test(priority = 34, dependsOnMethods = {"verifyDeviceValidationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify device validation with old version")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyDeviceValidationWithOldVersion() {
        // Test with older version - should still work
        SoundBoxTestUtils.executeDeviceValidationWithCustomHeaders(middlewareServicesObject, 
            AgentToken, "1.0.1", true, true, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyDeviceValidationWithOldVersion", true);
    }

    @Test(priority = 35, dependsOnMethods = {"verifyDeviceValidationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify device validation with no device scanned")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyDeviceValidationWithNoDeviceScanned() {
        // Test scenario where no device is scanned
        SoundBoxTestUtils.executeDeviceValidation(middlewareServicesObject, 
            AgentToken, "5.0.8", SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyDeviceValidationWithNoDeviceScanned", true);
    }

    @Test(priority = 36, dependsOnMethods = {"verifyDeviceValidationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify device validation with invalid agent customer ID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyDeviceValidationWithInvalidAgentCustId() {
        // Test with invalid agent customer ID
        SoundBoxTestUtils.executeDeviceValidation(middlewareServicesObject, 
            AgentToken, "5.0.8", SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyDeviceValidationWithInvalidAgentCustId", true);
    }

    @Test(priority = 37, dependsOnMethods = {"verifyDeviceValidationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify device validation with empty checksum")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyDeviceValidationWithEmptyChecksum() {
        SoundBoxTestUtils.executeDeviceValidationWithCustomHeaders(middlewareServicesObject, 
            AgentToken, "5.0.8", false, true, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyDeviceValidationWithEmptyChecksum", true);
    }

    @Test(priority = 38, dependsOnMethods = {"verifyDeviceValidationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify device validation with missing device identifier")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyDeviceValidationWithMissingDeviceIdentifier() {
        // Test with missing device identifier - should return error
        SoundBoxTestUtils.executeDeviceValidation(middlewareServicesObject, 
            AgentToken, "5.0.8", SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyDeviceValidationWithMissingDeviceIdentifier", true);
    }

    @Test(priority = 39, dependsOnMethods = {"verifyDeviceValidationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify device validation with missing checksum")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyDeviceValidationWithMissingChecksum() {
        // Test with missing checksum - should return 412 Precondition Failed
        SoundBoxTestUtils.executeDeviceValidationWithCustomHeaders(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, false, false, 412);
        
        logTestCompletion("verifyDeviceValidationWithMissingChecksum", true);
    }

    @Test(priority = 40, dependsOnMethods = {"verifyDeviceValidationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify device validation with wrong device identifier")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyDeviceValidationWithWrongDeviceIdentifier() {
        // Test with wrong device identifier
        SoundBoxTestUtils.executeDeviceValidation(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyDeviceValidationWithWrongDeviceIdentifier", true);
    }

    @Test(priority = 41, dependsOnMethods = {"verifyDeviceValidationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify device validation with wrong version")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyDeviceValidationWithWrongVersion() {
        SoundBoxTestUtils.executeDeviceValidation(middlewareServicesObject, 
            AgentToken, "2.1.0", SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyDeviceValidationWithWrongVersion", true);
    }

    @Test(priority = 42, dependsOnMethods = {"verifyDeviceValidationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify device validation with wrong session token")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyDeviceValidationWithWrongSessionToken() {
        String wrongToken = "eyJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiZGlyIn0..wrong.token.value";
        
        SoundBoxTestUtils.executeDeviceValidation(middlewareServicesObject, 
            wrongToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyDeviceValidationWithWrongSessionToken", true);
    }

    @Test(priority = 43, dependsOnMethods = {"verifyDeviceValidationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify device validation with retry logic")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyDeviceValidationWithRetryLogic() {
        try {
            SoundBoxTestUtils.executeDeviceValidation(middlewareServicesObject, 
                AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        } catch (AssertionError e) {
            LOGGER.warn("First device validation attempt failed, retrying with fresh token...");
            // Re-login and retry
            AgentLoginSoundBox();
            SoundBoxTestUtils.executeDeviceValidation(middlewareServicesObject, 
                AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        }
        
        logTestCompletion("verifyDeviceValidationWithRetryLogic", true);
    }

    @Test(priority = 44, dependsOnMethods = {"verifyDeviceValidationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify device validation with security headers")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyDeviceValidationWithSecurityHeaders() {
        // Test with all security headers included
        SoundBoxTestUtils.executeDeviceValidationWithCustomHeaders(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, true, true, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyDeviceValidationWithSecurityHeaders", true);
    }

    @Test(priority = 45, dependsOnMethods = {"verifyDeviceValidationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify device validation with missing security headers")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyDeviceValidationWithMissingSecurityHeaders() {
        // Test without security headers
        SoundBoxTestUtils.executeDeviceValidationWithCustomHeaders(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, false, false, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyDeviceValidationWithMissingSecurityHeaders", true);
    }

    @Test(priority = 46, dependsOnMethods = {"verifyDeviceValidationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_DEVICE, SoundBoxConstants.GROUP_SMOKE}, 
          description = "Verify device validation smoke test - quick validation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyDeviceValidationSmokeTest() {
        Response response = SoundBoxTestUtils.executeDeviceValidation(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        // Basic validation for smoke test
        Assert.assertNotNull(response.getBody(), "Response body should not be null");
        Assert.assertTrue(response.getTime() < 5000, "Response time should be less than 5 seconds");
        
        String refId = response.jsonPath().getString("refId");
        Assert.assertNotNull(refId, "Reference ID should be present");
        
        LOGGER.info("Device validation smoke test passed - Response time: {} ms, RefId: {}", 
            response.getTime(), refId);
        
        logTestCompletion("verifyDeviceValidationSmokeTest", true);
    }

    /**
     * Helper method to validate device validation response structure
     * @param response API response
     */
    private void validateDeviceValidationResponse(Response response) {
        String responseBody = response.getBody().asString();
        Assert.assertNotNull(responseBody, "Response body should not be null");
        
        // Check for reference ID in response
        String refId = response.jsonPath().getString("refId");
        if (refId != null) {
            LOGGER.info("Device validation successful - Reference ID: {}", refId);
        }
        
        LOGGER.info("Device validation response validation completed");
    }
}
