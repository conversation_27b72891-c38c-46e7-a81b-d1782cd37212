package OCL.Individual.SoundBox;

import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FetchReplacementReasonsTest {
	MiddlewareServices MiddlewareServicesObject= new MiddlewareServices();

    String entityType = "INDIVIDUAL";
    String solutionType = "sound_box";
    String questionType="Replacement";
    String solutionSubType="sound_box_replacement";

    @Test(priority = 1)
    public void FetchQnAForSounBoxReplacementValid() {
    	

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType",entityType);
        queryParams.put("solutionType", solutionType);
        queryParams.put("questionType", questionType);
        queryParams.put("solutionSubType",solutionSubType);


        Response ReplacementObj = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
        int httpcode = ReplacementObj.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 2)
    public void FetchQnAForSounBoxReplacement_WithInvalidEntityType() {
    	

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType","SS&87");
        queryParams.put("solutionType", solutionType);
        queryParams.put("questionType", questionType);
        queryParams.put("solutionSubType",solutionSubType);


        Response ReplacementObj = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
        int httpcode = ReplacementObj.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 3)
    public void FetchQnAForSounBoxReplacement_WithInvalidSolutionType() {
    	

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType",entityType);
        queryParams.put("solutionType", "&GDDGDG");
        queryParams.put("questionType", questionType);
        queryParams.put("solutionSubType",solutionSubType);


        Response ReplacementObj = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
        int httpcode = ReplacementObj.getStatusCode();
        Assert.assertTrue(httpcode == 500, "Testcase Failed");

    }
    @Test(priority = 4)
    public void FetchQnAForSounBoxReplacement_WithInvalidQuestionType() {
    	

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType",entityType);
        queryParams.put("solutionType", solutionType);
        queryParams.put("questionType", "66GYSGH");
        queryParams.put("solutionSubType",solutionSubType);


        Response ReplacementObj = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
        int httpcode = ReplacementObj.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 5)
    public void FetchQnAForSounBoxReplacement_WithInvaldSolutionSubtype() {
    	

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType",entityType);
        queryParams.put("solutionType", solutionType);
        queryParams.put("questionType", questionType);
        queryParams.put("solutionSubType","HDUD****");


        Response ReplacementObj = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
        int httpcode = ReplacementObj.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 6)
    public void FetchQnAForSounBoxReplacement_WithNullEntityType() {
    	

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType","SS&87");
        queryParams.put("solutionType", solutionType);
        queryParams.put("questionType", questionType);
        queryParams.put("solutionSubType",solutionSubType);


        Response ReplacementObj = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
        int httpcode = ReplacementObj.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 7)
    public void FetchQnAForSounBoxReplacement_WithNullSolutionType() {
    	

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType",entityType);
        queryParams.put("questionType", questionType);
        queryParams.put("solutionSubType",solutionSubType);


        Response ReplacementObj = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
        int httpcode = ReplacementObj.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 8)
    public void FetchQnAForSounBoxReplacement_WithNullQuestionType() {
    	

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType",entityType);
        queryParams.put("solutionType", solutionType);
        queryParams.put("solutionSubType",solutionSubType);


        Response ReplacementObj = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
        int httpcode = ReplacementObj.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 9)
    public void FetchQnAForSounBoxReplacement_WithNullSolutionSubtype() {
    	

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType",entityType);
        queryParams.put("solutionType", solutionType);
        queryParams.put("questionType", questionType);


        Response ReplacementObj = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
        int httpcode = ReplacementObj.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 10)
    public void FetchQnAForSounBoxReplacement_WithoutAnyQueryParams() {
    	

        Map<String, String> queryParams = new HashMap<String, String>();
        

        Response ReplacementObj = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
        int httpcode = ReplacementObj.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");

    }

}
