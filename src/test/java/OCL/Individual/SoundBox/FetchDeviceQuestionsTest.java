package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.FetchDeviceQuestions;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class FetchDeviceQuestionsTest extends BaseMethod {
	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> params;
	public FetchDeviceQuestions questionsObj;
	
	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	@Test
	public void verify_Questions_Are_Fetched_Successfully_For_Asset_Type_Battery() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "battery");
        params.put("solutionType", "asset_replacement");
        params.put("solutionSubType", "sound_box");
        params.put("serialNo", "869009061163854");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);
        Assert.assertEquals(response.getStatusCode(), 200);
	}


	
	@Test
	public void verify_Questions_Are_Fetched_Successfully_For_Asset_Type_Charger() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "charger");
        params.put("solutionType", "asset_replacement");
        params.put("solutionSubType", "sound_box");
        params.put("serialNo", "869009061163854");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);
     
        Assert.assertEquals(response.path("questionList[1].groupName").toString(), "standalone_asset_replacement_charger");
	}

	
	@Test
	public void verify_Battery_Questions_Contains_Battery_Working_Question() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "battery");
        params.put("solutionType", "asset_replacement");
        params.put("solutionSubType", "sound_box");
        params.put("serialNo", "869009061163854");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);
     
        Assert.assertEquals(response.getStatusCode(),200);
    }
 	
	@Test
	public void verify_Battery_Questions_Contains_Battery_Available_Question() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "battery");
        params.put("solutionType", "asset_replacement");
        params.put("solutionSubType", "sound_box");
        params.put("serialNo", "869009061163854");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);
     
        Assert.assertEquals(response.path("questionList[0].text").toString(), "Battery replacement needed?");
	}

	@Test
	public void verify_Charger_Questions_Contains_Charger_Working_Question() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "charger");
        params.put("solutionType", "asset_replacement");
        params.put("solutionSubType", "sound_box");
        params.put("serialNo", "869009061163854");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);
     
        Assert.assertEquals(response.path("questionList[1].text").toString(), "Is merchant returning the old charger?");
    }
	
	@Test
	public void verify_Charger_Questions_Contains_Charger_Available_Question() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "charger");
        params.put("solutionType", "asset_replacement");
        params.put("solutionSubType", "sound_box");
        params.put("serialNo", "869009061163854");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);
     
        Assert.assertEquals(response.path("questionList[0].text").toString(), "Charger replacement needed?");
	}
	
	@Test
	public void verify_battery_Working_Question_Depennds_On_Battery_Available() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "battery");
        params.put("solutionType", "asset_replacement");
        params.put("solutionSubType", "sound_box");
        params.put("serialNo", "869009061163854");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);
        Assert.assertEquals(response.getStatusCode(), 200);
  	}
	@Test
	public void verify_charger_Working_Question_Depennds_On_Charger_Available() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "charger");
        params.put("solutionType", "asset_replacement");
        params.put("solutionSubType", "sound_box");
        params.put("serialNo", "869009061163854");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);
     
        Assert.assertEquals(response.path("questionList[1].preRequisite.questionSlug").toString(), "charegerReplacementCost");

        
	}

	@Test
	public void verify_Error_Code_In_Case_OF_Any_Exception_Is_400() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "charger");
        params.put("solutionType", "asset_replacement");
        params.put("solutionSubType", "sound_buioireoioewruoox");
        params.put("serialNo", "869009061163854");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);
     
        Assert.assertTrue(response.getStatusCode()==400);
        
	}

}