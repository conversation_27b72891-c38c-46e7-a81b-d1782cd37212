package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.SimReplacementValidateOTP;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestSimReplacementValidateOTP extends BaseMethod {
	
	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> body;
	public Map<String, String> param;
	SimReplacementValidateOTP sim;
	
	
	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	@Test(priority=0)
	public void verify_Status_When_Lead_Is_Successfully_Created() {
		
		
		headers=new HashMap<>();
		body=new HashMap<>();
		param=new HashMap<String, String>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        param.put("entityType", "INDIVIDUAL");
        param.put("solutionType", "manage_sim");

        body.put("otp", "");
        body.put("state", "");
        body.put("userType", "merchant");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "false"); // Boolean as String
        body.put("individualSolutionType", "");
        body.put("mid", "HySHnd27878673398759");
        body.put("onlyValidateOtp", "false"); // Boolean as String
        body.put("custId", "1001788031");
        body.put("skipOtp", "true"); // Boolean as String
        body.put("tncVersion", "");
        body.put("tncSetName", "");

        sim=new SimReplacementValidateOTP();
        response=services.createSimReplace(sim, body, headers,param);	
        Assert.assertEquals(200, response.getStatusCode());
        
   }
	
	@Test(priority=1)
	public void verify_When_Sim_Replacement_Lead_Already_Exists_Then_Existing_Lead_Is_Fetched() {
		
		
		headers=new HashMap<>();
		body=new HashMap<>();
		param=new HashMap<String, String>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        param.put("entityType", "INDIVIDUAL");
        param.put("solutionType", "manage_sim");

        body.put("otp", "");
        body.put("state", "");
        body.put("userType", "merchant");
        body.put("mobile", "6665550217");
        body.put("individaulMerchantKyc", "false"); // Boolean as String
        body.put("individualSolutionType", "");
        body.put("mid", "DHVRVL02894215991065");
        body.put("onlyValidateOtp", "false"); // Boolean as String
        body.put("custId", "1001373711");
        body.put("skipOtp", "true"); // Boolean as String
        body.put("tncVersion", "");
        body.put("tncSetName", "");


        sim=new SimReplacementValidateOTP();
        response=services.createSimReplace(sim, body, headers,param);	
        Assert.assertEquals(200, response.getStatusCode());
        String lead1=response.path("leadId");
        
        body.put("otp", "");
        body.put("state", "");
        body.put("userType", "merchant");
        body.put("mobile", "6665550217");
        body.put("individaulMerchantKyc", "false"); // Boolean as String
        body.put("individualSolutionType", "");
        body.put("mid", "DHVRVL02894215991065");
        body.put("onlyValidateOtp", "false"); // Boolean as String
        body.put("custId", "1001373711");
        body.put("skipOtp", "true"); // Boolean as String
        body.put("tncVersion", "");
        body.put("tncSetName", "");

        sim=new SimReplacementValidateOTP();
        response=services.createSimReplace(sim, body, headers,param);	
        Assert.assertEquals(200, response.getStatusCode());
        String lead2=response.path("leadId");
        Assert.assertEquals(lead1,lead2);

    }

	@Test(priority=2)
	public void verify_Error_Code_If_Entity_Type_Is_Properitorship() {
		
		
		headers=new HashMap<>();
		body=new HashMap<>();
		param=new HashMap<String, String>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        param.put("entityType", "PROPREITORSHIP");
        param.put("solutionType", "manage_sim");

        body.put("otp", "");
        body.put("state", "");
        body.put("userType", "merchant");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "false"); // Boolean as String
        body.put("individualSolutionType", "");
        body.put("mid", "HySHnd27878673398759");
        body.put("onlyValidateOtp", "false"); // Boolean as String
        body.put("custId", "1001788031");
        body.put("skipOtp", "true"); // Boolean as String
        body.put("tncVersion", "");
        body.put("tncSetName", "");

        sim=new SimReplacementValidateOTP();
        response=services.createSimReplace(sim, body, headers,param);	
        Assert.assertEquals(500, response.getStatusCode());
        
   }
	
	@Test(priority=3)
	public void verify_Error_Code_If_Solution_Type_Is_Not_Passed_In_The_Request() {
		
		
		headers=new HashMap<>();
		body=new HashMap<>();
		param=new HashMap<String, String>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        param.put("entityType", "PROPREITORSHIP");
        

        body.put("otp", "");
        body.put("state", "");
        body.put("userType", "merchant");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "false"); // Boolean as String
        body.put("individualSolutionType", "");
        body.put("mid", "HySHnd27878673398759");
        body.put("onlyValidateOtp", "false"); // Boolean as String
        body.put("custId", "1001788031");
        body.put("skipOtp", "true"); // Boolean as String
        body.put("tncVersion", "");
        body.put("tncSetName", "");

        sim=new SimReplacementValidateOTP();
        response=services.createSimReplace(sim, body, headers,param);	
        Assert.assertEquals(500, response.getStatusCode());
        
   }
	
	
	@Test(priority=4)
	public void verify_Error_Code_If_OTP_Is_Missing_In_Request_Body() {
		
		
		headers=new HashMap<>();
		body=new HashMap<>();
		param=new HashMap<String, String>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        param.put("entityType", "INDIVIDIUAL");
        param.put("solutionType", "manage_sim");

      
        body.put("state", "");
        body.put("userType", "merchant");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "false"); // Boolean as String
        body.put("individualSolutionType", "");
        body.put("mid", "HySHnd27878673398759");
        body.put("onlyValidateOtp", "false"); // Boolean as String
        body.put("custId", "1001788031");
        body.put("skipOtp", "true"); // Boolean as String
        body.put("tncVersion", "");
        body.put("tncSetName", "");

        sim=new SimReplacementValidateOTP();
        response=services.createSimReplace(sim, body, headers,param);	
        Assert.assertEquals(500, response.getStatusCode());
        
   }
	
	@Test(priority=5)
	public void verify_Error_Code_If_State_Is_Missing_In_Request_Body() {
		
		
		headers=new HashMap<>();
		body=new HashMap<>();
		param=new HashMap<String, String>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        param.put("entityType", "INDIVIDUAL");
        param.put("solutionType", "manage_sim");

        body.put("otp", "");
     
        body.put("userType", "merchant");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "false"); // Boolean as String
        body.put("individualSolutionType", "");
        body.put("mid", "HySHnd27878673398759");
        body.put("onlyValidateOtp", "false"); // Boolean as String
        body.put("custId", "1001788031");
        body.put("skipOtp", "true"); // Boolean as String
        body.put("tncVersion", "");
        body.put("tncSetName", "");

        sim=new SimReplacementValidateOTP();
        response=services.createSimReplace(sim, body, headers,param);	
        Assert.assertEquals(200, response.getStatusCode());
        
   }
	
	@Test
	public void verify_Error_Code_If_UserType_Is_Missing_In_Request_Body() {
				
		
		headers=new HashMap<>();
		body=new HashMap<>();
		param=new HashMap<String, String>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        param.put("entityType", "INDIVIDUAL");
        param.put("solutionType", "manage_sim");

        body.put("otp", "");
        body.put("state", "");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "false"); // Boolean as String
        body.put("individualSolutionType", "");
        body.put("mid", "HySHnd27878673398759");
        body.put("onlyValidateOtp", "false"); // Boolean as String
        body.put("custId", "1001788031");
        body.put("skipOtp", "true"); // Boolean as String
        body.put("tncVersion", "");
        body.put("tncSetName", "");

        sim=new SimReplacementValidateOTP();
        response=services.createSimReplace(sim, body, headers,param);	
        Assert.assertEquals(500, response.getStatusCode());
        
   }
	
	@Test
	public void verify_Error_Code_If_mobile_Number_Is_Missing_In_Request_Body() {
			
		
		headers=new HashMap<>();
		body=new HashMap<>();
		param=new HashMap<String, String>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        param.put("entityType", "INDIVIDUAL");
        param.put("solutionType", "manage_sim");

        body.put("otp", "");
        body.put("state", "");
        body.put("userType", "merchant");
       
        body.put("individaulMerchantKyc", "false"); // Boolean as String
        body.put("individualSolutionType", "");
        body.put("mid", "HySHnd27878673398759");
        body.put("onlyValidateOtp", "false"); // Boolean as String
        body.put("custId", "1001788031");
        body.put("skipOtp", "true"); // Boolean as String
        body.put("tncVersion", "");
        body.put("tncSetName", "");

        sim=new SimReplacementValidateOTP();
        response=services.createSimReplace(sim, body, headers,param);	
        Assert.assertEquals(400, response.getStatusCode());
        
   }
	
	@Test
	public void verify_Error_Code_If_MID_Is_Missing_In_Request_Body() {
			
		
		headers=new HashMap<>();
		body=new HashMap<>();
		param=new HashMap<String, String>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        param.put("entityType", "INDIVIDUAL");
        param.put("solutionType", "manage_sim");

        body.put("otp", "");
        body.put("state", "");
        body.put("userType", "merchant");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "false"); // Boolean as String
        body.put("individualSolutionType", "");
     
        body.put("onlyValidateOtp", "false"); // Boolean as String
        body.put("custId", "1001788031");
        body.put("skipOtp", "true"); // Boolean as String
        body.put("tncVersion", "");
        body.put("tncSetName", "");

        sim=new SimReplacementValidateOTP();
        response=services.createSimReplace(sim, body, headers,param);	
        Assert.assertEquals(500, response.getStatusCode());
        
   }
	
	@Test
	public void verify_Error_Code_If_Merchant_Cust_Is_Missing_In_Request_Body() {
			
		
		headers=new HashMap<>();
		body=new HashMap<>();
		param=new HashMap<String, String>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        param.put("entityType", "INDIVIDUAL");
        param.put("solutionType", "manage_sim");

        body.put("otp", "");
        body.put("state", "");
        body.put("userType", "merchant");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "false"); // Boolean as String
        body.put("individualSolutionType", "");
        body.put("mid", "HySHnd27878673398759");
        body.put("onlyValidateOtp", "false"); // Boolean as String
        body.put("custId", "");
        body.put("skipOtp", "true"); // Boolean as String
        body.put("tncVersion", "");
        body.put("tncSetName", "");

        sim=new SimReplacementValidateOTP();
        response=services.createSimReplace(sim, body, headers,param);	
        Assert.assertEquals(400, response.getStatusCode());
        
   }
	
	@Test
	public void verify_Lead_Is_Not_Created_If_onlyValidateOtp_Is_True_In_Request_Body() {
			
		
		headers=new HashMap<>();
		body=new HashMap<>();
		param=new HashMap<String, String>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        param.put("entityType", "INDIVIDUAL");
        param.put("solutionType", "manage_sim");

        body.put("otp", "");
        body.put("state", "");
        body.put("userType", "merchant");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "false"); // Boolean as String
        body.put("individualSolutionType", "");
        body.put("mid", "HySHnd27878673398759");
        body.put("onlyValidateOtp", "true"); // Boolean as String
        body.put("custId", "1001788031");
        body.put("skipOtp", "true"); // Boolean as String
        body.put("tncVersion", "");
        body.put("tncSetName", "");

        sim=new SimReplacementValidateOTP();
        response=services.createSimReplace(sim, body, headers,param);	
        Assert.assertFalse(response.asString().contains("leadId"));
        
   }
	
	
	@Test
	public void verify_Error_Message_If_Skip_Otp_Is_False_In_Request_Body() {
			
		
		headers=new HashMap<>();
		body=new HashMap<>();
		param=new HashMap<String, String>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        param.put("entityType", "INDIVIDUAL");
        param.put("solutionType", "manage_sim");

        body.put("otp", "");
        body.put("state", "");
        body.put("userType", "merchant");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "false"); // Boolean as String
        body.put("individualSolutionType", "");
        body.put("mid", "HySHnd27878673398759");
        body.put("onlyValidateOtp", "false"); // Boolean as String
        body.put("custId", "1001788031");
        body.put("skipOtp", "false"); // Boolean as String
        body.put("tncVersion", "");
        body.put("tncSetName", "");

        sim=new SimReplacementValidateOTP();
        response=services.createSimReplace(sim, body, headers,param);	
        Assert.assertTrue(response.path("displayMessage").toString().contains("Server error. Please try again"));
        
   }
	
	@Test
	public void verify_Error_Code_If_Request_Is_Tempered_And_Changes_Made_In_Request_Body() {
			
		
		headers=new HashMap<>();
		body=new HashMap<>();
		param=new HashMap<String, String>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep"); on prod this header will not be applicable which is bypassing request tempering on staging
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        param.put("entityType", "INDIVIDUAL");
        param.put("solutionType", "manage_sim");

        body.put("otp", "");
        body.put("state", "");
        body.put("userType", "merchant");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "false"); // Boolean as String
        body.put("individualSolutionType", "");
        body.put("mid", "HySHnd27878673398759");
        body.put("onlyValidateOtp", "false"); // Boolean as String
        body.put("custId", "1001788031");
        body.put("skipOtp", "true"); // Boolean as String
        body.put("tncVersion", "");
        body.put("tncSetName", "");

        sim=new SimReplacementValidateOTP();
        response=services.createSimReplace(sim, body, headers,param);	
        Assert.assertEquals(412, response.getStatusCode());
        
   }
	
	
	
	
	
	@Test
	public void verify_Error_Code_If_JWT_Is_Missing_In_Request_Body() {
			
		
		headers=new HashMap<>();
		body=new HashMap<>();
		param=new HashMap<String, String>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
	
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        param.put("entityType", "INDIVIDUAL");
        param.put("solutionType", "manage_sim");

        body.put("otp", "");
        body.put("state", "");
        body.put("userType", "merchant");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "false"); // Boolean as String
        body.put("individualSolutionType", "");
        body.put("mid", "HySHnd27878673398759");
        body.put("onlyValidateOtp", "false"); // Boolean as String
        body.put("custId", "1001788031");
        body.put("skipOtp", "true"); // Boolean as String
        body.put("tncVersion", "");
        body.put("tncSetName", "");

        sim=new SimReplacementValidateOTP();
        response=services.createSimReplace(sim, body, headers,param);	
        Assert.assertEquals(401, response.getStatusCode());
        
   }
	

}