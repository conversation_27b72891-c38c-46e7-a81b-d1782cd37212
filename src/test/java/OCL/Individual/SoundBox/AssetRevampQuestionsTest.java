package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.FetchDeviceQuestions;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class AssetRevampQuestionsTest extends BaseMethod {

	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> params;
	public FetchDeviceQuestions questionsObj;
	
	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	@Test
	public void verify_Status_Code_When_Success_Is_Recieved_In_Response() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "charger");
        params.put("solutionType", "sound_box");
        params.put("solutionSubType", "sound_box_replacement");
        params.put("serialNo", "************");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);

        Assert.assertEquals(response.getStatusCode(), 200);
	}

	@Test
	public void verify_Display_Type_And_Display_Text_For_Old_Charger() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "charger");
        params.put("solutionType", "sound_box");
        params.put("solutionSubType", "sound_box_replacement");
        params.put("serialNo", "************");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);

        Assert.assertEquals(response.path("questionList[1].text"), "Is Merchant returning the old charger?");
        Assert.assertEquals(response.path("questionList[1].displayType"), "radio");
        
	}

	@Test
	public void verify_Display_Type_And_Display_Text_For_New_Charger() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "charger");
        params.put("solutionType", "sound_box");
        params.put("solutionSubType", "sound_box_replacement");
        params.put("serialNo", "************");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);

        Assert.assertEquals(response.path("questionList[2].text"), "Does the Merchant need new charger?");
        Assert.assertEquals(response.path("questionList[2].displayType"), "radio");
        
	}
	
	@Test
	public void verify_Questions_For_Old_And_New_Charger_Are_Mandatory() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "charger");
        params.put("solutionType", "sound_box");
        params.put("solutionSubType", "sound_box_replacement");
        params.put("serialNo", "************");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);

         Assert.assertTrue(response.path("questionList[1].mandatory"));
         Assert.assertTrue(response.path("questionList[2].mandatory"));

	}
	
	@Test
	public void verify_Answers_For_Old_And_New_Charger_Are_Contains_Yes_No_Option() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "charger");
        params.put("solutionType", "sound_box");
        params.put("solutionSubType", "sound_box_replacement");
        params.put("serialNo", "************");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);

        Assert.assertEquals(response.path("questionList[1].options[0].text"), "yes");
        Assert.assertEquals(response.path("questionList[1].options[1].text"), "no");
        Assert.assertEquals(response.path("questionList[2].options[0].text"), "yes");
        Assert.assertEquals(response.path("questionList[2].options[1].text"), "no");
        
	}
	
	

	@Test
	public void verify_Old_Device_Question_Is_Mandatory_For_New_Device_Question() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "charger");
        params.put("solutionType", "sound_box");
        params.put("solutionSubType", "sound_box_replacement");
        params.put("serialNo", "************");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);

        Assert.assertEquals(response.path("questionList[2].preRequisite.questionSlug").toString(), response.path("questionList[1].questionAlias").toString());
        
	}

	
	@Test
	public void verify_Error_Code_When_SolutionType_Is_Missing_From_Request() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "charger");
        
        params.put("solutionSubType", "sound_box_replacement");
        params.put("serialNo", "************");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);

        Assert.assertEquals(response.getStatusCode(),400);
        
	}

	@Test
	public void verify_Error_Code_When_SolutionSubType_Is_Missing_From_Request() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "charger");
        params.put("solutionType", "sound_box");
     
        params.put("serialNo", "************");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);

        Assert.assertEquals(response.getStatusCode(),200);
        
	}

	@Test
	public void verify_Error_Code_When_DeviceSerialNumber_Is_Missing_From_Request() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "charger");
        params.put("solutionType", "sound_box");
        params.put("solutionSubType", "sound_box_replacement");
    
        params.put("modelName", "Made In India Soundbox 3.0 4G");
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);

        Assert.assertEquals(response.getStatusCode(),400);
        
	}
	
	@Test
	public void verify_Error_Code_When_ModelName_Is_Missing_From_Request() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "charger");
        params.put("solutionType", "sound_box");
        params.put("solutionSubType", "sound_box_replacement");
        params.put("serialNo", "************");
       
        params.put("deviceType", "4G");
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);

        Assert.assertEquals(response.getStatusCode(),400);
        
	}


	@Test
	public void verify_Error_Code_When_DeviceType_Is_Missing_From_Request() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();

		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("assetSubType", "charger");
        params.put("solutionType", "sound_box");
        params.put("solutionSubType", "sound_box_replacement");
        params.put("serialNo", "************");
        params.put("modelName", "Made In India Soundbox 3.0 4G");
   
        
        questionsObj=new FetchDeviceQuestions();
        response=services.fetchDeviceQuestions(questionsObj, params, headers);

        Assert.assertEquals(response.getStatusCode(),400);
        
	}
	
	

	
}