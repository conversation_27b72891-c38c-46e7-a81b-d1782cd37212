package OCL.Individual.SoundBox;

import Request.SoundBox.SBMerchantDetails;
import Services.MechantService.MiddlewareServices;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class SBfetchplan  {
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    //String custId = "1001891503";
    //  MiddlewareServicesObject.custid=
    String AgentToken ;
    String version="7.1.7";
    String custid = "1001891503";

    @Test(priority = 1,description = "Getting custid details ")
    public void FetchMidFromBoss() {

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("Cookie","BOSS_SESSION=51e18c9e-3b33-4f69-ad32-a03c7e8404ba");
        headers.put("Content-Type", "application/json");
        Map<String, String> body = new HashMap<String, String>();
        SBMerchantDetails sbobj = new SBMerchantDetails(custid);
        Response getfetchPermissionResponse = MiddlewareServicesObject.v1SBMerchantDetails(sbobj,headers);
        System.out.print(getfetchPermissionResponse);
        int httpcode = getfetchPermissionResponse.getStatusCode();
        //Assert.assertTrue(httpcode==200, "Testcase Passed");



    }
    @Test(priority = 2,description = "When wrong custid is given")
    public void FetchMidFromBoss_PPBL_WAREHOUSE() {

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Cookie","BOSS_SESSION=51e18c9e-3b33-4f69-ad32-a03c7e8404ba");
        headers.put("Content-Type", "application/json");
        Map<String, String> body = new HashMap<String, String>();

        SBMerchantDetails sbobj = new SBMerchantDetails(custid);
        Response fetchPermissionResponse = MiddlewareServicesObject.v1SBMerchantDetails(sbobj, headers);
        int httpcode = fetchPermissionResponse.getStatusCode();
        //Assert.assertTrue(httpcode==200 , "Testcase Passed");
       // Assert.assertEquals(httpcode,403);
    }
    @Test(priority = 3,description = "When empty field is given")
    public void FetchMidFromBoss3() {
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Cookie","BOSS_SESSION=51e18c9e-3b33-4f69-ad32-a03c7e8404ba");
        headers.put("Content-Type", "application/json");
        Map<String, String> body = new HashMap<String, String>();


        SBMerchantDetails sbobj = new SBMerchantDetails("");
        Response fetchPermissionResponse = MiddlewareServicesObject.v1SBMerchantDetails(sbobj, headers);
        int httpcode = fetchPermissionResponse.getStatusCode();
        //Assert.assertTrue(httpcode==200 , "Testcase Passed");
       // Assert.assertEquals(httpcode,403);
    }
    @Test(priority = 4,description = "When token got expires")
    public void FetchMidFromBoss4() {

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Cookie","BOSS_SESSION=7b4b4742-348c-4be8-a81d-42e00cd8d318; BOSS_SESSION=9185e0e0-2384-48bd-98fd-17f570c5b8f2");
        headers.put("Content-Type", "application/json");
        Map<String, String> body = new HashMap<String, String>();

        SBMerchantDetails sbobj = new SBMerchantDetails(custid);
        Response fetchPermissionResponse = MiddlewareServicesObject.v1SBMerchantDetails(sbobj, headers);
        int httpcode = fetchPermissionResponse.getStatusCode();
        Assert.assertEquals(httpcode,403);
    }
    @Test(priority = 5,description = "When token field is empty")
    public void FetchMidFromBoss5() {

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Cookie","BOSS_SESSION=");
        headers.put("Content-Type", "application/json");
        Map<String, String> body = new HashMap<String, String>();

        SBMerchantDetails sbobj = new SBMerchantDetails(custid);
        Response fetchPermissionResponse = MiddlewareServicesObject.v1SBMerchantDetails(sbobj, headers);
        int httpcode = fetchPermissionResponse.getStatusCode();
        Assert.assertEquals(httpcode,403);
    }
    @Test(priority = 6,description = "When token field And CustId  are  empty")
    public void FetchMidFromBoss6() {

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Cookie","BOSS_SESSION=");
        headers.put("Content-Type", "application/json");
        Map<String, String> body = new HashMap<String, String>();

        SBMerchantDetails sbobj = new SBMerchantDetails(custid);
        Response fetchPermissionResponse = MiddlewareServicesObject.v1SBMerchantDetails(sbobj, headers);
        int httpcode = fetchPermissionResponse.getStatusCode();
        Assert.assertEquals(httpcode,403);
    }


}