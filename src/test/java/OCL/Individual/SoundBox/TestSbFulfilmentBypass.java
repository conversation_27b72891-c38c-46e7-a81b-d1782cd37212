package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.SbFulfilmentBypass;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestSbFulfilmentBypass extends BaseMethod {


		public String sessionToken;

		public Response response;
		public MiddlewareServices services;
		
		public Map<String,String> headers;
		public Map<String, String> body;
		SbFulfilmentBypass bypass;
		
		@BeforeMethod
		public void setToken() {
		
			sessionToken=AgentSessionToken("7771216290", "paytm@123");
			try {
				establishConnectiontoServer(sessionToken, 5);

			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		@Test()
		public void verify_Status_Code_In_Case_Of_Success_When_Solution_SubType_Is_Unbind() {
			
			headers=new HashMap<>();
			body=new HashMap<>();
			services=new MiddlewareServices();
			bypass = new SbFulfilmentBypass();
			
			headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
			headers.put("session_token", sessionToken);
			headers.put("version", "5.1.6");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
	        headers.put("latitude" , "12.836047");
	        headers.put("ipAddress" , "***********");
	        headers.put("isBusyBoxFound" , "false");
	        headers.put("osVersion" , "10");
	        headers.put("appLanguage","en");
	        headers.put("isDeviceRooted" , "false");
	        
	        
	        body.put("solutionType","sound_box");
	        body.put("solutionTypeLevel2","sound_box_unbind");
	        body.put("entityType","INDIVIDUAL");
	        body.put("mid","DHVRVL02894215991065");
	        body.put("userCustId","1001373711");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","6665550212");
	        body.put("deviceType","sound_box_3_0_4g");
	        body.put("deviceCategory","sound_box_3_0_4g");
	        body.put("isFromFSM","false");
	        body.put("deviceId","563747466003");
	        body.put("posId","POS1349570234775");
	        body.put("questionAlias","Select reason to close request");
	        body.put("answerAlias","Done with Backend JOT Form");
	        
	        response=services.v1FulfilmentBypass(bypass, body, headers);
	    
	        Assert.assertEquals(response.getStatusCode(), 200);
	        Assert.assertTrue(response.path("displayMessage").toString().contains("Fulfilment bypassed done"));
	        
		}
		
		
		@Test()
		public void verify_Status_Code_In_Case_Of_Success_When_Solution_SubType_Is_Replace() {
			
			headers=new HashMap<>();
			body=new HashMap<>();
			services=new MiddlewareServices();
			bypass = new SbFulfilmentBypass();
			
			headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
			headers.put("session_token", sessionToken);
			headers.put("version", "5.1.6");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
	        headers.put("latitude" , "12.836047");
	        headers.put("ipAddress" , "***********");
	        headers.put("isBusyBoxFound" , "false");
	        headers.put("osVersion" , "10");
	        headers.put("appLanguage","en");
	        headers.put("isDeviceRooted" , "false");
	        
	        
	        body.put("solutionType","sound_box");
	        body.put("solutionTypeLevel2","sound_box_replace");
	        body.put("entityType","INDIVIDUAL");
	        body.put("mid","DHVRVL02894215991065");
	        body.put("userCustId","1001373711");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","6665550212");
	        body.put("deviceType","sound_box_3_0_4g");
	        body.put("deviceCategory","sound_box_3_0_4g");
	        body.put("isFromFSM","false");
	        body.put("deviceId","563747466003");
	        body.put("posId","POS1349570234775");
	        body.put("questionAlias","Select reason to close request");
	        body.put("answerAlias","Done with Backend JOT Form");
	        
	        response=services.v1FulfilmentBypass(bypass, body, headers);
	    
	        Assert.assertEquals(response.getStatusCode(), 200);
	        Assert.assertTrue(response.path("displayMessage").toString().contains("Fulfilment bypassed done"));
	        
		}


		@Test()
		public void verify_Status_Code_In_Case_Of_Error_When_Solution_SubType_Is_Invalid() {
			
			headers=new HashMap<>();
			body=new HashMap<>();
			services=new MiddlewareServices();
			bypass = new SbFulfilmentBypass();
			
			headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
			headers.put("session_token", sessionToken);
			headers.put("version", "5.1.6");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
	        headers.put("latitude" , "12.836047");
	        headers.put("ipAddress" , "***********");
	        headers.put("isBusyBoxFound" , "false");
	        headers.put("osVersion" , "10");
	        headers.put("appLanguage","en");
	        headers.put("isDeviceRooted" , "false");
	        
	        
	        body.put("solutionType","sound_box");
	        body.put("solutionTypeLevel2","sound_box_bind");
	        body.put("entityType","INDIVIDUAL");
	        body.put("mid","DHVRVL02894215991065");
	        body.put("userCustId","1001373711");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","6665550212");
	        body.put("deviceType","sound_box_3_0_4g");
	        body.put("deviceCategory","sound_box_3_0_4g");
	        body.put("isFromFSM","false");
	        body.put("deviceId","563747466003");
	        body.put("posId","POS1349570234775");
	        body.put("questionAlias","Select reason to close request");
	        body.put("answerAlias","Done with Backend JOT Form");
	        
	        response=services.v1FulfilmentBypass(bypass, body, headers);
	    
	        Assert.assertEquals(response.getStatusCode(), 400);

	        
		}
		
		@Test()
		public void verify_Status_Code_In_Case_Of_Error_When_Solution_Type_Is_Invalid() {
			
			headers=new HashMap<>();
			body=new HashMap<>();
			services=new MiddlewareServices();
			bypass = new SbFulfilmentBypass();
			
			headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
			headers.put("session_token", sessionToken);
			headers.put("version", "5.1.6");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
	        headers.put("latitude" , "12.836047");
	        headers.put("ipAddress" , "***********");
	        headers.put("isBusyBoxFound" , "false");
	        headers.put("osVersion" , "10");
	        headers.put("appLanguage","en");
	        headers.put("isDeviceRooted" , "false");
	        
	        
	        body.put("solutionType","xyz");

	        body.put("entityType","INDIVIDUAL");
	        body.put("mid","DHVRVL02894215991065");
	        body.put("userCustId","1001373711");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","6665550212");
	        body.put("deviceType","sound_box_3_0_4g");
	        body.put("deviceCategory","sound_box_3_0_4g");
	        body.put("isFromFSM","false");
	        body.put("deviceId","563747466003");
	        body.put("posId","POS1349570234775");
	        body.put("questionAlias","Select reason to close request");
	        body.put("answerAlias","Done with Backend JOT Form");
	        
	        response=services.v1FulfilmentBypass(bypass, body, headers);
	    
	        Assert.assertEquals(response.getStatusCode(), 400);
	        
		}


		@Test()
		public void verify_Status_Code_In_Case_Of_Invalid_Entity() {
			
			headers=new HashMap<>();
			body=new HashMap<>();
			services=new MiddlewareServices();
			bypass = new SbFulfilmentBypass();
			
			headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
			headers.put("session_token", sessionToken);
			headers.put("version", "5.1.6");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
	        headers.put("latitude" , "12.836047");
	        headers.put("ipAddress" , "***********");
	        headers.put("isBusyBoxFound" , "false");
	        headers.put("osVersion" , "10");
	        headers.put("appLanguage","en");
	        headers.put("isDeviceRooted" , "false");
	        
	        
	        body.put("solutionType","sound_box");
	        body.put("solutionTypeLevel2","sound_box_unbind");
	        body.put("entityType","XYz");
	        body.put("mid","DHVRVL02894215991065");
	        body.put("userCustId","1001373711");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","6665550212");
	        body.put("deviceType","sound_box_3_0_4g");
	        body.put("deviceCategory","sound_box_3_0_4g");
	        body.put("isFromFSM","false");
	        body.put("deviceId","563747466003");
	        body.put("posId","POS1349570234775");
	        body.put("questionAlias","Select reason to close request");
	        body.put("answerAlias","Done with Backend JOT Form");
	        
	        response=services.v1FulfilmentBypass(bypass, body, headers);
	    
	        Assert.assertEquals(response.getStatusCode(), 400);
	        
		}
		
		
		@Test()
		public void verify_Status_Code_In_Case_Of_Invalid_MID() {
			
			headers=new HashMap<>();
			body=new HashMap<>();
			services=new MiddlewareServices();
			bypass = new SbFulfilmentBypass();
			
			headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
			headers.put("session_token", sessionToken);
			headers.put("version", "5.1.6");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
	        headers.put("latitude" , "12.836047");
	        headers.put("ipAddress" , "***********");
	        headers.put("isBusyBoxFound" , "false");
	        headers.put("osVersion" , "10");
	        headers.put("appLanguage","en");
	        headers.put("isDeviceRooted" , "false");
	        
	        
	        body.put("solutionType","sound_box");
	        body.put("solutionTypeLevel2","sound_box_unbind");
	        body.put("entityType","INDIVIDUAL");
	        body.put("mid","xyz");
	        body.put("userCustId","1001373711");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","6665550212");
	        body.put("deviceType","sound_box_3_0_4g");
	        body.put("deviceCategory","sound_box_3_0_4g");
	        body.put("isFromFSM","false");
	        body.put("deviceId","563747466003");
	        body.put("posId","POS1349570234775");
	        body.put("questionAlias","Select reason to close request");
	        body.put("answerAlias","Done with Backend JOT Form");
	        
	        response=services.v1FulfilmentBypass(bypass, body, headers);
	    
	        Assert.assertEquals(response.getStatusCode(), 400);
	        
		}
		
		@Test()
		public void verify_Status_Code_In_Case_Of_Invalid_User_CustID() {
			
			headers=new HashMap<>();
			body=new HashMap<>();
			services=new MiddlewareServices();
			bypass = new SbFulfilmentBypass();
			
			headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
			headers.put("session_token", sessionToken);
			headers.put("version", "5.1.6");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
	        headers.put("latitude" , "12.836047");
	        headers.put("ipAddress" , "***********");
	        headers.put("isBusyBoxFound" , "false");
	        headers.put("osVersion" , "10");
	        headers.put("appLanguage","en");
	        headers.put("isDeviceRooted" , "false");
	        
	        
	        body.put("solutionType","sound_box");
	        body.put("solutionTypeLevel2","sound_box_unbind");
	        body.put("entityType","INDIVIDUAL");
	        body.put("mid","DHVRVL02893215991065");
	        body.put("userCustId","abc");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","6665550212");
	        body.put("deviceType","sound_box_3_0_4g");
	        body.put("deviceCategory","sound_box_3_0_4g");
	        body.put("isFromFSM","false");
	        body.put("deviceId","563747466003");
	        body.put("posId","POS1349570234775");
	        body.put("questionAlias","Select reason to close request");
	        body.put("answerAlias","Done with Backend JOT Form");
	        
	        response=services.v1FulfilmentBypass(bypass, body, headers);
	    
	        Assert.assertEquals(response.getStatusCode(), 500);
	        
		}
		
		@Test()
		public void verify_Status_Code_In_Case_Of_Invalid_AgentCustID() {
			
			headers=new HashMap<>();
			body=new HashMap<>();
			services=new MiddlewareServices();
			bypass = new SbFulfilmentBypass();
			
			headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
			headers.put("session_token", sessionToken);
			headers.put("version", "5.1.6");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
	        headers.put("latitude" , "12.836047");
	        headers.put("ipAddress" , "***********");
	        headers.put("isBusyBoxFound" , "false");
	        headers.put("osVersion" , "10");
	        headers.put("appLanguage","en");
	        headers.put("isDeviceRooted" , "false");
	        
	        
	        body.put("solutionType","sound_box");
	        body.put("solutionTypeLevel2","sound_box_unbind");
	        body.put("entityType","INDIVIDUAL");
	        body.put("mid","DHVRVL02894215991065");
	        body.put("userCustId","1001373711");
	        body.put("agentCustId","11071a95733");
	        body.put("userMobile","6665550212");
	        body.put("deviceType","sound_box_3_0_4g");
	        body.put("deviceCategory","sound_box_3_0_4g");
	        body.put("isFromFSM","false");
	        body.put("deviceId","563747466003");
	        body.put("posId","POS1349570234775");
	        body.put("questionAlias","Select reason to close request");
	        body.put("answerAlias","Done with Backend JOT Form");
	        
	        response=services.v1FulfilmentBypass(bypass, body, headers);
	    
	        Assert.assertEquals(response.getStatusCode(), 500);
	  	}
		
		@Test()
		public void verify_Status_Code_In_Case_Of_Invalid_User_Mobile() {
			
			headers=new HashMap<>();
			body=new HashMap<>();
			services=new MiddlewareServices();
			bypass = new SbFulfilmentBypass();
			
			headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
			headers.put("session_token", sessionToken);
			headers.put("version", "5.1.6");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
	        headers.put("latitude" , "12.836047");
	        headers.put("ipAddress" , "***********");
	        headers.put("isBusyBoxFound" , "false");
	        headers.put("osVersion" , "10");
	        headers.put("appLanguage","en");
	        headers.put("isDeviceRooted" , "false");
	        
	        
	        body.put("solutionType","sound_box");
	        body.put("solutionTypeLevel2","sound_box_unbind");
	        body.put("entityType","INDIVIDUAL");
	        body.put("mid","DHVRVL02894215991065");
	        body.put("userCustId","1001373711");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","6665550212990");
	        body.put("deviceType","sound_box_3_0_4g");
	        body.put("deviceCategory","sound_box_3_0_4g");
	        body.put("isFromFSM","false");
	        body.put("deviceId","563747466003");
	        body.put("posId","POS1349570234775");
	        body.put("questionAlias","Select reason to close request");
	        body.put("answerAlias","Done with Backend JOT Form");
	        
	        response=services.v1FulfilmentBypass(bypass, body, headers);
	    
	        Assert.assertEquals(response.getStatusCode(), 500);
	  	}
		
		@Test()
		public void verify_Status_Code_In_Case_Of_Invalid_Device_Type() {
			
			headers=new HashMap<>();
			body=new HashMap<>();
			services=new MiddlewareServices();
			bypass = new SbFulfilmentBypass();
			
			headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
			headers.put("session_token", sessionToken);
			headers.put("version", "5.1.6");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
	        headers.put("latitude" , "12.836047");
	        headers.put("ipAddress" , "***********");
	        headers.put("isBusyBoxFound" , "false");
	        headers.put("osVersion" , "10");
	        headers.put("appLanguage","en");
	        headers.put("isDeviceRooted" , "false");
	        
	        
	        body.put("solutionType","sound_box");
	        body.put("solutionTypeLevel2","sound_box_unbind");
	        body.put("entityType","INDIVIDUAL");
	        body.put("mid","DHVRVL02894215991065");
	        body.put("userCustId","1001373711");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","6665550212990");
	        body.put("deviceType","sound_box_3_0_New");
	        body.put("deviceCategory","sound_box_3_0_4g");
	        body.put("isFromFSM","false");
	        body.put("deviceId","563747466003");
	        body.put("posId","POS1349570234775");
	        body.put("questionAlias","Select reason to close request");
	        body.put("answerAlias","Done with Backend JOT Form");
	        
	        response=services.v1FulfilmentBypass(bypass, body, headers);
	    
	        Assert.assertEquals(response.getStatusCode(), 500);
	  	}
		
		@Test()
		public void verify_Status_Code_In_Case_Of_Success_When_Device_ID_Is_Not_Passed_In_Request() {
			
			headers=new HashMap<>();
			body=new HashMap<>();
			services=new MiddlewareServices();
			bypass = new SbFulfilmentBypass();
			
			headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
			headers.put("session_token", sessionToken);
			headers.put("version", "5.1.6");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
	        headers.put("latitude" , "12.836047");
	        headers.put("ipAddress" , "***********");
	        headers.put("isBusyBoxFound" , "false");
	        headers.put("osVersion" , "10");
	        headers.put("appLanguage","en");
	        headers.put("isDeviceRooted" , "false");
	        
	        
	        body.put("solutionType","sound_box");
	        body.put("solutionTypeLevel2","sound_box_unbind");
	        body.put("entityType","INDIVIDUAL");
	        body.put("mid","DHVRVL02894215991065");
	        body.put("userCustId","1001373711");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","6665550212");
	        body.put("deviceType","sound_box_3_0_4g");
	        body.put("isFromFSM","false");

	        body.put("posId","POS1349570234775");
	        body.put("questionAlias","Select reason to close request");
	        body.put("answerAlias","Done with Backend JOT Form");
	        
	        response=services.v1FulfilmentBypass(bypass, body, headers);
	    
	        Assert.assertEquals(response.getStatusCode(), 400);
	}
		
		@Test()
		public void verify_Status_Code_In_Case_Of_Success_When_SecondaryID_Is_Not_Passed_In_Request() {
			
			headers=new HashMap<>();
			body=new HashMap<>();
			services=new MiddlewareServices();
			bypass = new SbFulfilmentBypass();
			
			headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
			headers.put("session_token", sessionToken);
			headers.put("version", "5.1.6");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
	        headers.put("latitude" , "12.836047");
	        headers.put("ipAddress" , "***********");
	        headers.put("isBusyBoxFound" , "false");
	        headers.put("osVersion" , "10");
	        headers.put("appLanguage","en");
	        headers.put("isDeviceRooted" , "false");
	        
	        
	        body.put("solutionType","sound_box");
	        body.put("solutionTypeLevel2","sound_box_unbind");
	        body.put("entityType","INDIVIDUAL");
	        body.put("mid","DHVRVL02894215991065");
	        body.put("userCustId","1001373711");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","6665550212");
	        body.put("deviceType","sound_box_3_0_4g");
	        body.put("deviceCategory","sound_box_3_0_4g");
	        body.put("isFromFSM","false");
	        body.put("deviceId","563747466003");

	        body.put("questionAlias","Select reason to close request");
	        body.put("answerAlias","Done with Backend JOT Form");
	        
	        response=services.v1FulfilmentBypass(bypass, body, headers);
	    
	        Assert.assertEquals(response.getStatusCode(), 400);
	        Assert.assertTrue(response.path("displayMessage").toString().contains("Fulfilment bypassed done"));
	        
		}

		@Test()
		public void verify_Status_Code_In_Case_Of_Success_When_Question_Alias_Is_Not_Passed_In_Request() {
			
			headers=new HashMap<>();
			body=new HashMap<>();
			services=new MiddlewareServices();
			bypass = new SbFulfilmentBypass();
			
			headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
			headers.put("session_token", sessionToken);
			headers.put("version", "5.1.6");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
	        headers.put("latitude" , "12.836047");
	        headers.put("ipAddress" , "***********");
	        headers.put("isBusyBoxFound" , "false");
	        headers.put("osVersion" , "10");
	        headers.put("appLanguage","en");
	        headers.put("isDeviceRooted" , "false");
	        
	        
	        body.put("solutionType","sound_box");
	        body.put("solutionTypeLevel2","sound_box_unbind");
	        body.put("entityType","INDIVIDUAL");
	        body.put("mid","DHVRVL02894215991065");
	        body.put("userCustId","1001373711");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","6665550212");
	        body.put("deviceType","sound_box_3_0_4g");
	        body.put("deviceCategory","sound_box_3_0_4g");
	        body.put("isFromFSM","false");
	        body.put("deviceId","563747466003");
	        body.put("posId","POS1349570234775");
	        body.put("questionAlias","");
	        body.put("answerAlias","Done with Backend JOT Form");
	        
	        response=services.v1FulfilmentBypass(bypass, body, headers);
	    
	        Assert.assertEquals(response.getStatusCode(), 400);
		}
		
		@Test()
		public void verify_Status_Code_In_Case_Of_Success_When_Answer_Is_Empty_In_Request() {
			
			headers=new HashMap<>();
			body=new HashMap<>();
			services=new MiddlewareServices();
			bypass = new SbFulfilmentBypass();
			
			headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
			headers.put("session_token", sessionToken);
			headers.put("version", "5.1.6");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
	        headers.put("latitude" , "12.836047");
	        headers.put("ipAddress" , "***********");
	        headers.put("isBusyBoxFound" , "false");
	        headers.put("osVersion" , "10");
	        headers.put("appLanguage","en");
	        headers.put("isDeviceRooted" , "false");
	        
	        
	        body.put("solutionType","sound_box");
	        body.put("solutionTypeLevel2","sound_box_replace");
	        body.put("entityType","INDIVIDUAL");
	        body.put("mid","DHVRVL02894215991065");
	        body.put("userCustId","1001373711");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","6665550212");
	        body.put("deviceType","sound_box_3_0_4g");
	        body.put("deviceCategory","sound_box_3_0_4g");
	        body.put("isFromFSM","false");
	        body.put("deviceId","563747466003");
	        body.put("posId","POS1349570234775");
	        body.put("questionAlias","");
	        body.put("answerAlias","");
	        
	        response=services.v1FulfilmentBypass(bypass, body, headers);
	    
	        Assert.assertEquals(response.getStatusCode(), 400);
		}

		@Test()
		public void verify_Status_Code_In_Case_Of_Success_When_Auth_Token_Is_Invalid_Or_Missing() {
			
			headers=new HashMap<>();
			body=new HashMap<>();
			services=new MiddlewareServices();
			bypass = new SbFulfilmentBypass();
			
			headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
			headers.put("version", "5.1.6");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
	        headers.put("latitude" , "12.836047");
	        headers.put("ipAddress" , "***********");
	        headers.put("isBusyBoxFound" , "false");
	        headers.put("osVersion" , "10");
	        headers.put("appLanguage","en");
	        headers.put("isDeviceRooted" , "false");
	        
	        
	        body.put("solutionType","sound_box");
	        body.put("solutionTypeLevel2","sound_box_replace");
	        body.put("entityType","INDIVIDUAL");
	        body.put("mid","DHVRVL02894215991065");
	        body.put("userCustId","1001373711");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","6665550212");
	        body.put("deviceType","sound_box_3_0_4g");
	        body.put("deviceCategory","sound_box_3_0_4g");
	        body.put("isFromFSM","false");
	        body.put("deviceId","563747466003");
	        body.put("posId","POS1349570234775");
	        body.put("questionAlias","Select reason to close request");
	        body.put("answerAlias","Done with Backend JOT Form");
	        
	        response=services.v1FulfilmentBypass(bypass, body, headers);
	    
	        Assert.assertEquals(response.getStatusCode(), 401);
	      }
		
		@Test()
		public void verify_Status_Code_In_Case_Of_Success_When_Version_Is_Not_Passed_In_Request_Headers() {
			
			headers=new HashMap<>();
			body=new HashMap<>();
			services=new MiddlewareServices();
			bypass = new SbFulfilmentBypass();
			
			headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
			headers.put("session_token", sessionToken);
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("latitude" , "12.836047");
	        headers.put("ipAddress" , "***********");
	        headers.put("isBusyBoxFound" , "false");
	        headers.put("osVersion" , "10");
	        headers.put("appLanguage","en");
	        headers.put("isDeviceRooted" , "false");
	        
	        
	        body.put("solutionType","sound_box");
	        body.put("solutionTypeLevel2","sound_box_replace");
	        body.put("entityType","INDIVIDUAL");
	        body.put("mid","DHVRVL02894215991065");
	        body.put("userCustId","1001573711");
	        body.put("agentCustId","1107195733");
	        body.put("userMobile","6665550212");
	        body.put("deviceType","sound_box_3_0_4g");
	        body.put("deviceCategory","sound_box_3_0_4g");
	        body.put("isFromFSM","false");
	        body.put("deviceId","563747466003");
	        body.put("posId","POS1349570234775");
	        body.put("questionAlias","Select reason to close request");
	        body.put("answerAlias","Done with Backend JOT Form");
	        
	        response=services.v1FulfilmentBypass(bypass, body, headers);
	    
	        Assert.assertEquals(response.getStatusCode(), 200);
	      }
}