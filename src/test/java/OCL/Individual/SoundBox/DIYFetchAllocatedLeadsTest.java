package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.DIYFetchAllocated;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class DIYFetchAllocatedLeadsTest extends BaseMethod {

	DIYFetchAllocated leads;
	public String sessionToken;
	
	public Response response;
	public MiddlewareServices services;
	public Map<String,String> headers;
	public Map<String,String> params;
	
	
	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	@Test
	public void verify_Status_Code_In_Case_Of_Success_Response() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		leads=new DIYFetchAllocated();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");
    
        params.put("solutionType", "diy_sound_box");
        params.put("merchantMobile", "");
        params.put("page", "0");
        params.put("limit", "10");

        response=services.fetchLeadsForDIY(leads, params, headers);
        Assert.assertEquals(response.getStatusCode(),200);
      
	}
	
	@Test
	public void verify_Response_Code_When_Agent_Has_No_Leads_Assigned() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		leads=new DIYFetchAllocated();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");
    
        params.put("solutionType", "diy_sound_box");
        params.put("merchantMobile", "");
        params.put("page", "0");
        params.put("limit", "10");

        response=services.fetchLeadsForDIY(leads, params, headers);
        Assert.assertEquals(response.getStatusCode(),200);
      
	}
	
	
	
	@Test
	public void verify_Response_Body_Is_Empty_When_Agent_Has_No_Leads_Assigned() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		leads=new DIYFetchAllocated();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");
    
        params.put("solutionType", "diy_sound_box");
        params.put("merchantMobile", "");
        params.put("page", "0");
        params.put("limit", "10");

        response=services.fetchLeadsForDIY(leads, params, headers);
        Assert.assertEquals(response.getStatusCode(),200);
      
	}



@Test
public void verify_Response_Body_Contains_LeadID_When_Agent_Has_Leads_Assigned() {
	headers=new HashMap<>();
	params=new HashMap<>();
	services=new MiddlewareServices();
	leads=new DIYFetchAllocated();
	
	headers.put("session_token", sessionToken);
	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
	headers.put("version", "5.1.0");
    headers.put("Content-Type", "application/json; charset=UTF-8");
    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
    headers.put("latitude" , "12.836047");
    headers.put("ipAddress" , "***********");
    headers.put("isBusyBoxFound" , "false");
    headers.put("osVersion" , "10");
    headers.put("appLanguage","en");
    headers.put("client" , "androidapp");
    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
    headers.put("applanguage" , "en");
    headers.put("accept-language" , "en-US,en;q=0.9");
    headers.put("isdevicerooted" , "false");
    headers.put("osversion" , "11");

    params.put("solutionType", "diy_sound_box");
    params.put("merchantMobile", "");
    params.put("page", "0");
    params.put("limit", "10");

    response=services.fetchLeadsForDIY(leads, params, headers);
    Assert.assertEquals(response.getStatusCode(),200);

}



@Test
public void verify_productName_Is_Correct_In_Response_Body_For_Leads_Assigned() {
	headers=new HashMap<>();
	params=new HashMap<>();
	services=new MiddlewareServices();
	leads=new DIYFetchAllocated();
	
	headers.put("session_token", sessionToken);
	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
	headers.put("version", "5.1.0");
    headers.put("Content-Type", "application/json; charset=UTF-8");
    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
    headers.put("latitude" , "12.836047");
    headers.put("ipAddress" , "***********");
    headers.put("isBusyBoxFound" , "false");
    headers.put("osVersion" , "10");
    headers.put("appLanguage","en");
    headers.put("client" , "androidapp");
    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
    headers.put("applanguage" , "en");
    headers.put("accept-language" , "en-US,en;q=0.9");
    headers.put("isdevicerooted" , "false");
    headers.put("osversion" , "11");

    params.put("solutionType", "diy_sound_box");
    params.put("merchantMobile", "");
    params.put("page", "0");
    params.put("limit", "10");

    response=services.fetchLeadsForDIY(leads, params, headers);
    Assert.assertEquals(response.getStatusCode(),200);
  
}


//
//@Test
//public void verify_mobile_Is_Correct_In_Response_Body_For_Leads_Assigned() {
//	headers=new HashMap<>();
//	params=new HashMap<>();
//	services=new MiddlewareServices();
//	leads=new DIYFetchAllocated();
//	
//	headers.put("session_token", sessionToken);
//	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
//	headers.put("version", "5.1.0");
//    headers.put("Content-Type", "application/json; charset=UTF-8");
//    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//    headers.put("latitude" , "12.836047");
//    headers.put("ipAddress" , "***********");
//    headers.put("isBusyBoxFound" , "false");
//    headers.put("osVersion" , "10");
//    headers.put("appLanguage","en");
//    headers.put("client" , "androidapp");
//    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
//    headers.put("applanguage" , "en");
//    headers.put("accept-language" , "en-US,en;q=0.9");
//    headers.put("isdevicerooted" , "false");
//    headers.put("osversion" , "11");
//
//    params.put("solutionType", "diy_sound_box");
//    params.put("merchantMobile", "");
//    params.put("page", "0");
//    params.put("limit", "10");
//
//    response=services.fetchLeadsForDIY(leads, params, headers);
//    Assert.assertEquals(response.path("allocatedLeads[0].mobile").toString(), "7771110136");
//  
//}
//


//@Test
//public void verify_orderId_Is_Correct_In_Response_Body_For_Leads_Assigned() {
//	headers=new HashMap<>();
//	params=new HashMap<>();
//	services=new MiddlewareServices();
//	leads=new DIYFetchAllocated();
//	
//	headers.put("session_token", sessionToken);
//	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
//	headers.put("version", "5.1.0");
//    headers.put("Content-Type", "application/json; charset=UTF-8");
//    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//    headers.put("latitude" , "12.836047");
//    headers.put("ipAddress" , "***********");
//    headers.put("isBusyBoxFound" , "false");
//    headers.put("osVersion" , "10");
//    headers.put("appLanguage","en");
//    headers.put("client" , "androidapp");
//    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
//    headers.put("applanguage" , "en");
//    headers.put("accept-language" , "en-US,en;q=0.9");
//    headers.put("isdevicerooted" , "false");
//    headers.put("osversion" , "11");
//
//    params.put("solutionType", "diy_sound_box");
//    params.put("merchantMobile", "");
//    params.put("page", "0");
//    params.put("limit", "10");
//
//    response=services.fetchLeadsForDIY(leads, params, headers);
//    Assert.assertEquals(response.path("allocatedLeads[0].orderId").toString(), "100075756319");
//  
//}
//


//@Test
//public void verify_language_Is_Correct_In_Response_Body_For_Leads_Assigned() {
//	headers=new HashMap<>();
//	params=new HashMap<>();
//	services=new MiddlewareServices();
//	leads=new DIYFetchAllocated();
//	
//	headers.put("session_token", sessionToken);
//	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
//	headers.put("version", "5.1.0");
//    headers.put("Content-Type", "application/json; charset=UTF-8");
//    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//    headers.put("latitude" , "12.836047");
//    headers.put("ipAddress" , "***********");
//    headers.put("isBusyBoxFound" , "false");
//    headers.put("osVersion" , "10");
//    headers.put("appLanguage","en");
//    headers.put("client" , "androidapp");
//    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
//    headers.put("applanguage" , "en");
//    headers.put("accept-language" , "en-US,en;q=0.9");
//    headers.put("isdevicerooted" , "false");
//    headers.put("osversion" , "11");
//
//    params.put("solutionType", "diy_sound_box");
//    params.put("merchantMobile", "");
//    params.put("page", "0");
//    params.put("limit", "10");
//
//    response=services.fetchLeadsForDIY(leads, params, headers);
//    Assert.assertEquals(response.path("allocatedLeads[0].language").toString(), "Gujarati");
//  
//}

@Test
public void verify_sim_Is_Correct_In_Response_Body_For_Leads_Assigned() {
	headers=new HashMap<>();
	params=new HashMap<>();
	services=new MiddlewareServices();
	leads=new DIYFetchAllocated();
	
	headers.put("session_token", sessionToken);
	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
	headers.put("version", "5.1.0");
    headers.put("Content-Type", "application/json; charset=UTF-8");
    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
    headers.put("latitude" , "12.836047");
    headers.put("ipAddress" , "***********");
    headers.put("isBusyBoxFound" , "false");
    headers.put("osVersion" , "10");
    headers.put("appLanguage","en");
    headers.put("client" , "androidapp");
    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
    headers.put("applanguage" , "en");
    headers.put("accept-language" , "en-US,en;q=0.9");
    headers.put("isdevicerooted" , "false");
    headers.put("osversion" , "11");

    params.put("solutionType", "diy_sound_box");
    params.put("merchantMobile", "");
    params.put("page", "0");
    params.put("limit", "10");

    response=services.fetchLeadsForDIY(leads, params, headers);
    Assert.assertEquals(response.path("allocatedLeads[0].sim").toString(), "Vodafone");
  
}

//@Test
//public void verify_itemId_Is_Correct_In_Response_Body_For_Leads_Assigned() {
//	headers=new HashMap<>();
//	params=new HashMap<>();
//	services=new MiddlewareServices();
//	leads=new DIYFetchAllocated();
//	
//	headers.put("session_token", sessionToken);
//	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
//	headers.put("version", "5.1.0");
//    headers.put("Content-Type", "application/json; charset=UTF-8");
//    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//    headers.put("latitude" , "12.836047");
//    headers.put("ipAddress" , "***********");
//    headers.put("isBusyBoxFound" , "false");
//    headers.put("osVersion" , "10");
//    headers.put("appLanguage","en");
//    headers.put("client" , "androidapp");
//    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
//    headers.put("applanguage" , "en");
//    headers.put("accept-language" , "en-US,en;q=0.9");
//    headers.put("isdevicerooted" , "false");
//    headers.put("osversion" , "11");
//
//    params.put("solutionType", "diy_sound_box");
//    params.put("merchantMobile", "");
//    params.put("page", "0");
//    params.put("limit", "10");
//
//    response=services.fetchLeadsForDIY(leads, params, headers);
//    Assert.assertEquals(response.path("allocatedLeads[0].itemId").toString(), "1349569290127");
//  
//}

//@Test
//public void verify_only__Leads_For_Mobile_Number_Is_Returned_In_Response_Body_When_MobileNumber_Is_Passed_In_Request() {
//	headers=new HashMap<>();
//	params=new HashMap<>();
//	services=new MiddlewareServices();
//	leads=new DIYFetchAllocated();
//	
//	headers.put("session_token", sessionToken);
//	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
//	headers.put("version", "5.1.0");
//    headers.put("Content-Type", "application/json; charset=UTF-8");
//    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//    headers.put("latitude" , "12.836047");
//    headers.put("ipAddress" , "***********");
//    headers.put("isBusyBoxFound" , "false");
//    headers.put("osVersion" , "10");
//    headers.put("appLanguage","en");
//    headers.put("client" , "androidapp");
//    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
//    headers.put("applanguage" , "en");
//    headers.put("accept-language" , "en-US,en;q=0.9");
//    headers.put("isdevicerooted" , "false");
//    headers.put("osversion" , "11");
//
//    params.put("solutionType", "diy_sound_box");
//    params.put("merchantMobile", "7771110136");
//    params.put("page", "0");
//    params.put("limit", "10");
//
//    response=services.fetchLeadsForDIY(leads, params, headers);
//    Assert.assertEquals(response.path("allocatedLeads[0].itemId").toString(), "1349569290127");
//  
//}



@Test
public void verify_No__Leads_For__Is_Returned_In_Response_Body_When_Mechant_Has_No_Lead_For_MobileNumber_Passed_In_Request() {
	headers=new HashMap<>();
	params=new HashMap<>();
	services=new MiddlewareServices();
	leads=new DIYFetchAllocated();
	
	headers.put("session_token", sessionToken);
	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
	headers.put("version", "5.1.0");
    headers.put("Content-Type", "application/json; charset=UTF-8");
    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
    headers.put("latitude" , "12.836047");
    headers.put("ipAddress" , "***********");
    headers.put("isBusyBoxFound" , "false");
    headers.put("osVersion" , "10");
    headers.put("appLanguage","en");
    headers.put("client" , "androidapp");
    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
    headers.put("applanguage" , "en");
    headers.put("accept-language" , "en-US,en;q=0.9");
    headers.put("isdevicerooted" , "false");
    headers.put("osversion" , "11");

    params.put("solutionType", "diy_sound_box");
    params.put("merchantMobile", "7771110137");
    params.put("page", "0");
    params.put("limit", "10");

    response=services.fetchLeadsForDIY(leads, params, headers);
    Assert.assertEquals(response.path("allocatedLeads").toString(), "[]");
  
}

@Test
public void verify_Error_Code_When_Incorrect_Solution_Type_Passed_In_Request() {
	headers=new HashMap<>();
	params=new HashMap<>();
	services=new MiddlewareServices();
	leads=new DIYFetchAllocated();
	
	headers.put("session_token", sessionToken);
	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
	headers.put("version", "5.1.0");
    headers.put("Content-Type", "application/json; charset=UTF-8");
    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
    headers.put("latitude" , "12.836047");
    headers.put("ipAddress" , "***********");
    headers.put("isBusyBoxFound" , "false");
    headers.put("osVersion" , "10");
    headers.put("appLanguage","en");
    headers.put("client" , "androidapp");
    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
    headers.put("applanguage" , "en");
    headers.put("accept-language" , "en-US,en;q=0.9");
    headers.put("isdevicerooted" , "false");
    headers.put("osversion" , "11");

    params.put("solutionType", "diy_sound_box_xyz_qa");
    params.put("merchantMobile", "");
    params.put("page", "0");
    params.put("limit", "10");

    response=services.fetchLeadsForDIY(leads, params, headers);
    Assert.assertEquals(response.getStatusCode(),500);
  
}



@Test
public void verify_Error_Code_When__Solution_Type_Is_Empty_In_Request() {
	headers=new HashMap<>();
	params=new HashMap<>();
	services=new MiddlewareServices();
	leads=new DIYFetchAllocated();
	
	headers.put("session_token", sessionToken);
	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
	headers.put("version", "5.1.0");
    headers.put("Content-Type", "application/json; charset=UTF-8");
    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
    headers.put("latitude" , "12.836047");
    headers.put("ipAddress" , "***********");
    headers.put("isBusyBoxFound" , "false");
    headers.put("osVersion" , "10");
    headers.put("appLanguage","en");
    headers.put("client" , "androidapp");
    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
    headers.put("applanguage" , "en");
    headers.put("accept-language" , "en-US,en;q=0.9");
    headers.put("isdevicerooted" , "false");
    headers.put("osversion" , "11");

    params.put("solutionType", "");
    params.put("merchantMobile", "");
    params.put("page", "0");
    params.put("limit", "10");

    response=services.fetchLeadsForDIY(leads, params, headers);
    Assert.assertEquals(response.getStatusCode(),500);
  
}



@Test
public void verify_Error_Code_When__Solution_Type_Is_Valid_But__Other_Than_DIY_In_Request() {
	headers=new HashMap<>();
	params=new HashMap<>();
	services=new MiddlewareServices();
	leads=new DIYFetchAllocated();
	
	headers.put("session_token", sessionToken);
	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
	headers.put("version", "5.1.0");
    headers.put("Content-Type", "application/json; charset=UTF-8");
    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
    headers.put("latitude" , "12.836047");
    headers.put("ipAddress" , "***********");
    headers.put("isBusyBoxFound" , "false");
    headers.put("osVersion" , "10");
    headers.put("appLanguage","en");
    headers.put("client" , "androidapp");
    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
    headers.put("applanguage" , "en");
    headers.put("accept-language" , "en-US,en;q=0.9");
    headers.put("isdevicerooted" , "false");
    headers.put("osversion" , "11");

    params.put("solutionType","sound_box");
    params.put("merchantMobile", "");
    params.put("page", "0");
    params.put("limit", "10");

    response=services.fetchLeadsForDIY(leads, params, headers);
    Assert.assertEquals(response.path("statusCode").toString(),"500");
  
}


@Test
public void verify_Error_Code_In_Case_Page_Is_Missing_From_Request() {
	headers=new HashMap<>();
	params=new HashMap<>();
	services=new MiddlewareServices();
	leads=new DIYFetchAllocated();
	
	headers.put("session_token", sessionToken);
	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
	headers.put("version", "5.1.0");
    headers.put("Content-Type", "application/json; charset=UTF-8");
    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
    headers.put("latitude" , "12.836047");
    headers.put("ipAddress" , "***********");
    headers.put("isBusyBoxFound" , "false");
    headers.put("osVersion" , "10");
    headers.put("appLanguage","en");
    headers.put("client" , "androidapp");
    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
    headers.put("applanguage" , "en");
    headers.put("accept-language" , "en-US,en;q=0.9");
    headers.put("isdevicerooted" , "false");
    headers.put("osversion" , "11");

    params.put("solutionType", "diy_sound_box");
    params.put("merchantMobile", "");
  
    params.put("limit", "10");

    response=services.fetchLeadsForDIY(leads, params, headers);
    Assert.assertEquals(response.getStatusCode(),500);
  
}

@Test
public void verify_Error_Code_In_Case_Limit_Is_Missing_From_Request() {
	headers=new HashMap<>();
	params=new HashMap<>();
	services=new MiddlewareServices();
	leads=new DIYFetchAllocated();
	
	headers.put("session_token", sessionToken);
	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
	headers.put("version", "5.1.0");
    headers.put("Content-Type", "application/json; charset=UTF-8");
    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
    headers.put("latitude" , "12.836047");
    headers.put("ipAddress" , "***********");
    headers.put("isBusyBoxFound" , "false");
    headers.put("osVersion" , "10");
    headers.put("appLanguage","en");
    headers.put("client" , "androidapp");
    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
    headers.put("applanguage" , "en");
    headers.put("accept-language" , "en-US,en;q=0.9");
    headers.put("isdevicerooted" , "false");
    headers.put("osversion" , "11");

    params.put("solutionType", "diy_sound_box");
    params.put("merchantMobile", "");
    params.put("page", "0");
 
    response=services.fetchLeadsForDIY(leads, params, headers);
    Assert.assertEquals(response.getStatusCode(),500);
  
}


@Test
public void verify_Error_Code_In_Case_Token_Is_Missing_From_Request() {
	headers=new HashMap<>();
	params=new HashMap<>();
	services=new MiddlewareServices();
	leads=new DIYFetchAllocated();
	

	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
	headers.put("version", "5.1.0");
    headers.put("Content-Type", "application/json; charset=UTF-8");
    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
    headers.put("latitude" , "12.836047");
    headers.put("ipAddress" , "***********");
    headers.put("isBusyBoxFound" , "false");
    headers.put("osVersion" , "10");
    headers.put("appLanguage","en");
    headers.put("client" , "androidapp");
    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
    headers.put("applanguage" , "en");
    headers.put("accept-language" , "en-US,en;q=0.9");
    headers.put("isdevicerooted" , "false");
    headers.put("osversion" , "11");

    params.put("solutionType", "diy_sound_box");
    params.put("merchantMobile", "");
    params.put("page", "0");
    params.put("limit", "10");

    response=services.fetchLeadsForDIY(leads, params, headers);
    Assert.assertEquals(response.getStatusCode(),401);
  
}


@Test
public void verify_Error_Code_In_Case_MerchantMobile_Is_Missing_From_Request() {
	headers=new HashMap<>();
	params=new HashMap<>();
	services=new MiddlewareServices();
	leads=new DIYFetchAllocated();
	
	headers.put("session_token", sessionToken);
	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
	headers.put("version", "5.1.0");
    headers.put("Content-Type", "application/json; charset=UTF-8");
    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
    headers.put("latitude" , "12.836047");
    headers.put("ipAddress" , "***********");
    headers.put("isBusyBoxFound" , "false");
    headers.put("osVersion" , "10");
    headers.put("appLanguage","en");
    headers.put("client" , "androidapp");
    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
    headers.put("applanguage" , "en");
    headers.put("accept-language" , "en-US,en;q=0.9");
    headers.put("isdevicerooted" , "false");
    headers.put("osversion" , "11");

    params.put("solutionType", "diy_sound_box");
    params.put("page", "0");
    params.put("limit", "10");

    response=services.fetchLeadsForDIY(leads, params, headers);
    Assert.assertEquals(response.getStatusCode(),200);
  
}


@Test
public void verify_Error_Code_In_Case_Version_Is_Missing_From_Request() {
	headers=new HashMap<>();
	params=new HashMap<>();
	services=new MiddlewareServices();
	leads=new DIYFetchAllocated();
	
	headers.put("session_token", sessionToken);
	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");

    headers.put("Content-Type", "application/json; charset=UTF-8");
    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
    headers.put("latitude" , "12.836047");
    headers.put("ipAddress" , "***********");
    headers.put("isBusyBoxFound" , "false");
    headers.put("osVersion" , "10");
    headers.put("appLanguage","en");
    headers.put("client" , "androidapp");
    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
    headers.put("applanguage" , "en");
    headers.put("accept-language" , "en-US,en;q=0.9");
    headers.put("isdevicerooted" , "false");
    headers.put("osversion" , "11");

    params.put("solutionType", "diy_sound_box");
    params.put("page", "0");
    params.put("limit", "10");

    response=services.fetchLeadsForDIY(leads, params, headers);
    Assert.assertEquals(response.getStatusCode(),200);
  
}

@Test
public void verify_Error_Code_In_Case_DeviceIdentifier_Is_Missing_From_Request() {
	headers=new HashMap<>();
	params=new HashMap<>();
	services=new MiddlewareServices();
	leads=new DIYFetchAllocated();
	
	headers.put("session_token", sessionToken);
	

    headers.put("Content-Type", "application/json; charset=UTF-8");
    headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
    headers.put("latitude" , "12.836047");
    headers.put("ipAddress" , "***********");
    headers.put("isBusyBoxFound" , "false");
    headers.put("osVersion" , "10");
    headers.put("appLanguage","en");
    headers.put("client" , "androidapp");
    headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
    headers.put("applanguage" , "en");
    headers.put("accept-language" , "en-US,en;q=0.9");
    headers.put("isdevicerooted" , "false");
    headers.put("osversion" , "11");

    params.put("solutionType", "diy_sound_box");
    params.put("page", "0");
    params.put("limit", "10");

    response=services.fetchLeadsForDIY(leads, params, headers);
    Assert.assertEquals(response.getStatusCode(),200);
  
}


}