package OCL.Individual.SoundBox;

import Request.SoundBox.*;
import Services.MechantService.MiddlewareServices;
import TestingLogics.TEST;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class SoundboxReplacementTest extends BaseMethod {
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(SoundboxReplacementTest.class);

    String AgentToken ;
    String version="7.1.7";
    String agentCustId="1001647902";
    String entityType="INDIVIDUAL";
    String mid="mhdUSq78940337659669";
    String posIds="DEFAULT";
    String deviceId="563984423430026";
    String deviceType="SOUNDBOX_3_BT";
    String userCustId="1702999343";
    String userMobile="9625807412";
    String reason="Service replacement";
    String reasonAlias="service_replacement";
    String questionAlias="Reason For Replacement";
    String answerAlias="service_replacement";
    String type="4G";
    String model="SOUNDBOX 3.0 BT";
    String leadId="";


    @BeforeMethod
    public void AgentLoginSoundBox() throws Exception {
        AgentToken = AgentSessionToken("7771216290", "paytm@123");
        LOGGER.info("Agent Token  for Soundbox : " + AgentToken);
        establishConnectiontoServer(AgentToken,5);

    }



    @Test(priority = 1)
    public void SoundboxReplacementFetchMid() {

            Map<String,String> headers = setCommonHeaders();
            Map<String,String> queryParams = new HashMap<>();
            queryParams.put("merchantCustId",userCustId);
            queryParams.put("solutionType","sound_box");
            queryParams.put("solutionSubType","sound_box_replacement");

            GetMerchantid GetMerchantidobj = new GetMerchantid();
            Response response = middlewareServicesObject.getmerchant(GetMerchantidobj,queryParams,headers);
            int statuscode = response.getStatusCode();
            Assert.assertEquals(statuscode,200);
            String mid= response.body().jsonPath().getString("mids[0].mid");
            Assert.assertEquals(mid,"mhdUSq78940337659669");

    }

    @Test(priority = 2)
    public void SoundboxReplacementCreateLeadWithEmptySessionToken() {

        Map<String,String> headers = setCommonHeaders();
        headers.put("session_token", "");
        Map<String,String> body = new HashMap<>();
        body.put("agentCustId" , agentCustId);
        body.put("entityType" , entityType);
        body.put("merchantName" ,"Aashit Display");
        body.put("mid" ,mid );
        body.put("posIds" ,posIds );
        body.put("solutionSubType" , "sound_box_replacement");
        body.put("deviceId" , deviceId);
        body.put("deviceType" , deviceType);
        body.put("reason", reason);
        body.put("reasonAlias",reasonAlias);
        body.put("questionAlias", questionAlias);
        body.put("answerAlias", answerAlias);
        body.put("userCustId" , userCustId);
        body.put("userMobile",userMobile);
        body.put("type",type);
        body.put("model",model);

        CreateSoundboxReplacementLead createSoundboxReplacementLead = new CreateSoundboxReplacementLead();
        Response response = middlewareServicesObject.SoundboxReplacementLead(createSoundboxReplacementLead , headers , body);
        int StatusCode = response.statusCode();
        Assert.assertEquals(StatusCode , 401);


    }
    @Test(priority = 3)
    public void SoundboxReplacementCreateLeadWithEmptyVersion() {

        Map<String,String> headers = setCommonHeaders();
        headers.put("version", "");
        Map<String,String> body = new HashMap<>();
        body.put("agentCustId" , agentCustId);
        body.put("entityType" , entityType);
        body.put("merchantName" ,"Aashit Display");
        body.put("mid" ,mid );
        body.put("posIds" ,posIds );
        body.put("solutionSubType" , "sound_box_replacement");
        body.put("deviceId" , deviceId);
        body.put("deviceType" , deviceType);
        body.put("reason", reason);
        body.put("reasonAlias",reasonAlias);
        body.put("questionAlias", questionAlias);
        body.put("answerAlias", answerAlias);
        body.put("userCustId" , userCustId);
        body.put("userMobile",userMobile);
        body.put("type",type);
        body.put("model",model);

        CreateSoundboxReplacementLead createSoundboxReplacementLead = new CreateSoundboxReplacementLead();
        Response response = middlewareServicesObject.SoundboxReplacementLead(createSoundboxReplacementLead , headers , body);
        int StatusCode = response.statusCode();
        String displayMessage= response.body().jsonPath().get("displayMessage").toString();
        Assert.assertEquals(StatusCode , 200);
        Assert.assertTrue(displayMessage.contains("version is empty in header"));


    }
    @Test(priority = 4)
    public void SoundboxReplacementCreateLeadWithEmptyEntityType() {
        Map<String,String> headers = setCommonHeaders();
        Map<String,String> body = new HashMap<>();
        body.put("agentCustId" , agentCustId);
        body.put("entityType" , "");
        body.put("merchantName" ,"Aashit Display");
        body.put("mid" ,mid );
        body.put("posIds" ,posIds );
        body.put("solutionSubType" , "sound_box_replacement");
        body.put("deviceId" , deviceId);
        body.put("deviceType" , deviceType);
        body.put("reason", reason);
        body.put("reasonAlias",reasonAlias);
        body.put("questionAlias", questionAlias);
        body.put("answerAlias", answerAlias);
        body.put("userCustId" , userCustId);
        body.put("userMobile",userMobile);
        body.put("type",type);
        body.put("model",model);

        CreateSoundboxReplacementLead createSoundboxReplacementLead = new CreateSoundboxReplacementLead();
        Response response = middlewareServicesObject.SoundboxReplacementLead(createSoundboxReplacementLead , headers , body);
        int StatusCode = response.statusCode();
        Assert.assertEquals(StatusCode , 400);



    }
    @Test(priority = 5)
    public void SoundboxReplacementCreateLeadWithEmptyMid() {
        Map<String,String> headers = setCommonHeaders();
        Map<String,String> body = new HashMap<>();
        body.put("agentCustId" , agentCustId);
        body.put("entityType" , entityType);
        body.put("merchantName" ,"Aashit Display");
        body.put("mid" ,"");
        body.put("posIds" ,posIds );
        body.put("solutionSubType" , "sound_box_replacement");
        body.put("deviceId" , deviceId);
        body.put("deviceType" , deviceType);
        body.put("reason", reason);
        body.put("reasonAlias",reasonAlias);
        body.put("questionAlias", questionAlias);
        body.put("answerAlias", answerAlias);
        body.put("userCustId" , userCustId);
        body.put("userMobile",userMobile);
        body.put("type",type);
        body.put("model",model);

        CreateSoundboxReplacementLead createSoundboxReplacementLead = new CreateSoundboxReplacementLead();
        Response response = middlewareServicesObject.SoundboxReplacementLead(createSoundboxReplacementLead , headers , body);
        int StatusCode = response.statusCode();
        Assert.assertEquals(StatusCode , 400);



    }
    @Test(priority = 6)
    public void SoundboxReplacementCreateLeadWithEmptyDeviceId() {
        Map<String,String> headers = setCommonHeaders();
        Map<String,String> body = new HashMap<>();
        body.put("agentCustId" , agentCustId);
        body.put("entityType" , entityType);
        body.put("merchantName" ,"Aashit Display");
        body.put("mid" ,mid);
        body.put("posIds" ,posIds );
        body.put("solutionSubType" , "sound_box_replacement");
        body.put("deviceId" , "");
        body.put("deviceType" , deviceType);
        body.put("reason", reason);
        body.put("reasonAlias",reasonAlias);
        body.put("questionAlias", questionAlias);
        body.put("answerAlias", answerAlias);
        body.put("userCustId" , userCustId);
        body.put("userMobile",userMobile);
        body.put("type",type);
        body.put("model",model);

        CreateSoundboxReplacementLead createSoundboxReplacementLead = new CreateSoundboxReplacementLead();
        Response response = middlewareServicesObject.SoundboxReplacementLead(createSoundboxReplacementLead , headers , body);
        int StatusCode = response.statusCode();
        Assert.assertEquals(StatusCode , 400);

    }
    @Test(priority = 7)
    public void SoundboxReplacementCreateLeadWithEmptyDevicetype() {
        Map<String,String> headers = setCommonHeaders();
        Map<String,String> body = new HashMap<>();
        body.put("agentCustId" , agentCustId);
        body.put("entityType" , entityType);
        body.put("merchantName" ,"Aashit Display");
        body.put("mid" ,mid);
        body.put("posIds" ,posIds );
        body.put("solutionSubType" , "sound_box_replacement");
        body.put("deviceId" , deviceId);
        body.put("deviceType" , "");
        body.put("reason", reason);
        body.put("reasonAlias",reasonAlias);
        body.put("questionAlias", questionAlias);
        body.put("answerAlias", answerAlias);
        body.put("userCustId" , userCustId);
        body.put("userMobile",userMobile);
        body.put("type",type);
        body.put("model",model);

        CreateSoundboxReplacementLead createSoundboxReplacementLead = new CreateSoundboxReplacementLead();
        Response response = middlewareServicesObject.SoundboxReplacementLead(createSoundboxReplacementLead , headers , body);
        int StatusCode = response.statusCode();
        Assert.assertEquals(StatusCode , 400);

    }
    @Test(priority = 8)
    public void SoundboxReplacementCreateLeadWithEmptyReason() {
        Map<String,String> headers = setCommonHeaders();
        Map<String,String> body = new HashMap<>();
        body.put("agentCustId" , agentCustId);
        body.put("entityType" , entityType);
        body.put("merchantName" ,"Aashit Display");
        body.put("mid" ,mid);
        body.put("posIds" ,posIds );
        body.put("solutionSubType" , "sound_box_replacement");
        body.put("deviceId" , deviceId);
        body.put("deviceType" , deviceType);
        body.put("reason", "");
        body.put("reasonAlias",reasonAlias);
        body.put("questionAlias", questionAlias);
        body.put("answerAlias", answerAlias);
        body.put("userCustId" , userCustId);
        body.put("userMobile",userMobile);
        body.put("type",type);
        body.put("model",model);

        CreateSoundboxReplacementLead createSoundboxReplacementLead = new CreateSoundboxReplacementLead();
        Response response = middlewareServicesObject.SoundboxReplacementLead(createSoundboxReplacementLead , headers , body);
        int StatusCode = response.statusCode();
        Assert.assertEquals(StatusCode , 400);

    }
    @Test(priority = 9)
    public void SoundboxReplacementCreateLeadWithEmptyUserCustId() {
        Map<String,String> headers = setCommonHeaders();
        Map<String,String> body = new HashMap<>();
        body.put("agentCustId" , agentCustId);
        body.put("entityType" , entityType);
        body.put("merchantName" ,"Aashit Display");
        body.put("mid" ,mid);
        body.put("posIds" ,posIds );
        body.put("solutionSubType" , "sound_box_replacement");
        body.put("deviceId" , deviceId);
        body.put("deviceType" , deviceType);
        body.put("reason", reason);
        body.put("reasonAlias",reasonAlias);
        body.put("questionAlias", questionAlias);
        body.put("answerAlias", answerAlias);
        body.put("userCustId" , "");
        body.put("userMobile",userMobile);
        body.put("type",type);
        body.put("model",model);

        CreateSoundboxReplacementLead createSoundboxReplacementLead = new CreateSoundboxReplacementLead();
        Response response = middlewareServicesObject.SoundboxReplacementLead(createSoundboxReplacementLead , headers , body);
        int StatusCode = response.statusCode();
        Assert.assertEquals(StatusCode , 400);

    }
    @Test(priority = 10)
    public void SoundboxReplacementCreateLeadWithEmptyUserMobile() {
        Map<String,String> headers = setCommonHeaders();
        Map<String,String> body = new HashMap<>();
        body.put("agentCustId" , agentCustId);
        body.put("entityType" , entityType);
        body.put("merchantName" ,"Aashit Display");
        body.put("mid" ,mid);
        body.put("posIds" ,posIds );
        body.put("solutionSubType" , "sound_box_replacement");
        body.put("deviceId" , deviceId);
        body.put("deviceType" , deviceType);
        body.put("reason", reason);
        body.put("reasonAlias",reasonAlias);
        body.put("questionAlias", questionAlias);
        body.put("answerAlias", answerAlias);
        body.put("userCustId" , userCustId);
        body.put("userMobile","");
        body.put("type",type);
        body.put("model",model);

        CreateSoundboxReplacementLead createSoundboxReplacementLead = new CreateSoundboxReplacementLead();
        Response response = middlewareServicesObject.SoundboxReplacementLead(createSoundboxReplacementLead , headers , body);
        int StatusCode = response.statusCode();
        Assert.assertEquals(StatusCode , 400);

    }
    @Test(priority = 11)
    public void SoundboxReplacementCreateLeadWithEmptyAgentCustId() {
        Map<String,String> headers = setCommonHeaders();
        Map<String,String> body = new HashMap<>();
        body.put("agentCustId" , "");
        body.put("entityType" , entityType);
        body.put("merchantName" ,"Aashit Display");
        body.put("mid" ,mid);
        body.put("posIds" ,posIds );
        body.put("solutionSubType" , "sound_box_replacement");
        body.put("deviceId" , deviceId);
        body.put("deviceType" , deviceType);
        body.put("reason", reason);
        body.put("reasonAlias",reasonAlias);
        body.put("questionAlias", questionAlias);
        body.put("answerAlias", answerAlias);
        body.put("userCustId" , userCustId);
        body.put("userMobile",userMobile);
        body.put("type",type);
        body.put("model",model);

        CreateSoundboxReplacementLead createSoundboxReplacementLead = new CreateSoundboxReplacementLead();
        Response response = middlewareServicesObject.SoundboxReplacementLead(createSoundboxReplacementLead , headers , body);
        int StatusCode = response.statusCode();
        Assert.assertEquals(StatusCode , 400);

    }

    @Test(priority = 12)
    public void SoundboxReplacementCreateLead() {

        Map<String,String> headers = setCommonHeaders();
        Map<String,String> body = new HashMap<>();
        body.put("agentCustId" , agentCustId);
        body.put("entityType" , entityType);
        body.put("merchantName" ,"Aashit Display");
        body.put("mid" ,mid );
        body.put("posIds" ,posIds );
        body.put("solutionSubType" , "sound_box_replacement");
        body.put("deviceId" , deviceId);
        body.put("deviceType" , deviceType);
        body.put("reason", "Service replacement");
        body.put("reasonAlias","service_replacement");
        body.put("questionAlias", "Reason For Replacement");
        body.put("answerAlias", "service_replacement");
        body.put("userCustId" , userCustId);
        body.put("userMobile",userMobile);
        body.put("type","4G");
        body.put("model","SOUNDBOX 3.0 BT");

        CreateSoundboxReplacementLead createSoundboxReplacementLead = new CreateSoundboxReplacementLead();
        Response response = middlewareServicesObject.SoundboxReplacementLead(createSoundboxReplacementLead , headers , body);
        int StatusCode = response.statusCode();
     /*   String displayMessage= response.body().jsonPath().get("displayMessage").toString();
        Assert.assertEquals(StatusCode , 200);
        Assert.assertTrue(displayMessage.contains("Lead successfully created."));
        leadId = response.jsonPath().get("leadId").toString();*/

    }
    @Test(priority = 13)
    public void SoundboxReplacementCreateLeadSameRequest() {

        Map<String,String> headers = setCommonHeaders();
        Map<String,String> body = new HashMap<>();
        body.put("agentCustId" , agentCustId);
        body.put("entityType" , entityType);
        body.put("merchantName" ,"Aashit Display");
        body.put("mid" ,mid );
        body.put("posIds" ,posIds );
        body.put("solutionSubType" , "sound_box_replacement");
        body.put("deviceId" , deviceId);
        body.put("deviceType" , deviceType);
        body.put("reason", "Service replacement");
        body.put("reasonAlias","service_replacement");
        body.put("questionAlias", "Reason For Replacement");
        body.put("answerAlias", "service_replacement");
        body.put("userCustId" , userCustId);
        body.put("userMobile",userMobile);
        body.put("type","4G");
        body.put("model","SOUNDBOX 3.0 BT");

        CreateSoundboxReplacementLead createSoundboxReplacementLead = new CreateSoundboxReplacementLead();
        Response response = middlewareServicesObject.SoundboxReplacementLead(createSoundboxReplacementLead , headers , body);
        int StatusCode = response.getStatusCode();
     /*   Assert.assertEquals(StatusCode , 200);
        Assert.assertTrue(displayMessage.contains("Lead already exists. Please continue working on same lead"));

*/
    }

    @Test(priority = 14)
    public void SoundboxReplacementFetchLead() {

        Map<String,String> headers = setCommonHeaders();
        Map<String,String> queryParams = new HashMap<>();
        queryParams.put("solutionType","sound_box");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("leadId",leadId);
        FetchSoundboxReplacementLead fetchSoundboxReplacementLead = new FetchSoundboxReplacementLead(userCustId);
        Response response = middlewareServicesObject.FetchSoundboxReplacementLead(fetchSoundboxReplacementLead , headers , queryParams);
   /*     int StatusCode = response.statusCode();
        Assert.assertEquals(StatusCode , 200);
        JsonPath jsonPath = response.jsonPath();
        Assert.assertEquals(jsonPath.getString("merchantDetails.solutionType"), "sound_box");
        Assert.assertEquals(jsonPath.getString("merchantDetails.solutionTypeLevel2"), "sound_box_replacement");
        Assert.assertEquals(jsonPath.getString("merchantDetails.questionAnswerList[0].questionAlias"), "Reason For Replacement");
        Assert.assertEquals(jsonPath.getString("merchantDetails.questionAnswerList[0].answerAlias"), "service_replacement");
        Assert.assertEquals(jsonPath.getString("merchantDetails.substage"), "Lead Created");*/


    }

    @Test(priority = 15)
    public void SoundboxReplacementFetchLeadWithEmptyLeadId() {

        Map<String,String> headers = setCommonHeaders();
        Map<String,String> queryParams = new HashMap<>();
        queryParams.put("solutionType","sound_box");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("leadId","");
        FetchSoundboxReplacementLead fetchSoundboxReplacementLead = new FetchSoundboxReplacementLead(userCustId);
        Response response = middlewareServicesObject.FetchSoundboxReplacementLead(fetchSoundboxReplacementLead , headers , queryParams);
        int StatusCode = response.statusCode();
       /* Assert.assertEquals(StatusCode , 200);
        String displayMessage= response.body().jsonPath().get("displayMessage").toString();
        Assert.assertTrue(displayMessage.contains("Data Not present for customer"));*/

    }

    @Test(priority = 15)
    public void SoundboxReplacementFetchLeadWithInvalidLeadId() {

        Map<String,String> headers = setCommonHeaders();
        Map<String,String> queryParams = new HashMap<>();
        queryParams.put("solutionType","sound_box");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("leadId","abcd");
        FetchSoundboxReplacementLead fetchSoundboxReplacementLead = new FetchSoundboxReplacementLead(userCustId);
        Response response = middlewareServicesObject.FetchSoundboxReplacementLead(fetchSoundboxReplacementLead , headers , queryParams);
        int StatusCode = response.statusCode();
        Assert.assertEquals(StatusCode , 200);
        String displayMessage= response.body().jsonPath().get("displayMessage").toString();
        Assert.assertTrue(displayMessage.contains("Data Not present for customer"));

    }

    @Test(priority = 16)
    public void SoundboxReplacementValidateOldDevice() {

        Map<String,String> headers = setCommonHeaders();
        Map<String,String> body = new HashMap<>();
        body.put("agentCustId" , agentCustId);
        body.put("deviceId" , deviceId);
        body.put("leadId" ,leadId);
        body.put("scannedBarcodeType" ,"ORIGINAL" );

        ValidateOldDevice validateOldDevice = new ValidateOldDevice();
        Response response = middlewareServicesObject.validateOldDevice(validateOldDevice , headers ,body);
        int StatusCode = response.statusCode();

    }



    @Test(priority = 17)
    public void SoundboxReplacementValidateOldDeviceWithInvalidDeviceId() {

        Map<String,String> headers = setCommonHeaders();
        Map<String,String> body = new HashMap<>();
        body.put("agentCustId" , agentCustId);
        body.put("deviceId" , "7568675888");
        body.put("leadId" ,leadId);
        body.put("scannedBarcodeType" ,"ORIGINAL" );

        ValidateOldDevice validateOldDevice = new ValidateOldDevice();
        Response response = middlewareServicesObject.validateOldDevice(validateOldDevice , headers ,body);
        int StatusCode = response.statusCode();

    }

    @Test(priority = 18)
    public void SoundboxReplacementFetchBatteryQuestions() {

        Map<String,String> headers = setCommonHeaders();
        Map<String,String> queryParams = new HashMap<>();
        queryParams.put("assetSubType","battery");
        queryParams.put("solutionType","sound_box");
        queryParams.put("solutionSubType","sound_box_replacement");
        queryParams.put("leadId",leadId);
        queryParams.put("serialNo",deviceId);
        queryParams.put("modelName",model);
        queryParams.put("deviceType",type);

        FetchDeviceQuestions fetchDeviceQuestions = new FetchDeviceQuestions();
        Response response = middlewareServicesObject.fetchDeviceQuestions(fetchDeviceQuestions ,queryParams, headers);
        int StatusCode = response.statusCode();
        Assert.assertEquals(StatusCode,200);

    }
    @Test(priority = 20)
    public void SoundboxReplacementFetchBatteryQuestionsInvalidLeadId() {

        Map<String,String> headers = setCommonHeaders();
        Map<String,String> queryParams = new HashMap<>();
        queryParams.put("assetSubType","battery");
        queryParams.put("solutionType","sound_box");
        queryParams.put("solutionSubType","sound_box_replacement");
        queryParams.put("leadId","sdvsdvsv");
        queryParams.put("serialNo",deviceId);
        queryParams.put("modelName",model);
        queryParams.put("deviceType",type);

        FetchDeviceQuestions fetchDeviceQuestions = new FetchDeviceQuestions();
        Response response = middlewareServicesObject.fetchDeviceQuestions(fetchDeviceQuestions ,queryParams, headers);
        int StatusCode = response.statusCode();
        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 21)
    public void SoundboxReplacementFetchDeviceQuestionsInvalidSerialNo() {

        Map<String,String> headers = setCommonHeaders();
        Map<String,String> queryParams = new HashMap<>();
        queryParams.put("assetSubType","battery");
        queryParams.put("solutionType","sound_box");
        queryParams.put("solutionSubType","sound_box_replacement");
        queryParams.put("leadId",leadId);
        queryParams.put("serialNo","8767878");
        queryParams.put("modelName",model);
        queryParams.put("deviceType",type);

        FetchDeviceQuestions fetchDeviceQuestions = new FetchDeviceQuestions();
        Response response = middlewareServicesObject.fetchDeviceQuestions(fetchDeviceQuestions ,queryParams, headers);
        int StatusCode = response.statusCode();
        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 19)
    public void SoundboxReplacementFetchChargerQuestions() {

        Map<String,String> headers = setCommonHeaders();
        Map<String,String> queryParams = new HashMap<>();
        queryParams.put("assetSubType","charger");
        queryParams.put("solutionType","sound_box");
        queryParams.put("solutionSubType","sound_box_replacement");
        queryParams.put("leadId",leadId);
        queryParams.put("serialNo",deviceId);
        queryParams.put("modelName",model);
        queryParams.put("deviceType",type);

        FetchDeviceQuestions fetchDeviceQuestions = new FetchDeviceQuestions();
        Response response = middlewareServicesObject.fetchDeviceQuestions(fetchDeviceQuestions ,queryParams, headers);
        int StatusCode = response.statusCode();


    }


    @Test(priority = 22)
    public void SoundboxReplacementFetchDeviceConditionsQuestions() {

        Map<String,String> headers = setCommonHeaders();
        Map<String,String> queryParams = new HashMap<>();
        queryParams.put("assetSubType","deviceCondition");
        queryParams.put("solutionType","sound_box");
        queryParams.put("solutionSubType","sound_box_replacement");
        queryParams.put("leadId",leadId);
        queryParams.put("serialNo",deviceId);
        queryParams.put("modelName",model);
        queryParams.put("deviceType",type);

        FetchDeviceQuestions fetchDeviceQuestions = new FetchDeviceQuestions();
        Response response = middlewareServicesObject.fetchDeviceQuestions(fetchDeviceQuestions ,queryParams, headers);
        int StatusCode = response.statusCode();


    }




    private Map<String, String> setCommonHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("X-SRC", "GGClient");
        headers.put("version", version);
        headers.put("appLanguage", "en");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("session_token", AgentToken);
        headers.put("latitude", "28.5772544");
        headers.put("longitude", "77.4384498");
        headers.put("ipAddress", "**************");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "Redmi_Note_5_Pro");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "Xiaomi-RedmiNote5Pro-861181041338761");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "08:25:25:99:13:38");
        headers.put("deviceManufacturer", "Xiaomi");
        headers.put("androidId", "131535fc93929702");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        return headers;
    }

}