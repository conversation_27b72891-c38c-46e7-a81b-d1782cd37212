package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Optional;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.BindDeviceByOTP;
import Request.SoundBox.CreatenewSBLead;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class BindDeviceByOTPTest extends BaseMethod {

public String sessionToken;
	
	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> body;
	public BindDeviceByOTP otpObj;
	String leadid="";
	
	
	@BeforeClass
	public void createLead(@Optional("0") int value) {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
			headers = new HashMap<>();
	        body = new HashMap<>();
			services=new MiddlewareServices();
	        headers.put("session_token", sessionToken);
			headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
			headers.put("version", "5.1.0");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


	        body.put("agentCustId" , "1107195733");
	        body.put("entityType" , "INDIVIDUAL");
	        body.put("merchantName" ,"Aashit Display");
	        body.put("mid" ,"jmQUYW26322276703759" );
	        body.put("solutionSubType" , "sound_box_bind");
	        body.put("deviceType" , "sound_box");
	        body.put("userCustId" , "1002503899");
	        body.put("userMobile","9553414790");
	        CreatenewSBLead obj = new CreatenewSBLead();
	        Response objresponse = services.CreatenewSBLead(obj , headers , body);
	        int statusCode = objresponse.statusCode();
	        System.out.println(statusCode);
	        if(statusCode==400 && value<3) {
	        	value++;
	        this.createLead(value);}
	        
	        leadid= objresponse.jsonPath().getJsonObject("leadId").toString();
	}
	
	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	

	@Test
	public void verify_Error_Message_When_Invalid_Otp_Is_Entered() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        body.put("deviceOtp", "072282");
        body.put("deviceSim", "8991102205751575964U");
        body.put("grantedWhatsAppConsent", "false");
        body.put("leadId", leadid);
        body.put("qrString", "1XG4N6XRYV");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("model", "");
        body.put("price", "");
        body.put("rentalAmount", "");
        body.put("rentalType", "");
        body.put("replacementProcessingFee", "");
        body.put("securityDeposit", "");
        body.put("type", "");
        body.put("httpStatusCode", "0");
        otpObj=new BindDeviceByOTP();
        response=services.bindDeviceByOtp(otpObj, body, headers);
        Assert.assertEquals(response.getStatusCode(), 500);
	}
	
	@Test
	public void verify_Error_Message_When_Otp_Is_Empty() {
			services=new MiddlewareServices();
	        
			headers=new HashMap<>();
			body=new HashMap<>();
			
			headers.put("session_token", sessionToken);
			headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
			headers.put("version", "5.1.0");
	        headers.put("Content-Type", "application/json; charset=UTF-8");
	        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

	        body.put("deviceOtp", "");
	        body.put("deviceSim", "8991102205751575964U");
	        body.put("grantedWhatsAppConsent", "false");
	        body.put("leadId", leadid);
	        body.put("qrString", "1XG4N6XRYV");
	        body.put("deviceType", "sound_box_3_0_4g");
	        body.put("model", "");
	        body.put("price", "");
	        body.put("rentalAmount", "");
	        body.put("rentalType", "");
	        body.put("replacementProcessingFee", "");
	        body.put("securityDeposit", "");
	        body.put("type", "");
	        body.put("httpStatusCode", "0");
	        otpObj=new BindDeviceByOTP();
	        response=services.bindDeviceByOtp(otpObj, body, headers);
	        Assert.assertEquals(response.getStatusCode(), 500);

	}

				@Test
				public void verify_Status_Code_In_Case_Of_Failure() {
				services=new MiddlewareServices();
		        
				headers=new HashMap<>();
				body=new HashMap<>();
				
				headers.put("session_token", sessionToken);
				headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
				headers.put("version", "5.1.0");
		        headers.put("Content-Type", "application/json; charset=UTF-8");
		        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

		        body.put("deviceOtp", "");
		        body.put("deviceSim", "8991102205751575964U");
		        body.put("grantedWhatsAppConsent", "false");
		        body.put("leadId", leadid);
		        body.put("qrString", "1XG4N6XRYV");
		        body.put("deviceType", "sound_box_3_0_4g");
		        body.put("model", "");
		        body.put("price", "");
		        body.put("rentalAmount", "");
		        body.put("rentalType", "");
		        body.put("replacementProcessingFee", "");
		        body.put("securityDeposit", "");
		        body.put("type", "");
		        body.put("httpStatusCode", "0");
		        otpObj=new BindDeviceByOTP();
		        response=services.bindDeviceByOtp(otpObj, body, headers);
		        Assert.assertEquals(response.getStatusCode(), 500);
		}

				
				@Test
				public void verify_Error_Code_When_Lead_ID_Is_Empty() {
				
					headers=new HashMap<>();
					body=new HashMap<>();
					services=new MiddlewareServices();
					
					headers.put("session_token", sessionToken);
					headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
					headers.put("version", "5.1.0");
			        headers.put("Content-Type", "application/json; charset=UTF-8");
			        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

			        body.put("deviceOtp", "072282");
			        body.put("deviceSim", "8991102205751575964U");
			        body.put("grantedWhatsAppConsent", "false");

			        body.put("qrString", "1XG4N6XRYV");
			        body.put("deviceType", "sound_box_3_0_4g");
			        body.put("model", "");
			        body.put("price", "");
			        body.put("rentalAmount", "");
			        body.put("rentalType", "");
			        body.put("replacementProcessingFee", "");
			        body.put("securityDeposit", "");
			        body.put("type", "");
			        body.put("httpStatusCode", "0");
			        otpObj=new BindDeviceByOTP();
			        response=services.bindDeviceByOtp(otpObj, body, headers);
			        Assert.assertEquals(response.getStatusCode(), 400);
				}
				
				
				
	
				@Test
				public void verify_Error_Code_When_QR_String_Is_Empty() {
				
					headers=new HashMap<>();
					body=new HashMap<>();
					services=new MiddlewareServices();
					
					headers.put("session_token", sessionToken);
					headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
					headers.put("version", "5.1.0");
			        headers.put("Content-Type", "application/json; charset=UTF-8");
			        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

			        body.put("deviceOtp", "072282");
			        body.put("deviceSim", "8991102205751575964U");
			        body.put("grantedWhatsAppConsent", "false");
			        body.put("leadId", "26e1e6a7-38d0-4cf3-8a8e-7ddd6bc93c75");

			        body.put("deviceType", "sound_box_3_0_4g");
			        body.put("model", "");
			        body.put("price", "");
			        body.put("rentalAmount", "");
			        body.put("rentalType", "");
			        body.put("replacementProcessingFee", "");
			        body.put("securityDeposit", "");
			        body.put("type", "");
			        body.put("httpStatusCode", "0");
			        otpObj=new BindDeviceByOTP();
			        response=services.bindDeviceByOtp(otpObj, body, headers);
			        Assert.assertEquals(response.getStatusCode(), 500);
				}
				
				@Test
				public void verify_Error_Code_When_Device_Type_Is_Empty() {
				
					headers=new HashMap<>();
					body=new HashMap<>();
					services=new MiddlewareServices();
					
					headers.put("session_token", sessionToken);
					headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
					headers.put("version", "5.1.0");
			        headers.put("Content-Type", "application/json; charset=UTF-8");
			        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

			        body.put("deviceOtp", "072282");
			        body.put("deviceSim", "8991102205751575964U");
			        body.put("grantedWhatsAppConsent", "false");
			        body.put("leadId", "26e1e6a7-38d0-4cf3-8a8e-7ddd6bc93c75");
			        body.put("qrString", "1XG4N6XRYV");

			        body.put("model", "");
			        body.put("price", "");
			        body.put("rentalAmount", "");
			        body.put("rentalType", "");
			        body.put("replacementProcessingFee", "");
			        body.put("securityDeposit", "");
			        body.put("type", "");
			        body.put("httpStatusCode", "0");
			        otpObj=new BindDeviceByOTP();
			        response=services.bindDeviceByOtp(otpObj, body, headers);
			        Assert.assertEquals(response.getStatusCode(), 500);
				}

				@Test
				public void verify_Error_Code_When_Device_Model_Is_Missing() {
				
					headers=new HashMap<>();
					body=new HashMap<>();
					services=new MiddlewareServices();
					
					headers.put("session_token", sessionToken);
					headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
					headers.put("version", "5.1.0");
			        headers.put("Content-Type", "application/json; charset=UTF-8");
			        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

			        body.put("deviceOtp", "072282");
			        body.put("deviceSim", "8991102205751575964U");
			        body.put("grantedWhatsAppConsent", "false");
			        body.put("leadId", "26e1e6a7-38d0-4cf3-8a8e-7ddd6bc93c75");
			        body.put("qrString", "1XG4N6XRYV");
			        body.put("deviceType", "sound_box_3_0_4g");
			        body.put("price", "");
			        body.put("rentalAmount", "");
			        body.put("rentalType", "");
			        body.put("replacementProcessingFee", "");
			        body.put("securityDeposit", "");
			        body.put("type", "");
			        body.put("httpStatusCode", "0");
			        otpObj=new BindDeviceByOTP();
			        response=services.bindDeviceByOtp(otpObj, body, headers);
			        Assert.assertEquals(response.getStatusCode(), 500);
				}
				
				@Test
				public void verify_Error_Code_When_Device_Price_Is_Missing() {
				
					headers=new HashMap<>();
					body=new HashMap<>();
					services=new MiddlewareServices();
					
					headers.put("session_token", sessionToken);
					headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
					headers.put("version", "5.1.0");
			        headers.put("Content-Type", "application/json; charset=UTF-8");
			        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

			        body.put("deviceOtp", "072282");
			        body.put("deviceSim", "8991102205751575964U");
			        body.put("grantedWhatsAppConsent", "false");
			        body.put("leadId", "26e1e6a7-38d0-4cf3-8a8e-7ddd6bc93c75");
			        body.put("qrString", "1XG4N6XRYV");
			        body.put("deviceType", "sound_box_3_0_4g");
			        body.put("model", "");
			        body.put("rentalAmount", "");
			        body.put("rentalType", "");
			        body.put("replacementProcessingFee", "");
			        body.put("securityDeposit", "");
			        body.put("type", "");
			        body.put("httpStatusCode", "0");
			        otpObj=new BindDeviceByOTP();
			        response=services.bindDeviceByOtp(otpObj, body, headers);
			        Assert.assertEquals(response.getStatusCode(), 500);
				}
				
				@Test
				public void verify_Error_Code_When_Device_Rental_Is_Missing() {
				
					headers=new HashMap<>();
					body=new HashMap<>();
					services=new MiddlewareServices();
					
					headers.put("session_token", sessionToken);
					headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
					headers.put("version", "5.1.0");
			        headers.put("Content-Type", "application/json; charset=UTF-8");
			        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

			        body.put("deviceOtp", "072282");
			        body.put("deviceSim", "8991102205751575964U");
			        body.put("grantedWhatsAppConsent", "false");
			        body.put("leadId", "26e1e6a7-38d0-4cf3-8a8e-7ddd6bc93c75");
			        body.put("qrString", "1XG4N6XRYV");
			        body.put("deviceType", "sound_box_3_0_4g");
			        body.put("model", "");
			        body.put("price", "");
//			        body.put("rentalAmount", "");
			        body.put("rentalType", "");
			        body.put("replacementProcessingFee", "");
			        body.put("securityDeposit", "");
			        body.put("type", "");
			        body.put("httpStatusCode", "0");
			        otpObj=new BindDeviceByOTP();
			        response=services.bindDeviceByOtp(otpObj, body, headers);
			        Assert.assertEquals(response.getStatusCode(), 500);
				}

				@Test
				public void verify_Error_Code_When_Device_Rental_Type_Is_Missing() {
				
					headers=new HashMap<>();
					body=new HashMap<>();
					services=new MiddlewareServices();
					
					headers.put("session_token", sessionToken);
					headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
					headers.put("version", "5.1.0");
			        headers.put("Content-Type", "application/json; charset=UTF-8");
			        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

			        body.put("deviceOtp", "072282");
			        body.put("deviceSim", "8991102205751575964U");
			        body.put("grantedWhatsAppConsent", "false");
			        body.put("leadId", "26e1e6a7-38d0-4cf3-8a8e-7ddd6bc93c75");
			        body.put("qrString", "1XG4N6XRYV");
			        body.put("deviceType", "sound_box_3_0_4g");
			        body.put("model", "");
			        body.put("price", "");
			        body.put("rentalAmount", "");
			        body.put("replacementProcessingFee", "");
			        body.put("securityDeposit", "");
			        body.put("type", "");
			        body.put("httpStatusCode", "0");
			        otpObj=new BindDeviceByOTP();
			        response=services.bindDeviceByOtp(otpObj, body, headers);
			        Assert.assertEquals(response.getStatusCode(), 500);
				}
				
				@Test
				public void verify_Error_Code_When_Device_Replacement_Fee_Is_Not_Passed_In_request() {
				
					headers=new HashMap<>();
					body=new HashMap<>();
					services=new MiddlewareServices();
					
					headers.put("session_token", sessionToken);
					headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
					headers.put("version", "5.1.0");
			        headers.put("Content-Type", "application/json; charset=UTF-8");
			        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

			        body.put("deviceOtp", "072282");
			        body.put("deviceSim", "8991102205751575964U");
			        body.put("grantedWhatsAppConsent", "false");
			        body.put("leadId", "26e1e6a7-38d0-4cf3-8a8e-7ddd6bc93c75");
			        body.put("qrString", "1XG4N6XRYV");
			        body.put("deviceType", "sound_box_3_0_4g");
			        body.put("model", "");
			        body.put("price", "");
			        body.put("rentalAmount", "");
			        body.put("securityDeposit", "");
			        body.put("type", "");
			        body.put("httpStatusCode", "0");
			        otpObj=new BindDeviceByOTP();
			        response=services.bindDeviceByOtp(otpObj, body, headers);
			        Assert.assertEquals(response.getStatusCode(), 500);
				}

				@Test
				public void verify_Error_Code_When_Device_Security_Deposit_Is_Not_Passed_In_request() {
				
					headers=new HashMap<>();
					body=new HashMap<>();
					services=new MiddlewareServices();
					
					headers.put("session_token", sessionToken);
					headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
					headers.put("version", "5.1.0");
			        headers.put("Content-Type", "application/json; charset=UTF-8");
			        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

			        body.put("deviceOtp", "072282");
			        body.put("deviceSim", "8991102205751575964U");
			        body.put("grantedWhatsAppConsent", "false");
			        body.put("leadId", "26e1e6a7-38d0-4cf3-8a8e-7ddd6bc93c75");
			        body.put("qrString", "1XG4N6XRYV");
			        body.put("deviceType", "sound_box_3_0_4g");
			        body.put("model", "");
			        body.put("price", "");
			        body.put("rentalAmount", "");
			        body.put("rentalType", "");
			        body.put("type", "");
			        body.put("httpStatusCode", "0");
			        otpObj=new BindDeviceByOTP();
			        response=services.bindDeviceByOtp(otpObj, body, headers);
			        Assert.assertEquals(response.getStatusCode(), 500);
				}


				@Test
				public void verify_Error_Code_When_type_Is_Not_Passed_In_request() {
				
					headers=new HashMap<>();
					body=new HashMap<>();
					services=new MiddlewareServices();
					
					headers.put("session_token", sessionToken);
					headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
					headers.put("version", "5.1.0");
			        headers.put("Content-Type", "application/json; charset=UTF-8");
			        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

			        body.put("deviceOtp", "072282");
			        body.put("deviceSim", "8991102205751575964U");
			        body.put("grantedWhatsAppConsent", "false");
			        body.put("leadId", "26e1e6a7-38d0-4cf3-8a8e-7ddd6bc93c75");
			        body.put("qrString", "1XG4N6XRYV");
			        body.put("deviceType", "sound_box_3_0_4g");
			        body.put("model", "");
			        body.put("price", "");
			        body.put("rentalAmount", "");
			        body.put("rentalType", "");
			        body.put("replacementProcessingFee", "");
			        body.put("securityDeposit", "");
			        body.put("type", "");
			        body.put("httpStatusCode", "0");
			        otpObj=new BindDeviceByOTP();
			        response=services.bindDeviceByOtp(otpObj, body, headers);
			        Assert.assertEquals(response.getStatusCode(), 500);
				}
				
				@Test
				public void verify_Error_Code_When_Lead_Id_is_already_closed() {
				
					headers=new HashMap<>();
					body=new HashMap<>();
					services=new MiddlewareServices();
					
					headers.put("session_token", sessionToken);
					headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
					headers.put("version", "5.1.0");
			        headers.put("Content-Type", "application/json; charset=UTF-8");
			        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

			        body.put("deviceOtp", "072282");
			        body.put("deviceSim", "8991102205751575964U");
			        body.put("grantedWhatsAppConsent", "false");
			        body.put("leadId", "608dd14a-fc60-4860-a9a2-dff1584c9d59");
			        body.put("qrString", "1XG4N6XRYV");
			        body.put("deviceType", "sound_box_3_0_4g");
			        body.put("model", "");
			        body.put("price", "");
			        body.put("rentalAmount", "");
			        body.put("rentalType", "");
			        body.put("replacementProcessingFee", "");
			        body.put("securityDeposit", "");
			        body.put("type", "");
			        body.put("httpStatusCode", "0");
			        otpObj=new BindDeviceByOTP();
			        response=services.bindDeviceByOtp(otpObj, body, headers);
			        Assert.assertEquals(response.getStatusCode(), 500);
				}
				
				
				@Test
				public void verify_Error_Code_When_Session_Token_Is_Missing_From_request() {
				
					headers=new HashMap<>();
					body=new HashMap<>();
					services=new MiddlewareServices();
					
					headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
					headers.put("version", "5.1.0");
			        headers.put("Content-Type", "application/json; charset=UTF-8");
			        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

			        body.put("deviceOtp", "072282");
			        body.put("deviceSim", "8991102205751575964U");
			        body.put("grantedWhatsAppConsent", "false");
			        body.put("leadId", "608dd14a-fc60-4860-a9a2-dff1584c9d59");
			        body.put("qrString", "1XG4N6XRYV");
			        body.put("deviceType", "sound_box_3_0_4g");
			        body.put("model", "");
			        body.put("price", "");
			        body.put("rentalAmount", "");
			        body.put("rentalType", "");
			        body.put("replacementProcessingFee", "");
			        body.put("securityDeposit", "");
			        body.put("type", "");
			        body.put("httpStatusCode", "0");
			        otpObj=new BindDeviceByOTP();
			        response=services.bindDeviceByOtp(otpObj, body, headers);
			        Assert.assertEquals(response.getStatusCode(), 401);
				}
				
				@Test
				public void verify_Error_Code_When_App_Version_Is_Missing_From_request() {
				
					headers=new HashMap<>();
					body=new HashMap<>();
					services=new MiddlewareServices();
					
					headers.put("session_token", sessionToken);
					headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
			        headers.put("Content-Type", "application/json; charset=UTF-8");
			        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

			        body.put("deviceOtp", "072282");
			        body.put("deviceSim", "8991102205751575964U");
			        body.put("grantedWhatsAppConsent", "false");
			        body.put("leadId", "608dd14a-fc60-4860-a9a2-dff1584c9d59");
			        body.put("qrString", "1XG4N6XRYV");
			        body.put("deviceType", "sound_box_3_0_4g");
			        body.put("model", "");
			        body.put("price", "");
			        body.put("rentalAmount", "");
			        body.put("rentalType", "");
			        body.put("replacementProcessingFee", "");
			        body.put("securityDeposit", "");
			        body.put("type", "");
			        body.put("httpStatusCode", "0");
			        otpObj=new BindDeviceByOTP();
			        response=services.bindDeviceByOtp(otpObj, body, headers);
			        Assert.assertTrue(response.path("message").toString().contains("version is empty in header"));

				}

	}