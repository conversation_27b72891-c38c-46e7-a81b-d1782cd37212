package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.FetchQrDetails;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestFetchQrDetails extends BaseMethod {

    public String sessionToken;
    public Response response;
    public MiddlewareServices services;
    public Map<String, String> headers;
    public Map<String, String> body;
    public FetchQrDetails qrDetails;

    @BeforeMethod
    public void setToken() {
        sessionToken = AgentSessionToken("8010630022", "paytm@123");
        try {
            establishConnectiontoServer(sessionToken, 5);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void verify_FetchQrDetails_Success_Response() {
        headers = new HashMap<>();
        body = new HashMap<>();
        services = new MiddlewareServices();
        qrDetails = new FetchQrDetails();

        // Setting headers based on the curl request and pattern from TestSBCheckBTStatus
        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("latitude", "0.0");
        headers.put("longitude", "0.0");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("x-mw-checksum-v3", "j24JSHuNdxtrPPx3idRR9kZjY+GjT+Aew5cEWmXBrvSHr/Cd1zX4fKydOBfxfs6sMnidsbU8YKVi0lA/Wm7p1n8cY0pFqwmn6Z/I3siEG3CHzSjQGV2ODqbZvbxAZRsFy4KS2wLvvMfx7jq8crY=");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        body.put("qrStickerId", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("qrStickerDetails", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("typeOfQR", "QRCODE");
        body.put("fetchMidDetails", "true");

        response = services.qrDetails(qrDetails, headers, body);
        Assert.assertEquals(response.getStatusCode(), 200);
        Assert.assertTrue(response.getBody().asString().contains("mid"));
    }

    @Test
    public void verify_FetchQrDetails_Failure_Response_When_QR_Is_Not_Mapped() {
        headers = new HashMap<>();
        body = new HashMap<>();
        services = new MiddlewareServices();
        qrDetails = new FetchQrDetails();

        // Setting headers based on the curl request and pattern from TestSBCheckBTStatus
        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("latitude", "0.0");
        headers.put("longitude", "0.0");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("x-mw-checksum-v3", "j24JSHuNdxtrPPx3idRR9kZjY+GjT+Aew5cEWmXBrvSHr/Cd1zX4fKydOBfxfs6sMnidsbU8YKVi0lA/Wm7p1n8cY0pFqwmn6Z/I3siEG3CHzSjQGV2ODqbZvbxAZRsFy4KS2wLvvMfx7jq8crY=");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        body.put("qrStickerId", "upi://pay?pa=paytm.us7namhb@pty&pn=Paytm");
        body.put("qrStickerDetails", "upi://pay?pa=paytm.us7namhb@pty&pn=Paytm");
        body.put("typeOfQR", "QRCODE");
        body.put("fetchMidDetails", "true");

        response = services.qrDetails(qrDetails, headers, body);
        Assert.assertEquals(response.getStatusCode(), 404);
    }

    @Test
    public void verify_FetchQrDetails_Failure_Response_When_QR_Is_Not_Active() {
        headers = new HashMap<>();
        body = new HashMap<>();
        services = new MiddlewareServices();
        qrDetails = new FetchQrDetails();

        // Setting headers based on the curl request and pattern from TestSBCheckBTStatus
        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("latitude", "0.0");
        headers.put("longitude", "0.0");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("x-mw-checksum-v3", "j24JSHuNdxtrPPx3idRR9kZjY+GjT+Aew5cEWmXBrvSHr/Cd1zX4fKydOBfxfs6sMnidsbU8YKVi0lA/Wm7p1n8cY0pFqwmn6Z/I3siEG3CHzSjQGV2ODqbZvbxAZRsFy4KS2wLvvMfx7jq8crY=");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        body.put("qrStickerId", "upi://pay?pa=paytm.us7namhc@pty&pn=Paytm");
        body.put("qrStickerDetails", "upi://pay?pa=paytm.us7namhc@pty&pn=Paytm");
        body.put("typeOfQR", "QRCODE");
        body.put("fetchMidDetails", "true");

        response = services.qrDetails(qrDetails, headers, body);
        Assert.assertEquals(response.getStatusCode(), 404);
    }

    @Test
    public void verify_FetchQrDetails_Failure_Response_Invalid_Session_Token() {
        headers = new HashMap<>();
        body = new HashMap<>();
        services = new MiddlewareServices();
        qrDetails = new FetchQrDetails();

        // Using invalid session token
        headers.put("session_token", "invalid_token_12345");
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("latitude", "0.0");
        headers.put("longitude", "0.0");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("x-mw-checksum-v3", "j24JSHuNdxtrPPx3idRR9kZjY+GjT+Aew5cEWmXBrvSHr/Cd1zX4fKydOBfxfs6sMnidsbU8YKVi0lA/Wm7p1n8cY0pFqwmn6Z/I3siEG3CHzSjQGV2ODqbZvbxAZRsFy4KS2wLvvMfx7jq8crY=");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        body.put("qrStickerId", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("qrStickerDetails", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("typeOfQR", "QRCODE");
        body.put("fetchMidDetails", "true");

        response = services.qrDetails(qrDetails, headers, body);
        Assert.assertEquals(response.getStatusCode(), 41);

    }

    @Test
    public void verify_FetchQrDetails_Failure_Response_Missing_Required_Headers() {
        headers = new HashMap<>();
        body = new HashMap<>();
        services = new MiddlewareServices();
        qrDetails = new FetchQrDetails();

        // Only including minimal required headers, missing version header
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("latitude", "0.0");
        headers.put("longitude", "0.0");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("x-mw-checksum-v3", "j24JSHuNdxtrPPx3idRR9kZjY+GjT+Aew5cEWmXBrvSHr/Cd1zX4fKydOBfxfs6sMnidsbU8YKVi0lA/Wm7p1n8cY0pFqwmn6Z/I3siEG3CHzSjQGV2ODqbZvbxAZRsFy4KS2wLvvMfx7jq8crY=");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        body.put("qrStickerId", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("qrStickerDetails", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("typeOfQR", "QRCODE");
        body.put("fetchMidDetails", "true");

        response = services.qrDetails(qrDetails, headers, body);
        Assert.assertEquals(response.getStatusCode(), 200);
        Assert.assertTrue(response.getBody().asString().contains("version is empty in header"));
        Assert.assertTrue(response.getBody().asString().contains("VERSION_FAILURE"));
        Assert.assertTrue(response.getBody().jsonPath().getBoolean("agentTncStatus"));
        Assert.assertTrue(response.getBody().jsonPath().getBoolean("agentKycStatus"));
    }

    @Test
    public void verify_FetchQrDetails_Failure_Response_Invalid_QR_Format() {
        headers = new HashMap<>();
        body = new HashMap<>();
        services = new MiddlewareServices();
        qrDetails = new FetchQrDetails();

        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("latitude", "0.0");
        headers.put("longitude", "0.0");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("x-mw-checksum-v3", "j24JSHuNdxtrPPx3idRR9kZjY+GjT+Aew5cEWmXBrvSHr/Cd1zX4fKydOBfxfs6sMnidsbU8YKVi0lA/Wm7p1n8cY0pFqwmn6Z/I3siEG3CHzSjQGV2ODqbZvbxAZRsFy4KS2wLvvMfx7jq8crY=");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        // Invalid QR format
        body.put("qrStickerId", "invalid-qr-format");
        body.put("qrStickerDetails", "invalid-qr-format");
        body.put("typeOfQR", "QRCODE");
        body.put("fetchMidDetails", "true");

        response = services.qrDetails(qrDetails, headers, body);
        Assert.assertEquals(response.getStatusCode(), 400);
        Assert.assertTrue(response.getBody().asString().contains("invalid-qr-format"));
    }

    @Test
    public void verify_FetchQrDetails_Failure_Response_Invalid_Device_Identifier() {
        headers = new HashMap<>();
        body = new HashMap<>();
        services = new MiddlewareServices();
        qrDetails = new FetchQrDetails();

        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "invalid-device-id");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("latitude", "0.0");
        headers.put("longitude", "0.0");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("x-mw-checksum-v3", "j24JSHuNdxtrPPx3idRR9kZjY+GjT+Aew5cEWmXBrvSHr/Cd1zX4fKydOBfxfs6sMnidsbU8YKVi0lA/Wm7p1n8cY0pFqwmn6Z/I3siEG3CHzSjQGV2ODqbZvbxAZRsFy4KS2wLvvMfx7jq8crY=");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        body.put("qrStickerId", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("qrStickerDetails", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("typeOfQR", "QRCODE");
        body.put("fetchMidDetails", "true");

        response = services.qrDetails(qrDetails, headers, body);
        Assert.assertEquals(response.getStatusCode(), 410);

    }



    @Test
    public void verify_FetchQrDetails_Success_Response_Invalid_Checksum() {
        headers = new HashMap<>();
        body = new HashMap<>();
        services = new MiddlewareServices();
        qrDetails = new FetchQrDetails();

        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("latitude", "0.0");
        headers.put("longitude", "0.0");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("x-mw-checksum-v3", "invalid_checksum");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        body.put("qrStickerId", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("qrStickerDetails", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("typeOfQR", "QRCODE");
        body.put("fetchMidDetails", "true");

        response = services.qrDetails(qrDetails, headers, body);
        Assert.assertEquals(response.getStatusCode(), 200);

    }

    @Test
    public void verify_FetchQrDetails_Response_Unsupported_QR_Type() {
        headers = new HashMap<>();
        body = new HashMap<>();
        services = new MiddlewareServices();
        qrDetails = new FetchQrDetails();

        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("latitude", "0.0");
        headers.put("longitude", "0.0");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("x-mw-checksum-v3", "j24JSHuNdxtrPPx3idRR9kZjY+GjT+Aew5cEWmXBrvSHr/Cd1zX4fKydOBfxfs6sMnidsbU8YKVi0lA/Wm7p1n8cY0pFqwmn6Z/I3siEG3CHzSjQGV2ODqbZvbxAZRsFy4KS2wLvvMfx7jq8crY=");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        body.put("qrStickerId", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("qrStickerDetails", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("typeOfQR", "");
        body.put("fetchMidDetails", "true");

        response = services.qrDetails(qrDetails, headers, body);
        Assert.assertEquals(response.getStatusCode(), 200);

    }

    @Test
    public void verify_FetchQrDetails_Success_Response_With_Different_App_Version() {
        headers = new HashMap<>();
        body = new HashMap<>();
        services = new MiddlewareServices();
        qrDetails = new FetchQrDetails();

        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.0.5");  // Different app version
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("latitude", "0.0");
        headers.put("longitude", "0.0");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("x-mw-checksum-v3", "j24JSHuNdxtrPPx3idRR9kZjY+GjT+Aew5cEWmXBrvSHr/Cd1zX4fKydOBfxfs6sMnidsbU8YKVi0lA/Wm7p1n8cY0pFqwmn6Z/I3siEG3CHzSjQGV2ODqbZvbxAZRsFy4KS2wLvvMfx7jq8crY=");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        body.put("qrStickerId", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("qrStickerDetails", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("typeOfQR", "QRCODE");
        body.put("fetchMidDetails", "true");

        response = services.qrDetails(qrDetails, headers, body);
        Assert.assertEquals(response.getStatusCode(), 200);
        Assert.assertTrue(response.getBody().asString().contains("mid"));
    }

    @Test
    public void verify_FetchQrDetails_Response_Invalid_QrStickerId() {
        headers = new HashMap<>();
        body = new HashMap<>();
        services = new MiddlewareServices();
        qrDetails = new FetchQrDetails();

        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("latitude", "0.0");
        headers.put("longitude", "0.0");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("x-mw-checksum-v3", "j24JSHuNdxtrPPx3idRR9kZjY+GjT+Aew5cEWmXBrvSHr/Cd1zX4fKydOBfxfs6sMnidsbU8YKVi0lA/Wm7p1n8cY0pFqwmn6Z/I3siEG3CHzSjQGV2ODqbZvbxAZRsFy4KS2wLvvMfx7jq8crY=");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        // Invalid qrStickerId format
        body.put("qrStickerId", "123456");
        body.put("qrStickerDetails", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("typeOfQR", "QRCODE");
        body.put("fetchMidDetails", "true");

        response = services.qrDetails(qrDetails, headers, body);
        Assert.assertEquals(response.getStatusCode(), 200);

    }

    @Test
    public void verify_FetchQrDetails_Success_Response_FetchMidDetails_False() {
        headers = new HashMap<>();
        body = new HashMap<>();
        services = new MiddlewareServices();
        qrDetails = new FetchQrDetails();

        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("latitude", "0.0");
        headers.put("longitude", "0.0");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("x-mw-checksum-v3", "j24JSHuNdxtrPPx3idRR9kZjY+GjT+Aew5cEWmXBrvSHr/Cd1zX4fKydOBfxfs6sMnidsbU8YKVi0lA/Wm7p1n8cY0pFqwmn6Z/I3siEG3CHzSjQGV2ODqbZvbxAZRsFy4KS2wLvvMfx7jq8crY=");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        body.put("qrStickerId", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("qrStickerDetails", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("typeOfQR", "QRCODE");
        body.put("fetchMidDetails", "false");

        response = services.qrDetails(qrDetails, headers, body);
        Assert.assertEquals(response.getStatusCode(), 200);
        Assert.assertNull(response.jsonPath().get("midDetails[0].PPI_LIMIT"));
        Assert.assertNull(response.jsonPath().get("midDetails[0].MERCHANT_NAME"));
    }

    @Test
    public void verify_FetchQrDetails_Success_Response_Without_QrStickerDetails() {
        headers = new HashMap<>();
        body = new HashMap<>();
        services = new MiddlewareServices();
        qrDetails = new FetchQrDetails();

        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("latitude", "0.0");
        headers.put("longitude", "0.0");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("x-mw-checksum-v3", "j24JSHuNdxtrPPx3idRR9kZjY+GjT+Aew5cEWmXBrvSHr/Cd1zX4fKydOBfxfs6sMnidsbU8YKVi0lA/Wm7p1n8cY0pFqwmn6Z/I3siEG3CHzSjQGV2ODqbZvbxAZRsFy4KS2wLvvMfx7jq8crY=");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        // Only sending qrStickerId without qrStickerDetails
        body.put("qrStickerId", "upi://pay?pa=paytm.us7namha@pty&pn=Paytm");
        body.put("typeOfQR", "QRCODE");
        body.put("fetchMidDetails", "true");

        response = services.qrDetails(qrDetails, headers, body);
        Assert.assertEquals(response.getStatusCode(), 400);

    }

    @Test
    public void verify_FetchQrDetails_Response_With_Special_Characters_In_QR() {
        headers = new HashMap<>();
        body = new HashMap<>();
        services = new MiddlewareServices();
        qrDetails = new FetchQrDetails();

        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("latitude", "0.0");
        headers.put("longitude", "0.0");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("x-mw-checksum-v3", "j24JSHuNdxtrPPx3idRR9kZjY+GjT+Aew5cEWmXBrvSHr/Cd1zX4fKydOBfxfs6sMnidsbU8YKVi0lA/Wm7p1n8cY0pFqwmn6Z/I3siEG3CHzSjQGV2ODqbZvbxAZRsFy4KS2wLvvMfx7jq8crY=");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        // QR with spaces in sticker details
        body.put("qrStickerId", "upi://pay?pa=paytm.us7namha@pty&pn= Paytm");
        body.put("qrStickerDetails", "upi://pay?pa=paytm.us7namha@pty&pn= Paytm");
        body.put("typeOfQR", "QRCODE");
        body.put("fetchMidDetails", "true");

        response = services.qrDetails(qrDetails, headers, body);
        Assert.assertEquals(response.getStatusCode(), 404);
    }

    @Test
    public void verify_FetchQrDetails_Response_With_Malformed_UPI() {
        headers = new HashMap<>();
        body = new HashMap<>();
        services = new MiddlewareServices();
        qrDetails = new FetchQrDetails();

        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("latitude", "0.0");
        headers.put("longitude", "0.0");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("x-mw-checksum-v3", "j24JSHuNdxtrPPx3idRR9kZjY+GjT+Aew5cEWmXBrvSHr/Cd1zX4fKydOBfxfs6sMnidsbU8YKVi0lA/Wm7p1n8cY0pFqwmn6Z/I3siEG3CHzSjQGV2ODqbZvbxAZRsFy4KS2wLvvMfx7jq8crY=");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        // Malformed UPI string (missing 'pa' parameter)
        body.put("qrStickerId", "upi://pay?pn=Paytm");
        body.put("qrStickerDetails", "upi://pay?pn=Paytm");
        body.put("typeOfQR", "QRCODE");
        body.put("fetchMidDetails", "true");

        response = services.qrDetails(qrDetails, headers, body);
        Assert.assertEquals(response.getStatusCode(), 404);

    }

    @Test
    public void verify_FetchQrDetails_Response_With_Unsupported_Bank_Channel() {
        headers = new HashMap<>();
        body = new HashMap<>();
        services = new MiddlewareServices();
        qrDetails = new FetchQrDetails();

        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("latitude", "0.0");
        headers.put("longitude", "0.0");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("x-mw-checksum-v3", "j24JSHuNdxtrPPx3idRR9kZjY+GjT+Aew5cEWmXBrvSHr/Cd1zX4fKydOBfxfs6sMnidsbU8YKVi0lA/Wm7p1n8cY0pFqwmn6Z/I3siEG3CHzSjQGV2ODqbZvbxAZRsFy4KS2wLvvMfx7jq8crY=");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");

        // UPI with unsupported bank channel (finobank)
        body.put("qrStickerId", "upi://pay?pa=paytm.s10u8jg@finobank&pn=Paytm");
        body.put("qrStickerDetails", "upi://pay?pa=paytm.s10u8jg@finobank&pn=Paytm");
        body.put("typeOfQR", "QRCODE");
        body.put("fetchMidDetails", "true");

        response = services.qrDetails(qrDetails, headers, body);
        Assert.assertEquals(response.getStatusCode(), 404);
    }
}
