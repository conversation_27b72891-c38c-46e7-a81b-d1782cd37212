package OCL.Individual.SoundBox.merchant;

import OCL.Individual.SoundBox.base.SoundBoxBaseTest;
import OCL.Individual.SoundBox.base.SoundBoxConstants;
import OCL.Individual.SoundBox.base.SoundBoxTestUtils;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * Test class for Merchant ID functionality in SoundBox
 * Contains all tests related to merchant ID retrieval and operations (Priority 47-55)
 */
public class MerchantIdTests extends SoundBoxBaseTest {

    @Test(priority = 47, dependsOnMethods = {"OCL.Individual.SoundBox.device.DeviceValidationTests.verifyDeviceValidationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_MERCHANT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify merchant ID retrieval with valid data")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyMerchantIdRetrievalWithValidData() {
        Response response = SoundBoxTestUtils.executeMerchantIdRetrieval(middlewareServicesObject, 
            custId, AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        // Extract and validate merchant information from response
        String responseBody = response.getBody().asString();
        Assert.assertNotNull(responseBody, "Response body should not be null");
        LOGGER.info("Merchant ID retrieval successful");
        
        logTestCompletion("verifyMerchantIdRetrievalWithValidData", true);
    }

    @Test(priority = 48, dependsOnMethods = {"verifyMerchantIdRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_MERCHANT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify MID fetch with valid customer ID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyMidFetchWithValidData() {
        Response response = SoundBoxTestUtils.executeMidFetch(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, custId, SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX, 
            SoundBoxConstants.STATUS_OK);
        
        // Extract MID information from response
        String responseBody = response.getBody().asString();
        Assert.assertNotNull(responseBody, "Response body should not be null");
        
        // Extract UserMID if present
        try {
            UserMID = response.jsonPath().getString("mid");
            if (UserMID != null) {
                LOGGER.info("MID fetch successful - UserMID: {}", UserMID);
            }
        } catch (Exception e) {
            LOGGER.warn("Could not extract UserMID from response: {}", e.getMessage());
        }
        
        logTestCompletion("verifyMidFetchWithValidData", true);
    }

    @Test(priority = 49, dependsOnMethods = {"verifyMerchantIdRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_MERCHANT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify merchant ID retrieval with inactive MID and login retry")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyMerchantIdRetrievalWithInactiveMid() throws Exception {
        Response response = SoundBoxTestUtils.executeMerchantIdRetrievalWithCustomParams(middlewareServicesObject, 
            "1000698372", SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX, SoundBoxConstants.SOLUTION_SUB_TYPE_BIND,
            AgentToken, SoundBoxConstants.VERSION, true, SoundBoxConstants.STATUS_OK);
        
        // Handle retry logic if needed
        int statusCode = response.getStatusCode();
        if (statusCode != 200 && retrycount < 3) {
            this.AgentLoginSoundBox();
            retrycount++;
            response = SoundBoxTestUtils.executeMerchantIdRetrievalWithCustomParams(middlewareServicesObject, 
                "1000698372", SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX, SoundBoxConstants.SOLUTION_SUB_TYPE_BIND,
                AgentToken, SoundBoxConstants.VERSION, true, SoundBoxConstants.STATUS_OK);
        }
        
        // Extract and log display message
        String message = response.jsonPath().getString("displayMessage");
        LOGGER.info("Display message for inactive MID: " + message);
        
        logTestCompletion("verifyMerchantIdRetrievalWithInactiveMid", true);
    }

    @Test(priority = 50, dependsOnMethods = {"verifyMerchantIdRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_MERCHANT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify merchant ID retrieval with old version")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyMerchantIdRetrievalWithOldVersion() {
        SoundBoxTestUtils.executeMerchantIdRetrievalWithCustomParams(middlewareServicesObject, 
            "1000698372", SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX, SoundBoxConstants.SOLUTION_SUB_TYPE_BIND,
            AgentToken, "1.0.1", true, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyMerchantIdRetrievalWithOldVersion", true);
    }

    @Test(priority = 51, dependsOnMethods = {"verifyMerchantIdRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_MERCHANT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify merchant ID retrieval with empty customer ID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyMerchantIdRetrievalWithEmptyCustomerId() {
        SoundBoxTestUtils.executeMerchantIdRetrievalWithCustomParams(middlewareServicesObject, 
            "", SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX, SoundBoxConstants.SOLUTION_SUB_TYPE_BIND,
            AgentToken, SoundBoxConstants.VERSION, true, SoundBoxConstants.STATUS_BAD_REQUEST);
        
        logTestCompletion("verifyMerchantIdRetrievalWithEmptyCustomerId", true);
    }

    @Test(priority = 52, dependsOnMethods = {"verifyMerchantIdRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_MERCHANT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify merchant ID retrieval with missing device identifier")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyMerchantIdRetrievalWithMissingDeviceIdentifier() {
        SoundBoxTestUtils.executeMerchantIdRetrievalWithCustomDevice(middlewareServicesObject, 
            "1000698372", AgentToken, SoundBoxConstants.VERSION, "", SoundBoxConstants.STATUS_GONE);
        
        logTestCompletion("verifyMerchantIdRetrievalWithMissingDeviceIdentifier", true);
    }

    @Test(priority = 53, dependsOnMethods = {"verifyMerchantIdRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_MERCHANT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify merchant ID retrieval with missing version")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyMerchantIdRetrievalWithMissingVersion() {
        SoundBoxTestUtils.executeMerchantIdRetrievalWithCustomDevice(middlewareServicesObject, 
            "1000698372", AgentToken, "", SoundBoxConstants.DEVICE_IDENTIFIER, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyMerchantIdRetrievalWithMissingVersion", true);
    }

    @Test(priority = 54, dependsOnMethods = {"verifyMerchantIdRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_MERCHANT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify merchant ID retrieval with wrong solution type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyMerchantIdRetrievalWithWrongSolutionType() {
        SoundBoxTestUtils.executeMerchantIdRetrievalWithCustomParams(middlewareServicesObject, 
            custId, "soundbox", SoundBoxConstants.SOLUTION_SUB_TYPE_BIND,
            AgentToken, SoundBoxConstants.VERSION, true, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyMerchantIdRetrievalWithWrongSolutionType", true);
    }

    @Test(priority = 55, dependsOnMethods = {"verifyMerchantIdRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_MERCHANT, SoundBoxConstants.GROUP_SMOKE}, 
          description = "Verify merchant ID retrieval smoke test - quick validation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyMerchantIdRetrievalSmokeTest() {
        Response response = SoundBoxTestUtils.executeMerchantIdRetrieval(middlewareServicesObject, 
            custId, AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        // Basic validation for smoke test
        Assert.assertNotNull(response.getBody(), "Response body should not be null");
        Assert.assertTrue(response.getTime() < 5000, "Response time should be less than 5 seconds");
        
        LOGGER.info("Merchant ID retrieval smoke test passed - Response time: {} ms", response.getTime());
        
        logTestCompletion("verifyMerchantIdRetrievalSmokeTest", true);
    }

    /**
     * Additional test for wrong solution sub-type
     */
    @Test(priority = 56, dependsOnMethods = {"verifyMerchantIdRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_MERCHANT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify merchant ID retrieval with wrong solution sub-type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyMerchantIdRetrievalWithWrongSolutionSubType() {
        SoundBoxTestUtils.executeMerchantIdRetrievalWithCustomParams(middlewareServicesObject, 
            custId, SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX, "soundboxbind",
            AgentToken, SoundBoxConstants.VERSION, true, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyMerchantIdRetrievalWithWrongSolutionSubType", true);
    }

    /**
     * Helper method to validate merchant ID response structure
     * @param response API response
     */
    private void validateMerchantIdResponse(Response response) {
        String responseBody = response.getBody().asString();
        Assert.assertNotNull(responseBody, "Response body should not be null");
        
        // Additional validations can be added here based on expected response structure
        LOGGER.info("Merchant ID response validation completed");
    }

    /**
     * Helper method to test merchant ID retrieval with retry logic
     * @param maxRetries Maximum number of retries
     */
    private void executeMerchantIdRetrievalWithRetry(int maxRetries) {
        int attempts = 0;
        Response response = null;
        
        while (attempts < maxRetries) {
            try {
                response = SoundBoxTestUtils.executeMerchantIdRetrieval(middlewareServicesObject, 
                    custId, AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
                break; // Success, exit loop
            } catch (AssertionError e) {
                attempts++;
                if (attempts >= maxRetries) {
                    throw e; // Re-throw if max retries reached
                }
                LOGGER.warn("Merchant ID retrieval attempt {} failed, retrying...", attempts);
                // Re-login and retry
                AgentLoginSoundBox();
            }
        }
        
        Assert.assertNotNull(response, "Response should not be null after retries");
        validateMerchantIdResponse(response);
    }

    /**
     * Test merchant ID retrieval with different customer IDs
     */
    @Test(priority = 57, dependsOnMethods = {"verifyMerchantIdRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_MERCHANT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify merchant ID retrieval with different customer IDs")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyMerchantIdRetrievalWithDifferentCustomerIds() {
        // Test with different customer ID formats
        String[] customerIds = {
            "1000698372",
            "1000540528", 
            SoundBoxConstants.TEST_USER_CUSTOMER_ID
        };
        
        for (String customerId : customerIds) {
            try {
                SoundBoxTestUtils.executeMerchantIdRetrieval(middlewareServicesObject, 
                    customerId, AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
                LOGGER.info("Merchant ID retrieval successful for customer: {}", customerId);
            } catch (AssertionError e) {
                LOGGER.warn("Merchant ID retrieval failed for customer: {} - {}", customerId, e.getMessage());
                // Continue with next customer ID
            }
        }
        
        logTestCompletion("verifyMerchantIdRetrievalWithDifferentCustomerIds", true);
    }

    /**
     * Test merchant ID retrieval with performance validation
     */
    @Test(priority = 58, dependsOnMethods = {"verifyMerchantIdRetrievalWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_MERCHANT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify merchant ID retrieval performance")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyMerchantIdRetrievalPerformance() {
        long startTime = System.currentTimeMillis();
        
        Response response = SoundBoxTestUtils.executeMerchantIdRetrieval(middlewareServicesObject, 
            custId, AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        long endTime = System.currentTimeMillis();
        long responseTime = endTime - startTime;
        
        // Validate performance
        Assert.assertTrue(responseTime < 3000, 
            String.format("Response time should be less than 3 seconds, actual: %d ms", responseTime));
        
        LOGGER.info("Merchant ID retrieval performance test passed - Response time: {} ms", responseTime);
        
        logTestCompletion("verifyMerchantIdRetrievalPerformance", true);
    }
}
