package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.*;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.SBPlanUpgradeCreateLead;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestSBPlanUpgradeCreateLead extends BaseMethod {
	
	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> body;
	
	SBPlanUpgradeCreateLead lead;
    int retryCount=0;

	@BeforeMethod
	public void setToken() throws Exception {
	
		sessionToken=AgentSessionToken("8010630022", "paytm@123");
        establishConnectiontoServer(sessionToken,5);
        retryCount=0;
	}
	
	@Test(priority = 1)
	public void verify_Status_Code_In_Case_Of_Success_When_Merchant_Is_Opting_For_Plan_Upgrade() {

		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "6958471900");
        body.put("mid", "OHDpzh00093359209989");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001891503");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        while(response.statusCode()!=200 && retryCount<3){
            response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
            retryCount++;
        }
        Assert.assertEquals(response.getStatusCode(), 200);

	}
	
	@Test(priority = 2)
	public void verify_LeadCreated_Has_No_EDC_Card_Soundbox_Conext() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "6958471900");
        body.put("mid", "OHDpzh00093359209989");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001891503");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);

        while(response.statusCode()!=200 && retryCount<3){
            response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
            retryCount++;
        }
        Assert.assertEquals(response.getStatusCode(), 200);
        boolean hasEdcContext = response.path("hasEdcContext");
        boolean hasEdcContextDropped = response.path("hasEdcContextDropped");
        boolean hasTapNPayContext = response.path("hasTapNPayContext");
        boolean hasTapNPayContextDropped = response.path("hasTapNPayContextDropped");
        Assert.assertFalse(hasEdcContext);
        Assert.assertFalse(hasEdcContextDropped);
        Assert.assertFalse(hasTapNPayContext);
        Assert.assertFalse(hasTapNPayContextDropped);
        
	}
	
	@Test(priority = 3)
	public void verify_After_LeadCreated_Assets_Screens_Not_Launched_And_UPI_Mandate_Is_Not_Needed() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "6958471900");
        body.put("mid", "OHDpzh00093359209989");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001891503");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);

        while(response.statusCode()!=200 && retryCount<3){
            response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
            retryCount++;
        }
        boolean skipCaptureDeviceImages = response.path("skipCaptureDeviceImages");
        boolean showSerializeOldDeviceFlow = response.path("showSerializeOldDeviceFlow");
        boolean scanNewCharger = response.path("scanNewCharger");
        boolean docUploadRequired = response.path("docUploadRequired");
        boolean isUpiMandateRequired = response.path("isUpiMandateRequired");
        Assert.assertFalse(skipCaptureDeviceImages);
        Assert.assertFalse(showSerializeOldDeviceFlow);
        Assert.assertFalse(scanNewCharger);
        Assert.assertFalse(docUploadRequired);
        Assert.assertFalse(isUpiMandateRequired);

	}
	
	@Test(priority = 4)
	public void verify_Existing_Lead_Is_Continued_If_Mid_And_DeviceID_Remains_Same() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "6958471900");
        body.put("mid", "OHDpzh00093359209989");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001891503");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        String lead1=response.path("leadId").toString();

        body=new HashMap<String, String>();
        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "6958471900");
        body.put("mid", "OHDpzh00093359209989");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001891503");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");
        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        String lead2=response.path("leadId").toString();
        Assert.assertEquals(lead1, lead2);
	}

	@Test(priority = 5)
	public void verify_Error_Code_In_Case_Of_Mid_Is_Not_WhiteListed_At_CT() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466003");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "6665550217");
        body.put("mid", "DHVRVL02894215991065");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001373711");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");
        
        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        String message=response.path("displayMessage");
        Assert.assertEquals(response.getStatusCode(), 400);
        Assert.assertTrue(message.contains("This MID is not eligible for plan upgrade. Please try with an MID which is eligible."));
	}
	
	
	@Test(priority = 6)
	public void verify_Error_Code_In_Case_Of_Mid_Is_Online_MID() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466004");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "5536283009");
        body.put("mid", "JFTbRB71149895485999");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1002377973");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");
        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        String message=response.path("displayMessage");
        Assert.assertEquals(response.getStatusCode(), 400);
        Assert.assertTrue(message.contains("Sorry, we can't service this MID as no active offline MID exists."));

  	}
	
	
	@Test(priority = 7)
	public void verify_Error_Code_In_Case_Of_Mid_Is_Inactive() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466004");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "5555508413");
        body.put("mid", "SBTest10068671323127");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); 
        body.put("userCustId", "1002513626");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");
        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        String message=response.path("displayMessage");
        Assert.assertEquals(response.getStatusCode(), 400);
      //  Assert.assertTrue(message.contains("Sorry, we can't service this MID as no active offline MID exists."));

  	}
	
	@Test(priority = 8)
	public void verify_Error_Code_In_Case_Of_Mid_Is_Aggregator_MID() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466004");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "5131864504");
        body.put("mid", "JDXVvt84826061061089");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1000155084");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");
        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        String message=response.path("displayMessage");
        Assert.assertEquals(response.getStatusCode(), 400);

  	}
	
	@Test(priority = 9)
	public void verify_Error_Code_In_Case_Device_ID_Is_Incorrect_In_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466004999009090909");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "5131864504");
        body.put("mid", "JDXVvt84826061061089");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1000155084");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");
        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        String message=response.path("displayMessage");
        Assert.assertEquals(response.getStatusCode(), 400);

  	}
	
	@Test(priority = 10)
	public void verify_Status_Code_In_Case_Of_Success_When_Merchant_Entity_Type_Passed_Is_Properitorship() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("entityType", "PROPRIETORSHIP");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test(priority = 11)
	public void verify_Status_Code_In_Case_Of_Success_When_Merchant_Entity_Type_Passed_Is_PRIVATE_LIMITED() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("entityType", "PRIVATE_LIMITED");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test(priority = 12)
	public void verify_Status_Code_In_Case_Of_Success_When_Merchant_Entity_Type_Passed_Is_TRUST() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("entityType", "TRUST");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test(priority = 13)
	public void verify_Status_Code_In_Case_Of_Success_When_Merchant_CustID_Is_Incorrect_In_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "7771110999");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 200);
	}
	
	
	@Test(priority = 14)
	public void verify_Status_Code_In_Case_Of_Success_When_Agent_Cust_ID_Is_Incorrect_In_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "6958471900");
        body.put("mid", "OHDpzh00093359209989");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); 
        body.put("userCustId", "1001891503");
        body.put("agentCustId", "6958471900");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 200);
	}
	
	@Test(priority = 15)
	public void verify_Status_Code_In_Case_Of_Success_When_Solution_Subtype_Is_Incorrect_In_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "6958471900");
        body.put("mid", "OHDpzh00093359209989");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); 
        body.put("userCustId", "6958471900");
        body.put("agentCustId", "6958471900");
        body.put("solutionSubType", "sb_plan");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 200);
	}
	
	@Test(priority = 16)
	public void verify_Status_Code_In_Case_Of_Missing_Device_ID_In_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

  
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "6958471900");
        body.put("mid", "OHDpzh00093359209989");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001891503");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 200);
	}


	@Test(priority = 17)
	public void verify_Status_Code_In_Case_Of_Missing_Merchant_MID_In_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "6958471900");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001891503");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test(priority = 18)
	public void verify_Status_Code_In_Case_Of_Missing_Merchant_CUSTID_In_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("mid", "OHDpzh00093359209989");
        body.put("userMobile", "6958471900");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 200);
	}
	
	@Test(priority = 19)
	public void verify_Status_Code_In_Case_Of_Missing_Agent_CUSTID_In_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("mid", "OHDpzh00093359209989");
        body.put("userMobile", "6958471900");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001891503");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test(priority = 20)
	public void verify_Status_Code_In_Case_Of_Missing_POSID_In_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("mid", "OHDpzh00093359209989");
        body.put("userMobile", "6958471900");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001891503");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("agentCustId", "1107195733");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 200);
	}
	
	
	@Test(priority = 21)
	public void verify_Status_Code_In_Case_Of_Success_When_Fse_Does_Not_Has_Needed_Permission() throws Exception {
		
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
        establishConnectiontoServer(sessionToken,5);
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "6958471900");
        body.put("mid", "OHDpzh00093359209989");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001891503");
        body.put("agentCustId", "1001647902");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);

	}
	
	@Test(priority = 22)
	public void verify_Status_Code_In_Case_Of_Success_When_Session_Token_Is_Invalid() throws Exception {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		sessionToken="invalidToken";
        establishConnectiontoServer(sessionToken,5);
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "6958471900");
        body.put("mid", "OHDpzh00093359209989");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001891503");
        body.put("agentCustId", "1001647902");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 410);
	}
	
	@Test(priority = 23)
	public void verify_Status_Code_In_Case_Of_Success_When_Session_Token_Is_Missing() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		sessionToken="invalidToken";

		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
//		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "6958471900");
        body.put("mid", "OHDpzh00093359209989");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001891503");
        body.put("agentCustId", "1001647902");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 401);
	}
	
	@Test(priority = 24)
	public void verify_DisplayMessage_In_Case_Of_Version_Is_Missing() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new SBPlanUpgradeCreateLead();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
//		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        body.put("deviceId", "563747466002");
        body.put("deviceType", "sound_box_3_0_4g");
        body.put("userMobile", "6958471900");
        body.put("mid", "OHDpzh00093359209989");
        body.put("entityType", "INDIVIDUAL");
        body.put("merchantName", "...."); // Assuming you have the actual merchant name
        body.put("userCustId", "1001891503");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "sb_plan_upgrade");
        body.put("posId", "DEFAULT");

        response=services.v1SBPlanUpgradeCreateLead(lead, body, headers);
        String message=response.path("displayMessage");
        Assert.assertEquals(message, "version is empty in header");
	}
	
}