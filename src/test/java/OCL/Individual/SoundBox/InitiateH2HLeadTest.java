package OCL.Individual.SoundBox;

import Request.SoundBox.InitiateH2HLead;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class InitiateH2HLeadTest extends BaseMethod {
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(InitiateH2HLeadTest.class);

    String AgentToken;
    String mid = "HySHnd27878673398759";
    String deviceId = "563747466002";
    String taskId = "12345";
    String merchantName = "test";
    String tag = "Soundbox Replacement";

    @BeforeClass
    public void AgentLoginSoundBox() throws Exception {
        AgentToken = AgentSessionToken("7771216290", "paytm@123");
        LOGGER.info("Agent Token for Soundbox: " + AgentToken);
        establishConnectiontoServer(AgentToken, 5);
    }


    @Test(priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInitiateH2HLeadWithEmptyMid() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, Object> body = createRequestBody();
        body.put("mid", "");

        InitiateH2HLead initiateH2HLeadObj = new InitiateH2HLead();
        Response response = middlewareServicesObject.initiateH2HLead(initiateH2HLeadObj, headers, body);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        
        // Validate error response
        Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("Mid cannot be empty"));
    }

    @Test(priority = 3)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInitiateH2HLeadWithEmptyDeviceId() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, Object> body = createRequestBody();
        body.put("deviceId", "");

        InitiateH2HLead initiateH2HLeadObj = new InitiateH2HLead();
        Response response = middlewareServicesObject.initiateH2HLead(initiateH2HLeadObj, headers, body);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        
        // Validate error response
        Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("Device Id cannot be empty"));
    }

    @Test(priority = 4)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInitiateH2HLeadWithEmptyTaskId() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, Object> body = createRequestBody();
        body.put("taskId", "");

        InitiateH2HLead initiateH2HLeadObj = new InitiateH2HLead();
        Response response = middlewareServicesObject.initiateH2HLead(initiateH2HLeadObj, headers, body);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        
        // Validate error response
        Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("Task Id cannot be empty"));
    }

    @Test(priority = 5)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInitiateH2HLeadWithEmptyMerchantName() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, Object> body = createRequestBody();
        body.put("merchantName", "");

        InitiateH2HLead initiateH2HLeadObj = new InitiateH2HLead();
        Response response = middlewareServicesObject.initiateH2HLead(initiateH2HLeadObj, headers, body);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        
        // Validate error response
        Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("Merchant Name cannot be empty"));
    }

    @Test(priority = 6)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInitiateH2HLeadWithEmptyAddress() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, Object> body = createRequestBody();
        body.put("address", "{}");

        InitiateH2HLead initiateH2HLeadObj = new InitiateH2HLead();
        Response response = middlewareServicesObject.initiateH2HLead(initiateH2HLeadObj, headers, body);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        
        // Validate error response
        Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("Address Details cannot be empty"));
    }


    @Test(priority = 7)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInitiateH2HLeadWithValidData() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, Object> body = createRequestBody();

        InitiateH2HLead initiateH2HLeadObj = new InitiateH2HLead();
        Response response = middlewareServicesObject.initiateH2HLead(initiateH2HLeadObj, headers, body);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String responseBody = response.getBody().asString();
        LOGGER.info("Response: " + responseBody);

        // Validate response fields
        Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("SUCCESS"));
    }

    private Map<String, String> setCommonHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", AgentToken);
        return headers;
    }

    private Map<String, Object> createRequestBody() {
        Map<String, Object> body = new HashMap<>();
        body.put("mid", mid);
        body.put("deviceId", deviceId);
        body.put("taskId", taskId);
        body.put("merchantName", merchantName);
        body.put("tags", new String[]{tag});
        
        // Create address object
        Map<String, String> address = new HashMap<>();
        address.put("line1", "plot 5");
        address.put("line2", "sector 44");
        address.put("line3", "sector 44");
        address.put("city", "Gurgaon");
        address.put("state", "Haryana");
        address.put("country", "India");
        address.put("pincode", "201301");
        address.put("latitude", "24.4357117");
        address.put("longitude", "85.5324338");
        
        body.put("address", address);
        
        return body;
    }
} 