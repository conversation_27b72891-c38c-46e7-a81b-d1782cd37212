package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.FetchPOSIdSB;
import Services.MechantService.MiddlewareServices;
import io.restassured.RestAssured;
import io.restassured.parsing.Parser;
import io.restassured.response.Response;

public class TestFetchPOSIdSB extends BaseMethod{
	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> params;
	
	FetchPOSIdSB posObj;
	
	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("8010630022", "paytm@123");
		RestAssured.defaultParser=Parser.JSON;
		try {
//			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	
	@Test
	public void TC01_verify_Status_Code_When_Success_Is_Recieved_In_Response() {

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		posObj=new FetchPOSIdSB();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
	
        params.put("mid", "OHDpzh00093359209989");
        params.put("merchantCustId", "1001891503");
        params.put("phoneNumber", "6958471900");
        params.put("solutionType", "sound_box");
        params.put("solutionTypeLevel2", "sound_box_replacement");
        params.put("entityType", "INDIVIDUAL");
        
        response=services.fetchMerchantPosIds(posObj, headers, params);
        Assert.assertEquals(response.getStatusCode(), 200);
	}
	
	@Test
	public void TC02_verify_Status_Code_When_No_Bindings_Exist_For_Mid() {

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		posObj=new FetchPOSIdSB();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
	
        params.put("mid", "DHVRVL02894215991065");
        params.put("merchantCustId", "1001373711");
        params.put("phoneNumber", "6665550217");
        params.put("solutionType", "sound_box");
        params.put("solutionTypeLevel2", "sound_box_replacement");
        params.put("entityType", "INDIVIDUAL");
        
        response=services.fetchMerchantPosIds(posObj, headers, params);
        Assert.assertEquals(response.getStatusCode(), 200);
        Assert.assertTrue(response.path("displayMessage").toString().contains("No Bindings for sound box found"));
        }
	
	@Test
    public void TC03_verify_Status_Code_When_Mid_Is_Missing() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		posObj=new FetchPOSIdSB();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("merchantCustId", "1001891503");
        params.put("phoneNumber", "6958471900");
        params.put("solutionType", "sound_box");
        params.put("solutionTypeLevel2", "sound_box_replacement");
        params.put("entityType", "INDIVIDUAL");
        
        response = services.fetchMerchantPosIds(posObj, headers, params);
        Assert.assertEquals(response.getStatusCode(), 400);
    }

	@Test
    public void TC04_verify_Status_Code_When_MerchantCustId_Is_Missing() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		posObj=new FetchPOSIdSB();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("mid", "OHDpzh00093359209989");
        params.put("phoneNumber", "6958471900");
        params.put("solutionType", "sound_box");
        params.put("solutionTypeLevel2", "sound_box_replacement");
        params.put("entityType", "INDIVIDUAL");
        
        response = services.fetchMerchantPosIds(posObj, headers, params);
        Assert.assertEquals(response.getStatusCode(), 200);
    }

	@Test
    public void TC05_verify_Status_Code_When_PhoneNumber_Is_Missing() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		posObj=new FetchPOSIdSB();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("mid", "OHDpzh00093359209989");
        params.put("merchantCustId", "1001891503");
        params.put("solutionType", "sound_box");
        params.put("solutionTypeLevel2", "sound_box_replacement");
        params.put("entityType", "INDIVIDUAL");
        
        response = services.fetchMerchantPosIds(posObj, headers, params);
        Assert.assertEquals(response.getStatusCode(), 200); 
    }

    @Test
    public void TC06_verify_Status_Code_When_SessionToken_Is_Missing() {
        headers = new HashMap<>();
        params = new HashMap<>();
        services = new MiddlewareServices();
        posObj = new FetchPOSIdSB();
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
//		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("mid", "OHDpzh00093359209989");
        params.put("merchantCustId", "1001891503");
        params.put("phoneNumber", "6958471900");
        params.put("solutionType", "sound_box");
        params.put("solutionTypeLevel2", "sound_box_replacement");
        params.put("entityType", "INDIVIDUAL");
        
        response = services.fetchMerchantPosIds(posObj, headers, params);
        Assert.assertEquals(response.getStatusCode(), 401);
    }

    @Test
    public void TC07_verify_Status_Code_When_SolutionType_Is_Missing() {
    	  headers = new HashMap<>();
          params = new HashMap<>();
          services = new MiddlewareServices();
          posObj = new FetchPOSIdSB();
          headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
          headers.put("session_token", sessionToken);
  		  headers.put("version", "5.1.6");
          headers.put("Content-Type", "application/json; charset=UTF-8");
          headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
          headers.put("latitude" , "12.836047");
          headers.put("ipAddress" , "***********");
          headers.put("isBusyBoxFound" , "false");
          headers.put("osVersion" , "10");
          headers.put("appLanguage","en");
          headers.put("isDeviceRooted" , "false");
        
        params.put("mid", "OHDpzh00093359209989");
        params.put("merchantCustId", "1001891503");
        params.put("phoneNumber", "6958471900");
        params.put("solutionTypeLevel2", "sound_box_replacement");
        params.put("entityType", "INDIVIDUAL");
        
        response = services.fetchMerchantPosIds(posObj, headers, params);
        Assert.assertEquals(response.getStatusCode(), 200); 
    }

    @Test
    public void TC08_verify_Status_Code_When_SolutionTypeLevel2_Is_Missing() {
  	  	headers = new HashMap<>();
  	  	params = new HashMap<>();
  	  	services = new MiddlewareServices();
  	  	posObj = new FetchPOSIdSB();
  	  	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
  	  	headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
		headers.put("Content-Type", "application/json; charset=UTF-8");
		headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
		headers.put("latitude" , "12.836047");
		headers.put("ipAddress" , "***********");
		headers.put("isBusyBoxFound" , "false");
		headers.put("osVersion" , "10");
		headers.put("appLanguage","en");
		headers.put("isDeviceRooted" , "false");
        
        params.put("mid", "OHDpzh00093359209989");
        params.put("merchantCustId", "1001891503");
        params.put("phoneNumber", "6958471900");
        params.put("solutionType", "sound_box");
        params.put("entityType", "INDIVIDUAL");
        
        response = services.fetchMerchantPosIds(posObj, headers, params);
        Assert.assertEquals(response.getStatusCode(), 200); 
    }
    
    @Test
    public void TC09_verify_Status_Code_And_Message_When_Version_Is_Missing_In_Header() {
     	headers = new HashMap<>();
  	  	params = new HashMap<>();
  	  	services = new MiddlewareServices();
  	  	posObj = new FetchPOSIdSB();
  	  	headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
  	  	headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
		headers.put("Content-Type", "application/json; charset=UTF-8");
		headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
		headers.put("latitude" , "12.836047");
		headers.put("ipAddress" , "***********");
		headers.put("isBusyBoxFound" , "false");
		headers.put("osVersion" , "10");
		headers.put("appLanguage","en");
		headers.put("isDeviceRooted" , "false");
        
        params.put("mid", "OHDpzh00093359209989");
        params.put("merchantCustId", "1001891503");
        params.put("phoneNumber", "6958471900");
        params.put("solutionType", "sound_box");
        params.put("solutionTypeLevel2", "sound_box_replacement");
        params.put("entityType", "INDIVIDUAL");
        
        response = services.fetchMerchantPosIds(posObj, headers, params);
        
      
        Assert.assertEquals(response.getStatusCode(), 200);
        
        Assert.assertTrue(response.path("message").toString().contains("version is empty in header"));
    }


}
