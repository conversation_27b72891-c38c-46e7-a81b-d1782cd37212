package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.DIYUpdateDeviceID;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class DIYUpdateDeviceIDTest extends BaseMethod{

	
	DIYUpdateDeviceID diyObj;
	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> body;
	
	
	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	

	@Test
	public void verify_Error_Code_When_Lead_Stage_Is_In_Failure_Stage() {
	
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		diyObj=new DIYUpdateDeviceID();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");

        body.put("leadId", "e7373a94-a375-4bc3-920b-b6a15d0b6f92");
        body.put("deviceId", "563747466003");
     
        response=services.diyUpdateDevice(diyObj, body, headers);
        Assert.assertEquals(response.getStatusCode(), 500);
	}
	
	@Test
	public void verify_Error_Message_When_Lead_Stage_Is_In_Failure_Stage() {
	
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		diyObj=new DIYUpdateDeviceID();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");

        body.put("leadId", "e7373a94-a375-4bc3-920b-b6a15d0b6f92");
        body.put("deviceId", "563747466003");
     
        response=services.diyUpdateDevice(diyObj, body, headers);
        Assert.assertEquals(response.getStatusCode(), 500);	}
	
	
	@Test
	public void verify_Error_Message_When_Lead_Stage_Is_In_Success_Stage_But_Other_Than_Required_Stage() {
	
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		diyObj=new DIYUpdateDeviceID();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");

        body.put("leadId", "aa87a2f6-a239-47e1-9d32-30840fb3e8e7");
        body.put("deviceId", "563747466004");
     
        response=services.diyUpdateDevice(diyObj, body, headers);
        Assert.assertTrue(response.path("displayMessage").toString().contains("Invalid Lead Stage"));
	}
	
	@Test
	public void verify_RefId_Is_Present_In_Response_In_Case_Of_Update_Failure() {
	
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		diyObj=new DIYUpdateDeviceID();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");

        body.put("leadId", "e7373a94-a375-4bc3-920b-b6a15d0b6f92");
        body.put("deviceId", "563747466003");
     
        response=services.diyUpdateDevice(diyObj, body, headers);
        Assert.assertTrue(!response.path("refId").toString().isEmpty());
	}
	
	@Test
	public void verify_Error_Code_When_Device_ID_Is_Missing() {
	
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		diyObj=new DIYUpdateDeviceID();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");

        body.put("leadId", "09fb51ed-f25a-4785-a1cf-838e1dbb0d39");
      
     
        response=services.diyUpdateDevice(diyObj, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	

	@Test
	public void verify_Error_Code_When_Lead_ID_Is_Missing() {
	
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		diyObj=new DIYUpdateDeviceID();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");

        body.put("deviceId", "98989898998");
      
     
        response=services.diyUpdateDevice(diyObj, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	



	@Test
	public void verify_Error_Message_When_Device_id_Is_Incorrect() {
	
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		diyObj=new DIYUpdateDeviceID();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");

        body.put("leadId", "55cb360a-9a5b-462e-9768-1b42bc61f6c0");
        body.put("deviceId", "xyz");
     
        response=services.diyUpdateDevice(diyObj, body, headers);
        Assert.assertEquals(response.getStatusCode(), 500);	}

	
	@Test
	public void verify_Error_Code_When_Device_id_Is_Already_Mapped_To_Another_MID_In_Defined_Threshold() {
	
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		diyObj=new DIYUpdateDeviceID();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");

        body.put("leadId", "b76a95c7-284d-4d62-a1be-f1fb5136b8e4");
        body.put("deviceId", "563747466001");
     
        response=services.diyUpdateDevice(diyObj, body, headers);
        Assert.assertTrue(response.path("statusCode").toString().contains("500"));
	}
	
	
	@Test
	public void verify_Error_Code_When_Session_Token_Is_Missing_From_Request() {
	
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		diyObj=new DIYUpdateDeviceID();
		
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");

        body.put("leadId", "b76a95c7-284d-4d62-a1be-f1fb5136b8e4");
        body.put("deviceId", "563747466001");
     
        response=services.diyUpdateDevice(diyObj, body, headers);
        Assert.assertEquals(response.getStatusCode(), 401);
	}
	
	
	@Test
	public void verify_Error_Code_When_App_Version_Is_Missing_From_Request() {
	
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		diyObj=new DIYUpdateDeviceID();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");

        body.put("leadId", "b76a95c7-284d-4d62-a1be-f1fb5136b8e4");
        body.put("deviceId", "563747466001");
     
        response=services.diyUpdateDevice(diyObj, body, headers);
        Assert.assertEquals(response.path("displayMessage"), "version is empty in header");
	}

	@Test
	public void verify_Error_Code_When_Device_Identifier_Is_Missing() {
	
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		diyObj=new DIYUpdateDeviceID();
		
		headers.put("session_token", sessionToken);
	
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");

        body.put("leadId", "b76a95c7-284d-4d62-a1be-f1fb5136b8e4");
        body.put("deviceId", "563747466001");
     
        response=services.diyUpdateDevice(diyObj, body, headers);
        Assert.assertEquals(response.getStatusCode(), 410);
	}

	
	
	@Test
	public void verify_Error_Code_When_Content_Type_Is_Missing_In_Request() {
	
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		diyObj=new DIYUpdateDeviceID();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "xyzuxy");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");

        body.put("leadId", "b76a95c7-284d-4d62-a1be-f1fb5136b8e4");
        body.put("deviceId", "563747466001");
     
        response=services.diyUpdateDevice(diyObj, body, headers);
        Assert.assertEquals(response.path("statusCode").toString(), "500");
        
	}


}