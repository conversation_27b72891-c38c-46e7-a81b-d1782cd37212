package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.SoundboxVASLeadCreation;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class SoundboxVASLeadCreationTest extends BaseMethod {

	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> body;
	public SoundboxVASLeadCreation vas;
	
	@BeforeMethod
	public void setToken() {
	System.out.println("----------------started");
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	@Test(priority =1)
	public void verify_Status_Code_When_Lead_Is_SUccessfully_Created() {
		headers=new HashMap<>();
		body=new HashMap<>();
	
		services=new MiddlewareServices();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
	
        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "563747466003");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "6665550217");
        body.put("mid", "DHVRVL02894215991065");
        body.put("merchantName", "TestBeneficiary");
        body.put("userCustId", "1001373711");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");
        
        vas = new SoundboxVASLeadCreation();
        response = services.createSbVASLead(vas, body, headers);
        Assert.assertEquals(response.getStatusCode(), 200);
	}
	
	@Test(priority =2)
	public void verify_Success_Message__When_A_New_Lead_Is_Created() {
		headers=new HashMap<>();
		body=new HashMap<>();
	
		services=new MiddlewareServices();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
	
        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "104180051517201");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("userMobile", "6665550217");
        body.put("mid", "DHVRVL02894215991065");
        body.put("merchantName", "TestBeneficiary");
        body.put("userCustId", "1001373711");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");
        
        vas = new SoundboxVASLeadCreation();
        response = services.createSbVASLead(vas, body, headers);
        Assert.assertEquals(response.getStatusCode(), 200);

	}
	
	@Test(priority =3)
	public void verify_Success_Message__When_A_Lead_Is_Already_Existing() {
		headers=new HashMap<>();
		body=new HashMap<>();
	
		services=new MiddlewareServices();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
	
        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "563747466003");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "6665550217");
        body.put("mid", "DHVRVL02894215991065");
        body.put("merchantName", "TestBeneficiary");
        body.put("userCustId", "1001373711");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");
        
        vas = new SoundboxVASLeadCreation();
        response = services.createSbVASLead(vas, body, headers);
        Assert.assertTrue(response.path("displayMessage").toString().contains("Lead already exists"));
        Assert.assertEquals(response.getStatusCode(), 200);
	}
	
	@Test(priority =4)
	public void verify_Error_Message_And_Error_Code_In_Case_Of_Empty_MID() {
		headers=new HashMap<>();
		body=new HashMap<>();
	
		services=new MiddlewareServices();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
	
        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "563747466003");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "9996669350");
        body.put("mid", "");
        body.put("merchantName", "TestBeneficiary");
        body.put("userCustId", "1001373711");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");
        
        vas = new SoundboxVASLeadCreation();
        response = services.createSbVASLead(vas, body, headers);
        Assert.assertTrue(response.path("displayMessage").toString().contains("Mid cannot be blank"));
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test(priority =5)
	public void verify_Error_Message_And_Error_Code_In_Case_Of_Missing_AgentID() {
		headers=new HashMap<>();
		body=new HashMap<>();
	
		services=new MiddlewareServices();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
	
        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "563747466003");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "9996669350");
        body.put("mid", "DHVRVL02894215991065");
        body.put("merchantName", "TestBeneficiary");
        body.put("userCustId", "1001373711");
        body.put("agentCustId", "");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");
        
        vas = new SoundboxVASLeadCreation();
        response = services.createSbVASLead(vas, body, headers);
        Assert.assertTrue(response.path("displayMessage").toString().contains("Agent CustId cannot be blank"));
        Assert.assertEquals(response.getStatusCode(), 400);
	}

	
	@Test(priority =6)
	public void verify_Error_Message_And_Error_Code_When_Vas_Active_Subcscription_Already_Exists_For_DeviceID_And_MID_() {
		headers=new HashMap<>();
		body=new HashMap<>();
	
		services=new MiddlewareServices();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
	
        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "563747466006");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "6665550217");
        body.put("mid", "");
        body.put("merchantName", "TestBeneficiary");
        body.put("userCustId", "1001373711");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");
        
        vas = new SoundboxVASLeadCreation();
        response = services.createSbVASLead(vas, body, headers);

        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test(priority =7)
	public void verify_Error_Message_And_Error_Code_When_Vas_Subcscription_Already_Exists_For_DeviceID_And_MID_In_Suspended_State() {
		headers=new HashMap<>();
		body=new HashMap<>();
	
		services=new MiddlewareServices();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
	
        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "563747466007");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "6665550217");
        body.put("mid", "");
        body.put("merchantName", "TestBeneficiary");
        body.put("userCustId", "");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");
        
        vas = new SoundboxVASLeadCreation();
        response = services.createSbVASLead(vas, body, headers);
       
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test(priority =8)
	public void verify_Status_Code_When_Solution_Type_Is_Not_Valid() {
		headers=new HashMap<>();
		body=new HashMap<>();
	
		services=new MiddlewareServices();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
	
//        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "563747466003");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "6665550217");
        body.put("mid", "DHVRVL02894215991065");
        body.put("merchantName", "TestBeneficiary");
        body.put("userCustId", "1001373711");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");
        
        vas = new SoundboxVASLeadCreation();
        response = services.createSbVASLead(vas, body, headers);
        Assert.assertEquals(response.getStatusCode(), 500);
	}
	
	@Test(priority =9)
	public void verify_Status_Code_When_DEVICEID_Type_Is_Not_Valid() {
		headers=new HashMap<>();
		body=new HashMap<>();
	
		services=new MiddlewareServices();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
	
        body.put("solutionTypeLevel3", "premium_care");
//        body.put("deviceId", "563747466003");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "6665550217");
        body.put("mid", "DHVRVL02894215991065");
        body.put("merchantName", "TestBeneficiary");
        body.put("userCustId", "1001373711");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");
        
        vas = new SoundboxVASLeadCreation();
        response = services.createSbVASLead(vas, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	
	@Test(priority =10)
	public void verify_Status_Code_When_Entity_Type_Is_Not_Valid() {
		headers=new HashMap<>();
		body=new HashMap<>();
	
		services=new MiddlewareServices();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
	
        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "563747466003");
//        body.put("deviceType", "SOUNDBOX_4G");
//        body.put("userMobile", "6665550217");
        body.put("mid", "DHVRVL02894215991065");
        body.put("merchantName", "TestBeneficiary");
        body.put("userCustId", "1001373711");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "PROPERITORSHIP");
        body.put("solutionSubType", "sound_box_vas");
        
        vas = new SoundboxVASLeadCreation();
        response = services.createSbVASLead(vas, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}

	
	
	@Test(priority =11)
	public void verify_Status_Code_When_SolutionSubType_Is_Not_Valid() {
		headers=new HashMap<>();
		body=new HashMap<>();
	
		services=new MiddlewareServices();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
	
        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "563747466003");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "6665550217");
        body.put("mid", "DHVRVL02894215991065");
        body.put("merchantName", "TestBeneficiary");
        body.put("userCustId", "1001373711");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
//        body.put("solutionSubType", "sound_box_vas");
        
        vas = new SoundboxVASLeadCreation();
        response = services.createSbVASLead(vas, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}

	
	
	@Test(priority =12)
	public void verify_Status_Code_When_Version_Is_Not_Passed_In_Request() {
		headers=new HashMap<>();
		body=new HashMap<>();
	
		services=new MiddlewareServices();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
//		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
	
        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "563747466003");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "6665550217");
        body.put("mid", "DHVRVL02894215991065");
        body.put("merchantName", "TestBeneficiary");
        body.put("userCustId", "1001373711");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");
        
        vas = new SoundboxVASLeadCreation();
        response = services.createSbVASLead(vas, body, headers);
        Assert.assertEquals(response.path("message"), "version is empty in header");
	}
	
	
	
	@Test(priority =13)
	public void verify_Status_Code_When_Token_Is_Not_Passed_In_Request() {
		headers=new HashMap<>();
		body=new HashMap<>();
	
		services=new MiddlewareServices();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
//		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
	
        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "563747466003");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "6665550217");
        body.put("mid", "DHVRVL02894215991065");
        body.put("merchantName", "TestBeneficiary");
        body.put("userCustId", "1001373711");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");
        
        vas = new SoundboxVASLeadCreation();
        response = services.createSbVASLead(vas, body, headers);
        Assert.assertEquals(response.getStatusCode(), 401);
	}

}