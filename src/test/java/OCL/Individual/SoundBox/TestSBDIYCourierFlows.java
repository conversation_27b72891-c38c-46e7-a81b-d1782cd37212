package OCL.Individual.SoundBox;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.DIYUpdateDeviceDetails;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;



/*Prerequisite to use this for regeression
1. update db ip in config.properties
2. update db name in basemethod class - BaseMethod.DbName
3. create a fresh lead and fresh qr(will automate qr part soon)
*/



public class TestSBDIYCourierFlows  extends BaseMethod {

	private Response response;
	private MiddlewareServices services;
	
	private Map<String,String> headers;
	private Map<String, String> body;
	private DIYUpdateDeviceDetails details;
	private String jwt="";
	private String leadId="060b7d24-b9a7-4f5d-998f-419586fe4fa8";
	private String invalidLead="7b6a1f0e-406f-4d5f-b1fa-fa492c38957d";
	private String cancelledLead="b7c6a6cf-d1a4-4db2-ac63-d683c4395049";

	@Test
	public void TC01_Check_Status_Code_And_Error_Message_When_Lead_Is_Not_At_STS_Order_Creation_Success() throws Exception {
		
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		details=new DIYUpdateDeviceDetails();
		jwt=createStsTokenForDIY();
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		stmt.execute("use releaseOctW4");
		
		ResultSet res= stmt.executeQuery("select id from user_business_mapping ubm where lead_id ='"+invalidLead+"'");
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = "+id+" and ws.is_active =1");
		res.next();
		String nodeId=res.getString("workflow_node_id");
		res=stmt.executeQuery("Select wn.sub_stage from workflow_node wn where wn.id = "+nodeId);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();
		
		Assert.assertNotEquals(subStage, "STS_ORDER_CREATION_SUCCESS");
		
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");

		body.put("leadId", invalidLead);
		body.put("action", "UPDATE_DEVICE_DETAILS");
		body.put("deviceId", "************");
		body.put("simDetails", "8991102105555713921U");
		body.put("qrString", "upi://pay?pa=paytmocl.s10u8jq@axis&pn=Paytm");
		
		response=services.diyUpdateLeadDeivceDetails(details, headers, body);
		response.then().statusCode(400);
		Assert.assertTrue(response.path("displayMessage").toString().contains("Invalid Lead Stage"));
		
	
	}
	
	@Test
	public void TC02_Check_Status_Code_And_Error_Message_When_Order_Is_Cancelled_And_Lead_Is_ForceFully_Closed() throws Exception {

		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		details=new DIYUpdateDeviceDetails();
		jwt=createStsTokenForDIY();
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		stmt.execute("use releaseOctW4");
		
		ResultSet res= stmt.executeQuery("select id from user_business_mapping ubm where lead_id ='"+cancelledLead+"'");
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = "+id+" and ws.is_active =1");
		res.next();
		String nodeId=res.getString("workflow_node_id");
		res=stmt.executeQuery("Select wn.sub_stage from workflow_node wn where wn.id = "+nodeId);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();
		
		Assert.assertEquals(subStage, "LEAD_FORCEFULLY_CLOSED");
		
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");

		body.put("leadId", cancelledLead);
		body.put("action", "UPDATE_DEVICE_DETAILS");
		body.put("deviceId", "************");
		body.put("simDetails", "8991102105555713921U");
		body.put("qrString", "upi://pay?pa=paytmocl.s10u8jq@axis&pn=Paytm");
		
		response=services.diyUpdateLeadDeivceDetails(details, headers, body);
		response.then().statusCode(400);
		Assert.assertTrue(response.path("displayMessage").toString().contains("Invalid LeadId"));
		
	
	}
	
	
	@Test
	public void TC03_Check_Status_Code_And_Error_Message_When_MSIDN_Number_For_Sim_Is_Missing_At_ATS() throws Exception {
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		details=new DIYUpdateDeviceDetails();
		jwt=createStsTokenForDIY();
	
	
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");

		body.put("leadId", leadId);
		body.put("action", "UPDATE_DEVICE_DETAILS");
		body.put("deviceId", "************");
		body.put("simDetails", "899110210500000001U");
		body.put("qrString", "upi://pay?pa=paytmocl.s10u8jq@axis&pn=Paytm");
		
		response=services.diyUpdateLeadDeivceDetails(details, headers, body);
		response.then().statusCode(400);
		Assert.assertTrue(response.path("displayMessage").toString().contains("Sim MSIDN is empty At ATS"));
		
		// verify that lead substage is not changed due to an error from ats
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		stmt.execute("use releaseOctW4");
		ResultSet res= stmt.executeQuery("select id from user_business_mapping ubm where lead_id ='"+leadId+"'");
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = "+id+" and ws.is_active =1");
		res.next();
		String nodeId=res.getString("workflow_node_id");
		res=stmt.executeQuery("Select wn.sub_stage from workflow_node wn where wn.id = "+nodeId);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();

		Assert.assertEquals(subStage, "STS_ORDER_CREATION_SUCCESS");
	
	
	}
	
	
	@Test
	public void TC04_Check_Status_Code_And_Error_Message_When_Account_Number_For_Sim_Is_Missing_At_ATS() throws Exception {
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		details=new DIYUpdateDeviceDetails();
		jwt=createStsTokenForDIY();
	
	
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");

		body.put("leadId", leadId);
		body.put("action", "UPDATE_DEVICE_DETAILS");
		body.put("deviceId", "************");
		body.put("simDetails", "899110210500000002U");
		body.put("qrString", "upi://pay?pa=paytmocl.s10u8jq@axis&pn=Paytm");
		
		response=services.diyUpdateLeadDeivceDetails(details, headers, body);
		response.then().statusCode(400);
		Assert.assertTrue(response.path("displayMessage").toString().contains("Sim Operator is empty At ATS"));
		
		// verify that lead substage is not changed due to an error from ats
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		stmt.execute("use releaseOctW4");
		ResultSet res= stmt.executeQuery("select id from user_business_mapping ubm where lead_id ='"+leadId+"'");
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = "+id+" and ws.is_active =1");
		res.next();
		String nodeId=res.getString("workflow_node_id");
		res=stmt.executeQuery("Select wn.sub_stage from workflow_node wn where wn.id = "+nodeId);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();

		Assert.assertEquals(subStage, "STS_ORDER_CREATION_SUCCESS");
	
	
	}
	
	
	@Test
	public void TC05_Check_Status_Code_And_Error_Message_When_SIM_Is_Not_Present_At_ATS() throws Exception {
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		details=new DIYUpdateDeviceDetails();
		jwt=createStsTokenForDIY();
	
	
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");

		body.put("leadId", leadId);
		body.put("action", "UPDATE_DEVICE_DETAILS");
		body.put("deviceId", "************");
		body.put("simDetails", "899110210500000002223344U");
		body.put("qrString", "upi://pay?pa=paytmocl.s10u8jq@axis&pn=Paytm");
		
		response=services.diyUpdateLeadDeivceDetails(details, headers, body);
		response.then().statusCode(400);
		Assert.assertTrue(response.path("displayMessage").toString().contains("No SIM details found at ATS"));
		
		// verify that lead substage is not changed due to an error from ats
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		stmt.execute("use releaseOctW4");
		ResultSet res= stmt.executeQuery("select id from user_business_mapping ubm where lead_id ='"+leadId+"'");
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = "+id+" and ws.is_active =1");
		res.next();
		String nodeId=res.getString("workflow_node_id");
		res=stmt.executeQuery("Select wn.sub_stage from workflow_node wn where wn.id = "+nodeId);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();

		Assert.assertEquals(subStage, "STS_ORDER_CREATION_SUCCESS");
	
	
	}
	
	@Test
	public void TC06_Check_Status_Code_And_Error_Message_When_DeviceID_Is_Not_Present_At_IOT() throws Exception {
		String leadId1="7b6a1f0e-406f-4d5f-b1fa-fa492c38957d";
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		details=new DIYUpdateDeviceDetails();
		jwt=createStsTokenForDIY();
	
	
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");

		body.put("leadId", leadId1);
		body.put("action", "UPDATE_DEVICE_DETAILS");
		body.put("deviceId", "************010101");
		body.put("simDetails", "8991102105555713801U");
		body.put("qrString", "upi://pay?pa=paytmocl.s10u8jq@axis&pn=Paytm");
		
		response=services.diyUpdateLeadDeivceDetails(details, headers, body);
		response.then().statusCode(400);
		Assert.assertTrue(response.path("displayMessage").toString().contains("Device information not found at IOT"));
		
		// verify that lead substage is not changed due to an error from ats
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		stmt.execute("use releaseOctW4");
		ResultSet res= stmt.executeQuery("select id from user_business_mapping ubm where lead_id ='"+leadId1+"'");
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = "+id+" and ws.is_active =1");
		res.next();
		String nodeId=res.getString("workflow_node_id");
		res=stmt.executeQuery("Select wn.sub_stage from workflow_node wn where wn.id = "+nodeId);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();

		Assert.assertEquals(subStage, "STS_ORDER_CREATION_SUCCESS");
	
	
	}
	
	@Test
	public void TC07_Check_Status_Code_When_Token_Is_Expired()  {
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		details=new DIYUpdateDeviceDetails();
		jwt=createStsTokenForDIY();
	
	
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRJZCI6IlNUUyIsImlzcyI6Ik9FIiwiY3VzdElkIjoiMTIzIiwidGltZXN0YW1wIjoiMjAyNC0xMC0wOVQwNzo1NToyMS41MDVaIn0.nCN2k70whIKM1yLD43pIwE76opXAGsncSjjAlz1NLsE");
		headers.put("custId", "123");

		body.put("leadId", leadId);
		body.put("action", "UPDATE_DEVICE_DETAILS");
		body.put("deviceId", "************010101");
		body.put("simDetails", "8991102105555713801U");
		body.put("qrString", "upi://pay?pa=paytmocl.s10u8jq@axis&pn=Paytm");
		
		response=services.diyUpdateLeadDeivceDetails(details, headers, body);
		response.then().statusCode(401);

			
	}
	
	@Test
	public void TC08_Check_Status_Code_When_Lead_Id_Is_Not_Passed_In_Request()  {
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		details=new DIYUpdateDeviceDetails();
		jwt=createStsTokenForDIY();
	
	
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");

		body.put("leadId", "");
		body.put("action", "UPDATE_DEVICE_DETAILS");
		body.put("deviceId", "************010101");
		body.put("simDetails", "8991102105555713801U");
		body.put("qrString", "upi://pay?pa=paytmocl.s10u8jq@axis&pn=Paytm");
		
		response=services.diyUpdateLeadDeivceDetails(details, headers, body);
		response.then().statusCode(400);
			
	}
	
	
	@Test
	public void TC09_Check_Status_Code_When_Device_Id_Is_Not_Passed_In_Request()  {
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		details=new DIYUpdateDeviceDetails();
		jwt=createStsTokenForDIY();
	
	
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");

		body.put("leadId", leadId);
		body.put("action", "UPDATE_DEVICE_DETAILS");
		body.put("deviceId", "");
		body.put("simDetails", "8991102105555713801U");
		body.put("qrString", "upi://pay?pa=paytmocl.s10u8jq@axis&pn=Paytm");
		
		response=services.diyUpdateLeadDeivceDetails(details, headers, body);
		response.then().statusCode(400);
			
	}
	
	@Test
	public void TC10_Check_Status_Code_When_Action_Is_Not_Passed_In_Request()  {
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		details=new DIYUpdateDeviceDetails();
		jwt=createStsTokenForDIY();
	
	
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");

		body.put("leadId", leadId);
		body.put("action", "");
		body.put("deviceId", "************");
		body.put("simDetails", "8991102105555713801U");
		body.put("qrString", "upi://pay?pa=paytmocl.s10u8jq@axis&pn=Paytm");
		
		response=services.diyUpdateLeadDeivceDetails(details, headers, body);
		response.then().statusCode(400);
			
	}
	
	@Test
	public void TC11_Check_Status_Code_When_Sim_Details_Is_Not_Passed_In_Request()  {
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		details=new DIYUpdateDeviceDetails();
		jwt=createStsTokenForDIY();
	
	
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");

		body.put("leadId", leadId);
		body.put("action", "");
		body.put("deviceId", "************");
		body.put("simDetails", "");
		body.put("qrString", "upi://pay?pa=paytmocl.s10u8jq@axis&pn=Paytm");
		
		response=services.diyUpdateLeadDeivceDetails(details, headers, body);
		response.then().statusCode(400);
			
	}

	@Test
	public void TC12_Check_Status_Code_When_QR_Is_Not_Passed_In_Request()  {
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		details=new DIYUpdateDeviceDetails();
		jwt=createStsTokenForDIY();
	
	
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");

		body.put("leadId", leadId);
		body.put("action", "");
		body.put("deviceId", "************");
		body.put("simDetails", "");
		body.put("qrString", "");
		
		response=services.diyUpdateLeadDeivceDetails(details, headers, body);
		response.then().statusCode(400);
			
	}
	
	
	
	//Run This case directly to update lead details if checking happy case
	
	@Test
	public void TC13_Check_Status_Code_And_Message_When_Lead_Is_Successfully_Updated() throws Exception {
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		details=new DIYUpdateDeviceDetails();
		jwt=createStsTokenForDIY();
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		stmt.execute("use releaseOctW4");
		
		ResultSet res= stmt.executeQuery("select id from user_business_mapping ubm where lead_id ='"+leadId+"'");
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = "+id+" and ws.is_active =1");
		res.next();
		String nodeId=res.getString("workflow_node_id");
		res=stmt.executeQuery("Select wn.sub_stage from workflow_node wn where wn.id = "+nodeId);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		
		
		Assert.assertEquals(subStage, "STS_ORDER_CREATION_SUCCESS");
		
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");

		body.put("leadId", leadId);
		body.put("action", "UPDATE_DEVICE_DETAILS");
		body.put("deviceId", "************");
		body.put("simDetails", "8991102105555713801U");
		body.put("qrString", "upi://pay?pa=paytmocl.s10u8jr@axis&pn=Paytm");
		
		response=services.diyUpdateLeadDeivceDetails(details, headers, body);
		response.then().statusCode(200);

		int count=0;
		
		while(count<4 && !nodeId.equalsIgnoreCase("1984")) {
		Thread.sleep(15000); //wait for rest of the jobs to run
		res=stmt.executeQuery("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = "+id+" and ws.is_active =1");
		res.next();
		nodeId=res.getString("workflow_node_id");
		count++;
		}
		res=stmt.executeQuery("Select wn.sub_stage from workflow_node wn where wn.id = "+nodeId);
		res.next();
		subStage=res.getString("sub_stage");
		Assert.assertEquals(subStage, "STS_UPDATE_INVOICE_DETAILS_SUCCESS");
	}

	
	
}
