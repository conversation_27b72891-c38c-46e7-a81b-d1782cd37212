package OCL.Individual.SoundBox;

import Request.SoundBox.GetSoundBoxMerchantLeadDetails;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import Services.DBConnection.DBConnection;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

/**
 * Test class for getSoundBoxMerchantLeadDetails API
 * This API retrieves merchant lead details for a sound box replacement
 */
public class GetSoundBoxMerchantLeadDetailsTest extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(GetSoundBoxMerchantLeadDetailsTest.class);

    String AgentToken;
    String merchantCustId = "1704489338";
    String merchantMobileNumber = "8853158998";
    String solutionType = "sound_box";
    String solutionTypeLevel2 = "sound_box_replacement";
    String entityType = "INDIVIDUAL";

    @BeforeClass
    public void setup() throws Exception {
        AgentToken = AgentSessionToken("7771216290", "paytm@123");
        LOGGER.info("Agent Token: " + AgentToken);
        establishConnectiontoServer(AgentToken, 5);
        
        // Check and close any active sound box replacement leads in the database
        updateSoundBoxReplacementLeadStatusInDB();
    }

    /**
     * Test case to verify API returns valid response with valid data
     */
    @Test(priority = 1)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testGetSoundBoxMerchantLeadDetailsWithValidData() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, String> queryParams = createQueryParams(merchantCustId, merchantMobileNumber, solutionType, solutionTypeLevel2, entityType);

        GetSoundBoxMerchantLeadDetails getSoundBoxMerchantLeadDetailsObj = new GetSoundBoxMerchantLeadDetails();
        
        // Call API directly
        for (Map.Entry<String, String> header : headers.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.setHeader(header.getKey(), header.getValue());
        }
        
        for (Map.Entry<String, String> param : queryParams.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.addParameter(param.getKey(), param.getValue());
        }
        
        Response response = getSoundBoxMerchantLeadDetailsObj.callAPI();
        
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        
        String responseBody = response.getBody().asString();
        LOGGER.info("Response for valid data: " + responseBody);
        
        // Validate response fields
        Assert.assertEquals(response.getBody().jsonPath().getInt("statusCode"), 0);
        Assert.assertNotNull(response.getBody().jsonPath().getString("refId"));
        Assert.assertTrue(response.getBody().jsonPath().getBoolean("merchantDetails.canDiscardCurrentLead"));
    }

    /**
     * Test case to verify API returns error with missing merchantCustId
     */
    @Test(priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testGetSoundBoxMerchantLeadDetailsWithMissingMerchantCustId() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, String> queryParams = createQueryParams("", merchantMobileNumber, solutionType, solutionTypeLevel2, entityType);

        GetSoundBoxMerchantLeadDetails getSoundBoxMerchantLeadDetailsObj = new GetSoundBoxMerchantLeadDetails();
        
        // Call API directly
        for (Map.Entry<String, String> header : headers.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.setHeader(header.getKey(), header.getValue());
        }
        
        for (Map.Entry<String, String> param : queryParams.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.addParameter(param.getKey(), param.getValue());
        }
        
        Response response = getSoundBoxMerchantLeadDetailsObj.callAPI();
        
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        
     
    }

    /**
     * Test case to verify API returns error with missing merchantMobileNumber
     */
    @Test(priority = 3)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testGetSoundBoxMerchantLeadDetailsWithMissingMerchantMobileNumber() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, String> queryParams = createQueryParams(merchantCustId, "", solutionType, solutionTypeLevel2, entityType);

        GetSoundBoxMerchantLeadDetails getSoundBoxMerchantLeadDetailsObj = new GetSoundBoxMerchantLeadDetails();
        
        // Call API directly
        for (Map.Entry<String, String> header : headers.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.setHeader(header.getKey(), header.getValue());
        }
        
        for (Map.Entry<String, String> param : queryParams.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.addParameter(param.getKey(), param.getValue());
        }
        
        Response response = getSoundBoxMerchantLeadDetailsObj.callAPI();
        
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        
    
    }

    /**
     * Test case to verify API returns error with invalid solutionType
     */
    @Test(priority = 4)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testGetSoundBoxMerchantLeadDetailsWithInvalidSolutionType() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, String> queryParams = createQueryParams(merchantCustId, merchantMobileNumber, "invalid_solution", solutionTypeLevel2, entityType);

        GetSoundBoxMerchantLeadDetails getSoundBoxMerchantLeadDetailsObj = new GetSoundBoxMerchantLeadDetails();
        
        // Call API directly
        for (Map.Entry<String, String> header : headers.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.setHeader(header.getKey(), header.getValue());
        }
        
        for (Map.Entry<String, String> param : queryParams.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.addParameter(param.getKey(), param.getValue());
        }
        
        Response response = getSoundBoxMerchantLeadDetailsObj.callAPI();
        
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 500);
        
        String responseBody = response.getBody().asString();
        LOGGER.info("Response for invalid solutionType: " + responseBody);
        
        // Validate error message in response
        Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("Failed to fetch sound box merchant lead details"),
                "Response should indicate invalid solutionType");
    }
    
    /**
     * Test case to verify API returns error with invalid solutionTypeLevel2
     */
    @Test(priority = 5)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testGetSoundBoxMerchantLeadDetailsWithInvalidSolutionTypeLevel2() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, String> queryParams = createQueryParams(merchantCustId, merchantMobileNumber, solutionType, "invalid_level2", entityType);

        GetSoundBoxMerchantLeadDetails getSoundBoxMerchantLeadDetailsObj = new GetSoundBoxMerchantLeadDetails();
        
        // Call API directly
        for (Map.Entry<String, String> header : headers.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.setHeader(header.getKey(), header.getValue());
        }
        
        for (Map.Entry<String, String> param : queryParams.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.addParameter(param.getKey(), param.getValue());
        }
        
        Response response = getSoundBoxMerchantLeadDetailsObj.callAPI();
        
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        
     
    }
    
    /**
     * Test case to verify API returns error with invalid entityType
     */
    @Test(priority = 6)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testGetSoundBoxMerchantLeadDetailsWithInvalidEntityType() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, String> queryParams = createQueryParams(merchantCustId, merchantMobileNumber, solutionType, solutionTypeLevel2, "INVALID_ENTITY");

        GetSoundBoxMerchantLeadDetails getSoundBoxMerchantLeadDetailsObj = new GetSoundBoxMerchantLeadDetails();
        
        // Call API directly
        for (Map.Entry<String, String> header : headers.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.setHeader(header.getKey(), header.getValue());
        }
        
        for (Map.Entry<String, String> param : queryParams.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.addParameter(param.getKey(), param.getValue());
        }
        
        Response response = getSoundBoxMerchantLeadDetailsObj.callAPI();
        
      
        
        String responseBody = response.getBody().asString();
        LOGGER.info("Response for invalid entityType: " + responseBody);
        
        // Validate error message in response
         Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("Failed to fetch sound box merchant lead details"),
                "Response should indicate invalid entityType");
    }
    
    /**
     * Test case to verify API returns error with invalid merchantMobileNumber format
     */
    @Test(priority = 7)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testGetSoundBoxMerchantLeadDetailsWithInvalidMobileNumberFormat() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, String> queryParams = createQueryParams(merchantCustId, "123", solutionType, solutionTypeLevel2, entityType);

        GetSoundBoxMerchantLeadDetails getSoundBoxMerchantLeadDetailsObj = new GetSoundBoxMerchantLeadDetails();
        
        // Call API directly
        for (Map.Entry<String, String> header : headers.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.setHeader(header.getKey(), header.getValue());
        }
        
        for (Map.Entry<String, String> param : queryParams.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.addParameter(param.getKey(), param.getValue());
        }
        
        Response response = getSoundBoxMerchantLeadDetailsObj.callAPI();
        
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        
        
    }
    
    /**
     * Test case to verify API returns error with non-existent merchantCustId
     */
    @Test(priority = 8)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testGetSoundBoxMerchantLeadDetailsWithNonExistentMerchantCustId() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, String> queryParams = createQueryParams("9999999999", merchantMobileNumber, solutionType, solutionTypeLevel2, entityType);

        GetSoundBoxMerchantLeadDetails getSoundBoxMerchantLeadDetailsObj = new GetSoundBoxMerchantLeadDetails();
        
        // Call API directly
        for (Map.Entry<String, String> header : headers.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.setHeader(header.getKey(), header.getValue());
        }
        
        for (Map.Entry<String, String> param : queryParams.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.addParameter(param.getKey(), param.getValue());
        }
        
        Response response = getSoundBoxMerchantLeadDetailsObj.callAPI();
        
        int statusCode = response.getStatusCode();
        
        String responseBody = response.getBody().asString();
        LOGGER.info("Response for non-existent merchantCustId: " + responseBody);
        
      
            Assert.assertEquals(statusCode, 200, "Expected 200 status code for non-existent merchantCustId");
           
        
    }
    
    /**
     * Test case to verify API with missing Authorization header
     */
    @Test(priority = 9)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testGetSoundBoxMerchantLeadDetailsWithMissingAuthorizationHeader() {
        Map<String, String> headers = new HashMap<>(setCommonHeaders());
        headers.remove("session_token"); // Remove the Authorization header
        
        Map<String, String> queryParams = createQueryParams(merchantCustId, merchantMobileNumber, solutionType, solutionTypeLevel2, entityType);

        GetSoundBoxMerchantLeadDetails getSoundBoxMerchantLeadDetailsObj = new GetSoundBoxMerchantLeadDetails();
        
        // Call API directly
        for (Map.Entry<String, String> header : headers.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.setHeader(header.getKey(), header.getValue());
        }
        
        for (Map.Entry<String, String> param : queryParams.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.addParameter(param.getKey(), param.getValue());
        }
        
        Response response = getSoundBoxMerchantLeadDetailsObj.callAPI();
        
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 401, "Expected 401 status code for missing Authorization header");
        
        String responseBody = response.getBody().asString();
        LOGGER.info("Response for missing Authorization header: " + responseBody);
        
      
    }
    
    /**
     * Test case to verify API with missing required headers
     */
    @Test(priority = 10)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testGetSoundBoxMerchantLeadDetailsWithMissingRequiredHeaders() {
        Map<String, String> headers = new HashMap<>();
        // Only add minimal headers to test with missing required ones
        headers.put("Content-Type", "application/json");
        headers.put("session_token", AgentToken);
        
        Map<String, String> queryParams = createQueryParams(merchantCustId, merchantMobileNumber, solutionType, solutionTypeLevel2, entityType);

        GetSoundBoxMerchantLeadDetails getSoundBoxMerchantLeadDetailsObj = new GetSoundBoxMerchantLeadDetails();
        
        // Call API directly
        for (Map.Entry<String, String> header : headers.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.setHeader(header.getKey(), header.getValue());
        }
        
        for (Map.Entry<String, String> param : queryParams.entrySet()) {
            getSoundBoxMerchantLeadDetailsObj.addParameter(param.getKey(), param.getValue());
        }
        
        Response response = getSoundBoxMerchantLeadDetailsObj.callAPI();
        
        int statusCode = response.getStatusCode();
        
        String responseBody = response.getBody().asString();
        LOGGER.info("Response for missing required headers: " + responseBody);
        
     
            Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("version is empty in header"));
         
       
    }
    
    /**
     * Updates the status of any existing sound box replacement leads in the database.
     * This method checks for entries with solution_type='sound_box', solution_type_level_2='sound_box_replacement',
     * and status=0, and updates their status to 2 (closed).
     * 
     * @throws Exception if there is an error connecting to the database or executing the query
     */
    private void updateSoundBoxReplacementLeadStatusInDB() throws Exception {
        LOGGER.info("Checking for existing sound box replacement leads in the database...");
        
        Connection connection = null;
        try {
            // Establish connection to the database
            connection = DBConnection.establishConnection();
            Statement statement = connection.createStatement();
            
            // First, check if there are any entries that match our criteria
            String selectQuery = "SELECT COUNT(*) as count FROM user_business_mapping WHERE solution_type = 'sound_box' AND solution_type_level_2 = 'sound_box_replacement' AND status = 0";
            ResultSet resultSet = statement.executeQuery(selectQuery);
            
            int count = 0;
            if (resultSet.next()) {
                count = resultSet.getInt("count");
            }
            
            LOGGER.info("Found " + count + " sound box replacement leads with status 0 in the database.");
            
            if (count > 0) {
                // Update the status of matching entries to 2
                String updateQuery = "UPDATE user_business_mapping SET status = 2 WHERE solution_type = 'sound_box' AND solution_type_level_2 = 'sound_box_replacement' AND status = 0";
                int rowsAffected = statement.executeUpdate(updateQuery);
                
                LOGGER.info("Updated " + rowsAffected + " sound box replacement leads to status 2 in the database.");
            } else {
                LOGGER.info("No sound box replacement leads with status 0 found in the database. No updates needed.");
            }
            
        } catch (SQLException e) {
            LOGGER.error("Error updating sound box replacement leads in the database: " + e.getMessage(), e);
            throw e;
        } finally {
            // Close the connection
            if (connection != null && !connection.isClosed()) {
                connection.close();
                LOGGER.info("Database connection closed.");
            }
        }
    }

    /**
     * Helper method to set common headers
     */
    private Map<String, String> setCommonHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Host", "goldengate-staging8.paytm.com");
        headers.put("sec-ch-ua-platform", "Android");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua", "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Android WebView\";v=\"134\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("imei", "861181041338761");
        headers.put("session_token", AgentToken);
        headers.put("devicemac", "08:25:25:99:13:38");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("devicename", "Redmi_Note_5_Pro");
        headers.put("ipaddress", "**************");
        headers.put("osversion", "9");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 9; Redmi Note 5 Pro Build/PKQ1.180904.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/134.0.6998.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.6-10.46.0-MB");
        headers.put("version", "7.3.7");
        headers.put("deviceidentifier", "Xiaomi-RedmiNote5Pro-861181041338761");
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        return headers;
    }

    /**
     * Helper method to create query parameters
     */
    private Map<String, String> createQueryParams(String merchantCustId, String merchantMobileNumber, String solutionType, String solutionTypeLevel2, String entityType) {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("merchantCustId", merchantCustId);
        queryParams.put("merchantMobileNumber", merchantMobileNumber);
        queryParams.put("solutionType", solutionType);
        queryParams.put("solutionTypeLevel2", solutionTypeLevel2);
        queryParams.put("entityType", entityType);
        return queryParams;
    }
} 