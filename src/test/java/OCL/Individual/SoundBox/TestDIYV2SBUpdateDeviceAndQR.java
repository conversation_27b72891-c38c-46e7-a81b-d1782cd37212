package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.DIYV2SBUpdateDeviceAndQR;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestDIYV2SBUpdateDeviceAndQR extends BaseMethod {
	
//	public String sessionToken;
//
//	public String jwt="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRJZCI6IlNUUyIsImlzcyI6Ik9FIiwiY3VzdElkIjoiMTIzIiwidGltZXN0YW1wIjoiMjAyNC0wMy0xOFQxNjo1MTowMi42NzErMDU6MzAifQ.hdwBxAXqHmKCHvm1BrLQmFQlFk1az9-Uo-ZbX9e32Rk";
//
//	public Response response;
//	public MiddlewareServices services;
//	
//	public Map<String,String> headers;
//	public Map<String, String> body;
//	DIYV2SBUpdateDeviceAndQR diy;
//	
//	@BeforeClass
//	public void setToken() {
//	
//		sessionToken=AgentSessionToken("8010630022", "paytm@123");
//		try {
//			establishConnectiontoServer(sessionToken, 5);
//
//		} catch (Exception e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//	
//	}
//	@Test()
//	public void verify_Error_Code_In_Case_Of_JWT_Is_Not_Passed_In_The_Request() {
//		
//		headers=new HashMap<>();
//		body=new HashMap<>();
//		services=new MiddlewareServices();
//		diy = new DIYV2SBUpdateDeviceAndQR();
//		
//		headers.put("custid", "123");
//		headers.put("Authorization", "");
//		headers.put("version", "5.1.6");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        
//        
//        body.put("leadId","45d2718d-8e27-4a20-aa6b-56b6ab972c9f");
//        body.put("action","UPDATE_DEVICE_DETAILS");
//        body.put("deviceId","563747466001");
//        body.put("simDetails","8991102205626804573U");
//        body.put("qrString","10NLNJ");
//        
//        response=services.updateDeviceNQRv2(diy, body, headers);
//    
//        Assert.assertEquals(response.getStatusCode(), 400);        
//	}
//
//	
//	
////	@Test()
////	public void verify_Status_Code_In_Case_Of_Success_Valid_Device_SIM_QR_Passed() {
////		
////		headers=new HashMap<>();
////		body=new HashMap<>();
////		services=new MiddlewareServices();
////		diy = new DIYV2SBUpdateDeviceAndQR();
////		
////		headers.put("custid", "123");
////		headers.put("Authorization", jwt);
////		headers.put("version", "5.1.6");
////        headers.put("Content-Type", "application/json; charset=UTF-8");
////        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
////        headers.put("latitude" , "12.836047");
////        headers.put("ipAddress" , "***********");
////        
////        
////        body.put("leadId","45d2718d-8e27-4a20-aa6b-56b6ab972c9f");
////        body.put("action","UPDATE_DEVICE_DETAILS");
////        body.put("deviceId","563747466001");
////        body.put("simDetails","8991102205626804573U");
////        body.put("qrString","10NLNJ");
////        
////        response=services.updateDeviceNQRv2(diy, body, headers);
////    
////        Assert.assertEquals(response.getStatusCode(), 200);        
////	}
//	
//	@Test()
//	public void verify_Error_Code_When_Lead_Is_Already_Closed_Successfully() {
//		
//		headers=new HashMap<>();
//		body=new HashMap<>();
//		services=new MiddlewareServices();
//		diy = new DIYV2SBUpdateDeviceAndQR();
//		
//		headers.put("custid", "123");
//		headers.put("Authorization", jwt);
//		headers.put("version", "5.1.6");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        
//        
//        body.put("leadId","3b1c1823-b6fb-4929-a048-6a61d3203b6b");
//        body.put("action","UPDATE_DEVICE_DETAILS");
//        body.put("deviceId","563747466001");
//        body.put("simDetails","8991102205626804573U");
//        body.put("qrString","10NLNJ");
//        
//        response=services.updateDeviceNQRv2(diy, body, headers);
//    
//        Assert.assertEquals(response.getStatusCode(), 400);        
//	}
//	
//	@Test()
//	public void verify_Error_Code_When_Order_Is_Cancelled() {
//		
//		headers=new HashMap<>();
//		body=new HashMap<>();
//		services=new MiddlewareServices();
//		diy = new DIYV2SBUpdateDeviceAndQR();
//		
//		headers.put("custid", "123");
//		headers.put("Authorization", jwt);
//		headers.put("version", "5.1.6");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        
//        
//        body.put("leadId","b967520d-6742-4873-ba1c-760f063757fc");
//        body.put("action","UPDATE_DEVICE_DETAILS");
//        body.put("deviceId","563747466001");
//        body.put("simDetails","8991102205626804573U");
//        body.put("qrString","10NLNJ");
//        
//        response=services.updateDeviceNQRv2(diy, body, headers);
//    
//        Assert.assertEquals(response.getStatusCode(), 400);        
//	}
//	
//	@Test()
//	public void verify_Error_Code_When_Lead_Is_Not_Passed_In_The_Request() {
//		
//		headers=new HashMap<>();
//		body=new HashMap<>();
//		services=new MiddlewareServices();
//		diy = new DIYV2SBUpdateDeviceAndQR();
//		
//		headers.put("custid", "123");
//		headers.put("Authorization", jwt);
//		headers.put("version", "5.1.6");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        
//        
//        body.put("leadId","");
//        body.put("action","UPDATE_DEVICE_DETAILS");
//        body.put("deviceId","563747466001");
//        body.put("simDetails","8991102205626804573U");
//        body.put("qrString","10NLNJ");
//        
//        response=services.updateDeviceNQRv2(diy, body, headers);
//    
//        Assert.assertEquals(response.getStatusCode(), 400);        
//	}
//	
//	@Test()
//	public void verify_Error_Code_When_Lead_SubStage_Is_PID_Validation_Failure() {
//		
//		headers=new HashMap<>();
//		body=new HashMap<>();
//		services=new MiddlewareServices();
//		diy = new DIYV2SBUpdateDeviceAndQR();
//		
//		headers.put("custid", "123");
//		headers.put("Authorization", jwt);
//		headers.put("version", "5.1.6");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        
//        
//        body.put("leadId","0a5d553e-5a2a-48a5-9a15-1eecee80f937");
//        body.put("action","UPDATE_DEVICE_DETAILS");
//        body.put("deviceId","563747466001");
//        body.put("simDetails","8991102205626804573U");
//        body.put("qrString","10NLNJ");
//        
//        response=services.updateDeviceNQRv2(diy, body, headers);
//    
//        Assert.assertEquals(response.getStatusCode(), 400);        
//	}
//	
//	@Test()
//	public void verify_Error_Code_When_Lead_SubStage_Is_MID_Identification_Failure() {
//		
//		headers=new HashMap<>();
//		body=new HashMap<>();
//		services=new MiddlewareServices();
//		diy = new DIYV2SBUpdateDeviceAndQR();
//		
//		headers.put("Authorization", jwt);
//		headers.put("custid", "123");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        
//        
//        body.put("leadId","2da8ff03-048e-4716-9ad5-4024e3d5a66a");
//        body.put("action","UPDATE_DEVICE_DETAILS");
//        body.put("deviceId","563747466001");
//        body.put("simDetails","8991102205626804573U");
//        body.put("qrString","10NLNJ");
//        
//        response=services.updateDeviceNQRv2(diy, body, headers);
//    
//        Assert.assertEquals(response.getStatusCode(), 400);        
//	}
//	
//	@Test()
//	public void verify_Error_Code_When_Lead_Is_In_STS_Failure_Substage() {
//		
//		headers=new HashMap<>();
//		body=new HashMap<>();
//		services=new MiddlewareServices();
//		diy = new DIYV2SBUpdateDeviceAndQR();
//		
//		headers.put("custid", "123");
//		headers.put("Authorization", jwt);
//		headers.put("version", "5.1.6");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        
//        
//        body.put("leadId","8b5b75b3-e885-48e7-8d25-a6f9eb1714b4");
//        body.put("action","UPDATE_DEVICE_DETAILS");
//        body.put("deviceId","563747466001");
//        body.put("simDetails","8991102205626804573U");
//        body.put("qrString","10NLNJ");
//        
//        response=services.updateDeviceNQRv2(diy, body, headers);
//    
//        Assert.assertEquals(response.getStatusCode(), 400);        
//	}
//	
//	@Test()
//	public void verify_Error_Code_When_Lead_Is_In_PFF_Sucess_And_Device_ID_Callback_Is_Already_Processed() {
//		
//		headers=new HashMap<>();
//		body=new HashMap<>();
//		services=new MiddlewareServices();
//		diy = new DIYV2SBUpdateDeviceAndQR();
//		
//		headers.put("custid", "123");
//		headers.put("Authorization", jwt);
//		headers.put("version", "5.1.6");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        
//        
//        body.put("leadId","8693ac47-d97d-4136-a516-8507543d0506");
//        body.put("action","UPDATE_DEVICE_DETAILS");
//        body.put("deviceId","563747466001");
//        body.put("simDetails","8991102205626804573U");
//        body.put("qrString","10NLNJ");
//        
//        response=services.updateDeviceNQRv2(diy, body, headers);
//    
//        Assert.assertEquals(response.getStatusCode(), 400);        
//	}
//	
//	@Test()
//	public void verify_Error_Code_In_Case_Of_Request_Action_Is_Not_Passed() {
//		
//		headers=new HashMap<>();
//		body=new HashMap<>();
//		services=new MiddlewareServices();
//		diy = new DIYV2SBUpdateDeviceAndQR();
//		
//		headers.put("custid", "123");
//		headers.put("Authorization", jwt);
//		headers.put("version", "5.1.6");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        
//        
//        body.put("leadId","c885d04a-82be-404b-9408-37a2c8dfc469");
//        body.put("action","");
//        body.put("deviceId","563747466001");
//        body.put("simDetails","8991102205626804573U");
//        body.put("qrString","10NLNJ");
//        
//        response=services.updateDeviceNQRv2(diy, body, headers);
//    
//        Assert.assertEquals(response.getStatusCode(), 400);        
//	}
//	
//	@Test()
//	public void verify_Error_Code_In_Case_Of_Request_DeviceID_Is_Not_Passed() {
//		
//		headers=new HashMap<>();
//		body=new HashMap<>();
//		services=new MiddlewareServices();
//		diy = new DIYV2SBUpdateDeviceAndQR();
//		
//		headers.put("custid", "123");
//		headers.put("Authorization", jwt);
//		headers.put("version", "5.1.6");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        
//        
//        body.put("leadId","c885d04a-82be-404b-9408-37a2c8dfc469");
//        body.put("action","UPDATE_DEVICE_DETAILS");
//        body.put("deviceId","");
//        body.put("simDetails","8991102205626804573U");
//        body.put("qrString","10NLNJ");
//        
//        response=services.updateDeviceNQRv2(diy, body, headers);
//    
//        Assert.assertEquals(response.getStatusCode(), 400);        
//	}
//	
//
//	@Test()
//	public void verify_Error_Code_In_Case_Of_Request_Sim_Is_Not_Passed() {
//		
//		headers=new HashMap<>();
//		body=new HashMap<>();
//		services=new MiddlewareServices();
//		diy = new DIYV2SBUpdateDeviceAndQR();
//		
//		headers.put("custid", "123");
//		headers.put("Authorization", jwt);
//		headers.put("version", "5.1.6");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        
//        
//        body.put("leadId","c885d04a-82be-404b-9408-37a2c8dfc469");
//        body.put("action","UPDATE_DEVICE_DETAILS");
//        body.put("deviceId","563747466004");
//        body.put("simDetails","");
//        body.put("qrString","10NLNJ");
//        
//        response=services.updateDeviceNQRv2(diy, body, headers);
//    
//        Assert.assertEquals(response.getStatusCode(), 400);        
//	}
//	
//	@Test()
//	public void verify_Error_Code_In_Case_Of_Payment_QR_Is_Not_Passed() {
//		
//		headers=new HashMap<>();
//		body=new HashMap<>();
//		services=new MiddlewareServices();
//		diy = new DIYV2SBUpdateDeviceAndQR();
//		
//		headers.put("custid", "123");
//		headers.put("Authorization", jwt);
//		headers.put("version", "5.1.6");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        
//        
//        body.put("leadId","c885d04a-82be-404b-9408-37a2c8dfc469");
//        body.put("action","UPDATE_DEVICE_DETAILS");
//        body.put("deviceId","563747466004");
//        body.put("simDetails","8991102105555713649U");
//        body.put("qrString","");
//        
//        response=services.updateDeviceNQRv2(diy, body, headers);
//    
//        Assert.assertEquals(response.getStatusCode(), 400);        
//	}
//	
//	@Test()
//	public void verify_Error_Code_In_Case_Of_Invalid_Payment_QR_Is_Passed() {
//		
//		headers=new HashMap<>();
//		body=new HashMap<>();
//		services=new MiddlewareServices();
//		diy = new DIYV2SBUpdateDeviceAndQR();
//		
//		headers.put("custid", "123");
//		headers.put("Authorization", jwt);
//		headers.put("version", "5.1.6");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        
//        
//        body.put("leadId","c885d04a-82be-404b-9408-37a2c8dfc469");
//        body.put("action","UPDATE_DEVICE_DETAILS");
//        body.put("deviceId","563747466004");
//        body.put("simDetails","8991102105555713649U");
//        body.put("qrString","Xyz123");
//        
//        response=services.updateDeviceNQRv2(diy, body, headers);
//    
//        Assert.assertEquals(response.getStatusCode(), 400);        
//	}
//	
//	@Test()
//	public void verify_Error_Code_In_Case_Of_Already_Mapped_Payment_QR_Is_Passed() {
//		
//		headers=new HashMap<>();
//		body=new HashMap<>();
//		services=new MiddlewareServices();
//		diy = new DIYV2SBUpdateDeviceAndQR();
//		
//		headers.put("custid", "123");
//		headers.put("Authorization", jwt);
//		headers.put("version", "5.1.6");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        
//        
//        body.put("leadId","c885d04a-82be-404b-9408-37a2c8dfc469");
//        body.put("action","UPDATE_DEVICE_DETAILS");
//        body.put("deviceId","563747466004");
//        body.put("simDetails","8991102105555713649U");
//        body.put("qrString","10NLNJ");
//        
//        response=services.updateDeviceNQRv2(diy, body, headers);
//    
//        Assert.assertEquals(response.getStatusCode(), 400);        
//	}
}