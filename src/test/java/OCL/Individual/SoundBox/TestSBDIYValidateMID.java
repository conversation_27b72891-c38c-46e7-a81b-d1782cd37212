package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.SBDIYValidateMID;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestSBDIYValidateMID  extends BaseMethod{

	SBDIYValidateMID mid;
	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> params;

	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

/*	@Test
	public void verify_Status_Code_When_MID_Is_Validated_Successfully() {
		mid=new SBDIYValidateMID();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadId", "bcaeb468-b931-4aef-96a0-d01d5bd9b2d3");
        
        response=services.SBDIYMidValidation(mid, params, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 200);
	} */
	// SB DIY Is is in off mode now so this code is redundant 
	
	@Test
	public void verify_Status_Code_When_MID_Is_Not_Validated_Successfully() {
		mid=new SBDIYValidateMID();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadId", "86Rtcf4a-9c7a-4f1b-9617-be176d15bfb9");
        
        response=services.SBDIYMidValidation(mid, params, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 400);
	}

	
	@Test
	public void verify_Status_Message_When_Lead_ID_Not_Valid() {
		mid=new SBDIYValidateMID();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadId", "86Rtcf4a-9c7a-4f1b-9617-be176d15bfb9");
        
        response=services.SBDIYMidValidation(mid, params, headers);	
       
        Assert.assertEquals(response.path("displayMessage"), "Invalid LeadId");
	}
	
	@Test
	public void verify_Error_Code_When_Lead_ID_Is_Missing() {
		mid=new SBDIYValidateMID();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadId", "");
        
        response=services.SBDIYMidValidation(mid, params, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test
	public void verify_RefID_IS_Present_In_Case_Of_Failure() {
		mid=new SBDIYValidateMID();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadId", "");
        
        response=services.SBDIYMidValidation(mid, params, headers);	
       
        Assert.assertTrue(!response.path("refId").toString().isEmpty());
        
  	}
	
	@Test
	public void verify_RefID_IS_Present_In_Case_Of_Success() {
		mid=new SBDIYValidateMID();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadId", "bcaeb468-b931-4aef-96a0-d01d5bd9b2d3");
        
        response=services.SBDIYMidValidation(mid, params, headers);	
       
        Assert.assertTrue(!response.path("refId").toString().isEmpty());
       
	}
	
/*	@Test
	public void verify_Status_When_Mid_Is_Valid() {
		mid=new SBDIYValidateMID();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadId", "bcaeb468-b931-4aef-96a0-d01d5bd9b2d3");
        
        response=services.SBDIYMidValidation(mid, params, headers);	
       
        Assert.assertTrue(response.path("midValid"));
       
	} */
	// SB DIY Is is in off mode now so this code is redundant 
	@Test
	public void verify_Status_When_Mid_Is_InValid() {
		mid=new SBDIYValidateMID();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadId", "55cb360a-9a5b-462e-9768-1b42bc61f6c0");
        
        response=services.SBDIYMidValidation(mid, params, headers);	
       
        Assert.assertFalse(!(response.getStatusCode()==400));
       
	}
	
/*	@Test
	public void verify_Status_Code_When_Device_Is_Already_Mapped_To_MID() {
		mid=new SBDIYValidateMID();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadId", "bcaeb468-b931-4aef-96a0-d01d5bd9b2d3");
        
        response=services.SBDIYMidValidation(mid, params, headers);	
       
        Assert.assertTrue(response.path("sbAlreadyMapped"));
	}
	*/ 
	// SB DIY Is is in off mode now so this code is redundant 
	@Test
	public void verify_Status_Code_When_Device_Is_Npt_Mapped_To_MID() {
		mid=new SBDIYValidateMID();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadId", "8b9ecf4a-9c7a-4f1b-9617-be176d15bfb0");
        
        response=services.SBDIYMidValidation(mid, params, headers);	
       
        Assert.assertFalse(!(response.getStatusCode()==400));
	}

	@Test
	public void verify_Status_Message_When_MID_Is_Active() {
		mid=new SBDIYValidateMID();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadId", "bcaeb468-b931-4aef-96a0-d01d5bd9b2d3");
        
        response=services.SBDIYMidValidation(mid, params, headers);	
       
        Assert.assertFalse(!(response.getStatusCode()==400));
       }


	@Test
	public void verify_Status_Message_When_MID_Is_InActive() {
		mid=new SBDIYValidateMID();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadId", "38e17075-67b0-4128-8f04-0352af48281f");
        
        response=services.SBDIYMidValidation(mid, params, headers);	
       
        Assert.assertFalse(!(response.getStatusCode()==400));
	}
	
	

}