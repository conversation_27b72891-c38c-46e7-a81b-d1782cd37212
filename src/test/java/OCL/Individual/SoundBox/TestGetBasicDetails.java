package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.GetMerchantBasicDetails;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestGetBasicDetails extends BaseMethod {
    
    public String sessionToken;
    public Response response;
    public MiddlewareServices services;
    public Map<String, String> headers;
    public Map<String, String> queryParams;
    public GetMerchantBasicDetails basicDetails;

    @BeforeMethod
    public void setToken() {
        sessionToken = AgentSessionToken("8010630022", "paytm@123");
        try {
            establishConnectiontoServer(sessionToken, 5);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void verify_GetBasicDetails_Success_Response() {
        headers = new HashMap<>();
        queryParams = new HashMap<>();
        services = new MiddlewareServices();
        basicDetails = new GetMerchantBasicDetails();

        // Setting headers based on the curl request
        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("osversion", "13");
        headers.put("x-mw-url-checksum", "U0Nc5c2+d/sViyaoJikuQnmdPwCqrX/M0o5AsEUdP4ZQ759YNYr8KP9HBxacuSxGXQzFiYkwdh671xLRVdTIECJwSIPJ4QfzQskEEb6HurNL5dcVJQl+h2fXUmrl1YHH");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        // Setting query parameters
        queryParams.put("mid", "OHDpzh00093359209989");
        queryParams.put("action", "sb_eligibility");

        response = services.getBasicDetails(basicDetails, headers, queryParams);
        
        System.out.println("Response: " + response.getBody().asString());
        
        // Only checking status code and reKycRequired
        Assert.assertEquals(response.getStatusCode(), 200);
        Assert.assertEquals(response.jsonPath().getBoolean("reKycRequired"), true);
    }

    @Test
    public void verify_GetBasicDetails_Missing_Action_Parameter() {
        headers = new HashMap<>();
        queryParams = new HashMap<>();
        services = new MiddlewareServices();
        basicDetails = new GetMerchantBasicDetails();

        // Setting headers
        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("osversion", "13");
        headers.put("x-mw-url-checksum", "U0Nc5c2+d/sViyaoJikuQnmdPwCqrX/M0o5AsEUdP4ZQ759YNYr8KP9HBxacuSxGXQzFiYkwdh671xLRVdTIECJwSIPJ4QfzQskEEb6HurNL5dcVJQl+h2fXUmrl1YHH");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        // Only setting MID, omitting action parameter
        queryParams.put("mid", "DHVRVL02894215991065");

        response = services.getBasicDetails(basicDetails, headers, queryParams);
        
        System.out.println("Response without action: " + response.getBody().asString());
        
        // Only checking status code
        Assert.assertNotEquals(response.getStatusCode(), 200);
    }

    @Test
    public void verify_GetBasicDetails_Both_Verified() {
        headers = new HashMap<>();
        queryParams = new HashMap<>();
        services = new MiddlewareServices();
        basicDetails = new GetMerchantBasicDetails();

        // Setting headers (same as previous tests)
        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        // ... existing headers ...

        // Setting query parameters for both verified case
        queryParams.put("mid", "OHDpzh00093359209989");
        queryParams.put("action", "sb_eligibility");

        response = services.getBasicDetails(basicDetails, headers, queryParams);
        
        System.out.println("Response (Both Verified): " + response.getBody().asString());
        
        Assert.assertEquals(response.getStatusCode(), 200);
        Assert.assertEquals(response.jsonPath().getBoolean("reKycRequired"), false);
    }

    @Test
    public void verify_GetBasicDetails_None_Verified() {
        // Setup same as above
        headers = new HashMap<>();
        queryParams = new HashMap<>();
        services = new MiddlewareServices();
        basicDetails = new GetMerchantBasicDetails();

        // Set headers
        headers.put("session_token", sessionToken);
        // ... existing headers ...

        // Setting query parameters for none verified case
        queryParams.put("mid", "DHVRVL02894215991065");
        queryParams.put("action", "sb_eligibility");

        response = services.getBasicDetails(basicDetails, headers, queryParams);
        
        System.out.println("Response (None Verified): " + response.getBody().asString());
        
        Assert.assertEquals(response.getStatusCode(), 200);
        Assert.assertEquals(response.jsonPath().getBoolean("reKycRequired"), true);
    }

    @Test
    public void verify_GetBasicDetails_Only_Pan_Verified() {
        // Setup same as above
        headers = new HashMap<>();
        queryParams = new HashMap<>();
        services = new MiddlewareServices();
        basicDetails = new GetMerchantBasicDetails();

        // Set headers
        headers.put("session_token", sessionToken);
        // ... existing headers ...

        // Setting query parameters for PAN verified case
        queryParams.put("mid", "EPepKo53809325810966");
        queryParams.put("action", "sb_eligibility");

        response = services.getBasicDetails(basicDetails, headers, queryParams);
        
        System.out.println("Response (PAN Verified): " + response.getBody().asString());
        
        Assert.assertEquals(response.getStatusCode(), 200);
        Assert.assertEquals(response.jsonPath().getBoolean("reKycRequired"), false);
    }

    @Test
    public void verify_GetBasicDetails_Only_Aadhar_Verified() {
        // Setup same as above
        headers = new HashMap<>();
        queryParams = new HashMap<>();
        services = new MiddlewareServices();
        basicDetails = new GetMerchantBasicDetails();

        // Set headers
        headers.put("session_token", sessionToken);
        // ... existing headers ...

        // Setting query parameters for Aadhar verified case
        queryParams.put("mid", "mhdUSq78940337659669");
        queryParams.put("action", "sb_eligibility");

        response = services.getBasicDetails(basicDetails, headers, queryParams);
        
        System.out.println("Response (Aadhar Verified): " + response.getBody().asString());
        
        Assert.assertEquals(response.getStatusCode(), 200);
        Assert.assertEquals(response.jsonPath().getBoolean("reKycRequired"), false);
    }

    @Test
    public void verify_GetBasicDetails_Failure_Response_Invalid_Session_Token() {
        headers = new HashMap<>();
        queryParams = new HashMap<>();
        services = new MiddlewareServices();
        basicDetails = new GetMerchantBasicDetails();

        // Using invalid session token
        headers.put("session_token", "invalid_token_12345");
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("osversion", "13");
        headers.put("x-mw-url-checksum", "U0Nc5c2+d/sViyaoJikuQnmdPwCqrX/M0o5AsEUdP4ZQ759YNYr8KP9HBxacuSxGXQzFiYkwdh671xLRVdTIECJwSIPJ4QfzQskEEb6HurNL5dcVJQl+h2fXUmrl1YHH");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        queryParams.put("mid", "OHDpzh00093359209989");
        queryParams.put("action", "sb_eligibility");

        response = services.getBasicDetails(basicDetails, headers, queryParams);
        Assert.assertEquals(response.getStatusCode(), 410);
    }

    @Test
    public void verify_GetBasicDetails_Success_Response_With_Different_App_Version() {
        headers = new HashMap<>();
        queryParams = new HashMap<>();
        services = new MiddlewareServices();
        basicDetails = new GetMerchantBasicDetails();

        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.0.5");  // Different app version
        // ... rest of the headers ...
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        queryParams.put("mid", "OHDpzh00093359209989");
        queryParams.put("action", "sb_eligibility");

        response = services.getBasicDetails(basicDetails, headers, queryParams);
        Assert.assertEquals(response.getStatusCode(), 200);
        Assert.assertEquals(response.jsonPath().getBoolean("reKycRequired"), true);
    }

    @Test
    public void verify_GetBasicDetails_Failure_Response_Missing_Required_Headers() {
        headers = new HashMap<>();
        queryParams = new HashMap<>();
        services = new MiddlewareServices();
        basicDetails = new GetMerchantBasicDetails();

        // Only including minimal required headers, missing version header
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "**************");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        queryParams.put("mid", "OHDpzh00093359209989");
        queryParams.put("action", "sb_eligibility");

        response = services.getBasicDetails(basicDetails, headers, queryParams);
        Assert.assertEquals(response.getStatusCode(), 200);
        Assert.assertTrue(response.getBody().asString().contains("version is empty in header"));
        Assert.assertTrue(response.getBody().asString().contains("VERSION_FAILURE"));
    }

    @Test
    public void verify_GetBasicDetails_Failure_Response_Missing_Mid() {
        headers = new HashMap<>();
        queryParams = new HashMap<>();
        services = new MiddlewareServices();
        basicDetails = new GetMerchantBasicDetails();

        // Setting all required headers
        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        // ... rest of the headers ...
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        // Only setting action, omitting mid
        queryParams.put("action", "sb_eligibility");

        response = services.getBasicDetails(basicDetails, headers, queryParams);
        Assert.assertEquals(response.getStatusCode(), 400);
    }

    @Test
    public void verify_GetBasicDetails_Failure_Response_Invalid_Mid_Format() {
        headers = new HashMap<>();
        queryParams = new HashMap<>();
        services = new MiddlewareServices();
        basicDetails = new GetMerchantBasicDetails();

        // Setting all required headers
        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        // ... rest of the headers ...
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        // Setting invalid mid format
        queryParams.put("mid", "mhdUSq789 40337659669");
        queryParams.put("action", "sb_eligibility");

        response = services.getBasicDetails(basicDetails, headers, queryParams);
        Assert.assertEquals(response.getStatusCode(), 400);
    }

    @Test
    public void verify_GetBasicDetails_Failure_Response_Invalid_Device_Identifier() {
        headers = new HashMap<>();
        queryParams = new HashMap<>();
        services = new MiddlewareServices();
        basicDetails = new GetMerchantBasicDetails();

        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "invalid-device-id");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("osversion", "13");
        headers.put("x-mw-url-checksum", "U0Nc5c2+d/sViyaoJikuQnmdPwCqrX/M0o5AsEUdP4ZQ759YNYr8KP9HBxacuSxGXQzFiYkwdh671xLRVdTIECJwSIPJ4QfzQskEEb6HurNL5dcVJQl+h2fXUmrl1YHH");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        queryParams.put("mid", "OHDpzh00093359209989");
        queryParams.put("action", "sb_eligibility");

        response = services.getBasicDetails(basicDetails, headers, queryParams);
        Assert.assertEquals(response.getStatusCode(), 410);
    }

    @Test
    public void verify_GetBasicDetails_Success_Response_Invalid_Checksum() {
        headers = new HashMap<>();
        queryParams = new HashMap<>();
        services = new MiddlewareServices();
        basicDetails = new GetMerchantBasicDetails();

        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("osversion", "13");
        headers.put("x-mw-url-checksum", "invalid_checksum");  // Invalid checksum
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        queryParams.put("mid", "OHDpzh00093359209989");
        queryParams.put("action", "sb_eligibility");

        response = services.getBasicDetails(basicDetails, headers, queryParams);
        Assert.assertEquals(response.getStatusCode(), 200);
        // Additional assertion to verify the response contains reKycRequired field
        Assert.assertTrue(response.jsonPath().getBoolean("reKycRequired"));
    }

    @Test
    public void verify_GetBasicDetails_ReKycReason_Not_Present_With_Empty_Action() {
        headers = new HashMap<>();
        queryParams = new HashMap<>();
        services = new MiddlewareServices();
        basicDetails = new GetMerchantBasicDetails();

        // Setting headers
        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("osversion", "13");
        headers.put("x-mw-url-checksum", "U0Nc5c2+d/sViyaoJikuQnmdPwCqrX/M0o5AsEUdP4ZQ759YNYr8KP9HBxacuSxGXQzFiYkwdh671xLRVdTIECJwSIPJ4QfzQskEEb6HurNL5dcVJQl+h2fXUmrl1YHH");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        // First API call with correct action
        queryParams.put("mid", "DHVRVL02894215991065");
        queryParams.put("action", "sb_eligibility");

        Response responseWithAction = services.getBasicDetails(basicDetails, headers, queryParams);
        System.out.println("Response with correct action: " + responseWithAction.getBody().asString());
        
        // Verify first response
        Assert.assertEquals(responseWithAction.getStatusCode(), 200);
        Assert.assertEquals(responseWithAction.jsonPath().getBoolean("reKycRequired"), true);
        Assert.assertTrue(responseWithAction.getBody().asString().contains("reKycReason"));

        // Second API call with empty action
        queryParams.put("action", "");

        Response responseWithEmptyAction = services.getBasicDetails(basicDetails, headers, queryParams);
        System.out.println("Response with empty action: " + responseWithEmptyAction.getBody().asString());
        
        // Verify second response
        Assert.assertEquals(responseWithEmptyAction.getStatusCode(), 200);
        Assert.assertEquals(responseWithEmptyAction.jsonPath().getBoolean("reKycRequired"), false);
        Assert.assertFalse(responseWithEmptyAction.getBody().asString().contains("reKycReason"));

        // Compare both responses
        Assert.assertNotEquals(
            responseWithAction.jsonPath().getBoolean("reKycRequired"),
            responseWithEmptyAction.jsonPath().getBoolean("reKycRequired"),
            "reKycRequired should be different for both calls"
        );
    }

    @Test
    public void verify_GetBasicDetails_ReKycReason_Not_Present_With_Invalid_Action() {
        headers = new HashMap<>();
        queryParams = new HashMap<>();
        services = new MiddlewareServices();
        basicDetails = new GetMerchantBasicDetails();

        // Setting headers
        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("version", "7.3.4");
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("x-src", "GGClient");
        headers.put("client", "androidapp");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "**************");
        headers.put("isbusyboxfound", "false");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("osversion", "13");
        headers.put("x-mw-url-checksum", "U0Nc5c2+d/sViyaoJikuQnmdPwCqrX/M0o5AsEUdP4ZQ759YNYr8KP9HBxacuSxGXQzFiYkwdh671xLRVdTIECJwSIPJ4QfzQskEEb6HurNL5dcVJQl+h2fXUmrl1YHH");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        // First API call with correct action
        queryParams.put("mid", "DHVRVL02894215991065");
        queryParams.put("action", "sb_eligibility");

        Response responseWithCorrectAction = services.getBasicDetails(basicDetails, headers, queryParams);
        System.out.println("Response with correct action: " + responseWithCorrectAction.getBody().asString());
        
        // Verify first response
        Assert.assertEquals(responseWithCorrectAction.getStatusCode(), 200);
        Assert.assertEquals(responseWithCorrectAction.jsonPath().getBoolean("reKycRequired"), true);
        Assert.assertTrue(responseWithCorrectAction.getBody().asString().contains("reKycReason"));

        // Second API call with invalid action
        queryParams.put("action", "invalid_action");

        Response responseWithInvalidAction = services.getBasicDetails(basicDetails, headers, queryParams);
        System.out.println("Response with invalid action: " + responseWithInvalidAction.getBody().asString());
        
        // Verify second response
        Assert.assertEquals(responseWithInvalidAction.getStatusCode(), 200);
        Assert.assertEquals(responseWithInvalidAction.jsonPath().getBoolean("reKycRequired"), false);
        Assert.assertFalse(responseWithInvalidAction.getBody().asString().contains("reKycReason"));

        // Compare both responses
        Assert.assertNotEquals(
            responseWithCorrectAction.jsonPath().getBoolean("reKycRequired"),
            responseWithInvalidAction.jsonPath().getBoolean("reKycRequired"),
            "reKycRequired should be different for both calls"
        );
    }
}
