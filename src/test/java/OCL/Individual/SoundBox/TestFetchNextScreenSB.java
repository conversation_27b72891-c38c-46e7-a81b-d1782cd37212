package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.FetchNextScreenSB;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestFetchNextScreenSB extends BaseMethod{
	
	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> params;
	FetchNextScreenSB screen;
	
	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("8010630022", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {

			e.printStackTrace();
		}
	}

	@Test
	public void TC01_verifyNextScreenForSBWhenPreviousScreenIsDEVICE_SCANNER_SCREEN_In_Bind_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		params.put("previousScreen", "DEVICE_SCANNER_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("solutionSubType", "sound_box_bind");
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "MID_SCREEN");
		Assert.assertEquals(response.statusCode(), 200);
	
	}
	
	@Test
	public void TC02_verifyNextScreenForSBWhenPreviousScreenIs_MID_SCREEN_In_Bind_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		params.put("previousScreen", "MID_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("solutionSubType", "sound_box_bind");
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "QR_SCAN_POS_SCREEN");
		Assert.assertEquals(response.statusCode(), 200);
		
		
	}
	
	
	@Test
	public void TC03_verifyNextScreenForSBWhenPreviousScreenIs_QR_SCAN_POS_SCREEN_In_Bind_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		params.put("solutionSubType", "sound_box_bind");
		params.put("previousScreen", "QR_SCAN_POS_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("leadId", "eb62cac5-45ee-40d3-a63c-ce471affb781");
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.statusCode(), 200);
			
	}
	
	@Test
	public void TC04_verifyNextScreenForSBWhenPreviousScreenIs_ADDRESS_SCREEN_In_Bind_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		params.put("solutionSubType", "sound_box_bind");
		params.put("previousScreen", "ADDRESS_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("leadId", "eb62cac5-45ee-40d3-a63c-ce471affb781");
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		
		Assert.assertEquals(response.path("nextScreen").toString(), "PLAN_SELECTION_SCREEN_NEW");

		Assert.assertEquals(response.statusCode(), 200);
			
	}

	@Test
	public void TC05_verifyNextScreenForSBWhenPreviousScreenIs_PLAN_SELECTION_SCREEN_NEW_In_Bind_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		params.put("solutionSubType", "sound_box_bind");
		params.put("previousScreen", "PLAN_SELECTION_SCREEN_NEW");
		params.put("solutionType", "sound_box");
		params.put("leadId", "eb62cac5-45ee-40d3-a63c-ce471affb781");
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "TNC_SCREEN");
		Assert.assertEquals(response.statusCode(), 200);
			
	}
	
	
	
	@Test
	public void TC06_verifyNextScreenForSBWhenPreviousScreenIs_TNC_SCREEN_In_Bind_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		params.put("solutionSubType", "sound_box_bind");
		params.put("previousScreen", "TNC_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("leadId", "eb62cac5-45ee-40d3-a63c-ce471affb781");
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "PAYMENT_SCREEN");
		Assert.assertEquals(response.statusCode(), 200);
			
	}
	
	@Test
	public void TC07_verifyNextScreenForSBWhenPreviousScreenIs_PAYMENT_SCREEN__In_Bind_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		params.put("solutionSubType", "sound_box_bind");
		params.put("previousScreen", "PAYMENT_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("leadId", "eb62cac5-45ee-40d3-a63c-ce471affb781");
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "UPI_MANDATE_SCREEN");
		Assert.assertEquals(response.statusCode(), 200);
			
	}
	
	@Test
	public void TC08_verifyNextScreenForSBWhenPreviousScreenIs_UPI_MANDATE_SCREEN__In_Bind_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		params.put("solutionSubType", "sound_box_bind");
		params.put("previousScreen", "UPI_MANDATE_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("leadId", "eb62cac5-45ee-40d3-a63c-ce471affb781");
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "GUIDELINES_SCREEN");
		Assert.assertEquals(response.statusCode(), 200);
			
	}
	
	@Test
	public void TC09_verifyNextScreenForSBWhenPreviousScreenIs_GUIDELINES_SCREEN_In_Bind_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		params.put("solutionSubType", "sound_box_bind");
		params.put("previousScreen", "GUIDELINES_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("leadId", "eb62cac5-45ee-40d3-a63c-ce471affb781");
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "CHARGER_SCAN_SCREEN");
		Assert.assertEquals(response.statusCode(), 200);
			
	}
	
	@Test
	public void TC10_verifyNextScreenForSBWhenPreviousScreenIs_CHARGER_SCAN_SCREEN_In_Bind_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		params.put("solutionSubType", "sound_box_bind");
		params.put("previousScreen", "CHARGER_SCAN_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("leadId", "eb62cac5-45ee-40d3-a63c-ce471affb781");
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "SIM_SCAN_SCREEN");
		Assert.assertEquals(response.statusCode(), 200);
			
	}
	
	@Test
	public void TC11_verifyNextScreenForSBWhenPreviousScreenIs_SIM_SCAN_SCREEN_In_Bind_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		params.put("solutionSubType", "sound_box_bind");
		params.put("previousScreen", "SIM_SCAN_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("leadId", "eb62cac5-45ee-40d3-a63c-ce471affb781");
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "MAPPING_PWD_SCREEN");
		Assert.assertEquals(response.statusCode(), 200);
			
	}
	
	@Test
	public void TC12_verifyNextScreenForSBWhenPreviousScreenIs_MAPPING_PWD_SCREEN_In_Bind_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		params.put("solutionSubType", "sound_box_bind");
		params.put("previousScreen", "MAPPING_PWD_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("leadId", "eb62cac5-45ee-40d3-a63c-ce471affb781");
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "POLLING_SCREEN");
		Assert.assertEquals(response.statusCode(), 200);
			
	}
	
	@Test
	public void TC13_verifyNextScreenForSBWhenPreviousScreenIs_MID_SCREEN_In_Sb_Replacement_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
		params.put("previousScreen", "MID_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("solutionSubType", "sound_box_replacement");
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "REPLACEMENT_REASON_SCREEN");
		Assert.assertEquals(response.statusCode(), 200);		
		
	}

	@Test
	public void TC14_verifyNextScreenForSBWhenPreviousScreenIs_REPLACEMENT_REASON_SCREEN_In_Sb_Replacement_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
		params.put("previousScreen", "REPLACEMENT_REASON_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("solutionSubType", "sound_box_replacement");
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "DEVICE_ID_SELECTION_SCREEN");
		Assert.assertEquals(response.statusCode(), 200);		
		
	}
	
	@Test
	public void TC15_verifyNextScreenForSBWhenPreviousScreenIs_DEVICE_ID_SELECTION_SCREEN_In_Sb_Replacement_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
		params.put("previousScreen", "DEVICE_ID_SELECTION_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("solutionSubType", "sound_box_replacement");
		params.put("leadId", "2918e69d-c300-4170-a5d2-72a9bca67a10"); 		
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "QR_SCAN_POS_SCREEN");
		Assert.assertEquals(response.statusCode(), 200);		
		
	}
	
	@Test
	public void TC16_verifyNextScreenForSBWhenPreviousScreenIs_QR_SCAN_POS_SCREEN_In_Sb_Replacement_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
		params.put("previousScreen", "QR_SCAN_POS_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("solutionSubType", "sound_box_replacement");
		params.put("leadId", "2918e69d-c300-4170-a5d2-72a9bca67a10"); 		
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "SERIALISE_OLD_DEVICE_SCANNER");
		Assert.assertEquals(response.statusCode(), 200);		
		
	}

	@Test
	public void TC17_verifyNextScreenForSBWhenPreviousScreenIs_SERIALISE_OLD_DEVICE_SCANNER_In_Sb_Replacement_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
		params.put("previousScreen", "SERIALISE_OLD_DEVICE_SCANNER");
		params.put("solutionType", "sound_box");
		params.put("solutionSubType", "sound_box_replacement");
		params.put("leadId", "2918e69d-c300-4170-a5d2-72a9bca67a10"); 		
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "DEVICE_IMAGE_CAPTURE_SCREEN");
		Assert.assertEquals(response.statusCode(), 200);		
		
	}
	
	@Test
	public void TC18_verifyNextScreenForSBWhenPreviousScreenIs_DEVICE_IMAGE_CAPTURE_SCREEN_In_Sb_Replacement_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
		params.put("previousScreen", "DEVICE_IMAGE_CAPTURE_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("solutionSubType", "sound_box_replacement");
		params.put("leadId", "2918e69d-c300-4170-a5d2-72a9bca67a10"); 		
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "DEVICE_SCANNER_SCREEN");
		Assert.assertEquals(response.statusCode(), 200);		
		
	}
	
	@Test
	public void TC19_verifyNextScreenForSBWhenPreviousScreenIs_DEVICE_SCANNER_SCREEN_In_Sb_Replacement_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
		params.put("previousScreen", "DEVICE_SCANNER_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("solutionSubType", "sound_box_replacement");
		params.put("leadId", "2918e69d-c300-4170-a5d2-72a9bca67a10"); 		
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "PLAN_SELECTION_SCREEN_NEW");
		Assert.assertEquals(response.statusCode(), 200);		
		
	}

	@Test
	public void TC20_verifyNextScreenForSBWhenPreviousScreenIs_PLAN_SELECTION_SCREEN_In_Sb_Replacement_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
		params.put("previousScreen", "PLAN_SELECTION_SCREEN_NEW");
		params.put("solutionType", "sound_box");
		params.put("solutionSubType", "sound_box_replacement");
		params.put("leadId", "2918e69d-c300-4170-a5d2-72a9bca67a10"); 		
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "ADDRESS_SCREEN");

		Assert.assertEquals(response.statusCode(), 200);		
		
	}

	@Test
	public void TC21_verifyNextScreenForSBWhenPreviousScreenIs_ADDRESS_SCREEN_In_Sb_Replacement_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
		params.put("previousScreen", "ADDRESS_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("solutionSubType", "sound_box_replacement");
		params.put("leadId", "2918e69d-c300-4170-a5d2-72a9bca67a10"); 		
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "TNC_SCREEN");

		Assert.assertEquals(response.statusCode(), 200);		
		
	}
	
	@Test
	public void TC22_verifyNextScreenForSBWhenPreviousScreenIs_TNC_SCREEN_In_Sb_Replacement_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
		params.put("previousScreen", "TNC_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("solutionSubType", "sound_box_replacement");
		params.put("leadId", "2918e69d-c300-4170-a5d2-72a9bca67a10"); 		
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "PAYMENT_SCREEN");

		Assert.assertEquals(response.statusCode(), 200);		
		
	}
	
	@Test
	public void TC23_verifyNextScreenForSBWhenPreviousScreenIs_PAYMENT_SCREEN_In_Sb_Replacement_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
		params.put("previousScreen", "PAYMENT_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("solutionSubType", "sound_box_replacement");
		params.put("leadId", "2918e69d-c300-4170-a5d2-72a9bca67a10"); 		
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "UPI_MANDATE_SCREEN");

		Assert.assertEquals(response.statusCode(), 200);		
		
	}
	
	
	@Test
	public void TC23_verifyNextScreenForSBWhenPreviousScreenIs_UPI_MANDATE_SCREEN_In_Sb_Replacement_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
		params.put("previousScreen", "UPI_MANDATE_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("solutionSubType", "sound_box_replacement");
		params.put("leadId", "2918e69d-c300-4170-a5d2-72a9bca67a10"); 		
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "SIM_SCAN_SCREEN");

		Assert.assertEquals(response.statusCode(), 200);		
		
	}

	@Test
	public void TC23_verifyNextScreenForSBWhenPreviousScreenIs_SIM_SCAN_SCREEN_In_Sb_Replacement_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
		params.put("previousScreen", "SIM_SCAN_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("solutionSubType", "sound_box_replacement");
		params.put("leadId", "2918e69d-c300-4170-a5d2-72a9bca67a10"); 		
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "MAPPING_PWD_SCREEN");

		Assert.assertEquals(response.statusCode(), 200);		
		
	}
	
	@Test
	public void TC23_verifyNextScreenForSBWhenPreviousScreenIs_MAPPING_PWD_SCREEN_In_Sb_Replacement_Flow() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging5.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
		params.put("previousScreen", "MAPPING_PWD_SCREEN");
		params.put("solutionType", "sound_box");
		params.put("solutionSubType", "sound_box_replacement");
		params.put("leadId", "2918e69d-c300-4170-a5d2-72a9bca67a10"); 		
		
		screen=new FetchNextScreenSB();
		services=new MiddlewareServices();
		response=services.fetchNextScreen(screen, headers, params);
		Assert.assertEquals(response.path("nextScreen").toString(), "REPLACEMENT_POLLING_SCREEN");

		Assert.assertEquals(response.statusCode(), 200);		
		
	}





}
