package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.CardSB_Create_Lead_And_Check_Existing_Subscription;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class Test_CardSB_Create_Lead_And_Check_Existing_Subscription extends BaseMethod {

	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> body;
	
	CardSB_Create_Lead_And_Check_Existing_Subscription lead;
	
	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Success_When_Merchant_Has_Not_Applied_For_VAS() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123207");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 200);
	}
	

	@Test()
	public void verify_Existing_Lead_Is_Continued_If_Mid_And_DeviceID_Remains_Same() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123207");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        String lead1=response.path("leadId").toString();

        body=new HashMap<String, String>();
        lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123207");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");
        response=services.v1VasCreateLead(lead, body, headers);
        String lead2=response.path("leadId").toString();
        Assert.assertEquals(lead1, lead2);
	}
	
	@Test()
	public void verify_Existing_Lead_Is_Continued_If_DeviceID_Is_Different() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123207");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        String lead1=response.path("leadId").toString();

        body=new HashMap<String, String>();
        lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123219");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");
        response=services.v1VasCreateLead(lead, body, headers);
        String lead2=response.path("leadId").toString();
        Assert.assertNotEquals(lead1, lead2);
	}
	
	@Test()
	public void verify_Lead_Created_Successfully_In_Case_If_Device_Has_No_Existing_VAS_Subscription() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123218");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertFalse(response.path("leadId").toString().isEmpty());
	}
	
	@Test()
	public void verify_Status_Code_And_Error_Message_In_Case_If_Device_Has__Existing_Active_VAS_Subscription() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123205");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertEquals(response.statusCode(), 200);
	}
	
	@Test()
	public void verify_Status_Code_And_Error_Message_In_Case_If_Device_Has__Existing_Suspended_VAS_Subscription() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123210");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertTrue(response.path("displayMessage").toString().contains("Merchant already enrolled for Premium Care. Please choose another Add-on to proceed."));
        Assert.assertEquals(response.statusCode(), 400);
	}
	
	@Test()
	public void verify_Lead_Created_Successfully_In_Case_If_Device_Has__Existing_Inactive_VAS_Subscription() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123202");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 200);
	}
	
	@Test()
	public void verify_Error_Code_When_VAS_Is_Not_Whitelisted_At_OE() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "test");
        body.put("deviceId", "385748394123200");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 500);
	}
	
	@Test()
	public void verify_Error_Code_When_MID_Is_Online_MID() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123200");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "5536283009");
        body.put("mid", "JFTbRB71149895485999");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test()
	public void verify_Error_Code_When_MID_Is_Inactive_MID() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123200");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "5536283009");
        body.put("mid", "fZmbCb39563729871672");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test()
	public void verify_Error_Code_When_Device_ID_Is_Not_WhiteListed_At_IOT() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123199");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	
	@Test()
	public void verify_Error_Code_When_Device_Type_Is_Not_WhiteListed() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "863005063745054");
        body.put("deviceType", "SOUNDBOX_8");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test()
	public void verify_Error_Code_When_User_MID_Is_Not_Passed_In_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "563747466008");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");

        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test()
	public void verify_Status_Code_In_Case_UserCustID_And_Mobile_Is_Missing_From_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123207");
        body.put("deviceType", "SOUNDBOX_4G");
    
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
   
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Agent_CustID_Is_Not_Passed_In_Request_When_Agent_CustID_Not_Recieved_From_FSM() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123207");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
   
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test()
	public void verify_Status_Code_In_Case_Entity_Type_Is_Missing_From_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123207");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
 
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test()
	public void verify_Status_Code_In_Solution_Sub_Type_Is_Not_Passed_In_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123207");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");


        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Auth_Token_Is_Missing_From_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
//		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123207");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 401);
	}
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Version_Is_Not_Passed_In_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		lead = new CardSB_Create_Lead_And_Check_Existing_Subscription();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
//		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("solutionTypeLevel3", "premium_care");
        body.put("deviceId", "385748394123207");
        body.put("deviceType", "SOUNDBOX_4G");
        body.put("userMobile", "7771110999");
        body.put("mid", "HySHnd27878673398759");
        body.put("merchantName", "Anmol jain");
        body.put("userCustId", "1001788031");
        body.put("agentCustId", "1107195733");
        body.put("entityType", "INDIVIDUAL");
        body.put("solutionSubType", "sound_box_vas");

        response=services.v1VasCreateLead(lead, body, headers);
        Assert.assertEquals(response.path("errorCode").toString(), "VERSION_FAILURE");
	}
	
	
}