package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.SBValidateNewChargerInBindFlow;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestSBValidateNewChargerInBindFlow extends BaseMethod {

	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> body;

	SBValidateNewChargerInBindFlow validate;
	
	
	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	
	@Test()
	public void verify_Status_Code_In_Case_Of_Success_When_Scanned_Asset_Is_A_Valid_Charger() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		validate = new SBValidateNewChargerInBindFlow();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        
        body.put("deviceId","CHARGER3");
        body.put("agentCustId","1001647902");
        body.put("leadId","816d0855-cfb9-419d-b584-e17ced588ad4");
        body.put("scannedBarcodeType","NEW_CHARGER");
        
        
        response=services.v1ValidateCharger(validate, body, headers);
    
        Assert.assertEquals(response.getStatusCode(), 200);
       
        
	}
	
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Failure_When_Scanned_Asset_Is_Soundbox_And_Not_Charger() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		validate = new SBValidateNewChargerInBindFlow();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        
        body.put("deviceId","563747466006");
        body.put("agentCustId","1001647902");
        body.put("leadId","816d0855-cfb9-419d-b584-e17ced588ad4");
        body.put("scannedBarcodeType","NEW_CHARGER");
        
        
        response=services.v1ValidateCharger(validate, body, headers);
    
        Assert.assertEquals(response.getStatusCode(), 400);
       
        
	}
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Failure_When_Scanned_Asset_Is_Battery_And_Not_Charger() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		validate = new SBValidateNewChargerInBindFlow();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        
        body.put("deviceId","BATTERY1");
        body.put("agentCustId","1001647902");
        body.put("leadId","816d0855-cfb9-419d-b584-e17ced588ad4");
        body.put("scannedBarcodeType","NEW_CHARGER");
        
        
        response=services.v1ValidateCharger(validate, body, headers);
    
        Assert.assertEquals(response.getStatusCode(), 400);
       
        
	}
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Failure_When_Scanned_Asset_Is_Sim_And_Not_Charger() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		validate = new SBValidateNewChargerInBindFlow();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        
        body.put("deviceId","8991102105555713649U");
        body.put("agentCustId","1001647902");
        body.put("leadId","816d0855-cfb9-419d-b584-e17ced588ad4");
        body.put("scannedBarcodeType","NEW_CHARGER");
        
        
        response=services.v1ValidateCharger(validate, body, headers);
    
        Assert.assertEquals(response.getStatusCode(), 400);
       
        
	}
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Failure_When_Scanned_Asset_Is_Already_Deployed() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		validate = new SBValidateNewChargerInBindFlow();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        body.put("deviceId","CHARGER2");
        body.put("agentCustId","1001647902");
        body.put("leadId","816d0855-cfb9-419d-b584-e17ced588ad4");
        body.put("scannedBarcodeType","NEW_CHARGER");
        
        
        response=services.v1ValidateCharger(validate, body, headers);
    
        Assert.assertEquals(response.getStatusCode(), 400);
       
        
	}
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Failure_When_Scanned_Asset_Is_Already_Unmapped() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		validate = new SBValidateNewChargerInBindFlow();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        
        body.put("deviceId","CHARGER1");
        body.put("agentCustId","1001647902");
        body.put("leadId","816d0855-cfb9-419d-b584-e17ced588ad4");
        body.put("scannedBarcodeType","NEW_CHARGER");
        
        
        response=services.v1ValidateCharger(validate, body, headers);
    
        Assert.assertEquals(response.getStatusCode(), 400);
       
        
	}
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Failure_When_Scanned_Asset_Is_Not_Onboarded() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		validate = new SBValidateNewChargerInBindFlow();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        body.put("deviceId","CHARGER1000000000");
        body.put("agentCustId","1001647902");
        body.put("leadId","816d0855-cfb9-419d-b584-e17ced588ad4");
        body.put("scannedBarcodeType","NEW_CHARGER");
        
        
        response=services.v1ValidateCharger(validate, body, headers);
    
        Assert.assertEquals(response.getStatusCode(), 400);
       
        
	}
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Failure_When_Scanned_Asset_Is_Not_Available_With_Different_FSE() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		validate = new SBValidateNewChargerInBindFlow();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        
        body.put("deviceId","CHARGER6");
        body.put("agentCustId","1001647902");
        body.put("leadId","816d0855-cfb9-419d-b584-e17ced588ad4");
        body.put("scannedBarcodeType","NEW_CHARGER");
        
        
        response=services.v1ValidateCharger(validate, body, headers);
    
        Assert.assertEquals(response.getStatusCode(), 400);
       
        
	}
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Failure_When_Scanned_Asset_Is_In_Pending_Acknowledgement() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		validate = new SBValidateNewChargerInBindFlow();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        
        body.put("deviceId","CHARGER10");
        body.put("agentCustId","1001647902");
        body.put("leadId","816d0855-cfb9-419d-b584-e17ced588ad4");
        body.put("scannedBarcodeType","NEW_CHARGER");
        
        
        response=services.v1ValidateCharger(validate, body, headers);
    
        Assert.assertEquals(response.getStatusCode(), 400);
       
        
	}
	
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Failure_If_Device_Id_Is_Missing_From_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		validate = new SBValidateNewChargerInBindFlow();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        

        body.put("agentCustId","1001647902");
        body.put("leadId","816d0855-cfb9-419d-b584-e17ced588ad4");
        body.put("scannedBarcodeType","NEW_CHARGER");
        
        
        response=services.v1ValidateCharger(validate, body, headers);
    
        Assert.assertEquals(response.getStatusCode(), 400);
       
        
	}
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Failure_If_Agent_Cust_Id_Is_Missing_From_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		validate = new SBValidateNewChargerInBindFlow();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        
        body.put("deviceId","CHARGER10");
        body.put("leadId","816d0855-cfb9-419d-b584-e17ced588ad4");
        body.put("scannedBarcodeType","NEW_CHARGER");
        
        
        response=services.v1ValidateCharger(validate, body, headers);
    
        Assert.assertEquals(response.getStatusCode(), 400);
       
        
	}
	
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Failure_When_LeadId_Is_Invalid() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		validate = new SBValidateNewChargerInBindFlow();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        
        body.put("deviceId","CHARGER1000000000");
        body.put("agentCustId","1001647902");
        body.put("leadId","816d0855-cfb9-419d-b584-e17441588ad4");
        body.put("scannedBarcodeType","NEW_CHARGER");
        
        
        response=services.v1ValidateCharger(validate, body, headers);
    
        Assert.assertEquals(response.getStatusCode(), 500);
       
        
	}
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Failure_If_Lead_Id_Is_Missing_From_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		validate = new SBValidateNewChargerInBindFlow();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        
        body.put("deviceId","CHARGER10");
        body.put("agentCustId","1001647902");
        body.put("scannedBarcodeType","NEW_CHARGER");
        
        
        response=services.v1ValidateCharger(validate, body, headers);
    
        Assert.assertEquals(response.getStatusCode(), 200);
       
        
	}
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Failure_If_BarCodeType_Id_Is_Missing_From_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		validate = new SBValidateNewChargerInBindFlow();
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        
        body.put("deviceId","CHARGER10");
        body.put("agentCustId","1001647902");
        body.put("leadId","816d0855-cfb9-419d-b584-e17ced588ad4");
       
        
        
        response=services.v1ValidateCharger(validate, body, headers);
    
        Assert.assertEquals(response.getStatusCode(), 400);
       
        
	}
	
	@Test()
	public void verify_Status_Code_In_Case_Of_Success_When_Session_Token_Is_Not_Passed() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		validate = new SBValidateNewChargerInBindFlow();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        
        body.put("deviceId","CHARGER3");
        body.put("agentCustId","1001647902");
        body.put("leadId","816d0855-cfb9-419d-b584-e17ced588ad4");
        body.put("scannedBarcodeType","NEW_CHARGER");
        
        
        response=services.v1ValidateCharger(validate, body, headers);
    
        Assert.assertEquals(response.getStatusCode(), 401);
       
        
	}
	
	@Test()
	public void verify_Status_Message_In_Case_Of_Success_When_Version_Is_Not_Passed_In_Request() {
		
		headers=new HashMap<String,String>();
		body=new HashMap<String, String>();
		services=new MiddlewareServices();
		validate = new SBValidateNewChargerInBindFlow();
		
		headers.put("X-SRC", "GGClient");
	    headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");

        
        body.put("deviceId","CHARGER3");
        body.put("agentCustId","1001647902");
        body.put("leadId","816d0855-cfb9-419d-b584-e17ced588ad4");
        body.put("scannedBarcodeType","NEW_CHARGER");
        
        
        response=services.v1ValidateCharger(validate, body, headers);
    
        Assert.assertEquals(response.path("message"), "version is empty in header");
       
        
	}
	
	
}