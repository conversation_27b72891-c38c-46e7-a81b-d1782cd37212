package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.AddOnDetails;
import Request.SoundBox.SimActivation;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class AddOnDetailsTest extends BaseMethod {
	AddOnDetails addOns;
	public String sessionToken;
	
	public Response response;
	public MiddlewareServices services;
	public Map<String,String> headers;
	public Map<String,String> params;
	
	public SimActivation simObj;

	
	@BeforeClass
	public void setToken() {
	
		sessionToken=AgentSessionToken("8010630022", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}
	
	@Test
	public void verify_Status_Code_In_Case_Of_Success_Response() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		addOns=new AddOnDetails();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("planIds", "11625");
        params.put("leadId", "d1a4e252-91e9-404b-bfa1-0590f59a76b8");
       
        response=services.fetchAddOnDetails(addOns, params, headers);
  
      
	}
	
	@Test
	public void verify_Frequency_Components_Are_Present_In_Add_On_Details() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		addOns=new AddOnDetails();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("planIds", "11625");
        params.put("leadId", "d1a4e252-91e9-404b-bfa1-0590f59a76b8");
       
        response=services.fetchAddOnDetails(addOns, params, headers);
        Assert.assertTrue(!(response.path("frequency").toString().isEmpty()));
        Assert.assertTrue(!(response.path("frequencyType").toString().isEmpty()));
        Assert.assertTrue(!(response.path("rentalFrequency").toString().isEmpty()));
      
	}

	@Test
	public void verify_Refid__Is_Present_In_Response_In_Case_Of_Success() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		addOns=new AddOnDetails();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("planIds", "11625");
        params.put("leadId", "d1a4e252-91e9-404b-bfa1-0590f59a76b8");
       
        response=services.fetchAddOnDetails(addOns, params, headers);
        Assert.assertTrue(!(response.path("refId").toString().isEmpty()));
      
	}

	@Test
	public void verify_Refid__Is_Present_In_Response_In_Case_Of_Failure() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		addOns=new AddOnDetails();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("planIds", "999");
        params.put("leadId", "d1a4e252-91e9-404b-bfa1-0590f59a76b8");
       
        response=services.fetchAddOnDetails(addOns, params, headers);
        Assert.assertTrue(!(response.path("refId").toString().isEmpty()));
      
	}

	@Test
	public void verify_Rental_Components_Are_Present_In_Add_On_Details() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		addOns=new AddOnDetails();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("planIds", "11625");
        params.put("leadId", "d1a4e252-91e9-404b-bfa1-0590f59a76b8");
       
        response=services.fetchAddOnDetails(addOns, params, headers);
        
         
	}

	@Test
	public void verify_Add_On_Type_Is_Correct_In_Response() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		addOns=new AddOnDetails();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("planIds", "11625");
        params.put("leadId", "d1a4e252-91e9-404b-bfa1-0590f59a76b8");
       
        response=services.fetchAddOnDetails(addOns, params, headers);
 
      
	}

	@Test
	public void verify_Name_of_Add_On_Type_Is_Correct_In_Response() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		addOns=new AddOnDetails();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("planIds", "11625");
        params.put("leadId", "d1a4e252-91e9-404b-bfa1-0590f59a76b8");
       
        response=services.fetchAddOnDetails(addOns, params, headers);
      
      
	}

	@Test
	public void verify_Error_Code_When_LeadID_Is_Missing_In_Request() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		addOns=new AddOnDetails();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("planIds", "999");
     
       
        response=services.fetchAddOnDetails(addOns, params, headers);
        Assert.assertEquals(response.getStatusCode(),400);
      
	}
	
	@Test
	public void verify_Error_Code_In_Case_Of_Token_Is_Missing_From_Request() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		addOns=new AddOnDetails();
		
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("planIds", "11625");
        params.put("leadId", "d1a4e252-91e9-404b-bfa1-0590f59a76b8");
       
        response=services.fetchAddOnDetails(addOns, params, headers);
        Assert.assertEquals(response.getStatusCode(),401);
      
	}
	@Test
	public void verify_Error_Code_In_Case_Of_DeviceIdentifier_Is_Missing_From_Request() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		addOns=new AddOnDetails();
		
		headers.put("session_token", sessionToken);

		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("planIds", "11625");
        params.put("leadId", "d1a4e252-91e9-404b-bfa1-0590f59a76b8");
       
        response=services.fetchAddOnDetails(addOns, params, headers);
        Assert.assertEquals(response.getStatusCode(),410);
      
	}

	@Test
	public void verify_Error_Message_In_Case_Of_Version_Is_Missing_From_Request() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		addOns=new AddOnDetails();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("planIds", "11625");
        params.put("leadId", "d1a4e252-91e9-404b-bfa1-0590f59a76b8");
       
        response=services.fetchAddOnDetails(addOns, params, headers);
        Assert.assertEquals(response.path("message").toString(),"version is empty in header");
        Assert.assertEquals(response.path("errorCode").toString(),"VERSION_FAILURE");	
      
	}


}