package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.SBCheckBTStatus;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestSBCheckBTStatus extends BaseMethod{

	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	public Map<String,String> headers;
	public Map<String,String> params;
	public SBCheckBTStatus status;

	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	@Test
	public void verify_Status_Code_And_Status_Message_In_Case_Of_Success_Response() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		status=new SBCheckBTStatus();

		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");
    
        params.put("leadId", "5d7339b6-da7a-43e2-a878-c9dc0dc79170");
        params.put("action", "BT_CONNECTIVITY_STATUS");
 

        response=services.checkBTStatus(status, params, headers);
        Assert.assertEquals(response.getStatusCode(),200);
        Assert.assertEquals(response.path("displayMessage"), "Paired");
      
	}
	
	
	@Test
	public void verify_Status_Code_And_Status_Message_In_Case_Device_Is_Not_Paired() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		status=new SBCheckBTStatus();
		sessionToken=AgentSessionToken("8010630022", "paytm@123");
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");
    
        params.put("leadId", "5d7339b6-da7a-43e2-a878-c9dc0dc79170");
        params.put("action", "BT_CONNECTIVITY_STATUS");
 

        response=services.checkBTStatus(status, params, headers);
        Assert.assertEquals(response.getStatusCode(),200);
       
      
	}
	
	@Test
	public void verify_Status_Code_And_Status_Message_In_Case_Lead_Is_Closed() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		status=new SBCheckBTStatus();
		sessionToken=AgentSessionToken("8010630022", "paytm@123");
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");
    
        params.put("leadId", "05676d68-057b-4f39-8b51-07e66f438aed");
        params.put("action", "BT_CONNECTIVITY_STATUS");
 

        response=services.checkBTStatus(status, params, headers);
        Assert.assertEquals(response.getStatusCode(),500);
      
	}
	
	@Test
	public void verify_Status_Code_And_Status_Message_In_Case_Lead_Is_Missing() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		status=new SBCheckBTStatus();
		sessionToken=AgentSessionToken("8010630022", "paytm@123");
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");
    
    
        params.put("action", "BT_CONNECTIVITY_STATUS");
 

        response=services.checkBTStatus(status, params, headers);
        Assert.assertEquals(response.getStatusCode(),400);
      
	}
	
	
	@Test
	public void verify_Status_Code_In_Case_Action_Is_Not_Passed() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		status=new SBCheckBTStatus();
		sessionToken=AgentSessionToken("8010630022", "paytm@123");
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");
    
        params.put("leadId", "05676d68-057b-4f39-8b51-07e66f438aed");

        response=services.checkBTStatus(status, params, headers);
        Assert.assertEquals(response.getStatusCode(),500);
      
	}
	
	@Test
	public void verify_Status_Code_And_Status_Message_In_Case_Of_Action_Is_Unbind_Device() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		status=new SBCheckBTStatus();
		sessionToken=AgentSessionToken("8010630022", "paytm@123");
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");
    
        params.put("leadId", "5d7339b6-da7a-43e2-a878-c9dc0dc79170");
        params.put("action", "UNBIND_DEVICE");
 

        response=services.checkBTStatus(status, params, headers);
        Assert.assertEquals(response.getStatusCode(),400);

      
	}
	
	@Test
	public void verify_Status_Code_And_Status_Message_In_Case_Of_Errror_From_IOT_When_Action_Is_Unbind_Device() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		status=new SBCheckBTStatus();
		sessionToken=AgentSessionToken("8010630022", "paytm@123");
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");
    
        params.put("leadId", "a9dc260f-33d3-43cb-9e38-b90884d4f856");
        params.put("action", "UNBIND_DEVICE");
 

        response=services.checkBTStatus(status, params, headers);
        Assert.assertEquals(response.getStatusCode(),400);
      
	}
	
	@Test
	public void verify_Status_Code_And_In_Case_Of_JWT_Not_Passed_In_Request() {
		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		status=new SBCheckBTStatus();
		sessionToken=AgentSessionToken("8010630022", "paytm@123");
		
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("client" , "androidapp");
        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
        headers.put("applanguage" , "en");
        headers.put("accept-language" , "en-US,en;q=0.9");
        headers.put("isdevicerooted" , "false");
        headers.put("osversion" , "11");
    
        params.put("leadId", "5d7339b6-da7a-43e2-a878-c9dc0dc79170");
        params.put("action", "BT_CONNECTIVITY_STATUS");
 

        response=services.checkBTStatus(status, params, headers);
        Assert.assertEquals(response.getStatusCode(),401);
 
      
	}
	
//	@Test
//	public void verify_Status_Code_And_In_Case_Of_Version_Not_Passed_In_Request() {
//		headers=new HashMap<>();
//		params=new HashMap<>();
//		services=new MiddlewareServices();
//		status=new SBCheckBTStatus();
//		sessionToken=AgentSessionToken("8010630022", "paytm@123");
//		
//		headers.put("session_token", sessionToken);
//		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        headers.put("isBusyBoxFound" , "false");
//        headers.put("osVersion" , "10");
//        headers.put("appLanguage","en");
//        headers.put("client" , "androidapp");
//        headers.put("x-mw-url-checksum" , "R6Uu4WbYuhyzfc6WolgITR0Rh+vwGWITyHCbF+TtiXP41kK8tTqbaJ7LwGwBvTkPqGKs089/eudgx+5/Q2yp56GG0ZDOPsQt0bs+MbSvKbRNw3HGYFZvAbtKYta4wRga");
//        headers.put("applanguage" , "en");
//        headers.put("accept-language" , "en-US,en;q=0.9");
//        headers.put("isdevicerooted" , "false");
//        headers.put("osversion" , "11");
//    
//        params.put("leadId", "5d7339b6-da7a-43e2-a878-c9dc0dc79170");
//        params.put("action", "BT_CONNECTIVITY_STATUS");
// 
//
//        response=services.checkBTStatus(status, params, headers);
//   
//        String displayMessage = response.path("displayMessage").toString();
//        Assert.assertTrue(displayMessage.contains("version is empty in header"));
//      
//	}

}