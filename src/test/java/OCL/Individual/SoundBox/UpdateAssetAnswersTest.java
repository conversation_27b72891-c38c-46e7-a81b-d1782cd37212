package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.UpdateAssetAnswers;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class UpdateAssetAnswersTest extends BaseMethod {

	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> body;
	public UpdateAssetAnswers answers;
	@BeforeClass
	public void setToken() {
	
		sessionToken=AgentSessionToken("8010630022", "paytm@123");
		try {
//			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}

	@Test
	public void verify_Next_Action_When_Old_Battery_Available_Is_Selected_As_No() throws Exception {
        establishConnectiontoServer(sessionToken, 5);

        headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("question","batteryAvailable");
        body.put("question2","batteryWorking");

        body.put("answer","no");
        body.put("answer2","");
        body.put("assetAction","BATTERY_QNA_SCREEN");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","c7a608f0-9b25-49db-af40-8994e89acec5");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");

        answers=new UpdateAssetAnswers();
        response=services.updateAssetsAnswer(answers, body, headers);	
        

        answers=new UpdateAssetAnswers();
        response=services.updateAssetsAnswer(answers, body, headers);	
        
        Assert.assertEquals(response.getStatusCode(), 400);

	}
	
	@Test
	public void verify_Next_Action_When_Old_Battery_Available_Is_Selected_As_Yes_And_Old_Battery_Working_No() throws Exception {
        establishConnectiontoServer(sessionToken, 5);

        headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("question","batteryAvailable");
        body.put("answer","yes");
        body.put("question2","batteryWorking");
        body.put("answer2","no");
        body.put("assetAction","BATTERY_QNA_SCREEN");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","c7a608f0-9b25-49db-af40-8994e89acec5");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");

        answers=new UpdateAssetAnswers();
        response=services.updateAssetsAnswer(answers, body, headers);	
        
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	
	@Test
	public void verify_Next_Action_When_Old_Battery_Available_Is_Selected_As_Yes_And_Old_Battery_Working_Yes() throws Exception {
        establishConnectiontoServer(sessionToken, 5);

        headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("question","batteryAvailable");
        body.put("answer","yes");
        body.put("question2","batteryWorking");
        body.put("answer2","yes");
        body.put("assetAction","BATTERY_QNA_SCREEN");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","c7a608f0-9b25-49db-af40-8994e89acec5");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");

        answers=new UpdateAssetAnswers();
        response=services.updateAssetsAnswer(answers, body, headers);	
        
        Assert.assertEquals(response.getStatusCode(), 400);
	}

	@Test
	public void verify_Next_Action_When_Old_Charger_Available_Is_Selected_As_No() throws Exception {
        establishConnectiontoServer(sessionToken, 5);

        headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("question","batteryAvailable");
        body.put("answer","no");
        body.put("question2","chargerWorking");
        body.put("answer2","");
        body.put("assetAction","CHARGER_QNA_SCREEN");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","c7a608f0-9b25-49db-af40-8994e89acec5");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");

        answers=new UpdateAssetAnswers();
        response=services.updateAssetsAnswer(answers, body, headers);	
        

        answers=new UpdateAssetAnswers();
        response=services.updateAssetsAnswer(answers, body, headers);	
        
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test
	public void verify_Next_Action_When_Old_Charger_Available_Is_Selected_As_Yes_And_Old_Charger_Working_No() throws Exception {
        establishConnectiontoServer(sessionToken, 5);

        headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("question","chargerAvailable");
        body.put("question2","chargerWorking");
        body.put("answer","yes");
        body.put("answer2","no");
        body.put("assetAction","CHARGER_QNA_SCREEN");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","c7a608f0-9b25-49db-af40-8994e89acec5");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");

        answers=new UpdateAssetAnswers();
        response=services.updateAssetsAnswer(answers, body, headers);	
        
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	
	@Test
	public void verifyStatusCodeWhenVersionIsEmpty() throws Exception {
        establishConnectiontoServer(sessionToken, 5);

        headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("question","chargerAvailable");
        body.put("question2","chargerWorking");
        body.put("answer","yes");
        body.put("answer2","no");
        body.put("assetAction","CHARGER_QNA_SCREEN");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","c7a608f0-9b25-49db-af40-8994e89acec5");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");

        answers=new UpdateAssetAnswers();
        response=services.updateAssetsAnswer(answers, body, headers);	
        
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	

	
	
	
	@Test
	public void verifyStatusCodeWhenSessionTokenIsInvalidInRequest() throws Exception {
        establishConnectiontoServer(sessionToken, 5);

        headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", "abc");
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("question","chargerAvailable");
        body.put("question2","chargerWorking");
        body.put("answer","yes");
        body.put("answer2","no");
        body.put("assetAction","CHARGER_QNA_SCREEN");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","c7a608f0-9b25-49db-af40-8994e89acec5");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");

        answers=new UpdateAssetAnswers();
        response=services.updateAssetsAnswer(answers, body, headers);	
        
        Assert.assertEquals(response.getStatusCode(), 410);
	}

	
	@Test
	public void verifyStatusCodeWhenSessionTokenIsNotPassedInRequest() throws Exception {
        establishConnectiontoServer(sessionToken, 5);

        headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("question","chargerAvailable");
        body.put("question2","chargerWorking");
        body.put("answer","yes");
        body.put("answer2","no");
        body.put("assetAction","CHARGER_QNA_SCREEN");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","c7a608f0-9b25-49db-af40-8994e89acec5");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");

        answers=new UpdateAssetAnswers();
        response=services.updateAssetsAnswer(answers, body, headers);	
        
        Assert.assertEquals(response.getStatusCode(), 401);
	}

	
	
	@Test(dependsOnMethods = {"verify_Next_Action_When_Old_Battery_Available_Is_Selected_As_Yes_And_Old_Battery_Working_Yes"})
	public void verify_Next_Action_When_Old_Charger_Available_Is_Selected_As_Yes_And_Old_Charger_Working_Yes() throws Exception {
        establishConnectiontoServer(sessionToken, 5);

        headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("question","chargerAvailable");
        body.put("answer","yes");
        body.put("question2","chargerWorking");
        body.put("answer2","yes");
        body.put("assetAction","CHARGER_QNA_SCREEN");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","c7a608f0-9b25-49db-af40-8994e89acec5");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");

        answers=new UpdateAssetAnswers();
        response=services.updateAssetsAnswer(answers, body, headers);	
        
        Assert.assertEquals(response.getStatusCode(), 400);
	}

	


}