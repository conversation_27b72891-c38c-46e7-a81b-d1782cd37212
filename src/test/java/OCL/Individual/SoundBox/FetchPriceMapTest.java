package OCL.Individual.SoundBox;

import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.LinkedHashMap;




public class FetchPriceMapTest extends BaseMethod{

	public String sessionToken;
	
	public Response response;
	public MiddlewareServices services;
	
	public LinkedHashMap<String,String> headers;
	public LinkedHashMap<String, String> params;

	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	@BeforeMethod
	private void initHeadersAndParams() {	
			
			headers=new LinkedHashMap<>();
			headers.put("version", "4.9.2");
	        headers.put("session_token", sessionToken);
	        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
	        headers.put("Content-Type", "application/json");
	    	params=new LinkedHashMap<>();
	        services=new MiddlewareServices();
	}
	
	@Test
	private void verify_Price_Details_For_Valid_Lead() {
		params.put("deviceType", "sound_box_3_0_4g");
		params.put("leadId", "d0988db1-a2bb-4c38-be1a-4e9c78559e45");
		response= services.FetchPlan(params,headers);	
		Assert.assertEquals(response.statusCode(),200);

	}
	
	@Test
	public void verify_No_Price_Details_For_Invalid_Lead() {
		params.put("deviceType", "sound_box_3_0_4g");
		params.put("leadId", "xyzabc");
		response= services.FetchPlan(params,headers);	
		Assert.assertEquals(response.statusCode(),200);

	}
	
	@Test
	public void verify_Price_Details_Without_DeviceType() {
		params.put("deviceType", "sound_box_3_0_4g");
		params.put("leadId", "d0988db1-a2bb-4c38-be1a-4e9c78559e45");
		response= services.FetchPlan(params,headers);	
		Assert.assertEquals(response.statusCode(),200);
	}
	
	@Test
	public void verify_Price_Details_With_Device_Type_Only() {
		params.put("deviceType", "sound_box_3_0_4g");
		response= services.FetchPlan(params,headers);	
		Assert.assertEquals(response.statusCode(),200);
	}
	
	@Test
	public void verify_Price_Details_For_Already_Closed_Lead() {
		params.put("deviceType", "sound_box_3_0_4g");
		params.put("leadId", "b5a7bc4e-f0f3-492c-b98f-bbd7b599f396");
		response= services.FetchPlan(params,headers);	
		Assert.assertEquals(response.statusCode(),200);
	}
}
