package OCL.Individual.SoundBox;

import Request.SoundBox.InitiateNssLead;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import Services.DBConnection.DBConnection;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

import java.util.HashMap;
import java.util.Map;

public class InitiateNssLeadTest extends BaseMethod {
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(InitiateNssLeadTest.class);

    String AgentToken;
    String mid = "zgzGbX48544612478938";
    String deviceId = "563747466002";
    String ticketNumber = "12345";
    String tag = "Soundbox Replacement";

    @BeforeClass
    public void AgentLoginSoundBox() throws Exception {
        AgentToken = AgentSessionToken("7771216290", "paytm@123");
        LOGGER.info("Agent Token for Soundbox: " + AgentToken);
        establishConnectiontoServer(AgentToken, 5);
    }

    @Test(priority = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInitiateNssLeadWithEmptyMid() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, Object> body = createRequestBody("", deviceId, ticketNumber, tag);

        InitiateNssLead initiateNssLeadObj = new InitiateNssLead();
        Response response = middlewareServicesObject.initiateNssLead(initiateNssLeadObj, headers, body);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        
        // Validate error response
        Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("Mid cannot be empty"));
    }

    @Test(priority = 3)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInitiateNssLeadWithEmptyDeviceId() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, Object> body = createRequestBody(mid, "", ticketNumber, tag);

        InitiateNssLead initiateNssLeadObj = new InitiateNssLead();
        Response response = middlewareServicesObject.initiateNssLead(initiateNssLeadObj, headers, body);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        
        // Validate error response
        Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("Device Id cannot be empty"));
    }

    @Test(priority = 4)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInitiateNssLeadWithEmptyTicketNumber() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, Object> body = createRequestBody(mid, deviceId, "", tag);

        InitiateNssLead initiateNssLeadObj = new InitiateNssLead();
        Response response = middlewareServicesObject.initiateNssLead(initiateNssLeadObj, headers, body);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        
        // Validate error response
        Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("Ticket Number cannot be empty"));
    }

    @Test(priority = 5)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInitiateNssLeadWithEmptyTag() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, Object> body = createRequestBody(mid, deviceId, ticketNumber, "");

        InitiateNssLead initiateNssLeadObj = new InitiateNssLead();
        Response response = middlewareServicesObject.initiateNssLead(initiateNssLeadObj, headers, body);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        
        // Validate error response
        Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("Tag cannot be empty"));
    }

    @Test(priority = 6)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInitiateNssLeadWithInvalidMid() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, Object> body = createRequestBody("INVALID_MID", deviceId, ticketNumber, tag);

        InitiateNssLead initiateNssLeadObj = new InitiateNssLead();
        Response response = middlewareServicesObject.initiateNssLead(initiateNssLeadObj, headers, body);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        
        // Validate error response
        Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("Exception while fetching Mbid details. Please try again"));
    }

    @Test(priority = 7)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInitiateNssLeadWithInvalidDeviceId() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, Object> body = createRequestBody(mid, "INVALID_DEVICE_ID", ticketNumber, tag);

        InitiateNssLead initiateNssLeadObj = new InitiateNssLead();
        Response response = middlewareServicesObject.initiateNssLead(initiateNssLeadObj, headers, body);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        
        // Validate error response
        Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("Device information not found at IOT"));
    }

    @Test(priority = 8)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInitiateNssLeadWithEmptyAddress() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, Object> body = new HashMap<>();
        body.put("mid", mid);
        body.put("deviceId", deviceId);
        body.put("ticketNumber", ticketNumber);
        body.put("tag", tag);
        body.put("address", "{}");


        InitiateNssLead initiateNssLeadObj = new InitiateNssLead();
        Response response = middlewareServicesObject.initiateNssLead(initiateNssLeadObj, headers, body);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 400);
        
        // Validate error response
        Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("Address Details cannot be empty"));
    }

    @Test(priority = 9)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void testInitiateNssLeadWithValidData() {
        Map<String, String> headers = setCommonHeaders();
        Map<String, Object> body = createRequestBody(mid, deviceId, ticketNumber, tag);

        InitiateNssLead initiateNssLeadObj = new InitiateNssLead();
        Response response = middlewareServicesObject.initiateNssLead(initiateNssLeadObj, headers, body);
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String responseBody = response.getBody().asString();
        LOGGER.info("Response: " + responseBody);

        // Validate response fields
        Assert.assertTrue(response.getBody().jsonPath().getString("displayMessage").contains("SUCCESS"));

    }

    /**
     * Updates the status of any existing NSS leads in the user_business_mapping table.
     * This method checks for entries with solution_type='nss', solution_type_level_2='sound_box_replacement',
     * and status=0, and updates their status to 2 (closed).
     * 
     * @throws Exception if there is an error connecting to the database or executing the query
     */
    @Test(priority = 1)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void updateNssLeadStatusInDB() throws Exception {
        LOGGER.info("Checking for existing NSS leads in the database...");
        
        Connection connection = null;
        try {
            // Establish connection to the database
            connection = DBConnection.establishConnection();
            Statement statement = connection.createStatement();
            
            // First, check if there are any entries that match our criteria
            String selectQuery = "SELECT COUNT(*) as count FROM user_business_mapping WHERE solution_type = 'nss' AND solution_type_level_2 = 'sound_box_replacement' AND status = 0";
            ResultSet resultSet = statement.executeQuery(selectQuery);
            
            int count = 0;
            if (resultSet.next()) {
                count = resultSet.getInt("count");
            }
            
            LOGGER.info("Found " + count + " NSS leads with status 0 in the database.");
            
            if (count > 0) {
                // Update the status of matching entries to 2
                String updateQuery = "UPDATE user_business_mapping SET status = 2 WHERE solution_type = 'nss' AND solution_type_level_2 = 'sound_box_replacement' AND status = 0";
                int rowsAffected = statement.executeUpdate(updateQuery);
                
                LOGGER.info("Updated " + rowsAffected + " NSS leads to status 2 in the database.");
                Assert.assertEquals(rowsAffected, count, "Number of updated rows should match the count of found rows");
            } else {
                LOGGER.info("No NSS leads with status 0 found in the database. No updates needed.");
            }
            
        } catch (SQLException e) {
            LOGGER.error("Error updating NSS leads in the database: " + e.getMessage(), e);
            throw e;
        } finally {
            // Close the connection
            if (connection != null && !connection.isClosed()) {
                connection.close();
                LOGGER.info("Database connection closed.");
            }
        }
    }

    private Map<String, String> setCommonHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", AgentToken);
        return headers;
    }

    private Map<String, Object> createRequestBody(String mid, String deviceId, String ticketNumber, String tag) {
        Map<String, Object> body = new HashMap<>();
        body.put("mid", mid);
        body.put("deviceId", deviceId);
        body.put("ticketNumber", ticketNumber);
        body.put("tag", tag);
        
        // Create address object
        Map<String, String> address = new HashMap<>();
        address.put("line1", "Park Road New Delhi Mahindra Park Bindapur");
        address.put("line2", "New Delhi Mahindra Park Bindapur");
        address.put("line3", "New Delhi West Delhi Delhi Division");
        address.put("city", "West Delhi");
        address.put("state", "Delhi");
        address.put("country", "India");
        address.put("pincode", "110001");
        
        // Convert address map to JSON string
        String addressJson = "{\"line1\":\"Park Road New Delhi Mahindra Park Bindapur\",\"line2\":\"New Delhi Mahindra Park Bindapur\",\"line3\":\"New Delhi West Delhi Delhi Division\",\"city\":\"West Delhi\",\"state\":\"Delhi\",\"country\":\"India\",\"pincode\":\"110001\"}";
        body.put("address", addressJson);
        
        return body;
    }
} 