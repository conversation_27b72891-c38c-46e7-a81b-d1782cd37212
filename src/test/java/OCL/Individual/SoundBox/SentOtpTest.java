package OCL.Individual.SoundBox;

import Services.MechantService.MiddlewareServices;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class SentOtpTest extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(SentOtpTest.class);
    public static String mobile = "7771110999";
    public static String userType = "sound_box";
    public static boolean call = false;
    public String agentToken="";
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();



    @Test(priority = 1, description = "When session_token expires")
    public void SendingOTp() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("solutionType", "sound_box");

        Map<String, String> headers = new HashMap<String, String>();
        //SentOtpTest sbobject =new SentOtpTest(P.TESTDATA.get("v1SentOtp"));
        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
      //  headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
        headers.put("deviceName", "RMP2102");
        headers.put("version", "7.1.9");
        headers.put("session_token", "eyJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiZGlyIn0..FXpBXh_92f2vtAKO.cWyjHR3PJqvGmzhyb8eDYuIkl-B5YViAEvl6bPm4h8Ni-aHZ57ykScgb9kJQOYT4ME674hTgjy7EDTecNhoFwtCCl1djpyZ5IVKA-11KM2HQFHFrOw4k3hPxgHb-un7kTXTh9l7mVbn4zYJRLAtzmm5eB2HhXafptuVmyVDpxpCO9no7eQ8ZdNWptMYOt3D41KfNKAmK7FOUrGaFhQypPz8xQUkaPXK8MXWPiplNHE63YaXJJf0Q0xAtb7l11CkAZ_i9XnvgZXyUeq-k.trrnB2GXfBaIwvMU9O0SYQ4100");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "realme-RMP2102-b0af55f050f73f9f");
        headers.put("osVersion", "11");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "AA:5E:45:0E:B3:46");
        headers.put("deviceManufacturer", "realme");
     //   headers.put("X-MW-CHECKSUM", "vcyR4VWcFjutny9Jbe3V9d+fQ/uxjJXGSeoqJQtpEZEHssdTW2omBMp0wqq/BVW3O92IZCltT+LlKzvI3FNgLid54Hxw6eoJAUPN4Cx+17ApAcRiUI3PNdunZ03fHw9+");
        headers.put("androidId", "b0af55f050f73f9f");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 11; RMP2102 Build/RP1A.200720.011)");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("mobile", "7771110999");
        body.put("userType", userType);
        body.put("call", call);

        Response fetchPermissionResponse = MiddlewareServicesObject.v1SentOtp(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        //Assert.assertTrue(httpcode == 200, "Testcase Failed");
        Assert.assertEquals(httpcode, 410);


    }

    @Test(priority = 2, description = "When something missing")
    public void SendingOtp2() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("solutionType", "sound_box");

        Map<String, String> headers = new HashMap<String, String>();
        //SentOtpTest sbobject =new SentOtpTest(P.TESTDATA.get("v1SentOtp"));
        // headers.put("X-SRC","GGClient");
        // headers.put("latitude", "0.0");
        // headers.put("ipAddress", "***********");
        //headers.put("isBusyBoxFound", "false");
        //headers.put("X-MW-URL-CHECKSUM","Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
        // headers.put("deviceName","RMP2102");
        headers.put("version", "7.1.9");
        //headers.put("session_token","eyJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiZGlyIn0..FXpBXh_92f2vtAKO.cWyjHR3PJqvGmzhyb8eDYuIkl-B5YViAEvl6bPm4h8Ni-aHZ57ykScgb9kJQOYT4ME674hTgjy7EDTecNhoFwtCCl1djpyZ5IVKA-11KM2HQFHFrOw4k3hPxgHb-un7kTXTh9l7mVbn4zYJRLAtzmm5eB2HhXafptuVmyVDpxpCO9no7eQ8ZdNWptMYOt3D41KfNKAmK7FOUrGaFhQypPz8xQUkaPXK8MXWPiplNHE63YaXJJf0Q0xAtb7l11CkAZ_i9XnvgZXyUeq-k.trrnB2GXfBaIwvMU9O0SYQ4100");
        //headers.put("session_token","eyJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiZGlyIn0..MRGcvP89zUCs8GJc.1pLPdIWQUE8o7nZX9HvikKE4CwZqJ4wvdCvFmOMx6LqQ0kqcIAFaoAZDsbvrxnrLuDAUE-g-3XlqueFKFi1gdVZG-GWadycY7vnBnYH-kTc0NCfR1L2cskZdSHRixY23iNDxH1vmJMxzVDVwQkB82vtvLRGccqZRh89diwMLOd7FupenyBknLRUQmczUDqyrab-joib58Y4jEp0v3ZYTQt7Rw86dOR0qUZQWiXr5FSWMH31ZdOv3U3swady_Ra4GO_YRCaeBq_At_FF9.YTXUVLdxSxxQsFOEs7zXlQ4100");
        //headers.put("isDeviceRooted","false");
        headers.put("deviceIdentifier", "realme-RMP2102-b0af55f050f73f9f");
        // headers.put("osVersion","11");
        // headers.put("isLocationMocked","false");
        // headers.put("client","androidapp");
        headers.put("deviceMac", "AA:5E:45:0E:B3:46");
        // headers.put("deviceManufacturer","realme");
    //    headers.put("X-MW-CHECKSUM", "vcyR4VWcFjutny9Jbe3V9d+fQ/uxjJXGSeoqJQtpEZEHssdTW2omBMp0wqq/BVW3O92IZCltT+LlKzvI3FNgLid54Hxw6eoJAUPN4Cx+17ApAcRiUI3PNdunZ03fHw9+");
        // headers.put("androidId","b0af55f050f73f9f");
        // headers.put("appLanguage","en");
        //  headers.put("longitude","0.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        //headers.put("User-Agent","Dalvik/2.1.0 (Linux; U; Android 11; RMP2102 Build/RP1A.200720.011)");
        // headers.put("Host","goldengate-staging5.paytm.com");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("mobile", mobile);
        body.put("userType", userType);
        body.put("call", call);

        Response fetchPermissionResponse = MiddlewareServicesObject.v1SentOtp(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        //Assert.assertTrue(httpcode == 200, "Testcase Failed");
        Assert.assertEquals(httpcode, 401);


    }

    @Test(priority = 3, description = "Removing the session token ")
    public void SendingOtp3() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("solutionType", "sound_box");
        // In headers i am removing this session_token
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
        headers.put("version", "7.1.9");
        headers.put("deviceIdentifier", "realme-RMP2102-b0af55f050f73f9f");
        headers.put("deviceMac", "AA:5E:45:0E:B3:46");
        headers.put("X-MW-CHECKSUM", "vcyR4VWcFjutny9Jbe3V9d+fQ/uxjJXGSeoqJQtpEZEHssdTW2omBMp0wqq/BVW3O92IZCltT+LlKzvI3FNgLid54Hxw6eoJAUPN4Cx+17ApAcRiUI3PNdunZ03fHw9+");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("mobile", mobile);
        body.put("userType", userType);
        body.put("call", call);

        Response fetchPermissionResponse = MiddlewareServicesObject.v1SentOtp(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        //Assert.assertTrue(httpcode == 200, "Testcase Failed");
        Assert.assertEquals(httpcode, 401);


    }

    @Test(priority = 4, description = "keeping the key value as blank")
    public void SendingOtp4() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("solutionType", "sound_box");
        // In headers i am removing this session_token
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
        headers.put("version", "7.1.9");
        //keeping the key value as blank
        headers.put("deviceIdentifier", "");
        headers.put("deviceMac", "AA:5E:45:0E:B3:46");
        headers.put("X-MW-CHECKSUM", "vcyR4VWcFjutny9Jbe3V9d+fQ/uxjJXGSeoqJQtpEZEHssdTW2omBMp0wqq/BVW3O92IZCltT+LlKzvI3FNgLid54Hxw6eoJAUPN4Cx+17ApAcRiUI3PNdunZ03fHw9+");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("mobile", mobile);
        body.put("userType", userType);
        body.put("call", call);

        Response fetchPermissionResponse = MiddlewareServicesObject.v1SentOtp(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        //Assert.assertTrue(httpcode == 200, "Testcase Failed");
        Assert.assertEquals(httpcode, 401);


    }

    @Test(priority = 5, description = "If the merchant number is invalid at body")
    public void SendingOtp5() throws Exception {
    	agentToken=AgentSessionToken("7771216290","paytm@123");
        establishConnectiontoServer(agentToken,5);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("solutionType", "sound_box");

        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
        headers.put("version", "7.1.9");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("session_token", agentToken);
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        Map<String, Object> body = new HashMap<String, Object>();
        body.put("mobile", "98798798798798798");
        body.put("userType", userType);
        body.put("call", call);

        Response fetchPermissionResponse = MiddlewareServicesObject.v1SentOtp(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        //Assert.assertTrue(httpcode == 200, "Testcase Failed");
        Assert.assertEquals(httpcode, 400);
    }

    @Test(priority = 6, description = "if we remove the query params")
    public void SendingOtp6() throws Exception {
    	agentToken=AgentSessionToken("7771216290","paytm@123");
        establishConnectiontoServer(agentToken,5);
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("X-MW-URL-CHECKSUM", "Y9jUHp6ND9ctGUMWky4zeXl0uNUNQF7sgfqcdqKz2KI+d2FmTpNy+wv/n8B/HLQLOMC7TzWD18uFKx6U375SLRlLRWKZa6jYys4OguMG7wfP1Pv9BWq4nNMT02s7suEX");
        headers.put("version", "7.1.9");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("session_token", agentToken);
//        headers.put("deviceMac", "AA:5E:45:0E:B3:46");
//        headers.put("X-MW-CHECKSUM", "vcyR4VWcFjutny9Jbe3V9d+fQ/uxjJXGSeoqJQtpEZEHssdTW2omBMp0wqq/BVW3O92IZCltT+LlKzvI3FNgLid54Hxw6eoJAUPN4Cx+17ApAcRiUI3PNdunZ03fHw9+");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("mobile", mobile);
        body.put("userType", userType);
        body.put("call", call);

        Response fetchPermissionResponse = MiddlewareServicesObject.v1SentOtp(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        //Assert.assertTrue(httpcode == 200, "Testcase Failed");
        Assert.assertEquals(httpcode, 200);
    }

    @Test(priority = 7, description = "When ALL THE HEADERS ARE GIVEN")
    public void SendingOTp7() throws Exception {
    	agentToken=AgentSessionToken("7771216290","paytm@123");
        establishConnectiontoServer(agentToken,5);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("solutionType", "sound_box");
        
        Map<String, String> headers = new HashMap<String, String>();
        //SentOtpTest sbobject =new SentOtpTest(P.TESTDATA.get("v1SentOtp"));
        headers.put("X-SRC", "GGClient");
        headers.put("version", "7.1.9");
        headers.put("appLanguage", "en");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("session_token", agentToken);
        headers.put("latitude", "28.5772544");
        headers.put("longitude", "77.4384498");
        headers.put("ipAddress", "**************");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "Redmi_Note_5_Pro");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "Xiaomi-RedmiNote5Pro-861181041338761");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "08:25:25:99:13:38");
        headers.put("deviceManufacturer", "Xiaomi");
        headers.put("androidId", "131535fc93929702");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("mobile", mobile);
        body.put("userType", userType);
        body.put("call", call);

        Response fetchPermissionResponse = MiddlewareServicesObject.v1SentOtp(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        //Assert.assertTrue(httpcode == 200, "Testcase Failed");
        Assert.assertEquals(httpcode, 200);


    }
}