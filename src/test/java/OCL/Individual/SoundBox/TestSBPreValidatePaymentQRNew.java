package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.SBPreValidatePaymentQRNew;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestSBPreValidatePaymentQRNew extends BaseMethod{

	SBPreValidatePaymentQRNew qr;
	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> body;
	
	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("8010630022", "paytm@123");
		try {
//			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	@Test
	public void verifyStatusCodeWhenMappedQrCodeIsScanned() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmocl.s108lyy@axis&pn=Paytm");
        body.put("pgMid", "HySHnd27878673398759");
        body.put("solutionSubtype", "sound_box_bind");
//        body.put("leadId", ""); 
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 200);

	}
	
	
	@Test
	public void verifyStatusCodeWhenScannedQrCodeIsIncorrect() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmocl.s108lyy@sbi&pn=Paytm");
        body.put("pgMid", "HySHnd27878673398759");
        body.put("solutionSubtype", "sound_box_bind");
//        body.put("leadId", ""); 
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

	}
	
	@Test
	public void verifyStatusCodeWhenScannedQrCodeIsNotMapped() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmqr10ny9f@paytm&pn=Paytm");
        body.put("pgMid", "HySHnd27878673398759");
        body.put("solutionSubtype", "sound_box_bind");
//        body.put("leadId", ""); 
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

	}
	
	@Test
	public void verifyStatusCodeWhenScannedQrCodeIsMappedToDifferentMid() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmqr10nw1j@paytm=Paytm");
        body.put("pgMid", "HySHnd27878673398759");
        body.put("solutionSubtype", "sound_box_bind");
//        body.put("leadId", ""); 
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

	}
	
	
	@Test
	public void verifyStatusCodeWhenScannedQrCodeIsMappedToMIDWhichIsInactive() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmqr10ny9g@paytm&pn=Paytm");
        body.put("pgMid", "rbeIqZ85073930943704");
        body.put("solutionSubtype", "sound_box_bind");
//        body.put("leadId", ""); 
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

	}
	
	
	
	@Test
	public void verifyStatusCodeWhenScannedQrCodeIsMappedToMIDWhichIsAgregator() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmqr10ny9h@paytm&pn=Paytm");
        body.put("pgMid", "SBTest10068671323128");
        body.put("solutionSubtype", "sound_box_bind");
//        body.put("leadId", ""); 
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

	}
	
	@Test
	public void verifyStatusCodeWhenLeadIDIsSentAsEmptyStringInRequest() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmqr10ny9h@paytm&pn=Paytm");
        body.put("pgMid", "SBTest10068671323128");
        body.put("solutionSubtype", "sound_box_bind");
        body.put("leadId", ""); 
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

	}

	
	@Test
	public void verifyStatusCodeWhenDifferentMIDIsSentInRequest() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmocl.s108lyy@axis&pn=Paytm");
        body.put("pgMid", "SBTest10068671323128");
        body.put("solutionSubtype", "sound_box_bind");
        body.put("leadId", ""); 
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

	}

	
	@Test
	public void verifyStatusCodeWhenMIDIsNotSentInRequest() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmocl.s108lyy@axis&pn=Paytm");
        body.put("pgMid", "");
        body.put("solutionSubtype", "sound_box_bind"); 
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

	}
	
	@Test
	public void verifyStatusCodeWhenQRIsNotSentInRequest() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "");
        body.put("pgMid", "HySHnd27878673398759");
        body.put("solutionSubtype", "sound_box_bind"); 
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

	}

	
	@Test
	public void verifyStatusCodeWhenAgentCustIDIsInvalid() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmocl.s108lyy@axis&pn=Paytm");
        body.put("pgMid", "HySHnd27878673398759");
        body.put("solutionSubtype", "sound_box_bind");
//        body.put("leadId", "");
        body.put("agentCustId", "110719573");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 200);

	}
	
	@Test
	public void verifyStatusCodeWhenAgentCustIDIsNotPassedInRequest() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmocl.s108lyy@axis&pn=Paytm");
        body.put("pgMid", "HySHnd27878673398759");
        body.put("solutionSubtype", "sound_box_bind");
//        body.put("leadId", "");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 200);

	}
	
	@Test
	public void verifyStatusCodeWhenRescanKeyIsNotPassedInRequest() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmocl.s108lyy@axis&pn=Paytm");
        body.put("pgMid", "HySHnd27878673398759");
        body.put("solutionSubtype", "sound_box_bind");
//        body.put("leadId", "");
        body.put("agentCustId", "1107195733");
//        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

	}
	
	@Test
	public void verifyStatusCodeWhenRescanIsSentAsTrue() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmocl.s108lyy@axis&pn=Paytm");
        body.put("pgMid", "HySHnd27878673398759");
        body.put("solutionSubtype", "sound_box_bind");
//        body.put("leadId", "");
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "true");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

	}
	
	@Test
	public void verifyStatusCodeWhenSolutionSubTypeIsNotPassedInRequest() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmocl.s108lyy@axis&pn=Paytm");
        body.put("pgMid", "HySHnd27878673398759");
      
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 200);

	}
	
	@Test
	public void verifyStatusCodeWhenContentTypeIsNotPassedInRequest() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");

		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		
        body.put("qrStickerDetails", "upi://pay?pa=paytmocl.s108lyy@xis&pn=Paytm");
        body.put("pgMid", "HySHnd27878673398759");
        body.put("solutionSubtype", "sound_box_bind");
//        body.put("leadId", ""); // Note: HashMap does not accept null values as strings. This might need special handling.
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

	}
	
	@Test
	public void verifyStatusCodeWhenVersionIsNotPassedInRequest() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
//		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmocl.s108lyy@axis&pn=Paytm");
        body.put("pgMid", "HySHnd27878673398759");
        body.put("solutionSubtype", "sound_box_bind");
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.path("message").toString(), "version is empty in header");

	}
	
	@Test
	public void verifyStatusCodeWhenRequestIsModifiedWithoutAccess() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
	
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmocl.s108lyy@axis&pn=Paytm");
        body.put("pgMid", "HySHnd27878673398759");
        body.put("solutionSubtype", "sound_box_bind");
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 412);

	}

	@Test
	public void verifyStatusCodeWhenInvalidSessionTokenIsUsed() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
		headers.put("session_token", "akjdkhdhdajkhsdkadldaladuiey8478ddsh9");
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmocl.s108lyy@axis&pn=Paytm");
        body.put("pgMid", "HySHnd27878673398759");
        body.put("solutionSubtype", "sound_box_bind");
//        body.put("leadId", ""); // Note: HashMap does not accept null values as strings. This might need special handling.
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 410);

	}
	
	@Test
	public void verifyStatusCodeWhenSessionTokenIsNotUSed() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("Host", "goldengate-staging8.paytm.com");
		headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
		headers.put("devicemac", ""); // This header appears to be incomplete or empty
		headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211");
		headers.put("x-src", "GGClient");
		headers.put("x-mw-checksum", "IX5Vl3fFJ5pcTS99P+T6WFSXqhvxErhKjITwKaauFBjex4HKNcXdjVzzNRSGIoiNXLVRynTMkNb8CSfBex4HAhlIGIMZ6xhkKrC/ZQSgo737JKUF2bYGOmVQJSYqonsQ");
		headers.put("applanguage", "en");
		headers.put("sec-ch-ua-platform", "\"Android\"");
		headers.put("isdevicerooted", "false");
//		headers.put("session_token", sessionToken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("isbusyboxfound", "false");
		headers.put("version", "7.2.3");
		headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
		headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
		headers.put("sec-fetch-site", "same-site");
		headers.put("sec-fetch-mode", "cors");
		headers.put("sec-fetch-dest", "empty");
		headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
		headers.put("accept-language", "en,en-US;q=0.9");
		headers.put("priority", "u=1, i");
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
		headers.put("Cookie", "JSESSIONID=F2B8FA16B9FADF3BAE793DA3B178B351");

		
        body.put("qrStickerDetails", "upi://pay?pa=paytmocl.s108lyy@axis&pn=Paytm");
        body.put("pgMid", "HySHnd27878673398759");
        body.put("solutionSubtype", "sound_box_bind");
//        body.put("leadId", ""); // Note: HashMap does not accept null values as strings. This might need special handling.
        body.put("agentCustId", "1107195733");
        body.put("reScanQRCase", "false");
        qr=new SBPreValidatePaymentQRNew();
        
        response=services.prevalidatePaymentQRNew(qr, body, headers);
        Assert.assertEquals(response.getStatusCode(), 401);

	}
}
