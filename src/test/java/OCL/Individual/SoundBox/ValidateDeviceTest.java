package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.ValidateBeatDetails;
import Request.SoundBox.Validatedevice;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class ValidateDeviceTest extends BaseMethod{

	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> body;
	public ValidateBeatDetails beatObj;
	
	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	
	@Test
    public void verify_Status_Code_Returned_In_Case_Of_Error_Recivevd_From_ATS_During_Device_Validation(){
    	headers = new HashMap<>();
        body = new HashMap<>();
        services=new MiddlewareServices();
        
       
        headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
       
        
        body.put("deviceId","8392781329788000000");
        body.put("agentCustId","1107195733");
        Validatedevice validatedeviceobj = new Validatedevice();
        response = services.validatedevice(validatedeviceobj,body,headers);
        Assert.assertEquals(response.getStatusCode(),400);
        
   
    }

	
	@Test
    public void verify_Status_Code_In_Case_Of_Success_OF_Device_Validation(){
    	headers = new HashMap<>();
        body = new HashMap<>();
        services=new MiddlewareServices();
        
        headers.put("version","5.2.0");
        headers.put("X-MW-CHECKSUM-V3","nVt3c3j8bx04HfhKoq099kNiabL0FLZFkJZTCDjB8/bXr/OQ0zb4fafJbUDxe8qsMCWQ7es0Y6w2011oDWrsgnhLbBtF8lyj7c2R35uNSiGHzSncH12IAK7esLJPYPC20GUyF8CPyZ6hA2NSXvA=");
        headers.put("session_token",sessionToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
       
        
        body.put("deviceId","385748394123210");
        body.put("agentCustId","1107195733");
        Validatedevice validatedeviceobj = new Validatedevice();
        response = services.validatedevice(validatedeviceobj,body,headers);
        Assert.assertEquals(response.getStatusCode(),200);
        
   
    }

	
	@Test
    public void verify_Error_Message_When_Scanned_Device_Is_Not_Onboarded_On_ATS(){
    	headers = new HashMap<>();
        body = new HashMap<>();
        services=new MiddlewareServices();
        
        headers.put("version","5.2.0");
        headers.put("X-MW-CHECKSUM-V3","nVt3c3j8bx04HfhKoq099kNiabL0FLZFkJZTCDjB8/bXr/OQ0zb4fafJbUDxe8qsMCWQ7es0Y6w2011oDWrsgnhLbBtF8lyj7c2R35uNSiGHzSncH12IAK7esLJPYPC20GUyF8CPyZ6hA2NSXvA=");
        headers.put("session_token",sessionToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
       
        
        body.put("deviceId","8392781329788000000");
        body.put("agentCustId","1107195733");
        Validatedevice validatedeviceobj = new Validatedevice();
        response = services.validatedevice(validatedeviceobj,body,headers);
        Assert.assertEquals(response.getStatusCode(), 400);
    
	}

	
	@Test
    public void verify_Error_Message_When_Scanned_Device_Is_In_Onboarded_State_On_ATS(){
    	headers = new HashMap<>();
        body = new HashMap<>();
        services=new MiddlewareServices();
        
        headers.put("version","5.2.0");
        headers.put("X-MW-CHECKSUM-V3","nVt3c3j8bx04HfhKoq099kNiabL0FLZFkJZTCDjB8/bXr/OQ0zb4fafJbUDxe8qsMCWQ7es0Y6w2011oDWrsgnhLbBtF8lyj7c2R35uNSiGHzSncH12IAK7esLJPYPC20GUyF8CPyZ6hA2NSXvA=");
        headers.put("session_token",sessionToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
       
        
        body.put("deviceId","385748394123212");
        body.put("agentCustId","1107195733");
        Validatedevice validatedeviceobj = new Validatedevice();
        response = services.validatedevice(validatedeviceobj,body,headers);
        String displayMessage = response.path("displayMessage").toString();
        Assert.assertTrue(displayMessage.contains("This device with serial number 385748394123212 is not available in your ATS inventory. Please transfer this device in your ATS inventory or choose devices from your ATS inventory"));
 
	}
	
//
//	@Test
//    public void verify_Error_Message_When_Scanned_Device_Is_In_Pending_Ack_State_On_ATS(){
//    	headers = new HashMap<>();
//        body = new HashMap<>();
//        services=new MiddlewareServices();
//        
//        headers.put("version","5.2.0");
//        headers.put("X-MW-CHECKSUM-V3","nVt3c3j8bx04HfhKoq099kNiabL0FLZFkJZTCDjB8/bXr/OQ0zb4fafJbUDxe8qsMCWQ7es0Y6w2011oDWrsgnhLbBtF8lyj7c2R35uNSiGHzSncH12IAK7esLJPYPC20GUyF8CPyZ6hA2NSXvA=");
//        headers.put("session_token",sessionToken);
//        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
//        headers.put("Content-Type","application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//       
//        
//        body.put("deviceId","563984423434001");
//        body.put("agentCustId","1107195733");
//        Validatedevice validatedeviceobj = new Validatedevice();
//        response = services.validatedevice(validatedeviceobj,body,headers);
//        String displayMessage = response.path("displayMessage").toString();
//        Assert.assertTrue(displayMessage.contains("This device with serial number 563984423434001 is not available in your ATS inventory."));
//      
//	} only fastag and ncmc sku can be assigned now so this is redundant
	
	@Test
    public void verify_Error_Code_When_Scanned_Device_Is_In_Unmapped_State_On_ATS(){
    	headers = new HashMap<>();
        body = new HashMap<>();
        services=new MiddlewareServices();
        
        headers.put("version","5.2.0");
        headers.put("X-MW-CHECKSUM-V3","nVt3c3j8bx04HfhKoq099kNiabL0FLZFkJZTCDjB8/bXr/OQ0zb4fafJbUDxe8qsMCWQ7es0Y6w2011oDWrsgnhLbBtF8lyj7c2R35uNSiGHzSncH12IAK7esLJPYPC20GUyF8CPyZ6hA2NSXvA=");
        headers.put("session_token",sessionToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
       
        
        body.put("deviceId","385748394123209");
        body.put("agentCustId","1107195733");
        Validatedevice validatedeviceobj = new Validatedevice();
        response = services.validatedevice(validatedeviceobj,body,headers);
        response = services.validatedevice(validatedeviceobj,body,headers);
        Assert.assertEquals(response.getStatusCode(), 400);    
	}
	
	@Test
    public void verify_Error_Code_When_Scanned_Device_Is_In_Deployed_State_On_ATS(){
    	headers = new HashMap<>();
        body = new HashMap<>();
        services=new MiddlewareServices();
        
        headers.put("version","5.2.0");
        headers.put("X-MW-CHECKSUM-V3","nVt3c3j8bx04HfhKoq099kNiabL0FLZFkJZTCDjB8/bXr/OQ0zb4fafJbUDxe8qsMCWQ7es0Y6w2011oDWrsgnhLbBtF8lyj7c2R35uNSiGHzSncH12IAK7esLJPYPC20GUyF8CPyZ6hA2NSXvA=");
        headers.put("session_token",sessionToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
       
        
        body.put("deviceId","385748394123213");
        body.put("agentCustId","1107195733");
        Validatedevice validatedeviceobj = new Validatedevice();
        response = services.validatedevice(validatedeviceobj,body,headers);
        Assert.assertEquals(response.getStatusCode(), 400);
        
	}

	
	@Test
    public void verify_Error_Message_When_Scanned_Device_Is_Available_With_Different_FSE_On_ATS(){
    	headers = new HashMap<>();
        body = new HashMap<>();
        services=new MiddlewareServices();
        
        headers.put("version","5.2.0");
        headers.put("X-MW-CHECKSUM-V3","nVt3c3j8bx04HfhKoq099kNiabL0FLZFkJZTCDjB8/bXr/OQ0zb4fafJbUDxe8qsMCWQ7es0Y6w2011oDWrsgnhLbBtF8lyj7c2R35uNSiGHzSncH12IAK7esLJPYPC20GUyF8CPyZ6hA2NSXvA=");
        headers.put("session_token",sessionToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
       
        
        body.put("deviceId","385748394123212");
        body.put("agentCustId","1107195733");
        Validatedevice validatedeviceobj = new Validatedevice();
        response = services.validatedevice(validatedeviceobj,body,headers);
        Assert.assertEquals(      response.getStatusCode(), 400);
    
	}
	
	@Test
    public void verify_Error_Message_When_Scanned_QR_Is_Not_Soundbox_Device_QR(){
    	headers = new HashMap<>();
        body = new HashMap<>();
        services=new MiddlewareServices();
        
        headers.put("version","5.2.0");
        headers.put("X-MW-CHECKSUM-V3","nVt3c3j8bx04HfhKoq099kNiabL0FLZFkJZTCDjB8/bXr/OQ0zb4fafJbUDxe8qsMCWQ7es0Y6w2011oDWrsgnhLbBtF8lyj7c2R35uNSiGHzSncH12IAK7esLJPYPC20GUyF8CPyZ6hA2NSXvA=");
        headers.put("session_token",sessionToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
       
        
        body.put("deviceId","qbc");
        body.put("agentCustId","1107195733");
        Validatedevice validatedeviceobj = new Validatedevice();
        response = services.validatedevice(validatedeviceobj,body,headers);
        String displayMessage = response.path("displayMessage").toString();
        Assert.assertTrue(displayMessage.contains("Incorrect QR Scanned, Please scan the QR or Barcode at the back of the device"));
    
	}
	
	@Test
    public void verify_Success_Message_When_Scanned_Device_Is_In_FSE_Inventory_On_ATS(){
    	headers = new HashMap<>();
        body = new HashMap<>();
        services=new MiddlewareServices();
        
        headers.put("version","5.2.0");
        headers.put("X-MW-CHECKSUM-V3","nVt3c3j8bx04HfhKoq099kNiabL0FLZFkJZTCDjB8/bXr/OQ0zb4fafJbUDxe8qsMCWQ7es0Y6w2011oDWrsgnhLbBtF8lyj7c2R35uNSiGHzSncH12IAK7esLJPYPC20GUyF8CPyZ6hA2NSXvA=");
        headers.put("session_token",sessionToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
       
        
        body.put("deviceId","385748394123210");
        body.put("agentCustId","1107195733");
        Validatedevice validatedeviceobj = new Validatedevice();
        response = services.validatedevice(validatedeviceobj,body,headers);
        String displayMessage = response.path("deviceType").toString();
        Assert.assertTrue(displayMessage.contains("sound_box_3_0_4g"));
    
	}
	
	@Test
    public void verify_Device_Type_Returned_Is_Correct_Device_Type(){
    	headers = new HashMap<>();
        body = new HashMap<>();
        services=new MiddlewareServices();
        
        headers.put("version","5.2.0");
        headers.put("X-MW-CHECKSUM-V3","nVt3c3j8bx04HfhKoq099kNiabL0FLZFkJZTCDjB8/bXr/OQ0zb4fafJbUDxe8qsMCWQ7es0Y6w2011oDWrsgnhLbBtF8lyj7c2R35uNSiGHzSncH12IAK7esLJPYPC20GUyF8CPyZ6hA2NSXvA=");
        headers.put("session_token",sessionToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
       
        
        body.put("deviceId","385748394123210");
        body.put("agentCustId","1107195733");
        Validatedevice validatedeviceobj = new Validatedevice();
        response = services.validatedevice(validatedeviceobj,body,headers);
        String displayMessage = response.path("deviceType").toString();
        Assert.assertTrue(displayMessage.contains("sound_box_3_0_4g"));
    
	}
	

	@Test
    public void verify_Error_When_Device_Id_is_Empty(){
    	headers = new HashMap<>();
        body = new HashMap<>();
        services=new MiddlewareServices();
        
        headers.put("version","5.2.0");
        headers.put("X-MW-CHECKSUM-V3","nVt3c3j8bx04HfhKoq099kNiabL0FLZFkJZTCDjB8/bXr/OQ0zb4fafJbUDxe8qsMCWQ7es0Y6w2011oDWrsgnhLbBtF8lyj7c2R35uNSiGHzSncH12IAK7esLJPYPC20GUyF8CPyZ6hA2NSXvA=");
        headers.put("session_token",sessionToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
       
        
        body.put("deviceId","");
        body.put("agentCustId","1107195733");
        Validatedevice validatedeviceobj = new Validatedevice();
        response = services.validatedevice(validatedeviceobj,body,headers);
        response = services.validatedevice(validatedeviceobj,body,headers);
        Assert.assertEquals(response.getStatusCode(), 400);
 
    
	}
	
	@Test
    public void verify_Error_When_Agent_Id_is_Empty(){
    	headers = new HashMap<>();
        body = new HashMap<>();
        services=new MiddlewareServices();
        
        headers.put("version","5.2.0");
        headers.put("X-MW-CHECKSUM-V3","nVt3c3j8bx04HfhKoq099kNiabL0FLZFkJZTCDjB8/bXr/OQ0zb4fafJbUDxe8qsMCWQ7es0Y6w2011oDWrsgnhLbBtF8lyj7c2R35uNSiGHzSncH12IAK7esLJPYPC20GUyF8CPyZ6hA2NSXvA=");
        headers.put("session_token",sessionToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
       
        
        body.put("deviceId","563747466007");
        body.put("agentCustId","");
        Validatedevice validatedeviceobj = new Validatedevice();
        response = services.validatedevice(validatedeviceobj,body,headers);
        response = services.validatedevice(validatedeviceobj,body,headers);
        Assert.assertEquals(response.getStatusCode(), 400);
   	}
	
	@Test
    public void verify_Error_Code_If_Required_Header_Empty(){
    	headers = new HashMap<>();
        body = new HashMap<>();
        services=new MiddlewareServices();
        
        headers.put("version","");
        headers.put("X-MW-CHECKSUM-V3","nVt3c3j8bx04HfhKoq099kNiabL0FLZFkJZTCDjB8/bXr/OQ0zb4fafJbUDxe8qsMCWQ7es0Y6w2011oDWrsgnhLbBtF8lyj7c2R35uNSiGHzSncH12IAK7esLJPYPC20GUyF8CPyZ6hA2NSXvA=");
        headers.put("session_token",sessionToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
       
        
        body.put("deviceId","563747466007");
        body.put("agentCustId","");
        Validatedevice validatedeviceobj = new Validatedevice();
        response = services.validatedevice(validatedeviceobj,body,headers);
        String displayMessage = response.path("displayMessage").toString();
        Assert.assertTrue(displayMessage.contains("version is empty in header"));
 	}
	
	
	@Test
    public void verify_Status_Code_Returned_In_Case_Of_Token_Is_Not_Passed_In_Request(){
    	headers = new HashMap<>();
        body = new HashMap<>();
        services=new MiddlewareServices();
        
       
        headers.put("session_token", "");
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
       
        
        body.put("deviceId","8392781329788000000");
        body.put("agentCustId","1107195733");
        Validatedevice validatedeviceobj = new Validatedevice();
        response = services.validatedevice(validatedeviceobj,body,headers);
        Assert.assertEquals(response.getStatusCode(),401);
        
   
    }


}