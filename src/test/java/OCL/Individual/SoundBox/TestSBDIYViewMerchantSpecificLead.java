package OCL.Individual.SoundBox;


import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.SBDIYViewMerchantSpecificLead;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestSBDIYViewMerchantSpecificLead extends BaseMethod {
	
	SBDIYViewMerchantSpecificLead leads;
	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> params;

	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	@Test
	public void verify_Status_Code_Leads_Are_Fetched_Successfully_Using_Merchant_Mobile() {
		leads=new SBDIYViewMerchantSpecificLead();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("solutionType", "diy_sound_box");
        params.put("merchantMobile", "7771110136");
        
        params.put("page", "0");
        params.put("limit", "10");
        
        response=services.SBViewMerchantSpecificLead(leads, params, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 200);
	}
	
	@Test
	public void verify_Status_Code_Leads_Are_Fetched_Successfully_Using_Order_ID() {
		leads=new SBDIYViewMerchantSpecificLead();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("solutionType", "diy_sound_box");
        params.put("orderId", "100075911293");
        
        params.put("page", "0");
        params.put("limit", "10");
        
        response=services.SBViewMerchantSpecificLead(leads, params, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 200);
	}
	
	@Test
	public void verify_Leads_Specific_To_Merchannt_Are_Fetched_Using_Merchant_Mobile() {
		leads=new SBDIYViewMerchantSpecificLead();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("solutionType", "diy_sound_box");
        params.put("merchantMobile", "7771110136");
        
        params.put("page", "0");
        params.put("limit", "10");
        
        response=services.SBViewMerchantSpecificLead(leads, params, headers);	
        Assert.assertEquals(response.getStatusCode(), 200);
	}
	

	@Test
	public void verify_Leads_Specific_To_OrderID_Are_Fetched_Using_Order() {
		leads=new SBDIYViewMerchantSpecificLead();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("solutionType", "diy_sound_box");
        params.put("orderId", "100075911293");
        
        params.put("page", "0");
        params.put("limit", "10");
        
        response=services.SBViewMerchantSpecificLead(leads, params, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 200);
	}

	
	@Test
	public void verify_All_Allocated_Leads_Are_Fetched_If_Mobild_Number_Is_Sent_Empty() {
		leads=new SBDIYViewMerchantSpecificLead();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("solutionType", "diy_sound_box");
        params.put("merchantMobile", "");
        
        params.put("page", "0");
        params.put("limit", "10");
        
        response=services.SBViewMerchantSpecificLead(leads, params, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 200);
	}
	
	
	
	@Test
	public void verify_All_Allocated_Leads_Are_Fetched_If_OrderID_Is_Sent_Empty() {
		leads=new SBDIYViewMerchantSpecificLead();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("solutionType", "diy_sound_box");
        params.put("orderId", "");
        
        params.put("page", "0");
        params.put("limit", "10");
        
        response=services.SBViewMerchantSpecificLead(leads, params, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 200);
	}

	
	
	@Test
	public void verify_No_Leads_Are_Fetched_If_Mobild_Number_Leads_Are_Assigned_To_Different_FSE() {
		leads=new SBDIYViewMerchantSpecificLead();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("solutionType", "diy_sound_box");
        params.put("merchantMobile", "7771110999");
        
        params.put("page", "0");
        params.put("limit", "10");
        
        response=services.SBViewMerchantSpecificLead(leads, params, headers);	
       
        String isNull="";
        try {
        	isNull=response.path("allocatedLeads[0].leadId").toString();
        	}
        	catch(NullPointerException e) {
        		
        	}
        Assert.assertTrue(isNull.isEmpty());
	}
	
	
	@Test
	public void verify_No_Leads_Are_Fetched_If_Order_Id_Is_Assigned_To_Different_FSE() {
		leads=new SBDIYViewMerchantSpecificLead();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("solutionType", "diy_sound_box");
        params.put("orderId", "10007591173");
        
        params.put("page", "0");
        params.put("limit", "10");
        
        response=services.SBViewMerchantSpecificLead(leads, params, headers);	
       
        String isNull="";
        try {
        	isNull=response.path("allocatedLeads[0].leadId").toString();
        	}
        	catch(NullPointerException e) {
        		
        	}
        Assert.assertTrue(isNull.isEmpty());
	}
	
	
	@Test
	public void verify_Error_Code_If_Solution_Type_Is_Incorrect() {
		leads=new SBDIYViewMerchantSpecificLead();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("solutionType", "diy_sound_bo");
        params.put("orderId", "10007591173");
        
        params.put("page", "0");
        params.put("limit", "10");
        
        response=services.SBViewMerchantSpecificLead(leads, params, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 500);
       
	}
	
	
	
	@Test
	public void verify_Error_Code_If_Page_Value_Is_Incorrect() {
		leads=new SBDIYViewMerchantSpecificLead();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("solutionType", "diy_sound_bo");
        params.put("orderId", "10007591173");
        
        params.put("page", "-1");
        params.put("limit", "10");
        
        response=services.SBViewMerchantSpecificLead(leads, params, headers);	
       
        Assert.assertEquals(response.path("statusCode").toString(), "500");
       
	}
	
	
	@Test
	public void verify_Error_Code_If_Limit_Value_Is_Incorrect() {
		leads=new SBDIYViewMerchantSpecificLead();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("solutionType", "diy_sound_bo");
        params.put("orderId", "10007591173");
        
        params.put("page", "0");
        params.put("limit", "-1");
        
        response=services.SBViewMerchantSpecificLead(leads, params, headers);	
       
        Assert.assertEquals(response.path("statusCode").toString(), "500");
       
	}
	
	@Test
	public void verify_Error_Code_If_Solution_Type__Missing() {
		leads=new SBDIYViewMerchantSpecificLead();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
        params.put("orderId", "10007591173");
        
        params.put("page", "0");
        params.put("limit", "10");
        
        response=services.SBViewMerchantSpecificLead(leads, params, headers);	
       
        Assert.assertEquals(response.statusCode(), 400);
       
	}
	
	
	@Test
	public void verify_Error_Code_If_Page_Field__Missing() {
		leads=new SBDIYViewMerchantSpecificLead();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("solutionType", "diy_sound_bo");
        params.put("orderId", "10007591173");
        
    
        params.put("limit", "10");
        
        response=services.SBViewMerchantSpecificLead(leads, params, headers);	
       
        Assert.assertEquals(response.statusCode(), 500);
       
	}
	
	
	
	@Test
	public void verify_Error_Code_If_Limit_Field__Missing() {
		leads=new SBDIYViewMerchantSpecificLead();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("solutionType", "diy_sound_bo");
        params.put("orderId", "10007591173");
        
        params.put("page", "0");
 
        
        response=services.SBViewMerchantSpecificLead(leads, params, headers);	
       
        Assert.assertEquals(response.statusCode(), 500);
       
	}
	
	
	@Test
	public void Verify_Error_Code_If_Token_Is_Missing() {
		leads=new SBDIYViewMerchantSpecificLead();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
	
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("solutionType", "diy_sound_box");
        params.put("merchantMobile", "7771110136");
        
        params.put("page", "0");
        params.put("limit", "10");
        
        response=services.SBViewMerchantSpecificLead(leads, params, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 401);
	}

	
	
	@Test
	public void Verify_Error_Message_If_Version_Is_Missing() {
		leads=new SBDIYViewMerchantSpecificLead();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
	
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("solutionType", "diy_sound_box");
        params.put("merchantMobile", "7771110136");
        
        params.put("page", "0");
        params.put("limit", "10");
        
        response=services.SBViewMerchantSpecificLead(leads, params, headers);	
       
        Assert.assertEquals(response.path("message"), "version is empty in header");
	}
}