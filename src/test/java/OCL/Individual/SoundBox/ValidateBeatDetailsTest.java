package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.ValidateBeatDetails;
import Services.MechantService.MiddlewareServices;
import io.restassured.RestAssured;
import io.restassured.parsing.Parser;
import io.restassured.response.Response;

public class ValidateBeatDetailsTest  extends BaseMethod{

	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> body;
	public ValidateBeatDetails beatObj;
	
	@BeforeClass
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		RestAssured.defaultParser=Parser.JSON;
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	@Test
	public void verify_Display_Message_When_SelectedDevice_And_BeatDevice_Are_Same() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("fseBeatTagId", "234645854321");
        body.put("fseBeatMappingId", "24425261008");
        body.put("fseBeatDeviceId", "56374746600332");
        body.put("selectedDeviceId", "56374746600332");
        body.put("solutionType", "sound_box");
        body.put("solutionTypelevel2", "sound_box_replacement");
        
        beatObj=new ValidateBeatDetails();
        response=services.validateBeat(beatObj, body, headers);
        Assert.assertTrue( response.path("displayMessage").toString().contains("Beat validation successful"));
	}
	
	@Test
	public void verify_Prompt_Required_Is_False_When_Beat_Device_Id_And_Selected_Device_Id_Are_Same() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("fseBeatTagId", "234645854321");
        body.put("fseBeatMappingId", "24425261008");
        body.put("fseBeatDeviceId", "56374746600332");
        body.put("selectedDeviceId", "56374746600332");
        body.put("solutionType", "sound_box");
        body.put("solutionTypelevel2", "sound_box_replacement");
        
        beatObj=new ValidateBeatDetails();
        response=services.validateBeat(beatObj, body, headers);
        Assert.assertEquals( response.path("promptRequired").toString(),"false");
	}
	
	
	
	@Test
	public void verify_Display_Message_And_Status_Code_When_Beat_Already_Exists_For_Selected_Device() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("fseBeatTagId", "234645854321");
        body.put("fseBeatMappingId", "5676665605118");
        body.put("fseBeatDeviceId", "56374746600332");
        body.put("selectedDeviceId", "563747466007");
        body.put("solutionType", "sound_box");
        body.put("solutionTypelevel2", "sound_box_replacement");
        
        beatObj=new ValidateBeatDetails();
        response=services.validateBeat(beatObj, body, headers);
        Assert.assertEquals(response.getStatusCode(), 200);
	}

	@Test
	public void verify_Display_Message_Contains_DeviceID_When_Beat_Already_Exists_For_Selected_Device() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("fseBeatTagId", "234645854321");
        body.put("fseBeatMappingId", "5676665605118");
        body.put("fseBeatDeviceId", "56374746600332");
        body.put("selectedDeviceId", "56374746600334");
        body.put("solutionType", "sound_box");
        body.put("solutionTypelevel2", "sound_box_replacement");
        
        beatObj=new ValidateBeatDetails();
        response=services.validateBeat(beatObj, body, headers);
        Assert.assertTrue( response.path("statusCode").toString().contains("400"));
	}
	
	@Test
	public void verify_Prompt_Required_Is_False_In_Case_Device_Beat_Already_Exists() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("fseBeatTagId", "234645854321");
        body.put("fseBeatMappingId", "5676665605118");
        body.put("fseBeatDeviceId", "56374746600332");
        body.put("selectedDeviceId", "56374746600333");
        body.put("solutionType", "sound_box");
        body.put("solutionTypelevel2", "sound_box_replacement");
        
        beatObj=new ValidateBeatDetails();
        response=services.validateBeat(beatObj, body, headers);
        Assert.assertTrue( response.asString().contains("promptRequired"));
	}

	
	
	@Test
	public void verify_Prompt_Message_Is_Displayed_When_SelectedDevice_And_BeatDevice_Are_Different() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("fseBeatTagId", "234645854321");
        body.put("fseBeatMappingId", "24425261008");
        body.put("fseBeatDeviceId", "56374746600332");
        body.put("selectedDeviceId", "56374746600333");
        body.put("solutionType", "sound_box");
        body.put("solutionTypelevel2", "sound_box_replacement");
        
        beatObj=new ValidateBeatDetails();
        response=services.validateBeat(beatObj, body, headers);
        Assert.assertEquals( response.path("displayMessage").toString().isEmpty(),false);
	}
	
	@Test
	public void verify_Prompt_Message_Contains_Selected_And_Beat_Device_Id() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        body.put("fseBeatTagId", "234645854321");
        body.put("fseBeatMappingId", "24425261008");
        body.put("fseBeatDeviceId", "56374746600332");
        body.put("selectedDeviceId", "56374746600334");
        body.put("solutionType", "sound_box");
        body.put("solutionTypelevel2", "sound_box_replacement");
        
        beatObj=new ValidateBeatDetails();
        response=services.validateBeat(beatObj, body, headers);
        Assert.assertTrue( response.asString().contains("promptRequired"));

	}
	@Test
	public void verify_Beat_Validation_Is_Working_Fine_For_Device_Unbind() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("fseBeatTagId", "234645854321");
        body.put("fseBeatMappingId", "5676665605118");
        body.put("fseBeatDeviceId", "56374746600332");
        body.put("selectedDeviceId", "563747466007");
        body.put("solutionType", "sound_box");
        body.put("solutionTypelevel2", "sound_box_unbind");
        
        beatObj=new ValidateBeatDetails();
        response=services.validateBeat(beatObj, body, headers);
     
        Assert.assertEquals(response.getStatusCode(), 200);
	}
	
	@Test
	public void verify_Status_Code_Returned_In_Case_Of_Token_Is_Not_Passed_In_Request() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", "");
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("fseBeatTagId", "234645854321");
        body.put("fseBeatMappingId", "24425261008");
        body.put("fseBeatDeviceId", "56374746600332");
        body.put("selectedDeviceId", "56374746600332");
        body.put("solutionType", "sound_box");
        body.put("solutionTypelevel2", "sound_box_replacement");
        
        beatObj=new ValidateBeatDetails();
        response=services.validateBeat(beatObj, body, headers);
        Assert.assertEquals(response.getStatusCode(),401);

	}
	
	@Test
	public void verify_Status_Code_Returned_In_Case_Of_BeatTagID_Is_Not_Passed_In_Request() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("fseBeatTagId", "");
        body.put("fseBeatMappingId", "24425261008");
        body.put("fseBeatDeviceId", "56374746600332");
        body.put("selectedDeviceId", "56374746600332");
        body.put("solutionType", "sound_box");
        body.put("solutionTypelevel2", "sound_box_replacement");
        
        beatObj=new ValidateBeatDetails();
        response=services.validateBeat(beatObj, body, headers);
        Assert.assertEquals(response.getStatusCode(),400);

	}

	@Test
	public void verify_Status_Code_Returned_In_Case_Of_BeatMappingID_Is_Not_Passed_In_Request() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("fseBeatTagId", "234645854321");
        body.put("fseBeatMappingId", "");
        body.put("fseBeatDeviceId", "56374746600332");
        body.put("selectedDeviceId", "56374746600332");
        body.put("solutionType", "sound_box");
        body.put("solutionTypelevel2", "sound_box_replacement");
        
        beatObj=new ValidateBeatDetails();
        response=services.validateBeat(beatObj, body, headers);
        Assert.assertEquals(response.getStatusCode(),400);

	}

	@Test
	public void verify_Status_Code_Returned_In_Case_Of_BeatDeviceID_Is_Not_Passed_In_Request() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("fseBeatTagId", "234645854321");
        body.put("fseBeatMappingId", "24425261008");
        body.put("fseBeatDeviceId", "");
        body.put("selectedDeviceId", "56374746600332");
        body.put("solutionType", "sound_box");
        body.put("solutionTypelevel2", "sound_box_replacement");
        
        beatObj=new ValidateBeatDetails();
        response=services.validateBeat(beatObj, body, headers);
        Assert.assertEquals(response.getStatusCode(),400);

	}

	@Test
	public void verify_Status_Code_Returned_In_Case_Of_SelectDeviceID_Is_Not_Passed_In_Request() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        body.put("fseBeatTagId", "234645854321");
        body.put("fseBeatMappingId", "24425261008");
        body.put("fseBeatDeviceId", "56374746600332");
        body.put("selectedDeviceId", "");
        body.put("solutionType", "sound_box");
        body.put("solutionTypelevel2", "sound_box_replacement");
        
        beatObj=new ValidateBeatDetails();
        response=services.validateBeat(beatObj, body, headers);
        Assert.assertEquals(response.getStatusCode(),400);

	}

	
	@Test
	public void verify_Response_Code_If_Token_Is_Not_Passed() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
//		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        body.put("fseBeatTagId", "234645854321");
        body.put("fseBeatMappingId", "24425261008");
        body.put("fseBeatDeviceId", "56374746600332");
        body.put("selectedDeviceId", "56374746600334");
        body.put("solutionType", "sound_box");
        body.put("solutionTypelevel2", "sound_box_replacement");
        
        beatObj=new ValidateBeatDetails();
        response=services.validateBeat(beatObj, body, headers);
        Assert.assertEquals(response.getStatusCode(), 401);

	}

	

}