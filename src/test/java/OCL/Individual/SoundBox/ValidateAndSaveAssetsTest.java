package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.ValidateAndSaveAssets;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class ValidateAndSaveAssetsTest extends BaseMethod{
	
	// This test is to be run on Staging6 environment

	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> body;
	public ValidateAndSaveAssets assets;
	
	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	@Test
	public void verify_Error_Message_When_Old_Battery_Is_Not_Present_On_ATS() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_OLD_BATTERY");
        body.put("deviceId","BATT690000");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
        
	}
	

	@Test
	public void verify_Error_Message_When_Old_Battery_Is_Present_On_ATS_In_Onboarded_State() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_OLD_BATTERY");
        body.put("deviceId","BATT602");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_Old_Battery_Is_Present_On_ATS_In_Pending_Ack() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_OLD_BATTERY");
        body.put("deviceId","BATT607");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_Old_Battery_Is_Present_On_ATS_In_Different_FSE_Inventory() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_OLD_BATTERY");
        body.put("deviceId","BATT608");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_Old_Battery_Is_Present_On_ATS_In_Already_Deployed_State() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_OLD_BATTERY");
        body.put("deviceId","BATT609");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_Old_Battery_Is_Present_On_ATS_In_Unmapped_State() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_OLD_BATTERY");
        body.put("deviceId","BATT610");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	
	
	
	@Test
	public void verify_Error_Message_When_Old_Charger_Is_Not_Present_On_ATS() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_OLD_CHARGER");
        body.put("deviceId","CGR690000");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_Old_Charger_Is_Present_On_ATS_In_Onboarded_State() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_OLD_CHARGER");
        body.put("deviceId","CGR602");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_Old_Charger_Is_Present_On_ATS_In_Pending_Ack() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_OLD_CHARGER");
        body.put("deviceId","CGR607");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}

	@Test
	public void verify_Error_Message_When_Old_Charger_Is_Present_On_ATS_In_Different_FSE_Inventory() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_OLD_CHARGER");
        body.put("deviceId","CGR608");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_Old_Charger_Is_Present_On_ATS_In_Already_Deployed_State() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_OLD_Charger");
        body.put("deviceId","CGR609");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_Old_Charger_Is_Present_On_ATS_In_Unmapped_State() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_OLD_CHARGER");
        body.put("deviceId","CGR610");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_NEW_Battery_Is_Not_Present_On_ATS() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_NEW_BATTERY");
        body.put("deviceId","BATT690000");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	

	@Test
	public void verify_Error_Message_When_NEW_Battery_Is_Present_On_ATS_In_Onboarded_State() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_NEW_BATTERY");
        body.put("deviceId","BATT602");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_NEW_Battery_Is_Present_On_ATS_In_Pending_Ack() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_NEW_BATTERY");
        body.put("deviceId","BATT607");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_NEW_Battery_Is_Present_On_ATS_In_Different_FSE_Inventory() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_NEW_BATTERY");
        body.put("deviceId","BATT608");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_NEW_Battery_Is_Present_On_ATS_In_Already_Deployed_State() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_NEW_BATTERY");
        body.put("deviceId","BATT609");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_NEW_Battery_Is_Present_On_ATS_In_Unmapped_State() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_NEW_BATTERY");
        body.put("deviceId","BATT610");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	
	
	
	@Test
	public void verify_Error_Message_When_NEW_Charger_Is_Not_Present_On_ATS() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_NEW_CHARGER");
        body.put("deviceId","CGR690000");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_NEW_Charger_Is_Present_On_ATS_In_Onboarded_State() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_NEW_CHARGER");
        body.put("deviceId","CGR602");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_NEW_Charger_Is_Present_On_ATS_In_Pending_Ack() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_NEW_CHARGER");
        body.put("deviceId","CGR607");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}

	@Test
	public void verify_Error_Message_When_NEW_Charger_Is_Present_On_ATS_In_Different_FSE_Inventory() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_NEW_CHARGER");
        body.put("deviceId","CGR608");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_NEW_Charger_Is_Present_On_ATS_In_Already_Deployed_State() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_NEW_Charger");
        body.put("deviceId","CGR609");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	@Test
	public void verify_Error_Message_When_NEW_Charger_Is_Present_On_ATS_In_Unmapped_State() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
	
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        



        body.put("assetAction","SCAN_NEW_CHARGER");
        body.put("deviceId","CGR610");
        body.put("userMobile","6665550217");
        body.put("mid","DHVRVL02894215991065");
        body.put("entityType","INDIVIDUAL");
        body.put("leadId","e0face24-9362-4ec0-8bf5-51522bba8141");
        body.put("merchantName","TestBeneficiary");
        body.put("userCustId","1001373711");
        body.put("agentCustId","1001647902");
        body.put("solutionSubType","sound_box");
	

        services=new MiddlewareServices();
        assets=new ValidateAndSaveAssets();
        
        
        response=services.validateNSaveAsset(assets, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);

        
	}
	
	
	

}