package OCL.Individual.SoundBox.base;

import OCL.Individual.SoundBox.base.SoundBoxConstants;
import Services.MechantService.MiddlewareServices;
import Request.SoundBox.*;
import Request.MerchantService.v3.*;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import com.paytm.apitools.core.P;

import java.util.HashMap;
import java.util.Map;

/**
 * Utility class containing all helper methods for SoundBox tests
 * Organized by functional category for easy maintenance
 */
public class SoundBoxTestUtils {
    
    private static final Logger LOGGER = LogManager.getLogger(SoundBoxTestUtils.class);
    
    // ==================== AUTHENTICATION HELPERS ====================
    
    /**
     * Executes Send OTP API call with retry logic
     * @param middlewareServices Middleware service instance
     * @param entityType Entity type parameter
     * @param solutionType Solution type parameter
     * @param agentToken Agent token
     * @param version Version parameter
     * @param mobile Mobile number
     * @param userType User type
     * @param expectedStatusCode Expected HTTP status code
     * @param retryCount Current retry count
     * @return Response object
     */
    public static Response executeSendOtp(MiddlewareServices middlewareServices, String entityType, 
            String solutionType, String agentToken, String version, String mobile, String userType, 
            int expectedStatusCode, int retryCount) {
        
        Map<String, String> headers = createStandardHeaders(agentToken, version);
        Map<String, String> queryParams = createQueryParams(entityType, solutionType);
        
        Map<String, String> body = new HashMap<>();
        body.put("mobile", mobile);
        body.put("userType", userType);
        body.put("individaulMerchantKyc", "false");
        body.put("individualSolutionType", "");
        body.put("onlyValidateOtp", "false");
        body.put("skipOtp", "false");
        body.put("tncVersion", "");
        body.put("tncSetName", "");
        
        SendOtp sendOtpObj = new SendOtp();
        Response response = middlewareServices.sendOtp(sendOtpObj, queryParams, headers, body);
        
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, expectedStatusCode);
        
        return response;
    }
    
    /**
     * Executes OTP validation API call
     * @param middlewareServices Middleware service instance
     * @param otp OTP value
     * @param state State value
     * @param mobile Mobile number
     * @param agentToken Agent token
     * @param version Version parameter
     * @param expectedStatus Expected HTTP status code
     * @return Response object
     */
    public static Response executeOtpValidation(MiddlewareServices middlewareServices, String otp, 
            String state, String mobile, String agentToken, String version, int expectedStatus) {
        
        Map<String, String> headers = createStandardHeaders(agentToken, version);
        Map<String, String> body = createOtpValidationBody(otp, state, mobile);
        
        ValidateOtp validateOtpObj = new ValidateOtp();
        Response response = middlewareServices.validateOtp(validateOtpObj, headers, body);
        
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, expectedStatus);
        
        return response;
    }
    
    // ==================== CONTENT HELPERS ====================
    
    /**
     * Executes YouTube link fetch test
     * @param middlewareServices Middleware service instance
     * @param agentToken Agent authentication token
     * @param version Version header value
     * @param expectedStatus Expected HTTP status code
     * @return Response object
     */
    public static Response executeYoutubeLinkFetch(MiddlewareServices middlewareServices, 
            String agentToken, String version, int expectedStatus) {
        
        Map<String, String> headers = new HashMap<>();
        headers.put("version", version);
        headers.put("session_token", agentToken);
        headers.put("deviceIdentifier", SoundBoxConstants.DEVICE_IDENTIFIER);
        headers.put("appLanguage", SoundBoxConstants.APP_LANGUAGE);
        headers.put("Content-Type", SoundBoxConstants.CONTENT_TYPE);
        
        SBFetchYoutubeLink youtubeLinkObj = new SBFetchYoutubeLink();
        Response response = middlewareServices.sbFetchYoutubeLink(youtubeLinkObj, headers);
        
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, expectedStatus);
        
        return response;
    }
    
    // ==================== DEVICE HELPERS ====================
    
    /**
     * Executes device validation test
     * @param middlewareServices Middleware service instance
     * @param agentToken Agent authentication token
     * @param version Version header value
     * @param expectedStatus Expected HTTP status code
     * @return Response object
     */
    public static Response executeDeviceValidation(MiddlewareServices middlewareServices, 
            String agentToken, String version, int expectedStatus) {
        
        Map<String, String> headers = createStandardHeaders(agentToken, version);
        headers.put("X-MW-URL-CHECKSUM-V3", SoundBoxConstants.CHECKSUM_V3);
        headers.put("UncleScrooge", SoundBoxConstants.UNCLE_SCROOGE);
        
        DeviceValidation deviceValidationObj = new DeviceValidation();
        Response response = middlewareServices.deviceValidation(deviceValidationObj, headers);
        
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, expectedStatus);
        
        return response;
    }
    
    /**
     * Executes IoT device retrieval test
     * @param middlewareServices Middleware service instance
     * @param agentToken Agent authentication token
     * @param version Version header value
     * @param deviceIdentifier Device identifier
     * @param includeChecksum Whether to include checksum header
     * @param expectedStatus Expected HTTP status code
     * @return Response object
     */
    public static Response executeIotDeviceRetrieval(MiddlewareServices middlewareServices, 
            String agentToken, String version, String deviceIdentifier, boolean includeChecksum, int expectedStatus) {
        
        Map<String, String> headers = new HashMap<>();
        headers.put("version", version);
        headers.put("session_token", agentToken);
        headers.put("deviceIdentifier", deviceIdentifier);
        headers.put("appLanguage", SoundBoxConstants.APP_LANGUAGE);
        headers.put("Content-Type", SoundBoxConstants.CONTENT_TYPE);
        
        if (includeChecksum) {
            headers.put("X-MW-URL-CHECKSUM-V3", SoundBoxConstants.IOT_CHECKSUM);
        }
        
        FetchDevicedIot getDevicesIotObj = new FetchDevicedIot();
        Response response = middlewareServices.fetchdevices(getDevicesIotObj, headers);
        
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, expectedStatus);
        
        return response;
    }
    
    // ==================== MERCHANT HELPERS ====================
    
    /**
     * Executes merchant ID retrieval test
     * @param middlewareServices Middleware service instance
     * @param merchantCustId Merchant customer ID
     * @param agentToken Agent authentication token
     * @param version Version header value
     * @param expectedStatus Expected HTTP status code
     * @return Response object
     */
    public static Response executeMerchantIdRetrieval(MiddlewareServices middlewareServices, 
            String merchantCustId, String agentToken, String version, int expectedStatus) {
        
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        
        queryParams.put("merchantCustId", merchantCustId);
        queryParams.put("SOLUTION_TYPE", SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX);
        queryParams.put("solutionSubType", SoundBoxConstants.SOLUTION_SUB_TYPE_BIND);
        
        headers.put("VERSION", version);
        headers.put("session_token", agentToken);
        headers.put("deviceIdentifier", SoundBoxConstants.DEVICE_IDENTIFIER);
        headers.put("appLanguage", SoundBoxConstants.APP_LANGUAGE);
        headers.put("Content-Type", SoundBoxConstants.CONTENT_TYPE);
        
        GetMerchantid getMerchantIdObj = new GetMerchantid();
        Response response = middlewareServices.getmerchant(getMerchantIdObj, queryParams, headers);
        
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, expectedStatus);
        
        return response;
    }
    
    // ==================== LEAD HELPERS ====================
    
    /**
     * Executes SoundBox lead creation test
     * @param middlewareServices Middleware service instance
     * @param agentCustId Agent customer ID
     * @param merchantName Merchant name
     * @param mid Merchant ID
     * @param userCustId User customer ID
     * @param userMobile User mobile number
     * @param agentToken Agent authentication token
     * @param version Version header value
     * @param expectedStatus Expected HTTP status code
     * @return Response object
     */
    public static Response executeSoundBoxLeadCreation(MiddlewareServices middlewareServices, 
            String agentCustId, String merchantName, String mid, String userCustId, String userMobile, 
            String agentToken, String version, int expectedStatus) {
        
        soundbox soundboxObj = new soundbox(P.TESTDATA.get("v1SoundBox"));
        
        Map<String, String> body = new HashMap<>();
        body.put("agentCustId", agentCustId);
        body.put("merchantName", merchantName);
        body.put("mid", mid);
        body.put("userCustId", userCustId);
        body.put("userMobile", userMobile);
        
        Response response = middlewareServices.v1SoundBoxCreateLead(soundboxObj, body, agentToken, version);
        
        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, expectedStatus);
        
        return response;
    }

    /**
     * Executes MID fetch test
     * @param middlewareServices Middleware service instance
     * @param agentToken Agent authentication token
     * @param version Version header value
     * @param custId Customer ID
     * @param solutionType Solution type
     * @param expectedStatus Expected HTTP status code
     * @return Response object
     */
    public static Response executeMidFetch(MiddlewareServices middlewareServices, String agentToken,
            String version, String custId, String solutionType, int expectedStatus) {

        MID v3FetchMid = new MID();
        Response response = middlewareServices.v3FetchMID(v3FetchMid, agentToken, version, custId, solutionType);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, expectedStatus);

        return response;
    }

    /**
     * Executes YouTube link fetch with custom headers
     * @param middlewareServices Middleware service instance
     * @param agentToken Agent authentication token
     * @param version Version header value
     * @param deviceIdentifier Device identifier (can be empty)
     * @param contentType Content-Type header (can be empty)
     * @param expectedStatus Expected HTTP status code
     * @return Response object
     */
    public static Response executeYoutubeLinkFetchWithCustomHeaders(MiddlewareServices middlewareServices,
            String agentToken, String version, String deviceIdentifier, String contentType, int expectedStatus) {

        Map<String, String> headers = new HashMap<>();
        headers.put("version", version);
        headers.put("session_token", agentToken);
        headers.put("deviceIdentifier", deviceIdentifier);
        headers.put("appLanguage", SoundBoxConstants.APP_LANGUAGE);

        if (!contentType.isEmpty()) {
            headers.put("Content-Type", contentType);
        }

        SBFetchYoutubeLink youtubeLinkObj = new SBFetchYoutubeLink();
        Response response = middlewareServices.sbFetchYoutubeLink(youtubeLinkObj, headers);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, expectedStatus);

        return response;
    }

    // ==================== ADVANCED HELPER METHODS ====================

    /**
     * Executes YouTube link fetch with custom token
     * @param middlewareServices Middleware service instance
     * @param sessionToken Custom session token (can be invalid or expired)
     * @param version Version header value
     * @param expectedStatus Expected HTTP status code
     * @return Response object
     */
    public static Response executeYoutubeLinkFetchWithCustomToken(MiddlewareServices middlewareServices,
            String sessionToken, String version, int expectedStatus) {

        Map<String, String> headers = new HashMap<>();
        headers.put("version", version);
        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", SoundBoxConstants.DEVICE_IDENTIFIER);
        headers.put("appLanguage", SoundBoxConstants.APP_LANGUAGE);
        headers.put("Content-Type", SoundBoxConstants.CONTENT_TYPE);

        SBFetchYoutubeLink youtubeLinkObj = new SBFetchYoutubeLink();
        Response response = middlewareServices.sbFetchYoutubeLink(youtubeLinkObj, headers);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, expectedStatus);

        return response;
    }

    /**
     * Executes device validation with custom headers
     * @param middlewareServices Middleware service instance
     * @param agentToken Agent authentication token
     * @param version Version header value
     * @param includeChecksum Whether to include checksum
     * @param includeUncleScrooge Whether to include UncleScrooge header
     * @param expectedStatus Expected HTTP status code
     * @return Response object
     */
    public static Response executeDeviceValidationWithCustomHeaders(MiddlewareServices middlewareServices,
            String agentToken, String version, boolean includeChecksum, boolean includeUncleScrooge, int expectedStatus) {

        Map<String, String> headers = createStandardHeaders(agentToken, version);

        if (includeChecksum) {
            headers.put("X-MW-URL-CHECKSUM-V3", SoundBoxConstants.CHECKSUM_V3);
        }

        if (includeUncleScrooge) {
            headers.put("UncleScrooge", SoundBoxConstants.UNCLE_SCROOGE);
        }

        DeviceValidation deviceValidationObj = new DeviceValidation();
        Response response = middlewareServices.deviceValidation(deviceValidationObj, headers);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, expectedStatus);

        return response;
    }

    /**
     * Executes merchant ID retrieval with custom parameters
     * @param middlewareServices Middleware service instance
     * @param merchantCustId Merchant customer ID
     * @param solutionType Solution type parameter
     * @param solutionSubType Solution sub-type parameter
     * @param agentToken Agent authentication token
     * @param version Version header value
     * @param includeSecurityHeaders Whether to include security headers
     * @param expectedStatus Expected HTTP status code
     * @return Response object
     */
    public static Response executeMerchantIdRetrievalWithCustomParams(MiddlewareServices middlewareServices,
            String merchantCustId, String solutionType, String solutionSubType, String agentToken, String version,
            boolean includeSecurityHeaders, int expectedStatus) {

        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();

        queryParams.put("merchantCustId", merchantCustId);
        queryParams.put("solutionType", solutionType);
        queryParams.put("solutionSubType", solutionSubType);

        headers.put("version", version);
        headers.put("session_token", agentToken);
        headers.put("deviceIdentifier", SoundBoxConstants.DEVICE_IDENTIFIER);
        headers.put("appLanguage", SoundBoxConstants.APP_LANGUAGE);
        headers.put("Content-Type", SoundBoxConstants.CONTENT_TYPE);

        if (includeSecurityHeaders) {
            headers.put("X-MW-URL-CHECKSUM-V3", SoundBoxConstants.MERCHANT_CHECKSUM);
            headers.put("UncleScrooge", SoundBoxConstants.UNCLE_SCROOGE);
        }

        GetMerchantid getMerchantIdObj = new GetMerchantid();
        Response response = middlewareServices.getmerchant(getMerchantIdObj, queryParams, headers);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, expectedStatus);

        return response;
    }

    /**
     * Executes IoT device retrieval test with custom token for authentication testing
     * @param middlewareServices Middleware service instance
     * @param sessionToken Custom session token (can be invalid or expired)
     * @param version Version header value
     * @param deviceIdentifier Device identifier
     * @param expectedStatus Expected HTTP status code
     * @return Response object
     */
    public static Response executeIotDeviceRetrievalWithCustomToken(MiddlewareServices middlewareServices,
            String sessionToken, String version, String deviceIdentifier, int expectedStatus) {

        Map<String, String> headers = new HashMap<>();
        headers.put("version", version);
        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier", deviceIdentifier);
        headers.put("X-MW-URL-CHECKSUM-V3", SoundBoxConstants.IOT_CHECKSUM);
        headers.put("appLanguage", SoundBoxConstants.APP_LANGUAGE);
        headers.put("Content-Type", SoundBoxConstants.CONTENT_TYPE);

        FetchDevicedIot getDevicesIotObj = new FetchDevicedIot();
        Response response = middlewareServices.fetchdevices(getDevicesIotObj, headers);

        int statusCode = response.getStatusCode();
        Assert.assertEquals(statusCode, expectedStatus);

        return response;
    }



    // ==================== UTILITY METHODS ====================
    
    /**
     * Creates standard headers for API calls
     * @param agentToken Agent token
     * @param version Version parameter
     * @return Map containing standard headers
     */
    private static Map<String, String> createStandardHeaders(String agentToken, String version) {
        Map<String, String> headers = new HashMap<>();
        headers.put("version", version);
        headers.put("session_token", agentToken);
        headers.put("deviceIdentifier", SoundBoxConstants.DEVICE_IDENTIFIER);
        headers.put("appLanguage", SoundBoxConstants.APP_LANGUAGE);
        headers.put("Content-Type", SoundBoxConstants.CONTENT_TYPE);
        headers.put("latitude", SoundBoxConstants.LATITUDE);
        headers.put("longitude", SoundBoxConstants.LONGITUDE);
        headers.put("ipAddress", SoundBoxConstants.IP_ADDRESS);
        return headers;
    }
    
    /**
     * Creates query parameters for entity and solution type
     * @param entityType Entity type parameter
     * @param solutionType Solution type parameter
     * @return Map containing query parameters
     */
    private static Map<String, String> createQueryParams(String entityType, String solutionType) {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solutionType", solutionType);
        return queryParams;
    }
    
    /**
     * Creates request body for OTP validation
     * @param otp OTP value
     * @param state State value
     * @param mobile Mobile number
     * @return Map containing request body
     */
    private static Map<String, String> createOtpValidationBody(String otp, String state, String mobile) {
        Map<String, String> body = new HashMap<>();
        body.put("otp", otp);
        body.put("state", state);
        body.put("userType", SoundBoxConstants.USER_TYPE);
        body.put("mobile", mobile);
        body.put("individaulMerchantKyc", "false");
        body.put("individualSolutionType", "");
        body.put("onlyValidateOtp", "true");
        body.put("skipOtp", "false");
        body.put("tncVersion", "");
        body.put("tncSetName", "");
        return body;
    }
}
