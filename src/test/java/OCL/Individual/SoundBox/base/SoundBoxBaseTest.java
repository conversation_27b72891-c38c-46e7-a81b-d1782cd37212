package OCL.Individual.SoundBox.base;

import OCL.Individual.SoundBox.base.SoundBoxConstants;
import OCL.Individual.SoundBox.base.SoundBoxTestUtils;
import OCL.Middleware.MiddlewareServices;
import OCL.Middleware.Pojo.AgentLogin;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;

import java.util.HashMap;
import java.util.Map;

/**
 * Base test class for SoundBox test suite
 * Contains common setup, configuration, and shared functionality
 */
public abstract class SoundBoxBaseTest {
    
    // ==================== LOGGER ====================
    protected static final Logger LOGGER = LogManager.getLogger(SoundBoxBaseTest.class);
    
    // ==================== SHARED TEST DATA ====================
    protected static MiddlewareServices middlewareServicesObject;
    protected static String AgentToken;
    protected static String custId;
    protected static String MOBILE_NUMBER;
    protected static String UserMID;
    protected static String MERCHANT_NAME;
    protected static String AGENT_CUST_ID;
    protected static String ENTITY_TYPE;
    protected static String Lead_ID;
    protected static String Tnc_secureKey;
    
    // ==================== RETRY CONFIGURATION ====================
    protected static int retrycount = 0;
    
    // ==================== SETUP METHODS ====================
    
    /**
     * Base setup method executed before all tests in the class
     * Initializes common test data and performs agent login
     */
    @BeforeClass
    public void baseSetup() {
        LOGGER.info("Starting SoundBox test suite setup...");
        
        // Initialize middleware services
        middlewareServicesObject = new MiddlewareServices();
        
        // Initialize test data
        initializeTestData();
        
        // Perform agent login
        performAgentLogin();
        
        LOGGER.info("SoundBox test suite setup completed successfully");
    }
    
    /**
     * Method executed before each test method
     * Resets retry count and performs any pre-test setup
     */
    @BeforeMethod
    public void beforeEachTest() {
        retrycount = 0;
        LOGGER.info("Starting test execution...");
    }
    
    // ==================== INITIALIZATION METHODS ====================
    
    /**
     * Initialize test data with default values
     */
    private void initializeTestData() {
        custId = SoundBoxConstants.TEST_USER_CUSTOMER_ID;
        MOBILE_NUMBER = SoundBoxConstants.TEST_MOBILE_NUMBER;
        AGENT_CUST_ID = SoundBoxConstants.TEST_AGENT_CUSTOMER_ID;
        ENTITY_TYPE = SoundBoxConstants.ENTITY_TYPE_INDIVIDUAL;
        MERCHANT_NAME = SoundBoxConstants.TEST_MERCHANT_NAME;
        
        LOGGER.info("Test data initialized - Customer ID: {}, Mobile: {}", custId, MOBILE_NUMBER);
    }
    
    /**
     * Perform agent login and obtain authentication token
     */
    private void performAgentLogin() {
        try {
            LOGGER.info("Performing agent login...");
            
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", SoundBoxConstants.CONTENT_TYPE);
            headers.put("version", SoundBoxConstants.VERSION);
            
            Map<String, String> body = new HashMap<>();
            body.put("userType", "AGENT");
            body.put("mobile", "7000000007");
            body.put("password", "123456");
            body.put("entityType", SoundBoxConstants.ENTITY_TYPE_INDIVIDUAL);
            body.put("solutionType", SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX);
            
            AgentLogin agentLoginObj = new AgentLogin();
            Response agentLoginResponse = middlewareServicesObject.agentLogin(agentLoginObj, headers, body);
            
            int statusCode = agentLoginResponse.getStatusCode();
            Assert.assertEquals(statusCode, SoundBoxConstants.STATUS_OK, "Agent login failed");
            
            AgentToken = agentLoginResponse.jsonPath().getString("sessionToken");
            Assert.assertNotNull(AgentToken, "Agent token should not be null");
            
            LOGGER.info("Agent login successful - Token obtained");
            
        } catch (Exception e) {
            LOGGER.error("Agent login failed: {}", e.getMessage());
            throw new RuntimeException("Failed to perform agent login", e);
        }
    }
    
    // ==================== UTILITY METHODS ====================
    
    /**
     * Re-perform agent login (used for retry scenarios)
     */
    protected void AgentLoginSoundBox() {
        LOGGER.info("Re-performing agent login for retry scenario...");
        performAgentLogin();
    }
    
    /**
     * Create standard headers for API calls
     * @return Map containing standard headers
     */
    protected Map<String, String> createStandardHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("version", SoundBoxConstants.VERSION);
        headers.put("session_token", AgentToken);
        headers.put("deviceIdentifier", SoundBoxConstants.DEVICE_IDENTIFIER);
        headers.put("appLanguage", SoundBoxConstants.APP_LANGUAGE);
        headers.put("Content-Type", SoundBoxConstants.CONTENT_TYPE);
        headers.put("latitude", SoundBoxConstants.LATITUDE);
        headers.put("longitude", SoundBoxConstants.LONGITUDE);
        headers.put("ipAddress", SoundBoxConstants.IP_ADDRESS);
        return headers;
    }
    
    /**
     * Create query parameters for entity and solution type
     * @param entityType Entity type parameter
     * @param solutionType Solution type parameter
     * @return Map containing query parameters
     */
    protected Map<String, String> createQueryParams(String entityType, String solutionType) {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("entityType", entityType);
        queryParams.put("solutionType", solutionType);
        return queryParams;
    }
    
    /**
     * Validate response status code with retry logic
     * @param response API response
     * @param expectedStatus Expected status code
     * @param maxRetries Maximum retry attempts
     * @return true if validation successful, false if retry needed
     */
    protected boolean validateResponseWithRetry(Response response, int expectedStatus, int maxRetries) {
        int actualStatus = response.getStatusCode();
        
        if (actualStatus == expectedStatus) {
            return true;
        }
        
        if (retrycount < maxRetries) {
            LOGGER.warn("Status code mismatch. Expected: {}, Actual: {}. Retry count: {}", 
                expectedStatus, actualStatus, retrycount);
            retrycount++;
            return false;
        }
        
        Assert.assertEquals(actualStatus, expectedStatus, 
            String.format("Status code validation failed after %d retries", maxRetries));
        return true;
    }
    
    /**
     * Log test completion with status
     * @param testName Name of the test
     * @param success Whether test was successful
     */
    protected void logTestCompletion(String testName, boolean success) {
        if (success) {
            LOGGER.info("Test '{}' completed successfully", testName);
        } else {
            LOGGER.error("Test '{}' failed", testName);
        }
    }
}
