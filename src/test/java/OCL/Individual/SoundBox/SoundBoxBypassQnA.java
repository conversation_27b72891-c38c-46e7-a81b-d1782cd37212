package OCL.Individual.SoundBox;

import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class SoundBoxBypassQnA {
	MiddlewareServices MiddlewareServicesObject= new MiddlewareServices();

	    String entityType = "INDIVIDUAL";
	    String solutionType = "sound_box";
	    String questionType="fulfilment_bypass";
	    String solutionSubType="sound_box_replacement";

	    @Test(priority = 1)
	    public void FetchQnAForSounBoxBypassFulfilmentValid() {
	    	

	        Map<String, String> queryParams = new HashMap<String, String>();
	        queryParams.put("entityType",entityType);
	        queryParams.put("solutionType", solutionType);
	        queryParams.put("questionType", questionType);
	        queryParams.put("solutionSubType",solutionSubType);


	        Response v1ByPassReplaceResponse = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
	        int httpcode = v1ByPassReplaceResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");

	    }
	    @Test(priority = 2)
	    public void FetchQnAForSounBoxBypassFulfilment_WithInvalidEntityType() {
	    	

	        Map<String, String> queryParams = new HashMap<String, String>();
	        queryParams.put("entityType","74848HHH");
	        queryParams.put("solutionType", solutionType);
	        queryParams.put("questionType", questionType);
	        queryParams.put("solutionSubType",solutionSubType);


	        Response v1ByPassReplaceResponse = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
	        int httpcode = v1ByPassReplaceResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");

	    }
	    @Test(priority = 3)
	    public void FetchQnAForSounBoxBypassFulfilment_WithInvalidSolutionType() {
	    	

	        Map<String, String> queryParams = new HashMap<String, String>();
	        queryParams.put("entityType",entityType);
	        queryParams.put("solutionType", "HSGSU39399");
	        queryParams.put("questionType", questionType);
	        queryParams.put("solutionSubType",solutionSubType);


	        Response v1ByPassReplaceResponse = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
	        int httpcode = v1ByPassReplaceResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 500, "Testcase Failed");

	    }
	    @Test(priority = 4)
	    public void FetchQnAForSounBoxBypassFulfilment_WithInvalidQuestionType() {
	    	

	        Map<String, String> queryParams = new HashMap<String, String>();
	        queryParams.put("entityType",entityType);
	        queryParams.put("solutionType", solutionType);
	        queryParams.put("questionType", "Hdud^%$");
	        queryParams.put("solutionSubType",solutionSubType);


	        Response v1ByPassReplaceResponse = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
	        int httpcode = v1ByPassReplaceResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");

	    }
	    @Test(priority = 5)
	    public void FetchQnAForSounBoxBypassFulfilment_WithInvalidSolutionSubType() {
	    	

	        Map<String, String> queryParams = new HashMap<String, String>();
	        queryParams.put("entityType",entityType);
	        queryParams.put("solutionType", solutionType);
	        queryParams.put("questionType",questionType );
	        queryParams.put("solutionSubType","DUGDD");


	        Response v1ByPassReplaceResponse = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
	        int httpcode = v1ByPassReplaceResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	    }
	  
	    @Test(priority = 6)
	    public void FetchQnAForSounBoxBypassFulfilment_WithNullEntityType() {
	    	

	        Map<String, String> queryParams = new HashMap<String, String>();
	        queryParams.put("solutionType", solutionType);
	        queryParams.put("questionType", questionType);
	        queryParams.put("solutionSubType",solutionSubType);


	        Response v1ByPassReplaceResponse = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
	        int httpcode = v1ByPassReplaceResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");

	    }
	    @Test(priority = 7)
	    public void FetchQnAForSounBoxBypassFulfilment_WithNUllSolutionType() {
	    	

	        Map<String, String> queryParams = new HashMap<String, String>();
	        queryParams.put("entityType",entityType);
	        queryParams.put("questionType", questionType);
	        queryParams.put("solutionSubType",solutionSubType);


	        Response v1ByPassReplaceResponse = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
	        int httpcode = v1ByPassReplaceResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");

	    }
	    @Test(priority = 8)
	    public void FetchQnAForSounBoxBypassFulfilment_WithNullQuestType() {
	    	

	        Map<String, String> queryParams = new HashMap<String, String>();
	        queryParams.put("entityType",entityType);
	        queryParams.put("solutionType", solutionType);
	        queryParams.put("solutionSubType",solutionSubType);


	        Response v1ByPassReplaceResponse = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
	        int httpcode = v1ByPassReplaceResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");

	    }
	    @Test(priority = 9)
	    public void FetchQnAForSounBoxBypassFulfilment_WithNullSolutionSubType() {
	    	

	        Map<String, String> queryParams = new HashMap<String, String>();
	        queryParams.put("entityType",entityType);
	        queryParams.put("solutionType", solutionType);
	        queryParams.put("questionType", questionType);


	        Response v1ByPassReplaceResponse = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
	        int httpcode = v1ByPassReplaceResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");

	    }
	    @Test(priority = 10)
	    public void FetchQnAForSounBoxBypassFulfilment_WithNoQueryParameters() {
	    	

	        Map<String, String> queryParams = new HashMap<String, String>();
	      


	        Response v1ByPassReplaceResponse = MiddlewareServicesObject.v1SoundBoxBypassQnA(queryParams);
	        int httpcode = v1ByPassReplaceResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");

	    }
	 
}
