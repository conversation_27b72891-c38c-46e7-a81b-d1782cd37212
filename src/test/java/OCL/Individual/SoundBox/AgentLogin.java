package OCL.Individual.SoundBox;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.annotations.Test;
import Services.MechantService.MiddlewareServices;
import org.testng.Assert;
import Request.SoundBox.AgentLogin.DeviceIdentifier;
import Request.SoundBox.AgentLogin.AgentLoginApi2;

import java.util.HashMap;
import java.util.Map;

import TestingLogics.TEST;

public class AgentLogin extends BaseMethod {

        MiddlewareServices middlewareServicesObject = new MiddlewareServices();
        public static String AgentToken = "";
        public static String MobileNumber = "8010630022";
        public static String version = "5.1.2";
        public static String signature = "mZ9QGNxScwoDNnPMYKNjxBnpAPTHo6+donPbbOf3aAq5TMaNSnlDtQLo9mmm8lSgUFkIeyzDPZhJyfjntfDHQamcH/I7ipbXUsioCgX6JLUuIr5feK5zpDxD+rhGKeoWUg8U3feqFn9MLxDgq6mllKcLY5iGRYXKqvndrR1AOn3u14mygdjTqCnummLslC6noSlz0tkzQoCPD75dT4tIEAjCKLaBiq4ynxB6dY+denzlSbGDELn7xR6PxiRJzPDGL6vvYEuIRvfN0qkXw40SytMzHrGTiJUx6GulRWLIKD+pOp2KTM9Hq6+jaBQnhj/B01D5vFaCJ8eYmzL343YR7A==";
        TEST obj = new TEST();
        private static final Logger LOGGER = LogManager.getLogger(FlowSoundBox.class);

        @Test(priority = 1,groups = "Regression" , description = "FirstApiHitted for agent Login")
        @Owner(emailId = "<EMAIL>" , isAutomated = true)
        public void AgentLoginAPI1 () {
            Map<String, String> queryParams = new HashMap<>();
            Map<String, String> headers = new HashMap<>();
            headers.put("Host", "accounts-staging.paytm.in");
            headers.put("authorization", "Basic c3RhZ2luZy1nb2xkZW4tZ2F0ZTo2NWI1ZDczOC1iYWZjLTQzZTItOWM3My04MTBkMzJjMmZlYjE=");
            headers.put("x-device-manufacturer", "Xiaomi");
            headers.put("x-device-name", "M2004J19C");
            headers.put("x-app-rid", "474445b0-9634-4fc1-a9a7-69b2b8af7217:*************:9:9");
            headers.put("x-device-identifier", "Xiaomi-M2004J19C-474445b0-9634-4fc1-a9a7-69b2b8af7217");
            headers.put("x-phone-number", MobileNumber);
            headers.put("x-epoch", "**********");
            headers.put("content-type", "application/json");
            headers.put("x-app-version", version);
            headers.put("user-agent", "okhttp/4.9.1");
            queryParams.put("deviceIdentifier", "Xiaomi-M2004J19C-474445b0-9634-4fc1-a9a7-69b2b8af7217");
            queryParams.put("deviceManufacturer", "Xiaomi");
            queryParams.put("deviceName", "M2004J19C");
            queryParams.put("version", version);
            queryParams.put("playStore", "false");
            queryParams.put("lat", "0.0");
            queryParams.put("long", "0.0");
            queryParams.put("language", "en");
            queryParams.put("networkType", "WIFI");
            queryParams.put("osVersion", "10");
            queryParams.put("locale", "en-IN");
            queryParams.put("flow", "LOGIN_REGISTER");
            DeviceIdentifier deviceIdentifier = new DeviceIdentifier();
            Response agentlogin1objresp = middlewareServicesObject.agentlogin1(deviceIdentifier, queryParams, headers);
            int StatusCode = agentlogin1objresp.getStatusCode();
            Assert.assertEquals(StatusCode , 200);
        }
//        @Test(priority = 1 , groups = "Regression" , description = "SecondApiHitted for agent Login")
//        @Owner(emailId = "<EMAIL>" , isAutomated = true)
//        public void AgentLoginAPI2(){
//            Map<String,String> queryParams = new HashMap<>();
//            Map<String,String> headers = new HashMap<>();
//            Map<String,String> body = new HashMap<>();
//            queryParams.put("deviceIdentifier", "Xiaomi-M2004J19C-474445b0-9634-4fc1-a9a7-69b2b8af7217");
//            queryParams.put("deviceManufacturer", "Xiaomi");
//            queryParams.put("deviceName", "M2004J19C");
//            queryParams.put("version", version);
//            queryParams.put("playStore", "false");
//            queryParams.put("lat", "0.0");
//            queryParams.put("long", "0.0");
//            queryParams.put("language", "en");
//            queryParams.put("networkType", "WIFI");
//            queryParams.put("osVersion", "10");
//            queryParams.put("locale", "en-IN");
//            headers.put("Host", "accounts-staging.paytm.in");
//            headers.put("authorization", "Basic c3RhZ2luZy1nb2xkZW4tZ2F0ZTo2NWI1ZDczOC1iYWZjLTQzZTItOWM3My04MTBkMzJjMmZlYjE=");
//            headers.put("x-device-manufacturer", "Xiaomi");
//            headers.put("autoreadhash","zPBQxPnX6nV");
//            headers.put("x-device-name", "M2004J19C");
//            headers.put("x-client-signature",signature);
//            headers.put("x-app-rid", "474445b0-9634-4fc1-a9a7-69b2b8af7217:*************:9:9");
//            headers.put("x-device-identifier", "Xiaomi-M2004J19C-474445b0-9634-4fc1-a9a7-69b2b8af7217");
//            headers.put("x-public-key", "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA04A0bIJINLCcXRrE6CZTUkI2hCPYyoj1r6XEGR+GM0iArx7I3ZdGomAaXNFmncAiHTmqGAHkvJ4gfPKThwYyRmJvWXJt3osNiNoDSJUTb+t9P/fYyb+cVMp1u3LXN0HyOfuXZ0dnz8z8mkZzwC2MYN8rJ6lcsWfufmroJwtrratRry7LpXWNCw8W55dWn5/th1i0UcHD9s4JCQ24PILLfF5yGlKuF+014HlLcUAwBWcmvQJ3tD+fiak5hUgcfoejaEbgXMVn5IFDmVIPvLsq+uZBO4ixLCa0rTeP3YpQcQ1gdySGCdJTjQaLmfNkwnfZgmtE1J0K6KvdG69jsTVG+QIDAQAB");
//            headers.put("x-epoch", "**********");
//            headers.put("x-app-version", version);
//            headers.put("content-type", "application/json");
//            headers.put("user-agent", "okhttp/4.9.1");
//            AgentLoginApi2 agentlogin2obj = new AgentLoginApi2();
//            Response agentlogin2objresp =  middlewareServicesObject.agentlogin2(agentlogin2obj,queryParams,headers,body);
//        }
    }