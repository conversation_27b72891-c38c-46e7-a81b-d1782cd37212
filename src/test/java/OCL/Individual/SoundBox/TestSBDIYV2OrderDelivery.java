package OCL.Individual.SoundBox;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.SBDIYV2OrderDelivery;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestSBDIYV2OrderDelivery extends BaseMethod{

	private Response response;
	private MiddlewareServices services;
	
	private Map<String,String> headers;
	private Map<String, String> body;
	private Map<String, String> params;
	
	private String freshLeadOrderID="100076954719";
	private String freshLeadItemID="1349571074319";
	private String closedLeadOrderID="100076927741";
	private String closedLeadItemID="1349571662001";
	private String cancelledLeadOrderID="100077390518";
	private String cancelledLeadItemID="1349571905342";
	private String rtoLeadOrderID="100076961341";
	private String rtoLeadItemID="1349571706809";
	private String invalidStageLeadOrderID="100076906851";
	private String invalidStageItemID="1349571010147";
	private String invalidOrderId="10007690685123";
	private String invalidItemID="134957101014723";
	
	private String currentDb="releaseNovW2";
	private String query="";
	private SBDIYV2OrderDelivery obj;
	
		
	@Test
	private void TC01_Check_Response_Code_When_Lead_Is_PFF_Manifestation_Stage() throws Exception {
		headers=new HashMap<>();
		body=new HashMap<>();
		params=new HashMap<>();
		obj=new SBDIYV2OrderDelivery();
		services =new MiddlewareServices();
		
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		query = String.format("use %s;", currentDb);
		stmt.execute("use releaseNovW2");
		query=String.format("Select * from user_business_mapping ubm where solution_type_level_3 = %s;", freshLeadOrderID);
		ResultSet res= stmt.executeQuery(query);
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery(String.format("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = %s and ws.is_active =1;", id));
		res.next();
		String nodeId=res.getString("workflow_node_id");
		query=String.format("Select wn.sub_stage from workflow_node wn where wn.id = %s", nodeId);
		res=stmt.executeQuery(query);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();
		
		Assert.assertEquals(subStage, "PFF_MANIFEST_MOVEMENT_SUCCESS");
		headers.put("Content-Type", "application/json");
		headers.put("Cookie", "JSESSIONID=90C409384FA0ACDB86FBF555632E8185; JSESSIONID=865CDF8E037850013FF118051349970D");
		headers.put("Connection", "keep-alive");
		
		body.put("id", "***********8");
        body.put("payment_status", "7");
        body.put("pg_amount","148");
        body.put("customer_id", "1001891503");
        body.put("order_id", freshLeadOrderID);


        body.put("item_id", freshLeadItemID);
        body.put("product_id", "1234826015");
        body.put("status", "7");
        body.put("price", "5");

   
        body.put("state", "7");
        body.put("updatedAt", "2024-08-23T11:53:06.000Z");
        String metaData = "{\\\"customerId\\\":\\\"1001891503\\\",\\\"solutionType\\\":\\\"courier_automation\\\",\\\"solutionTypeLevel2\\\":\\\"sound_box_bind\\\"}";
        body.put("meta_data",metaData);
        
        
        params.put("order_id", freshLeadOrderID);
        
        response=services.notifyDelievery(obj, headers, params, body);
        Assert.assertEquals(response.getStatusCode(), 200);
        
  	}
	
	@Test
	private void TC02_Check_Response_Code_When_Lead_Is_Already_Closed() throws Exception {
		headers=new HashMap<>();
		body=new HashMap<>();
		params=new HashMap<>();
		obj=new SBDIYV2OrderDelivery();
		services =new MiddlewareServices();
		
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		query = String.format("use %s;", currentDb);
		stmt.execute("use releaseNovW2");
		query=String.format("Select * from user_business_mapping ubm where solution_type_level_3 = %s;", closedLeadOrderID);
		ResultSet res= stmt.executeQuery(query);
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery(String.format("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = %s and ws.is_active =1;", id));
		res.next();
		String nodeId=res.getString("workflow_node_id");
		query=String.format("Select wn.sub_stage from workflow_node wn where wn.id = %s", nodeId);
		res=stmt.executeQuery(query);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();
		
		Assert.assertEquals(subStage, "LEAD_SUCCESSFULLY_CLOSED");
		headers.put("Content-Type", "application/json");
		headers.put("Cookie", "JSESSIONID=90C409384FA0ACDB86FBF555632E8185; JSESSIONID=865CDF8E037850013FF118051349970D");
		headers.put("Connection", "keep-alive");
		
		body.put("id", "***********8");
        body.put("payment_status", "7");
        body.put("pg_amount","148");
        body.put("customer_id", "1001891503");
        body.put("order_id", closedLeadOrderID);


        body.put("item_id", closedLeadItemID);
        body.put("product_id", "1234826015");
        body.put("status", "7");
        body.put("price", "5");

   
        body.put("state", "7");
        body.put("updatedAt", "2024-08-23T11:53:06.000Z");
        String metaData = "{\\\"customerId\\\":\\\"1001891503\\\",\\\"solutionType\\\":\\\"courier_automation\\\",\\\"solutionTypeLevel2\\\":\\\"sound_box_bind\\\"}";
        body.put("meta_data",metaData);
        
        
        params.put("order_id", closedLeadOrderID);
        
        response=services.notifyDelievery(obj, headers, params, body);
        Assert.assertEquals(response.getStatusCode(), 400);
        
  	}
	
	@Test
	private void TC03_Check_Response_Code_When_Order_Is_Cancelled_And_Lead_Is_Already_Closed() throws Exception {
		headers=new HashMap<>();
		body=new HashMap<>();
		params=new HashMap<>();
		obj=new SBDIYV2OrderDelivery();
		services =new MiddlewareServices();
		
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		query = String.format("use %s;", currentDb);
		stmt.execute("use releaseNovW2");
		query=String.format("Select * from user_business_mapping ubm where solution_type_level_3 = %s;", cancelledLeadOrderID);
		ResultSet res= stmt.executeQuery(query);
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery(String.format("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = %s and ws.is_active =1;", id));
		res.next();
		String nodeId=res.getString("workflow_node_id");
		query=String.format("Select wn.sub_stage from workflow_node wn where wn.id = %s", nodeId);
		res=stmt.executeQuery(query);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();
		
		Assert.assertEquals(subStage, "LEAD_FORCEFULLY_CLOSED");
		headers.put("Content-Type", "application/json");
		headers.put("Cookie", "JSESSIONID=90C409384FA0ACDB86FBF555632E8185; JSESSIONID=865CDF8E037850013FF118051349970D");
		headers.put("Connection", "keep-alive");
		
		body.put("id", "***********8");
        body.put("payment_status", "7");
        body.put("pg_amount","148");
        body.put("customer_id", "1001891503");
        body.put("order_id", cancelledLeadOrderID);


        body.put("item_id", cancelledLeadItemID);
        body.put("product_id", "1234826015");
        body.put("status", "7");
        body.put("price", "5");

   
        body.put("state", "7");
        body.put("updatedAt", "2024-08-23T11:53:06.000Z");
        String metaData = "{\\\"customerId\\\":\\\"1001891503\\\",\\\"solutionType\\\":\\\"courier_automation\\\",\\\"solutionTypeLevel2\\\":\\\"sound_box_bind\\\"}";
        body.put("meta_data",metaData);
        
        
        params.put("order_id", cancelledLeadOrderID);
        
        response=services.notifyDelievery(obj, headers, params, body);
        Assert.assertEquals(response.getStatusCode(), 400);
        
  	}
	
	@Test
	private void TC04_Check_Response_Code_When_Order_Is_RTO_And_Lead_Is_Already_Closed() throws Exception {
		headers=new HashMap<>();
		body=new HashMap<>();
		params=new HashMap<>();
		obj=new SBDIYV2OrderDelivery();
		services =new MiddlewareServices();
		
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		query = String.format("use %s;", currentDb);
		stmt.execute("use releaseNovW2");
		query=String.format("Select * from user_business_mapping ubm where solution_type_level_3 = %s;", rtoLeadOrderID);
		ResultSet res= stmt.executeQuery(query);
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery(String.format("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = %s and ws.is_active =1;", id));
		res.next();
		String nodeId=res.getString("workflow_node_id");
		query=String.format("Select wn.sub_stage from workflow_node wn where wn.id = %s", nodeId);
		res=stmt.executeQuery(query);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();
		
		Assert.assertEquals(subStage, "LEAD_FORCEFULLY_CLOSED");
		headers.put("Content-Type", "application/json");
		headers.put("Cookie", "JSESSIONID=90C409384FA0ACDB86FBF555632E8185; JSESSIONID=865CDF8E037850013FF118051349970D");
		headers.put("Connection", "keep-alive");
		
		body.put("id", "***********8");
        body.put("payment_status", "7");
        body.put("pg_amount","148");
        body.put("customer_id", "1001891503");
        body.put("order_id", rtoLeadOrderID);


        body.put("item_id", rtoLeadItemID);
        body.put("product_id", "1234826015");
        body.put("status", "7");
        body.put("price", "5");

   
        body.put("state", "7");
        body.put("updatedAt", "2024-08-23T11:53:06.000Z");
        String metaData = "{\\\"customerId\\\":\\\"1001891503\\\",\\\"solutionType\\\":\\\"courier_automation\\\",\\\"solutionTypeLevel2\\\":\\\"sound_box_bind\\\"}";
        body.put("meta_data",metaData);
        
        
        params.put("order_id", rtoLeadOrderID);
        
        response=services.notifyDelievery(obj, headers, params, body);
        Assert.assertEquals(response.getStatusCode(), 400);
        
  	}
	
	@Test
	private void TC05_Check_Response_Code_When_Order_Is_Not_Closed_And_Lead_Is_At_Invalid_Stage() throws Exception {
		headers=new HashMap<>();
		body=new HashMap<>();
		params=new HashMap<>();
		obj=new SBDIYV2OrderDelivery();
		services =new MiddlewareServices();
		
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		query = String.format("use %s;", currentDb);
		stmt.execute("use releaseNovW2");
		query=String.format("Select * from user_business_mapping ubm where solution_type_level_3 = %s;", invalidStageLeadOrderID);
		ResultSet res= stmt.executeQuery(query);
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery(String.format("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = %s and ws.is_active =1;", id));
		res.next();
		String nodeId=res.getString("workflow_node_id");
		query=String.format("Select wn.sub_stage from workflow_node wn where wn.id = %s", nodeId);
		res=stmt.executeQuery(query);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();
		
		Assert.assertEquals(subStage, "STS_UPDATE_INVOICE_DETAILS_ERROR");
	
		headers.put("Content-Type", "application/json");
		headers.put("Cookie", "JSESSIONID=90C409384FA0ACDB86FBF555632E8185; JSESSIONID=865CDF8E037850013FF118051349970D");
		headers.put("Connection", "keep-alive");
		
		body.put("id", "***********8");
        body.put("payment_status", "7");
        body.put("pg_amount","148");
        body.put("customer_id", "1001891503");
        body.put("order_id", invalidStageLeadOrderID);


        body.put("item_id", invalidStageItemID);
        body.put("product_id", "1234826015");
        body.put("status", "7");
        body.put("price", "5");

   
        body.put("state", "7");
        body.put("updatedAt", "2024-08-23T11:53:06.000Z");
        String metaData = "{\\\"customerId\\\":\\\"1001891503\\\",\\\"solutionType\\\":\\\"courier_automation\\\",\\\"solutionTypeLevel2\\\":\\\"sound_box_bind\\\"}";
        body.put("meta_data",metaData);
        
        
        params.put("order_id", invalidStageLeadOrderID);
        
        response=services.notifyDelievery(obj, headers, params, body);
        Assert.assertEquals(response.getStatusCode(), 400);
        
  	}
	
	
	@Test(priority =0)
	private void TC06_Error_Response_Code_When_Lead_Is_PFF_Manifestation_Stage_And_CustID_Is_Not_Passed_In_Request() throws Exception {
		headers=new HashMap<>();
		body=new HashMap<>();
		params=new HashMap<>();
		obj=new SBDIYV2OrderDelivery();
		services =new MiddlewareServices();
		
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		query = String.format("use %s;", currentDb);
		stmt.execute("use releaseNovW2");
		query=String.format("Select * from user_business_mapping ubm where solution_type_level_3 = %s;", freshLeadOrderID);
		ResultSet res= stmt.executeQuery(query);
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery(String.format("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = %s and ws.is_active =1;", id));
		res.next();
		String nodeId=res.getString("workflow_node_id");
		query=String.format("Select wn.sub_stage from workflow_node wn where wn.id = %s", nodeId);
		res=stmt.executeQuery(query);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();
		
		Assert.assertEquals(subStage, "PFF_MANIFEST_MOVEMENT_SUCCESS");
		headers.put("Content-Type", "application/json");
		headers.put("Cookie", "JSESSIONID=90C409384FA0ACDB86FBF555632E8185; JSESSIONID=865CDF8E037850013FF118051349970D");
		headers.put("Connection", "keep-alive");
		
		body.put("id", "***********8");
        body.put("payment_status", "7");
        body.put("pg_amount","148");
        body.put("customer_id", "");
        body.put("order_id", freshLeadOrderID);


        body.put("item_id", freshLeadItemID);
        body.put("product_id", "1234826015");
        body.put("status", "7");
        body.put("price", "5");

   
        body.put("state", "7");
        body.put("updatedAt", "2024-08-23T11:53:06.000Z");
        String metaData = "{\\\"customerId\\\":\\\"1001891503\\\",\\\"solutionType\\\":\\\"courier_automation\\\",\\\"solutionTypeLevel2\\\":\\\"sound_box_bind\\\"}";
        body.put("meta_data",metaData);
        
        
        params.put("order_id", freshLeadOrderID);
        
        response=services.notifyDelievery(obj, headers, params, body);
        Assert.assertEquals(response.getStatusCode(),400);
        
  	}
	
	@Test(priority =1)
	private void TC06_Error_Response_Code_When_Lead_Is_PFF_Manifestation_Stage_And_CustID__Passed_In_Request_Is_InCorrect() throws Exception {
		headers=new HashMap<>();
		body=new HashMap<>();
		params=new HashMap<>();
		obj=new SBDIYV2OrderDelivery();
		services =new MiddlewareServices();
		
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		query = String.format("use %s;", currentDb);
		stmt.execute("use releaseNovW2");
		query=String.format("Select * from user_business_mapping ubm where solution_type_level_3 = %s;", freshLeadOrderID);
		ResultSet res= stmt.executeQuery(query);
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery(String.format("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = %s and ws.is_active =1;", id));
		res.next();
		String nodeId=res.getString("workflow_node_id");
		query=String.format("Select wn.sub_stage from workflow_node wn where wn.id = %s", nodeId);
		res=stmt.executeQuery(query);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();
		
		Assert.assertEquals(subStage, "PFF_MANIFEST_MOVEMENT_SUCCESS");
		headers.put("Content-Type", "application/json");
		headers.put("Cookie", "JSESSIONID=90C409384FA0ACDB86FBF555632E8185; JSESSIONID=865CDF8E037850013FF118051349970D");
		headers.put("Connection", "keep-alive");
		
		body.put("id", "***********8");
        body.put("payment_status", "7");
        body.put("pg_amount","148");
        body.put("customer_id", "1001788031");
        body.put("order_id", freshLeadOrderID);


        body.put("item_id", freshLeadItemID);
        body.put("product_id", "1234826015");
        body.put("status", "7");
        body.put("price", "5");

   
        body.put("state", "7");
        body.put("updatedAt", "2024-08-23T11:53:06.000Z");
        String metaData = "{\\\"customerId\\\":\\\"1001788031\\\",\\\"solutionType\\\":\\\"courier_automation\\\",\\\"solutionTypeLevel2\\\":\\\"sound_box_bind\\\"}";
        body.put("meta_data",metaData);
        
        
        params.put("order_id", freshLeadOrderID);
        
        response=services.notifyDelievery(obj, headers, params, body);
        Assert.assertEquals(response.getStatusCode(),400);
        
  	}
	
	@Test(priority = 2)
	private void TC07_Check_Response_Code_When_OrderID_Is_NotPassed() throws Exception {
		headers=new HashMap<>();
		body=new HashMap<>();
		params=new HashMap<>();
		obj=new SBDIYV2OrderDelivery();
		services =new MiddlewareServices();
		
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		query = String.format("use %s;", currentDb);
		stmt.execute("use releaseNovW2");
		query=String.format("Select * from user_business_mapping ubm where solution_type_level_3 = %s;", freshLeadOrderID);
		ResultSet res= stmt.executeQuery(query);
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery(String.format("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = %s and ws.is_active =1;", id));
		res.next();
		String nodeId=res.getString("workflow_node_id");
		query=String.format("Select wn.sub_stage from workflow_node wn where wn.id = %s", nodeId);
		res=stmt.executeQuery(query);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();
		
		Assert.assertEquals(subStage, "PFF_MANIFEST_MOVEMENT_SUCCESS");
		headers.put("Content-Type", "application/json");
		headers.put("Cookie", "JSESSIONID=90C409384FA0ACDB86FBF555632E8185; JSESSIONID=865CDF8E037850013FF118051349970D");
		headers.put("Connection", "keep-alive");
		
		body.put("id", "***********8");
        body.put("payment_status", "7");
        body.put("pg_amount","148");
        body.put("customer_id", "1001891503");
        body.put("order_id", "");


        body.put("item_id", freshLeadItemID);
        body.put("product_id", "1234826015");
        body.put("status", "7");
        body.put("price", "5");

   
        body.put("state", "7");
        body.put("updatedAt", "2024-08-23T11:53:06.000Z");
        String metaData = "{\\\"customerId\\\":\\\"1001891503\\\",\\\"solutionType\\\":\\\"courier_automation\\\",\\\"solutionTypeLevel2\\\":\\\"sound_box_bind\\\"}";
        body.put("meta_data",metaData);
        
        
        params.put("order_id", freshLeadOrderID);
        
        response=services.notifyDelievery(obj, headers, params, body);
        Assert.assertEquals(response.getStatusCode(), 400);
        
  	}
	
	@Test(priority = 2)
	private void TC07_Check_Response_Code_When_Item_Is_NotPassed() throws Exception {
		headers=new HashMap<>();
		body=new HashMap<>();
		params=new HashMap<>();
		obj=new SBDIYV2OrderDelivery();
		services =new MiddlewareServices();
		
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		query = String.format("use %s;", currentDb);
		stmt.execute("use releaseNovW2");
		query=String.format("Select * from user_business_mapping ubm where solution_type_level_3 = %s;", freshLeadOrderID);
		ResultSet res= stmt.executeQuery(query);
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery(String.format("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = %s and ws.is_active =1;", id));
		res.next();
		String nodeId=res.getString("workflow_node_id");
		query=String.format("Select wn.sub_stage from workflow_node wn where wn.id = %s", nodeId);
		res=stmt.executeQuery(query);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();
		
		Assert.assertEquals(subStage, "PFF_MANIFEST_MOVEMENT_SUCCESS");
		headers.put("Content-Type", "application/json");
		headers.put("Cookie", "JSESSIONID=90C409384FA0ACDB86FBF555632E8185; JSESSIONID=865CDF8E037850013FF118051349970D");
		headers.put("Connection", "keep-alive");
		
		body.put("id", "***********8");
        body.put("payment_status", "7");
        body.put("pg_amount","148");
        body.put("customer_id", "1001891503");
        body.put("order_id", freshLeadOrderID);


        body.put("item_id", "");
        body.put("product_id", "1234826015");
        body.put("status", "7");
        body.put("price", "5");

   
        body.put("state", "7");
        body.put("updatedAt", "2024-08-23T11:53:06.000Z");
        String metaData = "{\\\"customerId\\\":\\\"1001891503\\\",\\\"solutionType\\\":\\\"courier_automation\\\",\\\"solutionTypeLevel2\\\":\\\"sound_box_bind\\\"}";
        body.put("meta_data",metaData);
        
        
        params.put("order_id", freshLeadOrderID);
        
        response=services.notifyDelievery(obj, headers, params, body);
        Assert.assertEquals(response.getStatusCode(), 400);
        
  	}
	
	@Test(priority = 2)
	private void TC07_Check_Response_Code_When_Status_Passed_Is_Incorrect() throws Exception {
		headers=new HashMap<>();
		body=new HashMap<>();
		params=new HashMap<>();
		obj=new SBDIYV2OrderDelivery();
		services =new MiddlewareServices();
		
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		query = String.format("use %s;", currentDb);
		stmt.execute("use releaseNovW2");
		query=String.format("Select * from user_business_mapping ubm where solution_type_level_3 = %s;", freshLeadOrderID);
		ResultSet res= stmt.executeQuery(query);
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery(String.format("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = %s and ws.is_active =1;", id));
		res.next();
		String nodeId=res.getString("workflow_node_id");
		query=String.format("Select wn.sub_stage from workflow_node wn where wn.id = %s", nodeId);
		res=stmt.executeQuery(query);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();
		
		Assert.assertEquals(subStage, "PFF_MANIFEST_MOVEMENT_SUCCESS");
		headers.put("Content-Type", "application/json");
		headers.put("Cookie", "JSESSIONID=90C409384FA0ACDB86FBF555632E8185; JSESSIONID=865CDF8E037850013FF118051349970D");
		headers.put("Connection", "keep-alive");
		
		body.put("id", "***********8");
        body.put("payment_status", "7");
        body.put("pg_amount","148");
        body.put("customer_id", "1001891503");
        body.put("order_id", freshLeadOrderID);


        body.put("item_id", "");
        body.put("product_id", "1234826015");
        body.put("status", "7");
        body.put("price", "5");

   
        body.put("state", "191");
        body.put("updatedAt", "2024-08-23T11:53:06.000Z");
        String metaData = "{\\\"customerId\\\":\\\"1001891503\\\",\\\"solutionType\\\":\\\"courier_automation\\\",\\\"solutionTypeLevel2\\\":\\\"sound_box_bind\\\"}";
        body.put("meta_data",metaData);
        
        
        params.put("order_id", freshLeadOrderID);
        
        response=services.notifyDelievery(obj, headers, params, body);
        Assert.assertEquals(response.getStatusCode(), 400);
        
  	}


}
