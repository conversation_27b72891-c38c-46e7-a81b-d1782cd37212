package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.SBShopInsuranceUpdateLead;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestSBShopInsuranceUpdateLead  extends BaseMethod{

	SBShopInsuranceUpdateLead ins;
	
	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> body;

	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	@Test
	public void verify_Status_Code_When_Lead_Is_Updated_Successfully() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("leadId","7a526521-a203-4de9-b807-328bb5813624");
        body.put("shopInsuranceOpted","false");
        body.put("planId","13124");
        body.put("name","SB Shop Insurance 1");
        body.put("pid","1235131993");
        body.put("price","401");

        body.put("agentCustId","1107195733");


        ins=new SBShopInsuranceUpdateLead();
        response=services.updateSBShopInsurance(ins, body, headers);	
       
	}

	
	
	
	@Test
	public void verify_Error_Code_In_Case_Of_Update_Lead_Failure() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("leadId","8248a4b2-420b-4a24-be3a-aec7d771df01");
        body.put("shopInsuranceOpted","false");
        body.put("planId","13124");
        body.put("name","SB Shop Insurance 1");
        body.put("pid","1235131993");
        body.put("price","401");

        body.put("agentCustId","1107195733");


        ins=new SBShopInsuranceUpdateLead();
        response=services.updateSBShopInsurance(ins, body, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 500);
	}
	
	@Test
	public void verify_Error_Code_When_Lead_ID_Is_Missing() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("leadId","");
        body.put("shopInsuranceOpted","false");
        body.put("planId","13124");
        body.put("name","SB Shop Insurance 1");
        body.put("pid","1235131993");
        body.put("price","401");

        body.put("agentCustId","1107195733");


        ins=new SBShopInsuranceUpdateLead();
        response=services.updateSBShopInsurance(ins, body, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	
	@Test
	public void verify_Error_Code_When_Lead_ID_Is_Incorrect() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("leadId","abc123");
        body.put("shopInsuranceOpted","false");
        body.put("planId","13124");
        body.put("name","SB Shop Insurance 1");
        body.put("pid","1235131993");
        body.put("price","401");

        body.put("agentCustId","1107195733");


        ins=new SBShopInsuranceUpdateLead();
        response=services.updateSBShopInsurance(ins, body, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 500);
	}
	
	@Test
	public void verify_Error_Code_When_Lead_ID_Is_Already_Closed() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("leadId","dcce72e6-9453-4769-822c-e9aa06f06bf");
        body.put("shopInsuranceOpted","false");
        body.put("planId","13124");
        body.put("name","SB Shop Insurance 1");
        body.put("pid","1235131993");
        body.put("price","401");

        body.put("agentCustId","1107195733");


        ins=new SBShopInsuranceUpdateLead();
        response=services.updateSBShopInsurance(ins, body, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 500);
	}
	
	@Test
	public void verify_Ref_Code_Status_Message_Are_Present_In_Case_Of_Failure() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("leadId","9ec48e23-6173-4e9b-ee3963ee54c9 ");
        body.put("shopInsuranceOpted","false");
        body.put("planId","13124");
        body.put("name","SB Shop Insurance 1");
        body.put("pid","1235131993");
        body.put("price","401");

        body.put("agentCustId","1107195733");


        ins=new SBShopInsuranceUpdateLead();
        response=services.updateSBShopInsurance(ins, body, headers);	
       
        Assert.assertTrue(!response.path("displayMessage").toString().isEmpty());
        Assert.assertTrue(!response.path("refId").toString().isEmpty());
        Assert.assertTrue(!response.path("statusCode").toString().isEmpty());
	}

	@Test
	public void verify_Ref_Code_Status_Message_Are_Present_In_Case_Of_Success() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("leadId","7a526521-a203-4de9-b807-328bb5813624");
        body.put("shopInsuranceOpted","false");
        body.put("planId","13124");
        body.put("name","SB Shop Insurance 1");
        body.put("pid","1235131993");
        body.put("price","401");

        body.put("agentCustId","1107195733");


        ins=new SBShopInsuranceUpdateLead();
        response=services.updateSBShopInsurance(ins, body, headers);	
       
        Assert.assertTrue(!response.path("displayMessage").toString().isEmpty());
        Assert.assertTrue(!response.path("refId").toString().isEmpty());
        Assert.assertTrue(!response.path("statusCode").toString().isEmpty());
	}

	@Test
	public void verify_Error_Code_When_Insurance_Details_Are_Missing() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("leadId","9ec48e23-6173-4e9b-99d6-ee3963ee54c9");
        body.put("shopInsuranceOpted","false");
      

        ins=new SBShopInsuranceUpdateLead();
        response=services.updateSBShopInsurance(ins, body, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 400);
	}

	
	@Test
	public void verify_Error_Code_In_Case_Of_Session_Token_Is_Missing() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
	
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        

        body.put("leadId","9ec48e23-6173-4e9b-99d6-ee3963ee54c9");
        body.put("shopInsuranceOpted","false");
        body.put("planId","13124");
        body.put("name","SB Shop Insurance 1");
        body.put("pid","1235131993");
        body.put("price","401");

        body.put("agentCustId","1107195733");


        ins=new SBShopInsuranceUpdateLead();
        response=services.updateSBShopInsurance(ins, body, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 401);
	}


	@Test
	public void verify_Error_Code_In_Case_Of_Version_Is_Missing() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
	

        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
		headers.put("session_token", sessionToken);


        body.put("leadId","9ec48e23-6173-4e9b-99d6-ee3963ee54c9");
        body.put("shopInsuranceOpted","false");
        body.put("planId","13124");
        body.put("name","SB Shop Insurance 1");
        body.put("pid","1235131993");
        body.put("price","401");

        body.put("agentCustId","1107195733");


        ins=new SBShopInsuranceUpdateLead();
        response=services.updateSBShopInsurance(ins, body, headers);	
       
        Assert.assertEquals(response.path("displayMessage"), "version is empty in header");
	}

}