package OCL.Individual.SoundBox;

import Request.SoundBox.SoundBoxLead;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSchException;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class TestSoundBoxFlow extends BaseMethod {
    public static String AgentToken = "";
    public static String MerchantcustId = "1001224789";
    public static String OTP = "";
    public static String mobileNo = "5599977821";
    public static String UserMID = "";
    public static String MerchantMID = "";
    public static String MERCHANT_NAME = "";
    public static String EntityType = "INDIVIDUAL";
    public static String solutionType = "sound_box";
    public static String userType = "sound_box";
    public static String version = "5.3.4";
    public static String State = "";
    public static String AgentCust_ID = "1001647902";
    public static String deviceType = "";
    public static String solutionSubType = "";
    public static String SoundBox_Lead_ID = "";
    public static String relatedBusinessUuid = "";
    public static String PG_PLUS_MID = "";
    public static String PG_MID = "";
    public static String PinCode = "110032";
    public static String tncSet = "sound_box";
    public static String Morefun_Lead_ID = "";
    public static String deviceId = "";
    public static String questionAlias = "";
    public static String answerAlias = "";



    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(TestSoundBoxFlow.class);

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLoginSoundBox() throws Exception {
       // AgentToken = AgentSessionToken("9953828631", "paytm@123");
        LOGGER.info("Agent Token  for Soundbox : " + AgentToken);
        AgentToken = CommonAgentToken;

        DBConnection.UpdateQueryToCloseLead(mobileNo,"sound_box");
        /*TestBase testBase =new TestBase();
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '"+mobileNo+"' and status = '0' and solution_type='sound_box';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes); */

       /* SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp, EntityType, solutionType, AgentToken, version, mobileNo, userType);
        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        State = SendOtpResp.jsonPath().getString("state");
        LOGGER.info("State is : " + State);
        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        waitForLoad(2000);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("validateOtpSoundBox"));

        OTP = getOTP(mobileNo);
        //    OTP="888888";
        LOGGER.info("This is OTP " + OTP);
        Response validateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObj, EntityType, solutionType, AgentToken, version, mobileNo, userType, State, OTP);
         StatusCode = validateOtp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        custId = validateOtp.jsonPath().getString("custId");
        LOGGER.info("Merchant cust ID is : " + custId);*/

    }

    @Test(priority = 1, description = "Fetch Merchant's MID", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void GetMerchantMID() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("merchantCustId", MerchantcustId);

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");


        Response responseObject1 = middlewareServicesObject.FetchMID(queryParams, headers);


//        LOGGER.info("UserMID : " + responseObject1.jsonPath().getString("mids[0].mid"));
//        Assert.assertEquals(responseObject1.jsonPath().getString("mids[0].mid"), "yfJtEq47107147055914");
//
//        MerchantMID = responseObject1.jsonPath().getString("mids[0].mid");
//
//
//        LOGGER.info("MERCHANT_NAME : " + responseObject1.jsonPath().getString("mids[0].MERCHANT_NAME"));
//        Assert.assertEquals(responseObject1.jsonPath().getString("mids[0].MERCHANT_NAME"), "TestBeneficiary");
//
//        MERCHANT_NAME = responseObject1.jsonPath().getString("mids[0].MERCHANT_NAME");
//
//        int statusCode = responseObject1.getStatusCode();
//        Assert.assertEquals(statusCode, 200);


    }

    @Test(priority = 1, description = "Negative case to Fetch Merchant's MID without sending CustID value", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativeCaseGetMerchantMIDwithoutsendCustID() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("merchantCustId", "");

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");


//        Response responseFetchMidWithoutSendCustIDValue = middlewareServicesObject.FetchMID(queryParams, headers);
//
//        int statusCode = responseFetchMidWithoutSendCustIDValue.getStatusCode();
//        Assert.assertEquals(statusCode, 400);


    }

    @Test(priority = 1, description = "Negative case to Fetch Merchant's MID by sending invalid CustID value", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativeCaseGetMerchantMIDwithInvalidCustID() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("merchantCustId", "100122478");

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");


        Response responseFetchMidWithInvalidCustIDValue = middlewareServicesObject.FetchMID(queryParams, headers);

//        int statusCode = responseFetchMidWithInvalidCustIDValue.getStatusCode();
//        Assert.assertEquals(statusCode, 200);
//
//        Assert.assertTrue(responseFetchMidWithInvalidCustIDValue.jsonPath().getString("message").contains("Mid's not present."));

    }

    @Test(priority = 1, description = "Negative case to Fetch Merchant's MID by sending invalid key for CustID", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativeCaseGetMerchantMIDwithInValidKeyForCustID() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("merchantCustID", MerchantcustId);

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");


//        Response responseFetchMidWithInvalidKeyCustID = middlewareServicesObject.FetchMID(queryParams, headers);
//
//        int statusCode = responseFetchMidWithInvalidKeyCustID.getStatusCode();
//        Assert.assertEquals(statusCode, 400);

    }



    @Test(priority = 1, description = "create lead for soundbox Bind Flow", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void SoundboxLeadCreate() {

        SoundBoxLead SoundboxLeadObj = new SoundBoxLead("MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxCreateLeadRequest.json");

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("agentCustId", AgentCust_ID);
        body.put("merchantName", MERCHANT_NAME);
        body.put("mid", MerchantMID);
        body.put("userCustId", MerchantcustId);
        body.put("userMobile", mobileNo);
        body.put("entityType", EntityType);
        body.put("solutionSubType", "sound_box_bind");
        body.put("deviceType", "sound_box");


        Response ResponseSoundboxLeadCreate = middlewareServicesObject.Lead(SoundboxLeadObj,queryParams, headers, body);

//        SoundBox_Lead_ID = ResponseSoundboxLeadCreate.jsonPath().getString("leadId");
//        LOGGER.info("Soundbox Lead ID is : " + SoundBox_Lead_ID);
//        int StatusCode = ResponseSoundboxLeadCreate.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, description = "Negative case for soundbox lead create with no mid", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativecaseSoundboxLeadCreateNoMid() {

        SoundBoxLead SoundboxLeadObj = new SoundBoxLead("MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxNoMidRequest.json");

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("agentCustId", AgentCust_ID);
        body.put("merchantName", MERCHANT_NAME);

        body.put("userCustId", MerchantcustId);
        body.put("userMobile", mobileNo);
        body.put("entityType", EntityType);
        body.put("solutionSubType", "sound_box_bind");
        body.put("deviceType", "sound_box");


        Response ResponseSoundboxNoMid = middlewareServicesObject.Lead(SoundboxLeadObj,queryParams, headers, body);

//        int StatusCode = ResponseSoundboxNoMid.getStatusCode();
//        Assert.assertEquals(StatusCode, 400);
//
//        Assert.assertTrue(ResponseSoundboxNoMid.jsonPath().getString("displayMessage").contains("Mid cannot be blank"));

    }

    @Test(priority = 1, description = "Negative case for soundbox lead create with no Solution sub type", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativecaseSoundboxLeadCreateNoSolutionSubType() {

        SoundBoxLead SoundboxLeadObj = new SoundBoxLead("MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxNoSolutionSubTypeRequest.json");

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("agentCustId", AgentCust_ID);
        body.put("merchantName", MERCHANT_NAME);
        body.put("mid", MerchantMID);
        body.put("userCustId", MerchantcustId);
        body.put("userMobile", mobileNo);
        body.put("entityType", EntityType);
        body.put("deviceType", "sound_box");


        Response ResponseSoundboxNoSolutionSUbType = middlewareServicesObject.Lead(SoundboxLeadObj,queryParams, headers, body);

//        int StatusCode = ResponseSoundboxNoSolutionSUbType.getStatusCode();
//        Assert.assertEquals(StatusCode, 400);
//
//        Assert.assertTrue(ResponseSoundboxNoSolutionSUbType.jsonPath().getString("displayMessage").contains("SolutionSubType cannot be blank"));


    }

    @Test(priority = 1, description = "Negative case for soundbox lead create with no Entity Type", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativecaseSoundboxLeadCreateNoEntityType() {

        SoundBoxLead SoundboxLeadObj = new SoundBoxLead("MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxNoEntityTypeRequest.json");

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("agentCustId", AgentCust_ID);
        body.put("merchantName", MERCHANT_NAME);
        body.put("mid", MerchantMID);
        body.put("userCustId", MerchantcustId);
        body.put("userMobile", mobileNo);
        body.put("solutionSubType", "sound_box_bind");
        body.put("deviceType", "sound_box");


        Response ResponseSoundboxLeadCreateNoEntityType = middlewareServicesObject.Lead(SoundboxLeadObj,queryParams, headers, body);


//        int StatusCode = ResponseSoundboxLeadCreateNoEntityType.getStatusCode();
//        Assert.assertEquals(StatusCode, 400);
//
//        Assert.assertTrue(ResponseSoundboxLeadCreateNoEntityType.jsonPath().getString("displayMessage").contains("Entity Type cannot be blank"));
//

    }


    @Test(priority = 1, description = "Negative case for soundbox lead create with no user cust ID", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativecaseSoundboxLeadCreateNoUserCustID() {

        SoundBoxLead SoundboxLeadObj = new SoundBoxLead("MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxNoUserCustIDRequest.json");

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("agentCustId", AgentCust_ID);
        body.put("merchantName", MERCHANT_NAME);
        body.put("mid", MerchantMID);
        body.put("userMobile", mobileNo);
        body.put("entityType", EntityType);
        body.put("solutionSubType", "sound_box_bind");
        body.put("deviceType", "sound_box");


        Response ResponseSoundboxLeadCreateNoUserCustID = middlewareServicesObject.Lead(SoundboxLeadObj,queryParams, headers, body);


//        int StatusCode = ResponseSoundboxLeadCreateNoUserCustID.getStatusCode();
//        Assert.assertEquals(StatusCode, 500);
//
//        Assert.assertTrue(ResponseSoundboxLeadCreateNoUserCustID.jsonPath().getString("displayMessage").contains("User CustId cannot be blank"));
//

    }

    @Test(priority = 1, description = "Negative case for soundbox lead create with no Agent cust ID", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativecaseSoundboxLeadCreateNoAgentCustID() {

        SoundBoxLead SoundboxLeadObj = new SoundBoxLead("MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxNoAgentCustIDRequest.json");

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("merchantName", MERCHANT_NAME);
        body.put("mid", MerchantMID);
        body.put("userMobile", mobileNo);
        body.put("entityType", EntityType);
        body.put("solutionSubType", "sound_box_bind");
        body.put("deviceType", "sound_box");
        body.put("userCustId", MerchantcustId);


        Response ResponseSoundboxLeadCreateNoAgentCustID = middlewareServicesObject.Lead(SoundboxLeadObj,queryParams, headers, body);


        int StatusCode = ResponseSoundboxLeadCreateNoAgentCustID.getStatusCode();
       // Assert.assertEquals(StatusCode, 400);

       // Assert.assertTrue(ResponseSoundboxLeadCreateNoAgentCustID.jsonPath().getString("displayMessage").contains("Agent CustId cannot be blank"));


    }

    @Test(priority = 1, description = "Negative case for soundbox lead create with no Merchant name", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativecaseSoundboxLeadCreateNoMerchantName() {

        SoundBoxLead SoundboxLeadObj = new SoundBoxLead("MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxNoMerchantNameRequest.json");

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("agentCustId", AgentCust_ID);
        body.put("mid", MerchantMID);
        body.put("userMobile", mobileNo);
        body.put("entityType", EntityType);
        body.put("solutionSubType", "sound_box_bind");
        body.put("deviceType", "sound_box");
        body.put("userCustId", MerchantcustId);


        Response ResponseSoundboxLeadCreateNoMerchantName = middlewareServicesObject.Lead(SoundboxLeadObj,queryParams, headers, body);


//        int StatusCode = ResponseSoundboxLeadCreateNoMerchantName.getStatusCode();
//        Assert.assertEquals(StatusCode, 400);
//
//        Assert.assertTrue(ResponseSoundboxLeadCreateNoMerchantName.jsonPath().getString("displayMessage").contains("Merchant Name cannot be blank"));
//

    }

    @Test(priority = 1, description = "Negative case for soundbox lead create with Invalid MID", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativecaseSoundboxLeadCreateInvalidMID() {

        SoundBoxLead SoundboxLeadObj = new SoundBoxLead("MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxCreateLeadRequest.json");

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("agentCustId", AgentCust_ID);
        body.put("merchantName", MERCHANT_NAME);
        body.put("mid", "juJhhX9036227556081");
        body.put("userCustId", MerchantcustId);
        body.put("userMobile", mobileNo);
        body.put("entityType", EntityType);
        body.put("solutionSubType", "sound_box_bind");
        body.put("deviceType", "sound_box");


        Response ResponseSoundboxLeadCreateInvalidMID = middlewareServicesObject.Lead(SoundboxLeadObj,queryParams, headers, body);

//        int StatusCode = ResponseSoundboxLeadCreateInvalidMID.getStatusCode();
//        Assert.assertEquals(StatusCode, 410);
//
//        Assert.assertTrue(ResponseSoundboxLeadCreateInvalidMID.jsonPath().getString("displayMessage").contains("Unable to fetch PG Plus Mid!!"));
//

    }


    @Test(priority = 1, description = "create lead for Morefun  Flow", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void morefunLeadCreate() {

        SoundBoxLead MorefunLeadObj = new SoundBoxLead("MerchantService/V1/SoundBox/SoundBoxCreateLead/MorefunCreateLeadRequest.json");

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("agentCustId", AgentCust_ID);
        body.put("merchantName", MERCHANT_NAME);
        body.put("mid", MerchantMID);
        body.put("userCustId", MerchantcustId);
        body.put("userMobile", mobileNo);
        body.put("entityType", EntityType);
        body.put("solutionSubType", "sound_box_bind");
        body.put("deviceType", "morefun");


        Response ResponseSoundboxLeadCreate = middlewareServicesObject.Lead(MorefunLeadObj,queryParams, headers, body);
//
//        Morefun_Lead_ID = ResponseSoundboxLeadCreate.jsonPath().getString("leadId");
//        LOGGER.info("Morefun Lead ID is : " + Morefun_Lead_ID);
//        int StatusCode = ResponseSoundboxLeadCreate.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);

    }





    @Test(priority = 1, groups = {"Regression"}, description = "Fetch SoundBox Device Type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void GetSoundBoxDeviceType() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("deviceType", "sound_box");

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");


        Response responseObject = middlewareServicesObject.soundboxDeviceType(queryParams, headers);

//        LOGGER.info("Percentage : " + responseObject.jsonPath().getString("deviceDetails.sound_box.applicableTaxes[0].percentage"));
//        Assert.assertEquals(responseObject.jsonPath().getString("deviceDetails.sound_box.applicableTaxes[0].percentage"), "18");
//
//        LOGGER.info("Price : " + responseObject.jsonPath().getString("deviceDetails.sound_box.typeToModelPriceMap.2G.PSB1[0].price"));
//        Assert.assertEquals(responseObject.jsonPath().getString("deviceDetails.sound_box.typeToModelPriceMap.2G.PSB1[0].price"), "499.0");
//
//        LOGGER.info("rentalType : " + responseObject.jsonPath().getString("deviceDetails.sound_box.typeToModelPriceMap.2G.PSB1[0].rentalType"));
//        Assert.assertEquals(responseObject.jsonPath().getString("deviceDetails.sound_box.typeToModelPriceMap.2G.PSB1[0].rentalType"), "Monthly Plan");
//
//        LOGGER.info("securityDeposit : " + responseObject.jsonPath().getString("deviceDetails.sound_box.typeToModelPriceMap.2G.PSB1[0].securityDeposit"));
//        Assert.assertEquals(responseObject.jsonPath().getString("deviceDetails.sound_box.typeToModelPriceMap.2G.PSB1[0].securityDeposit"), "499.0");
//
//        LOGGER.info("rentalAmount : " + responseObject.jsonPath().getString("deviceDetails.sound_box.typeToModelPriceMap.2G.PSB1[0].rentalAmount"));
//        Assert.assertEquals(responseObject.jsonPath().getString("deviceDetails.sound_box.typeToModelPriceMap.2G.PSB1[0].rentalAmount"), "125.0");
//
//        LOGGER.info("otpLength : " + responseObject.jsonPath().getString("deviceDetails.sound_box.otpLength"));
//        Assert.assertEquals(responseObject.jsonPath().getString("deviceDetails.sound_box.otpLength"), "4");
//

    }


    @Test(priority = 1, groups = {"Regression"}, description = "Fetch Morefun Device Details")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void GetSoundBoxMorefunDetails() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("deviceType", "morefun");

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");


        Response responseObject = middlewareServicesObject.soundboxDeviceType(queryParams, headers);

        /*LOGGER.info("Percentage for Morefun Device : " + responseObject.jsonPath().getString("deviceDetails.morefun.applicableTaxes[0].percentage"));
        Assert.assertEquals(responseObject.jsonPath().getString("deviceDetails.morefun.applicableTaxes[0].percentage"), "18");

        LOGGER.info("Price for Morefun device: " + responseObject.jsonPath().getString("deviceDetails.morefun.typeToModelPriceMap.2G[\"Dynamic QR+\"][0].price"));
        Assert.assertEquals(responseObject.jsonPath().getString("deviceDetails.morefun.typeToModelPriceMap.2G[\"Dynamic QR+\"][0].price"), "674.0");

        LOGGER.info("rentalType for Morefun Device : " + responseObject.jsonPath().getString("deviceDetails.morefun.typeToModelPriceMap.2G[\"Dynamic QR+\"][0].rentalType"));
        Assert.assertEquals(responseObject.jsonPath().getString("deviceDetails.morefun.typeToModelPriceMap.2G[\"Dynamic QR+\"][0].rentalType"), "SD+Monthly Fee");

        LOGGER.info("securityDeposit for Morefun device: " + responseObject.jsonPath().getString("deviceDetails.morefun.typeToModelPriceMap.2G[\"Dynamic QR+\"][0].securityDeposit"));
        Assert.assertEquals(responseObject.jsonPath().getString("deviceDetails.morefun.typeToModelPriceMap.2G[\"Dynamic QR+\"][0].securityDeposit"), "674.0");

        LOGGER.info("rentalAmount for Morefun device: " + responseObject.jsonPath().getString("deviceDetails.morefun.typeToModelPriceMap.2G[\"Dynamic QR+\"][0].rentalAmount"));
        Assert.assertEquals(responseObject.jsonPath().getString("deviceDetails.morefun.typeToModelPriceMap.2G[\"Dynamic QR+\"][0].rentalAmount"), "175.0");

        LOGGER.info("otpLength for Morefun device: " + responseObject.jsonPath().getString("deviceDetails.morefun.otpLength"));
        Assert.assertEquals(responseObject.jsonPath().getString("deviceDetails.morefun.otpLength"), "6");

        LOGGER.info("Device Name : " + responseObject.jsonPath().getString("deviceDetails.morefun.deviceName"));
        Assert.assertEquals(responseObject.jsonPath().getString("deviceDetails.morefun.deviceName"), "Dynamic QR+");*/



    }

    @Test(priority = 1, description = "Fetch priceMappindDetails for Soundbox Bind flow", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchPlanSoundBox() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("leadId", SoundBox_Lead_ID);
        queryParams.put("deviceType", "sound_box");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("Content-Type", "application/json");

        Response ResponseFetchPlan = middlewareServicesObject.FetchPlan(queryParams, headers);
//
//        LOGGER.info("Percentage : " + ResponseFetchPlan.jsonPath().getString("applicableTaxes[0].percentage"));
//        Assert.assertEquals(ResponseFetchPlan.jsonPath().getString("applicableTaxes[0].percentage"), "18");
//
//        LOGGER.info("Price : " + ResponseFetchPlan.jsonPath().getString("typeToModelPriceMap.2G.PSB1[0].price"));
//        Assert.assertEquals(ResponseFetchPlan.jsonPath().getString("typeToModelPriceMap.2G.PSB1[0].price"), "499.0");
//
//        LOGGER.info("rentalType : " + ResponseFetchPlan.jsonPath().getString("typeToModelPriceMap.2G.PSB1[0].rentalType"));
//        Assert.assertEquals(ResponseFetchPlan.jsonPath().getString("typeToModelPriceMap.2G.PSB1[0].rentalType"), "SD+Monthly Fee");
//
//        LOGGER.info("securityDeposit : " + ResponseFetchPlan.jsonPath().getString("typeToModelPriceMap.2G.PSB1[0].securityDeposit"));
//        Assert.assertEquals(ResponseFetchPlan.jsonPath().getString("typeToModelPriceMap.2G.PSB1[0].securityDeposit"), "499.0");
//
//        LOGGER.info("rentalAmount : " + ResponseFetchPlan.jsonPath().getString("typeToModelPriceMap.2G.PSB1[0].rentalAmount"));
//        Assert.assertEquals(ResponseFetchPlan.jsonPath().getString("typeToModelPriceMap.2G.PSB1[0].rentalAmount"), "125.0");

    }

    @Test(priority = 1, description = "Negative case to Fetch priceMappindDetails for Soundbox Bind flow without sending lead id", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativecaseFetchPlanSoundBoxwithoutLeadID() {

        Map<String, String> queryParams = new HashMap<String, String>();


        queryParams.put("deviceType", "sound_box");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("Content-Type", "application/json");

        Response ResponseFetchPlanWithoutLeadID = middlewareServicesObject.FetchPlan(queryParams, headers);


        int StatusCode = ResponseFetchPlanWithoutLeadID.getStatusCode();
//        Assert.assertEquals(StatusCode, 400);
//
//        Assert.assertTrue(ResponseFetchPlanWithoutLeadID.jsonPath().getString("displayMessage").contains("Invalid request, leadId not specified"));

    }





    @Test(priority = 1, description = "Fetch priceMappindDetails for Morefun flow", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchPlanMorefun() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("leadId", Morefun_Lead_ID);
        queryParams.put("deviceType","morefun" );

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("Content-Type", "application/json");

        Response ResponseFetchPlanforMorefun = middlewareServicesObject.FetchPlan(queryParams, headers);

        /*LOGGER.info("Price mapping details for Morefun Percentage : " + ResponseFetchPlanforMorefun.jsonPath().getString("applicableTaxes[0].percentage"));
        Assert.assertEquals(ResponseFetchPlanforMorefun.jsonPath().getString("applicableTaxes[0].percentage"), "18");

        LOGGER.info("Price mapping details for Morefun Price : " + ResponseFetchPlanforMorefun.jsonPath().getString("typeToModelPriceMap.2G[\"Dynamic QR+\"][0].price"));
        Assert.assertEquals(ResponseFetchPlanforMorefun.jsonPath().getString("typeToModelPriceMap.2G[\"Dynamic QR+\"][0].price"), "918.0");

        LOGGER.info("Price mapping details for Morefun rentalType : " + ResponseFetchPlanforMorefun.jsonPath().getString("typeToModelPriceMap.2G[\"Dynamic QR+\"][0].rentalType"));
        Assert.assertEquals(ResponseFetchPlanforMorefun.jsonPath().getString("typeToModelPriceMap.2G[\"Dynamic QR+\"][0].rentalType"), "One time plan for 18 months");

        LOGGER.info("Price mapping details for Morefun securityDeposit : " + ResponseFetchPlanforMorefun.jsonPath().getString("typeToModelPriceMap.2G[\"Dynamic QR+\"][0].securityDeposit"));
        Assert.assertEquals(ResponseFetchPlanforMorefun.jsonPath().getString("typeToModelPriceMap.2G[\"Dynamic QR+\"][0].securityDeposit"), "918.0");

        LOGGER.info("Price mapping details for Morefun rentalAmount : " + ResponseFetchPlanforMorefun.jsonPath().getString("typeToModelPriceMap.2G[\"Dynamic QR+\"][0].rentalAmount"));
        Assert.assertEquals(ResponseFetchPlanforMorefun.jsonPath().getString("typeToModelPriceMap.2G[\"Dynamic QR+\"][0].rentalAmount"), "118.0");

        LOGGER.info("Price mapping details for Morefun device name : " + ResponseFetchPlanforMorefun.jsonPath().getString("deviceName"));
        Assert.assertEquals(ResponseFetchPlanforMorefun.jsonPath().getString("deviceName"), "Dynamic QR+");*/

    }



        @Test(priority = 1, groups = {"Regression"}, description = "Count Soundbox lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void GetSoundBoxLeadCount() {
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Response ResponseLeadCount = middlewareServicesObject.soundboxLeadCount(queryParams, headers);

//        LOGGER.info("LeadCountClosed : " + ResponseLeadCount.jsonPath().getString("leadsCount.closed"));
//        Assert.assertEquals(ResponseLeadCount.jsonPath().getString("leadsCount.closed"), "0");
//
//        LOGGER.info("LeadCountAssigned : " + ResponseLeadCount.jsonPath().getString("leadsCount.assigned"));
//        Assert.assertEquals(ResponseLeadCount.jsonPath().getString("leadsCount.assigned"), "0");
//
//        LOGGER.info("LeadCountOpen : " + ResponseLeadCount.jsonPath().getString("leadsCount.open"));
//        Assert.assertEquals(ResponseLeadCount.jsonPath().getString("leadsCount.open"), "0");

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get soundbox merchant details")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void GetSoundBoxMerchantDetails() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("leadId", SoundBox_Lead_ID);
        queryParams.put("entityType", EntityType);
        queryParams.put("solutionType", solutionType);


        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Response ResponseMerchantDetails = middlewareServicesObject.soundboxMerchantDetails(queryParams, headers, MerchantcustId);

//        LOGGER.info("SOLUTION_TYPE_LEVEL_3 : " + ResponseMerchantDetails.jsonPath().getString("merchantDetails.solutionAdditionalInfo.SOLUTION_TYPE_LEVEL_3"));
//        Assert.assertEquals(ResponseMerchantDetails.jsonPath().getString("merchantDetails.solutionAdditionalInfo.SOLUTION_TYPE_LEVEL_3"), "sound_box");
//
//        LOGGER.info("SOLUTION_TYPE_LEVEL_2 : " + ResponseMerchantDetails.jsonPath().getString("merchantDetails.solutionAdditionalInfo.SOLUTION_TYPE_LEVEL_2"));
//        Assert.assertEquals(ResponseMerchantDetails.jsonPath().getString("merchantDetails.solutionAdditionalInfo.SOLUTION_TYPE_LEVEL_2"), "sound_box_bind");
//
//        LOGGER.info("IS_OMS_FLOW : " + ResponseMerchantDetails.jsonPath().getString("merchantDetails.solutionAdditionalInfo.IS_OMS_FLOW"));
//        Assert.assertEquals(ResponseMerchantDetails.jsonPath().getString("merchantDetails.solutionAdditionalInfo.IS_OMS_FLOW"), "true");
//
//        LOGGER.info("substage : " + ResponseMerchantDetails.jsonPath().getString("merchantDetails.substage"));
//        Assert.assertEquals(ResponseMerchantDetails.jsonPath().getString("merchantDetails.substage"), "Lead Created");
//
//        LOGGER.info("nameOfShop : " + ResponseMerchantDetails.jsonPath().getString("merchantDetails.nameOfShop"));
//        Assert.assertEquals(ResponseMerchantDetails.jsonPath().getString("merchantDetails.nameOfShop"), "TestBeneficiary");
//
//        relatedBusinessUuid = ResponseMerchantDetails.jsonPath().getString("merchantDetails.relatedBusinessUuid");
//        LOGGER.info("relatedBusinessUuid is : " + relatedBusinessUuid);
//
//        PG_PLUS_MID = ResponseMerchantDetails.jsonPath().getString("merchantDetails.solutionAdditionalInfo.PG_PLUS_MID");
//        LOGGER.info("PG_PLUS_MID is : " + PG_PLUS_MID);
//
//        PG_MID = ResponseMerchantDetails.jsonPath().getString("merchantDetails.solutionAdditionalInfo.PG_MID");
//        LOGGER.info("PG_MID is : " + PG_MID);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Negative case with empty value in lead ID for fetching merchant details for SoundBox Device")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativeCaseGetSoundBoxMerchantDetailswithoutLeadID() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("leadId", "");
        queryParams.put("entityType", EntityType);
        queryParams.put("solutionType", solutionType);


        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Response ResponseMerchantDetailsWithoutLeadID = middlewareServicesObject.soundboxMerchantDetails(queryParams, headers, MerchantcustId);


//        int StatusCode = ResponseMerchantDetailsWithoutLeadID.getStatusCode();
//        Assert.assertEquals(StatusCode, 500);
//
//        Assert.assertTrue(ResponseMerchantDetailsWithoutLeadID.jsonPath().getString("message").contains("Failed to fetch merchant lead details"));
//

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Negative case with invalid value in lead ID for fetching merchant details for SoundBox Device")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativeCaseGetSoundBoxMerchantDetailsInvalidLeadID() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("leadId", "ee87e731-2e67-47ca-984a-78b2f4164faw");
        queryParams.put("entityType", EntityType);
        queryParams.put("solutionType", solutionType);


        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Response ResponseMerchantDetailsInvalidLeadID = middlewareServicesObject.soundboxMerchantDetails(queryParams, headers, MerchantcustId);


//        int StatusCode = ResponseMerchantDetailsInvalidLeadID.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//
//        Assert.assertTrue(ResponseMerchantDetailsInvalidLeadID.jsonPath().getString("message").contains("Data Not present for customer"));
//

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Negative case with invalid lead ID key pass to fetch merchant details for SoundBox Device")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativeCaseGetSoundBoxMerchantDetailsIncorrectLeadIDKey() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("LeadId", SoundBox_Lead_ID);
        queryParams.put("entityType", EntityType);
        queryParams.put("solutionType", solutionType);


        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Response ResponseMerchantDetailsInvalidLeadIDKey = middlewareServicesObject.soundboxMerchantDetails(queryParams, headers, MerchantcustId);


//        int StatusCode = ResponseMerchantDetailsInvalidLeadIDKey.getStatusCode();
//        Assert.assertEquals(StatusCode, 500);
//
//        Assert.assertTrue(ResponseMerchantDetailsInvalidLeadIDKey.jsonPath().getString("message").contains("Failed to fetch merchant lead details"));
//

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Negative case with no Entity Type pass to fetch merchant details for SoundBox Device")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativeCaseGetSoundBoxMerchantDetailswithNoEntityType() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("leadId", SoundBox_Lead_ID);
        queryParams.put("solutionType", solutionType);


        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Response ResponseMerchantDetailswithNoEntityType = middlewareServicesObject.soundboxMerchantDetails(queryParams, headers, MerchantcustId);

//
//        int StatusCode = ResponseMerchantDetailswithNoEntityType.getStatusCode();
//        Assert.assertEquals(StatusCode, 400);

    }



    @Test(priority = 1, groups = {"Regression"}, description = "Negative case with no lead ID  pass to fetch merchant details for SoundBox Device")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativeCaseGetSoundBoxMerchantDetailswithNoLeadID() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("leadId", SoundBox_Lead_ID);
        queryParams.put("entityType", EntityType);
        queryParams.put("solutionType", solutionType);


        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", "d58c394b-31f4-4908-a531-1282f992330");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Response ResponseMerchantDetailswithNoInvalidSessionToken = middlewareServicesObject.soundboxMerchantDetails(queryParams, headers, MerchantcustId);


//        int StatusCode = ResponseMerchantDetailswithNoInvalidSessionToken.getStatusCode();
//        Assert.assertEquals(StatusCode, 410);
//
//        Assert.assertTrue(ResponseMerchantDetailswithNoInvalidSessionToken.jsonPath().getString("message").contains("Please login again. Either your session has expired or you are already logged in on another device."));

    }




    @Test(priority = 1, groups = {"Regression"}, description = "Negative case with Invalid Session Token pass to fetch merchant details for Soundbox Device")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void NegativeCaseGetSoundBoxMerchantDetailswithInvalidSessionToken() {
        Map<String, String> queryParams = new HashMap<String, String>();


        queryParams.put("entityType", EntityType);
        queryParams.put("solutionType", solutionType);


        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Response ResponseMerchantDetailswithNoLeadID = middlewareServicesObject.soundboxMerchantDetails(queryParams, headers, MerchantcustId);


//        int StatusCode = ResponseMerchantDetailswithNoLeadID.getStatusCode();
//        Assert.assertEquals(StatusCode, 500);
//
//        Assert.assertTrue(ResponseMerchantDetailswithNoLeadID.jsonPath().getString("message").contains("Failed to fetch merchant lead details"));

    }


    @Test(priority = 1, groups = {"Regression"}, description = "Get Pincode Details")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void GetPinCodeDetails() {
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Response ResponsePincodeDetails = middlewareServicesObject.PinCodeDetails(queryParams, headers, PinCode);
//
//
//        LOGGER.info("State : " + ResponsePincodeDetails.jsonPath().getString("state"));
//        Assert.assertEquals(ResponsePincodeDetails.jsonPath().getString("state"), "Delhi");
//
//        LOGGER.info("city : " + ResponsePincodeDetails.jsonPath().getString("city"));
//        Assert.assertEquals(ResponsePincodeDetails.jsonPath().getString("city"), "East Delhi");
//
//        LOGGER.info("country : " + ResponsePincodeDetails.jsonPath().getString("country"));
//        Assert.assertEquals(ResponsePincodeDetails.jsonPath().getString("country"), "India");

    }

    @Test(priority = 1, description = "Fetch TnC SoundBox", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchTnCSoundBox() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("tncSet", tncSet);
        queryParams.put("entityType", EntityType);
        queryParams.put("solutionType", solutionType);

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("Content-Type", "application/json");

        Response ResponseFetchTnCSoundBox = middlewareServicesObject.FetchTnCSoundBoxDetails(queryParams, headers);

        int StatusCode = ResponseFetchTnCSoundBox.getStatusCode();
       // Assert.assertEquals(StatusCode, 200);


//        LOGGER.info("Status : " + ResponseFetchTnCSoundBox.jsonPath().getString("status"));
//        Assert.assertEquals(ResponseFetchTnCSoundBox.jsonPath().getString("status"), "SUCCESS");
//
//
//        LOGGER.info("URL : " + ResponseFetchTnCSoundBox.jsonPath().getString("url"));
//        Assert.assertEquals(ResponseFetchTnCSoundBox.jsonPath().getString("url"), "https://cif-staging.paytm.in/kyc/tnc/get/150001370");

    }

    @Test(priority = 1, description = "Fetch QNA SoundBox Replacement", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchSoundBoxReplacementQNA() {

        Map<String, String> queryParams = new HashMap<String, String>();


        queryParams.put("entityType", EntityType);
        queryParams.put("solutionType", solutionType);
        queryParams.put("solutionSubType", "sound_box_replacement");
        queryParams.put("questionType", "replacement");


        Map<String, String> headers = new HashMap<String, String>();

        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("Content-Type", "application/json");

        Response ResponseFetchReplaceQNA = middlewareServicesObject.FetchReplacementQNA(queryParams, headers);

    }

    @Test(priority = 1, description = "create soundbox Replacement lead Flow", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void SoundboxReplacementLeadCreate() {

        SoundBoxLead SoundboxLeadObj = new SoundBoxLead("MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxChoosePlan/SoundBoxReplacementLeadRequest.json");

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();


        headers.put("X-SRC", "GGClient");
        headers.put("latitude", "0.0");
        headers.put("ipAddress", "***********");
        headers.put("isBusyBoxFound", "false");
        headers.put("deviceName", "CPH1859");
        headers.put("version", version);
        headers.put("session_token", AgentToken);
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headers.put("isDeviceRooted", "false");
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("osVersion", "10");
        headers.put("isLocationMocked", "false");
        headers.put("client", "androidapp");
        headers.put("deviceMac", "70:5E:55:B3:F8:7B");
        headers.put("deviceManufacturer", "OPPO");
        headers.put("androidId", "bfc82c51c6f057ba");
        headers.put("appLanguage", "en");
        headers.put("longitude", "0.0");
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; RMX1851 Build/QKQ1.190918.001)");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("agentCustId", AgentCust_ID);
        body.put("merchantName", MERCHANT_NAME);
        body.put("mid", MerchantMID);
        body.put("userCustId", MerchantcustId);
        body.put("userMobile", mobileNo);
        body.put("entityType", EntityType);
        body.put("solutionSubType", "sound_box_replacement");
        body.put("deviceId", "866779040305494");
        body.put("questionAlias", "Reason for replacement");
        body.put("answerAlias", "Continuous red light");


        Response ResponseSoundboxReplacementLeadCreate = middlewareServicesObject.Lead(SoundboxLeadObj,queryParams, headers, body);



    }





    }