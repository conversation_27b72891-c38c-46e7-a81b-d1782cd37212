<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">

<suite name="SoundBox Test Suite" verbose="1" parallel="false">
    <parameter name="environment" value="staging"/>
    <parameter name="browser" value="chrome"/>
    
    <!-- Test Groups Definition -->
    <groups>
        <run>
            <include name="Regression"/>
            <include name="Smoke"/>
        </run>
    </groups>
    
    <!-- Listeners for reporting and logging -->
    <listeners>
        <listener class-name="org.testng.reporters.EmailableReporter"/>
        <listener class-name="org.testng.reporters.JUnitReportReporter"/>
    </listeners>
    
    <!-- Authentication Tests -->
    <test name="Authentication Tests" preserve-order="true">
        <groups>
            <run>
                <include name="Authentication"/>
                <include name="Regression"/>
            </run>
        </groups>
        <classes>
            <class name="OCL.Individual.SoundBox.authentication.SendOtpTests"/>
            <class name="OCL.Individual.SoundBox.authentication.OtpValidationTests"/>
        </classes>
    </test>
    
    <!-- Content Tests -->
    <test name="Content Tests" preserve-order="true">
        <groups>
            <run>
                <include name="Content"/>
                <include name="Regression"/>
            </run>
        </groups>
        <classes>
            <class name="OCL.Individual.SoundBox.content.YoutubeLinkTests"/>
        </classes>
    </test>
    
    <!-- Device Tests -->
    <test name="Device Tests" preserve-order="true">
        <groups>
            <run>
                <include name="Device"/>
                <include name="Regression"/>
            </run>
        </groups>
        <classes>
            <class name="OCL.Individual.SoundBox.device.DeviceValidationTests"/>
            <class name="OCL.Individual.SoundBox.device.IotDeviceTests"/>
        </classes>
    </test>
    
    <!-- Merchant Tests -->
    <test name="Merchant Tests" preserve-order="true">
        <groups>
            <run>
                <include name="Merchant"/>
                <include name="Regression"/>
            </run>
        </groups>
        <classes>
            <class name="OCL.Individual.SoundBox.merchant.MerchantIdTests"/>
        </classes>
    </test>
    
    <!-- Lead Management Tests -->
    <test name="Lead Management Tests" preserve-order="true">
        <groups>
            <run>
                <include name="Lead"/>
                <include name="Regression"/>
            </run>
        </groups>
        <classes>
            <!-- Will be added when classes are created -->
            <!-- <class name="OCL.Individual.SoundBox.lead.LeadCreationTests"/> -->
            <!-- <class name="OCL.Individual.SoundBox.lead.LeadManagementTests"/> -->
        </classes>
    </test>
    
    <!-- Payment Tests -->
    <test name="Payment Tests" preserve-order="true">
        <groups>
            <run>
                <include name="Payment"/>
                <include name="Regression"/>
            </run>
        </groups>
        <classes>
            <!-- Will be added when classes are created -->
            <!-- <class name="OCL.Individual.SoundBox.payment.PaymentTests"/> -->
        </classes>
    </test>
    
    <!-- Smoke Test Suite (Quick validation) -->
    <test name="Smoke Tests" preserve-order="true">
        <groups>
            <run>
                <include name="Smoke"/>
            </run>
        </groups>
        <classes>
            <!-- Key smoke tests from each category -->
            <class name="OCL.Individual.SoundBox.authentication.SendOtpTests">
                <methods>
                    <include name="verifySendOtpSmokeTest"/>
                </methods>
            </class>
            <class name="OCL.Individual.SoundBox.authentication.OtpValidationTests">
                <methods>
                    <include name="verifyOtpValidationSmokeTest"/>
                </methods>
            </class>
            <class name="OCL.Individual.SoundBox.content.YoutubeLinkTests">
                <methods>
                    <include name="verifyYoutubeLinkFetchSmokeTest"/>
                </methods>
            </class>
            <class name="OCL.Individual.SoundBox.device.DeviceValidationTests">
                <methods>
                    <include name="verifyDeviceValidationSmokeTest"/>
                </methods>
            </class>
            <class name="OCL.Individual.SoundBox.device.IotDeviceTests">
                <methods>
                    <include name="verifyIotDeviceRetrievalSmokeTest"/>
                </methods>
            </class>
            <class name="OCL.Individual.SoundBox.merchant.MerchantIdTests">
                <methods>
                    <include name="verifyMerchantIdRetrievalSmokeTest"/>
                </methods>
            </class>
        </classes>
    </test>
    
</suite>
