package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.SbInsuranceEligibility;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestSbInsuranceEligibility extends BaseMethod {

	SbInsuranceEligibility ins;
	public String sessionToken;
	String leadid="";
	public Map<String, String> body;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> params;

	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
//	@Test
//	public void verify_Status_Code_In_Case_Of_Success() {
//		ins=new SbInsuranceEligibility();
//
//		headers=new HashMap<>();
//		params=new HashMap<>();
//		services=new MiddlewareServices();
//		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
//		headers.put("session_token", sessionToken);
//		headers.put("version", "5.1.6");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        headers.put("isBusyBoxFound" , "false");
//        headers.put("osVersion" , "10");
//        headers.put("appLanguage","en");
//        headers.put("isDeviceRooted" , "false");
//        
//        params.put("leadId", leadid);
//        
//        response=services.InsuranceEligibility(ins, params, headers);	
//       
//        Assert.assertEquals(response.getStatusCode(), 200);
//	}
//	
//	
	@Test
	public void verify_Error_Code_In_Case_Of_Failure() {
		ins=new SbInsuranceEligibility();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadI", "d26a53d8-0d0c-4351-adf2-2301a86941fc");
        
        response=services.InsuranceEligibility(ins, params, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	
	@Test
	public void verify_Status_Code_In_Case_Of_Failure() {
		ins=new SbInsuranceEligibility();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadI", "d26a53d8-0d0c-4351-adf2-2301a86941fc");
        
        response=services.InsuranceEligibility(ins, params, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	
	@Test
	public void verify_Status_Code_In_Case_Of_Incorrect_lead_Id() {
		ins=new SbInsuranceEligibility();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadId", "d26a53d8-0d0c-4351-adf2-");
        
        response=services.InsuranceEligibility(ins, params, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 500);
	}
	
	
	@Test
	public void verify_Status_Code_In_Case_Of_Missing_lead_Id() {
		ins=new SbInsuranceEligibility();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadId", "");
        
        response=services.InsuranceEligibility(ins, params, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	
	@Test
	public void verify_Status_Code_In_Case_Of_Missing_Auth_Token() {
		ins=new SbInsuranceEligibility();

		headers=new HashMap<>();
		params=new HashMap<>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
				headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        params.put("leadId", "d26a53d8-0d0c-4351-adf2-2301a86941fc");
        
        response=services.InsuranceEligibility(ins, params, headers);	
       
        Assert.assertEquals(response.getStatusCode(), 401);
	}
	
//	@Test
//	public void verify_Status_Message_In_Case_Of_Merchant_Is_Not_Eligible_For_Insurance() {
//		ins=new SbInsuranceEligibility();
//
//		headers=new HashMap<>();
//		params=new HashMap<>();
//		services=new MiddlewareServices();
//		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
//		headers.put("session_token", sessionToken);
//
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        headers.put("isBusyBoxFound" , "false");
//        headers.put("osVersion" , "10");
//        headers.put("appLanguage","en");
//        headers.put("isDeviceRooted" , "false");
//        
//        params.put("leadId", "d26a53d8-0d0c-4351-adf2-2301a86941fc");
//        
//        response=services.InsuranceEligibility(ins, params, headers);	
//       
//        Assert.assertEquals(response.path("insuranceFlowEnabled").toString(), "false");	}
	
//	@Test
//	public void verify_Status_Message_In_Case_Of_Merchant_Is__Eligible_For_Insurance() {
//		ins=new SbInsuranceEligibility();
//
//		headers=new HashMap<>();
//		params=new HashMap<>();
//		services=new MiddlewareServices();
//		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
//		headers.put("session_token", sessionToken);
//
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("latitude" , "12.836047");
//        headers.put("ipAddress" , "***********");
//        headers.put("isBusyBoxFound" , "false");
//        headers.put("osVersion" , "10");
//        headers.put("appLanguage","en");
//        headers.put("isDeviceRooted" , "false");
//        
//        params.put("leadId", "d9e48112-8327-4b8f-ab5f-beeb5ced279d");
//        
//        response=services.InsuranceEligibility(ins, params, headers);	
//       
//        Assert.assertEquals(response.path("insuranceFlowEnabled").toString(), "true");	}
	
}