package OCL.Individual.SoundBox.content;

import OCL.Individual.SoundBox.base.SoundBoxBaseTest;
import OCL.Individual.SoundBox.base.SoundBoxConstants;
import OCL.Individual.SoundBox.base.SoundBoxTestUtils;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * Test class for YouTube Link functionality in SoundBox
 * Contains all tests related to YouTube link fetching (Priority 23-32)
 */
public class YoutubeLinkTests extends SoundBoxBaseTest {

    @Test(priority = 23, dependsOnMethods = {"OCL.Individual.SoundBox.authentication.OtpValidationTests.verifyOtpValidationWithValidOtp"}, 
          groups = {SoundBoxConstants.GROUP_CONTENT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify YouTube link fetch with valid data")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyYoutubeLinkFetchWithValidData() {
        Response response = SoundBoxTestUtils.executeYoutubeLinkFetch(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        // Validate response contains YouTube link
        String responseBody = response.getBody().asString();
        Assert.assertNotNull(responseBody, "Response body should not be null");
        LOGGER.info("YouTube link fetch successful");
        
        logTestCompletion("verifyYoutubeLinkFetchWithValidData", true);
    }

    @Test(priority = 24, dependsOnMethods = {"verifyYoutubeLinkFetchWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_CONTENT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify YouTube link fetch with missing X-SRC header")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyYoutubeLinkFetchWithMissingXSrc() {
        // Using custom headers to test missing X-SRC scenario
        SoundBoxTestUtils.executeYoutubeLinkFetchWithCustomToken(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyYoutubeLinkFetchWithMissingXSrc", true);
    }

    @Test(priority = 25, dependsOnMethods = {"verifyYoutubeLinkFetchWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_CONTENT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify YouTube link fetch with missing version")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyYoutubeLinkFetchWithMissingVersion() {
        SoundBoxTestUtils.executeYoutubeLinkFetchWithCustomToken(middlewareServicesObject, 
            AgentToken, "", SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyYoutubeLinkFetchWithMissingVersion", true);
    }

    @Test(priority = 26, dependsOnMethods = {"verifyYoutubeLinkFetchWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_CONTENT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify YouTube link fetch with old version")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyYoutubeLinkFetchWithOldVersion() {
        SoundBoxTestUtils.executeYoutubeLinkFetchWithCustomToken(middlewareServicesObject, 
            AgentToken, "1.0.1", SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyYoutubeLinkFetchWithOldVersion", true);
    }

    @Test(priority = 27, dependsOnMethods = {"verifyYoutubeLinkFetchWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_CONTENT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify YouTube link fetch with empty agent token")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyYoutubeLinkFetchWithEmptyAgentToken() {
        SoundBoxTestUtils.executeYoutubeLinkFetchWithCustomToken(middlewareServicesObject, 
            "", SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyYoutubeLinkFetchWithEmptyAgentToken", true);
    }

    @Test(priority = 28, dependsOnMethods = {"verifyYoutubeLinkFetchWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_CONTENT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify YouTube link fetch with missing device identifier")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyYoutubeLinkFetchWithMissingDeviceIdentifier() {
        SoundBoxTestUtils.executeYoutubeLinkFetchWithCustomHeaders(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, "", SoundBoxConstants.CONTENT_TYPE, 
            SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyYoutubeLinkFetchWithMissingDeviceIdentifier", true);
    }

    @Test(priority = 29, dependsOnMethods = {"verifyYoutubeLinkFetchWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_CONTENT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify YouTube link fetch with invalid checksum")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyYoutubeLinkFetchWithInvalidChecksum() {
        // This test simulates invalid checksum scenario
        SoundBoxTestUtils.executeYoutubeLinkFetch(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyYoutubeLinkFetchWithInvalidChecksum", true);
    }

    @Test(priority = 30, dependsOnMethods = {"verifyYoutubeLinkFetchWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_CONTENT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify YouTube link fetch with missing Content-Type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyYoutubeLinkFetchWithMissingContentType() {
        SoundBoxTestUtils.executeYoutubeLinkFetchWithCustomHeaders(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.DEVICE_IDENTIFIER, "", 
            SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyYoutubeLinkFetchWithMissingContentType", true);
    }

    @Test(priority = 31, dependsOnMethods = {"verifyYoutubeLinkFetchWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_CONTENT, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify YouTube link fetch with expired agent token")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyYoutubeLinkFetchWithExpiredToken() {
        String expiredToken = "eyJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiZGlyIn0..expired.token.value";
        
        SoundBoxTestUtils.executeYoutubeLinkFetchWithCustomToken(middlewareServicesObject, 
            expiredToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifyYoutubeLinkFetchWithExpiredToken", true);
    }

    @Test(priority = 32, dependsOnMethods = {"verifyYoutubeLinkFetchWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_CONTENT, SoundBoxConstants.GROUP_SMOKE}, 
          description = "Verify YouTube link fetch smoke test - quick validation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyYoutubeLinkFetchSmokeTest() {
        Response response = SoundBoxTestUtils.executeYoutubeLinkFetch(middlewareServicesObject, 
            AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        // Basic validation for smoke test
        Assert.assertNotNull(response.getBody(), "Response body should not be null");
        Assert.assertTrue(response.getTime() < 5000, "Response time should be less than 5 seconds");
        LOGGER.info("YouTube link fetch smoke test passed - Response time: {} ms", response.getTime());
        
        logTestCompletion("verifyYoutubeLinkFetchSmokeTest", true);
    }

    /**
     * Helper method to validate YouTube link response structure
     * @param response API response
     */
    private void validateYoutubeLinkResponse(Response response) {
        String responseBody = response.getBody().asString();
        Assert.assertNotNull(responseBody, "Response body should not be null");
        
        // Additional validations can be added here based on expected response structure
        LOGGER.info("YouTube link response validation completed");
    }

    /**
     * Helper method to test YouTube link fetch with retry logic
     * @param maxRetries Maximum number of retries
     */
    private void executeYoutubeLinkFetchWithRetry(int maxRetries) {
        int attempts = 0;
        Response response = null;
        
        while (attempts < maxRetries) {
            try {
                response = SoundBoxTestUtils.executeYoutubeLinkFetch(middlewareServicesObject, 
                    AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
                break; // Success, exit loop
            } catch (AssertionError e) {
                attempts++;
                if (attempts >= maxRetries) {
                    throw e; // Re-throw if max retries reached
                }
                LOGGER.warn("YouTube link fetch attempt {} failed, retrying...", attempts);
                // Re-login and retry
                AgentLoginSoundBox();
            }
        }
        
        Assert.assertNotNull(response, "Response should not be null after retries");
        validateYoutubeLinkResponse(response);
    }
}
