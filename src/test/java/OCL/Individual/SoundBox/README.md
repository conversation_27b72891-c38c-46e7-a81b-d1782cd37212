# SoundBox Test Suite

## 📋 Overview
This is a comprehensive test suite for SoundBox functionality, refactored from a monolithic class into a modular, maintainable structure.

## 🏗️ Architecture

### Directory Structure
```
src/test/java/OCL/Individual/SoundBox/
├── base/                           # Base infrastructure
│   ├── SoundBoxBaseTest.java      # Common setup and configuration
│   ├── SoundBoxTestUtils.java     # Shared utility methods
│   └── SoundBoxConstants.java     # Constants and static values
├── authentication/                # Authentication tests
│   ├── SendOtpTests.java         # Send OTP tests (Priority 1-10)
│   └── OtpValidationTests.java   # OTP validation tests (Priority 11-22)
├── content/                       # Content-related tests
│   └── YoutubeLinkTests.java     # YouTube link tests (Priority 23-32)
├── device/                        # Device management tests
│   ├── DeviceValidationTests.java # Device validation (Priority 33-46)
│   └── IotDeviceTests.java       # IoT device tests (Priority 56-63)
├── merchant/                      # Merchant operations
│   └── MerchantIdTests.java      # Merchant ID tests (Priority 47-55)
├── lead/                          # Lead management
│   ├── LeadCreationTests.java    # Lead creation (Priority 64-69)
│   └── LeadManagementTests.java  # Lead management operations
├── payment/                       # Payment processing
│   └── PaymentTests.java         # Payment tests
├── soundbox-test-suite.xml       # TestNG suite configuration
└── README.md                     # This documentation
```

## 🔧 Base Infrastructure

### SoundBoxBaseTest.java
- **Purpose**: Common setup, configuration, and shared functionality
- **Features**:
  - Agent login and token management
  - Common test data initialization
  - Shared utility methods
  - Retry logic configuration
  - Logging setup

### SoundBoxTestUtils.java
- **Purpose**: Centralized helper methods for all test operations
- **Categories**:
  - Authentication helpers (Send OTP, OTP validation)
  - Content helpers (YouTube link fetching)
  - Device helpers (Device validation, IoT operations)
  - Merchant helpers (Merchant ID operations)
  - Lead helpers (Lead creation and management)
  - Utility methods (Headers, query params, validation)

### SoundBoxConstants.java
- **Purpose**: All constants, configuration values, and static data
- **Categories**:
  - API configuration (versions, content types, headers)
  - Device information (identifiers, manufacturer details)
  - Location data (coordinates, IP addresses)
  - Checksum values for different operations
  - Test data (mobile numbers, customer IDs, merchant info)
  - Error messages and validation patterns
  - Status codes and retry configuration

## 🧪 Test Categories

### 1. Authentication Tests (Priority 1-22)
- **Send OTP Tests**: Mobile validation, OTP generation, retry scenarios
- **OTP Validation Tests**: Valid/invalid OTP, expired OTP, state validation

### 2. Content Tests (Priority 23-32)
- **YouTube Link Tests**: Link fetching, authentication, error scenarios

### 3. Device Tests (Priority 33-46, 56-63)
- **Device Validation**: Device registration, validation, security checks
- **IoT Device Tests**: Device retrieval, authentication, edge cases

### 4. Merchant Tests (Priority 47-55)
- **Merchant ID Tests**: ID retrieval, validation, error handling

### 5. Lead Tests (Priority 64-69+)
- **Lead Creation**: Lead generation, validation, complex scenarios
- **Lead Management**: Lead updates, status changes, lifecycle

### 6. Payment Tests (Priority 70+)
- **Payment Processing**: Order creation, payment validation, UPI flows

## 🚀 Usage

### Running Tests

#### Run All Tests
```bash
mvn test -DsuiteXmlFile=src/test/java/OCL/Individual/SoundBox/soundbox-test-suite.xml
```

#### Run Specific Test Category
```bash
# Authentication tests only
mvn test -Dgroups=Authentication

# Regression tests only
mvn test -Dgroups=Regression

# Smoke tests only
mvn test -Dgroups=Smoke
```

#### Run Individual Test Class
```bash
mvn test -Dtest=SendOtpTests
mvn test -Dtest=DeviceValidationTests
```

### Test Dependencies
Tests are organized with proper dependencies:
- Authentication tests run first (establish session)
- Device tests depend on authentication
- Merchant tests depend on device validation
- Lead tests depend on merchant operations
- Payment tests depend on lead creation

## 📊 Benefits of New Structure

### ✅ Maintainability
- **Focused Classes**: Each class handles 10-15 related tests
- **Clear Separation**: Logical grouping by functionality
- **Easy Navigation**: Find specific tests quickly

### ✅ Collaboration
- **Parallel Development**: Multiple developers can work simultaneously
- **Reduced Conflicts**: Changes isolated to specific areas
- **Code Reviews**: Smaller, focused pull requests

### ✅ Performance
- **Faster Compilation**: Smaller compilation units
- **Better IDE Performance**: Reduced memory usage
- **Selective Execution**: Run only needed test categories

### ✅ Extensibility
- **Easy Addition**: Add new test categories without affecting existing ones
- **Modular Growth**: Scale individual components independently
- **Flexible Configuration**: TestNG groups and dependencies

## 🔄 Migration Status

### ✅ Completed (Phase 1)
- [x] Base infrastructure created
- [x] Constants extracted and organized
- [x] Helper methods centralized
- [x] TestNG suite configuration

### ✅ Completed (Phase 2)
- [x] Authentication tests extraction
  - [x] SendOtpTests.java (10 tests, Priority 1-10)
  - [x] OtpValidationTests.java (11 tests, Priority 11-22)
- [x] TestNG suite updated with authentication tests
- [x] Smoke test configuration added

### ✅ Completed (Phase 3)
- [x] Content tests extraction
  - [x] YoutubeLinkTests.java (10 tests, Priority 23-32)
- [x] TestNG suite updated with content tests
- [x] Smoke test configuration updated
- [x] YouTube helper methods added to SoundBoxTestUtils

### 🔄 In Progress (Phase 4)
- [ ] Device tests extraction
- [ ] Merchant tests extraction
- [ ] Lead tests extraction

### ⏳ Planned (Phase 3)
- [ ] Payment tests extraction
- [ ] Integration testing
- [ ] Documentation updates
- [ ] CI/CD pipeline updates

## 📝 Development Guidelines

### Adding New Tests
1. Extend appropriate base class (`SoundBoxBaseTest`)
2. Use helper methods from `SoundBoxTestUtils`
3. Follow naming convention: `verify[Operation]With[Scenario]()`
4. Add appropriate TestNG groups and priorities
5. Include proper documentation and owner annotations

### Adding New Helper Methods
1. Add to `SoundBoxTestUtils` in appropriate category
2. Make methods static and well-documented
3. Use constants from `SoundBoxConstants`
4. Include proper error handling and assertions

### Modifying Constants
1. Update `SoundBoxConstants` class
2. Use descriptive names and proper categorization
3. Add documentation for complex values
4. Ensure backward compatibility

## 🎯 Best Practices

- **Consistent Naming**: Follow established patterns
- **Proper Grouping**: Use TestNG groups effectively
- **Error Handling**: Include retry logic where appropriate
- **Documentation**: Keep README and code comments updated
- **Testing**: Verify changes don't break existing functionality

## 📞 Support

For questions or issues with the test suite:
1. Check this README for common scenarios
2. Review existing test patterns for examples
3. Consult the base classes for shared functionality
4. Contact the test automation team for complex issues
