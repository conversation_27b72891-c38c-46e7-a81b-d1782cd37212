package OCL.Individual.SoundBox.lead;

import OCL.Individual.SoundBox.base.SoundBoxBaseTest;
import OCL.Individual.SoundBox.base.SoundBoxConstants;
import OCL.Individual.SoundBox.base.SoundBoxTestUtils;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * Test class for Lead Creation functionality in SoundBox
 * Contains all tests related to lead creation and management (Priority 64-69)
 */
public class LeadCreationTests extends SoundBoxBaseTest {

    @Test(priority = 64, dependsOnMethods = {"OCL.Individual.SoundBox.merchant.MerchantIdTests.verifyMidFetchWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_LEAD, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify SoundBox lead creation with valid data")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifySoundBoxLeadCreationWithValidData() {
        Response response = SoundBoxTestUtils.executeSoundBoxLeadCreation(middlewareServicesObject, 
            AGENT_CUST_ID, MERCHANT_NAME, UserMID, custId, MOBILE_NUMBER, AgentToken, 
            SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        // Extract lead ID if available
        try {
            String leadId = response.jsonPath().getString("leadId");
            if (leadId != null) {
                Lead_ID = leadId;
                LOGGER.info("Lead creation successful - Lead ID: {}", leadId);
            }
        } catch (Exception e) {
            LOGGER.info("Lead ID not available in response: {}", e.getMessage());
        }
        
        logTestCompletion("verifySoundBoxLeadCreationWithValidData", true);
    }

    @Test(priority = 65, dependsOnMethods = {"verifySoundBoxLeadCreationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_LEAD, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify complex SoundBox lead creation with complete headers")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifyComplexSoundBoxLeadCreation() {
        Response response = SoundBoxTestUtils.executeComplexSoundBoxLeadCreation(middlewareServicesObject, 
            AGENT_CUST_ID, SoundBoxConstants.ENTITY_TYPE_INDIVIDUAL, SoundBoxConstants.TEST_MERCHANT_NAME,
            UserMID, SoundBoxConstants.SOLUTION_SUB_TYPE_BIND, SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX, 
            custId, MOBILE_NUMBER, AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        // Extract and store lead ID
        try {
            String leadId = response.jsonPath().getJsonObject("leadId").toString();
            if (leadId != null) {
                Lead_ID = leadId;
                LOGGER.info("Complex lead creation successful - Lead ID: {}", leadId);
            }
        } catch (Exception e) {
            LOGGER.warn("Could not extract lead ID from complex lead creation: {}", e.getMessage());
        }
        
        logTestCompletion("verifyComplexSoundBoxLeadCreation", true);
    }

    @Test(priority = 66, dependsOnMethods = {"verifySoundBoxLeadCreationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_LEAD, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify SoundBox lead creation with wrong MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifySoundBoxLeadCreationWithWrongMid() {
        SoundBoxTestUtils.executeComplexSoundBoxLeadCreation(middlewareServicesObject, 
            "1000540528", SoundBoxConstants.ENTITY_TYPE_INDIVIDUAL, SoundBoxConstants.TEST_MERCHANT_NAME,
            "juJhhX90362275540618", SoundBoxConstants.SOLUTION_SUB_TYPE_BIND, SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX, 
            "1000698372", "7771110132", AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_BAD_REQUEST);
        
        logTestCompletion("verifySoundBoxLeadCreationWithWrongMid", true);
    }

    @Test(priority = 67, dependsOnMethods = {"verifySoundBoxLeadCreationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_LEAD, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify SoundBox lead creation when lead already exists")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifySoundBoxLeadCreationWhenLeadExists() {
        Response response = SoundBoxTestUtils.executeComplexSoundBoxLeadCreation(middlewareServicesObject, 
            "1000540528", SoundBoxConstants.ENTITY_TYPE_INDIVIDUAL, SoundBoxConstants.TEST_MERCHANT_NAME,
            "juJhhX90362275560814", SoundBoxConstants.SOLUTION_SUB_TYPE_BIND, SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX, 
            "1000698372", "7771110132", AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        LOGGER.info("Lead creation response when lead already exists - Status: {}", response.statusCode());
        
        logTestCompletion("verifySoundBoxLeadCreationWhenLeadExists", true);
    }

    @Test(priority = 68, dependsOnMethods = {"verifySoundBoxLeadCreationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_LEAD, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify SoundBox lead creation with no device bind at MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifySoundBoxLeadCreationWithNoDeviceBind() {
        SoundBoxTestUtils.executeComplexSoundBoxLeadCreation(middlewareServicesObject, 
            "1000540528", SoundBoxConstants.ENTITY_TYPE_INDIVIDUAL, SoundBoxConstants.TEST_MERCHANT_NAME,
            "juJhhX90362275560814", SoundBoxConstants.SOLUTION_SUB_TYPE_BIND, SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX, 
            "1000698372", "7771110132", AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        logTestCompletion("verifySoundBoxLeadCreationWithNoDeviceBind", true);
    }

    @Test(priority = 69, dependsOnMethods = {"verifySoundBoxLeadCreationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_LEAD, SoundBoxConstants.GROUP_SMOKE}, 
          description = "Verify SoundBox lead creation smoke test - quick validation")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifySoundBoxLeadCreationSmokeTest() {
        Response response = SoundBoxTestUtils.executeSoundBoxLeadCreation(middlewareServicesObject, 
            AGENT_CUST_ID, MERCHANT_NAME, UserMID, custId, MOBILE_NUMBER, AgentToken, 
            SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        // Basic validation for smoke test
        Assert.assertNotNull(response.getBody(), "Response body should not be null");
        Assert.assertTrue(response.getTime() < 5000, "Response time should be less than 5 seconds");
        
        LOGGER.info("SoundBox lead creation smoke test passed - Response time: {} ms", response.getTime());
        
        logTestCompletion("verifySoundBoxLeadCreationSmokeTest", true);
    }

    /**
     * Additional test for lead creation with missing agent token
     */
    @Test(priority = 70, dependsOnMethods = {"verifySoundBoxLeadCreationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_LEAD, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify SoundBox lead creation with missing agent token")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifySoundBoxLeadCreationWithMissingAgentToken() {
        SoundBoxTestUtils.executeComplexSoundBoxLeadCreationWithCustomToken(middlewareServicesObject, 
            AGENT_CUST_ID, SoundBoxConstants.ENTITY_TYPE_INDIVIDUAL, SoundBoxConstants.TEST_MERCHANT_NAME,
            UserMID, SoundBoxConstants.SOLUTION_SUB_TYPE_BIND, SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX, 
            custId, MOBILE_NUMBER, "", SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_UNAUTHORIZED);
        
        logTestCompletion("verifySoundBoxLeadCreationWithMissingAgentToken", true);
    }

    /**
     * Additional test for lead creation with wrong agent token
     */
    @Test(priority = 71, dependsOnMethods = {"verifySoundBoxLeadCreationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_LEAD, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify SoundBox lead creation with wrong agent token")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifySoundBoxLeadCreationWithWrongAgentToken() {
        String wrongToken = "kjsdfnvouerbnqwcns;udkji";
        
        SoundBoxTestUtils.executeComplexSoundBoxLeadCreationWithCustomToken(middlewareServicesObject, 
            AGENT_CUST_ID, SoundBoxConstants.ENTITY_TYPE_INDIVIDUAL, SoundBoxConstants.TEST_MERCHANT_NAME,
            UserMID, SoundBoxConstants.SOLUTION_SUB_TYPE_BIND, SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX, 
            custId, MOBILE_NUMBER, wrongToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_GONE);
        
        logTestCompletion("verifySoundBoxLeadCreationWithWrongAgentToken", true);
    }

    /**
     * Additional test for lead creation with missing device identifier
     */
    @Test(priority = 72, dependsOnMethods = {"verifySoundBoxLeadCreationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_LEAD, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify SoundBox lead creation with missing device identifier")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifySoundBoxLeadCreationWithMissingDeviceIdentifier() {
        SoundBoxTestUtils.executeComplexSoundBoxLeadCreationWithCustomHeaders(middlewareServicesObject, 
            AGENT_CUST_ID, SoundBoxConstants.ENTITY_TYPE_INDIVIDUAL, SoundBoxConstants.TEST_MERCHANT_NAME,
            UserMID, SoundBoxConstants.SOLUTION_SUB_TYPE_BIND, SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX, 
            custId, MOBILE_NUMBER, AgentToken, SoundBoxConstants.VERSION, "", true, SoundBoxConstants.STATUS_GONE);
        
        logTestCompletion("verifySoundBoxLeadCreationWithMissingDeviceIdentifier", true);
    }

    /**
     * Additional test for lead creation with missing solution sub-type
     */
    @Test(priority = 73, dependsOnMethods = {"verifySoundBoxLeadCreationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_LEAD, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify SoundBox lead creation with missing solution sub-type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifySoundBoxLeadCreationWithMissingSolutionSubType() {
        SoundBoxTestUtils.executeComplexSoundBoxLeadCreation(middlewareServicesObject, 
            AGENT_CUST_ID, SoundBoxConstants.ENTITY_TYPE_INDIVIDUAL, SoundBoxConstants.TEST_MERCHANT_NAME,
            UserMID, "", SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX, 
            custId, MOBILE_NUMBER, AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_BAD_REQUEST);
        
        logTestCompletion("verifySoundBoxLeadCreationWithMissingSolutionSubType", true);
    }

    /**
     * Additional test for lead creation with missing agent customer ID
     */
    @Test(priority = 74, dependsOnMethods = {"verifySoundBoxLeadCreationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_LEAD, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify SoundBox lead creation with missing agent customer ID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifySoundBoxLeadCreationWithMissingAgentCustId() {
        SoundBoxTestUtils.executeComplexSoundBoxLeadCreation(middlewareServicesObject, 
            "", SoundBoxConstants.ENTITY_TYPE_INDIVIDUAL, SoundBoxConstants.TEST_MERCHANT_NAME,
            UserMID, SoundBoxConstants.SOLUTION_SUB_TYPE_BIND, SoundBoxConstants.SOLUTION_TYPE_SOUND_BOX, 
            custId, MOBILE_NUMBER, AgentToken, SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_BAD_REQUEST);
        
        logTestCompletion("verifySoundBoxLeadCreationWithMissingAgentCustId", true);
    }

    /**
     * Helper method to validate lead creation response structure
     * @param response API response
     */
    private void validateLeadCreationResponse(Response response) {
        String responseBody = response.getBody().asString();
        Assert.assertNotNull(responseBody, "Response body should not be null");
        
        // Check for lead ID in response
        try {
            String leadId = response.jsonPath().getString("leadId");
            if (leadId != null) {
                LOGGER.info("Lead creation successful - Lead ID: {}", leadId);
            }
        } catch (Exception e) {
            LOGGER.info("Lead ID not available in response");
        }
        
        LOGGER.info("Lead creation response validation completed");
    }

    /**
     * Helper method to test lead creation with retry logic
     * @param maxRetries Maximum number of retries
     */
    private void executeLeadCreationWithRetry(int maxRetries) {
        int attempts = 0;
        Response response = null;
        
        while (attempts < maxRetries) {
            try {
                response = SoundBoxTestUtils.executeSoundBoxLeadCreation(middlewareServicesObject, 
                    AGENT_CUST_ID, MERCHANT_NAME, UserMID, custId, MOBILE_NUMBER, AgentToken, 
                    SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
                break; // Success, exit loop
            } catch (AssertionError e) {
                attempts++;
                if (attempts >= maxRetries) {
                    throw e; // Re-throw if max retries reached
                }
                LOGGER.warn("Lead creation attempt {} failed, retrying...", attempts);
                // Re-login and retry
                AgentLoginSoundBox();
            }
        }
        
        Assert.assertNotNull(response, "Response should not be null after retries");
        validateLeadCreationResponse(response);
    }

    /**
     * Test lead creation with performance validation
     */
    @Test(priority = 75, dependsOnMethods = {"verifySoundBoxLeadCreationWithValidData"}, 
          groups = {SoundBoxConstants.GROUP_LEAD, SoundBoxConstants.GROUP_REGRESSION}, 
          description = "Verify SoundBox lead creation performance")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void verifySoundBoxLeadCreationPerformance() {
        long startTime = System.currentTimeMillis();
        
        Response response = SoundBoxTestUtils.executeSoundBoxLeadCreation(middlewareServicesObject, 
            AGENT_CUST_ID, MERCHANT_NAME, UserMID, custId, MOBILE_NUMBER, AgentToken, 
            SoundBoxConstants.VERSION, SoundBoxConstants.STATUS_OK);
        
        long endTime = System.currentTimeMillis();
        long responseTime = endTime - startTime;
        
        // Validate performance
        Assert.assertTrue(responseTime < 3000, 
            String.format("Response time should be less than 3 seconds, actual: %d ms", responseTime));
        
        LOGGER.info("SoundBox lead creation performance test passed - Response time: {} ms", responseTime);
        
        logTestCompletion("verifySoundBoxLeadCreationPerformance", true);
    }
}
