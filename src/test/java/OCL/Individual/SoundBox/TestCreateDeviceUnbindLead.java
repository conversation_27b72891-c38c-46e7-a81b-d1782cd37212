package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.CreateDeviceUnbindLead;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestCreateDeviceUnbindLead extends BaseMethod {

	public String sessionToken;

	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> body;
	CreateDeviceUnbindLead createLead;
	
	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	@Test(priority=0)
	public void verify_Status_Code_And_Status_Message_When_Lead_Is_Created_Successfully() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		createLead=new CreateDeviceUnbindLead();
		
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
        body.put("agentCustId","1107195733");
        body.put("entityType","INDIVIDUAL");
        body.put("merchantName","TestBeneficiary");
        body.put("mid","DHVRVL02894215991065");
        body.put("posIds","DEFAULT");
        body.put("solutionSubType","sound_box_unbind");
        body.put("deviceId","663005068885300");
        body.put("deviceType","sound_box_3_0_4g");
        body.put("userCustId","1001373711");
        body.put("userMobile","6665550217");
        body.put("type","4G");
        body.put("model","Made In India Soundbox 3.0 4G");
        
        response=services.unbindDevice(createLead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
        // device unbind lead can not be created without a device unbind beat at fsm due to recent code changes
	}
	
	@Test
	public void verify_Same_Lead_Is_Returned_If_Lead_For_Device_Is_Already_Created() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		createLead=new CreateDeviceUnbindLead();
		
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
        body.put("agentCustId","1107195733");
        body.put("entityType","INDIVIDUAL");
        body.put("merchantName","TestBeneficiary");
        body.put("mid","DHVRVL02894215991065");
        body.put("posIds","DEFAULT");
        body.put("solutionSubType","sound_box_unbind");
        body.put("deviceId","663005068885300");
        body.put("deviceType","sound_box_3_0_4g");
        body.put("userCustId","1001373711");
        body.put("userMobile","6665550217");
        body.put("type","4G");
        body.put("model","Made In India Soundbox 3.0 4G");
        
        response=services.unbindDevice(createLead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
        // device unbind lead can not be created without a device unbind beat at fsm due to recent code changes
	}
	
	
	
	@Test
	public void verify_Different_Lead_Is_Returned_If_Device_Types_Are_Different() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		createLead=new CreateDeviceUnbindLead();
		
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
        body.put("agentCustId","1107195733");
        body.put("entityType","INDIVIDUAL");
        body.put("merchantName","TestBeneficiary");
        body.put("mid","DHVRVL02894215991065");
        body.put("posIds","DEFAULT");
        body.put("solutionSubType","sound_box_unbind");
        body.put("deviceId","663005068885300");
        body.put("deviceType","sound_box_3_0_4g");
        body.put("userCustId","1001373711");
        body.put("userMobile","6665550217");
        body.put("type","4G");
        body.put("model","Made In India Soundbox 3.0 4G");
        
     

        Assert.assertEquals(response.getStatusCode(), 400);
        // device unbind lead can not be created without a device unbind beat at fsm due to recent code changes	}
	}
	@Test
	public void verify_Different_Lead_Is_Returned_If_Device_IDs_Are_Different_For_Same_Device_Type() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		createLead=new CreateDeviceUnbindLead();
		
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
        body.put("agentCustId","1107195733");
        body.put("entityType","INDIVIDUAL");
        body.put("merchantName","TestBeneficiary");
        body.put("mid","DHVRVL02894215991065");
        body.put("posIds","POSID3");
        body.put("solutionSubType","sound_box_unbind");
        body.put("deviceId","8392781329785");
        body.put("deviceType","sound_box");
        body.put("userCustId","1001373711");
        body.put("userMobile","6665550217");
        body.put("type","2G");
        body.put("model","PSB1 with Battery");
        
        response=services.unbindDevice(createLead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
        // device unbind lead can not be created without a device unbind beat at fsm due to recent code changes    
	}
	
	@Test
	public void verify_Error_Code_When_Device_ID_Does_Not_Exists() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		createLead=new CreateDeviceUnbindLead();
		
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
        body.put("agentCustId","1107195733");
        body.put("entityType","INDIVIDUAL");
        body.put("merchantName","TestBeneficiary");
        body.put("mid","DHVRVL02894215991065");
        body.put("posIds","DEFAULT");
        body.put("solutionSubType","sound_box_unbind");
        body.put("deviceType","sound_box_3_0_4g");
        body.put("userCustId","1001373711");
        body.put("userMobile","6665550217");
        body.put("type","4G");
        body.put("model","Made In India Soundbox 3.0 4G");
        
        response=services.unbindDevice(createLead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
  
	}
	
	@Test
	public void verify_Status_Code_And_Status_Message_When_Device_Type_Is_InCorrect() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		createLead=new CreateDeviceUnbindLead();
		
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
        body.put("agentCustId","1107195733");
        body.put("entityType","INDIVIDUAL");
        body.put("merchantName","TestBeneficiary");
        body.put("mid","DHVRVL02894215991065");
        body.put("posIds","DEFAULT");
        body.put("solutionSubType","sound_box_unbind");
        body.put("deviceId","663005068885300");
        body.put("deviceType","sound_box");
        body.put("userCustId","1001373711");
        body.put("userMobile","6665550217");
        body.put("type","4G");
        body.put("model","Made In India Soundbox 3.0 4G");
        
        response=services.unbindDevice(createLead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
     
	}
	
	@Test
	public void verify_Status_Code_And_Status_Message_When_Agent_Id_Is_Missing_From_Request() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		createLead=new CreateDeviceUnbindLead();
		
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
       
        body.put("entityType","INDIVIDUAL");
        body.put("merchantName","TestBeneficiary");
        body.put("mid","DHVRVL02894215991065");
        body.put("posIds","DEFAULT");
        body.put("solutionSubType","sound_box_unbind");
        body.put("deviceId","663005068885300");
        body.put("deviceType","sound_box_3_0_4g");
        body.put("userCustId","1001373711");
        body.put("userMobile","6665550217");
        body.put("type","4G");
        body.put("model","Made In India Soundbox 3.0 4G");
        
        response=services.unbindDevice(createLead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
        Assert.assertTrue(response.path("displayMessage").toString().contains("Agent CustId cannot be blank"));
	}
	
	
	
	@Test
	public void verify_Status_Code_When_MID_IS_Not_Binded_To_A_Device() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		createLead=new CreateDeviceUnbindLead();
		
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
        body.put("agentCustId","1107195733");
        body.put("entityType","INDIVIDUAL");
        body.put("merchantName","TestBeneficiary");
        body.put("mid","qCwCjJ51596523491178");
        body.put("posIds","DEFAULT");
        body.put("solutionSubType","sound_box_unbind");
        body.put("deviceId","663005068885300");
        body.put("deviceType","sound_box_3_0_4g");
        body.put("userCustId","1001796594");
        body.put("userMobile","7771110998");
        body.put("type","4G");
        body.put("model","Made In India Soundbox 3.0 4G");
        
        response=services.unbindDevice(createLead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test
	public void verify_Status_Code_When_Merchant_ID_Is_Of_Online_Category() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		createLead=new CreateDeviceUnbindLead();
		
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
        body.put("agentCustId","1107195733");
        body.put("entityType","INDIVIDUAL");
        body.put("merchantName","TestBeneficiary");
        body.put("mid","LGCGVs74890974950930");
        body.put("posIds","DEFAULT");
        body.put("solutionSubType","sound_box_unbind");
        body.put("deviceId","663005068885300");
        body.put("deviceType","sound_box_3_0_4g");
        body.put("userCustId","1001373711");
        body.put("userMobile","5435312808");
        body.put("type","4G");
        body.put("model","Made In India Soundbox 3.0 4G");
        
        response=services.unbindDevice(createLead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test
	public void verify_Error_Code_When_Merchant_ID_Is_Inactive() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		createLead=new CreateDeviceUnbindLead();
		
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
        body.put("agentCustId","1107195733");
        body.put("entityType","INDIVIDUAL");
        body.put("merchantName","TestBeneficiary");
        body.put("mid","JFTbRB71149895485999");
        body.put("posIds","DEFAULT");
        body.put("solutionSubType","sound_box_unbind");
        body.put("deviceId","663005068885300");
        body.put("deviceType","sound_box_3_0_4g");
        body.put("userCustId","1001373711");
        body.put("userMobile","8018495590");
        body.put("type","4G");
        body.put("model","Made In India Soundbox 3.0 4G");
        
        response=services.unbindDevice(createLead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test
	public void verify_Status_Code_And_When_Secondary_ID_Is_Incorrect() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		createLead=new CreateDeviceUnbindLead();
		
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
        body.put("agentCustId","1107195733");
        body.put("entityType","INDIVIDUAL");
        body.put("merchantName","TestBeneficiary");
        body.put("mid","DHVRVL02894215991065");
        body.put("posIds","ABC");
        body.put("solutionSubType","sound_box_unbind");
        body.put("deviceId","663005068885300");
        body.put("deviceType","sound_box_3_0_4g");
        body.put("userCustId","1001373711");
        body.put("userMobile","6665550217");
        body.put("type","4G");
        body.put("model","Made In India Soundbox 3.0 4G");
        
        response=services.unbindDevice(createLead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test
	public void verify_Status_Code__When_Incorrect_Solution_Sub_Type_Is_Passed_In_The_Request() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		createLead=new CreateDeviceUnbindLead();
		
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
        body.put("agentCustId","1107195733");
        body.put("entityType","INDIVIDUAL");
        body.put("merchantName","TestBeneficiary");
        body.put("mid","DHVRVL02894215991065");
        body.put("posIds","DEFAULT");
         body.put("deviceId","663005068885300");
        body.put("deviceType","sound_box_3_0_4g");
        body.put("userCustId","1001373711");
        body.put("userMobile","6665550217");
        body.put("type","4G");
        body.put("model","Made In India Soundbox 3.0 4G");
        
        response=services.unbindDevice(createLead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test
	public void verify_Status_Code_When_Device_Model_Is_Not_Passed_In_The_Request() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		createLead=new CreateDeviceUnbindLead();
		
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
        body.put("agentCustId","1107195733");
        body.put("entityType","INDIVIDUAL");
        body.put("merchantName","TestBeneficiary");
        body.put("mid","DHVRVL02894215991065");
        body.put("posIds","DEFAULT");
        body.put("solutionSubType","sound_box_unbind");
        body.put("deviceId","663005068885300");
        body.put("deviceType","sound_box_3_0_4g");
        body.put("userCustId","1001373711");

        body.put("type","4G");

        
        response=services.unbindDevice(createLead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
    }
	
	@Test
	public void verify_Status_Code_When_Errpr_Is_Recieved_From_IOT() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		createLead=new CreateDeviceUnbindLead();
		
		
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
        body.put("agentCustId","1107195733");
        body.put("entityType","INDIVIDUAL");
        body.put("merchantName","TestBeneficiary");
        body.put("mid","BenBzH39879077561920");
        body.put("posIds","DEFAULT");
        body.put("solutionSubType","sound_box_unbind");
        body.put("deviceId","663005068885300");
        body.put("deviceType","sound_box_3_0_4g");
        body.put("userMobile","9994553537");
        body.put("type","4G");
        body.put("model","Made In India Soundbox 3.0 4G");
        
        response=services.unbindDevice(createLead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 400);
	}
	
	@Test
	public void verify_Status_Code_When_Session_Token_Is_Missing_Or_Invalid() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		createLead=new CreateDeviceUnbindLead();
		
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
	
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
        body.put("agentCustId","1107195733");
        body.put("entityType","INDIVIDUAL");
        body.put("merchantName","TestBeneficiary");
        body.put("mid","DHVRVL02894215991065");
        body.put("posIds","DEFAULT");
        body.put("solutionSubType","sound_box_unbind");
        body.put("deviceId","663005068885300");
        body.put("deviceType","sound_box_3_0_4g");
        body.put("userCustId","1001373711");
        body.put("userMobile","6665550217");
        body.put("type","4G");
        body.put("model","Made In India Soundbox 3.0 4G");
        
        response=services.unbindDevice(createLead, body, headers);
        Assert.assertEquals(response.getStatusCode(), 401);
	}
	
	
	@Test
	public void verify__Status_Message_When_App_Version_Is_Missing_From_Request() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		createLead=new CreateDeviceUnbindLead();
		
		headers.put("deviceIdentifier", "motorola-motoe40-5e237e4fcde1d40d");
		headers.put("session_token", sessionToken);

        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        
        body.put("agentCustId","1107195733");
        body.put("entityType","INDIVIDUAL");
        body.put("merchantName","TestBeneficiary");
        body.put("mid","DHVRVL02894215991065");
        body.put("posIds","DEFAULT");
        body.put("solutionSubType","sound_box_unbind");
        body.put("deviceId","663005068885300");
        body.put("deviceType","sound_box_3_0_4g");
        body.put("userCustId","1001373711");
        body.put("userMobile","6665550217");
        body.put("type","4G");
        body.put("model","Made In India Soundbox 3.0 4G");
        
        response=services.unbindDevice(createLead, body, headers);
        Assert.assertEquals(response.path("message"), "version is empty in header");
	}
}