package OCL.Individual.SoundBox;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Optional;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.SimActivation;
import Request.SoundBox.SimReplacementValidateOTP;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class SimActivationTest extends BaseMethod {
	
	public String sessionToken;
	
	public Response response;
	public MiddlewareServices services;
	
	public Map<String,String> headers;
	public Map<String, String> body;
	public SimActivation simObj;

	public Map<String, String> param;
	SimReplacementValidateOTP sim;
	String leadID="";
	

	@BeforeClass
	public void createLead(@Optional("0") int value) {	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		headers=new HashMap<>();
		body=new HashMap<>();
		param=new HashMap<String, String>();
		services=new MiddlewareServices();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        
        param.put("entityType", "INDIVIDUAL");
        param.put("solutionType", "manage_sim");

        body.put("otp", "");
        body.put("state", "");
        body.put("userType", "merchant");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "false"); // Boolean as String
        body.put("individualSolutionType", "");
        body.put("mid", "HySHnd27878673398759");
        body.put("onlyValidateOtp", "false"); // Boolean as String
        body.put("custId", "1001788031");
        body.put("skipOtp", "true"); // Boolean as String
        body.put("tncVersion", "");
        body.put("tncSetName", "");

        sim=new SimReplacementValidateOTP();
        response=services.createSimReplace(sim, body, headers,param);
        int statusCode=response.getStatusCode();
        if(statusCode==400 && value<3) {
        	value++;
        this.createLead(value);}
        

        leadID=response.jsonPath().getJsonObject("leadId");
	}

	@BeforeMethod
	public void setToken() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");
		try {
			establishConnectiontoServer(sessionToken, 5);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	@Test
	public void verify_Error_Message_When_Sim_No_Length_Is_Less_Than_Required_() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", leadID);
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "123");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
     
        
	}

	@Test
	public void verify_Error_Message_When_Sim_No_Length_Is_More_Than_Required_() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", leadID);
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "1234567890123456789012345678901234567890");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
   
        
	}

	@Test
	public void verify_Status_Code_When_Sim_Is_Available_In_Fse_Bucket_With_Ats_Validation_Enabled() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", leadID);
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205751575931U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
 

	}
	
	@Test
	public void verify_Display_Message_When_Sim_Is_Available_In_Fse_Bucket_With_Ats_Validation_Enabled() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "7.1.9");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", leadID);
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205751568746");
        body.put("agentCustId", "1107195733");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
        
	}
	

	@Test
	public void verify_Show_Sim_Mismatch_Pending_In_Case_Of_Same_Sim() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "7.1.9");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", leadID);
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205751568746U");
        body.put("agentCustId", "1107195733");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
  
                
	}
	
	@Test
	 	public void verify_Show_Sim_Action_Pending_In_Case_Of_Same_Sim() {
 
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", leadID);
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205751568746U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
   
                
	}
	
	
	@Test
	public void verify_Sim_Reuse_Is_False_in_Case_of_New_Sim_Scanned_And_Updated() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", leadID);
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205751575964U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
  
        
	}

	@Test
	public void verify_Error_Message_With_Ats_validation_When_Sim_Is_Alread_Deployed() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", "95c8cf61-46a3-4a82-8c10-87a6b9df63c7");
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205751576053U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
   
        
	}
	@Test
	public void verify_Error_Message_With_Ats_validation_When_Sim_Is_Not_Present_On_Ats() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", "95c8cf61-46a3-4a82-8c10-87a6b9df63c7");
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205733515760538U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
  
        
	}

	@Test
	public void verify_Error_Message_With_Ats_validation_When_Sim_Is_In_Pending_Ack_On_Ats() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", "95c8cf61-46a3-4a82-8c10-87a6b9df63c7");
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205751576087U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
    
        
	}
	@Test
	public void verify_Error_Message_With_Ats_validation_When_Sim_Is_In_Onboarded_Stage_On_Ats() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", "95c8cf61-46a3-4a82-8c10-87a6b9df63c7");
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "89911022057515758999U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
 
        
	}
	
	@Test
	public void verify_Error_Message_With_Ats_validation_When_Sim_Is_In_Unmapped_Stage_On_Ats() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", "95c8cf61-46a3-4a82-8c10-87a6b9df63c7");
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "89911022057515758888U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
  
        
	}
	@Test
	public void verify_Error_Message_With_Ats_validation_When_Sim_Is_In_Available_With_Different_FSE_On_Ats() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", "95c8cf61-46a3-4a82-8c10-87a6b9df63c7");
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205751575634U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
   
        
	}
	@Test
	public void verify_Error_Message_In_Case_Of_Sim_MisMatch() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", leadID);
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205751575931U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
     
        
	}
	
	@Test
	public void verify_Show_Sim_Action_Pending_In_Case_Of_Sim_MisMatch() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", leadID);
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205751575931U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);

        
	}
	
	@Test
	public void verify_Show_Sim_Mismatch_Pending_In_Case_Of_Sim_MisMatch() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", leadID);
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205751575931U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
 
        
	}
	
	@Test
	public void verify_Status_Code_In_Case_Of_Failure_From_Ats() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", leadID);
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205751575111111111111634U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
        Assert.assertEquals(response.getStatusCode(),400);
        
	}

	@Test
	public void verify_Sim_Not_Present_On_ATS_Can_Be_Deployed_Without_ATS_Validation_Enabled() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");

		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", "82246634-c188-4ba7-9173-06340e4601a5");
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205733515760538U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
 

	}

	@Test
	public void verify_Erro_Code_When_Session_Token_Is_Missing() {
	
		sessionToken=AgentSessionToken("7771216290", "paytm@123");

		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		

		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", "95c8cf61-46a3-4a82-8c10-87a6b9df63c7");
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205733515760538U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
        Assert.assertEquals(response.getStatusCode(),401);

	}

	@Test
	public void verify_Error_Code_When_Lead_Id_Is_Missing_From_Request() {
	
		sessionToken=AgentSessionToken("8010630022", "paytm@123");

		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        

        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205733515760538U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
        Assert.assertEquals(response.getStatusCode(),400);

	}

	@Test
	public void verify_Error_Code_When_Action_Is_Missing_From_Request() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", "82246634-c188-4ba7-9173-06340e4601a5");
        body.put("deviceId", "");
        body.put("deviceType", "SOUNDBOX_5");
        body.put("modelName", "");
      
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205751575931U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
        Assert.assertEquals(response.getStatusCode(),500);
     
	}

	@Test
	public void verify_Error_Code_When_Device_Information_Is_Missing_From_Request() {
	
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		
		headers.put("session_token", sessionToken);
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("version", "5.1.0");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");


        
        body.put("leadId", "82246634-c188-4ba7-9173-06340e4601a5");
        body.put("action", "ACTIVATION");
        body.put("imsiNo", "");
        body.put("replacementDeviceType", "sound_box_3_0_4g");
        body.put("simString", "8991102205751568746U");
        body.put("agentCustId", "1107195732");
        
        simObj=new SimActivation();
        response=services.validateAndUpdateSimDetails(simObj, body, headers);
        Assert.assertEquals(response.getStatusCode(),500);
        
	}



}