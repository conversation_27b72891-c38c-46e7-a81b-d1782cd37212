package OCL.Individual.SoundBox;

import Services.MechantService.MiddlewareServices;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.google.gson.Gson;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class SBPINCODE extends BaseMethod{
    private static final Logger LOGGER = LogManager.getLogger(SBPINCODE.class);
    public static String pin1 = "425001";
    public static String pinnew = "110085";
    public static String tier3Type = "CITY";
    public static String addressResponseType= "SECONDARY";
    public static String secondaryType= "TIER3";
    public static String BlankPINErrorMessage="Pincode list cannot be empty";
    public static String InvalidPINErrorMessage="Invalid pincode";

    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    @Test(priority = 1,description = "Fetching Single PIN with Primary details")

    public void AddingSinglePINs() {
        Map<String, String> headers = new HashMap<String, String>();
        // SBPINCODE SBPINCODE =new SBPINCODE(P.TESTDATA.get("v1SBAddressInfo"));
        // headers.put("session_token", session_token);
        // headers.put("accept", "*/*");
        // headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        List<String> lt=new ArrayList<String>();
        lt.add(pin1);
        String ls=new Gson().toJson(lt);
        //        String ls=lt.toString();
        body.put("pincodes", ls);
        body.put("addressResponseType",addressResponseType);
        body.put("tier3Type",tier3Type);
        body.put("secondaryType",secondaryType);
        Response fetchPermissionResponse = MiddlewareServicesObject.v1SBPinCode(headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 2,description = "Fetching Multiple PIN with Primary details")
    public void AddingMultiplePINs()
    {    Map<String, String> headers = new HashMap<String, String>();
        // Map<String, String> headers = new HashMap<String, String>();
        //headers.put("session_token", session_token);
        // headers.put("accept", "*/*");
        // headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        List<String> lt=new ArrayList<String>();
        lt.add(pin1); lt.add(pinnew);
        String ls=lt.toString();
        body.put("pincodes", ls);
        body.put("addressResponseType",addressResponseType);
        body.put("tier3Type",tier3Type);
        body.put("secondaryType",secondaryType);
        Response fetchPermissionResponse = MiddlewareServicesObject.v1SBPinCode(headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }

    @Test(priority = 3,description = "Blank pincode")
    public void BlankPin()
    {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        List<String> lt=new ArrayList<String>();

        lt.add("");
        lt.clear();
        String ls=new Gson().toJson(lt);
        body.put("pincodes", ls);
        body.put("addressResponseType",addressResponseType );
        body.put("tier3Type",tier3Type);
        body.put("secondaryType",secondaryType);

        Response fetchPermissionResponse = MiddlewareServicesObject.v1SBPinCode(headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        // Assert.assertEquals(fetchPermissionResponse.jsonPath().get("responseCode"),"400");

        // Assert.assertEquals(fetchPermissionResponse.jsonPath().get("responseMessage"),BlankPINErrorMessage);

    }
    @Test(priority = 4,description = "Invalid pincode")
    public void InvalidPin()
    {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        List<String> lt=new ArrayList<String>();

        lt.add("");
        String ls=new Gson().toJson(lt);
        // body.put("pincodes", ls);
        body.put("addressResponseType",addressResponseType );
        body.put("tier3Type",tier3Type);
        body.put("secondaryType",secondaryType);


        Response fetchPermissionResponse = MiddlewareServicesObject.v1SBPinCode(headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");


        //Assert.assertEquals(fetchPermissionResponse.jsonPath().get("responseCode"),"400");

        //Assert.assertEquals(fetchPermissionResponse.jsonPath().get("responseMessage"),InvalidPINErrorMessage);

    }
    @Test(priority = 5,description = "Fetching Single PIN with Primary details")

    public void AddingtoSinglePINs() {
        Map<String, String> headers = new HashMap<String, String>();

        // headers.put("session_token", session_token);
        // headers.put("accept", "*/*");
        // headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        List<String> lt=new ArrayList<String>();
        lt.add(pin1);
        String ls=new Gson().toJson(lt);
        //        String ls=lt.toString();
        body.put("pincodes", ls);
        body.put("addressResponseType",addressResponseType);
        body.put("tier3Type",tier3Type);
        body.put("secondaryType",secondaryType);
        Response fetchPermissionResponse = MiddlewareServicesObject.v1SBPinCode(headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
//        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
}


