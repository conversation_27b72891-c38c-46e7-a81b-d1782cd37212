package OCL.Individual.SoundBox;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import org.testng.Assert;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.SoundBox.DIYSBQRUpdateAWBNumber;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestDIYSBQRUpdateAWBNumber extends BaseMethod {

	private Response response;
	private MiddlewareServices services;
	
	private Map<String,String> headers;
	private Map<String, String> body;
	private  DIYSBQRUpdateAWBNumber number;
	private String jwt="";
	private long baseAwbNo=2097897000000L;
	
	private String leadId="060b7d24-b9a7-4f5d-998f-419586fe4fa8";
	private String invalidLead="7b6a1f0e-406f-4d5f-b1fa-fa492c38957d";
	private String cancelledLead="b7c6a6cf-d1a4-4db2-ac63-d683c4395049";

	
	private String getRandomAwb() {
		long num= new Random().nextInt(20000)+baseAwbNo;
		return String.format("%s", num);
	}
	
	// add this case only to suite to  check e2e lead closure for happy case journey
	
	@Test
	public void TC01_Check_Status_Code_And__Message_When_AWB_Number_Is_Successfully_Updated_For_Lead() throws Exception {
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		stmt.execute("use releaseOctW4");
		
		ResultSet res= stmt.executeQuery("select id from user_business_mapping ubm where lead_id ='"+leadId+"'");
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = "+id+" and ws.is_active =1");
		res.next();
		String nodeId=res.getString("workflow_node_id");
		res=stmt.executeQuery("Select wn.sub_stage from workflow_node wn where wn.id = "+nodeId);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		number =new DIYSBQRUpdateAWBNumber();
		jwt=createStsTokenForDIY();
		
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");
		
		body.put("leadId", leadId);
		body.put("action", "UPDATE_AWS_NUMBER");
		body.put("awbNumber", getRandomAwb());
	
		response=services.diyUpdateAwbNum(number, headers, body);
		Assert.assertEquals(response.statusCode(), 200);
	}
	
	@Test(dependsOnMethods = "TC01_Check_Status_Code_And__Message_When_AWB_Number_Is_Successfully_Updated_For_Lead")
	public void TC02_Check_Lead_Stage_Is_Moved_To_Pff_Update_Success_After_AWB_Update() throws Exception {
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		stmt.execute("use releaseOctW4");
		
		ResultSet res= stmt.executeQuery("select id from user_business_mapping ubm where lead_id ='"+leadId+"'");
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = "+id+" and ws.is_active =1");
		res.next();
		String nodeId=res.getString("workflow_node_id");
		res=stmt.executeQuery("Select wn.sub_stage from workflow_node wn where wn.id = "+nodeId);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		
		int count=0;
		
		while(count<4 && !nodeId.equalsIgnoreCase("1987")) {
		Thread.sleep(15000); //wait for rest of the jobs to run
		res=stmt.executeQuery("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = "+id+" and ws.is_active =1");
		res.next();
		nodeId=res.getString("workflow_node_id");
		count++;
		}
		res=stmt.executeQuery("Select wn.sub_stage from workflow_node wn where wn.id = "+nodeId);
		res.next();
		subStage=res.getString("sub_stage");
		Assert.assertEquals(subStage, "PFF_MANIFEST_MOVEMENT_SUCCESS");
		connect.close();
	}
		
		
		
	
	
	
	@Test
	public void TC03_Check_Status_Code_And__Message_When_Lead_Is_Not_Required_Substage_For_AWB_Update() throws Exception {
		
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		stmt.execute("use releaseOctW4");
		
		ResultSet res= stmt.executeQuery("select id from user_business_mapping ubm where lead_id ='"+invalidLead+"'");
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = "+id+" and ws.is_active =1");
		res.next();
		String nodeId=res.getString("workflow_node_id");
		res=stmt.executeQuery("Select wn.sub_stage from workflow_node wn where wn.id = "+nodeId);
		res.next();
		String subStage=res.getString("sub_stage");
		System.out.println(subStage);
		connect.close();
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		number =new DIYSBQRUpdateAWBNumber();
		jwt=createStsTokenForDIY();
		
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");
		
		body.put("leadId", invalidLead);
		body.put("action", "UPDATE_AWS_NUMBER");
		body.put("awbNumber", getRandomAwb());
	
		response=services.diyUpdateAwbNum(number, headers, body);
		Assert.assertEquals(response.statusCode(), 400);
		Assert.assertNotEquals(subStage, "STS_UPDATE_INVOICE_DETAILS_SUCCESS");
		Assert.assertEquals(response.path("displayMessage").toString().contains("Invalid Lead Stage"),true);
	}
	

	
	@Test
	public void TC04_Check_Status_Code_And__Message_When_Lead_ID_Is_Not_Passed() throws Exception {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		number =new DIYSBQRUpdateAWBNumber();
		jwt=createStsTokenForDIY();
		
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");
		
		body.put("leadId", "");
		body.put("action", "UPDATE_AWS_NUMBER");
		body.put("awbNumber", getRandomAwb());
	
		response=services.diyUpdateAwbNum(number, headers, body);
		Assert.assertEquals(response.statusCode(), 400);
	}
	
	@Test
	public void TC05_Check_Status_Code_And__Message_When_Action_Is_Not_Passed() throws Exception {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		number =new DIYSBQRUpdateAWBNumber();
		jwt=createStsTokenForDIY();
		
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");
		
		body.put("leadId", leadId);
		body.put("action", "");
		body.put("awbNumber", getRandomAwb());
	
		response=services.diyUpdateAwbNum(number, headers, body);
		Assert.assertEquals(response.statusCode(), 400);
	}
	
	@Test
	public void TC06_Check_Status_Code_And__Message_When_AWB_Is_Not_Passed() throws Exception {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		number =new DIYSBQRUpdateAWBNumber();
		jwt=createStsTokenForDIY();
		
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");
		
		body.put("leadId", leadId);
		body.put("action", "UPDATE_AWS_NUMBER");
		body.put("awbNumber", "");
	
		response=services.diyUpdateAwbNum(number, headers, body);
		Assert.assertEquals(response.statusCode(), 400);
	}
	
	@Test
	public void TC07_Check_Status_Code_And__Message_When_Order_ID_Is_Already_Cancelled() throws Exception {
		Connection connect = DBConnection.establishConnection();
		Statement stmt=connect.createStatement();
		stmt.execute("use releaseOctW4");
		
		ResultSet res= stmt.executeQuery("select id from user_business_mapping ubm where lead_id ='"+cancelledLead+"'");
		res.next();
		String id=res.getString("id");
		res=stmt.executeQuery("Select ws.workflow_node_id from workflow_status ws where ws.user_business_mapping_id = "+id+" and ws.is_active =1");
		res.next();
		String nodeId=res.getString("workflow_node_id");
		res=stmt.executeQuery("Select wn.sub_stage from workflow_node wn where wn.id = "+nodeId);
		connect.close();
		
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		number =new DIYSBQRUpdateAWBNumber();
		jwt=createStsTokenForDIY();
		
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "123");
		
		body.put("leadId", cancelledLead);
		body.put("action", "UPDATE_AWS_NUMBER");
		body.put("awbNumber", getRandomAwb());
	
		Assert.assertEquals(nodeId, "155");
		response=services.diyUpdateAwbNum(number, headers, body);
		Assert.assertEquals(response.statusCode(), 400);
	}
	
	@Test
	public void TC08_Check_Status_Code_And__Message_When_JWT_Is_Not_Passed() throws Exception {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		number =new DIYSBQRUpdateAWBNumber();
		jwt=createStsTokenForDIY();
		
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", "");
		headers.put("custId", "123");
		
		body.put("leadId", leadId);
		body.put("action", "UPDATE_AWS_NUMBER");
		body.put("awbNumber", getRandomAwb());
	
		response=services.diyUpdateAwbNum(number, headers, body);
		Assert.assertEquals(response.statusCode(), 401);
	}
	
	@Test
	public void TC04_Check_Status_Code_And__Message_When_CustID_Is_Not_Passed() throws Exception {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		number =new DIYSBQRUpdateAWBNumber();
		jwt=createStsTokenForDIY();
		
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", jwt);
		headers.put("custId", "");
		
		body.put("leadId", leadId);
		body.put("action", "UPDATE_AWS_NUMBER");
		body.put("awbNumber", getRandomAwb());
	
		response=services.diyUpdateAwbNum(number, headers, body);
		Assert.assertEquals(response.statusCode(), 401);
	}
	

	
}
