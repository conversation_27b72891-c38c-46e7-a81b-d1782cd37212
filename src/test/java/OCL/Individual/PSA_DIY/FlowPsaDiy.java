package OCL.Individual.PSA_DIY;

import Request.MerchantService.oe.V1.Payment.Order.NotifyCallback;
import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.GamePind.FetchQuestion;
import Request.MerchantService.v1.GamePind.StartTest;
import Request.MerchantService.v1.GamePind.SubmitQuestion;
import Request.MerchantService.v1.Payments.OrderFullfillment;
import Request.MerchantService.v1.sdMerchant.ApplicationStatus;
import Request.MerchantService.v1.sdMerchant.Lead_create;
import Request.MerchantService.v1.sdMerchant.Lead_fetch;
import Request.MerchantService.v1.sdMerchant.bank;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FlowPsaDiy extends BaseMethod
{
    private static final Logger LOGGER = LogManager.getLogger(FlowPsaDiy.class);
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    Faker GenerateFake = new Faker();

    public String lineOne = GenerateFake.address().streetAddress();
    public String lineTwo = GenerateFake.address().cityName();
    public String lineThree = GenerateFake.address().streetName();
    public String lat = "28.605410";
    public String longi = "77.341996";
    public static String mobileNo = "";
    private static String ApplicantToken ="";
    private static String AppCustId = "";
    public static String leadId = "";
    File CanCelledChequePDF = new File("output.jpg");
    float score = 0 ;
    public static String contestStatus = null;
    public static int QCstatusCode = 0;
    public static String QcSubStgae ="QC_REJECTED";

    public static String WorkFlowId ;
    public static String SubStage = "";
    public static List<String> AddressProof = new ArrayList<>();
    public static String AddressProofDoc = "";
    public static String SelfieDoc ="";
    public static String EducationProof ="";
    public static String ApplicationStage ="";
    public static String OrderId = "";
    public static String MID = "";
    public static String Amount = "";
    public static String PgResponse = "";
    public static String paymentDone ;
    public static int GamePindstatusCode = 0;

    List <Object> GetDocuments = new ArrayList<>();
    public static List<Object> DMS = new ArrayList<>();
    public static List<Object> DMSforUUID = new ArrayList<>();
    public static List<Object> DocType = new ArrayList<>();
    public static List<Object> DocProvided = new ArrayList<>();
    public static List<Object> DocumentRequest = new ArrayList<>();
    public static List<Object> NameOfDoc = new ArrayList<>();
    public static List<Object> TypeOfDoc = new ArrayList<>();
    public static String OePanelDocStatus = "REJECTED";
    public static String RejectionReason = "Wrong Photo";
    public static String DocumetRequestDeserialised = "";





    @BeforeClass
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void GetSessionToken()
    {
        Utilities accObj = new Utilities();
        mobileNo = accObj.randomMobileNumberGenerator();
        LOGGER.info("New Number for PSA DIY  : " + mobileNo);
        CreateApplicantOauth(mobileNo);
        ApplicantToken = ApplicantToken(mobileNo,"paytm@123");
        LOGGER.info("Applicant Session Token for PSA DIY is : " + ApplicantToken);
        AppCustId = FetchUserDetails(mobileNo,"phone");

     /*  // TokenXMV tokenXMW=new TokenXMV();
        Response responseObject=middlewareServicesObject.v1Token("9560526665","paytm@123");
        XMWCookie=responseObject.getHeader("Set-Cookie").toString();
        LOGGER.info(" OE Panel Cookie is  : " + XMWCookie);*/

    }
    @Test(priority = 0,description = "Fetching Lead Status for PSA DIY",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_GetLeadStatusPsaDiy()
    {
        Lead_fetch fetchLead = new Lead_fetch();

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", "psa_diy");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionTypeLevel2","business_service");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("client","androidapp");
        headers.put("latitude","28.9342");
        headers.put("longitude","72.48543");
        headers.put("androidId","AashitAndroidId");

        Response responseObject = middlewareServicesObject.FetchLead(fetchLead, queryParams, headers);

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(200,statusCode);


    }

    @Test(priority = 0,dependsOnMethods = "TC001_GetLeadStatusPsaDiy",description = "Creating lead for PSA DIY",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_CreateLeadPsaDiy()
    {
        Lead_create createLead = new Lead_create(P.TESTDATA.get("EmptyBody"));

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", "psa_diy");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionTypeLevel2","business_service");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("client","androidapp");
        headers.put("latitude","28.9342");
        headers.put("longitude","72.48543");
        headers.put("androidId","AashitAndroidId");

        Map<String, String> body = new HashMap<String, String>();

        Response responseObject = middlewareServicesObject.CreateLead(createLead, queryParams, headers,body);

        String displayMsg = "Lead successfully created.";
        String actualDisplayMsg = responseObject.jsonPath().getJsonObject("displayMessage");
        Assert.assertTrue(displayMsg.contains(actualDisplayMsg));

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(200,statusCode);

        leadId = responseObject.jsonPath().getJsonObject("leadId");
        LOGGER.info(" PSA DIY LeadId : " +leadId);

    }

    @Test(priority = 0,dependsOnMethods = "TC002_CreateLeadPsaDiy",description = "Updating lead of PSA DIY",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public  void TC003_UpdateLeadPsaDiy()
    {
        Lead_create createLead = new Lead_create(P.TESTDATA.get("PsaDiyUpdateLead"));

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", "psa_diy");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionTypeLevel2","business_service");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("client","androidapp");
        headers.put("latitude","28.9342");
        headers.put("longitude","72.48543");
        headers.put("androidId","AashitAndroidId");

        LOGGER.info(" PSA DIY Address - " +" \n Line one : "  + lineOne + "\n Line two : " +lineTwo + " \n Line Three : " + lineThree + "\n Latitude : " +lat + "\n Longitude : " +longi);

        Map<String, String> body = new HashMap<String, String>();
        body.put("line1",lineOne);
        body.put("line2",lineTwo);
        body.put("line3",lineThree);
        body.put("latitude",lat);
        body.put("longitude",longi);

        Response responseObject = middlewareServicesObject.CreateLead(createLead, queryParams, headers,body);

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(200,statusCode);

        String responseLeadId = responseObject.jsonPath().getJsonObject("leadId");
        Assert.assertTrue(responseLeadId.contains(leadId));

    }

    @Test(priority = 0,description = "Positive Fetch Documents for PSA DIY",dependsOnMethods ="TC003_UpdateLeadPsaDiy",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_FetchDocumentsPsaDiy() throws JsonProcessingException
    {
        File DocPath = new File(System.getProperty("user.dir")+"/PaytmImage.jpg");
        LOGGER.info("File Path is : " + DocPath.getAbsolutePath());

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", "psa_diy");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionLeadId",leadId);
        queryParams.put("solutionTypeLevel2","business_service");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("client","androidapp");
        headers.put("latitude","28.9342");
        headers.put("longitude","72.48543");
        headers.put("androidId","AashitAndroidId");

        Map<String, String> queryParamsUpload = new HashMap<String, String>();
        queryParamsUpload.put("solution", "psa_diy");
        queryParamsUpload.put("entityType", "INDIVIDUAL");
        queryParamsUpload.put("channel", "PAYTM_APP");
        queryParamsUpload.put("solutionLeadId", leadId);
        queryParamsUpload.put("merchantCustId",AppCustId);
        queryParamsUpload.put("solutionTypeLevel2","business_service");

        FetchUploadDiyDoc(queryParams,queryParamsUpload,headers,DocPath);

        //v4Docs.validateResponseAgainstJSONSchema("MerchantService/V4/FetchDynamicDocs/FetchDynamicDocQrMerchantSchema.json");
    }

    @Test(priority = 0,description = "Save Bank Details PSA DIY",dependsOnMethods ="TC004_FetchDocumentsPsaDiy",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_SaveBakDetailsPsaDiy()
    {
        bank UpdateBank = new bank(P.TESTDATA.get("PsaDiyBankUpdate"));

        Response responseObject = middlewareServicesObject.getBankDetails(UpdateBank, "INDIVIDUAL", "business_service", "", "psa_diy", "PAYTM_APP", ApplicantToken, "application/json", mobileNo, "sbin0005226");

       int statusCode =  responseObject.getStatusCode();
       Assert.assertEquals(200,statusCode);

    }

    @Test(priority = 0,description = "Fetch Application Status",dependsOnMethods ="TC005_SaveBakDetailsPsaDiy",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC006_FetchApplicationStatusPsaDiy()
    {
        ApplicationStatus fetchStatus = new ApplicationStatus();

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", "psa_diy");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionLeadId",leadId);
        queryParams.put("isToProvideOnlyStage","false");
        queryParams.put("solutionTypeLevel2","business_service");


        Map<String, String> headers = new HashMap<String, String>();

        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("client","androidapp");
        headers.put("latitude","28.9342");
        headers.put("longitude","72.48543");
        headers.put("androidId","AashitAndroidId");

        Response responseObj = middlewareServicesObject.FetchApplicationStatus(fetchStatus,queryParams,headers);

         ApplicationStage = responseObj.jsonPath().getJsonObject("stage").toString();
         LOGGER.info("Current Application Stage is : " +ApplicationStage);

        int statusCode = responseObj.getStatusCode();
        Assert.assertEquals(200,statusCode);
    }

    @Test(priority = 0,description = "Gamepind Start Tes",dependsOnMethods ="TC006_FetchApplicationStatusPsaDiy",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_StartAssesmentPsaDiy()
    {


        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("leadId",leadId);

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("client","androidapp");
        headers.put("latitude","28.9342");
        headers.put("longitude","72.48543");
        headers.put("androidId","AashitAndroidId");

        int statusCode = 0;
        String displayMessage ="";
        for (int i = 0; i <=3;i++)
        {
            if(statusCode != 200)

            {
                waitForLoad(3000);
                StartTest TestBegan = new StartTest();

                Response responseObj = middlewareServicesObject.GamePindStartTest(TestBegan,queryParams,headers);

                displayMessage = responseObj.jsonPath().getJsonObject("displayMessage").toString();
                statusCode = responseObj.getStatusCode();
            }

            else
            {
                LOGGER.info("Status Code is 200 ");
                break;
            }
        }

        Assert.assertEquals(200,statusCode);
        Assert.assertTrue(displayMessage.contains("New Session Started"));


    }

    @Test(priority = 0,description = "Gamepind Assesment",dependsOnMethods ="TC007_StartAssesmentPsaDiy",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_GamePindAssesmentPsaDiy()
    {

        for (int i = 0; i <= 20;i++ )
        {
            LOGGER.info("Inside For Loop for Question / Answers");
            if (score <= 0)
            {
                LOGGER.info("Inside If to fetch Questions ");
                FetchQuestion fetchQues = new FetchQuestion();

                Map<String, String> queryParams = new HashMap<String, String>();

                queryParams.put("leadId",leadId);

                Map<String, String> headers = new HashMap<String, String>();

                headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
                headers.put("session_token", ApplicantToken);
                headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
                headers.put("client","androidapp");
                headers.put("latitude","28.9342");
                headers.put("longitude","72.48543");
                headers.put("androidId","AashitAndroidId");


                String QuesId =null;
                String CorrectAns = null;

                waitForLoad(300);
                Response responseObj = middlewareServicesObject.GamePindFetchQuestion(fetchQues,queryParams,headers);

                GamePindstatusCode = responseObj.getStatusCode();

                if (GamePindstatusCode == 200)
                {
                    QuesId =responseObj.jsonPath().getJsonObject("contestQuestionsList[0].quesId").toString();
                    LOGGER.info("This is Question Id : " + QuesId);

                    CorrectAns = responseObj.jsonPath().getJsonObject("contestQuestionsList[0].correctAnswer").toString();
                    LOGGER.info(" This is Correct Answer : " +CorrectAns);

                    score = responseObj.jsonPath().getJsonObject("score");
                    LOGGER.info(" Current Score is : " + score);

                    contestStatus = responseObj.jsonPath().getJsonObject("contestStatus");
                    LOGGER.info(" Contest Status is : " + contestStatus);
                }

                if (GamePindstatusCode == 417)
                {
                    LOGGER.info("Status code of GamePind is 417");
                    for (int j = 0; j<=5;j++)
                    {
                        LOGGER.info("Inside loop of GamePind Fetch Question for 417 and count is : " +j);
                        if (GamePindstatusCode == 417)
                        {
                            FetchQuestion fetchQueserr = new FetchQuestion();

                            Map<String, String> queryParamserr = new HashMap<String, String>();

                            queryParamserr.put("leadId",leadId);

                            Map<String, String> headerserr = new HashMap<String, String>();

                            headerserr.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
                            headerserr.put("session_token", ApplicantToken);
                            headerserr.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
                            headerserr.put("client","androidapp");
                            headerserr.put("latitude","28.9342");
                            headerserr.put("longitude","72.48543");
                            headerserr.put("androidId","AashitAndroidId");

                            waitForLoad(10000);
                            LOGGER.info("Inside IF condition of GamePind Assesment Loop ");
                            Response responseObjErr = middlewareServicesObject.GamePindFetchQuestion(fetchQueserr, queryParamserr, headerserr);
                            LOGGER.info("API called inside loop of assesment for 417");

                            GamePindstatusCode = responseObjErr.getStatusCode();
                            LOGGER.info("Fetching Statuscode inside for 417 : " +GamePindstatusCode);

                            if (GamePindstatusCode == 200)
                            {
                                QuesId = responseObjErr.jsonPath().getJsonObject("contestQuestionsList[0].quesId").toString();
                                LOGGER.info("This is Question Id : " + QuesId);

                                CorrectAns = responseObjErr.jsonPath().getJsonObject("contestQuestionsList[0].correctAnswer").toString();
                                LOGGER.info(" This is Correct Answer : " + CorrectAns);

                                score = responseObjErr.jsonPath().getJsonObject("score");
                                LOGGER.info(" Current Score is : " + score);

                                contestStatus = responseObjErr.jsonPath().getJsonObject("contestStatus");
                                LOGGER.info(" Contest Status is : " + contestStatus);
                            }

                        }
                        else
                        {
                            LOGGER.info("Status code of GamePind is 200, Breaking Loop");
                            break;
                        }
                    }

                }


                if (score <= 0 && contestStatus == null)
                {
                    LOGGER.info("Inside If to Submit Questions ");
                    SubmitQuestion subQues = new SubmitQuestion();

                    Map<String, String> queryParamsSub = new HashMap<String, String>();

                    Map<String, String> headersSub = new HashMap<String, String>();

                    headersSub.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
                    headersSub.put("session_token", ApplicantToken);
                    headersSub.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
                    headersSub.put("client","androidapp");
                    headersSub.put("latitude","28.9342");
                    headersSub.put("longitude","72.48543");
                    headersSub.put("androidId","AashitAndroidId");

                    Map<String, String> bodySub = new HashMap<String, String>();
                    bodySub.put("questionId",QuesId);
                    bodySub.put("answer",CorrectAns);

                    Response responseObjSub = middlewareServicesObject.GamePindSubmitQuestion(subQues,queryParamsSub,headersSub,bodySub);

                    int statusCodeSub = responseObjSub.getStatusCode();

                    if (statusCodeSub == 417)
                    {
                        LOGGER.info("Status code of GamePind is 417");
                        for (int k = 0; k<=5;k++)
                        {
                            LOGGER.info("Inside loop of GamePind Submit Question for 417 and count is : " +k);
                            if (statusCodeSub == 417)
                            {
                                waitForLoad(2000);

                                SubmitQuestion subQuesErr = new SubmitQuestion();
                                Response responseObjSubErr = middlewareServicesObject.GamePindSubmitQuestion(subQuesErr,queryParamsSub,headersSub,bodySub);

                                 statusCodeSub = responseObjSubErr.getStatusCode();
                                LOGGER.info("Fetching Statuscode inside for 417 of Submit : " +statusCodeSub);


                            }
                            else
                            {
                                LOGGER.info("Status code of GamePind Submit is 200, Breaking Loop");
                                break;
                            }
                        }

                    }

                    if (statusCodeSub == 200)
                    {
                        Assert.assertEquals(statusCodeSub,200);
                    }

                }
                else
                {
                    LOGGER.info("All questions are attempted ");

                }


            }

            else
            {
                LOGGER.info("Contest has been completed");
                break;
            }
        }

    }

    @Test(priority = 0,description = "Generating Order",dependsOnMethods ="TC008_GamePindAssesmentPsaDiy",groups = {"Regression"} ,enabled = true)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC009_GenerateOrderPsaDiy()
    {
        OrderFullfillment orderObj = new OrderFullfillment();

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("leadId",leadId);
        queryParams.put("generateOrder","true");
        queryParams.put("channel","PAYTM_APP");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("client","androidapp");
        headers.put("latitude","28.9342");
        headers.put("longitude","72.48543");
        headers.put("androidId","AashitAndroidId");

        waitForLoad(5000);
        Response resObj = middlewareServicesObject.OrderFullfillment(orderObj,queryParams,headers);

        OrderId = resObj.jsonPath().getJsonObject("ORDER_ID");
        MID = resObj.jsonPath().getJsonObject("MID");
        Amount = resObj.jsonPath().getString("TXN_AMOUNT");
        paymentDone = resObj.jsonPath().getJsonObject("paymentDone").toString();

        int statusCode = resObj.getStatusCode();
        Assert.assertEquals(200,statusCode);
        LOGGER.info("Status of Payment is : " +paymentDone);
    }

    @Test(priority = 0,description = "Payting for Order",dependsOnMethods ="TC009_GenerateOrderPsaDiy",groups = {"Regression"},enabled = true )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0010_OrderPaymentPsaDiy()
    {
        PgResponse = PayMerchant(OrderId,Amount,MID,"FalseOrderCreated");

        TC009_GenerateOrderPsaDiy();

        if(!paymentDone.equals("true"))
        {
            for(int i = 0 ;i <3;i++)
            {
                LOGGER.info("Inside Payment Loop");
                if(!PgResponse.equals("01"))
                {
                    LOGGER.info(" Performing Payment " +i+1 + " Time");
                    PgResponse = PayMerchant(OrderId, Amount, MID, "FalseOrderCreated");
                    LOGGER.info(" PG Response for payment is : " + PgResponse);
                }

                else
                {
                    TC009_GenerateOrderPsaDiy();
                    LOGGER.info("Payment Done After Payment Loop");
                    break;
                }
            }
        }

        Assert.assertTrue(paymentDone.contains("true"));
        LOGGER.info("Payment Done");

    }


    @Test(priority = 0,description = "Rejecting QC Documents",dependsOnMethods ="TC0010_OrderPaymentPsaDiy",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0011_RejectQcDocumentsPsaDiy() throws JsonProcessingException
    {
        if(!contestStatus.equals("Pass"))
        {
            TC007_StartAssesmentPsaDiy();
            TC008_GamePindAssesmentPsaDiy();
        }
        else
        {
            waitForLoad(25000);
            ReallocatingAgent(leadId,"1152");
        }

        for(int i = 0;i <3;i++)
        {
            LOGGER.info("Inside QC Loop of PSA Diy");
            if(QCstatusCode!=200)
            {
                LOGGER.info("Inside if Condition for QC Psa DIY : " + i+1);
                Map <String,String> RequestPanel = new HashMap<>();
                Map <String,String> ResponsePanel = new HashMap<>();

                RequestPanel.put("docStatus",OePanelDocStatus);
                RequestPanel.put("rejectionReason",RejectionReason);
                RequestPanel.put("leadId",leadId);


                ResponsePanel = FetchPanelLead(RequestPanel);

                DocumetRequestDeserialised = ResponsePanel.get("DocumenstArray");
                WorkFlowId = ResponsePanel.get("WorkFlowId");
                LOGGER. info("Lead Stage is : " + ResponsePanel.get("LeadStage"));

                waitForLoad(5000);
                EditLead v1EditLeadObj=new EditLead(leadId, P.TESTDATA.get("EditLeadPsaDiyPositive"));

                v1EditLeadObj.getProperties().setProperty("documents",DocumetRequestDeserialised);
                v1EditLeadObj.getProperties().setProperty("WorkFlowId", String.valueOf(WorkFlowId));

                Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "9560526665", "1106992015", XMWCookie,"application/json");

                QCstatusCode = responseObject.getStatusCode();
                SubStage = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
            }
            else
            {
                LOGGER.info("QC has been done Successfully");
                break;
            }
        }

        Assert.assertEquals(QCstatusCode,200);
        Assert.assertTrue(SubStage.contains(QcSubStgae));

    }

    @Test(priority = 0,description = "Rejecting QC Documents again",dependsOnMethods ="TC0011_RejectQcDocumentsPsaDiy",groups = {"Regression"},enabled = true)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0012_ApproveQcDocumentsPsaDiy() throws JsonProcessingException
    {
        TC004_FetchDocumentsPsaDiy();
        waitForLoad(5000);
        ReallocatingAgent(leadId,"1152");

        OePanelDocStatus = "APPROVED";
        RejectionReason = null;

        Map <String,String> RequestPanel = new HashMap<>();
        Map <String,String> ResponsePanel = new HashMap<>();
        RequestPanel.put("docStatus",OePanelDocStatus);
        RequestPanel.put("rejectionReason",RejectionReason);
        RequestPanel.put("leadId",leadId);


        ResponsePanel = FetchPanelLead(RequestPanel);

        DocumetRequestDeserialised = ResponsePanel.get("DocumenstArray");
        WorkFlowId = ResponsePanel.get("WorkFlowId");
        LOGGER. info("Lead Stage is : " + ResponsePanel.get("LeadStage"));

        waitForLoad(5000);
        EditLead v1EditLeadObj=new EditLead(leadId, P.TESTDATA.get("EditLeadPsaDiyPositive"));

        v1EditLeadObj.getProperties().setProperty("documents",DocumetRequestDeserialised);
        v1EditLeadObj.getProperties().setProperty("WorkFlowId", String.valueOf(WorkFlowId));

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "9560526665", "1106992015", XMWCookie,"application/json");

        QCstatusCode = responseObject.getStatusCode();
        SubStage = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();

        Assert.assertEquals(QCstatusCode,200);
        QcSubStgae = "PANEL_SUCCESS";
        Assert.assertTrue(SubStage.contains(QcSubStgae));
    }

    @Test(priority = 0,description = "Notify Order Callback",dependsOnMethods ="TC0010_OrderPaymentPsaDiy",groups = {"Regression"},enabled = false )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0013_OrderCallbackPsaDiy()
    {
        NotifyCallback notyObj = new NotifyCallback(P.TESTDATA.get("PsaDiyOrderNotify"));

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("orderId",OrderId);

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("client","androidapp");

        Map<String,String> body = new HashMap<>();
        body.put("orderId",OrderId);
        body.put("leadId",leadId);
        body.put("custId",AppCustId);

        Response respObj = middlewareServicesObject.OrderNotify(notyObj,queryParams,headers,body);

        int statusCode = respObj.getStatusCode();
        Assert.assertEquals(200,statusCode);


    }


}
