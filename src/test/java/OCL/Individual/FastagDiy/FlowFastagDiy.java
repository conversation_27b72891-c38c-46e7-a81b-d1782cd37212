package OCL.Individual.FastagDiy;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.sdMerchant.Lead_create;
import Services.MechantService.MiddlewareServices;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

public class FlowFastagDiy extends BaseMethod
{
    private static final Logger LOGGER = LogManager.getLogger(FlowFastagDiy.class);

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    Faker GenerateFake = new Faker();

    public int DMS1 = GenerateFake.number().numberBetween(100000,100000000);
    public int DMS2 = GenerateFake.number().numberBetween(100000,100000000);
    public int DMS3 = GenerateFake.number().numberBetween(100000,100000000);
    public int DMS4 = GenerateFake.number().numberBetween(100000,100000000);
    public int DMS5 = GenerateFake.number().numberBetween(100000,100000000);
    public int Vehicle = GenerateFake.number().numberBetween(1000,9999);
    public int CityCode = GenerateFake.number().numberBetween(1,99);

    public String LeadId ="";
    public static String OePanelDocStatus = "";
    public static String RejectionReason = "";
    public static String DocumetRequestDeserialised = "";
    public static String WorkFlowId = "";

    public String generateJwtToken()

    {

        LocalDateTime localDateTime=LocalDateTime.now(ZoneId.of("GMT+05:30"));
        // LocalDate localDate = localDateTime.toLocalDate();
        LOGGER.info("Date is :"+localDateTime);
        String ts= localDateTime.toString();
        LOGGER.info("This is TMP : " +ts+"+05:30");

        Algorithm buildAlgorithm = Algorithm.HMAC256("b5ab9b19-7f36-4eae-a845-5bdae889dfb8");
        String token= com.auth0.jwt.JWT.create().withIssuer("OE")
                .withClaim("clientId", "FIS")
                .withClaim("timestamp",ts+"+05:30")
                .withClaim("custId", "1001077503").sign(buildAlgorithm);

        LOGGER.info("JWT Token is : " + token);
        return token;
    }

    @Test(description = "Creating lead for Fastag DIY",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0001_CreateLead()
    {
        LOGGER.info("DMS 1 : " +DMS1 + "\n" + "DMS 2 : " +DMS2 + "\n" + "DMS 3: " + DMS3 + "\n" + "DMS 4: " + DMS4 + "\n" + "DMS 5: " + DMS5);
        LOGGER.info("Vehicle no. : " + Vehicle);
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", "fastag");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "FIS");
        queryParams.put("solutionTypeLevel2", "DIY");
        queryParams.put("solutionTypeLevel3", String.valueOf(Vehicle));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", generateJwtToken());
        headers.put("custId", "1001077503");
        headers.put("latitude", "28.9088");
        headers.put("longitude", "77.2727");

        Map<String, String> body = new HashMap<String, String>();
        body.put("DMS1", String.valueOf(DMS1));
        body.put("DMS2", String.valueOf(DMS2));
        body.put("DMS3", String.valueOf(DMS3));
        body.put("DMS4", String.valueOf(DMS4));
        body.put("DMS5", String.valueOf(DMS5));
        body.put("FASTAG_TAG_SEQUENCE_NUMBER", String.valueOf(DMS5));
        body.put("VEHICLE_IDENTIFICATION_NUMBER",String.valueOf(CityCode)+"CH"+String.valueOf(Vehicle));

        body.put("nameAsPerPan", "TOUCH WOOD LIMITED");


        Lead_create leadResponseObject = new Lead_create(P.TESTDATA.get("FastagDiyCreateLead"));

        Response responseObject = middlewareServicesObject.CreateLead(leadResponseObject, queryParams, headers, body);

        LOGGER.info("This is Display Message : " +responseObject.jsonPath().getString("displayMessage"));

        LeadId = responseObject.jsonPath().getString("leadId");
        LOGGER.info("This is Lead ID : " +LeadId);

        String ExpectedMsg = "Lead successfully created.";
        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),ExpectedMsg);
        Assert.assertEquals(responseObject.getStatusCode(),200);


    }

    @Test(description = "Submiting Lead of Fastag DIY",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0002_SubmitLeadPanel() throws JsonProcessingException {
        waitForLoad(5000);
        ReallocatingAgent(LeadId,"1152");

        OePanelDocStatus = "APPROVED";
        RejectionReason = null;

        Map <String,String> RequestPanel = new HashMap<>();
        Map <String,String> ResponsePanel = new HashMap<>();
        RequestPanel.put("docStatus",OePanelDocStatus);
        RequestPanel.put("rejectionReason",RejectionReason);
        RequestPanel.put("leadId",LeadId);


        ResponsePanel = FetchPanelLead(RequestPanel);

        DocumetRequestDeserialised = ResponsePanel.get("DocumenstArray");
        WorkFlowId = ResponsePanel.get("WorkFlowId");
        LOGGER. info("Lead Stage is : " + ResponsePanel.get("LeadStage"));

        waitForLoad(5000);
        EditLead v1EditLeadObj=new EditLead(LeadId, P.TESTDATA.get("EditLeadPsaDiyPositive"));

        v1EditLeadObj.getProperties().setProperty("documents",DocumetRequestDeserialised);
        v1EditLeadObj.getProperties().setProperty("WorkFlowId", String.valueOf(WorkFlowId));

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "9560526665", "1106992015", XMWCookie,"application/json");

        Assert.assertEquals(responseObject.getStatusCode(),200);
        Assert.assertTrue(responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString().contains("PANEL_SUCCESS"));
    }
}
