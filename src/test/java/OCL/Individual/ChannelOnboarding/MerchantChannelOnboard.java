package OCL.Individual.ChannelOnboarding;

import Request.MerchantService.v3.SendOtp;
import Request.MerchantService.v3.ValidateOtp;
import Request.MerchantService.v3.merchant.mid.Mobile;
import Request.chlbwp.subscription.v3.add.ChannelAdd;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.goldengate.common.GoogleAuthorizeUtil;
import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.sheets.v4.Sheets;
import com.jcraft.jsch.JSchException;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MerchantChannelOnboard extends BaseMethod {

    //Test data sheet
    String spreadsheetId = "1pSpZtqQ-YexgkdVn2Km8OsCXwjx2Sf9i08CAzq-n7Cg";
    public Sheets service;

    private static final Logger LOGGER = LogManager.getLogger(MerchantChannelOnboard.class);
    private static String OTP = null;
    public static String version = "3.7.8";
    public static String mobileNo = "7771110015";
    MiddlewareServices middlewareServicesObject;
    private String AgentToken;
    private String CustId;
    private int count=0;

    //Generate token
    BaseMethod ServiceToken = new BaseMethod();

    String sToken = "";
    @BeforeTest
    public void Setup() throws GeneralSecurityException, IOException
    {
        sToken = ServiceToken.AgentSessionToken("9654279917", "paytm@123");

//        final JsonFactory JSON_FACTORY = JacksonFactory.getDefaultInstance();
//        final NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();
//        GoogleAuthorizeUtil GoogleAuthorizeUtil= new GoogleAuthorizeUtil();
//        Credential credential= GoogleAuthorizeUtil.getCredentials(HTTP_TRANSPORT);
//        service = new Sheets.Builder(HTTP_TRANSPORT, JSON_FACTORY, credential).build();

    }

    @Test(testName = "Get Merchant details without MerchantID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getMerchantWithoutMerchantId() {
        middlewareServicesObject = new MiddlewareServices();

        Mobile getMerchantDetails = new Mobile();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("version", "3.8.2");
        headers.put("session_token", sToken);

        Map<String, String> params = new HashMap<String, String>();
        params.put("merchantMobile", "");
        params.put("requestType", "EDC");

        Response responseObject = middlewareServicesObject.searchMerchantDetails(getMerchantDetails, headers, params);
        String errorMsg = responseObject.jsonPath().getString("message");
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        //  System.out.println("Error message is " + errorMsg);
        //Assert.assertEquals(true, errorMsg.contains("version is empty in header"));

    }

    @Test(testName = "Get Merchant details without version header")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getMerchantWithoutVersion() {
        middlewareServicesObject = new MiddlewareServices();

        Mobile getMerchantDetails = new Mobile();

        Map<String, String> headers = new HashMap<String, String>();
        //      headers.put("version", "3.8.2");
        headers.put("session_token", sToken);

        Map<String, String> params = new HashMap<String, String>();
        params.put("merchantMobile", "6662220001");
        params.put("requestType", "EDC");

        Response responseObject = middlewareServicesObject.searchMerchantDetails(getMerchantDetails, headers, params);
        String errorMsg = responseObject.jsonPath().getString("message");
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Error message is " + errorMsg);
        //String errorMsg= responseObject.jsonPath().getString("errorMsg");
        Assert.assertEquals(true, errorMsg.contains("version is empty in header"));

    }

    @Test(testName = "Get Merchant details without session token")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getMerchantWithoutToken() {
        middlewareServicesObject = new MiddlewareServices();

        Mobile getMerchantDetails = new Mobile();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("version", "3.8.2");
//        headers.put("session_token", sToken);

        Map<String, String> params = new HashMap<String, String>();
        params.put("merchantMobile", "6662220001");
        params.put("requestType", "EDC");

        Response responseObject = middlewareServicesObject.searchMerchantDetails(getMerchantDetails, headers, params);
        // String errorMsg = responseObject.jsonPath().getString("message");
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 401);
        //  System.out.println("Error message is " + errorMsg);
        //String errorMsg= responseObject.jsonPath().getString("errorMsg");
        //Assert.assertEquals(true,errorMsg.contains("version is empty in header"));

    }

    @Test(testName = "Get Merchant details")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getMerchant() {
        middlewareServicesObject = new MiddlewareServices();

        Mobile getMerchantDetails = new Mobile();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("version", "3.8.2");
        headers.put("session_token", sToken);

        Map<String, String> params = new HashMap<String, String>();
        params.put("merchantMobile", mobileNo);
        params.put("requestType", "EDC");

        Response responseObject = middlewareServicesObject.searchMerchantDetails(getMerchantDetails, headers, params);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);

    }


    @Test(priority = 0, description = "Positive Send OTP for Channel Onboarding")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void PositiveSendOtp() {
        SendOtp SendOtp = new SendOtp();

        Response SendOtpResp = middlewareServicesObject.v3SentOtp(SendOtp, "INDIVIDUAL", "mgv_channels", sToken, version, mobileNo, "user_channels");

        String State = SendOtpResp.jsonPath().getString("state");
        System.out.println("New State " + State);

        String expectedMsg = "SUCCESS";
        String actualMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualMsg.contains(expectedMsg));

        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 36,description = "Positive ValidateOTP",dependsOnMethods = "PositiveSendOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveValidateOtp() throws IOException, JSchException {
        PositiveSendOtp();
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("ValidateOtpRequestChannelOnboard"));
        //OTP = getOTP(mobileNo);
        OTP="888888";
        System.out.println("This is OTP " + OTP);
        Response SendOtpResp = null;
        String newState = SendOtpResp.jsonPath().getString("state");
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj, "INDIVIDUAL", "mgv_channels", sToken, version, mobileNo, "user_channels", newState, OTP);

        String leadId = ResObj.jsonPath().getString("leadId");
        LOGGER.info("This is Lead ID : " + leadId);
        Assert.assertNotEquals(leadId, null);

        int StatusCode = ResObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        //TODO:Setting Value of CustID
        CustId = ResObj.jsonPath().getString("custId");
        System.out.println("CustId " + CustId);
        Assert.assertNotEquals(CustId, null);


        //String sample = "custId";
        if (null != CustId) {
            Assert.assertNotEquals(CustId, null);
        } else {
            System.out.println("Failed Because of Oauth");
        }
    }


    @Test(priority = 0, description = "Channel Subscription Tests")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void ChannelSubscriptionAPI() throws IOException {

        middlewareServicesObject = new MiddlewareServices();
        String range = "ChannelSubscription";

        Map<String, List<Object>> googleSheetMap = GoogleAuthorizeUtil.GoogleSheetMap(service, spreadsheetId, range);

        for (int i = 0; i < googleSheetMap.get("TestCase Name").size(); i++) {
            System.out.println("Test case "+i+" executing:");
            ChannelAdd ChannelAdd = new ChannelAdd();

            Map<String, Object> headers = new HashMap<String, Object>();
            headers.put("jwt", googleSheetMap.get("jwt").get(i));
            headers.put("trace-id", googleSheetMap.get("trace-id").get(i));
            headers.put("client-id", googleSheetMap.get("client-id").get(i));

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("pg_mid", googleSheetMap.get("pg_mid").get(i));
            body.put("kyb_id", googleSheetMap.get("kyb_id").get(i));
            body.put("shop_id", googleSheetMap.get("shop_id").get(i));
            body.put("plan_id", googleSheetMap.get("plan_id").get(i));

            Response responseObject = middlewareServicesObject.postChannelSubscriptionGoogle(ChannelAdd, headers, body);

            int StatusCode = responseObject.getStatusCode();
            String StatusCode1= Integer.toString(StatusCode);

            try
            {
                waitForLoad(5000);
                SoftAssert sa= new SoftAssert();
                sa.assertEquals(StatusCode1,googleSheetMap.get("StatusCode").get(i).toString());
                sa.assertEquals(responseObject.prettyPrint(),googleSheetMap.get("Response").get(i).toString());
                sa.assertAll();
            }
            catch (AssertionError e)
            {
                count++;
                System.out.println(e.getMessage());
                LOGGER.info("Number of test case failed: "+count);
            }
        }
    }
}

