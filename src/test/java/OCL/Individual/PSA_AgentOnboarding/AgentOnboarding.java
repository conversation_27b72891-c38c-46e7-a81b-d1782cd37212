package OCL.Individual.PSA_AgentOnboarding;

import OCL.Individual.SoundBox.FlowSoundBox;
import Request.PSA_Agent_Onboarding.BankDetailsAgentOnboarding;
import Request.PSA_Agent_Onboarding.PSAFetchScreenDetails;
import Request.PSA_Agent_Onboarding.PostAdhaardata;
import Request.PSA_Agent_Onboarding.PostBankDetailsAgentOnboarding;
import Request.SoundBox.SendOTPV1;
import Request.SoundBox.ValidateotpSB;
import Services.MechantService.MiddlewareServices;
import TestingLogics.TEST;
import com.amazonaws.services.identitymanagement.model.EntityType;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

public class AgentOnboarding extends BaseMethod {
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    TEST obj=new TEST();
    private static final Logger LOGGER = LogManager.getLogger(FlowSoundBox.class);
    public static String AgentToken = "";
    public static String EntityType = "INDIVIDUAL";
    public static String solutionType = "psa";
    public static String version = "7.2.2";
    public static String State = "";
    public static int retrycount = 10;
    public static String mobileNo = "**********";
    public static String userType = "merchant";
    public static String OTP = "888888";
    private String custId = "**********";
    private String leadid ;
    private String ifsccode = "HDFC0000001";
    public static String accountnumber = "************";
    public static  String Bankname = "HDFC Bank";

    @BeforeMethod
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLoginSoundBox() throws Exception{
        AgentToken = AgentSessionToken("**********", "paytm@123");
        establishConnectiontoServer(AgentToken , 5);
        LOGGER.info("Agent Token  for Soundbox : " + AgentToken);

    }
    // function to generate random 10 digits phone number
    public String generaterandomnumber(){
        // we created the object random
        Random random = new Random();
        // we are making sure our number is of 9 digit
        // This will generate any number less than ********* it coud be 22 now to make it 9 digit we are adding ********* and this will result to ********* --> 9********* this will be the actual number
        int nineDigitNumber = random.nextInt(*********) + *********;
        // Convert the number to a String
        String nineDigitString = Integer.toString(nineDigitNumber);
        // Prepend '9' to the 9-digit string to create a 10-digit number
        String tenDigitNumber = "9" + nineDigitString;
        return tenDigitNumber;

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Send OTP for PSA")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void PSAPositiveSendOtp() {
        mobileNo = this.generaterandomnumber();
        SendOTPV1 v3SendOtp = new SendOTPV1();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp, EntityType, solutionType, AgentToken, version, mobileNo, userType );
        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        String message = SendOtpResp.jsonPath().getJsonObject("message").toString();
        State = SendOtpResp.jsonPath().getJsonObject("state").toString();
        LOGGER.info("Message for the otp status is : "+ message);
        LOGGER.info("State for send OTP:" + State);
    }
    @Test(priority = 2 , dependsOnMethods = "PSAPositiveSendOtp", groups = {"Regression"} , description = "Validate Otp")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtp( ){
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String> headers = new HashMap<>();
        Map<String,String> body = new HashMap<>();
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("solutionType" , solutionType);
        headers.put("X-SRC","GGClient");
        headers.put("latitude","26.9009199");
        headers.put("ipAddress","************");
        headers.put("isBusyBoxFound","false");
        headers.put("deviceName","M2004J19C");
        headers.put("version",version);
        headers.put("X-MW-CHECKSUM-V3","glx8P0vUTCo/MORspOMs9kRgbOH1T7JIl8tXCWrF9KTQo6PLgGatKayUO0X2K8n6ZiLL4+k9MfFjiVhoDzW40HgaNk4Z8lP9vMXLi5yJSnWHzSncH1OPCq/dsrJAafUgj9Jea8dYcO8p4l8uZZM=");
        headers.put("isDeviceRooted","false");
        headers.put("session_token",AgentToken);
        headers.put("deviceIdentifier","Xiaomi-M2004J19C-131535fc93929702");
        headers.put("osVersion","10");
        headers.put("isLocationMocked","false");
        headers.put("X-MW-URL-CHECKSUM-V3","pSkVaXDbLC1OHf9oss5o9hVlP7XzHLFJlZleWj+X9PKFrKTP12/4Ka6aOxP1KZuqNXOQsbRuOqEwhltrCDjrhHkdNEkZrwny6ZuejZyKHiaHzSncH1OPCq/dsrJAYDuc9sC+uKABk87fh42Rv6s=");
        headers.put("client","androidapp");
        headers.put("deviceMac","60:6E:E8:D6:95:DB");
        headers.put("deviceManufacturer","Xiaomi");
        headers.put("androidId","131535fc93929702");
        headers.put("longitude","80.9287744");
        headers.put("appLanguage","en");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("User-Agent","Dalvik/2.1.0 (Linux; U; Android 10; M2004J19C MIUI/V12.0.3.0.QJCINXM)");
        headers.put("Host","goldengate-staging5.paytm.com");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("otp",OTP);
        body.put("state",State);
        body.put("userType",userType);
        body.put("mobile",mobileNo);
        body.put("individaulMerchantKyc","false");
        body.put("individualSolutionType","");
        body.put("onlyValidateOtp","true");
        body.put("skipOtp","false");
        body.put("tncVersion","");
        body.put("tncSetName","");
        ValidateotpSB validateotpobj = new ValidateotpSB();
        Response validateotprespobj = middlewareServicesObject.validateOTPSB(validateotpobj , queryParams , headers , body);
        custId = validateotprespobj.jsonPath().getJsonObject("custId").toString();
        leadid = validateotprespobj.jsonPath().getJsonObject("leadId").toString();
        LOGGER.info("Custumer Id is :" + custId);
        int statuscode = validateotprespobj.getStatusCode();
        Assert.assertEquals(statuscode , 200);
    }

    @Test(priority = 2 , dependsOnMethods = "PSAPositiveSendOtp", groups = {"Regression"} , description = "Validate Otp")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtp_Mobile_Number_NotGoing( ){
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String> headers = new HashMap<>();
        Map<String,String> body = new HashMap<>();
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("solutionType" , solutionType);
        headers.put("X-SRC","GGClient");
        headers.put("latitude","26.9009199");
        headers.put("ipAddress","************");
        headers.put("isBusyBoxFound","false");
        headers.put("deviceName","M2004J19C");
        headers.put("version",version);
        headers.put("X-MW-CHECKSUM-V3","glx8P0vUTCo/MORspOMs9kRgbOH1T7JIl8tXCWrF9KTQo6PLgGatKayUO0X2K8n6ZiLL4+k9MfFjiVhoDzW40HgaNk4Z8lP9vMXLi5yJSnWHzSncH1OPCq/dsrJAafUgj9Jea8dYcO8p4l8uZZM=");
        headers.put("isDeviceRooted","false");
        headers.put("session_token",AgentToken);
        headers.put("deviceIdentifier","Xiaomi-M2004J19C-131535fc93929702");
        headers.put("osVersion","10");
        headers.put("isLocationMocked","false");
        headers.put("X-MW-URL-CHECKSUM-V3","pSkVaXDbLC1OHf9oss5o9hVlP7XzHLFJlZleWj+X9PKFrKTP12/4Ka6aOxP1KZuqNXOQsbRuOqEwhltrCDjrhHkdNEkZrwny6ZuejZyKHiaHzSncH1OPCq/dsrJAYDuc9sC+uKABk87fh42Rv6s=");
        headers.put("client","androidapp");
        headers.put("deviceMac","60:6E:E8:D6:95:DB");
        headers.put("deviceManufacturer","Xiaomi");
        headers.put("androidId","131535fc93929702");
        headers.put("longitude","80.9287744");
        headers.put("appLanguage","en");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("User-Agent","Dalvik/2.1.0 (Linux; U; Android 10; M2004J19C MIUI/V12.0.3.0.QJCINXM)");
        headers.put("Host","goldengate-staging5.paytm.com");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("otp",OTP);
        body.put("state",State);
        body.put("userType",userType);
        body.put("mobile","");
        body.put("individaulMerchantKyc","false");
        body.put("individualSolutionType","");
        body.put("onlyValidateOtp","true");
        body.put("skipOtp","false");
        body.put("tncVersion","");
        body.put("tncSetName","");
        ValidateotpSB validateotpobj = new ValidateotpSB();
        Response validateotprespobj = middlewareServicesObject.validateOTPSB(validateotpobj , queryParams , headers , body);
//        custId = validateotprespobj.jsonPath().getJsonObject("custId").toString();
//        leadid = validateotprespobj.jsonPath().getJsonObject("leadId").toString();
//        LOGGER.info("Custumer Id is :" + custId);
        int statuscode = validateotprespobj.getStatusCode();
        Assert.assertEquals(statuscode , 200);
    }
    @Test(priority = 2 , dependsOnMethods = "PSAPositiveSendOtp", groups = {"Regression"} , description = "Validate Otp")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtp_otp_not_going( ){
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String> headers = new HashMap<>();
        Map<String,String> body = new HashMap<>();
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("solutionType" , solutionType);
        headers.put("X-SRC","GGClient");
        headers.put("latitude","26.9009199");
        headers.put("ipAddress","************");
        headers.put("isBusyBoxFound","false");
        headers.put("deviceName","M2004J19C");
        headers.put("version",version);
        headers.put("X-MW-CHECKSUM-V3","glx8P0vUTCo/MORspOMs9kRgbOH1T7JIl8tXCWrF9KTQo6PLgGatKayUO0X2K8n6ZiLL4+k9MfFjiVhoDzW40HgaNk4Z8lP9vMXLi5yJSnWHzSncH1OPCq/dsrJAafUgj9Jea8dYcO8p4l8uZZM=");
        headers.put("isDeviceRooted","false");
        headers.put("session_token",AgentToken);
        headers.put("deviceIdentifier","Xiaomi-M2004J19C-131535fc93929702");
        headers.put("osVersion","10");
        headers.put("isLocationMocked","false");
        headers.put("X-MW-URL-CHECKSUM-V3","pSkVaXDbLC1OHf9oss5o9hVlP7XzHLFJlZleWj+X9PKFrKTP12/4Ka6aOxP1KZuqNXOQsbRuOqEwhltrCDjrhHkdNEkZrwny6ZuejZyKHiaHzSncH1OPCq/dsrJAYDuc9sC+uKABk87fh42Rv6s=");
        headers.put("client","androidapp");
        headers.put("deviceMac","60:6E:E8:D6:95:DB");
        headers.put("deviceManufacturer","Xiaomi");
        headers.put("androidId","131535fc93929702");
        headers.put("longitude","80.9287744");
        headers.put("appLanguage","en");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("User-Agent","Dalvik/2.1.0 (Linux; U; Android 10; M2004J19C MIUI/V12.0.3.0.QJCINXM)");
        headers.put("Host","goldengate-staging5.paytm.com");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("otp","");
        body.put("state",State);
        body.put("userType",userType);
        body.put("mobile",mobileNo);
        body.put("individaulMerchantKyc","false");
        body.put("individualSolutionType","");
        body.put("onlyValidateOtp","true");
        body.put("skipOtp","false");
        body.put("tncVersion","");
        body.put("tncSetName","");
        ValidateotpSB validateotpobj = new ValidateotpSB();
        Response validateotprespobj = middlewareServicesObject.validateOTPSB(validateotpobj , queryParams , headers , body);
//        custId = validateotprespobj.jsonPath().getJsonObject("custId").toString();
//        leadid = validateotprespobj.jsonPath().getJsonObject("leadId").toString();
//        LOGGER.info("Custumer Id is :" + custId);
        int statuscode = validateotprespobj.getStatusCode();
        Assert.assertEquals(statuscode , 200);
    }
    @Test(priority = 2 , dependsOnMethods = "PSAPositiveSendOtp", groups = {"Regression"} , description = "Validate Otp")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtp_State_notgoing( ){
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String> headers = new HashMap<>();
        Map<String,String> body = new HashMap<>();
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("solutionType" , solutionType);
        headers.put("X-SRC","GGClient");
        headers.put("latitude","26.9009199");
        headers.put("ipAddress","************");
        headers.put("isBusyBoxFound","false");
        headers.put("deviceName","M2004J19C");
        headers.put("version",version);
        headers.put("X-MW-CHECKSUM-V3","glx8P0vUTCo/MORspOMs9kRgbOH1T7JIl8tXCWrF9KTQo6PLgGatKayUO0X2K8n6ZiLL4+k9MfFjiVhoDzW40HgaNk4Z8lP9vMXLi5yJSnWHzSncH1OPCq/dsrJAafUgj9Jea8dYcO8p4l8uZZM=");
        headers.put("isDeviceRooted","false");
        headers.put("session_token",AgentToken);
        headers.put("deviceIdentifier","Xiaomi-M2004J19C-131535fc93929702");
        headers.put("osVersion","10");
        headers.put("isLocationMocked","false");
        headers.put("X-MW-URL-CHECKSUM-V3","pSkVaXDbLC1OHf9oss5o9hVlP7XzHLFJlZleWj+X9PKFrKTP12/4Ka6aOxP1KZuqNXOQsbRuOqEwhltrCDjrhHkdNEkZrwny6ZuejZyKHiaHzSncH1OPCq/dsrJAYDuc9sC+uKABk87fh42Rv6s=");
        headers.put("client","androidapp");
        headers.put("deviceMac","60:6E:E8:D6:95:DB");
        headers.put("deviceManufacturer","Xiaomi");
        headers.put("androidId","131535fc93929702");
        headers.put("longitude","80.9287744");
        headers.put("appLanguage","en");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("User-Agent","Dalvik/2.1.0 (Linux; U; Android 10; M2004J19C MIUI/V12.0.3.0.QJCINXM)");
        headers.put("Host","goldengate-staging5.paytm.com");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("otp",OTP);
        body.put("state","");
        body.put("userType",userType);
        body.put("mobile",mobileNo);
        body.put("individaulMerchantKyc","false");
        body.put("individualSolutionType","");
        body.put("onlyValidateOtp","true");
        body.put("skipOtp","false");
        body.put("tncVersion","");
        body.put("tncSetName","");
        ValidateotpSB validateotpobj = new ValidateotpSB();
        Response validateotprespobj = middlewareServicesObject.validateOTPSB(validateotpobj , queryParams , headers , body);
//        custId = validateotprespobj.jsonPath().getJsonObject("custId").toString();
//        leadid = validateotprespobj.jsonPath().getJsonObject("leadId").toString();
//        LOGGER.info("Custumer Id is :" + custId);
        int statuscode = validateotprespobj.getStatusCode();
        Assert.assertEquals(statuscode , 200);
    }



    @Test(priority = 2 , dependsOnMethods = "PSAPositiveSendOtp", groups = {"Regression"} , description = "Validate Otp")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtp_solution_type_different( ){
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String> headers = new HashMap<>();
        Map<String,String> body = new HashMap<>();
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("solutionType" , "sound_box");
        headers.put("X-SRC","GGClient");
        headers.put("latitude","26.9009199");
        headers.put("ipAddress","************");
        headers.put("isBusyBoxFound","false");
        headers.put("deviceName","M2004J19C");
        headers.put("version",version);
        headers.put("X-MW-CHECKSUM-V3","glx8P0vUTCo/MORspOMs9kRgbOH1T7JIl8tXCWrF9KTQo6PLgGatKayUO0X2K8n6ZiLL4+k9MfFjiVhoDzW40HgaNk4Z8lP9vMXLi5yJSnWHzSncH1OPCq/dsrJAafUgj9Jea8dYcO8p4l8uZZM=");
        headers.put("isDeviceRooted","false");
        headers.put("session_token",AgentToken);
        headers.put("deviceIdentifier","Xiaomi-M2004J19C-131535fc93929702");
        headers.put("osVersion","10");
        headers.put("isLocationMocked","false");
        headers.put("X-MW-URL-CHECKSUM-V3","pSkVaXDbLC1OHf9oss5o9hVlP7XzHLFJlZleWj+X9PKFrKTP12/4Ka6aOxP1KZuqNXOQsbRuOqEwhltrCDjrhHkdNEkZrwny6ZuejZyKHiaHzSncH1OPCq/dsrJAYDuc9sC+uKABk87fh42Rv6s=");
        headers.put("client","androidapp");
        headers.put("deviceMac","60:6E:E8:D6:95:DB");
        headers.put("deviceManufacturer","Xiaomi");
        headers.put("androidId","131535fc93929702");
        headers.put("longitude","80.9287744");
        headers.put("appLanguage","en");
        headers.put("Content-Type","application/json; charset=UTF-8");
        headers.put("User-Agent","Dalvik/2.1.0 (Linux; U; Android 10; M2004J19C MIUI/V12.0.3.0.QJCINXM)");
        headers.put("Host","goldengate-staging5.paytm.com");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("otp",OTP);
        body.put("state",State);
        body.put("userType",userType);
        body.put("mobile",mobileNo);
        body.put("individaulMerchantKyc","false");
        body.put("individualSolutionType","");
        body.put("onlyValidateOtp","true");
        body.put("skipOtp","false");
        body.put("tncVersion","");
        body.put("tncSetName","");
        ValidateotpSB validateotpobj = new ValidateotpSB();
        Response validateotprespobj = middlewareServicesObject.validateOTPSB(validateotpobj , queryParams , headers , body);
//        custId = validateotprespobj.jsonPath().getJsonObject("custId").toString();
//        leadid = validateotprespobj.jsonPath().getJsonObject("leadId").toString();
//        LOGGER.info("Custumer Id is :" + custId);
        int statuscode = validateotprespobj.getStatusCode();
        Assert.assertEquals(statuscode , 401);
    }

    @Test(priority = 3 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void  fetchScreenDetails(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("fetchStrategy" , "SCREEN_DETAILS");
        queryParams.put("solutionType" , solutionType);
        queryParams.put("leadId" , leadid);

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("sec-ch-ua", "\"Chromium\";v=\"124\", \"Android WebView\";v=\"124\", \"Not-A.Brand\";v=\"99\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-cb220dcf01f7f14f");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "V7L0MQCzE0idXyrcveFxO+mJXstSoYJ+/3PQ/xhdVhL1sx1Io8vQmoYtYG5BWQyR6o2QR7wu7T6b7aa9Qv/rw19DmnQJ26DhEZzbFvhfZZDJ/B9bV3Q4kqCa2au6KigW");
        headers.put("androidid", "cb220dcf01f7f14f");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.179 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        PSAFetchScreenDetails obj = new PSAFetchScreenDetails();
        Response objresp = middlewareServicesObject.psa_fetch_screen_details(obj , queryParams , headers);
        int StatusCode = objresp.statusCode();
       // Assert.assertEquals(StatusCode , 200);
    }

    @Test(priority = 3 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void  fetchScreenDetails_agent_token_missing(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("fetchStrategy" , "SCREEN_DETAILS");
        queryParams.put("solutionType" , solutionType);
        queryParams.put("leadId" , leadid);

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("sec-ch-ua", "\"Chromium\";v=\"124\", \"Android WebView\";v=\"124\", \"Not-A.Brand\";v=\"99\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-cb220dcf01f7f14f");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "V7L0MQCzE0idXyrcveFxO+mJXstSoYJ+/3PQ/xhdVhL1sx1Io8vQmoYtYG5BWQyR6o2QR7wu7T6b7aa9Qv/rw19DmnQJ26DhEZzbFvhfZZDJ/B9bV3Q4kqCa2au6KigW");
        headers.put("androidid", "cb220dcf01f7f14f");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", "");
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.179 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        PSAFetchScreenDetails obj = new PSAFetchScreenDetails();
        Response objresp = middlewareServicesObject.psa_fetch_screen_details(obj , queryParams , headers);
        int StatusCode = objresp.statusCode();
        Assert.assertEquals(StatusCode , 401);
    }

    @Test(priority = 3 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void  fetchScreenDetails_version_empty(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("fetchStrategy" , "SCREEN_DETAILS");
        queryParams.put("solutionType" , solutionType);
        queryParams.put("leadId" , leadid);

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("sec-ch-ua", "\"Chromium\";v=\"124\", \"Android WebView\";v=\"124\", \"Not-A.Brand\";v=\"99\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-cb220dcf01f7f14f");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "V7L0MQCzE0idXyrcveFxO+mJXstSoYJ+/3PQ/xhdVhL1sx1Io8vQmoYtYG5BWQyR6o2QR7wu7T6b7aa9Qv/rw19DmnQJ26DhEZzbFvhfZZDJ/B9bV3Q4kqCa2au6KigW");
        headers.put("androidid", "cb220dcf01f7f14f");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.179 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", "");
        headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        PSAFetchScreenDetails obj = new PSAFetchScreenDetails();
        Response objresp = middlewareServicesObject.psa_fetch_screen_details(obj , queryParams , headers);
        int StatusCode = objresp.statusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 3 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void  fetchScreenDetails_older_version_configured(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("fetchStrategy" , "SCREEN_DETAILS");
        queryParams.put("solutionType" , solutionType);
        queryParams.put("leadId" , leadid);

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("sec-ch-ua", "\"Chromium\";v=\"124\", \"Android WebView\";v=\"124\", \"Not-A.Brand\";v=\"99\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-cb220dcf01f7f14f");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "V7L0MQCzE0idXyrcveFxO+mJXstSoYJ+/3PQ/xhdVhL1sx1Io8vQmoYtYG5BWQyR6o2QR7wu7T6b7aa9Qv/rw19DmnQJ26DhEZzbFvhfZZDJ/B9bV3Q4kqCa2au6KigW");
        headers.put("androidid", "cb220dcf01f7f14f");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.179 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", "1.1.1");
        headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        PSAFetchScreenDetails obj = new PSAFetchScreenDetails();
        Response objresp = middlewareServicesObject.psa_fetch_screen_details(obj , queryParams , headers);
        int StatusCode = objresp.statusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 3 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void  fetchScreenDetails_leadid_empty(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("fetchStrategy" , "SCREEN_DETAILS");
        queryParams.put("solutionType" , solutionType);
        queryParams.put("leadId" , "");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("sec-ch-ua", "\"Chromium\";v=\"124\", \"Android WebView\";v=\"124\", \"Not-A.Brand\";v=\"99\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-cb220dcf01f7f14f");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "V7L0MQCzE0idXyrcveFxO+mJXstSoYJ+/3PQ/xhdVhL1sx1Io8vQmoYtYG5BWQyR6o2QR7wu7T6b7aa9Qv/rw19DmnQJ26DhEZzbFvhfZZDJ/B9bV3Q4kqCa2au6KigW");
        headers.put("androidid", "cb220dcf01f7f14f");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.179 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        PSAFetchScreenDetails obj = new PSAFetchScreenDetails();
        Response objresp = middlewareServicesObject.psa_fetch_screen_details(obj , queryParams , headers);
        int StatusCode = objresp.statusCode();
       // Assert.assertEquals(StatusCode , 400);
    }
    @Test(priority = 3 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void  fetchScreenDetails_lead_id_random(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("fetchStrategy" , "SCREEN_DETAILS");
        queryParams.put("solutionType" , solutionType);
        queryParams.put("leadId" , "werttrewavcx");

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("sec-ch-ua", "\"Chromium\";v=\"124\", \"Android WebView\";v=\"124\", \"Not-A.Brand\";v=\"99\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-cb220dcf01f7f14f");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "V7L0MQCzE0idXyrcveFxO+mJXstSoYJ+/3PQ/xhdVhL1sx1Io8vQmoYtYG5BWQyR6o2QR7wu7T6b7aa9Qv/rw19DmnQJ26DhEZzbFvhfZZDJ/B9bV3Q4kqCa2au6KigW");
        headers.put("androidid", "cb220dcf01f7f14f");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.179 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        PSAFetchScreenDetails obj = new PSAFetchScreenDetails();
        Response objresp = middlewareServicesObject.psa_fetch_screen_details(obj , queryParams , headers);
        int StatusCode = objresp.statusCode();
       // Assert.assertEquals(StatusCode , 400);
    }
    @Test(priority = 3 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void  fetchScreenDetails_solutiontype_random(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("fetchStrategy" , "SCREEN_DETAILS");
        queryParams.put("solutionType" , "random");
        queryParams.put("leadId" , leadid);

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("sec-ch-ua", "\"Chromium\";v=\"124\", \"Android WebView\";v=\"124\", \"Not-A.Brand\";v=\"99\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-cb220dcf01f7f14f");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "V7L0MQCzE0idXyrcveFxO+mJXstSoYJ+/3PQ/xhdVhL1sx1Io8vQmoYtYG5BWQyR6o2QR7wu7T6b7aa9Qv/rw19DmnQJ26DhEZzbFvhfZZDJ/B9bV3Q4kqCa2au6KigW");
        headers.put("androidid", "cb220dcf01f7f14f");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.179 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        PSAFetchScreenDetails obj = new PSAFetchScreenDetails();
        Response objresp = middlewareServicesObject.psa_fetch_screen_details(obj , queryParams , headers);
        int StatusCode = objresp.statusCode();
      //  Assert.assertEquals(StatusCode , 400);
    }
    @Test(priority = 3 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void  fetchScreenDetails_fetchstrategy_empty(){
        Map<String , String> queryParams = new HashMap<>();
        queryParams.put("fetchStrategy" , "");
        queryParams.put("solutionType" , solutionType);
        queryParams.put("leadId" , leadid);

        Map<String, String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging6.paytm.com");
        headers.put("sec-ch-ua", "\"Chromium\";v=\"124\", \"Android WebView\";v=\"124\", \"Not-A.Brand\";v=\"99\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-cb220dcf01f7f14f");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "V7L0MQCzE0idXyrcveFxO+mJXstSoYJ+/3PQ/xhdVhL1sx1Io8vQmoYtYG5BWQyR6o2QR7wu7T6b7aa9Qv/rw19DmnQJ26DhEZzbFvhfZZDJ/B9bV3Q4kqCa2au6KigW");
        headers.put("androidid", "cb220dcf01f7f14f");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.179 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");

        PSAFetchScreenDetails obj = new PSAFetchScreenDetails();
        Response objresp = middlewareServicesObject.psa_fetch_screen_details(obj , queryParams , headers);
        int StatusCode = objresp.statusCode();
       // Assert.assertEquals(StatusCode , 400);
    }

    // Posting Adding adhaar data
    @Test(priority = 4 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void PostAdhaarData()  {
        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String > body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "gnMo+RoDZzBJR/x6yxzRQ7s1fUP4ekhAS9tYmA5U9GwU8m4HLQPpxhnfd3zUOQcYyNiFlO6Kc1hEx6PxjF49aqe+CpemaqeILR/M+XkqhaKL/lanKuqa3CMmlz32Gxw7");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "0UWBPw3TjvcEUPGZIAKG5uAyVni0LD9xINIGypONsUyFSj5W2ix7/N/JX6umlqV+5AHh/Ep4yvhHZ7CtdYMM4AxjCaXsy5cHkRtLv0rSD8vG/gcxfU8KmlnY/gXUyAwh");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.133 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("Cookie", "JSESSIONID=8D280182233FCD817CBCF389722D26A5");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        queryParams.put("solutionType", "psa");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadid);

        body.put("id", leadid);
        body.put("merchantCustId", custId);
        body.put("userAddressLine1", "Test");
        body.put("userAddressLine2", "Unnamed road");
        body.put("userAddressLine3", "Lucknow");
        body.put("userAddressState", "Uttar Pradesh");
        body.put("userAddressCity", "Lucknow");
        body.put("userAddressPincode", "226020");
        body.put("userAddressLandMark", "");
        body.put("NAME_AS_PER_AADHAR", "Kartikeya Tiwari");
        body.put("MERCHANT_DOB", "07/19/2006");
        body.put("GENDER", "male");
        body.put("AADHAR_NO_NOT_READABLE", "true");
        body.put("AADHAR_REF_NUMBER", "************");
        body.put("AADHAR_DATA_EDITED", "true");
        body.put("GUARDIAN_NAME", "Cpt");
        body.put("AADHAAR_DATA_SOURCE", "OCR");

        PostAdhaardata obj = new PostAdhaardata(custId);
        Response respobj = middlewareServicesObject.post_adhaar_data_agent_onboarding(obj , headers , queryParams , body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 4 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void PostAdhaarData_lead_empty_query_params(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String > body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "gnMo+RoDZzBJR/x6yxzRQ7s1fUP4ekhAS9tYmA5U9GwU8m4HLQPpxhnfd3zUOQcYyNiFlO6Kc1hEx6PxjF49aqe+CpemaqeILR/M+XkqhaKL/lanKuqa3CMmlz32Gxw7");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "0UWBPw3TjvcEUPGZIAKG5uAyVni0LD9xINIGypONsUyFSj5W2ix7/N/JX6umlqV+5AHh/Ep4yvhHZ7CtdYMM4AxjCaXsy5cHkRtLv0rSD8vG/gcxfU8KmlnY/gXUyAwh");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.133 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("Cookie", "JSESSIONID=8D280182233FCD817CBCF389722D26A5");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        queryParams.put("solutionType", "psa");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", "");

        body.put("id", leadid);
        body.put("merchantCustId", custId);
        body.put("userAddressLine1", "Test");
        body.put("userAddressLine2", "Unnamed road");
        body.put("userAddressLine3", "Lucknow");
        body.put("userAddressState", "Uttar Pradesh");
        body.put("userAddressCity", "Lucknow");
        body.put("userAddressPincode", "226020");
        body.put("userAddressLandMark", "");
        body.put("NAME_AS_PER_AADHAR", "Kartikeya Tiwari");
        body.put("MERCHANT_DOB", "07/19/2006");
        body.put("GENDER", "male");
        body.put("AADHAR_NO_NOT_READABLE", "true");
        body.put("AADHAR_REF_NUMBER", "************");
        body.put("AADHAR_DATA_EDITED", "true");
        body.put("GUARDIAN_NAME", "Cpt");
        body.put("AADHAAR_DATA_SOURCE", "OCR");

        PostAdhaardata obj = new PostAdhaardata(custId);
        Response respobj = middlewareServicesObject.post_adhaar_data_agent_onboarding(obj , headers , queryParams , body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 500);

    }
    @Test(priority = 4 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void PostAdhaarData_lead_id_empty_body(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String > body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "gnMo+RoDZzBJR/x6yxzRQ7s1fUP4ekhAS9tYmA5U9GwU8m4HLQPpxhnfd3zUOQcYyNiFlO6Kc1hEx6PxjF49aqe+CpemaqeILR/M+XkqhaKL/lanKuqa3CMmlz32Gxw7");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "0UWBPw3TjvcEUPGZIAKG5uAyVni0LD9xINIGypONsUyFSj5W2ix7/N/JX6umlqV+5AHh/Ep4yvhHZ7CtdYMM4AxjCaXsy5cHkRtLv0rSD8vG/gcxfU8KmlnY/gXUyAwh");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.133 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("Cookie", "JSESSIONID=8D280182233FCD817CBCF389722D26A5");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        queryParams.put("solutionType", "psa");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadid);

        body.put("id", "");
        body.put("merchantCustId", custId);
        body.put("userAddressLine1", "Test");
        body.put("userAddressLine2", "Unnamed road");
        body.put("userAddressLine3", "Lucknow");
        body.put("userAddressState", "Uttar Pradesh");
        body.put("userAddressCity", "Lucknow");
        body.put("userAddressPincode", "226020");
        body.put("userAddressLandMark", "");
        body.put("NAME_AS_PER_AADHAR", "Kartikeya Tiwari");
        body.put("MERCHANT_DOB", "07/19/2006");
        body.put("GENDER", "male");
        body.put("AADHAR_NO_NOT_READABLE", "true");
        body.put("AADHAR_REF_NUMBER", "************");
        body.put("AADHAR_DATA_EDITED", "true");
        body.put("GUARDIAN_NAME", "Cpt");
        body.put("AADHAAR_DATA_SOURCE", "OCR");

        PostAdhaardata obj = new PostAdhaardata(custId);
        Response respobj = middlewareServicesObject.post_adhaar_data_agent_onboarding(obj , headers , queryParams , body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 4 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void PostAdhaarData_cust_id_empty(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String > body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "gnMo+RoDZzBJR/x6yxzRQ7s1fUP4ekhAS9tYmA5U9GwU8m4HLQPpxhnfd3zUOQcYyNiFlO6Kc1hEx6PxjF49aqe+CpemaqeILR/M+XkqhaKL/lanKuqa3CMmlz32Gxw7");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "0UWBPw3TjvcEUPGZIAKG5uAyVni0LD9xINIGypONsUyFSj5W2ix7/N/JX6umlqV+5AHh/Ep4yvhHZ7CtdYMM4AxjCaXsy5cHkRtLv0rSD8vG/gcxfU8KmlnY/gXUyAwh");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.133 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("Cookie", "JSESSIONID=8D280182233FCD817CBCF389722D26A5");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        queryParams.put("solutionType", "psa");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadid);

        body.put("id", leadid);
        body.put("merchantCustId", "");
        body.put("userAddressLine1", "Test");
        body.put("userAddressLine2", "Unnamed road");
        body.put("userAddressLine3", "Lucknow");
        body.put("userAddressState", "Uttar Pradesh");
        body.put("userAddressCity", "Lucknow");
        body.put("userAddressPincode", "226020");
        body.put("userAddressLandMark", "");
        body.put("NAME_AS_PER_AADHAR", "Kartikeya Tiwari");
        body.put("MERCHANT_DOB", "07/19/2006");
        body.put("GENDER", "male");
        body.put("AADHAR_NO_NOT_READABLE", "true");
        body.put("AADHAR_REF_NUMBER", "************");
        body.put("AADHAR_DATA_EDITED", "true");
        body.put("GUARDIAN_NAME", "Cpt");
        body.put("AADHAAR_DATA_SOURCE", "OCR");

        PostAdhaardata obj = new PostAdhaardata(custId);
        Response respobj = middlewareServicesObject.post_adhaar_data_agent_onboarding(obj , headers , queryParams , body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 4 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void PostAdhaarData_cust_id_empty_path_parameter(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String > body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "gnMo+RoDZzBJR/x6yxzRQ7s1fUP4ekhAS9tYmA5U9GwU8m4HLQPpxhnfd3zUOQcYyNiFlO6Kc1hEx6PxjF49aqe+CpemaqeILR/M+XkqhaKL/lanKuqa3CMmlz32Gxw7");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "0UWBPw3TjvcEUPGZIAKG5uAyVni0LD9xINIGypONsUyFSj5W2ix7/N/JX6umlqV+5AHh/Ep4yvhHZ7CtdYMM4AxjCaXsy5cHkRtLv0rSD8vG/gcxfU8KmlnY/gXUyAwh");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.133 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("Cookie", "JSESSIONID=8D280182233FCD817CBCF389722D26A5");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        queryParams.put("solutionType", "psa");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadid);

        body.put("id", leadid);
        body.put("merchantCustId", custId);
        body.put("userAddressLine1", "Test");
        body.put("userAddressLine2", "Unnamed road");
        body.put("userAddressLine3", "Lucknow");
        body.put("userAddressState", "Uttar Pradesh");
        body.put("userAddressCity", "Lucknow");
        body.put("userAddressPincode", "226020");
        body.put("userAddressLandMark", "");
        body.put("NAME_AS_PER_AADHAR", "Kartikeya Tiwari");
        body.put("MERCHANT_DOB", "07/19/2006");
        body.put("GENDER", "male");
        body.put("AADHAR_NO_NOT_READABLE", "true");
        body.put("AADHAR_REF_NUMBER", "************");
        body.put("AADHAR_DATA_EDITED", "true");
        body.put("GUARDIAN_NAME", "Cpt");
        body.put("AADHAAR_DATA_SOURCE", "OCR");

        PostAdhaardata obj = new PostAdhaardata("");
        Response respobj = middlewareServicesObject.post_adhaar_data_agent_onboarding(obj , headers , queryParams , body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 405);

    }
    @Test(priority = 4 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void PostAdhaarData_version_missing(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String > body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "gnMo+RoDZzBJR/x6yxzRQ7s1fUP4ekhAS9tYmA5U9GwU8m4HLQPpxhnfd3zUOQcYyNiFlO6Kc1hEx6PxjF49aqe+CpemaqeILR/M+XkqhaKL/lanKuqa3CMmlz32Gxw7");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "0UWBPw3TjvcEUPGZIAKG5uAyVni0LD9xINIGypONsUyFSj5W2ix7/N/JX6umlqV+5AHh/Ep4yvhHZ7CtdYMM4AxjCaXsy5cHkRtLv0rSD8vG/gcxfU8KmlnY/gXUyAwh");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.133 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", "");
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("Cookie", "JSESSIONID=8D280182233FCD817CBCF389722D26A5");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        queryParams.put("solutionType", "psa");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadid);

        body.put("id", leadid);
        body.put("merchantCustId", custId);
        body.put("userAddressLine1", "Test");
        body.put("userAddressLine2", "Unnamed road");
        body.put("userAddressLine3", "Lucknow");
        body.put("userAddressState", "Uttar Pradesh");
        body.put("userAddressCity", "Lucknow");
        body.put("userAddressPincode", "226020");
        body.put("userAddressLandMark", "");
        body.put("NAME_AS_PER_AADHAR", "Kartikeya Tiwari");
        body.put("MERCHANT_DOB", "07/19/2006");
        body.put("GENDER", "male");
        body.put("AADHAR_NO_NOT_READABLE", "true");
        body.put("AADHAR_REF_NUMBER", "************");
        body.put("AADHAR_DATA_EDITED", "true");
        body.put("GUARDIAN_NAME", "Cpt");
        body.put("AADHAAR_DATA_SOURCE", "OCR");

        PostAdhaardata obj = new PostAdhaardata(custId);
        Response respobj = middlewareServicesObject.post_adhaar_data_agent_onboarding(obj , headers , queryParams , body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 4 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void PostAdhaarData_Agent_token_missing(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String > body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "gnMo+RoDZzBJR/x6yxzRQ7s1fUP4ekhAS9tYmA5U9GwU8m4HLQPpxhnfd3zUOQcYyNiFlO6Kc1hEx6PxjF49aqe+CpemaqeILR/M+XkqhaKL/lanKuqa3CMmlz32Gxw7");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "0UWBPw3TjvcEUPGZIAKG5uAyVni0LD9xINIGypONsUyFSj5W2ix7/N/JX6umlqV+5AHh/Ep4yvhHZ7CtdYMM4AxjCaXsy5cHkRtLv0rSD8vG/gcxfU8KmlnY/gXUyAwh");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", "");
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.133 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("Cookie", "JSESSIONID=8D280182233FCD817CBCF389722D26A5");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        queryParams.put("solutionType", "psa");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadid);

        body.put("id", leadid);
        body.put("merchantCustId", custId);
        body.put("userAddressLine1", "Test");
        body.put("userAddressLine2", "Unnamed road");
        body.put("userAddressLine3", "Lucknow");
        body.put("userAddressState", "Uttar Pradesh");
        body.put("userAddressCity", "Lucknow");
        body.put("userAddressPincode", "226020");
        body.put("userAddressLandMark", "");
        body.put("NAME_AS_PER_AADHAR", "Kartikeya Tiwari");
        body.put("MERCHANT_DOB", "07/19/2006");
        body.put("GENDER", "male");
        body.put("AADHAR_NO_NOT_READABLE", "true");
        body.put("AADHAR_REF_NUMBER", "************");
        body.put("AADHAR_DATA_EDITED", "true");
        body.put("GUARDIAN_NAME", "Cpt");
        body.put("AADHAAR_DATA_SOURCE", "OCR");

        PostAdhaardata obj = new PostAdhaardata(custId);
        Response respobj = middlewareServicesObject.post_adhaar_data_agent_onboarding(obj , headers , queryParams , body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 401);

    }

    @Test(priority = 4 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void PostAdhaarData_session_token_wrong(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String > body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "gnMo+RoDZzBJR/x6yxzRQ7s1fUP4ekhAS9tYmA5U9GwU8m4HLQPpxhnfd3zUOQcYyNiFlO6Kc1hEx6PxjF49aqe+CpemaqeILR/M+XkqhaKL/lanKuqa3CMmlz32Gxw7");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "0UWBPw3TjvcEUPGZIAKG5uAyVni0LD9xINIGypONsUyFSj5W2ix7/N/JX6umlqV+5AHh/Ep4yvhHZ7CtdYMM4AxjCaXsy5cHkRtLv0rSD8vG/gcxfU8KmlnY/gXUyAwh");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", "Random");
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.133 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("Cookie", "JSESSIONID=8D280182233FCD817CBCF389722D26A5");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        queryParams.put("solutionType", "psa");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadid);

        body.put("id", leadid);
        body.put("merchantCustId", custId);
        body.put("userAddressLine1", "Test");
        body.put("userAddressLine2", "Unnamed road");
        body.put("userAddressLine3", "Lucknow");
        body.put("userAddressState", "Uttar Pradesh");
        body.put("userAddressCity", "Lucknow");
        body.put("userAddressPincode", "226020");
        body.put("userAddressLandMark", "");
        body.put("NAME_AS_PER_AADHAR", "Kartikeya Tiwari");
        body.put("MERCHANT_DOB", "07/19/2006");
        body.put("GENDER", "male");
        body.put("AADHAR_NO_NOT_READABLE", "true");
        body.put("AADHAR_REF_NUMBER", "************");
        body.put("AADHAR_DATA_EDITED", "true");
        body.put("GUARDIAN_NAME", "Cpt");
        body.put("AADHAAR_DATA_SOURCE", "OCR");

        PostAdhaardata obj = new PostAdhaardata(custId);
        Response respobj = middlewareServicesObject.post_adhaar_data_agent_onboarding(obj , headers , queryParams , body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 410);

    }
    @Test(priority = 4 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void PostAdhaarData_version_lower(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String > body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "gnMo+RoDZzBJR/x6yxzRQ7s1fUP4ekhAS9tYmA5U9GwU8m4HLQPpxhnfd3zUOQcYyNiFlO6Kc1hEx6PxjF49aqe+CpemaqeILR/M+XkqhaKL/lanKuqa3CMmlz32Gxw7");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "0UWBPw3TjvcEUPGZIAKG5uAyVni0LD9xINIGypONsUyFSj5W2ix7/N/JX6umlqV+5AHh/Ep4yvhHZ7CtdYMM4AxjCaXsy5cHkRtLv0rSD8vG/gcxfU8KmlnY/gXUyAwh");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.133 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", "1.1.1");
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("Cookie", "JSESSIONID=8D280182233FCD817CBCF389722D26A5");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        queryParams.put("solutionType", "psa");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadid);

        body.put("id", leadid);
        body.put("merchantCustId", custId);
        body.put("userAddressLine1", "Test");
        body.put("userAddressLine2", "Unnamed road");
        body.put("userAddressLine3", "Lucknow");
        body.put("userAddressState", "Uttar Pradesh");
        body.put("userAddressCity", "Lucknow");
        body.put("userAddressPincode", "226020");
        body.put("userAddressLandMark", "");
        body.put("NAME_AS_PER_AADHAR", "Kartikeya Tiwari");
        body.put("MERCHANT_DOB", "07/19/2006");
        body.put("GENDER", "male");
        body.put("AADHAR_NO_NOT_READABLE", "true");
        body.put("AADHAR_REF_NUMBER", "************");
        body.put("AADHAR_DATA_EDITED", "true");
        body.put("GUARDIAN_NAME", "Cpt");
        body.put("AADHAAR_DATA_SOURCE", "OCR");

        PostAdhaardata obj = new PostAdhaardata(custId);
        Response respobj = middlewareServicesObject.post_adhaar_data_agent_onboarding(obj , headers , queryParams , body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 4 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void PostAdhaarData_upper_version(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String > body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "gnMo+RoDZzBJR/x6yxzRQ7s1fUP4ekhAS9tYmA5U9GwU8m4HLQPpxhnfd3zUOQcYyNiFlO6Kc1hEx6PxjF49aqe+CpemaqeILR/M+XkqhaKL/lanKuqa3CMmlz32Gxw7");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "0UWBPw3TjvcEUPGZIAKG5uAyVni0LD9xINIGypONsUyFSj5W2ix7/N/JX6umlqV+5AHh/Ep4yvhHZ7CtdYMM4AxjCaXsy5cHkRtLv0rSD8vG/gcxfU8KmlnY/gXUyAwh");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.133 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", "8.0.1");
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("Cookie", "JSESSIONID=8D280182233FCD817CBCF389722D26A5");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        queryParams.put("solutionType", "psa");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadid);

        body.put("id", leadid);
        body.put("merchantCustId", custId);
        body.put("userAddressLine1", "Test");
        body.put("userAddressLine2", "Unnamed road");
        body.put("userAddressLine3", "Lucknow");
        body.put("userAddressState", "Uttar Pradesh");
        body.put("userAddressCity", "Lucknow");
        body.put("userAddressPincode", "226020");
        body.put("userAddressLandMark", "");
        body.put("NAME_AS_PER_AADHAR", "Kartikeya Tiwari");
        body.put("MERCHANT_DOB", "07/19/2006");
        body.put("GENDER", "male");
        body.put("AADHAR_NO_NOT_READABLE", "true");
        body.put("AADHAR_REF_NUMBER", "************");
        body.put("AADHAR_DATA_EDITED", "true");
        body.put("GUARDIAN_NAME", "Cpt");
        body.put("AADHAAR_DATA_SOURCE", "OCR");

        PostAdhaardata obj = new PostAdhaardata(custId);
        Response respobj = middlewareServicesObject.post_adhaar_data_agent_onboarding(obj , headers , queryParams , body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 4 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void PostAdhaarData_lead_random(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String > body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "gnMo+RoDZzBJR/x6yxzRQ7s1fUP4ekhAS9tYmA5U9GwU8m4HLQPpxhnfd3zUOQcYyNiFlO6Kc1hEx6PxjF49aqe+CpemaqeILR/M+XkqhaKL/lanKuqa3CMmlz32Gxw7");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "0UWBPw3TjvcEUPGZIAKG5uAyVni0LD9xINIGypONsUyFSj5W2ix7/N/JX6umlqV+5AHh/Ep4yvhHZ7CtdYMM4AxjCaXsy5cHkRtLv0rSD8vG/gcxfU8KmlnY/gXUyAwh");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.133 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("Cookie", "JSESSIONID=8D280182233FCD817CBCF389722D26A5");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        queryParams.put("solutionType", "psa");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", "random");

        body.put("id", leadid);
        body.put("merchantCustId", custId);
        body.put("userAddressLine1", "Test");
        body.put("userAddressLine2", "Unnamed road");
        body.put("userAddressLine3", "Lucknow");
        body.put("userAddressState", "Uttar Pradesh");
        body.put("userAddressCity", "Lucknow");
        body.put("userAddressPincode", "226020");
        body.put("userAddressLandMark", "");
        body.put("NAME_AS_PER_AADHAR", "Kartikeya Tiwari");
        body.put("MERCHANT_DOB", "07/19/2006");
        body.put("GENDER", "male");
        body.put("AADHAR_NO_NOT_READABLE", "true");
        body.put("AADHAR_REF_NUMBER", "************");
        body.put("AADHAR_DATA_EDITED", "true");
        body.put("GUARDIAN_NAME", "Cpt");
        body.put("AADHAAR_DATA_SOURCE", "OCR");

        PostAdhaardata obj = new PostAdhaardata(custId);
        Response respobj = middlewareServicesObject.post_adhaar_data_agent_onboarding(obj , headers , queryParams , body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 500);

    }
    @Test(priority = 4 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void PostAdhaarData_body_lead_id_random(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String > body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "gnMo+RoDZzBJR/x6yxzRQ7s1fUP4ekhAS9tYmA5U9GwU8m4HLQPpxhnfd3zUOQcYyNiFlO6Kc1hEx6PxjF49aqe+CpemaqeILR/M+XkqhaKL/lanKuqa3CMmlz32Gxw7");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "0UWBPw3TjvcEUPGZIAKG5uAyVni0LD9xINIGypONsUyFSj5W2ix7/N/JX6umlqV+5AHh/Ep4yvhHZ7CtdYMM4AxjCaXsy5cHkRtLv0rSD8vG/gcxfU8KmlnY/gXUyAwh");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.133 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("Cookie", "JSESSIONID=8D280182233FCD817CBCF389722D26A5");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        queryParams.put("solutionType", "psa");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadid);

        body.put("id", "random");
        body.put("merchantCustId", custId);
        body.put("userAddressLine1", "Test");
        body.put("userAddressLine2", "Unnamed road");
        body.put("userAddressLine3", "Lucknow");
        body.put("userAddressState", "Uttar Pradesh");
        body.put("userAddressCity", "Lucknow");
        body.put("userAddressPincode", "226020");
        body.put("userAddressLandMark", "");
        body.put("NAME_AS_PER_AADHAR", "Kartikeya Tiwari");
        body.put("MERCHANT_DOB", "07/19/2006");
        body.put("GENDER", "male");
        body.put("AADHAR_NO_NOT_READABLE", "true");
        body.put("AADHAR_REF_NUMBER", "************");
        body.put("AADHAR_DATA_EDITED", "true");
        body.put("GUARDIAN_NAME", "Cpt");
        body.put("AADHAAR_DATA_SOURCE", "OCR");

        PostAdhaardata obj = new PostAdhaardata(custId);
        Response respobj = middlewareServicesObject.post_adhaar_data_agent_onboarding(obj , headers , queryParams , body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 4 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void PostAdhaarData_custid_random(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String > body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "gnMo+RoDZzBJR/x6yxzRQ7s1fUP4ekhAS9tYmA5U9GwU8m4HLQPpxhnfd3zUOQcYyNiFlO6Kc1hEx6PxjF49aqe+CpemaqeILR/M+XkqhaKL/lanKuqa3CMmlz32Gxw7");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "0UWBPw3TjvcEUPGZIAKG5uAyVni0LD9xINIGypONsUyFSj5W2ix7/N/JX6umlqV+5AHh/Ep4yvhHZ7CtdYMM4AxjCaXsy5cHkRtLv0rSD8vG/gcxfU8KmlnY/gXUyAwh");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.133 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("Cookie", "JSESSIONID=8D280182233FCD817CBCF389722D26A5");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        queryParams.put("solutionType", "psa");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadid);

        body.put("id", leadid);
        body.put("merchantCustId", "random");
        body.put("userAddressLine1", "Test");
        body.put("userAddressLine2", "Unnamed road");
        body.put("userAddressLine3", "Lucknow");
        body.put("userAddressState", "Uttar Pradesh");
        body.put("userAddressCity", "Lucknow");
        body.put("userAddressPincode", "226020");
        body.put("userAddressLandMark", "");
        body.put("NAME_AS_PER_AADHAR", "Kartikeya Tiwari");
        body.put("MERCHANT_DOB", "07/19/2006");
        body.put("GENDER", "male");
        body.put("AADHAR_NO_NOT_READABLE", "true");
        body.put("AADHAR_REF_NUMBER", "************");
        body.put("AADHAR_DATA_EDITED", "true");
        body.put("GUARDIAN_NAME", "Cpt");
        body.put("AADHAAR_DATA_SOURCE", "OCR");

        PostAdhaardata obj = new PostAdhaardata(custId);
        Response respobj = middlewareServicesObject.post_adhaar_data_agent_onboarding(obj , headers , queryParams , body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 4 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void PostAdhaarData_body_data_not_going(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String > body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "gnMo+RoDZzBJR/x6yxzRQ7s1fUP4ekhAS9tYmA5U9GwU8m4HLQPpxhnfd3zUOQcYyNiFlO6Kc1hEx6PxjF49aqe+CpemaqeILR/M+XkqhaKL/lanKuqa3CMmlz32Gxw7");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "0UWBPw3TjvcEUPGZIAKG5uAyVni0LD9xINIGypONsUyFSj5W2ix7/N/JX6umlqV+5AHh/Ep4yvhHZ7CtdYMM4AxjCaXsy5cHkRtLv0rSD8vG/gcxfU8KmlnY/gXUyAwh");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.133 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("Cookie", "JSESSIONID=8D280182233FCD817CBCF389722D26A5");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        queryParams.put("solutionType", "psa");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadid);

        body.put("id", leadid);
        body.put("merchantCustId", custId);


        PostAdhaardata obj = new PostAdhaardata(custId);
        Response respobj = middlewareServicesObject.post_adhaar_data_agent_onboarding(obj , headers , queryParams , body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 4 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void PostAdhaarData_cust_id_random_path_parameter(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String > body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "gnMo+RoDZzBJR/x6yxzRQ7s1fUP4ekhAS9tYmA5U9GwU8m4HLQPpxhnfd3zUOQcYyNiFlO6Kc1hEx6PxjF49aqe+CpemaqeILR/M+XkqhaKL/lanKuqa3CMmlz32Gxw7");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "0UWBPw3TjvcEUPGZIAKG5uAyVni0LD9xINIGypONsUyFSj5W2ix7/N/JX6umlqV+5AHh/Ep4yvhHZ7CtdYMM4AxjCaXsy5cHkRtLv0rSD8vG/gcxfU8KmlnY/gXUyAwh");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.133 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("Cookie", "JSESSIONID=8D280182233FCD817CBCF389722D26A5");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        queryParams.put("solutionType", "psa");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadid);

        body.put("id", leadid);
        body.put("merchantCustId", custId);
        body.put("userAddressLine1", "Test");
        body.put("userAddressLine2", "Unnamed road");
        body.put("userAddressLine3", "Lucknow");
        body.put("userAddressState", "Uttar Pradesh");
        body.put("userAddressCity", "Lucknow");
        body.put("userAddressPincode", "226020");
        body.put("userAddressLandMark", "");
        body.put("NAME_AS_PER_AADHAR", "Kartikeya Tiwari");
        body.put("MERCHANT_DOB", "07/19/2006");
        body.put("GENDER", "male");
        body.put("AADHAR_NO_NOT_READABLE", "true");
        body.put("AADHAR_REF_NUMBER", "************");
        body.put("AADHAR_DATA_EDITED", "true");
        body.put("GUARDIAN_NAME", "Cpt");
        body.put("AADHAAR_DATA_SOURCE", "OCR");

        PostAdhaardata obj = new PostAdhaardata("random");
        Response respobj = middlewareServicesObject.post_adhaar_data_agent_onboarding(obj , headers , queryParams , body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 500);

    }
    @Test(priority = 5 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetch_bank_details_with_ifsc(){
        Map<String , String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "r9CQfthl6dUrCCtVIK2hz68Et3NqcizeGLivwzu/NRDBf2XWDayL52R3AdofKP4BDKT8aj1LBDyH1lF4Nbq9qo/a7D2W15zHXFVl5NYNI++wqi640yr0IAEMG9JAjiP2");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        BankDetailsAgentOnboarding obj = new BankDetailsAgentOnboarding(ifsccode);
        Response respobj = middlewareServicesObject.fetch_bank_details_agent_onboarding(obj , headers );
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);
        Bankname = respobj.jsonPath().getJsonObject("bankDetails.bankName").toString();
    }
    @Test(priority = 5 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetch_bank_details_with_ifsc_agenttoken_empty(){
        Map<String , String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "r9CQfthl6dUrCCtVIK2hz68Et3NqcizeGLivwzu/NRDBf2XWDayL52R3AdofKP4BDKT8aj1LBDyH1lF4Nbq9qo/a7D2W15zHXFVl5NYNI++wqi640yr0IAEMG9JAjiP2");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", "");
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        BankDetailsAgentOnboarding obj = new BankDetailsAgentOnboarding(ifsccode);
        Response respobj = middlewareServicesObject.fetch_bank_details_agent_onboarding(obj , headers );
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 401);
    }
    @Test(priority = 5 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetch_bank_details_with_ifsc_randomagentsession(){
        Map<String , String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "r9CQfthl6dUrCCtVIK2hz68Et3NqcizeGLivwzu/NRDBf2XWDayL52R3AdofKP4BDKT8aj1LBDyH1lF4Nbq9qo/a7D2W15zHXFVl5NYNI++wqi640yr0IAEMG9JAjiP2");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", "random");
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        BankDetailsAgentOnboarding obj = new BankDetailsAgentOnboarding(ifsccode);
        Response respobj = middlewareServicesObject.fetch_bank_details_agent_onboarding(obj , headers );
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 410);
    }
    @Test(priority = 5 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetch_bank_details_with_ifsc_version_empty(){
        Map<String , String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "r9CQfthl6dUrCCtVIK2hz68Et3NqcizeGLivwzu/NRDBf2XWDayL52R3AdofKP4BDKT8aj1LBDyH1lF4Nbq9qo/a7D2W15zHXFVl5NYNI++wqi640yr0IAEMG9JAjiP2");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", "");
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        BankDetailsAgentOnboarding obj = new BankDetailsAgentOnboarding(ifsccode);
        Response respobj = middlewareServicesObject.fetch_bank_details_agent_onboarding(obj , headers );
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 5 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetch_bank_details_with_ifsc_version_random(){
        Map<String , String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "r9CQfthl6dUrCCtVIK2hz68Et3NqcizeGLivwzu/NRDBf2XWDayL52R3AdofKP4BDKT8aj1LBDyH1lF4Nbq9qo/a7D2W15zHXFVl5NYNI++wqi640yr0IAEMG9JAjiP2");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", "12.1.0");
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        BankDetailsAgentOnboarding obj = new BankDetailsAgentOnboarding(ifsccode);
        Response respobj = middlewareServicesObject.fetch_bank_details_agent_onboarding(obj , headers );
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 5 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetch_bank_details_with_ifsc_older_version(){
        Map<String , String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "r9CQfthl6dUrCCtVIK2hz68Et3NqcizeGLivwzu/NRDBf2XWDayL52R3AdofKP4BDKT8aj1LBDyH1lF4Nbq9qo/a7D2W15zHXFVl5NYNI++wqi640yr0IAEMG9JAjiP2");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", "1.2.1");
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        BankDetailsAgentOnboarding obj = new BankDetailsAgentOnboarding(ifsccode);
        Response respobj = middlewareServicesObject.fetch_bank_details_agent_onboarding(obj , headers );
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 5 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetch_bank_details_with_ifsc_ifsc_code_empty(){
        Map<String , String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "r9CQfthl6dUrCCtVIK2hz68Et3NqcizeGLivwzu/NRDBf2XWDayL52R3AdofKP4BDKT8aj1LBDyH1lF4Nbq9qo/a7D2W15zHXFVl5NYNI++wqi640yr0IAEMG9JAjiP2");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        BankDetailsAgentOnboarding obj = new BankDetailsAgentOnboarding("");
        Response respobj = middlewareServicesObject.fetch_bank_details_agent_onboarding(obj , headers );
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 5 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetch_bank_details_with_ifsc_random(){
        Map<String , String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "r9CQfthl6dUrCCtVIK2hz68Et3NqcizeGLivwzu/NRDBf2XWDayL52R3AdofKP4BDKT8aj1LBDyH1lF4Nbq9qo/a7D2W15zHXFVl5NYNI++wqi640yr0IAEMG9JAjiP2");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        BankDetailsAgentOnboarding obj = new BankDetailsAgentOnboarding("random");
        Response respobj = middlewareServicesObject.fetch_bank_details_agent_onboarding(obj , headers );
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 5 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetch_bank_details_with_ifsc_checksum_missing(){
        Map<String , String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "r9CQfthl6dUrCCtVIK2hz68Et3NqcizeGLivwzu/NRDBf2XWDayL52R3AdofKP4BDKT8aj1LBDyH1lF4Nbq9qo/a7D2W15zHXFVl5NYNI++wqi640yr0IAEMG9JAjiP2");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");

        BankDetailsAgentOnboarding obj = new BankDetailsAgentOnboarding(ifsccode);
        Response respobj = middlewareServicesObject.fetch_bank_details_agent_onboarding(obj , headers );
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        query_params.put("solutionType", "psa");
        query_params.put("entityType", "INDIVIDUAL");
        query_params.put("leadId", leadid);

        body.put("leadId", leadid);
        body.put("onlyValidateBankDetails", true);
        body.put("bankAccountNumber", accountnumber);
        body.put("ifsc", ifsccode);
        body.put("bankName", Bankname);

        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details_lead_id_empty_queryparams(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        query_params.put("solutionType", "psa");
        query_params.put("entityType", "INDIVIDUAL");
        query_params.put("leadId", "");

        body.put("leadId", leadid);
        body.put("onlyValidateBankDetails", true);
        body.put("bankAccountNumber", accountnumber);
        body.put("ifsc", ifsccode);
        body.put("bankName", Bankname);

        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 500);

    }
    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details_leadidempty_body(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        query_params.put("solutionType", "psa");
        query_params.put("entityType", "INDIVIDUAL");
        query_params.put("leadId", leadid);

        body.put("leadId", "");
        body.put("onlyValidateBankDetails", true);
        body.put("bankAccountNumber", accountnumber);
        body.put("ifsc", ifsccode);
        body.put("bankName", Bankname);

        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details_checksum_missing(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");


        query_params.put("solutionType", "psa");
        query_params.put("entityType", "INDIVIDUAL");
        query_params.put("leadId", leadid);

        body.put("leadId", leadid);
        body.put("onlyValidateBankDetails", true);
        body.put("bankAccountNumber", accountnumber);
        body.put("ifsc", ifsccode);
        body.put("bankName", Bankname);

        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 412);

    }
    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details_solution_type_missing(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        query_params.put("solutionType", "");
        query_params.put("entityType", "INDIVIDUAL");
        query_params.put("leadId", leadid);

        body.put("leadId", leadid);
        body.put("onlyValidateBankDetails", true);
        body.put("bankAccountNumber", accountnumber);
        body.put("ifsc", ifsccode);
        body.put("bankName", Bankname);

        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 500);

    }
    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details_entity_missing(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        query_params.put("solutionType", "psa");
        query_params.put("entityType", "");
        query_params.put("leadId", leadid);

        body.put("leadId", leadid);
        body.put("onlyValidateBankDetails", true);
        body.put("bankAccountNumber", accountnumber);
        body.put("ifsc", ifsccode);
        body.put("bankName", Bankname);

        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 500);

    }
    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details_Bank_name_random(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        query_params.put("solutionType", "psa");
        query_params.put("entityType", "INDIVIDUAL");
        query_params.put("leadId", leadid);

        body.put("leadId", leadid);
        body.put("onlyValidateBankDetails", true);
        body.put("bankAccountNumber", accountnumber);
        body.put("ifsc", ifsccode);
        body.put("bankName", "Random");

        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 400);

    }
    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details_ifsc_code_random(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        query_params.put("solutionType", "psa");
        query_params.put("entityType", "INDIVIDUAL");
        query_params.put("leadId", leadid);

        body.put("leadId", leadid);
        body.put("onlyValidateBankDetails", true);
        body.put("bankAccountNumber", accountnumber);
        body.put("ifsc", "random");
        body.put("bankName", Bankname);

        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details_account_number_random(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        query_params.put("solutionType", "psa");
        query_params.put("entityType", "INDIVIDUAL");
        query_params.put("leadId", leadid);

        body.put("leadId", leadid);
        body.put("onlyValidateBankDetails", true);
        body.put("bankAccountNumber", "random");
        body.put("ifsc", ifsccode);
        body.put("bankName", Bankname);

        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details_body_lead_id_random(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        query_params.put("solutionType", "psa");
        query_params.put("entityType", "INDIVIDUAL");
        query_params.put("leadId", leadid);

        body.put("leadId", "2a153168-0c42-4c9f-b7d1");
        body.put("onlyValidateBankDetails", true);
        body.put("bankAccountNumber", accountnumber);
        body.put("ifsc", ifsccode);
        body.put("bankName", Bankname);

        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details_solution_type_random(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        query_params.put("solutionType", "random");
        query_params.put("entityType", "INDIVIDUAL");
        query_params.put("leadId", leadid);

        body.put("leadId", leadid);
        body.put("onlyValidateBankDetails", true);
        body.put("bankAccountNumber", accountnumber);
        body.put("ifsc", ifsccode);
        body.put("bankName", Bankname);

        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 500);

    }

    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details_lower_version(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", "1.1.1");
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        query_params.put("solutionType", "psa");
        query_params.put("entityType", "INDIVIDUAL");
        query_params.put("leadId", leadid);

        body.put("leadId", leadid);
        body.put("onlyValidateBankDetails", true);
        body.put("bankAccountNumber", accountnumber);
        body.put("ifsc", ifsccode);
        body.put("bankName", Bankname);

        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details_higher_version_configured(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", "10.10.1");
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        query_params.put("solutionType", "psa");
        query_params.put("entityType", "INDIVIDUAL");
        query_params.put("leadId", leadid);

        body.put("leadId", leadid);
        body.put("onlyValidateBankDetails", true);
        body.put("bankAccountNumber", accountnumber);
        body.put("ifsc", ifsccode);
        body.put("bankName", Bankname);

        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details_wrong_ifsc(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        query_params.put("solutionType", "psa");
        query_params.put("entityType", "INDIVIDUAL");
        query_params.put("leadId", leadid);

        body.put("leadId", leadid);
        body.put("onlyValidateBankDetails", true);
        body.put("bankAccountNumber", accountnumber);
        body.put("ifsc", "hdfc000000");
        body.put("bankName", Bankname);

        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details_shorter_length_account_number(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        query_params.put("solutionType", "psa");
        query_params.put("entityType", "INDIVIDUAL");
        query_params.put("leadId", leadid);

        body.put("leadId", leadid);
        body.put("onlyValidateBankDetails", true);
        body.put("bankAccountNumber", "1");
        body.put("ifsc", ifsccode);
        body.put("bankName", Bankname);

        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details_higher_length_account_number(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        query_params.put("solutionType", "psa");
        query_params.put("entityType", "INDIVIDUAL");
        query_params.put("leadId", leadid);

        body.put("leadId", leadid);
        body.put("onlyValidateBankDetails", true);
        body.put("bankAccountNumber", "98435345324523453245234523452345123414323423432453453453532342424234");
        body.put("ifsc", ifsccode);
        body.put("bankName", Bankname);

        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 500);

    }
    @Test(priority = 6 , groups = {"Regression"} , description = "PSA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void post_bank_details_body_details_missing(){
        HashMap<String, String> headers = new HashMap<>();
        HashMap<String, Object> body = new HashMap<>();
        HashMap<String, String> query_params = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "9wrKO8kIBbblqxa8DR8DLh6MNtpmK/ioYaA7qPtsRaOXoLjqoMu8Qt90oJoekSAiqcBPzbiv0F/V6nXRoIBVX7BZCLgTXCOWTN589NKveXA+wIR5ey5xaZoQum63xTvC");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "JRjFOFeLlnO70LGqqlBG3Sj6eu+v53kX3Jrb7sSS6cehRe6s7X9IBPgWrT5YrMGS0iUi+Go2NIkQdadlcCNk5gTs79uNUL0g6Ms5LmtIIPblhEeQoD8zITK5MmZMFI/c");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", AgentToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.2-10.42.0-RC1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        query_params.put("solutionType", "psa");
        query_params.put("entityType", "INDIVIDUAL");
        query_params.put("leadId", leadid);


        PostBankDetailsAgentOnboarding obj = new PostBankDetailsAgentOnboarding(custId);
        Response respobj = middlewareServicesObject.post_bank_details_agent_onboarding(obj , headers , query_params,body);
        int StatusCode = respobj.statusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    
}
