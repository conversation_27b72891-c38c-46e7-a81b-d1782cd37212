package OCL.Individual.RegisterLead;

import Request.MerchantService.v1.leadManagement.RegisterLead;
import Services.MechantService.MiddlewareServices;
import com.auth0.jwt.algorithms.Algorithm;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.time.LocalDateTime;
import java.time.ZoneId;

public class FlowRegisterLead extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(FlowRegisterLead.class);

    public static  String mobileNo = "9953828631";
    public static String emailAdd = "<EMAIL>";
    public static String JWT = "";
    public static String LeadId = "";

    MiddlewareServices middlewareServices = new MiddlewareServices();

    public String generateJwtToken(String additional)
    
    {

        LocalDateTime localDateTime=LocalDateTime.now(ZoneId.of("GMT+05:30"));
        // LocalDate localDate = localDateTime.toLocalDate();
        LOGGER.info("Date is :"+localDateTime);
        String ts= localDateTime.toString();

        Algorithm buildAlgorithm = Algorithm.HMAC256("67256102-1ray-41w2-8t0z-5r672q13b806");
       String token= com.auth0.jwt.JWT.create().withIssuer("OE")
                .withClaim("clientId", "POS")
               .withClaim("timestamp",ts+"+05:30")
               .withClaim("additional", additional).sign(buildAlgorithm);

       LOGGER.info("JWT Token is : " + token);
        return token;
    }

    @Test(priority = 198,groups = {"Regression"},description = "Positive Register Lead Using Mobile Number")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void RegisterLeadPositiveMobile()
    {
        LOGGER.info(" Executing Method : RegisterLeadPositiveMobile ");
        RegisterLead v1RegLeadobj = new RegisterLead(P.TESTDATA.get("RegisterLead"));

        JWT = generateJwtToken(mobileNo);

        v1RegLeadobj.setHeader("additional",mobileNo);
        v1RegLeadobj.addParameter("mobile",mobileNo);
        v1RegLeadobj.addParameter("email",emailAdd);

        Response regLeadResp = middlewareServices.v1RegisterLead(v1RegLeadobj,"register_lead",JWT);

        String refId = regLeadResp.jsonPath().getString("refId");
        String leadID = regLeadResp.jsonPath().getString("leadId");

        LOGGER.info("This is RefId : " + refId);
        LOGGER.info("This is LeadId : " + leadID);

        LeadId = leadID;
        LOGGER.info("LeadId in Main has been set as : " + LeadId);


        int statusCode = regLeadResp.getStatusCode();
        Assert.assertEquals(statusCode,200);

        v1RegLeadobj.validateResponseAgainstJSONSchema("MerchantService/V1/leadManagement/registerLead/RegisterLeadSchema.json");
    }

    @Test(priority = 199,groups = {"Regression"},description = "Positive Register Lead With Email")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void RegisterLeadPositiveEmail()
    {
        LOGGER.info(" Executing Method : RegisterLeadPositiveEmail ");
        RegisterLead v1RegLeadobj = new RegisterLead(P.TESTDATA.get("RegisterLead"));

        JWT = generateJwtToken(emailAdd);

        v1RegLeadobj.setHeader("additional",emailAdd);
        v1RegLeadobj.addParameter("mobile",mobileNo);
        v1RegLeadobj.addParameter("email",emailAdd);

        Response regLeadResp = middlewareServices.v1RegisterLead(v1RegLeadobj,"register_lead",JWT);

        String refId = regLeadResp.jsonPath().getString("refId");
        String leadID = regLeadResp.jsonPath().getString("leadId");

        LOGGER.info("This is RefId : " + refId);
        LOGGER.info("This is LeadId : " + leadID);

        int statusCode = regLeadResp.getStatusCode();
        Assert.assertEquals(statusCode,200);

        v1RegLeadobj.validateResponseAgainstJSONSchema("MerchantService/V1/leadManagement/registerLead/RegisterLeadSchema.json");
    }

    @Test(priority = 200,groups = {"Regression"},description = "Positive Register Lead Edit Lead",dependsOnMethods = "RegisterLeadPositiveMobile")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void RegisterLeadPositiveEditLead()
    {
        LOGGER.info(" Executing Method : RegisterLeadPositiveEditLead ");
        RegisterLead v1RegLeadobj = new RegisterLead(P.TESTDATA.get("RegisterLeadEdit"));

        JWT = generateJwtToken("<EMAIL>");

        v1RegLeadobj.setHeader("additional",emailAdd);
        v1RegLeadobj.addParameter("mobile",mobileNo);
        v1RegLeadobj.addParameter("email",emailAdd);
        v1RegLeadobj.getProperties().setProperty("leadId",LeadId);

        Response regLeadResp = middlewareServices.v1RegisterLead(v1RegLeadobj,"register_lead",JWT);

        String refId = regLeadResp.jsonPath().getString("refId");
        String leadID = regLeadResp.jsonPath().getString("leadId");

        LOGGER.info("This is RefId : " + refId);
        LOGGER.info("This is LeadId : " + leadID);

        int statusCode = regLeadResp.getStatusCode();
        Assert.assertEquals(statusCode,200);

        Assert.assertEquals(leadID,LeadId);

        v1RegLeadobj.validateResponseAgainstJSONSchema("MerchantService/V1/leadManagement/registerLead/RegisterLeadSchema.json");
    }

    @Test(priority = 201,groups = {"Regression"},description = "Positive Register Lead Only With Mobile Number")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void RegisterLeadPositiveNoEmail()
    {
        LOGGER.info(" Executing Method : RegisterLeadPositiveNoEmail ");
        RegisterLead v1RegLeadobj = new RegisterLead(P.TESTDATA.get("RegisterLead"));

        JWT = generateJwtToken(mobileNo);

        v1RegLeadobj.setHeader("additional",mobileNo);
        v1RegLeadobj.addParameter("mobile",mobileNo);

        Response regLeadResp = middlewareServices.v1RegisterLead(v1RegLeadobj,"register_lead",JWT);

        String refId = regLeadResp.jsonPath().getString("refId");
        String leadID = regLeadResp.jsonPath().getString("leadId");

        LOGGER.info("This is RefId : " + refId);
        LOGGER.info("This is LeadId : " + leadID);

        int statusCode = regLeadResp.getStatusCode();
        Assert.assertEquals(statusCode,200);

        v1RegLeadobj.validateResponseAgainstJSONSchema("MerchantService/V1/leadManagement/registerLead/RegisterLeadSchema.json");
    }

    @Test(priority = 202,groups = {"Regression"},description = "Positive Register Lead With Only Email")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void RegisterLeadPositiveNoMobile()
    {
        LOGGER.info(" Executing Method : RegisterLeadPositiveNoMobile ");
        RegisterLead v1RegLeadobj = new RegisterLead(P.TESTDATA.get("RegisterLead"));

        JWT = generateJwtToken(emailAdd);

        v1RegLeadobj.setHeader("additional",emailAdd);
        v1RegLeadobj.addParameter("email",emailAdd);

        Response regLeadResp = middlewareServices.v1RegisterLead(v1RegLeadobj,"register_lead",JWT);

        String refId = regLeadResp.jsonPath().getString("refId");
        String leadID = regLeadResp.jsonPath().getString("leadId");

        LOGGER.info("This is RefId : " + refId);
        LOGGER.info("This is LeadId : " + leadID);

        int statusCode = regLeadResp.getStatusCode();
        Assert.assertEquals(statusCode,200);

        v1RegLeadobj.validateResponseAgainstJSONSchema("MerchantService/V1/leadManagement/registerLead/RegisterLeadSchema.json");
    }

    @Test(priority = 203,groups = {"Regression"},description = "When Mobile provided in JWT is different from Mobile provided in Params")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void RegisterLeadDiffAdditionalNumber()
    {
        LOGGER.info(" Executing Method : RegisterLeadDiffAdditionalNumber ");
        RegisterLead v1RegLeadobj = new RegisterLead(P.TESTDATA.get("RegisterLead"));

        JWT = generateJwtToken("7011653794");

        v1RegLeadobj.setHeader("additional",mobileNo);
        v1RegLeadobj.addParameter("mobile",mobileNo);
        v1RegLeadobj.addParameter("email",emailAdd);

        Response regLeadResp = middlewareServices.v1RegisterLead(v1RegLeadobj,"register_lead",JWT);

        int statusCode = regLeadResp.getStatusCode();
        Assert.assertEquals(statusCode,401);
    }

    @Test(priority = 204,groups = {"Regression"},description = "When Email provided in JWT is different from Email provided in Params")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void RegisterLeadDiffAdditionalEmail()
    {
        LOGGER.info(" Executing Method : RegisterLeadDiffAdditionalEmail ");
        RegisterLead v1RegLeadobj = new RegisterLead(P.TESTDATA.get("RegisterLead"));

        JWT = generateJwtToken("<EMAIL>");

        v1RegLeadobj.setHeader("additional",emailAdd);
        v1RegLeadobj.addParameter("mobile",mobileNo);
        v1RegLeadobj.addParameter("email",emailAdd);

        Response regLeadResp = middlewareServices.v1RegisterLead(v1RegLeadobj,"register_lead",JWT);

        int statusCode = regLeadResp.getStatusCode();
        Assert.assertEquals(statusCode,401);
    }

    @Test(priority = 205,groups = {"Regression"},description = "Mobile number provided in JWT is different from mobile number provided in Header")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void RegisterLeadDiffMobileInAdditional()
    {
        LOGGER.info(" Executing Method : RegisterLeadDiffMobileInAdditional ");
        RegisterLead v1RegLeadobj = new RegisterLead(P.TESTDATA.get("RegisterLead"));

        JWT = generateJwtToken(mobileNo);

        v1RegLeadobj.setHeader("additional","7011653794");
        v1RegLeadobj.addParameter("mobile",mobileNo);
        v1RegLeadobj.addParameter("email",emailAdd);

        Response regLeadResp = middlewareServices.v1RegisterLead(v1RegLeadobj,"register_lead",JWT);

        int statusCode = regLeadResp.getStatusCode();
        Assert.assertEquals(statusCode,401);

    }

    @Test(priority = 206,groups = {"Regression"},description = "Email provided in JWT is different from Email provided in Header")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void RegisterLeadDiffEmailInAdditional()
    {
        LOGGER.info(" Executing Method : RegisterLeadDiffEmailInAdditional ");
        RegisterLead v1RegLeadobj = new RegisterLead(P.TESTDATA.get("RegisterLead"));

        JWT = generateJwtToken(emailAdd);

        v1RegLeadobj.setHeader("additional","<EMAIL>");
        v1RegLeadobj.addParameter("mobile",mobileNo);
        v1RegLeadobj.addParameter("email",emailAdd);

        Response regLeadResp = middlewareServices.v1RegisterLead(v1RegLeadobj,"register_lead",JWT);

        int statusCode = regLeadResp.getStatusCode();
        Assert.assertEquals(statusCode,401);

    }

    @Test(priority = 207,groups = {"Regression"},description = "Email provided in Params is different from Email provided in JWT and Header")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void RegisterLeadDiffEmailInParam()
    {
        LOGGER.info(" Executing Method : RegisterLeadDiffEmailInParam ");
        RegisterLead v1RegLeadobj = new RegisterLead(P.TESTDATA.get("RegisterLead"));

        JWT = generateJwtToken(emailAdd);

        v1RegLeadobj.setHeader("additional",emailAdd);
        v1RegLeadobj.addParameter("mobile",mobileNo);
        v1RegLeadobj.addParameter("email","<EMAIL>");

        Response regLeadResp = middlewareServices.v1RegisterLead(v1RegLeadobj,"register_lead",JWT);

        int statusCode = regLeadResp.getStatusCode();
        Assert.assertEquals(statusCode,400);

    }

    @Test(priority = 208,groups = {"Regression"},description = "Mobile number provided in Params is different from number provided in JWT and Header")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void RegisterLeadDiffMobileInParam()
    {
        LOGGER.info(" Executing Method : RegisterLeadDiffMobileInParam ");
        RegisterLead v1RegLeadobj = new RegisterLead(P.TESTDATA.get("RegisterLead"));

        JWT = generateJwtToken(mobileNo);

        v1RegLeadobj.setHeader("additional",mobileNo);
        v1RegLeadobj.addParameter("mobile","7011653794");
        v1RegLeadobj.addParameter("email",emailAdd);

        Response regLeadResp = middlewareServices.v1RegisterLead(v1RegLeadobj,"register_lead",JWT);

        int statusCode = regLeadResp.getStatusCode();
        Assert.assertEquals(statusCode,400);

    }

    @Test(priority = 209,groups = {"Regression"},description = "JWT is generated from Mobile Number and Email is provided on Email")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void RegisterLeadDiffJWTMobile()
    {
        LOGGER.info(" Executing Method : RegisterLeadDiffJWTMobile ");
        RegisterLead v1RegLeadobj = new RegisterLead(P.TESTDATA.get("RegisterLead"));

        JWT = generateJwtToken(mobileNo);

        v1RegLeadobj.setHeader("additional",mobileNo);
        v1RegLeadobj.addParameter("email",emailAdd);

        Response regLeadResp = middlewareServices.v1RegisterLead(v1RegLeadobj,"register_lead",JWT);

        int statusCode = regLeadResp.getStatusCode();
        Assert.assertEquals(statusCode,400);

    }

    @Test(priority = 210,groups = {"Regression"},description = "JWT is generated from Email and Mobile Number is provided on Email")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void RegisterLeadDiffJWTEmail()
    {
        LOGGER.info(" Executing Method : RegisterLeadDiffJWTEmail ");
        RegisterLead v1RegLeadobj = new RegisterLead(P.TESTDATA.get("RegisterLead"));

        JWT = generateJwtToken(emailAdd);

        v1RegLeadobj.setHeader("additional",emailAdd);
        v1RegLeadobj.addParameter("email",mobileNo);

        Response regLeadResp = middlewareServices.v1RegisterLead(v1RegLeadobj,"register_lead",JWT);

        int statusCode = regLeadResp.getStatusCode();
        Assert.assertEquals(statusCode,400);

    }

}
