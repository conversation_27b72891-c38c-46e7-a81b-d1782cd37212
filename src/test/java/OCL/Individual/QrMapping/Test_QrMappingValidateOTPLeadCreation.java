package OCL.Individual.QrMapping;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.MerchantService.v3.QRMapping.QrMappingSendOtp;
import Request.MerchantService.v3.QRMapping.QrMappingValidateOTPLeadCreation;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class Test_QrMappingValidateOTPLeadCreation extends BaseMethod {


	public Response response;
	public MiddlewareServices services;
	public String sessionToken;

	public Map<String,String> headers;
	public Map<String, String> body;
	public Map<String,String> params;

	QrMappingSendOtp sendOtp;
	QrMappingValidateOTPLeadCreation createLead;
	String state;

	@BeforeClass
	public void setToken() {

		sessionToken=AgentSessionToken("7771216290", "paytm@123");

	}

	@Test(priority = 1)
	public void verifyStatusCodeAndResponseMessageWhenDataIsCorrect() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");

        response=services.QrValidateOtp(createLead, body, headers, params);

	    Assert.assertTrue(response.prettyPrint().contains("Lead successfully created"));
        Assert.assertEquals(response.getStatusCode(), 200, "Status code is as expected");

  	}

	@Test(priority = 2)
	public void verifyNewLeadIsCreatedIfLeadLeadIsOnLeadCreatedStage() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");

        response=services.QrValidateOtp(createLead, body, headers, params);
        String lead1=response.path("leadId");

		body=new HashMap<>();

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");

        response=services.QrValidateOtp(createLead, body, headers, params);
        String lead2=response.path("leadId");

        Assert.assertNotEquals(lead1, lead2);

 }

	@Test(priority = 3)
	public void verifyResponseForInvalidSolutionTypeinParams() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		params=new HashMap<>();

		body=new HashMap<>();
	    params.put("solutionType", "xyz");
        params.put("entityType", "INDIVIDUAL");
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");

        response=services.QrValidateOtp(createLead, body, headers, params);
        Assert.assertEquals(response.getStatusCode(), 500);

 	}

	@Test(priority = 4)
	public void verifyResponseForInvalidEntityTypeinParams() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
 		params=new HashMap<>();

		body=new HashMap<>();
	    params.put("solutionType", "qr_mapping");
        params.put("entityType", "Select * from entity limit 1");
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");

        response=services.QrValidateOtp(createLead, body, headers, params);

	    Assert.assertTrue(response.prettyPrint().contains("Entity type in request empty or not supported"));
        Assert.assertEquals(response.getStatusCode(), 400, "Status code is as expected");

 	}

	@Test(priority = 5)
	public void verifyStatusCodeAndResponseifOldStateTokenIsUsed() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");

        response=services.QrValidateOtp(createLead, body, headers, params);
        createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");

        response=services.QrValidateOtp(createLead, body, headers, params);
	    Assert.assertTrue(response.prettyPrint().contains("Dear user, please contact support at"));

   	}

	@Test(priority = 6)
	public void verifyStatusCodeAndResponseWhenuserTypeIsNotPassedInRequest() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
//		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");

        response=services.QrValidateOtp(createLead, body, headers, params);

	    Assert.assertTrue(response.prettyPrint().contains("Failed to validate OTP"));
        Assert.assertEquals(response.getStatusCode(), 500, "Status code is as expected");

 	}

	@Test(priority = 7)
	public void verifyStatusCodeAndResponseWhenStateIsNotPassedInRequest() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
//        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");

        response=services.QrValidateOtp(createLead, body, headers, params);

	    Assert.assertTrue(response.prettyPrint().contains("Server error"));

 	}

	@Test(priority = 8)
	public void verifyStatusCodeAndResponseWhenInvalidOtpPassedInRequest() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "8888");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");

        response=services.QrValidateOtp(createLead, body, headers, params);

	    Assert.assertTrue(response.prettyPrint().contains("Invalid Paytm Code entered"));
        Assert.assertEquals(response.getStatusCode(), 200, "Status code is as expected");

 	}


	@Test(priority = 9)
	public void verifyStatusCodeAndResponseWhenMobileIsNotPassedInRequest() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
//        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");

        response=services.QrValidateOtp(createLead, body, headers, params);

	    Assert.assertTrue(response.prettyPrint().contains("Lead successfully created")); // mobile is not a mandatory field
        Assert.assertEquals(response.getStatusCode(), 200, "Status code is as expected");

 	}

	@Test(priority = 10)
	public void verifyStatusCodeAndResponseWhenKYCFlagIsNotPassedInRequest() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "7771110999");
//        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");

        response=services.QrValidateOtp(createLead, body, headers, params);

	    Assert.assertTrue(response.prettyPrint().contains("Lead successfully created"));
        Assert.assertEquals(response.getStatusCode(), 200, "Status code is as expected");

 	}

	@Test(priority = 11)
	public void verifyStatusCodeAndResponseWhenSolutionSubTypePassedIsInvalidInRequest() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "xyz");
        body.put("mid", "HySHnd27878673398759");

        response=services.QrValidateOtp(createLead, body, headers, params);

	    Assert.assertTrue(response.prettyPrint().contains("Invalid solution sub type"));
	    Assert.assertEquals(response.getStatusCode(), 401, "Status code is as expected");

 	}

	@Test(priority = 12)
	public void verifyStatusCodeAndResponseWhenMIDNotPassednRequest() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
//      body.put("mid", "");

        response=services.QrValidateOtp(createLead, body, headers, params);
        Assert.assertEquals(response.getStatusCode(), 400, "Status code is as expected");

 	}

	@Test(priority = 13)
	public void verifyStatusCodeAndResponseWhenTokenNotPassednRequest() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");

		headers.put("session_token", "");
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");

        Assert.assertEquals(response.getStatusCode(), 401, "Status code is as expected");

 	}

	@Test(priority = 14)
	public void verifyStatusCodeAndResponseWhenTokenPassedIsForUserWithNoPermission() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		sessionToken=AgentSessionToken("8908908901", "paytm@123");

		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");

        Assert.assertEquals(response.getStatusCode(), 401, "Status code is as expected");

 	}

	@Test(priority = 15)
	public void verifyStatusCodeAndResponseWhenVersionIsMissingInRequest() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");

		headers.put("session_token", sessionToken);
//		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "7771110999");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");

	    Assert.assertTrue(response.prettyPrint().contains("version is empty in header"));

 	}




}