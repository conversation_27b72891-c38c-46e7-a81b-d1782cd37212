package OCL.Individual.QrMapping;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.MerchantService.v3.QRMapping.QrMappingSendOtp;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class Test_QRMappingSendOTP extends BaseMethod{

	public Response response;
	public MiddlewareServices services;
	public String sessionToken;

	public Map<String,String> headers;
	public Map<String, String> body;
	public Map<String,String> params;

	QrMappingSendOtp obj;

	@BeforeClass
	public void setToken() {

		sessionToken=AgentSessionToken("7771216290", "paytm@123");

	}

	@Test(priority = 1)
	public void verifyStatusCodeAndResponseMessageWhenDataIsCorrect() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    obj=new QrMappingSendOtp();

 	    response=services.QrSendOtp(obj, body, headers, params);
 	    Assert.assertTrue(response.prettyPrint().contains("state"));
        Assert.assertEquals(response.getStatusCode(), 200, "Status code is as expected");
 	}

	@Test(priority = 2)
	public void verifyResponseForInvalidMobileNumber() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "9999888111111");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    obj=new QrMappingSendOtp();

 	    response=services.QrSendOtp(obj, body, headers, params);
 	    Assert.assertTrue(response.asString().contains("Please provide a Paytm registered mobile number"));
        Assert.assertEquals(response.getStatusCode(), 400, "Status code is not as expected");

	}


	@Test(priority = 3)
	public void verifyResponseForInvalidSolutionType() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    obj=new QrMappingSendOtp();

 	    response=services.QrSendOtp(obj, body, headers, params);
        Assert.assertEquals(response.getStatusCode(), 500);

	}

	@Test(priority = 4)
	public void verifyEntityTypeIsNotAMandtoryParam() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    obj=new QrMappingSendOtp();

 	    response=services.QrSendOtp(obj, body, headers, params);
        Assert.assertEquals(response.getStatusCode(), 200); // entity type is not a mandatory param for this request

	}

	@Test(priority = 5)
	public void verifyResponseForInvalidUserType() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "abc");
 	   	body.put("call", "false");

 	    obj=new QrMappingSendOtp();

 	    response=services.QrSendOtp(obj, body, headers, params);
 	    Assert.assertTrue(response.asString().contains("Invalid action"));

	}

	@Test(priority = 6)
	public void verifyResponseWhenCallParamIsMissingInRequestBody() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
// 	   	body.put("call", "false");

 	    obj=new QrMappingSendOtp();

 	    response=services.QrSendOtp(obj, body, headers, params);
        Assert.assertEquals(response.getStatusCode(), 400);

	}


	@Test(priority = 7)
	public void verifyResponseWhenSessionTokenIsMissing() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
//		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    obj=new QrMappingSendOtp();

 	    response=services.QrSendOtp(obj, body, headers, params);

        Assert.assertEquals(response.getStatusCode(), 401, "Status code is  as expected");

	}


	@Test(priority = 8)
	public void verifyResponseWhenAppversionIsMissing() {

		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
//		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "7771110999");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    obj=new QrMappingSendOtp();

 	    response=services.QrSendOtp(obj, body, headers, params);

 	    Assert.assertTrue(response.asString().contains("version is empty in header"));

	}




}