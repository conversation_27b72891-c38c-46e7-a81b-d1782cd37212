package OCL.Individual.QrMapping;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.MerchantService.v3.QRMapping.QRMappingFetchEligibleBanks;
import Request.MerchantService.v3.QRMapping.QrMappingSendOtp;
import Request.MerchantService.v3.QRMapping.QrMappingValidateOTPLeadCreation;
import Services.MechantService.MiddlewareServices;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;

public class TestQRMappingFetchEligibleBanks extends BaseMethod {

	public Response response;
	public MiddlewareServices services;
	public String sessionToken;

	public Map<String,String> headers;
	public Map<String, String> body;
	public Map<String,String> params;

	QrMappingSendOtp sendOtp;
	QrMappingValidateOTPLeadCreation createLead;
	String state;
	String leadID="";


	@BeforeClass
	public void setToken() throws Exception {

		sessionToken=AgentSessionToken("**********", "paytm@123");
        establishConnectiontoServer(sessionToken,5);

	}

	@Test(priority = 1)
	public void verifyStatusWhenSuccessIsRecieved() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "**********");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "**********");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");
        response=services.QrValidateOtp(createLead, body, headers, params);
        leadID=response.path("leadId").toString();

		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("mid","HySHnd27878673398759");
        params.put("leadId",leadID);
        params.put("existingMerchant","true");
        QRMappingFetchEligibleBanks eligibleBanks=new QRMappingFetchEligibleBanks();
        services.fetchEligibleBanks(eligibleBanks, params, headers).then().assertThat().statusCode(200);

	}

	@Test(priority = 2)
	public void verifyPayTMIsNotRecievedInELigibleBanksWhenMerchantIsNotExistingMerchant() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "**********");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "**********");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "OHDpzh00093359209989");
        response=services.QrValidateOtp(createLead, body, headers, params);
        leadID=response.path("leadId").toString();

		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("mid","OHDpzh00093359209989");
        params.put("leadId",leadID);
        params.put("existingMerchant","false");
        QRMappingFetchEligibleBanks eligibleBanks=new QRMappingFetchEligibleBanks();
        response=services.fetchEligibleBanks(eligibleBanks, params, headers);
        Assert.assertFalse(response.asString().contains("PAYTM BANK"));
	}

	@Test(priority = 3)
	public void verifyPaytmBankIsRecievedInELigibleBanksWhenMerchantIsExistingMerchant() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "**********");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "**********");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");
        response=services.QrValidateOtp(createLead, body, headers, params);
        leadID=response.path("leadId").toString();

		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("mid","HySHnd27878673398759");
        params.put("leadId",leadID);
        params.put("existingMerchant","true");
        QRMappingFetchEligibleBanks eligibleBanks=new QRMappingFetchEligibleBanks();
        response=services.fetchEligibleBanks(eligibleBanks, params, headers);
        JsonPath json=response.jsonPath();
        List<String> listOfBanks=json.getList("eligibility.bankName");
        Assert.assertTrue(listOfBanks.size()>1);
        Assert.assertTrue(response.asString().contains("PAYTM BANK"));

	}

	@Test(priority = 4)
	public void VerifyOnlyPaytmBankIsRecievedWhenReKycIsNotDoneForTheMerchant() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "**********");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "**********");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "DcFcoJ97348642540712");
        response=services.QrValidateOtp(createLead, body, headers, params);
        leadID=response.path("leadId").toString();

		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("mid","DcFcoJ97348642540712");
        params.put("leadId",leadID);
        params.put("existingMerchant","true");
        QRMappingFetchEligibleBanks eligibleBanks=new QRMappingFetchEligibleBanks();
        response=services.fetchEligibleBanks(eligibleBanks, params, headers);
        JsonPath json=response.jsonPath();
        List<String> listOfBanks=json.getList("eligibility.bankName");
        Assert.assertEquals(listOfBanks.size(), 1);
        Assert.assertEquals(listOfBanks.get(0), "PAYTM BANK");
	}


	@Test(priority = 5)
	public void verifyThatBanksNotEligibleAreReturnedInListAlongWithReason() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "**********");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "**********");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "ecmVFI00693998030228");
        response=services.QrValidateOtp(createLead, body, headers, params);
        response.prettyPrint();
        leadID=response.path("leadId").toString();

		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("mid","ecmVFI00693998030228");
        params.put("leadId",leadID);
        params.put("existingMerchant","false");
        QRMappingFetchEligibleBanks eligibleBanks=new QRMappingFetchEligibleBanks();
        response=services.fetchEligibleBanks(eligibleBanks, params, headers);
        Assert.assertEquals(response.path("eligibility[1].status"), "NOT_ELIGIBLE");
        Assert.assertEquals(response.path("eligibility[1].missingParams[0]"), "DATE_OF_BIRTH");

	}

	@Test(priority = 6)
	public void verifyAccessIsDeniedIfOriginIsOtherThenPaytm() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "**********");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "**********");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "ecmVFI00693998030228");
        response=services.QrValidateOtp(createLead, body, headers, params);
        response.prettyPrint();
        leadID=response.path("leadId").toString();

		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("origin" , "https://ggapp-frontend-qa.xyz.com");

        params.put("mid","ecmVFI00693998030228");
        params.put("leadId",leadID);
        params.put("existingMerchant","false");
        QRMappingFetchEligibleBanks eligibleBanks=new QRMappingFetchEligibleBanks();
        response=services.fetchEligibleBanks(eligibleBanks, params, headers);
        Assert.assertEquals(response.statusCode(), 403);

	}

	@Test(priority = 7)
	public void verifyErrorCodeWhenValidSessionTokenIsNotPassed() {


		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "**********");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "**********");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "ecmVFI00693998030228");
        response=services.QrValidateOtp(createLead, body, headers, params);
   response.prettyPrint();
        leadID=response.path("leadId").toString();

		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", "abc");
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");


        params.put("mid","ecmVFI00693998030228");
        params.put("leadId",leadID);
        params.put("existingMerchant","false");
        QRMappingFetchEligibleBanks eligibleBanks=new QRMappingFetchEligibleBanks();
        response=services.fetchEligibleBanks(eligibleBanks, params, headers);
        Assert.assertEquals(response.statusCode(), 403);

	}

	@Test(priority = 8)
	public void verifyErrorCodeWhenSessionTokenIsNotPassed() {


		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "**********");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "**********");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "ecmVFI00693998030228");
        response=services.QrValidateOtp(createLead, body, headers, params);
        response.prettyPrint();
        leadID=response.path("leadId").toString();

		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
//		headers.put("session_token", "abc");
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");


        params.put("mid","ecmVFI00693998030228");
        params.put("leadId",leadID);
        params.put("existingMerchant","false");
        QRMappingFetchEligibleBanks eligibleBanks=new QRMappingFetchEligibleBanks();
        response=services.fetchEligibleBanks(eligibleBanks, params, headers);
        Assert.assertEquals(response.statusCode(), 401);

	}


	@Test(priority = 9)
	public void verifyErrorCodeWhenAppVersionIsNotPassed() {


		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "**********");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "**********");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "ecmVFI00693998030228");
        response=services.QrValidateOtp(createLead, body, headers, params);
        response.prettyPrint();
        leadID=response.path("leadId").toString();

		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
//		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");


        params.put("mid","ecmVFI00693998030228");
        params.put("leadId",leadID);
        params.put("existingMerchant","false");
        QRMappingFetchEligibleBanks eligibleBanks=new QRMappingFetchEligibleBanks();
        response=services.fetchEligibleBanks(eligibleBanks, params, headers);
        Assert.assertEquals(response.path("message").toString(), "version is empty in header");

	}


	@Test(priority = 10)
	public void verifyStatusWhenMIDIsNotPassedInRequest() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "**********");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "**********");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");
        response=services.QrValidateOtp(createLead, body, headers, params);
        leadID=response.path("leadId").toString();

		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

//        params.put("mid","HySHnd27878673398759");
        params.put("leadId",leadID);
        params.put("existingMerchant","true");
        QRMappingFetchEligibleBanks eligibleBanks=new QRMappingFetchEligibleBanks();
        services.fetchEligibleBanks(eligibleBanks, params, headers).then().assertThat().statusCode(400);

	}

	@Test(priority = 11)
	public void verifyStatusWhenLeadIDIsNotPassedInRequest() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "**********");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "**********");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");
        response=services.QrValidateOtp(createLead, body, headers, params);
        leadID=response.path("leadId").toString();

		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("mid","HySHnd27878673398759");
//        params.put("leadId",leadID);
        params.put("existingMerchant","true");
        QRMappingFetchEligibleBanks eligibleBanks=new QRMappingFetchEligibleBanks();
        services.fetchEligibleBanks(eligibleBanks, params, headers).then().assertThat().statusCode(400);

	}

	@Test(priority = 12)
	public void verifyResponseISRecievedEvenIfExistingMerchantFlagIsNotPassedInRequest() {
		headers=new HashMap<>();
		body=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("entityType", "INDIVIDUAL");

 	   	body.put("mobile", "**********");
 	   	body.put("userType", "qr_code_mapping");
 	   	body.put("call", "false");

 	    sendOtp=new QrMappingSendOtp();

 	    response=services.QrSendOtp(sendOtp, body, headers, params);
 	    state=response.path("state");

 	    createLead=new QrMappingValidateOTPLeadCreation();
		body=new HashMap<>();
		body.put("userType", "qr_code_mapping");
        body.put("state", state);
        body.put("otp", "888888");
        body.put("mobile", "**********");
        body.put("individaulMerchantKyc", "true");
        body.put("solutionSubType", "PAYMENTS_QR");
        body.put("mid", "HySHnd27878673398759");
        response=services.QrValidateOtp(createLead, body, headers, params);
        leadID=response.path("leadId").toString();

		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-***************");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("mid","HySHnd27878673398759");
        params.put("leadId",leadID);
//        params.put("existingMerchant","true");
        QRMappingFetchEligibleBanks eligibleBanks=new QRMappingFetchEligibleBanks();
        services.fetchEligibleBanks(eligibleBanks, params, headers).then().assertThat().statusCode(200);

	}



}