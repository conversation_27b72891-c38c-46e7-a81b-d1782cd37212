package OCL.Individual.QrMapping;

import Request.MerchantService.v3.QRMapping.QRMappingFetchPosDetails;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class TestQrMappingFetchPosDetails extends BaseMethod {

    public Response response;
    public MiddlewareServices services;
    public String sessionToken;

    public Map<String,String> headers;
    public Map<String, String> body;
    public Map<String,String> params;



    @BeforeClass
    public void setToken() {
        try {
            sessionToken = AgentSessionToken("7771216290", "paytm@123");
            if (sessionToken == null || sessionToken.isEmpty()) {
                throw new RuntimeException("Session token is null or empty");
            }
            log.info("Successfully obtained session token: " + sessionToken);
        } catch (Exception e) {
            log.error("Failed to get session token: " + e.getMessage(), e);
            throw new RuntimeException("Failed to initialize test: " + e.getMessage(), e);
        }
    }

    @Test(priority = 1)
    public void fetchPosDetailsWithoutLinking() {
        headers=new HashMap<>();
        body=new HashMap<>();
        services=new MiddlewareServices();
        params=new HashMap<>();
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("session_token", sessionToken);
        headers.put("version", "7.2.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("merchantRequestType", "MAP_QR_WITHOUT_LINKING");
        params.put("mid", "juJhhX90362275560814");

        services=new MiddlewareServices();
        QRMappingFetchPosDetails qrMappingFetchPosDetails=new QRMappingFetchPosDetails();
        services.qrMappingFetchPosDetails(qrMappingFetchPosDetails, params, headers).then().assertThat().statusCode(200);

    }

    @Test(priority = 2)
    public void fetchPosDetailsWithoutLinkingWithoutMid() {
        headers=new HashMap<>();
        body=new HashMap<>();
        services=new MiddlewareServices();
        params=new HashMap<>();
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("session_token", sessionToken);
        headers.put("version", "7.2.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("merchantRequestType", "MAP_QR_WITHOUT_LINKING");


        services=new MiddlewareServices();
        QRMappingFetchPosDetails qrMappingFetchPosDetails=new QRMappingFetchPosDetails();
        services.qrMappingFetchPosDetails(qrMappingFetchPosDetails, params, headers).then().assertThat().statusCode(400);

    }


    @Test(priority = 3)
    public void fetchPosDetailsWithoutLinkingWithoutSolutiontype() {
        headers=new HashMap<>();
        body=new HashMap<>();
        services=new MiddlewareServices();
        params=new HashMap<>();
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("session_token", sessionToken);
        headers.put("version", "7.2.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("merchantRequestType", "MAP_QR_WITHOUT_LINKING");
        params.put("mid", "juJhhX90362275560814");

        services=new MiddlewareServices();
        QRMappingFetchPosDetails qrMappingFetchPosDetails=new QRMappingFetchPosDetails();
        services.qrMappingFetchPosDetails(qrMappingFetchPosDetails, params, headers).then().assertThat().statusCode(200);

    }

    @Test(priority = 4)
    public void fetchPosDetailsWithoutMerchantRequestType() {
        headers=new HashMap<>();
        body=new HashMap<>();
        services=new MiddlewareServices();
        params=new HashMap<>();
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("session_token", sessionToken);
        headers.put("version", "7.2.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("mid", "juJhhX90362275560814");

        services=new MiddlewareServices();
        QRMappingFetchPosDetails qrMappingFetchPosDetails=new QRMappingFetchPosDetails();
        services.qrMappingFetchPosDetails(qrMappingFetchPosDetails, params, headers).then().assertThat().statusCode(200);

    }

    @Test(priority = 5)
    public void fetchPosDetailsWithLinkingAnyDevice() {
        headers=new HashMap<>();
        body=new HashMap<>();
        services=new MiddlewareServices();
        params=new HashMap<>();
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("session_token", sessionToken);
        headers.put("version", "7.2.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("merchantRequestType", "MAP_QR_WITH_LINKING");
        params.put("mid", "juJhhX90362275560814");

        services=new MiddlewareServices();
        QRMappingFetchPosDetails qrMappingFetchPosDetails=new QRMappingFetchPosDetails();
        services.qrMappingFetchPosDetails(qrMappingFetchPosDetails, params, headers).then().assertThat().statusCode(200);

    }

    @Test(priority = 6)
    public void fetchPosDetailsWithLinkingAnyDeviceWithoutMid() {
        headers=new HashMap<>();
        body=new HashMap<>();
        services=new MiddlewareServices();
        params=new HashMap<>();
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("session_token", sessionToken);
        headers.put("version", "7.2.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("merchantRequestType", "MAP_QR_WITH_LINKING");


        services=new MiddlewareServices();
        QRMappingFetchPosDetails qrMappingFetchPosDetails=new QRMappingFetchPosDetails();
        services.qrMappingFetchPosDetails(qrMappingFetchPosDetails, params, headers).then().assertThat().statusCode(200);

    }

    @Test(priority = 7)
    public void fetchPosDetailsWithLinkingAnyDeviceWithoutSolutionType() {
        headers=new HashMap<>();
        body=new HashMap<>();
        services=new MiddlewareServices();
        params=new HashMap<>();
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("session_token", sessionToken);
        headers.put("version", "7.2.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("merchantRequestType", "MAP_QR_WITH_LINKING");
        params.put("mid", "juJhhX90362275560814");

        services=new MiddlewareServices();
        QRMappingFetchPosDetails qrMappingFetchPosDetails=new QRMappingFetchPosDetails();
        services.qrMappingFetchPosDetails(qrMappingFetchPosDetails, params, headers).then().assertThat().statusCode(200);

    }

    @Test(priority = 8)
    public void fetchPosDetailsWithSBDeployment() {
        headers=new HashMap<>();
        body=new HashMap<>();
        services=new MiddlewareServices();
        params=new HashMap<>();
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("session_token", sessionToken);
        headers.put("version", "7.2.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("merchantRequestType", "MAP_QR_SB_DEPLOYMENT");
        params.put("mid", "juJhhX90362275560814");

        services=new MiddlewareServices();
        QRMappingFetchPosDetails qrMappingFetchPosDetails=new QRMappingFetchPosDetails();
        services.qrMappingFetchPosDetails(qrMappingFetchPosDetails, params, headers).then().assertThat().statusCode(200);

    }

    @Test(priority = 9)
    public void fetchPosDetailsWithMechantRequestTypeSBDeploymentWithoutMid() {
        headers=new HashMap<>();
        body=new HashMap<>();
        services=new MiddlewareServices();
        params=new HashMap<>();
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("session_token", sessionToken);
        headers.put("version", "7.2.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("solutionType", "qr_mapping");
        params.put("merchantRequestType", "MAP_QR_SB_DEPLOYMENT");

        services=new MiddlewareServices();
        QRMappingFetchPosDetails qrMappingFetchPosDetails=new QRMappingFetchPosDetails();
        services.qrMappingFetchPosDetails(qrMappingFetchPosDetails, params, headers).then().assertThat().statusCode(200);

    }

    @Test(priority = 10)
    public void fetchPosDetailsWithMechantRequestTypeSBDeploymentWithoutsolutionType() {
        headers=new HashMap<>();
        body=new HashMap<>();
        services=new MiddlewareServices();
        params=new HashMap<>();
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("session_token", sessionToken);
        headers.put("version", "7.2.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("merchantRequestType", "MAP_QR_SB_DEPLOYMENT");
        params.put("mid", "juJhhX90362275560814");
        services=new MiddlewareServices();
        QRMappingFetchPosDetails qrMappingFetchPosDetails=new QRMappingFetchPosDetails();
        services.qrMappingFetchPosDetails(qrMappingFetchPosDetails, params, headers).then().assertThat().statusCode(200);

    }
}