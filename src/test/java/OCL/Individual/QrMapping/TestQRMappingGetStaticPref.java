package OCL.Individual.QrMapping;

import java.util.HashMap;
import java.util.Map;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;

import Request.MerchantService.v3.QRMapping.QRMappingGetStaticPref;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class TestQRMappingGetStaticPref extends BaseMethod  {
	public Response response;
	public MiddlewareServices services;
	public String sessionToken;
	public Map<String,String> headers;
	public Map<String,String> params;
	private QRMappingGetStaticPref staticPref;


	@BeforeClass
	public void setToken() {

		sessionToken=AgentSessionToken("7771216290", "paytm@123");

	}

	@Test(priority = 1)
	public void verifyStatusWhenSuccessIsRecieved() {
		staticPref=new QRMappingGetStaticPref();
		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("mid","HySHnd27878673398759");
        params.put("solutionType","qr_mapping");
        params.put("solutionTypeLevel2","PAYMENTS_QR");
        params.put("prefs","RE_KYC_ENABLED");

        services.fetchStaticPrefs(staticPref, params, headers).then().assertThat().statusCode(200);

	}

	@Test(priority = 2)
	public void verifyStatusWhenRekycIsNotEnabledOnMid() {
		staticPref=new QRMappingGetStaticPref();
		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("mid","HySHnd27878673398759");
        params.put("solutionType","qr_mapping");
        params.put("solutionTypeLevel2","PAYMENTS_QR");
        params.put("prefs","DcFcoJ97348642540712");

        String responseJson=services.fetchStaticPrefs(staticPref, params, headers).asString();
        Assert.assertFalse(responseJson.contains("RE_KYC_ENABLED"));
	}

	@Test(priority = 3)
	public void verifyStatusCodeWhenMidIsNotPassedInRequestBody() {
		staticPref=new QRMappingGetStaticPref();
		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

//        params.put("mid","HySHnd27878673398759");
        params.put("solutionType","qr_mapping");
        params.put("solutionTypeLevel2","PAYMENTS_QR");
        params.put("prefs","RE_KYC_ENABLED");

        services.fetchStaticPrefs(staticPref, params, headers).then().assertThat().statusCode(400);

	}

	@Test(priority = 4)
	public void verifyStatusCodeWhenPrefsFlagNotPassedInRequestBody() {
		staticPref=new QRMappingGetStaticPref();
		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("mid","HySHnd27878673398759");
        params.put("solutionType","qr_mapping");
        params.put("solutionTypeLevel2","PAYMENTS_QR");
//        params.put("prefs","RE_KYC_ENABLED");

        services.fetchStaticPrefs(staticPref, params, headers).then().assertThat().statusCode(400);

	}


	@Test(priority = 5)
	public void verifyErrorWhenOriginIsNotPaytm() {
		staticPref=new QRMappingGetStaticPref();
		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");
        headers.put("origin" , "https://ggapp-frontend-qa.google.com");

        params.put("mid","HySHnd27878673398759");
        params.put("solutionType","qr_mapping");
        params.put("solutionTypeLevel2","PAYMENTS_QR");
        params.put("prefs","RE_KYC_ENABLED");

        services.fetchStaticPrefs(staticPref, params, headers).then().assertThat().statusCode(403);

	}

	@Test(priority = 6)
	public void verifyErrorWhenUserIsNotAuthorized() {


		staticPref=new QRMappingGetStaticPref();
		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
//		headers.put("session_token", sessionToken);
		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("mid","HySHnd27878673398759");
        params.put("solutionType","qr_mapping");
        params.put("solutionTypeLevel2","PAYMENTS_QR");
        params.put("prefs","RE_KYC_ENABLED");
        services.fetchStaticPrefs(staticPref, params, headers).then().statusCode(401);

	}

	@Test(priority = 7)
	public void verifyErrorWhenAppVersionIsNotProvided() {


		staticPref=new QRMappingGetStaticPref();
		headers=new HashMap<>();
		services=new MiddlewareServices();
		params=new HashMap<>();
		headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
		headers.put("session_token", sessionToken);
//		headers.put("version", "5.1.6");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("latitude" , "12.836047");
        headers.put("ipAddress" , "***********");
        headers.put("isBusyBoxFound" , "false");
        headers.put("osVersion" , "10");
        headers.put("appLanguage","en");
        headers.put("isDeviceRooted" , "false");

        params.put("mid","HySHnd27878673398759");
        params.put("solutionType","qr_mapping");
        params.put("solutionTypeLevel2","PAYMENTS_QR");
        params.put("prefs","RE_KYC_ENABLED");
        response=services.fetchStaticPrefs(staticPref, params, headers);
        String message=response.path("message").toString();
        Assert.assertEquals(message, "version is empty in header");
	}



}