package OCL.Individual.FastagAssisted;

import Request.MerchantService.Fastag.FastagDropDown;
import Request.MerchantService.Fastag.GetFastagTags;
import Request.MerchantService.Fastag.ValidateTag;
import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.sdMerchant.Lead_create;
import Request.MerchantService.v2.fastag.CreateFastag;
import Request.MerchantService.v2.fastag.FetchFastagPayment;
import Request.MerchantService.v2.fastag.FetchIssuance;
import Request.MerchantService.v2.fastag.ValidateFastag;
import Request.MerchantService.v3.GetMerchant;
import Request.MerchantService.v3.SendOtp;
import Request.MerchantService.v3.SubmitDocs;
import Request.MerchantService.v3.ValidateOtp;
import Request.MerchantService.v3.merchant.fetch.FetchV3Merchant;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.google.zxing.NotFoundException;
import com.jcraft.jsch.JSchException;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

public class FlowFastagAssistedBasic extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(FlowFastagAssistedBasic.class);

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();


    Faker GenerateFake = new Faker();
    Object TagNumberObj = "";
    String TagNumber = "";
    String Version = "4.2.0";
    String AgentSessionToken = "";
    private String LeadId = "";
    public int Vehicle = GenerateFake.number().numberBetween(1000,9999);
    public int CityCode = GenerateFake.number().numberBetween(1,99);
    public String Code = GenerateFake.name().title();
    public int DMS1 = GenerateFake.number().numberBetween(100000,100000000);
    public int DMS2 = GenerateFake.number().numberBetween(100000,100000000);
    public int DMS3 = GenerateFake.number().numberBetween(100000,100000000);

    public String VehiceNo = "UP"+String.valueOf(CityCode)+"CH"+String.valueOf(Vehicle);
    private String MobileNo = "5556660342";
    private String OtpState = "";
    private String OTP = "";
    private String CustId = "";
    private String PaymentStatus ;
    private String Base64 = "";
    private String QrCodeId = "";
    private  String pathQrCode = "output.jpg";
    private String StatusCodePG = "";
    private String PositivePgResponse = "01";
    private String OePanelDocStatus = "REJECTED";
    private String RejectionReason = "Wrong Document Uploaded";
    private String DocumetRequestDeserialised = "";
    private String WorkFlowId = "";

    public String generateJwtToken()

    {

        LocalDateTime localDateTime=LocalDateTime.now(ZoneId.of("GMT+05:30"));
        // LocalDate localDate = localDateTime.toLocalDate();
        LOGGER.info("Date is :"+localDateTime);
        String ts= localDateTime.toString();
        LOGGER.info("This is TMP : " +ts+"+05:30");

        Algorithm buildAlgorithm = Algorithm.HMAC256("b5ab9b19-7f36-4eae-a845-5bdae889dfb8");
        String token= com.auth0.jwt.JWT.create().withIssuer("OE")
                .withClaim("clientId", "FIS")
                .withClaim("timestamp",ts+"+05:30")
                .withClaim("custId", CustId).sign(buildAlgorithm);

        LOGGER.info("JWT Token is : " + token);
        return token;
    }
    @BeforeClass
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void AgentLoginFastagAssisted() throws Exception {
        LOGGER.info("Before Fastag Assisted Test, Agent Login");
        AgentSessionToken = AgentSessionToken("7771110007","paytm@123");
       // AgentSessionToken = CommonAgentToken;
        DBConnection.UpdateQueryToCloseLead(MobileNo,"fastag");

       /* TestBase testBase =new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '"+MobileNo+"' and status = '0' and solution_type='fastag';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes); */

    }

    @Test(priority = 0,description = "Fetch Valid Tags from FIS",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0001_FetchValidTags()
    {
        GetFastagTags GetTag = new GetFastagTags();

        Map<String,String> query = new HashMap<>();

        query.put("tvc","4");
        query.put("sec_string","nfdjn-nfdnk-343nj-fjenjj");
        query.put("limit","5");

        Response GetTagResp = middlewareServicesObject.FisFetchTags(GetTag,query);

        List TagList = GetTagResp.jsonPath().getList("");
        LOGGER.info("This is Tag List : " +TagList);
        int sizeOfTagList = TagList.size();
        LOGGER.info("Size of the list : " +sizeOfTagList);

        Random rand = new Random();
        int randomNo =  rand.nextInt(sizeOfTagList);
        LOGGER.info("Random No is : " +randomNo);
        TagNumberObj = TagList.get(randomNo);
        LOGGER.info("Tag Number is : " +TagNumberObj);

        TagNumber =  String.valueOf(TagNumberObj);
        LOGGER.info("String Value of Tag No. : " +TagNumber);

    }

    @Test(priority = 0,dependsOnMethods = "TC0001_FetchValidTags",description = "Validate Tags from FIS",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0002_ValidateTags()
    {
        ValidateTag valTag = new ValidateTag();
        valTag.getProperties().setProperty("tagSeqNum",TagNumber);

        Response RespValidateTag = middlewareServicesObject.ValidateTag(valTag,Version,AgentSessionToken);
        int statusCode = RespValidateTag.getStatusCode();
        LOGGER.info("Status Code is : " +statusCode);

        for (int i = 0 ; i<=4 ;i ++)
        {
            if(statusCode!=200)
            {
                ValidateTag valTagErr = new ValidateTag();
                valTagErr.getProperties().setProperty("tagSeqNum",TagNumber);

                Response RespValidateTagErr = middlewareServicesObject.ValidateTag(valTagErr,Version,AgentSessionToken);
                statusCode = RespValidateTagErr.getStatusCode();
                LOGGER.info("Status Code after Error is : " + statusCode);
            }
            else
            {
                LOGGER.info("Success from NCPI");
                break;

            }
        }

        Assert.assertEquals(statusCode,200);

    }
    @Test(priority = 0,dependsOnMethods = "TC0001_FetchValidTags",description = "Fetching Invalid Tag status from FIS",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0002_FetchRandomTag()
    {
        ValidateTag valTag = new ValidateTag();
        valTag.getProperties().setProperty("tagSeqNum","3323810990");

        Response RespValidateTag = middlewareServicesObject.ValidateTag(valTag,Version,AgentSessionToken);

        Assert.assertTrue(RespValidateTag.jsonPath().getString("displayMessage").contains("Tag Number you have entered doesn’t exist. Please contact the support team"));


    }

    @Test(priority = 0,dependsOnMethods = "TC0001_FetchValidTags",description = "Fetching Empty Tags status from FIS",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0002_FetchEmptyTag()
    {
        ValidateTag valTag = new ValidateTag();
        valTag.getProperties().setProperty("tagSeqNum","");

        Response RespValidateTag = middlewareServicesObject.ValidateTag(valTag,Version,AgentSessionToken);

        Assert.assertTrue(RespValidateTag.jsonPath().getString("displayMessage").contains("tagSeqNum Tag Sequence Number is required"));

    }

    @Test(priority = 0,dependsOnMethods = "TC0001_FetchValidTags",description = "Adding extra parameter in Tag Validation",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0002_AddingExtraParamValidateTag()
    {
        ValidateTag valTag = new ValidateTag();
        valTag.addParameter("entity","INDIVIDUAL");
        valTag.getProperties().setProperty("tagSeqNum","3327770990");

        Response RespValidateTag = middlewareServicesObject.ValidateTag(valTag,Version,AgentSessionToken);

        Assert.assertTrue(RespValidateTag.jsonPath().getString("displayMessage").contains("Tag Number you have entered doesn’t exist. Please contact the support team"));

    }

    @Test(priority = 0,dependsOnMethods = "TC0001_FetchValidTags",description = "Empty Body in Tag Validation",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0002_EmptyBodyValidateTag()
    {
        ValidateTag valTag = new ValidateTag();

        Response RespValidateTag = middlewareServicesObject.ValidateTag(valTag,Version,AgentSessionToken);

        Assert.assertTrue(RespValidateTag.jsonPath().getString("displayMessage").contains("tagSeqNum Tag Sequence Number is required"));
    }

    @Test(priority = 0,dependsOnMethods = "TC0001_FetchValidTags",description = "Empty App Version in Tag Validation",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0002_EmptyVersionValidateTag()
    {
        ValidateTag valTag = new ValidateTag();

        Response RespValidateTag = middlewareServicesObject.ValidateTag(valTag,"",AgentSessionToken);

        Assert.assertTrue(RespValidateTag.jsonPath().getString("message").contains("version is empty in header"));
    }

    @Test(priority = 0,dependsOnMethods = "TC0002_ValidateTags",description = "Getting Drop Down List Element",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0003_GettingDropDownList()
    {
        FastagDropDown getDropDown = new FastagDropDown();

        Response RespDropDown = middlewareServicesObject.FastagDropDownList(getDropDown,Version,AgentSessionToken);



    }

    @Test(priority = 0,dependsOnMethods = "TC0002_ValidateTags",description = "Adding Param in Drop Down List Element",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0003_AddingParamDropDownList()
    {
        FastagDropDown getDropDown = new FastagDropDown();
        getDropDown.addParameter("entity","INDIVIDUAL");
        getDropDown.addParameter("solutionType","fastag");

        Response RespDropDown = middlewareServicesObject.FastagDropDownList(getDropDown,Version,AgentSessionToken);

    }

    @Test(priority = 0,dependsOnMethods = "TC0002_ValidateTags",description = "Empty Version in Drop Down List Element",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0003_EmptyVersionDropDownList()
    {
        FastagDropDown getDropDown = new FastagDropDown();

        Response RespDropDown = middlewareServicesObject.FastagDropDownList(getDropDown,"",AgentSessionToken);

        Assert.assertTrue(RespDropDown.jsonPath().getString("message").contains("version is empty in header"));

    }

    @Test(priority = 0,dependsOnMethods = "TC0003_GettingDropDownList",description = "Fetch Issuance of Vrn",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0004_FetchIssuance()
    {
        FetchIssuance fetchIss = new FetchIssuance();
        fetchIss.addParameter("vrn",VehiceNo);

        Response respIssu = middlewareServicesObject.FetchIssuanceFastag(fetchIss,Version,AgentSessionToken);

    }

    @Test(priority = 0,dependsOnMethods = "TC0003_GettingDropDownList",description = "Fetch Issuance of Wrong Vrn",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0004_WrongVrnIssuance()
    {
        FetchIssuance fetchIss = new FetchIssuance();
        fetchIss.addParameter("vrn","VEHICLENO");

        Response respIssu = middlewareServicesObject.FetchIssuanceFastag(fetchIss,Version,AgentSessionToken);

    }

    @Test(priority = 0,dependsOnMethods = "TC0003_GettingDropDownList",description = "Passsing Wrong Parameter",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0004_WrongParameterIssuance()
    {
        FetchIssuance fetchIss = new FetchIssuance();
        fetchIss.addParameter("entity","INDIVIDUAL");

        Response respIssu = middlewareServicesObject.FetchIssuanceFastag(fetchIss,Version,AgentSessionToken);

        Assert.assertEquals(respIssu.getStatusCode(),400);

    }

    @Test(priority = 0,dependsOnMethods = "TC0003_GettingDropDownList",description = "Fetch Issuance of Empty Vrn",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0004_EmptyVrnIssuance()
    {
        FetchIssuance fetchIss = new FetchIssuance();

        Response respIssu = middlewareServicesObject.FetchIssuanceFastag(fetchIss,Version,AgentSessionToken);

        Assert.assertEquals(respIssu.getStatusCode(),400);

    }

    @Test(priority = 0,dependsOnMethods = "TC0003_GettingDropDownList",description = "Empty App version in Fetch Issuance of Vrn",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0004_EmptyVersionFetchIssuance()
    {
        FetchIssuance fetchIss = new FetchIssuance();
        fetchIss.addParameter("vrn",VehiceNo);

        Response respIssu = middlewareServicesObject.FetchIssuanceFastag(fetchIss,"",AgentSessionToken);

        Assert.assertTrue(respIssu.jsonPath().getString("message").contains("version is empty in header"));

    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_01_InvalidSolutionCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fas");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        Assert.assertTrue(RespCreateLead.jsonPath().getString("displayMessage").contains("solution should be fastag"));


    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_02_EmptySolutionCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        Assert.assertTrue(RespCreateLead.jsonPath().getString("displayMessage").contains("solution should be fastag"));

    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_03_NoSolutionCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        Assert.assertEquals(RespCreateLead.getStatusCode(),400);
    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_04_InvalidEntityCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIV");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        Assert.assertTrue(RespCreateLead.jsonPath().getString("displayMessage").contains("entity should be INDIVIDUAL"));
    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_05_EmptyEntityCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        Assert.assertTrue(RespCreateLead.jsonPath().getString("displayMessage").contains("entity should be INDIVIDUAL"));

    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_06_DifferentEntityCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","PUBLIC_LIMITED");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        Assert.assertTrue(RespCreateLead.jsonPath().getString("displayMessage").contains("entity should be INDIVIDUAL"));

    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_07_NoEntityCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        Assert.assertEquals(RespCreateLead.getStatusCode(),400);

    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_08_EmptySolLevel3CreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3","");
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        Assert.assertTrue(RespCreateLead.jsonPath().getString("displayMessage").contains("Invalid vehicle number. Please check the vehicle number format"));


    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_09_NoSolLevel3CreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        Assert.assertTrue(RespCreateLead.jsonPath().getString("displayMessage").contains("Something went wrong. Please try again after some time"));

    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_10_InvalidSolLevel3CreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3","!@#$%^&*()_+|}{");
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        Assert.assertTrue(RespCreateLead.jsonPath().getString("displayMessage").contains("Invalid vehicle number. Please check the vehicle number format"));

    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_10_InvalidVehicleNumCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo","(@)*$^%(#_<>:?.,");
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_10_EmptyVehicleNumCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo","");
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_10_NoVehicleNumCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_11_EmptyTagCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum","");
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_12_InvalidTagCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum","00000111929");
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        Assert.assertTrue(RespCreateLead.jsonPath().getString("displayMessage").contains("Tag Number you have entered doesn’t exist. Please contact the support team"));


    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_13_NoTagCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        Assert.assertTrue(RespCreateLead.jsonPath().getString("displayMessage").contains("Missing parameters in request"));

    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_14_NoFlowCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        Assert.assertTrue(RespCreateLead.jsonPath().getString("displayMessage").contains("flow should either be BASIC or SMART"));

    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_16_InvalidFlowCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","STANDARD");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        Assert.assertTrue(RespCreateLead.jsonPath().getString("displayMessage").contains("flow should either be BASIC or SMART"));



    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_17_EmptyFlowCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        Assert.assertTrue(RespCreateLead.jsonPath().getString("displayMessage").contains("flow should either be BASIC or SMART"));


    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_18_NoIssuanceTypeCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);


    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_19_EmptyIssuanceTypeCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);


    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_20_InvalidIssuanceTypeCreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","0998901233@");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        Assert.assertEquals(RespCreateLead.getStatusCode(),400);

    }

    @Test(priority = 0,dependsOnMethods = "TC0004_FetchIssuance",description = "Create Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_21_CreateLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("vehicleIssuanceType","2");

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        LeadId = RespCreateLead.jsonPath().getJsonObject("leadId");
        int statusCode = RespCreateLead.getStatusCode();

        Assert.assertEquals(statusCode,200);
        Assert.assertNotNull(LeadId);
        Assert.assertNotEquals(LeadId,"");
    }

    @Test(priority = 0,dependsOnMethods = "TC0005_21_CreateLeadFastag",description = "Validate Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0006_01_EmptyLeadIdValidateFastag()
    {
        ValidateFastag valFast = new ValidateFastag();

        valFast.getProperties().setProperty("tagSeqNum",TagNumber);
        valFast.getProperties().setProperty("vehicleNo",VehiceNo);
        valFast.getProperties().setProperty("leadId","");

        Map<String,String>query = new HashMap<>();

        Response RespValFast = middlewareServicesObject.ValidateFastag(valFast,query,Version,AgentSessionToken);

    }

    @Test(priority = 0,dependsOnMethods = "TC0005_21_CreateLeadFastag",description = "Validate Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0006_02_InvalidLeadIdValidateFastag()
    {
        ValidateFastag valFast = new ValidateFastag();

        valFast.getProperties().setProperty("tagSeqNum",TagNumber);
        valFast.getProperties().setProperty("vehicleNo",VehiceNo);
        valFast.getProperties().setProperty("leadId",AgentSessionToken);

        Map<String,String>query = new HashMap<>();

        Response RespValFast = middlewareServicesObject.ValidateFastag(valFast,query,Version,AgentSessionToken);

    }

    @Test(priority = 0,dependsOnMethods = "TC0005_21_CreateLeadFastag",description = "Validate Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0006_03_NoLeadIdValidateFastag()
    {
        ValidateFastag valFast = new ValidateFastag();

        valFast.getProperties().setProperty("tagSeqNum",TagNumber);
        valFast.getProperties().setProperty("vehicleNo",VehiceNo);

        Map<String,String>query = new HashMap<>();

        Response RespValFast = middlewareServicesObject.ValidateFastag(valFast,query,Version,AgentSessionToken);

    }

    @Test(priority = 0,dependsOnMethods = "TC0005_21_CreateLeadFastag",description = "Validate Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0006_04_EmptyVehicleValidateFastag()
    {
        ValidateFastag valFast = new ValidateFastag();

        valFast.getProperties().setProperty("tagSeqNum",TagNumber);
        valFast.getProperties().setProperty("vehicleNo","");
        valFast.getProperties().setProperty("leadId",LeadId);

        Map<String,String>query = new HashMap<>();

        Response RespValFast = middlewareServicesObject.ValidateFastag(valFast,query,Version,AgentSessionToken);

        Assert.assertTrue(RespValFast.jsonPath().getString("displayMessage").contains("Both Vehicle id and vehicle number cannot be null"));


    }

    @Test(priority = 0,dependsOnMethods = "TC0005_21_CreateLeadFastag",description = "Validate Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0006_05_InvalidVehicleValidateFastag()
    {
        ValidateFastag valFast = new ValidateFastag();

        valFast.getProperties().setProperty("tagSeqNum",TagNumber);
        valFast.getProperties().setProperty("vehicleNo","!$%^&*()_+|}{<>?:");
        valFast.getProperties().setProperty("leadId",LeadId);

        Map<String,String>query = new HashMap<>();

        Response RespValFast = middlewareServicesObject.ValidateFastag(valFast,query,Version,AgentSessionToken);

        Assert.assertTrue(RespValFast.jsonPath().getString("displayMessage").contains("Please enter a valid Vehicle Number"));

    }

    @Test(priority = 0,dependsOnMethods = "TC0005_21_CreateLeadFastag",description = "Validate Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0006_06_NoVehicleValidateFastag()
    {
        ValidateFastag valFast = new ValidateFastag();

        valFast.getProperties().setProperty("tagSeqNum",TagNumber);
        valFast.getProperties().setProperty("leadId",LeadId);

        Map<String,String>query = new HashMap<>();

        Response RespValFast = middlewareServicesObject.ValidateFastag(valFast,query,Version,AgentSessionToken);

        Assert.assertTrue(RespValFast.jsonPath().getString("displayMessage").contains("Both Vehicle id and vehicle number cannot be null"));

    }

    @Test(priority = 0,dependsOnMethods = "TC0005_21_CreateLeadFastag",description = "Validate Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0006_07_EmptyTagValidateFastag()
    {
        ValidateFastag valFast = new ValidateFastag();

        valFast.getProperties().setProperty("tagSeqNum","");
        valFast.getProperties().setProperty("vehicleNo",VehiceNo);
        valFast.getProperties().setProperty("leadId",LeadId);

        Map<String,String>query = new HashMap<>();

        Response RespValFast = middlewareServicesObject.ValidateFastag(valFast,query,Version,AgentSessionToken);

        Assert.assertTrue(RespValFast.jsonPath().getString("displayMessage").contains("tagSeqNum Tag sequence number must not be null"));


    }

    @Test(priority = 0,dependsOnMethods = "TC0005_21_CreateLeadFastag",description = "Validate Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0006_08_InvalidTagValidateFastag()
    {
        ValidateFastag valFast = new ValidateFastag();

        valFast.getProperties().setProperty("tagSeqNum","0987654412");
        valFast.getProperties().setProperty("vehicleNo",VehiceNo);
        valFast.getProperties().setProperty("leadId",LeadId);

        Map<String,String>query = new HashMap<>();

        Response RespValFast = middlewareServicesObject.ValidateFastag(valFast,query,Version,AgentSessionToken);

        Assert.assertTrue(RespValFast.jsonPath().getString("displayMessage").contains("You have not been allocated this tag for issuance. Please contact your superior for getting the inventory allocated to you"));

    }

    @Test(priority = 0,dependsOnMethods = "TC0005_21_CreateLeadFastag",description = "Validate Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0006_09_NoTagValidateFastag()
    {
        ValidateFastag valFast = new ValidateFastag();

        valFast.getProperties().setProperty("vehicleNo",VehiceNo);
        valFast.getProperties().setProperty("leadId",LeadId);

        Map<String,String>query = new HashMap<>();

        Response RespValFast = middlewareServicesObject.ValidateFastag(valFast,query,Version,AgentSessionToken);

        Assert.assertTrue(RespValFast.jsonPath().getString("displayMessage").contains("tagSeqNum Tag sequence number must not be null"));


    }

    @Test(priority = 0,dependsOnMethods = "TC0005_21_CreateLeadFastag",description = "Validate Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0006_10_ValidateFastag()
    {
        ValidateFastag valFast = new ValidateFastag();

        valFast.getProperties().setProperty("tagSeqNum",TagNumber);
        valFast.getProperties().setProperty("vehicleNo",VehiceNo);
        valFast.getProperties().setProperty("leadId",LeadId);

        Map<String,String>query = new HashMap<>();

        Response RespValFast = middlewareServicesObject.ValidateFastag(valFast,query,Version,AgentSessionToken);

    }

    @Test(priority = 0,dependsOnMethods = "TC0006_10_ValidateFastag",description = "Fetch V3 Fastag Details",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0007_01_InvalidSolutionFetchV3FastagDetails()
    {
        FetchV3Merchant v3Merch = new FetchV3Merchant(LeadId);

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fas");

        Response RespV3Fetch = middlewareServicesObject.FetchV3MerchantLead(v3Merch,query,Version,AgentSessionToken);

        Assert.assertTrue(RespV3Fetch.jsonPath().getString("message").contains("Failed to fetch merchant lead details"));
    }

    @Test(priority = 0,dependsOnMethods = "TC0006_10_ValidateFastag",description = "Fetch V3 Fastag Details",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0007_02_EmptySolutionFetchV3FastagDetails()
    {
        FetchV3Merchant v3Merch = new FetchV3Merchant(LeadId);

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","");

        Response RespV3Fetch = middlewareServicesObject.FetchV3MerchantLead(v3Merch,query,Version,AgentSessionToken);

        Assert.assertTrue(RespV3Fetch.jsonPath().getString("message").contains("Failed to fetch merchant lead details"));
    }

    @Test(priority = 0,dependsOnMethods = "TC0006_10_ValidateFastag",description = "Fetch V3 Fastag Details",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0007_03_DifferentSolutionFetchV3FastagDetails()
    {
        FetchV3Merchant v3Merch = new FetchV3Merchant(LeadId);

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","p2p_100k");

        Response RespV3Fetch = middlewareServicesObject.FetchV3MerchantLead(v3Merch,query,Version,AgentSessionToken);

        Assert.assertTrue(RespV3Fetch.jsonPath().getString("message").contains("Failed to fetch merchant lead details"));
    }

    @Test(priority = 0,dependsOnMethods = "TC0006_10_ValidateFastag",description = "Fetch V3 Fastag Details",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0007_04_NoSolutionFetchV3FastagDetails()
    {
        FetchV3Merchant v3Merch = new FetchV3Merchant(LeadId);

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");


        Response RespV3Fetch = middlewareServicesObject.FetchV3MerchantLead(v3Merch,query,Version,AgentSessionToken);

        Assert.assertEquals(RespV3Fetch.getStatusCode(),400);
    }

    @Test(priority = 0,dependsOnMethods = "TC0006_10_ValidateFastag",description = "Fetch V3 Fastag Details",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0007_05_InvalidEntityFetchV3FastagDetails()
    {
        FetchV3Merchant v3Merch = new FetchV3Merchant(LeadId);

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDI");
        query.put("solutionType","p2p_100k");

        Response RespV3Fetch = middlewareServicesObject.FetchV3MerchantLead(v3Merch,query,Version,AgentSessionToken);

        Assert.assertTrue(RespV3Fetch.jsonPath().getString("message").contains("Failed to fetch merchant lead details"));

    }

    @Test(priority = 0,dependsOnMethods = "TC0006_10_ValidateFastag",description = "Fetch V3 Fastag Details",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0007_06_DifferentEntityFetchV3FastagDetails()
    {
        FetchV3Merchant v3Merch = new FetchV3Merchant(LeadId);

        Map<String,String> query = new HashMap<>();
        query.put("entityType","PUBLIC_LIMITED");
        query.put("solutionType","p2p_100k");

        Response RespV3Fetch = middlewareServicesObject.FetchV3MerchantLead(v3Merch,query,Version,AgentSessionToken);

    }

    @Test(priority = 0,dependsOnMethods = "TC0006_10_ValidateFastag",description = "Fetch V3 Fastag Details",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0007_07_EmptyEntityFetchV3FastagDetails()
    {
        FetchV3Merchant v3Merch = new FetchV3Merchant(LeadId);

        Map<String,String> query = new HashMap<>();
        query.put("entityType","");
        query.put("solutionType","p2p_100k");

        Response RespV3Fetch = middlewareServicesObject.FetchV3MerchantLead(v3Merch,query,Version,AgentSessionToken);
    }

    @Test(priority = 0,dependsOnMethods = "TC0006_10_ValidateFastag",description = "Fetch V3 Fastag Details",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0007_08_NoEntityFetchV3FastagDetails()
    {
        FetchV3Merchant v3Merch = new FetchV3Merchant(LeadId);

        Map<String,String> query = new HashMap<>();
        query.put("solutionType","p2p_100k");

        Response RespV3Fetch = middlewareServicesObject.FetchV3MerchantLead(v3Merch,query,Version,AgentSessionToken);

        Assert.assertEquals(RespV3Fetch.getStatusCode(),400);
    }

    @Test(priority = 0,dependsOnMethods = "TC0006_10_ValidateFastag",description = "Fetch V3 Fastag Details",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0007_09_FetchV3FastagDetails()
    {
        FetchV3Merchant v3Merch = new FetchV3Merchant(LeadId);

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespV3Fetch = middlewareServicesObject.FetchV3MerchantLead(v3Merch,query,Version,AgentSessionToken);
    }

    @Test(priority = 0,dependsOnMethods = "TC0007_09_FetchV3FastagDetails",description = "Send OTP Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0008_SentOtpFastag()
    {
        SendOtp SendOTPobj = new SendOtp();

        Response ResObject = middlewareServicesObject.v3SentOtp(SendOTPobj,"INDIVIDUAL","fastag",AgentSessionToken,Version,MobileNo,"fastag");
        String expectedErrorMsg = "Otp sent to phone";
        OtpState = ResObject.jsonPath().getString("state");
        LOGGER.info("OTP State is : " + OtpState);
        String actualErrorMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        Assert.assertEquals(StatusCode,200);
    }

    @Test(priority = 0,dependsOnMethods = "TC0008_SentOtpFastag",description = "Validate OTP Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0009_ValidateOtpFastag() throws IOException, JSchException {
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("ValidateOtpFastagAssisted"));
        //OTP = getOTP(mobileNo);
        OTP="888888";
        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj,"INDIVIDUAL", "fastag", AgentSessionToken, Version, MobileNo, "fastag", OtpState,OTP);
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");


        for (int i = 0; i<=3;i++)
        {

            if (CustId == null || CustId.isEmpty() || StatusCode!=200)
            {
                LOGGER.info("Lead Id is null, Inside loop : " +i);
                TC0008_SentOtpFastag();
                waitForLoad(5000);
                ValidateOtp ValidateOTPobjErr = new ValidateOtp(P.TESTDATA.get("ValidateOtpFastagAssisted"));
                OTP = getOTP(MobileNo);
                LOGGER.info("This is OTP " + OTP);
                Response ResNewObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobjErr,"INDIVIDUAL", "fastag", AgentSessionToken, Version, MobileNo, "fastag", OtpState,OTP);
                StatusCode = ResNewObj.getStatusCode();
                CustId = ResNewObj.jsonPath().getString("custId");
            }
            else
            {

                Assert.assertEquals(StatusCode, 200);
                break;
            }
        }

        LOGGER.info("CustId " + CustId);
        Assert.assertNotEquals(CustId,null);
    }

    @Test(priority = 0,dependsOnMethods = "TC0009_ValidateOtpFastag",description = "Fetch Merchant Details Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00010_01_InvalidEntityFetchMerchantFastag()
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDI", "fastag", AgentSessionToken, Version);
        int StatusCode = resObjGetMerchant.getStatusCode();
        // Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0,dependsOnMethods = "TC0009_ValidateOtpFastag",description = "Fetch Merchant Details Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00010_02_DifferentEntityFetchMerchantFastag()
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"PUBLIC_LIMITED", "fastag", AgentSessionToken, Version);
        int StatusCode = resObjGetMerchant.getStatusCode();
        // Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0,dependsOnMethods = "TC0009_ValidateOtpFastag",description = "Fetch Merchant Details Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00010_03_EmptyEntityFetchMerchantFastag()
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"", "fastag", AgentSessionToken, Version);
        int StatusCode = resObjGetMerchant.getStatusCode();
        // Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0,dependsOnMethods = "TC0009_ValidateOtpFastag",description = "Fetch Merchant Details Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00010_04_InvalidEntityFetchMerchantFastag()
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "fas", AgentSessionToken, Version);
        int StatusCode = resObjGetMerchant.getStatusCode();
        // Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0,dependsOnMethods = "TC0009_ValidateOtpFastag",description = "Fetch Merchant Details Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00010_05_EmptyEntityFetchMerchantFastag()
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "", AgentSessionToken, Version);
        int StatusCode = resObjGetMerchant.getStatusCode();
        // Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0,dependsOnMethods = "TC0009_ValidateOtpFastag",description = "Fetch Merchant Details Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00010_06_DifferentEntityFetchMerchantFastag()
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "p2p_100K", AgentSessionToken, Version);
        int StatusCode = resObjGetMerchant.getStatusCode();
        // Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0,dependsOnMethods = "TC0009_ValidateOtpFastag",description = "Fetch Merchant Details Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00010_07_EmptySessionTokenFetchMerchantFastag()
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "fastag", "", Version);
        int StatusCode = resObjGetMerchant.getStatusCode();
        // Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0,dependsOnMethods = "TC0009_ValidateOtpFastag",description = "Fetch Merchant Details Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00010_08_FetchMerchantFastag()
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "fastag", AgentSessionToken, Version);
        int StatusCode = resObjGetMerchant.getStatusCode();
       // Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0,dependsOnMethods = "TC00010_08_FetchMerchantFastag",description = "Submit Fastag Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00011_SubmitLeadFastag()
    {
        CreateFastag createFastag = new CreateFastag();

        createFastag.getProperties().setProperty("solutionTypeLevel3",VehiceNo);
        createFastag.getProperties().setProperty("tagSeqNum",TagNumber);
        createFastag.getProperties().setProperty("vehicleNo",VehiceNo);
        createFastag.getProperties().setProperty("flow","BASIC");
        createFastag.getProperties().setProperty("customerName","Aashit Sharma");
        createFastag.getProperties().setProperty("email",VehiceNo+"@gmail.com");
        createFastag.getProperties().setProperty("merchantCustId",CustId);
        createFastag.getProperties().setProperty("leadId",LeadId);

        Map<String,String> query = new HashMap<>();
        query.put("entityType","INDIVIDUAL");
        query.put("solutionType","fastag");

        Response RespCreateLead = middlewareServicesObject.FastagCreateLead(createFastag,query,Version,AgentSessionToken);

        LeadId = RespCreateLead.jsonPath().getJsonObject("leadId");
    }

    @Test(priority = 0,dependsOnMethods = "TC00011_SubmitLeadFastag",description = "Validate Fastag Lead after submission",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00012_ValidateAfterSubmitFastag()
    {
        ValidateFastag valFast = new ValidateFastag();

        valFast.getProperties().setProperty("tagSeqNum",TagNumber);
        valFast.getProperties().setProperty("vehicleNo",VehiceNo);
        valFast.getProperties().setProperty("leadId",LeadId);
        valFast.getProperties().setProperty("customerName","Aashit Sharma");
        valFast.getProperties().setProperty("email",VehiceNo+"@gmail.com");
        valFast.getProperties().setProperty("customerId",CustId);

        Map<String,String>query = new HashMap<>();

        Response RespValFast = middlewareServicesObject.ValidateFastag(valFast,query,Version,AgentSessionToken);

    }

    @Test(priority = 0,dependsOnMethods = "TC00012_ValidateAfterSubmitFastag",description = "Upload Documents of Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00012_UploadDocumentsFastag()
    {
        SubmitDocs v3DocSubmitRC  = new SubmitDocs();

        Map<String,String> queryCommon = new HashMap<>();

        queryCommon.put("docId",MobileNo);
        queryCommon.put("pageNo","0");
        queryCommon.put("docCount","0");
        queryCommon.put("merchantCustId",CustId);
        queryCommon.put("leadId",LeadId);
        queryCommon.put("entityType","INDIVIDUAL");
        queryCommon.put("solutionType","fastag");
        LOGGER.info("This is Single Page map : "  +queryCommon);

        Map<String,String> RC = queryCommon;
        RC.put("docType","VehicleRCPhoto");


        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmitRC, AgentSessionToken, Version,RC);
        int errorCode = submitDocs.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCode, 204);

        SubmitDocs v3DocSubmitRCBack  = new SubmitDocs();

        Map<String,String> RCBack = queryCommon;
        RCBack.put("docType","VehicleRCBackPhoto");


        Response submitDocsBack = middlewareServicesObject.V3SubmitDocs(v3DocSubmitRCBack, AgentSessionToken, Version,RCBack);
        int errorCodeBack = submitDocsBack.jsonPath().getInt("errorCode");
        Assert.assertEquals(errorCodeBack, 204);


        SubmitDocs v3DocSubmitGVW  = new SubmitDocs();

        Map<String,String> GVW = queryCommon;
        GVW.put("docType","VehicleGVWPhoto");
        Response submitDocsGVW = middlewareServicesObject.V3SubmitDocs(v3DocSubmitGVW, AgentSessionToken, Version,GVW);
        int errorCodeGVW = submitDocsGVW.jsonPath().getInt("errorCode");
        //Assert.assertEquals(errorCodeGVW, 204);

    }

    @Test(priority = 0,dependsOnMethods = "TC00012_UploadDocumentsFastag",description = "Fetch Invalid Lead Payment Satus of Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00013_01_InvalidLeadFetchPaymentStatusFastag() {
        FetchFastagPayment fetchPay = new FetchFastagPayment();
        Map<String, String> queryCommon = new HashMap<>();
        queryCommon.put("generateQR", "true");
        queryCommon.put("leadId", AgentSessionToken);


        Response RespFetchPay = middlewareServicesObject.FetchFastagPayment(fetchPay, queryCommon, Version, AgentSessionToken);
    }

    @Test(priority = 0,dependsOnMethods = "TC00012_UploadDocumentsFastag",description = "Fetch Invalid Lead Payment Satus of Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00013_02_EmptyLeadFetchPaymentStatusFastag() {
        FetchFastagPayment fetchPay = new FetchFastagPayment();
        Map<String, String> queryCommon = new HashMap<>();
        queryCommon.put("generateQR", "true");
        queryCommon.put("leadId", "");


        Response RespFetchPay = middlewareServicesObject.FetchFastagPayment(fetchPay, queryCommon, Version, AgentSessionToken);
    }

    @Test(priority = 0,dependsOnMethods = "TC00012_UploadDocumentsFastag",description = "Fetch Invalid Lead Payment Satus of Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00013_03_NoLeadFetchPaymentStatusFastag() {
        FetchFastagPayment fetchPay = new FetchFastagPayment();
        Map<String, String> queryCommon = new HashMap<>();
        queryCommon.put("generateQR", "true");


        Response RespFetchPay = middlewareServicesObject.FetchFastagPayment(fetchPay, queryCommon, Version, AgentSessionToken);
    }

    @Test(priority = 0,dependsOnMethods = "TC00012_UploadDocumentsFastag",description = "Fetch Invalid Lead Payment Satus of Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00013_04_EmptyQrPaymentStatusFastag() {
        FetchFastagPayment fetchPay = new FetchFastagPayment();
        Map<String, String> queryCommon = new HashMap<>();
        queryCommon.put("generateQR", "");
        queryCommon.put("leadId",LeadId);


        Response RespFetchPay = middlewareServicesObject.FetchFastagPayment(fetchPay, queryCommon, Version, AgentSessionToken);
    }

    @Test(priority = 0,dependsOnMethods = "TC00012_UploadDocumentsFastag",description = "Fetch Invalid Lead Payment Satus of Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00013_05_InvalidQrPaymentStatusFastag() {
        FetchFastagPayment fetchPay = new FetchFastagPayment();
        Map<String, String> queryCommon = new HashMap<>();
        queryCommon.put("generateQR", "YES001");
        queryCommon.put("leadId",LeadId);


        Response RespFetchPay = middlewareServicesObject.FetchFastagPayment(fetchPay, queryCommon, Version, AgentSessionToken);
    }

    @Test(priority = 0,dependsOnMethods = "TC00012_UploadDocumentsFastag",description = "Fetch Invalid Lead Payment Satus of Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00013_06_NoQrPaymentStatusFastag() {
        FetchFastagPayment fetchPay = new FetchFastagPayment();
        Map<String, String> queryCommon = new HashMap<>();
        queryCommon.put("leadId",LeadId);


        Response RespFetchPay = middlewareServicesObject.FetchFastagPayment(fetchPay, queryCommon, Version, AgentSessionToken);
    }

    @Test(priority = 0,dependsOnMethods = "TC00012_UploadDocumentsFastag",description = "Fetch Invalid Lead Payment Satus of Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00013_07_NoParamsPaymentStatusFastag() {
        FetchFastagPayment fetchPay = new FetchFastagPayment();
        Map<String, String> queryCommon = new HashMap<>();


        Response RespFetchPay = middlewareServicesObject.FetchFastagPayment(fetchPay, queryCommon, Version, AgentSessionToken);
    }

    @Test(priority = 0,dependsOnMethods = "TC00012_UploadDocumentsFastag",description = "Fetch Invalid Lead Payment Satus of Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00013_08_WrongParamsPaymentStatusFastag() {
        FetchFastagPayment fetchPay = new FetchFastagPayment();
        Map<String, String> queryCommon = new HashMap<>();
        queryCommon.put("entity","PUBLIC_LIMITED");
        queryCommon.put("solutionType","fastag");


        Response RespFetchPay = middlewareServicesObject.FetchFastagPayment(fetchPay, queryCommon, Version, AgentSessionToken);
    }

    @Test(priority = 0,dependsOnMethods = "TC00012_UploadDocumentsFastag",description = "Fetch Payment Satus of Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00013_09_FetchPaymentStatusFastag()
    {
        FetchFastagPayment fetchPay = new FetchFastagPayment();
        Map<String,String> queryCommon = new HashMap<>();
        queryCommon.put("generateQR","true");
        queryCommon.put("leadId",LeadId);


        Response RespFetchPay = middlewareServicesObject.FetchFastagPayment(fetchPay,queryCommon,Version,AgentSessionToken);

        PaymentStatus = RespFetchPay.jsonPath().getJsonObject("paymentDone").toString();
        LOGGER.info("This is Payment Status : " +PaymentStatus);

        if(!PaymentStatus.equals("true"))
        {
            Base64 = RespFetchPay.jsonPath().getJsonObject("qrCodeBase64");
        }
        else
        {
            LOGGER.info("Payment is Done");
        }

    }

    @Test(priority = 0,dependsOnMethods = "TC00013_09_FetchPaymentStatusFastag",description = "Make Payment of Fastag",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00014_MakePaymentFastag() throws IOException, NotFoundException {

        if (!PaymentStatus.equals("true"))
        {
            for (int i = 0; i < 4; ++i)
            {

                if (!StatusCodePG.equals(PositivePgResponse) && !PaymentStatus.equals("true"))
                {
                    LOGGER.info("Payment Is not Done :(");
                    QrCodeId = QrCodeExtractor(Base64,pathQrCode);
                    FetchQrDetails(QrCodeId);
                    LOGGER.info("MID to Pay : " + PayMID);
                    LOGGER.info("MGUID to Pay : " + PayMGUID);
                    LOGGER.info("Amount to be Payed : " + Amount);
                    LOGGER.info("OrderId Generated : " + OrderId);

                    StatusCodePG = PayMerchant(OrderId, Amount, PayMID,"TrueOrderCreated");
                    TC00013_09_FetchPaymentStatusFastag();
                }
                else if (StatusCodePG.equals(PositivePgResponse) && !PaymentStatus.equals("true"))
                {
                    LOGGER.info("Payment is Done from PG, Payment status is False");
                    TC00013_09_FetchPaymentStatusFastag();
                }
                else {
                    LOGGER.info("Payment Successfully Done for LeadID : " + LeadId);
                    break;
                }

            }
        }
    }

    @Test(priority = 0,description = "Fetch Lead & Submit on Panelfor Fastag",groups = {"Regression"},dependsOnMethods = "TC00014_MakePaymentFastag")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00015_RejectLeadFastag() throws JsonProcessingException
    {
        Map <String,String> RequestPanel = new HashMap<>();
        Map <String,String> ResponsePanel = new HashMap<>();

        RequestPanel.put("docStatus",OePanelDocStatus);
        RequestPanel.put("rejectionReason",RejectionReason);
        RequestPanel.put("leadId",LeadId);

        ResponsePanel = FetchPanelLead(RequestPanel);

        DocumetRequestDeserialised = ResponsePanel.get("DocumenstArray");
        WorkFlowId = ResponsePanel.get("WorkFlowId");
        LOGGER. info("Lead Stage is : " + ResponsePanel.get("LeadStage"));


        LOGGER.info("Inside ReallocatingAgent Method ");
        ReallocatingAgent(LeadId,"1152");

        EditLead v1EditLeadObj=new EditLead(LeadId, P.TESTDATA.get("EditLeadRejectFastagAssisted"));

        v1EditLeadObj.getProperties().setProperty("documents",DocumetRequestDeserialised);
        // v1EditLeadObj.getProperties().setProperty("reEnterAccountNumber",mobileNo);
        v1EditLeadObj.getProperties().setProperty("workflowStatusId",WorkFlowId);

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "**********", "**********", XMWCookie,"application/json");

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode,200);


    }
    @Test(priority = 0,description = "Fetch Lead & Submit on Panelfor Fastag",groups = {"Regression"},dependsOnMethods = "TC00014_MakePaymentFastag")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00016_UploadRejectedDocumentsFastag()
    {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", "fastag");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "FIS");
        queryParams.put("solutionTypeLevel2", "ASSISTED");
        queryParams.put("solutionTypeLevel3", VehiceNo);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", generateJwtToken());
        headers.put("custId", CustId);
        headers.put("latitude", "28.9088");
        headers.put("longitude", "77.2727");

        Map<String, String> body = new HashMap<String, String>();
        body.put("DMS1", String.valueOf(DMS1));
        body.put("DMS2", String.valueOf(DMS2));
        body.put("DMS3", String.valueOf(DMS3));

        waitForLoad(3000);
        Lead_create leadResponseObject = new Lead_create(P.TESTDATA.get("FastagUpdateLead"));

        Response responseObject = middlewareServicesObject.CreateLead(leadResponseObject, queryParams, headers, body);

        LOGGER.info("This is Display Message : " +responseObject.jsonPath().getString("message"));

        LeadId = responseObject.jsonPath().getString("leadId");
        LOGGER.info("This is Lead ID : " +LeadId);

        String ExpectedMsg = "SUCCESS";
        Assert.assertEquals(responseObject.jsonPath().getString("message"),ExpectedMsg);
        Assert.assertEquals(responseObject.getStatusCode(),200);
    }

    @Test(priority = 0,description = "Fetch Lead & Submit on Panelfor Fastag",groups = {"Regression"},dependsOnMethods = "TC00014_MakePaymentFastag")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC00017_SubmitApproveDocFastag() throws JsonProcessingException {
        OePanelDocStatus = "APPROVED";
         RejectionReason = null;
        TC00015_RejectLeadFastag();

    }


}
