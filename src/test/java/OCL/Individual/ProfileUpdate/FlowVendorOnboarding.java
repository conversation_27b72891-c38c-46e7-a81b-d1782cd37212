package OCL.Individual.ProfileUpdate;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.v1.profile.update.CreateVendor;
import Request.MerchantService.v1.profile.update.doc.StatusDoc;
import Request.MerchantService.v1.profile.update.lead.Status;
import Request.MerchantService.v2.upgradeMid.doc.uploadCancelledCheque;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
//import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;

import org.testng.Assert;
import org.testng.annotations.Test;

import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
  /*
public class FlowVendorOnboarding extends BaseMethod {
 private static final Logger LOGGER = Logger.getLogger(FlowVendorOnboarding.class);

    public static String ApplicantToken = "";
    List BankList = new ArrayList();
    Map Bank = new HashMap<>();
    public static String SavedBank = "";
    public static String MID = "";
    public static String MID2 = "";
    public static String LeadId = "";
    public static String LeadStatus = "";
    public static String LeadClosed = "LEAD_CLOSED";
    public static String LeadNotPresent = "LEAD_NOT_PRESENT";
    public static String ImagesPending = "IMAGES_PENDING";
    public static String AwaitingClose = "AWAITING_CLOSE";
    public static String NameMatch;
    public static String DocType = "";
    public static String DocProvided = "";
    public static String XMWToken = "";
    public static String WorkFlowId = "";
    public static String CancelledChequeUUID = "";
    public static String BankAcNo = "";
    public static String IFSC = "";
    public static String BankName = "";
    public static String MobileNo1 = "**********";
    public static String MobileNo2 = "**********";
    public static String App1CustId = "";
    public static String App2CustId = "";
    public static String AccountNumber;
    String leadStage = "";
    static String IsBankPresent = "";
    private File CanCelledChequePDF = new File(System.getProperty("user.dir") + "/PaytmImage.jpg");


    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    Utilities accObj = new Utilities();

    @Test(priority = 0)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC000_GetSessionToken() throws SQLException {
        ApplicantToken = ApplicantToken(MobileNo1, "paytm@123");
        LOGGER.info("Applicant Session Token is : " + ApplicantToken);
        App1CustId = FetchUserDetails(MobileNo1, "mobile");
        MID = FetchMID(App1CustId);

    }

    public void FetchLeadStatus(String AppMID, String AppToken) {
        Status fetcStatus = new Status(P.TESTDATA.get("FetchStatusVendorOnboard"));
        fetcStatus.getProperties().setProperty("addVendorLeadId", LeadId);
        fetcStatus.getProperties().setProperty("mid", AppMID);


        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus, "pg_profile_update", "INDIVIDUAL", "ADD_VENDOR", "application/json", "BabaBlackSheepWeAreInShitDeep", AppToken, AppMID);

        LeadStatus = responseObject.jsonPath().getJsonObject("leadStatus").toString();

        if (responseObject.jsonPath().getJsonObject("leadId") != null) {
            LOGGER.info("Inside if Lead Id exist in fetch lead status");
            LeadId = responseObject.jsonPath().getJsonObject("leadId").toString();
        }
        LOGGER.info("Current Lead Status is : " + LeadStatus);

    }

   @Test(priority = 0, description = "Fetch Lead status of Add Vendor Onboarding", dependsOnMethods = "TC000_GetSessionToken")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC01_FetchLeadStatusVendorOnboarding() {
        FetchLeadStatus(MID, ApplicantToken);
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create Add Vendor lead", priority = 1, dependsOnMethods = "TC000_GetSessionToken")
    public void TC0001_CreateVendorOnboardingWithEmptysolutionSubType() {

        AccountNumber = accObj.randomMobileNumberGenerator();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("businessName", "Name Business");
        body.put("ifsc", "CITI0000007");
        body.put("bankName", "CITI BANK");
        body.put("bankAccountNumber", AccountNumber);
        body.put("beneficiaryName", "Test");
        body.put("vendorName", "Puneet");
        body.put("vendorEmail", "<EMAIL>");
        body.put("vendorMobile", MobileNo1);
        body.put("pan", "crzpk0964l");
        body.put("businessType", "INDIVIDUAL");
        body.put("category", "Individual Services");
        body.put("subcategory", "Photo & Video Making");

        // Update v1Update = new Update("AddVendor");
        CreateVendor v1Update = new CreateVendor();


        Response responseObject = middlewareServicesObject.v1ProfileUpdateForAddVendor(v1Update, queryParams, headers, body);

        String StatusCode = responseObject.jsonPath().getJsonObject("statusCode").toString();
        Assert.assertEquals(StatusCode, "400");
        // xyz = responseObject.getStatusCode();
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create Add Vendor lead", priority = 1, dependsOnMethods = "TC000_GetSessionToken")
    public void TC0002_CreateVendorOnboardingWithEmptySessionToken() {

        AccountNumber = accObj.randomMobileNumberGenerator();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", "");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("businessName", "Name Business");
        body.put("ifsc", "CITI0000007");
        body.put("bankName", "CITI BANK");
        body.put("bankAccountNumber", AccountNumber);
        body.put("beneficiaryName", "Test");
        body.put("vendorName", "Puneet");
        body.put("vendorEmail", "<EMAIL>");
        body.put("vendorMobile", MobileNo1);
        body.put("pan", "crzpk0964l");
        body.put("businessType", "INDIVIDUAL");
        body.put("category", "Individual Services");
        body.put("subcategory", "Photo & Video Making");

        // Update v1Update = new Update("AddVendor");
        CreateVendor v1Update = new CreateVendor();


        Response responseObject = middlewareServicesObject.v1ProfileUpdateForAddVendor(v1Update, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
        System.out.println("status code is" + statusCode);
        //NameMatch = responseObject.jsonPath().getJsonObject("nameMatchSuccess").toString();
       // LeadId = responseObject.jsonPath().getJsonObject("leadId").toString();
        //String StatusCode = responseObject.jsonPath().getJsonObject("statusCode").toString();
        Assert.assertEquals(statusCode, 401);

        // xyz = responseObject.getStatusCode();
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create Add Vendor lead", priority = 1, dependsOnMethods = "TC000_GetSessionToken")
    public void TC0003_CreateVendorOnboardingWithEmptyMID() {

        AccountNumber = accObj.randomMobileNumberGenerator();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "ADD_VENDOR");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "");
        body.put("businessName", "Name Business");
        body.put("ifsc", "CITI0000007");
        body.put("bankName", "CITI BANK");
        body.put("bankAccountNumber", AccountNumber);
        body.put("beneficiaryName", "Test");
        body.put("vendorName", "Puneet");
        body.put("vendorEmail", "<EMAIL>");
        body.put("vendorMobile", MobileNo1);
        body.put("pan", "crzpk0964l");
        body.put("businessType", "INDIVIDUAL");
        body.put("category", "Individual Services");
        body.put("subcategory", "Photo & Video Making");

        // Update v1Update = new Update("AddVendor");
        CreateVendor v1Update = new CreateVendor();


        Response responseObject = middlewareServicesObject.v1ProfileUpdateForAddVendor(v1Update, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
       // NameMatch = responseObject.jsonPath().getJsonObject("nameMatchSuccess").toString();
        //LeadId = responseObject.jsonPath().getJsonObject("leadId").toString();
        String StatusCode = responseObject.jsonPath().getJsonObject("statusCode").toString();
        Assert.assertEquals(statusCode, 400);
        // xyz = responseObject.getStatusCode();
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create Add Vendor lead", priority = 1, dependsOnMethods = "TC000_GetSessionToken")
    public void TC0004_CreateVendorOnboardingWithEmptyvendorName() {

        AccountNumber = accObj.randomMobileNumberGenerator();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "ADD_VENDOR");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("businessName", "Name Business");
        body.put("ifsc", "CITI0000007");
        body.put("bankName", "CITI BANK");
        body.put("bankAccountNumber", AccountNumber);
        body.put("beneficiaryName", "Test");
        body.put("vendorName", "");
        body.put("vendorEmail", "<EMAIL>");
        body.put("vendorMobile", MobileNo1);
        body.put("pan", "crzpk0964l");
        body.put("businessType", "INDIVIDUAL");
        body.put("category", "Individual Services");
        body.put("subcategory", "Photo & Video Making");

        // Update v1Update = new Update("AddVendor");
        CreateVendor v1Update = new CreateVendor();


        Response responseObject = middlewareServicesObject.v1ProfileUpdateForAddVendor(v1Update, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
       // NameMatch = responseObject.jsonPath().getJsonObject("nameMatchSuccess").toString();
     //   LeadId = responseObject.jsonPath().getJsonObject("leadId").toString();
        String StatusCode = responseObject.jsonPath().getJsonObject("statusCode").toString();
        Assert.assertEquals(StatusCode, "400");
        // xyz = responseObject.getStatusCode();
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create Add Vendor lead", priority = 1, dependsOnMethods = "TC000_GetSessionToken")
    public void TC0005_CreateVendorOnboardingWithEmptyvendorEmail() {

        AccountNumber = accObj.randomMobileNumberGenerator();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "ADD_VENDOR");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("businessName", "Name Business");
        body.put("ifsc", "CITI0000007");
        body.put("bankName", "CITI BANK");
        body.put("bankAccountNumber", AccountNumber);
        body.put("beneficiaryName", "Test");
        body.put("vendorName", "Puneet");
        body.put("vendorEmail", "");
        body.put("vendorMobile", MobileNo1);
        body.put("pan", "crzpk0964l");
        body.put("businessType", "INDIVIDUAL");
        body.put("category", "Individual Services");
        body.put("subcategory", "Photo & Video Making");

        // Update v1Update = new Update("AddVendor");
        CreateVendor v1Update = new CreateVendor();


        Response responseObject = middlewareServicesObject.v1ProfileUpdateForAddVendor(v1Update, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
      //  NameMatch = responseObject.jsonPath().getJsonObject("nameMatchSuccess").toString();
      String StatusCode = responseObject.jsonPath().getJsonObject("statusCode").toString();
        Assert.assertEquals(StatusCode, "400");
        // xyz = responseObject.getStatusCode();
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create Add Vendor lead", priority = 1, dependsOnMethods = "TC000_GetSessionToken")
    public void TC0006_CreateVendorOnboardingWithEmptyvendorMobile() {

        AccountNumber = accObj.randomMobileNumberGenerator();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "ADD_VENDOR");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("businessName", "Name Business");
        body.put("ifsc", "CITI0000007");
        body.put("bankName", "CITI BANK");
        body.put("bankAccountNumber", AccountNumber);
        body.put("beneficiaryName", "Test");
        body.put("vendorName", "Puneet");
        body.put("vendorEmail", "");
        body.put("vendorMobile", "");
        body.put("pan", "crzpk0964l");
        body.put("businessType", "INDIVIDUAL");
        body.put("category", "Individual Services");
        body.put("subcategory", "Photo & Video Making");

        // Update v1Update = new Update("AddVendor");
        CreateVendor v1Update = new CreateVendor();


        Response responseObject = middlewareServicesObject.v1ProfileUpdateForAddVendor(v1Update, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
        System.out.println("status code is" + statusCode);

        String dismsg = responseObject.jsonPath().getJsonObject("displayMessage").toString();
        Assert.assertEquals("Vendor Mobile Number cannot be empty.", dismsg);
        // xyz = responseObject.getStatusCode();
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create Add Vendor lead", priority = 1, dependsOnMethods = "TC000_GetSessionToken")
    public void TC0007_CreateVendorOnboardingWithEmptypan() {

        AccountNumber = accObj.randomMobileNumberGenerator();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "ADD_VENDOR");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("businessName", "Name Business");
        body.put("ifsc", "CITI0000007");
        body.put("bankName", "CITI BANK");
        body.put("bankAccountNumber", AccountNumber);
        body.put("beneficiaryName", "Test");
        body.put("vendorName", "Puneet");
        body.put("vendorEmail", "<EMAIL>");
        body.put("vendorMobile", "**********");
        body.put("pan", "");
        body.put("businessType", "INDIVIDUAL");
        body.put("category", "Individual Services");
        body.put("subcategory", "Photo & Video Making");

        // Update v1Update = new Update("AddVendor");
        CreateVendor v1Update = new CreateVendor();


        Response responseObject = middlewareServicesObject.v1ProfileUpdateForAddVendor(v1Update, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
        String StatusCode = responseObject.jsonPath().getJsonObject("statusCode").toString();
        Assert.assertEquals(statusCode, 400);
        // xyz = responseObject.getStatusCode();
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create Add Vendor lead", priority = 1, dependsOnMethods = "TC000_GetSessionToken")
    public void TC0008_CreateVendorOnboardingWithEmptybusinessType() {

        AccountNumber = accObj.randomMobileNumberGenerator();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "ADD_VENDOR");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("businessName", "Name Business");
        body.put("ifsc", "CITI0000007");
        body.put("bankName", "CITI BANK");
        body.put("bankAccountNumber", AccountNumber);
        body.put("beneficiaryName", "Test");
        body.put("vendorName", "Puneet");
        body.put("vendorEmail", "<EMAIL>");
        body.put("vendorMobile", "**********");
        body.put("pan", "crapl0897h");
        body.put("businessType", "");
        body.put("category", "Individual Services");
        body.put("subcategory", "Photo & Video Making");

        // Update v1Update = new Update("AddVendor");
        CreateVendor v1Update = new CreateVendor();


        Response responseObject = middlewareServicesObject.v1ProfileUpdateForAddVendor(v1Update, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
        String StatusCode = responseObject.jsonPath().getJsonObject("statusCode").toString();
        Assert.assertEquals(statusCode, 400);
        // xyz = responseObject.getStatusCode();
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create Add Vendor lead", priority = 1, dependsOnMethods = "TC000_GetSessionToken")
    public void TC0009_CreateVendorOnboardingWithEmptycategory() {

        AccountNumber = accObj.randomMobileNumberGenerator();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "ADD_VENDOR");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("businessName", "Name Business");
        body.put("ifsc", "CITI0000007");
        body.put("bankName", "CITI BANK");
        body.put("bankAccountNumber", AccountNumber);
        body.put("beneficiaryName", "Test");
        body.put("vendorName", "Puneet");
        body.put("vendorEmail", "<EMAIL>");
        body.put("vendorMobile", "**********");
        body.put("pan", "crapl0897h");
        body.put("businessType", "INDIVIDUAL");
        body.put("category", "");
        body.put("subcategory", "Photo & Video Making");

        // Update v1Update = new Update("AddVendor");
        CreateVendor v1Update = new CreateVendor();


        Response responseObject = middlewareServicesObject.v1ProfileUpdateForAddVendor(v1Update, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
        String StatusCode = responseObject.jsonPath().getJsonObject("statusCode").toString();
        Assert.assertEquals(StatusCode, "400");
        // xyz = responseObject.getStatusCode();
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create Add Vendor lead", priority = 1, dependsOnMethods = "TC000_GetSessionToken")
    public void TC0010_CreateVendorOnboardingWithEmptySubCategory() {

        AccountNumber = accObj.randomMobileNumberGenerator();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "ADD_VENDOR");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("businessName", "Name Business");
        body.put("ifsc", "CITI0000007");
        body.put("bankName", "CITI BANK");
        body.put("bankAccountNumber", AccountNumber);
        body.put("beneficiaryName", "Test");
        body.put("vendorName", "Puneet");
        body.put("vendorEmail", "<EMAIL>");
        body.put("vendorMobile", "**********");
        body.put("pan", "crapl0897h");
        body.put("businessType", "INDIVIDUAL");
        body.put("category", "Individual Services");
        body.put("subcategory", "");

        // Update v1Update = new Update("AddVendor");
        CreateVendor v1Update = new CreateVendor();


        Response responseObject = middlewareServicesObject.v1ProfileUpdateForAddVendor(v1Update, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
        String StatusCode = responseObject.jsonPath().getJsonObject("statusCode").toString();
        Assert.assertEquals(StatusCode, "400");
        // xyz = responseObject.getStatusCode();
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create Add Vendor lead", priority = 1, dependsOnMethods = "TC000_GetSessionToken")
    public void TC0011_CreateVendorOnboardingWithEmptyBankAccount() {

        AccountNumber = accObj.randomMobileNumberGenerator();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "ADD_VENDOR");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("businessName", "Name Business");
        body.put("ifsc", "CITI0000007");
        body.put("bankName", "CITI BANK");
        body.put("bankAccountNumber", "");
        body.put("beneficiaryName", "Test");
        body.put("vendorName", "Puneet");
        body.put("vendorEmail", "<EMAIL>");
        body.put("vendorMobile", "**********");
        body.put("pan", "crapl0897h");
        body.put("businessType", "INDIVIDUAL");
        body.put("category", "Individual Services");
        body.put("subcategory", "Photo & Video Making");

        // Update v1Update = new Update("AddVendor");
        CreateVendor v1Update = new CreateVendor();


        Response responseObject = middlewareServicesObject.v1ProfileUpdateForAddVendor(v1Update, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
        String StatusCode = responseObject.jsonPath().getJsonObject("statusCode").toString();
        Assert.assertEquals(StatusCode, "400");
        // xyz = responseObject.getStatusCode();
    }
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create Add Vendor lead", priority = 1, dependsOnMethods = "TC000_GetSessionToken")
    public void TC0012_CreateVendorOnboardingWithEmptybeneficiaryName() {

        AccountNumber = accObj.randomMobileNumberGenerator();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "ADD_VENDOR");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("businessName", "Name Business");
        body.put("ifsc", "CITI0000007");
        body.put("bankName", "CITI BANK");
        body.put("bankAccountNumber", AccountNumber);
        body.put("beneficiaryName", "");
        body.put("vendorName", "Puneet");
        body.put("vendorEmail", "<EMAIL>");
        body.put("vendorMobile", "**********");
        body.put("pan", "crapl0897h");
        body.put("businessType", "INDIVIDUAL");
        body.put("category", "Individual Services");
        body.put("subcategory", "Photo & Video Making");

        // Update v1Update = new Update("AddVendor");
        CreateVendor v1Update = new CreateVendor();


        Response responseObject = middlewareServicesObject.v1ProfileUpdateForAddVendor(v1Update, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
        String StatusCode = responseObject.jsonPath().getJsonObject("statusCode").toString();
        Assert.assertEquals(StatusCode, "400");
        // xyz = responseObject.getStatusCode();
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create Add Vendor lead", priority = 1, dependsOnMethods = "TC000_GetSessionToken")
    public void TC001_CreateVendorOnboarding() {

        AccountNumber = accObj.randomMobileNumberGenerator();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "ADD_VENDOR");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);
        body.put("businessName", "Name Business");
        body.put("ifsc", "CITI0000007");
        body.put("bankName", "CITI BANK");
        body.put("bankAccountNumber", AccountNumber);
        body.put("beneficiaryName", "Test");
        body.put("vendorName", "Puneet");
        body.put("vendorEmail", "<EMAIL>");
        body.put("vendorMobile", MobileNo1);
        body.put("pan", "crzpk0964l");
        body.put("businessType", "INDIVIDUAL");
        body.put("category", "Individual Services");
        body.put("subcategory", "Photo & Video Making");

        // Update v1Update = new Update("AddVendor");
        CreateVendor v1Update = new CreateVendor();


        Response responseObject = middlewareServicesObject.v1ProfileUpdateForAddVendor(v1Update, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
         LeadId = responseObject.jsonPath().getJsonObject("leadId").toString();

        Assert.assertEquals(statusCode, 200);
        // xyz = responseObject.getStatusCode();
    }

    @Test(priority = 3, description = "Fetch Lead Documents where Name Match Fails", dependsOnMethods = {"TC001_CreateVendorOnboarding", "TC000_GetSessionToken"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC002_FetchDocumentsAddVendor() {
        FetchLeadStatus(MID, ApplicantToken);
        if (LeadStatus.equals(ImagesPending)) {
            StatusDoc v1FetchDoc = new StatusDoc();
            Map<String, String> queryParams = new HashMap<String, String>();

            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "PAYTM_APP");
            queryParams.put("leadId", LeadId);
            queryParams.put("solutionSubType", "ADD_VENDOR");


            Map<String, String> headers = new HashMap<String, String>();
            headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);

            Response v1FetchDocResp = middlewareServicesObject.FetchDocProfileUpdate(v1FetchDoc, queryParams, headers);

            DocType = v1FetchDocResp.jsonPath().getJsonObject("docDetailsSet[0].docType").toString();
            LOGGER.info("Doc Type is : " + DocType);

            DocProvided = v1FetchDocResp.jsonPath().getJsonObject("docDetailsSet[0].possibleDocuments[0].docProvided").toString();
            LOGGER.info("Doc Provided is : " + DocProvided);

            LOGGER.info("    Fetching Document for Array  ");
            List<Object> DocumentArray = v1FetchDocResp.jsonPath().getList("docDetailsSet");
            LOGGER.info("This is Document Array : " + DocumentArray);
            LOGGER.info(" Array list Length : " + DocumentArray.size());

            int StatusCode = v1FetchDocResp.getStatusCode();
            Assert.assertEquals(StatusCode, 200);

            int APIStatus = v1FetchDocResp.jsonPath().getJsonObject("statusCode");
            Assert.assertEquals(APIStatus, 200);
        } else {
            LOGGER.info("Name Mathch Status is : " + NameMatch + " And Current Lead Status is : " + LeadStatus);
        }

    }


    @Test(priority = 0, description = "Upload Documents where Name Match Fails", dependsOnMethods = {"TC002_FetchDocumentsAddVendor", "TC001_CreateVendorOnboarding", "TC000_GetSessionToken"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_UploadDocumentAddVendor() {
        if (LeadStatus.equals(ImagesPending)) {

            uploadCancelledCheque cancelledCheque = new uploadCancelledCheque();

            Map<String, String> queryParams = new HashMap<String, String>();

            queryParams.put("solutionType", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "PAYTM_APP");
            queryParams.put("solutionLeadId", LeadId);
            queryParams.put("solutionTypeLevel2", "ADD_VENDOR");
            queryParams.put("docProvided", DocProvided);
            queryParams.put("docType", DocType);
            queryParams.put("type", "jpg");


            Map<String, String> headers = new HashMap<String, String>();
            headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);

            Response responseObject = middlewareServicesObject.uploadCancelCheque(cancelledCheque, queryParams, headers, CanCelledChequePDF);

            cancelledCheque.setRefId(responseObject.jsonPath().getString("refId"));
            cancelledCheque.setStatusCode(responseObject.jsonPath().getInt("statusCode"));

            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode, 200);
            LOGGER.info("Status Code is " + StatusCode);
        } else {
            LOGGER.info("Name Mathch Status is : " + NameMatch + " And Current Lead Status is : " + LeadStatus);
        }
    }

    @Test(priority = 0, description = "Fetch Add vendor lead in Panel", dependsOnMethods = {"TC003_UploadDocumentAddVendor", "TC002_FetchDocumentsAddVendor", "TC001_CreateVendorOnboarding", "TC000_GetSessionToken"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_FetchLeadDetailsAddVendor() {
        waitForLoad(6000);

        FetchLead v1FetchLeadObj = new FetchLead(LeadId);
        XMWToken = XMWCookie;

        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWToken);

        WorkFlowId = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.workflowStatusId").toString();

        leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        LOGGER.info("This is Current Lead Stage : " + leadStage);

        for (int i = 0; i <= 5; i++) {
            if (!leadStage.equals("DATA_ENTRY_ACTION_PENDING")) {
                FetchLead v1FetchLeadObjReap = new FetchLead(LeadId);
                Response v1FetchLeadRespReap = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObjReap, XMWToken);

                WorkFlowId = v1FetchLeadRespReap.jsonPath().getJsonObject("leadDetails.leadInfo.workflowStatusId").toString();

                leadStage = v1FetchLeadRespReap.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
                LOGGER.info("This is Current Lead Stage : " + leadStage);
            } else {
                CancelledChequeUUID = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.documents[0].uuid").toString();
                LOGGER.info(" Reallocating lead to Rajan ");
                ReallocatingAgent(LeadId, "1152");
                break;
            }
        }

    }

    @Test(priority = 0, description = "Approve Add vendor lead from panel", dependsOnMethods = {"TC004_FetchLeadDetailsAddVendor", "TC003_UploadDocumentAddVendor", "TC002_FetchDocumentsAddVendor", "TC001_CreateVendorOnboarding", "TC000_GetSessionToken"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_ApproveLeadAddVendor() throws SQLException {
        if (leadStage.equals("DATA_ENTRY_ACTION_PENDING")) {
            EditLead v1EditLeadObj = new EditLead(LeadId, P.TESTDATA.get("EditLeadFromPanel"));

            v1EditLeadObj.getProperties().setProperty("uuid", CancelledChequeUUID);
            v1EditLeadObj.getProperties().setProperty("uuids", CancelledChequeUUID);
            v1EditLeadObj.getProperties().setProperty("workflowStatusId", WorkFlowId);


            Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "9560526665", "1106992015", XMWToken, "application/json");

            int statusCode = responseObject.getStatusCode();
            Assert.assertEquals(statusCode, 200);

            String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
            Assert.assertTrue(LeadSatge.contains("PANEL_SUCCESS"));
        }
    }

}
*/
