package OCL.Individual.ProfileUpdate;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.v1.profile.update.bank_fetchChild;
import Request.MerchantService.v1.profile.update.doc.StatusDoc;
import Request.MerchantService.v1.profile.update.editBank;
import Request.MerchantService.v1.profile.update.lead.Status;
import Request.MerchantService.v2.upgradeMid.doc.uploadCancelledCheque;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FlowPGProfileUpdateBankUpdate extends BaseMethod
{
    private static final Logger LOGGER = LogManager.getLogger(FlowPGProfileUpdateBankUpdate.class);

    public static String ApplicantToken = "";
    List BankList = new ArrayList();
    Map Bank = new HashMap<>();
    public static String SavedBank = "";
    public static String MID = "";
    public static String MID2 = "";
    public static String LeadId = "";
    public static String LeadStatus = "";
    public static String LeadClosed = "LEAD_CLOSED";
    public static String LeadNotPresent = "LEAD_NOT_PRESENT";
    public static String ImagesPending = "IMAGES_PENDING";
    public static String AwaitingClose = "AWAITING_CLOSE";
    public static Boolean NameMatch ;
    public static String DocType = "";
    public static String DocProvided = "";
    public static String XMWToken = "";
    public static String WorkFlowId = "";
    public static String CancelledChequeUUID = "";
    public static String BankAcNo = "";
    public static String IFSC = "";
    public static String BankName = "";
//    public static String MobileNo1 = "**********";
    public static String MobileNo1 = "**********";
    public static String MobileNo2 = "**********";
    public static String App1CustId = "";
    public static String App2CustId = "";
    String leadStage = "";
    static String IsBankPresent = "";
    private File CanCelledChequePDF = new File(System.getProperty("user.dir")+"/PaytmImage.jpg");


    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    @Test(priority = 0)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC000_GetSessionToken() throws Exception {
        ApplicantToken = ApplicantToken(MobileNo1,"paytm@123");
        LOGGER.info("Applicant Session Token is : " + ApplicantToken);
        App1CustId = FetchUserDetails(MobileNo1,"mobile");
        MID = FetchMID(App1CustId);

        LOGGER.info(" Inside DB execution to reset lead : ");
        waitForLoad(7000);
        DBConnection.UpdateQueryToCloseLead(MobileNo1,"pg_profile_update");

       /* TestBase testBase =new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '"+MobileNo1+"' and status = '0' and solution_type='pg_profile_update';");
        int UpdateRes1 = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes1); */
    }

    public void FetchLeadStatus(String AppMID, String AppToken)
    {
        Status fetcStatus = new Status(P.TESTDATA.get("FetchStatusBankUpdate"));
        fetcStatus.getProperties().setProperty("bankDetailsLeadFetch","true");

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","INDIVIDUAL","BANK_DETAIL_UPDATE","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",AppToken,AppMID);

        LeadStatus = responseObject.jsonPath().getJsonObject("leadStatus").toString();

        if (responseObject.jsonPath().getJsonObject("leadId") != null)
        {
            LOGGER.info("Inside if Lead Id exist in fetch lead status");
            LeadId= responseObject.jsonPath().getJsonObject("leadId").toString();
        }
        LOGGER.info("Current Lead Status is : " +  LeadStatus );

    }

    @Test()
    public void PgCallback() throws Exception {
        DbName = DbStagingSprint;
        String CustID = FetchUserDetails(MobileNo1,"mobile");
      /*  String Query = "SELECT sai.solution_value from user_business_mapping ubm join related_business_solution_mapping rbsm join solution_additional_info sai on rbsm.id = ubm.related_business_solution_mapping_id and sai.solution_id = rbsm.solution_id where ubm.lead_id ='"+LeadId+"' and sai.solution_key = 'PG_REQUEST_ID';";
        TestBase testBase = new TestBase();
      String Result =  testBase.getResult(Query,"solution_value");
        ManualPgCallBack(CustID,Result,MID); */

        String requestid=DBConnection.getPgRequestId(LeadId);
        System.out.println("Request id is : "+requestid);
        ManualPgCallBack(CustID,requestid,MID);


//        String Query = DBConnection.SELECTQUERYJOIN(LeadId,"PG_REQUEST_ID");
//        System.out.println(Query);

    }

    @Test(priority = 0,description = "Fetch Lead status of bank details update",dependsOnMethods = "TC000_GetSessionToken")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_FetchLeadStatusBankUpdate()
    {
        FetchLeadStatus(MID,ApplicantToken);
    }

    @Test(priority = 0,description = "Fetch All saved Banks",dependsOnMethods = "TC001_FetchLeadStatusBankUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_FetchSavedBanksBankUpdate() throws JsonProcessingException {
        bank_fetchChild fetchBank = new bank_fetchChild();
        Response responseObject = middlewareServicesObject.fetchSavedBank(fetchBank, "INDIVIDUAL", "pg_profile_update", ApplicantToken);

        if (responseObject.jsonPath().getList("bankDetailsList") == null || responseObject.jsonPath().getList("bankDetailsList").isEmpty())
        {
            LOGGER.info("There is no Bank account Present for this merchant");
            IsBankPresent = "false";
            Map<String,String> BankCreated = new HashMap<>();
            BankCreated.put("bankAccountNumber",MobileNo1);
            BankCreated.put("bankName","INDUSIND BANK");
            BankCreated.put("ifsc","INDB0000488");

            ObjectMapper mapper = new ObjectMapper();
            SavedBank = mapper.writeValueAsString(BankCreated);
            LOGGER.info("Deserialized Response is  : " + SavedBank);

            BankAcNo =MobileNo1;
            IFSC = "INDB0000488";
            BankName = "INDUSIND BANK";

            LOGGER.info(" This is Bank A/C : " + BankAcNo + " This is IFSC : " + IFSC + " This is Bank Name : " + BankName);

        }
        else
        {
            IsBankPresent = "true";
            BankList = responseObject.jsonPath().getList("bankDetailsList");
            LOGGER.info("Captured Response is : " + BankList);
            Bank = (Map) BankList.get(0);

            ObjectMapper mapper = new ObjectMapper();
            SavedBank = mapper.writeValueAsString(Bank);
            LOGGER.info("Deserialized Response is  : " + SavedBank);

            BankAcNo = (String) Bank.get("bankAccountNumber");
            BankName = (String) Bank.get("bankName");
            IFSC = (String) Bank.get("ifsc");

            LOGGER.info(" This is Bank A/C : " + BankAcNo + " This is IFSC : " + IFSC + " This is Bank Name : " + BankName);
        }


    }

    @Test(priority = 0,description = "Create Lead Update Bank details",dependsOnMethods = "TC002_FetchSavedBanksBankUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_CreateLeadBankUpdate() throws Exception {
        editBank createNewLead = new editBank(P.TESTDATA.get("CreateLeadBankUpdatePayments"));

        if (LeadStatus.equals(LeadClosed) || LeadStatus.equals(LeadNotPresent))
        {
            Map<String, String> queryParams = new HashMap<String, String>();

            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "PAYTM_APP");
            queryParams.put("solutionType", "pg_profile_update");
            queryParams.put("solutionSubType", "BANK_DETAIL_UPDATE");

            Map<String, String> body = new HashMap<String, String>();

            body.put("bankDetails", SavedBank);
            body.put("mid",MID);

            if (IsBankPresent.contains("true"))
            {
                body.put("authorisedSignatory", "AASHIT SHARMA");
                body.put("businessName", "Aashit Sharma");
            }
            else if(IsBankPresent.contains("false"))
            {
                body.put("authorisedSignatory", "TestBeneficiary");
                body.put("businessName", "TestBeneficiary");
            }



            Map<String, String> headers = new HashMap<String, String>();

            headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);

            Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

            LeadId = responseObject.jsonPath().getString("leadId");
            LOGGER.info(" Created LeadId is " + " : " + LeadId);

            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode,200);

            NameMatch = responseObject.jsonPath().getJsonObject("nameMatchSuccess");
            LOGGER.info("Name Match Status is : " + NameMatch);
            PgCallback();
        }

        else
        {
            LOGGER.info("Lead is in non-terminating stage : " + LeadStatus );
        }


    }

    @Test(priority = 0,description = "Create Lead Update Bank details where Name Match Fails",dependsOnMethods = "TC003_CreateLeadBankUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_CreateBankUpdateNameMatchFail() throws Exception {
        waitForLoad(8000);
        MobileNo1 = MobileNo2;
        TC000_GetSessionToken();

        FetchLeadStatus(MID,ApplicantToken);
        TC002_FetchSavedBanksBankUpdate();

        editBank createNewLead = new editBank(P.TESTDATA.get("CreateLeadBankUpdatePayments"));

        if (LeadStatus.equals(LeadClosed) || LeadStatus.equals(LeadNotPresent) || LeadStatus.equals(ImagesPending) || !LeadStatus.equals(AwaitingClose))
        {
            Map<String, String> queryParams = new HashMap<String, String>();

            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "PAYTM_APP");
            queryParams.put("solutionType", "pg_profile_update");
            queryParams.put("solutionSubType", "BANK_DETAIL_UPDATE");

            Map<String, String> body = new HashMap<String, String>();

            body.put("bankDetails", SavedBank);
            body.put("authorisedSignatory", "Harish Garg");
            body.put("businessName", "Harish Garg");
            body.put("mid",MID);

            Map<String, String> headers = new HashMap<String, String>();

            headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);

            Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);
            LeadId = responseObject.jsonPath().getString("leadId");
            LOGGER.info(" Created LeadId is " + " : " + LeadId);

            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode,200);

            NameMatch = responseObject.jsonPath().getJsonObject("nameMatchSuccess");
            LOGGER.info("Name Match Status is : " + NameMatch);

            FetchLeadStatus(MID,ApplicantToken);

        }

        else
        {
            NameMatch = false ;
            LOGGER.info("Lead is in Awaiting Closed stage : " + LeadStatus + "Name Match : " + NameMatch );
        }
    }


    @Test(priority = 0,description = "Fetch Lead Documents where Name Match Fails",dependsOnMethods = "TC004_CreateBankUpdateNameMatchFail")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_FetchDocumentsBankUpdate()
    {
        if(!NameMatch && LeadStatus.equals(ImagesPending))
        {
            StatusDoc v1FetchDoc = new StatusDoc();
            Map<String, String> queryParams = new HashMap<String, String>();

            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "PAYTM_APP");
            queryParams.put("leadId", LeadId);
            queryParams.put("solutionSubType", "BANK_DETAIL_UPDATE");


            Map<String, String> headers = new HashMap<String, String>();
            headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);

            Response v1FetchDocResp = middlewareServicesObject.FetchDocProfileUpdate(v1FetchDoc, queryParams, headers);

            DocType = v1FetchDocResp.jsonPath().getJsonObject("docDetailsSet[0].docType").toString();
            LOGGER.info("Doc Type is : " + DocType);

            DocProvided = v1FetchDocResp.jsonPath().getJsonObject("docDetailsSet[0].possibleDocuments[0].docProvided").toString();
            LOGGER.info("Doc Provided is : " + DocProvided);

            LOGGER.info("    Fetching Document for Array  ");
            List<Object> DocumentArray = v1FetchDocResp.jsonPath().getList("docDetailsSet") ;
            LOGGER.info("This is Document Array : " + DocumentArray);
            LOGGER.info(" Array list Length : " + DocumentArray.size());

            int StatusCode = v1FetchDocResp.getStatusCode();
            Assert.assertEquals(StatusCode,200);

            int APIStatus = v1FetchDocResp.jsonPath().getJsonObject("statusCode");
            Assert.assertEquals(APIStatus,200);
        }
        else
        {
            LOGGER.info("Name Mathch Status is : " + NameMatch + " And Current Lead Status is : " + LeadStatus);
        }

    }


    @Test(priority = 0,description = "Upload Documents where Name Match Fails",dependsOnMethods = "TC005_FetchDocumentsBankUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC006_UploadDocumentBankUpdate()
    {
        if(!NameMatch && LeadStatus.equals(ImagesPending))
        {

            uploadCancelledCheque cancelledCheque = new uploadCancelledCheque();

            Map<String, String> queryParams = new HashMap<String, String>();

            queryParams.put("solutionType", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "PAYTM_APP");
            queryParams.put("solutionLeadId", LeadId);
            queryParams.put("solutionTypeLevel2", "BANK_DETAIL_UPDATE");
            queryParams.put("docProvided",DocProvided);
            queryParams.put("docType",DocType);
            queryParams.put("type","jpg");


            Map<String, String> headers = new HashMap<String, String>();
            headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);

            Response responseObject = middlewareServicesObject.uploadCancelCheque(cancelledCheque,queryParams,headers , CanCelledChequePDF);

            cancelledCheque.setRefId(responseObject.jsonPath().getString("refId"));
            cancelledCheque.setStatusCode(responseObject.jsonPath().getInt("statusCode"));

            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode, 200);
            LOGGER.info("Status Code is " + StatusCode);
        }
        else
        {
            LOGGER.info("Name Mathch Status is : " + NameMatch + " And Current Lead Status is : " + LeadStatus);
        }
    }

    @Test(priority = 0,description = "Fetch Banks Details update lead in Panel",dependsOnMethods = "TC006_UploadDocumentBankUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_FetchLeadDetailsBankUpdate()
    {
        waitForLoad(6000);

        FetchLead v1FetchLeadObj = new FetchLead(LeadId);
        XMWToken = XMWCookie ;

        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWToken);

        WorkFlowId = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.workflowStatusId").toString();

        leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        LOGGER.info("This is Current Lead Stage : " + leadStage);

        for(int i = 0 ; i<=5;i++)
        {
            if (!leadStage.equals("DATA_ENTRY_ACTION_PENDING"))
            {
                FetchLead v1FetchLeadObjReap = new FetchLead(LeadId);
                Response v1FetchLeadRespReap = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObjReap, XMWToken);

                WorkFlowId = v1FetchLeadRespReap.jsonPath().getJsonObject("leadDetails.leadInfo.workflowStatusId").toString();

                leadStage = v1FetchLeadRespReap.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
                LOGGER.info("This is Current Lead Stage : " + leadStage);
            }
            else {
                CancelledChequeUUID = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.documents[0].uuid").toString();
                LOGGER.info(" Reallocating lead to Rajan ");
                ReallocatingAgent(LeadId, "1152");
                break;
            }
        }

    }

    @Test(priority = 0,description = "Submit bank details update lead from panel",dependsOnMethods = "TC007_FetchLeadDetailsBankUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_SubmitLeadBankUpdate() throws Exception {
        if (leadStage.equals("DATA_ENTRY_ACTION_PENDING"))
        {
            EditLead v1EditLeadObj = new EditLead(LeadId, P.TESTDATA.get("SubmitbankDetailsUpdate"));

            v1EditLeadObj.getProperties().setProperty("bankAccountNumber", BankAcNo);
            v1EditLeadObj.getProperties().setProperty("bankName", BankName);
            v1EditLeadObj.getProperties().setProperty("ifscCode", IFSC);
            v1EditLeadObj.getProperties().setProperty("pgMID", MID);
            v1EditLeadObj.getProperties().setProperty("mobileNumber", MobileNo2);
            v1EditLeadObj.getProperties().setProperty("uuid", CancelledChequeUUID);
            v1EditLeadObj.getProperties().setProperty("uuids", CancelledChequeUUID);
            v1EditLeadObj.getProperties().setProperty("workflowStatusId", WorkFlowId);


            Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "**********", "**********", XMWToken, "application/json");

            int statusCode = responseObject.getStatusCode();
            Assert.assertEquals(statusCode, 200);

            String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
            Assert.assertTrue(LeadSatge.contains("PANEL_SUCCESS"));
            PgCallback();

        }
    }

}
