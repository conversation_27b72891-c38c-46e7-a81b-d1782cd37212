package OCL.Individual.ProfileUpdate;

import Request.MerchantService.v1.profile.Update;
import Request.MerchantService.v1.profile.update.Commissiontncs;
import Request.MerchantService.v1.profile.update.lead.Status;
import Request.PG.CreateMerchantOnPG;
import Request.PG.MarkingMIDInactiveOnPG;
import Request.oAuth.oAuthWormhole.CreateUser;
import Services.MechantService.MiddlewareServices;
import Services.PG.PGServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FlowMerchantReactivation extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(FlowMerchantReactivation.class);
    //MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    PGServices pgServicesObject = new PGServices();

    public static String ApplicantToken = "";
    public static String MID = "";
    public static String MobileNo = "";
    public static String App1CustId = "";
    public static String LeadStatus = "";
    public static String requestID = "";
    PGServices createMerchant50k = new PGServices();
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    public static String LeadId = "";


    @Test(priority = 0, description = "Create Applicant on Oauth", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_CreateApplicantOauth() {
        //generating new Number
        Utilities accObj = new Utilities();
        MobileNo = accObj.randomMobileNumberGenerator();
        LOGGER.info("New Number is : " + MobileNo);
        CreateUser OauthObj = new CreateUser();
        OauthObj.setHeader("Content-Type", "application/json");
        OauthObj.getProperties().setProperty("mobile", MobileNo);
        OauthObj.getProperties().setProperty("loginPassword", "paytm@123");
        Response OauthResp = OauthObj.callAPI();
        int StatusCode = OauthResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }


    @Test(priority = 1, description = "Create Merchant on PG", groups = {"Regression"}, dependsOnMethods = "TC001_CreateApplicantOauth")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC002_createMerchantOnPG() {
        //getting CustID
        App1CustId = FetchUserDetails(MobileNo, "phone");
        LOGGER.info("custID is  : " + App1CustId);
        Utilities accObj = new Utilities();
        requestID = accObj.randomMobileNumberGenerator();

        CreateMerchantOnPG CreateMerch = new CreateMerchantOnPG(P.TESTDATA.get("CreateMerchantOnPG"));

        Map<String, String> Body = new HashMap<>();
        Body.put("REQUEST_ID", MobileNo);
        Body.put("USER_NAME", MobileNo);
        Body.put("CUST_ID", App1CustId);
        Body.put("KYB_ID", App1CustId);
        Body.put("BUSINESS_TYPE", "INDIVIDUAL");
        Body.put("MERCHANT_NAME", "Automation User");
        Response createMID50k = createMerchant50k.createMerchantOnPG(CreateMerch, Body);
        String responeMessage = createMID50k.jsonPath().getJsonObject("STATUS");
        Assert.assertEquals(responeMessage, "MID generation is in progress");


    }

    @Test(description = "Fetching Session Token and MID", dependsOnMethods = "TC002_createMerchantOnPG")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_GetMID() throws InterruptedException {
        ApplicantToken = ApplicantToken("9891497839", "paytm@123");
        LOGGER.info("Applicant Session Token is : " + ApplicantToken);
        App1CustId = FetchUserDetails(MobileNo, "mobile");
        Thread.sleep(10000);
        MID = FetchMIDFromPG(App1CustId, MobileNo,ApplicantToken);

        System.out.println("Mid is " + MID);
    }

    @Test(description = "Mark MID Inactive on PG", dependsOnMethods = "TC003_GetMID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_MakeMIDInactive() {

        MarkingMIDInactiveOnPG MakingMIDIncative = new MarkingMIDInactiveOnPG(P.TESTDATA.get("MarkMIDInactive"));

        Map<String, String> Body = new HashMap<>();
        Body.put("mid", MID);
        Response createMID50k = createMerchant50k.MarkingMIDInactiveOnPG(MakingMIDIncative, Body);
        String responeMessage = createMID50k.jsonPath().getJsonObject("status");
        Assert.assertEquals(responeMessage, "MID is blocked successfully.");

    }

    @Test(description = "Lead status", dependsOnMethods = "TC004_MakeMIDInactive")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_GetLeadStatus() {

        Status fetcStatus = new Status(P.TESTDATA.get("FetchStatusMerchantReactivation"));
        fetcStatus.getProperties().setProperty("mid", MID);


        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus, "pg_profile_update", "INDIVIDUAL", "MERCHANT_REACTIVATION", "application/json", "BabaBlackSheepWeAreInShitDeep", ApplicantToken, MID);

        LeadStatus = responseObject.jsonPath().getJsonObject("leadStatus").toString();

        if (responseObject.jsonPath().getJsonObject("leadId") != null) {
            LOGGER.info("Inside if Lead Id exist in fetch lead status");
            LeadId = responseObject.jsonPath().getJsonObject("leadId").toString();
        }
        LOGGER.info("Current Lead Status is : " + LeadStatus);
        Assert.assertEquals(LeadStatus, "LEAD_NOT_PRESENT");

    }

    @Test(description = "Lead status", dependsOnMethods = "TC004_MakeMIDInactive")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_1_GetLeadStatusWithEmptySolutionType() {

        Status fetcStatus = new Status(P.TESTDATA.get("FetchStatusMerchantReactivation"));
        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus, "", "INDIVIDUAL", "MERCHANT_REACTIVATION", "application/json", "BabaBlackSheepWeAreInShitDeep", ApplicantToken, MID);
        int statusCode = responseObject.getStatusCode();
        LOGGER.info("Status is : " + statusCode);
        Assert.assertEquals(statusCode, 500);

    }
    @Test(description = "Lead status", dependsOnMethods = "TC004_MakeMIDInactive")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_2_GetLeadStatusWithEmptyEntityType() {

        Status fetcStatus = new Status(P.TESTDATA.get("FetchStatusMerchantReactivation"));
        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus, "pg_profile_update", "", "MERCHANT_REACTIVATION", "application/json", "BabaBlackSheepWeAreInShitDeep", ApplicantToken, MID);
        int statusCode = responseObject.getStatusCode();
        LOGGER.info("Status is : " + statusCode);
        Assert.assertEquals(statusCode, 400);

    }

    @Test(description = "Lead status", dependsOnMethods = "TC004_MakeMIDInactive")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_3_GetLeadStatusWithEmptySolutionSubtype() {

        Status fetcStatus = new Status(P.TESTDATA.get("FetchStatusMerchantReactivation"));
        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus, "pg_profile_update", "INDIVIDUAL", "", "application/json", "BabaBlackSheepWeAreInShitDeep", ApplicantToken, MID);
        int statusCode = responseObject.getStatusCode();
        LOGGER.info("Status is : " + statusCode);
        Assert.assertEquals(statusCode, 500);

    }

    @Test(description = "Lead status", dependsOnMethods = "TC004_MakeMIDInactive")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_4_GetLeadStatusWithEmptySessionToken() {

        Status fetcStatus = new Status(P.TESTDATA.get("FetchStatusMerchantReactivation"));
        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus, "pg_profile_update", "INDIVIDUAL", "MERCHANT_REACTIVATION", "application/json", "BabaBlackSheepWeAreInShitDeep", "", MID);
        int statusCode = responseObject.getStatusCode();
        LOGGER.info("Status is : " + statusCode);
        Assert.assertEquals(statusCode, 401);

    }

    @Test(description = "Lead status", dependsOnMethods = "TC004_MakeMIDInactive")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_5_GetLeadStatusWithEmptyMID() {

        Status fetcStatus = new Status(P.TESTDATA.get("FetchStatusMerchantReactivation"));
        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus, "pg_profile_update", "INDIVIDUAL", "MERCHANT_REACTIVATION", "application/json", "BabaBlackSheepWeAreInShitDeep", ApplicantToken, "");
        int statusCode = responseObject.getStatusCode();
        LOGGER.info("Status is : " + statusCode);
        Assert.assertEquals(statusCode, 400);

    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create lead Merchant Reactivation", priority = 1, dependsOnMethods = "TC005_GetLeadStatus")
    public void TC006_CreateLeadMerchantReactivation() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "MERCHANT_REACTIVATION");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);

        Update createLead = new Update(P.TESTDATA.get("CreateLeadMerchantReactivation"));
        Response responseObject = middlewareServicesObject.merchnantReactivation(createLead, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
        String displayMessage = responseObject.jsonPath().getJsonObject("displayMessage").toString();

        Assert.assertEquals(displayMessage, "Update Request Submitted Successfully");
        // xyz = responseObject.getStatusCode();
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create lead Merchant Reactivation", priority = 1, dependsOnMethods = "TC005_GetLeadStatus")
    public void TC007_StopMerchantReactivationWithEmptyMID() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "MERCHANT_REACTIVATION");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "");

        Update createLead = new Update(P.TESTDATA.get("CreateLeadMerchantReactivation"));
        Response responseObject = middlewareServicesObject.merchnantReactivation(createLead, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
      //  String displayMessage = responseObject.jsonPath().getJsonObject("displayMessage").toString();

        Assert.assertEquals(statusCode, 400);
        // xyz = responseObject.getStatusCode();
    }

    @Test(priority = 0, description = "Get Commision T&C", groups = {"Regression"}, dependsOnMethods = "TC005_GetLeadStatus")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC013_GetCommisonMIDInActive() {
        Commissiontncs CommissiontncsObj = new Commissiontncs();

        CommissiontncsObj.setHeader("Content-Type", "application/json");
        CommissiontncsObj.setHeader("session_token", ApplicantToken);
        CommissiontncsObj.addFormParameter("solution", "pg_profile_update");

        CommissiontncsObj.addFormParameter("entityType","INDIVIDUAL");
        CommissiontncsObj.addFormParameter("mid", MID);
        CommissiontncsObj.addFormParameter("channel", "P4B APP");
        CommissiontncsObj.addFormParameter("solutionSubType", "MERCHANT_REACTIVATION");

        Response CommissiontncsResp = CommissiontncsObj.callAPI();
        int StatusCode = CommissiontncsResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create lead Merchant Reactivation", priority = 1, dependsOnMethods = "TC005_GetLeadStatus")

    public void TC008_StopMerchantReactivationWithEmptySolutionSubtype() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);

        Update createLead = new Update(P.TESTDATA.get("CreateLeadMerchantReactivation"));
        Response responseObject = middlewareServicesObject.merchnantReactivation(createLead, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
        String displayMessage = responseObject.jsonPath().getJsonObject("displayMessage").toString();

        Assert.assertEquals(displayMessage, "solutionSubType can not be empty for solution type : pg_profile_update");
        // xyz = responseObject.getStatusCode();
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create lead Merchant Reactivation", priority = 1, dependsOnMethods = "TC005_GetLeadStatus")
    public void TC009_StopMerchantReactivationWithEmptySolutionType() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "MERCHANT_REACTIVATION");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);

        Update createLead = new Update(P.TESTDATA.get("CreateLeadMerchantReactivation"));
        Response responseObject = middlewareServicesObject.merchnantReactivation(createLead, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
        String displayMessage = responseObject.jsonPath().getJsonObject("displayMessage").toString();

        Assert.assertEquals(displayMessage, "No enum constant com.paytm.oe.enums.SolutionType.");
        // xyz = responseObject.getStatusCode();
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create lead Merchant Reactivation", priority = 1, dependsOnMethods = "TC005_GetLeadStatus")
    public void TC010_StopMerchantReactivationWithEmptyEntity() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "MERCHANT_REACTIVATION");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);

        Update createLead = new Update(P.TESTDATA.get("CreateLeadMerchantReactivation"));
        Response responseObject = middlewareServicesObject.merchnantReactivation(createLead, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
        String displayMessage = responseObject.jsonPath().getJsonObject("displayMessage").toString();

        Assert.assertEquals(displayMessage, "We noticed a problem with your entity type, please reach out to Merchant help desk for assistance.");
        // xyz = responseObject.getStatusCode();
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create lead Merchant Reactivation", priority = 1, dependsOnMethods = "TC005_GetLeadStatus")
    public void TC011_StopMerchantReactivationWithEmptySessionToken() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "MERCHANT_REACTIVATION");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", "");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);

        Update createLead = new Update(P.TESTDATA.get("CreateLeadMerchantReactivation"));
        Response responseObject = middlewareServicesObject.merchnantReactivation(createLead, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
       // String displayMessage = responseObject.jsonPath().getJsonObject("displayMessage").toString();

        Assert.assertEquals(statusCode, 401);
        // xyz = responseObject.getStatusCode();
    }
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "Create lead Merchant Reactivation", priority = 1, dependsOnMethods = "TC005_GetLeadStatus")
    public void TC012_StopMerchantReactivationWithSameRequestAgain() {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", "MERCHANT_REACTIVATION");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", ApplicantToken);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", MID);

        Update createLead = new Update(P.TESTDATA.get("CreateLeadMerchantReactivation"));
        Response responseObject = middlewareServicesObject.merchnantReactivation(createLead, queryParams, headers, body);
        int statusCode = responseObject.getStatusCode();
        String displayMessage = responseObject.jsonPath().getJsonObject("displayMessage").toString();

        Assert.assertEquals(displayMessage, "Your details could not be saved. We already have a application in process, please continue with same.");
        // xyz = responseObject.getStatusCode();
    }

    @Test(priority = 0, description = "Get Commision T&C", groups = {"Regression"}, dependsOnMethods = "TC005_GetLeadStatus")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC018_GetCommisonMIDActive() {
        Commissiontncs CommissiontncsObj = new Commissiontncs();

        CommissiontncsObj.setHeader("Content-Type", "application/json");
        CommissiontncsObj.setHeader("session_token", ApplicantToken);
        CommissiontncsObj.addFormParameter("solution", "pg_profile_update");

        CommissiontncsObj.addFormParameter("entityType","INDIVIDUAL");
        CommissiontncsObj.addFormParameter("mid", MID);
        CommissiontncsObj.addFormParameter("channel", "P4B APP");
        CommissiontncsObj.addFormParameter("solutionSubType", "MERCHANT_REACTIVATION");

        Response CommissiontncsResp = CommissiontncsObj.callAPI();
        int StatusCode = CommissiontncsResp.getStatusCode();
//        String displayMessage = CommissiontncsResp.jsonPath().getJsonObject("displayMessage").toString();
        Assert.assertEquals(StatusCode, 200);

        //Assert.assertEquals(displayMessage, "Merchant account is already active");
        // xyz = responseObject.getStatusCode();
    }

    @Test(priority = 0, description = "Get Commision T&C", groups = {"Regression"}, dependsOnMethods = "TC005_GetLeadStatus")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC014_GetCommisonWithEmptySession() {
        Commissiontncs CommissiontncsObj = new Commissiontncs();

        CommissiontncsObj.setHeader("Content-Type", "application/json");
        CommissiontncsObj.setHeader("session_token", "");
        CommissiontncsObj.addFormParameter("solution", "pg_profile_update");

        CommissiontncsObj.addFormParameter("entityType","INDIVIDUAL");
        CommissiontncsObj.addFormParameter("mid", MID);
        CommissiontncsObj.addFormParameter("channel", "P4B APP");
        CommissiontncsObj.addFormParameter("solutionSubType", "MERCHANT_REACTIVATION");

        Response CommissiontncsResp = CommissiontncsObj.callAPI();
        int StatusCode = CommissiontncsResp.getStatusCode();
        Assert.assertEquals(StatusCode, 401);
    }

    @Test(priority = 0, description = "Get Commision T&C", groups = {"Regression"}, dependsOnMethods = "TC005_GetLeadStatus")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC015_GetCommisonWithEmptySolution() {
        Commissiontncs CommissiontncsObj = new Commissiontncs();

        CommissiontncsObj.setHeader("Content-Type", "application/json");
        CommissiontncsObj.setHeader("session_token", ApplicantToken);
        CommissiontncsObj.addFormParameter("solution", "");

        CommissiontncsObj.addFormParameter("entityType","INDIVIDUAL");
        CommissiontncsObj.addFormParameter("mid", MID);
        CommissiontncsObj.addFormParameter("channel", "P4B APP");
        CommissiontncsObj.addFormParameter("solutionSubType", "MERCHANT_REACTIVATION");

        Response CommissiontncsResp = CommissiontncsObj.callAPI();
        int StatusCode = CommissiontncsResp.getStatusCode();
        Assert.assertEquals(StatusCode, 500);
    }

    @Test(priority = 0, description = "Get Commision T&C", groups = {"Regression"}, dependsOnMethods = "TC005_GetLeadStatus")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC016_GetCommisonWithEmptyEntity() {
        Commissiontncs CommissiontncsObj = new Commissiontncs();

        CommissiontncsObj.setHeader("Content-Type", "application/json");
        CommissiontncsObj.setHeader("session_token", ApplicantToken);
        CommissiontncsObj.addFormParameter("solution", "pg_profile_update");

        CommissiontncsObj.addFormParameter("entityType","");
        CommissiontncsObj.addFormParameter("mid", MID);
        CommissiontncsObj.addFormParameter("channel", "P4B APP");
        CommissiontncsObj.addFormParameter("solutionSubType", "MERCHANT_REACTIVATION");

        Response CommissiontncsResp = CommissiontncsObj.callAPI();
        int StatusCode = CommissiontncsResp.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
    }

    @Test(priority = 0, description = "Get Commision T&C", groups = {"Regression"}, dependsOnMethods = "TC005_GetLeadStatus")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC017_GetCommisonWithEmptySolutionSubType() {
        Commissiontncs CommissiontncsObj = new Commissiontncs();

        CommissiontncsObj.setHeader("Content-Type", "application/json");
        CommissiontncsObj.setHeader("session_token", ApplicantToken);
        CommissiontncsObj.addFormParameter("solution", "pg_profile_update");

        CommissiontncsObj.addFormParameter("entityType","INDIVIDUAL");
        CommissiontncsObj.addFormParameter("mid", MID);
        CommissiontncsObj.addFormParameter("channel", "P4B APP");
        CommissiontncsObj.addFormParameter("solutionSubType", "");

        Response CommissiontncsResp = CommissiontncsObj.callAPI();
        int StatusCode = CommissiontncsResp.getStatusCode();
        Assert.assertEquals(StatusCode, 500);
    }

}

