package OCL.Individual.ProfileUpdate;

import Request.KYB.KybEdit;
import Request.KYB.KybSave;
import Request.MerchantService.v1.profile.update.editBank;
import Request.MerchantService.v1.profile.update.lead.Status;
import Request.PG.CreateMerchantOnPG;
import Services.KYB.KybServices;
import Services.MechantService.MiddlewareServices;
import Services.PG.PGServices;
import Services.Utilities.TestBase;
import Services.Utilities.Utilities;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.opencsv.CSVWriter;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.AfterClass;
import org.testng.annotations.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowAddAddress extends BaseMethod {

    public static String MobileNo = "";
    public static String App1CustId = "";
    public static String PgMID = "";
    public static String PgMBID= "";
    public static String PgKey= "";
    public static String KybID= "";
    public static String LeadStatus= "";
    public static String LeadId= "";
    public static String PAN = "";
    public static String LeadClosed = "LEAD_CLOSED";
    public static String LeadNotPresent = "LEAD_NOT_PRESENT";
    public static String ImagesPending = "IMAGES_PENDING";
    public static String AwaitingClose = "AWAITING_CLOSE";
    public static String ApplicantToken = "";
    Faker GenerateFake = new Faker();

    public String lineOne = GenerateFake.address().streetAddress();


    private static final Logger LOGGER = LogManager.getLogger(FlowAddAddress.class);
    PGServices createMerchant50k = new PGServices();
    KybServices kybServices = new KybServices();
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    Utilities accObj = new Utilities();


    @Test(priority = 0, description = "Create Applicant on Oauth", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_CreateApplicantOauth() {
        //generating new Number

        MobileNo = accObj.randomMobileNumberGenerator();
        LOGGER.info("New Number is : " + MobileNo);
        CreateApplicantOauth(MobileNo);

        App1CustId = FetchUserDetails(MobileNo, "phone");
        LOGGER.info("custID is  : " + App1CustId);


    }

    @Test(priority = 0, description = "Create Merchant on Kyb", groups = {"Regression"},dependsOnMethods = "TC001_CreateApplicantOauth")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC002_createMerchantKyb() {
        KybSave kybSave = new KybSave();

        Map<String,String> Qurery = new HashMap<>();

        Map<String,String> Body = new HashMap<>();
        Body.put("userId",App1CustId);
        Body.put("accountNumber",MobileNo);

        Response KybSaveResp = kybServices.KybSave(kybSave,Qurery,Body);

        KybID = KybSaveResp.jsonPath().getString("profileId");
        LOGGER.info("KYB Id is : " +KybID);

        Assert.assertEquals(KybSaveResp.getStatusCode(),200);
    }


    @Test(priority = 0, description = "Create Merchant on PG", groups = {"Regression"},dependsOnMethods = "TC002_createMerchantKyb")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_createMerchantOnPG() {

        PAN =  accObj.randomIndividualPANValueGenerator();
        LOGGER.info("PAN no. is : " +PAN);

        CreateMerchantOnPG CreateMerch = new CreateMerchantOnPG(P.TESTDATA.get("CreateMerchantOnPG"));

        Map<String,String> Body = new HashMap<>();
        Body.put("REQUEST_ID", MobileNo);
        Body.put("USER_NAME", MobileNo);
        Body.put("CUST_ID", App1CustId);
        Body.put("KYB_ID", KybID);
        Body.put("BUSINESS_TYPE", "INDIVIDUAL");
        Body.put("MERCHANT_NAME", "AASHIT SHARMA");
        Response createMID50k = createMerchant50k.createMerchantOnPG(CreateMerch,Body);
        String responeMessage = createMID50k.jsonPath().getJsonObject("STATUS");
        Assert.assertEquals(responeMessage, "MID generation is in progress");



    }

    @Test(priority = 0, description = "PG Callback", groups = {"Regression"},dependsOnMethods = "TC003_createMerchantOnPG")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_PgCallback() throws SQLException, JsonProcessingException {

        waitForLoad(3000);
        DbName = DbStaging6;
        String query = "SELECT DISTINCT callback_request from pg_callback_info where cust_id = " +App1CustId+ " LIMIT 1";
        LOGGER.info("Extpected Quesry is : "+query);

        try {
            /*TestBase testBase = new TestBase();
            String Result = testBase.getResult(query, "callback_request");
            Reporter.log("Output of DB Query : " + Result, true);
            ObjectMapper mapper = new ObjectMapper();
            Map<String, String> map = mapper.readValue(Result, new TypeReference<Map<String, String>>() {
            });
            LOGGER.info("Created Map is : " + map + "\n");

            PgMID = map.get("mid");
            PgMBID = map.get("mbid");
            PgKey = map.get("merchantKey");
            LOGGER.info("\n PG MBID is : " + PgMBID + "\n PG Key is : " + PgKey); */
        }
        catch (Exception e)
        {
            LOGGER.info("This is Exception : " +e);
        }
    }

    @Test(priority = 0, description = "Edit Merchant on Kyb", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_EditMerchantKyb() {
        KybEdit kybEdit = new KybEdit(App1CustId,"unified_payment_merchant");
        Map<String,String> Query = new HashMap<>();

        Map<String,String> Body = new HashMap<>();
        Body.put("pgMid",PgMID);
        Body.put("pgMbid",PgMBID);
        Body.put("pgKey",PgKey);

        Response KybEditResp = kybServices.KybEdit(kybEdit,Query,Body);

        Assert.assertEquals(KybEditResp.getStatusCode(),200);
    }

    public void FetchLeadStatus(String AppMID, String AppToken)
    {
        Status fetcStatus = new Status(P.TESTDATA.get("FetchAddAddress"));
        fetcStatus.getProperties().setProperty("addressLeadFetch","true");

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","INDIVIDUAL","ADD_ADDRESS","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",AppToken,AppMID);

        LeadStatus = responseObject.jsonPath().getJsonObject("leadStatus").toString();

        if (responseObject.jsonPath().getJsonObject("leadId") != null)
        {
            LOGGER.info("Inside if Lead Id exist in fetch lead status");
            LeadId= responseObject.jsonPath().getJsonObject("leadId").toString();
        }
        LOGGER.info("Current Lead Status is : " +  LeadStatus );

    }

    @Test(priority = 0,dependsOnMethods = "TC005_EditMerchantKyb")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC006_GetSessionToken() throws SQLException {
        ApplicantToken = ApplicantToken(MobileNo, "paytm@123");
    }

    @Test(priority = 0,description = "Fetch Lead status of Add Address with wrong MID",dependsOnMethods = "TC006_GetSessionToken")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_01_FetchLeadStatusAddAddressWrongMid()
    {
        FetchLeadStatus("AashitMID99538286",ApplicantToken);
    }

    @Test(priority = 0,description = "Fetch Lead status of Add Address with wrong Token",dependsOnMethods = "TC006_GetSessionToken")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_02_FetchLeadStatusAddAddressWrongToken()
    {
        Status fetcStatus = new Status(P.TESTDATA.get("FetchAddAddress"));
        fetcStatus.getProperties().setProperty("addressLeadFetch","true");

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","INDIVIDUAL","ADD_ADDRESS","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep","ApplicantToken",PgMID);

        Assert.assertEquals(responseObject.getStatusCode(),410);
    }

    @Test(priority = 0,description = "Fetch Lead status of Add Address with Empty MID",dependsOnMethods = "TC006_GetSessionToken")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_03_FetchLeadStatusAddAddressEmptyMid()
    {
        Status fetcStatus = new Status(P.TESTDATA.get("FetchAddAddress"));
        fetcStatus.getProperties().setProperty("addressLeadFetch","true");

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","INDIVIDUAL","ADD_ADDRESS","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantToken,"");

        Assert.assertEquals(responseObject.getStatusCode(),400);
    }

    @Test(priority = 0,description = "Fetch Lead status of Add Address with Empty Token",dependsOnMethods = "TC006_GetSessionToken")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_04_FetchLeadStatusAddAddressEmptyToken()
    {
        Status fetcStatus = new Status(P.TESTDATA.get("FetchAddAddress"));
        fetcStatus.getProperties().setProperty("addressLeadFetch","true");

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","INDIVIDUAL","ADD_ADDRESS","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep","",PgMID);

        Assert.assertEquals(responseObject.getStatusCode(),401);
    }

    @Test(priority = 0,description = "Fetch Lead status of Add Address",dependsOnMethods = "TC006_GetSessionToken")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_05_FetchLeadStatusAddAddress()
    {
        FetchLeadStatus(PgMID,ApplicantToken);
    }

    @Test(priority = 0,description = "Create Lead Add Address details",dependsOnMethods = "TC007_05_FetchLeadStatusAddAddress")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_01_CreateLeadAddAddressEmptyMid() {
        editBank createNewLead = new editBank(P.TESTDATA.get("AddAddress"));

        if (LeadStatus.equals(LeadNotPresent)) {
            Map<String, String> queryParams = new HashMap<String, String>();

            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "PAYTM_APP");
            queryParams.put("solutionType", "pg_profile_update");
            queryParams.put("solutionSubType", "ADD_ADDRESS");

            Map<String, String> body = new HashMap<String, String>();

            body.put("isShopFrontPhotoMandatory", "false");
            body.put("kybId", KybID);
            body.put("mid", "");
            body.put("line1", lineOne);

            Map<String, String> headers = new HashMap<String, String>();

            headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);

            Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode, 400);
        }
    }

    @Test(priority = 0,description = "Create Lead Add Address details",dependsOnMethods = "TC007_05_FetchLeadStatusAddAddress")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_02_CreateLeadAddAddressWrongMid() {
        editBank createNewLead = new editBank(P.TESTDATA.get("AddAddress"));

        if (LeadStatus.equals(LeadNotPresent)) {
            Map<String, String> queryParams = new HashMap<String, String>();

            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "PAYTM_APP");
            queryParams.put("solutionType", "pg_profile_update");
            queryParams.put("solutionSubType", "ADD_ADDRESS");

            Map<String, String> body = new HashMap<String, String>();

            body.put("isShopFrontPhotoMandatory", "false");
            body.put("kybId", KybID);
            body.put("mid", "AashitMID99538286");
            body.put("line1", lineOne);

            Map<String, String> headers = new HashMap<String, String>();

            headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);

            Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode, 400);
        }
    }

    @Test(priority = 0,description = "Create Lead Add Address details",dependsOnMethods = "TC007_05_FetchLeadStatusAddAddress")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_03_CreateLeadAddAddressEmptyKybId() {
        editBank createNewLead = new editBank(P.TESTDATA.get("AddAddress"));

        if (LeadStatus.equals(LeadNotPresent)) {
            Map<String, String> queryParams = new HashMap<String, String>();

            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "PAYTM_APP");
            queryParams.put("solutionType", "pg_profile_update");
            queryParams.put("solutionSubType", "ADD_ADDRESS");

            Map<String, String> body = new HashMap<String, String>();

            body.put("isShopFrontPhotoMandatory", "false");
            body.put("kybId", "");
            body.put("mid", PgMID);
            body.put("line1", lineOne);

            Map<String, String> headers = new HashMap<String, String>();

            headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);

            Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode, 400);
        }
    }

    @Test(priority = 0,description = "Create Lead Add Address details",dependsOnMethods = "TC007_05_FetchLeadStatusAddAddress")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_04_CreateLeadAddAddressEmptyAddress() {
        editBank createNewLead = new editBank(P.TESTDATA.get("AddAddress"));

        if (LeadStatus.equals(LeadNotPresent)) {
            Map<String, String> queryParams = new HashMap<String, String>();

            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "PAYTM_APP");
            queryParams.put("solutionType", "pg_profile_update");
            queryParams.put("solutionSubType", "ADD_ADDRESS");

            Map<String, String> body = new HashMap<String, String>();

            body.put("isShopFrontPhotoMandatory", "false");
            body.put("kybId", KybID);
            body.put("mid", PgMID);
            body.put("line1", "");

            Map<String, String> headers = new HashMap<String, String>();

            headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);

            Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode, 400);
        }
    }

    @Test(priority = 0,description = "Create Lead Add Address details",dependsOnMethods = "TC007_05_FetchLeadStatusAddAddress")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_05_CreateLeadAddAddressNullKybId() {
        editBank createNewLead = new editBank(P.TESTDATA.get("AddAddress"));

        if (LeadStatus.equals(LeadNotPresent)) {
            Map<String, String> queryParams = new HashMap<String, String>();

            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "PAYTM_APP");
            queryParams.put("solutionType", "pg_profile_update");
            queryParams.put("solutionSubType", "ADD_ADDRESS");

            Map<String, String> body = new HashMap<String, String>();

            body.put("isShopFrontPhotoMandatory", "false");
            body.put("mid", PgMID);
            body.put("line1", lineOne);

            Map<String, String> headers = new HashMap<String, String>();

            headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);

            Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

            int StatusCode = responseObject.getStatusCode();
            //Assert.assertEquals(StatusCode, 200);
        }
    }

    @Test(priority = 0,description = "Create Lead Add Address details",dependsOnMethods = "TC007_05_FetchLeadStatusAddAddress")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_06_CreateLeadAddAddressNullMid() {
        editBank createNewLead = new editBank(P.TESTDATA.get("AddAddress"));

        if (LeadStatus.equals(LeadNotPresent)) {
            Map<String, String> queryParams = new HashMap<String, String>();

            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "PAYTM_APP");
            queryParams.put("solutionType", "pg_profile_update");
            queryParams.put("solutionSubType", "ADD_ADDRESS");

            Map<String, String> body = new HashMap<String, String>();

            body.put("isShopFrontPhotoMandatory", "false");
            body.put("kybId", KybID);
            body.put("line1", lineOne);

            Map<String, String> headers = new HashMap<String, String>();

            headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);

            Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode, 400);
        }
    }

    @Test(priority = 0,description = "Create Lead Add Address details",dependsOnMethods = "TC007_05_FetchLeadStatusAddAddress")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_07_CreateLeadAddAddressNullAddress() {
        editBank createNewLead = new editBank(P.TESTDATA.get("AddAddress"));

        if (LeadStatus.equals(LeadNotPresent)) {
            Map<String, String> queryParams = new HashMap<String, String>();

            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "PAYTM_APP");
            queryParams.put("solutionType", "pg_profile_update");
            queryParams.put("solutionSubType", "ADD_ADDRESS");

            Map<String, String> body = new HashMap<String, String>();

            body.put("isShopFrontPhotoMandatory", "false");
            body.put("kybId", KybID);
            body.put("mid", PgMID);

            Map<String, String> headers = new HashMap<String, String>();

            headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);

            Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode, 400);
        }
    }

    @Test(priority = 0,description = "Create Lead Add Address details",dependsOnMethods = "TC007_05_FetchLeadStatusAddAddress")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_08_CreateLeadAddAddress() {
        editBank createNewLead = new editBank(P.TESTDATA.get("AddAddress"));

        if (LeadStatus.equals(LeadNotPresent)) {
            Map<String, String> queryParams = new HashMap<String, String>();

            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "PAYTM_APP");
            queryParams.put("solutionType", "pg_profile_update");
            queryParams.put("solutionSubType", "ADD_ADDRESS");

            Map<String, String> body = new HashMap<String, String>();

            body.put("isShopFrontPhotoMandatory", "false");
            body.put("kybId", KybID);
            body.put("mid", PgMID);
            body.put("line1", lineOne);

            Map<String, String> headers = new HashMap<String, String>();

            headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);

            Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

            LeadId = responseObject.jsonPath().getString("leadId");
            LOGGER.info(" Created LeadId is " + " : " + LeadId);

            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode, 200);
        }
    }

    @Test(priority = 0,description = "PG Callback for Add Address",dependsOnMethods = "TC007_05_FetchLeadStatusAddAddress")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC009_PgCallbackAddAddress() throws SQLException {
        DbName = DbStagingSprint;
        String CustID = FetchUserDetails(MobileNo,"mobile");
        String Query = "SELECT sai.solution_value from user_business_mapping ubm join related_business_solution_mapping rbsm join solution_additional_info sai on rbsm.id = ubm.related_business_solution_mapping_id and sai.solution_id = rbsm.solution_id where ubm.lead_id ='"+LeadId+"' and sai.solution_key = 'PG_REQUEST_ID';";
        /*TestBase testBase = new TestBase();
        String Result =  testBase.getResult(Query,"solution_value");
        ManualPgCallBack(CustID,Result,PgMID); */
    }

    @AfterClass(description = "Adding Details on CSV")
    public void AddingDataCsv() throws IOException {
        LOGGER.info("In After Test of " +getClass());

        File fileUpload = new File("OnboardedMerchant.csv") ;
        // Create csv file
        FileWriter outputfile = new FileWriter(fileUpload,true);

        // Write to CSV file which is open
        CSVWriter writer = new CSVWriter(outputfile);

        if(!PgMID.isEmpty()) {
            LOGGER.info("MID Is not Empty");
            // add data to csv
            String[] data1 = {PgMID, App1CustId, MobileNo, "ProfileUpdate", "AddAddress"};
            writer.writeNext(data1);
            writer.flush();
            writer.close();
        }
        else
        {
            LOGGER.info("MID is Empty");
        }
    }
}
