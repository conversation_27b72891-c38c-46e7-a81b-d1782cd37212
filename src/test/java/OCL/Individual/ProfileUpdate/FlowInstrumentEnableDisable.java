package OCL.Individual.ProfileUpdate;

import Request.MerchantService.v1.profile.Update;
import Request.MerchantService.v1.profile.update.Instrument.CombinedLeadStatus;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FlowInstrumentEnableDisable extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(FlowInstrumentEnableDisable.class);
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    public static String ApplicantToken = "";
    public static String MID = "";
    public static String MobileNo = "5497775588";// 5556668181 5433322111
    public static String MobileNo2 = "6665550217";
    public static String App1CustId = "";
    public static String LeadStatus = "";
    public static String LeadId = "";

    @Test(description = "Fetching Session Token and MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_GetSessionToken() throws SQLException {
        ApplicantToken = ApplicantToken(MobileNo, "paytm@123");
        LOGGER.info("Applicant Session Token is : " + ApplicantToken);

        /*CommonAgentToken = AgentSessionToken("9953828631","paytm@123");
        LOGGER.info(" Common Agent Token is : " + CommonAgentToken);*/

        App1CustId = FetchUserDetails(MobileNo, "mobile");
        MID = FetchMID(App1CustId);
     /*   TestBase testBase =new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("DELETE from user_business_mapping WHERE mobile_number= '"+MobileNo+"' and solution_type='pg_profile_update' and solution_type_level_2 like '%INSTRUMENT_%' and status in(0,2);");
        int UpdateRes2 = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes2); */
    }

    public void PgCallback() throws SQLException {
        DbName = DbStagingSprint;
        String Query = "SELECT sai.solution_value from user_business_mapping ubm join related_business_solution_mapping rbsm join solution_additional_info sai on rbsm.id = ubm.related_business_solution_mapping_id and sai.solution_id = rbsm.solution_id where ubm.lead_id ='" + LeadId + "' and sai.solution_key = 'PG_REQUEST_ID';";
        /*TestBase testBase = new TestBase();
        String Result =  testBase.getResult(Query,"solution_value");
        ManualPgCallBack(App1CustId,Result,MID); */
    }

    public void FetchLeadStatus(String Entity, String SolSub, String Instrument) {
        CombinedLeadStatus objLead = new CombinedLeadStatus();

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", Entity);
        queryParams.put("channel", "UMP_WEB");
        queryParams.put("solutionSubType", SolSub);
        queryParams.put("solutionTypeLevel3", Instrument);

        Map<String, String> header = new HashMap<String, String>();

        header.put("session_token", ApplicantToken);
        header.put("Content", "application/json");

        Response responseObject = middlewareServicesObject.CombinedLeadStatus(objLead, queryParams, header);

        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


        try {
            if (responseObject.jsonPath().getJsonObject("displayMessage") == null) {
                List<Object> InstrumentList = responseObject.jsonPath().getList("instrumentLeadStatuses");
                Map InstumentMap = (Map) InstrumentList.get(InstrumentList.size() - 1);
                LeadStatus = InstumentMap.get("leadStatus").toString();
                String PrevLeadId = InstumentMap.get("leadId").toString();
                LOGGER.info("This is Lead Status : " + LeadStatus);
                LOGGER.info("This is Lead Id : " + PrevLeadId);
            } else {
                LeadStatus = "LEAD_NOT_PRESENT";
                LOGGER.info("This is Lead Status : " + LeadStatus);
            }
        } catch (Exception e) {
            LOGGER.info("Exception Occured : " + e);
        }


    }


    public void CreateEnableDisableInstruments(String SolSub, String Instrument) throws SQLException {
        FetchLeadStatus("INDIVIDUAL", SolSub, Instrument);
        if (LeadStatus.equals("LEAD_CLOSED") || LeadStatus.equals("LEAD_NOT_PRESENT")) {
            Update InstEnable = new Update(P.TESTDATA.get("CreateLeadEnableDisable"));

            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "UMP_WEB");
            queryParams.put("solutionSubType", SolSub);
            queryParams.put("solutionTypeLevel3", Instrument);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            headers.put("version", "7.3.0");
            headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);
            headers.put("ipAddress", "********");

            Map<String, String> body = new HashMap<String, String>();
            body.put("mid", MID);

            Response InstEnableResp = middlewareServicesObject.v1ProfileUpdate(InstEnable, queryParams, headers, body);

            String DispMsg = InstEnableResp.jsonPath().getJsonObject("displayMessage").toString();
            int StatusCode = InstEnableResp.getStatusCode();


            if (DispMsg.equals("Update Request Submitted Successfully")) {
                LeadId = InstEnableResp.jsonPath().getJsonObject("leadId").toString();
                LOGGER.info("Instrument Enable/Disable LeadId : " + LeadId);
                Assert.assertEquals(StatusCode, 200);
                PgCallback();
            } else if (StatusCode == 400 && DispMsg.equals(null)) {
                Update InstEnableErr = new Update(P.TESTDATA.get("CreateLeadEnableDisable"));
                Response InstEnableRespErr = middlewareServicesObject.v1ProfileUpdate(InstEnableErr, queryParams, headers, body);
                DispMsg = InstEnableRespErr.jsonPath().getJsonObject("displayMessage").toString();
                LOGGER.info("This is Display Message : " + DispMsg);
                StatusCode = InstEnableRespErr.getStatusCode();
                LeadId = InstEnableResp.jsonPath().getJsonObject("leadId").toString();
                LOGGER.info("Instrument Enable/Disable LeadId : " + LeadId);
                Assert.assertEquals(StatusCode, 200);
                PgCallback();
            } else if (SolSub.equals("INSTRUMENT_ENABLE") && DispMsg.contains("already enabled")) {
                LeadStatus = "ALREADY_ENABLED";
                LOGGER.info("Instrument is already Enabled : " + LeadStatus);

            } else if (SolSub.equals("INSTRUMENT_DISABLE") && DispMsg.contains("already disabled")) {
                LeadStatus = "ALREADY_DISABLED";
                LOGGER.info("Instrument is already Disabled : " + LeadStatus);
            } else {
                LOGGER.info("Some Other Error occured : " + DispMsg);
            }
        } else {
            LOGGER.info("Lead Exist and it is in non terminating stage : " + LeadStatus);
        }
    }

    @Test(description = "Creating Enable/Disable POSTPAID Lead", dependsOnMethods = "TC001_GetSessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_CreateEnableDisablePostpaid() throws SQLException {
        LOGGER.info("Enabling POSTPAID");
        CreateEnableDisableInstrument("INSTRUMENT_ENABLE", "POSTPAID");
        if (LeadStatus.equals("ALREADY_ENABLED") || LeadStatus.equals("AWAITING_CLOSE")) {
            LOGGER.info("POSTPAID Alreay Enabled, Disabling POSTPAID");
            CreateEnableDisableInstrument("INSTRUMENT_DISABLE", "POSTPAID");

        } else {
            LOGGER.info("Some Error occured while Enabling/Disabling POSTPAID");
        }

    }

    @Test(description = "Creating Disable PPI Lead", dependsOnMethods = "TC001_GetSessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC002_CreateEnableDisablePpi() throws SQLException {
        waitForLoad(5000);
        LOGGER.info("Enabling PPI");
    //    CreateEnableDisableInstrument("INSTRUMENT_ENABLE", "PPI");
        if (LeadStatus.equals("ALREADY_ENABLED") || LeadStatus.equals("AWAITING_CLOSE")) {
            LOGGER.info("PPI Alreay Enabled, Disabling PPI");
            CreateEnableDisableInstrument("INSTRUMENT_DISABLE", "PPI");

        } else {
            LOGGER.info("Some Error occured while Enabling/Disabling PPI");
        }

    }

    @Test(description = "Creating Disable CC Lead", dependsOnMethods = "TC001_GetSessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_CreateEnableDisableCc() throws SQLException {
        waitForLoad(5000);
        LOGGER.info("Enabling CC");
        CreateEnableDisableInstrument("INSTRUMENT_ENABLE", "CC");
        if (LeadStatus.equals("ALREADY_ENABLED") || LeadStatus.equals("AWAITING_CLOSE")) {
            LOGGER.info("CC Alreay Enabled, Disabling CC");
            CreateEnableDisableInstrument("INSTRUMENT_DISABLE", "CC");

        } else {
            LOGGER.info("Some Error occured while Enabling/Disabling CC");
        }

    }

    @Test(description = "Creating Disable DC Lead", dependsOnMethods = "TC001_GetSessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_CreateEnableDisableDc() throws SQLException {
        waitForLoad(5000);
        LOGGER.info("Enabling DC");
        CreateEnableDisableInstrument("INSTRUMENT_ENABLE", "DC");
        if (LeadStatus.equals("ALREADY_ENABLED") || LeadStatus.equals("AWAITING_CLOSE")) {
            LOGGER.info("DC Alreay Enabled, Disabling DC");
            CreateEnableDisableInstrument("INSTRUMENT_DISABLE", "DC");

        } else {
            LOGGER.info("Some Error occured while Enabling/Disabling DC");
        }

    }

    @Test(description = "Creating Disable NB Lead", dependsOnMethods = "TC001_GetSessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_CreateEnableDisableNb() throws SQLException {
        waitForLoad(5000);
        LOGGER.info("Enabling NB");
        CreateEnableDisableInstrument("INSTRUMENT_ENABLE", "NB");
        if (LeadStatus.equals("ALREADY_ENABLED") || LeadStatus.equals("AWAITING_CLOSE")) {
            LOGGER.info("NB Alreay Enabled, Disabling NB");
            CreateEnableDisableInstrument("INSTRUMENT_DISABLE", "NB");

        } else {
            LOGGER.info("Some Error occured while Enabling/Disabling NB");
        }

    }

    @Test(description = "Creating VIP Enable/Disable NB Lead", dependsOnMethods = "TC001_GetSessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC00006_CreateVipEnableDisableNb() throws SQLException {
        MobileNo = MobileNo2;
        LOGGER.info("Current Mobile no. is : " + MobileNo);
        TC001_GetSessionToken();

        TC005_CreateEnableDisableNb();
    }

    @Test(description = "Creating VIP Enable/Disable DC Lead", dependsOnMethods = "TC001_GetSessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_CreateVipEnableDisableDc() throws SQLException {
        TC004_CreateEnableDisableDc();
    }

    @Test(description = "Creating VIP Enable/Disable CC Lead", dependsOnMethods = "TC001_GetSessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_CreateVipEnableDisableCC() throws SQLException {
        TC003_CreateEnableDisableCc();
    }

    @Test(description = "Creating VIP Enable/Disable PPI Lead", dependsOnMethods = "TC001_GetSessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC008_CreateVipEnableDisablePpi() throws SQLException {
        TC002_CreateEnableDisablePpi();
    }

    @Test(description = "Creating VIP Enable/Disable POSTPAID Lead", dependsOnMethods = "TC001_GetSessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC009_CreateVipEnableDisablePostPaid() throws SQLException {
        TC001_CreateEnableDisablePostpaid();
    }

    public void CreateEnableDisableInstrument(String SolSub, String Instrument) throws SQLException {
        FetchLeadStatus("INDIVIDUAL", SolSub, Instrument);
        if (LeadStatus.equals("LEAD_CLOSED") || LeadStatus.equals("LEAD_NOT_PRESENT")) {
            Update InstEnable = new Update(P.TESTDATA.get("CreateLeadEnableDisable"));

            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "UMP_WEB");
            queryParams.put("solutionSubType", SolSub);
            queryParams.put("solutionTypeLevel3", Instrument);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            headers.put("version", "7.3.0");
            headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);
            headers.put("ipAddress", "********");

            Map<String, String> body = new HashMap<String, String>();
            body.put("mid", MID);

            Response InstEnableResp = middlewareServicesObject.v1ProfileUpdate(InstEnable, queryParams, headers, body);

            String DispMsg = InstEnableResp.jsonPath().getJsonObject("displayMessage").toString();
            int StatusCode = InstEnableResp.getStatusCode();


            if (DispMsg.equals("Update Request Submitted Successfully")) {
                LeadId = InstEnableResp.jsonPath().getJsonObject("leadId").toString();
                LOGGER.info("Instrument Enable/Disable LeadId : " + LeadId);
                Assert.assertEquals(StatusCode, 200);
                PgCallback();
            } else if (StatusCode == 400 && DispMsg.equals(null)) {
                Update InstEnableErr = new Update(P.TESTDATA.get("CreateLeadEnableDisable"));
                Response InstEnableRespErr = middlewareServicesObject.v1ProfileUpdate(InstEnableErr, queryParams, headers, body);
                DispMsg = InstEnableRespErr.jsonPath().getJsonObject("displayMessage").toString();
                LOGGER.info("This is Display Message : " + DispMsg);
                StatusCode = InstEnableRespErr.getStatusCode();
                LeadId = InstEnableResp.jsonPath().getJsonObject("leadId").toString();
                LOGGER.info("Instrument Enable/Disable LeadId : " + LeadId);
                Assert.assertEquals(StatusCode, 200);
                PgCallback();
            } else if (SolSub.equals("INSTRUMENT_ENABLE") && DispMsg.contains("already enabled")) {
                LeadStatus = "ALREADY_ENABLED";
                LOGGER.info("Instrument is already Enabled : " + LeadStatus);

            } else if (SolSub.equals("INSTRUMENT_DISABLE") && DispMsg.contains("already disabled")) {
                LeadStatus = "ALREADY_DISABLED";
                LOGGER.info("Instrument is already Disabled : " + LeadStatus);
            } else {
                LOGGER.info("Some Other Error occured : " + DispMsg);
            }
        } else {
            LOGGER.info("Lead Exist and it is in non terminating stage : " + LeadStatus);
        }
    }

    @Test(description = "Creating Disable NB Lead", dependsOnMethods = "TC001_GetSessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC00005_CreateEnableDisableNb() throws SQLException {
        waitForLoad(5000);
        LOGGER.info("Enabling NB");
        CreateEnableDisableInstrument("INSTRUMENT_ENABLE", "NB");
        if (LeadStatus.equals("ALREADY_ENABLED") || LeadStatus.equals("AWAITING_CLOSE")) {
            LOGGER.info("NB Alreay Enabled, Disabling NB");
            CreateEnableDisableInstrument("INSTRUMENT_DISABLE", "NB");

        } else {
            LOGGER.info("Some Error occured while Enabling/Disabling NB");
        }

    }

    @Test(description = "Creating VIP Enable/Disable NB Lead", dependsOnMethods = "TC001_GetSessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_CreateVipEnableDisableNb() throws SQLException {
        MobileNo = MobileNo2;
        LOGGER.info("Current Mobile no. is : " + MobileNo);
        TC001_GetSessionToken();

        TC005_CreateEnableDisableNb();
    }

    @Test(description = "Creating VIP Enable/Disable DC Lead", dependsOnMethods = "TC001_GetSessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC00007_CreateVipEnableDisableDc() throws SQLException {
        TC004_CreateEnableDisableDc();
    }

    @Test(description = "Creating VIP Enable/Disable CC Lead", dependsOnMethods = "TC001_GetSessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC00007_CreateVipEnableDisableCC() throws SQLException {
        TC003_CreateEnableDisableCc();
    }

    @Test(description = "Creating VIP Enable/Disable PPI Lead", dependsOnMethods = "TC001_GetSessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC00008_CreateVipEnableDisablePpi() throws SQLException {
        TC002_CreateEnableDisablePpi();
    }

    @Test(description = "Creating VIP Enable/Disable POSTPAID Lead", dependsOnMethods = "TC001_GetSessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC009_CreateVeipEnableDisablePostPaid() throws SQLException {
        TC001_CreateEnableDisablePostpaid();
    }

    public void CreateEnableeDisableInstrument(String SolSub, String Instrument) throws SQLException {
        FetchLeadStatus("INDIVIDUAL", SolSub, Instrument);
        if (LeadStatus.equals("LEAD_CLOSED") || LeadStatus.equals("LEAD_NOT_PRESENT")) {
            Update InstEnable = new Update(P.TESTDATA.get("CreateLeadEnableDisable"));

            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "UMP_WEB");
            queryParams.put("solutionSubType", SolSub);
            queryParams.put("solutionTypeLevel3", Instrument);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            headers.put("version", "7.3.0");
            headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);
            headers.put("ipAddress", "********");

            Map<String, String> body = new HashMap<String, String>();
            body.put("mid", MID);

            Response InstEnableResp = middlewareServicesObject.v1ProfileUpdate(InstEnable, queryParams, headers, body);

            String DispMsg = InstEnableResp.jsonPath().getJsonObject("displayMessage").toString();
            int StatusCode = InstEnableResp.getStatusCode();


            if (DispMsg.equals("Update Request Submitted Successfully")) {
                LeadId = InstEnableResp.jsonPath().getJsonObject("leadId").toString();
                LOGGER.info("Instrument Enable/Disable LeadId : " + LeadId);
                Assert.assertEquals(StatusCode, 200);
                PgCallback();
            } else if (StatusCode == 400 && DispMsg.equals(null)) {
                Update InstEnableErr = new Update(P.TESTDATA.get("CreateLeadEnableDisable"));
                Response InstEnableRespErr = middlewareServicesObject.v1ProfileUpdate(InstEnableErr, queryParams, headers, body);
                DispMsg = InstEnableRespErr.jsonPath().getJsonObject("displayMessage").toString();
                LOGGER.info("This is Display Message : " + DispMsg);
                StatusCode = InstEnableRespErr.getStatusCode();
                LeadId = InstEnableResp.jsonPath().getJsonObject("leadId").toString();
                LOGGER.info("Instrument Enable/Disable LeadId : " + LeadId);
                Assert.assertEquals(StatusCode, 200);
                PgCallback();
            } else if (SolSub.equals("INSTRUMENT_ENABLE") && DispMsg.contains("already enabled")) {
                LeadStatus = "ALREADY_ENABLED";
                LOGGER.info("Instrument is already Enabled : " + LeadStatus);

            } else if (SolSub.equals("INSTRUMENT_DISABLE") && DispMsg.contains("already disabled")) {
                LeadStatus = "ALREADY_DISABLED";
                LOGGER.info("Instrument is already Disabled : " + LeadStatus);
            } else {
                LOGGER.info("Some Other Error occured : " + DispMsg);
            }
        } else {
            LOGGER.info("Lead Exist and it is in non terminating stage : " + LeadStatus);
        }
    }



}
