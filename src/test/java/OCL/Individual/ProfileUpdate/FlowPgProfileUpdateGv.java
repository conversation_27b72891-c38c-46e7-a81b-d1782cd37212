package OCL.Individual.ProfileUpdate;

import Request.MerchantService.v1.profile.Update;
import Request.MerchantService.v1.profile.update.lead.Status;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.SkipException;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
public class FlowPgProfileUpdateGv extends BaseMethod{

    private static final Logger LOGGER = LogManager.getLogger(FlowPgProfileUpdateGv.class);
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    //Status statusResponseobject = new Status();
    public static String ApplicantTokenUpdate = "";
    public String LeadId = "";
    public String LEADSTATUSEXPECTEDMESSAGE="LEAD_CLOSED";
    public String LEADSTATUSEXPECTEDMESSAGE_1="LEAD_NOT_PRESENT";
    public String LEADSTATUSACTUALMESSAGE = "";
    public String MID = "";
    public String PAN = "**********";
    public String KybId = "B01nx93gye54e000";
    public String CustID = "";
    private String MobileNo1 = "5738108274";

    @Test(priority = 159)
    public void getApplicantTokenUpdate() throws Exception {
        //TestBase testBase =new TestBase();
        DBConnection.UpdateQueryToCloseLead(MobileNo1,"pg_profile_update");
        DbName = DbStagingSprint;
        /*testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '"+MobileNo1+"' and status = '0' and solution_type='pg_profile_update';");
        int UpdateRes1 = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes1); */
        ApplicantTokenUpdate = ApplicantToken(MobileNo1,"paytm@123");
        CustID=FetchUserDetails(MobileNo1,"phone");
        MID=FetchMID(CustID);

    }
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    @Test(description = "To Fetch The Status Of Gift_Voucher Lead",priority = 160,dependsOnMethods = "getApplicantTokenUpdate")
    public void leadStatus()
    {
        Status statusResponseobject = new Status(P.TESTDATA.get("FetchStatusGV"));
        statusResponseobject.getProperties().setProperty("giftVoucher","true");

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(statusResponseobject,"pg_profile_update","INDIVIDUAL","GIFT_VOUCHER","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantTokenUpdate,MID);
        statusResponseobject.setRefId(responseObject.jsonPath().getString("refId"));
        statusResponseobject.setStatusCode(responseObject.jsonPath().getString("statusCode"));
        statusResponseobject.setLeadStatus(responseObject.jsonPath().getString("leadStatus"));
        statusResponseobject.setLeadId(responseObject.jsonPath().getString("leadId"));
        int statusCode = responseObject.getStatusCode();
        LeadId = responseObject.jsonPath().getString("leadId");
        LEADSTATUSACTUALMESSAGE = responseObject.jsonPath().getString("leadStatus");
        /*if(LeadId!=null)
            LOGGER.info("Lead is present with Lead Id"+ LeadId );
        else
            LOGGER.info("Lead is Not present");*/
        Assert.assertEquals(statusCode, 200);
    }
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "To Update the Gift_Voucher Lead", priority = 161,dependsOnMethods = "leadStatus")
    public void updateLeadGiftVoucherWithInvalidMID ()
    {
        if ((LEADSTATUSEXPECTEDMESSAGE.equals(LEADSTATUSACTUALMESSAGE)) || LEADSTATUSEXPECTEDMESSAGE_1.equals(LEADSTATUSACTUALMESSAGE) )
        {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "UMP_WEB");
            queryParams.put("solutionSubType", "GIFT_VOUCHER");

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            headers.put("version", "7.3.0");
            headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantTokenUpdate);
            headers.put("ipAddress", "********");

            Map<String, String> body = new HashMap<String, String>();
            body.put("displayName", "name Display");
            body.put("mid", "MID");
            body.put("pan", PAN);
            body.put("businessName", "Name Business");
            body.put("businessType", "INDIVIDUAL");
            body.put("planSelected", "DEFAULT");
            body.put("gstin", "09OPVPI6041A4Z5");
            body.put("kybId", KybId);

            Update v1Update = new Update("updateGiftVoucher");

            Response responseObject = middlewareServicesObject.v1ProfileUpdate(v1Update,queryParams, headers, body);
            int statusCode = responseObject.getStatusCode();
            Assert.assertEquals(statusCode, 500);
            // xyz = responseObject.getStatusCode();
        }
        else
        {
            throw new SkipException("SKip");
        }
    }
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "To Update the Gift_Voucher Lead", priority = 162)
    public void updateLeadGiftVoucherWithInvalidPAN ()
    {
        if ((LEADSTATUSEXPECTEDMESSAGE.equals(LEADSTATUSACTUALMESSAGE)) || LEADSTATUSEXPECTEDMESSAGE_1.equals(LEADSTATUSACTUALMESSAGE) )
        {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "UMP_WEB");
            queryParams.put("solutionSubType", "GIFT_VOUCHER");
            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            headers.put("version", "7.3.0");
            headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantTokenUpdate);
            headers.put("ipAddress", "********");
            Map<String, String> body = new HashMap<String, String>();
            body.put("displayName", "name Display");
            body.put("mid", MID);
            body.put("pan", "OPVCI6041");
            body.put("businessName", "Name Business");
            body.put("businessType", "INDIVIDUAL");
            body.put("planSelected", "DEFAULT");
            body.put("gstin", "09OPVPI6041A4Z5");
            body.put("kybId", KybId);

            Update v1Update = new Update("updateGiftVoucher");
            Response responseObject = middlewareServicesObject.v1ProfileUpdate(v1Update,queryParams, headers, body);
            int statusCode = responseObject.getStatusCode();
            Assert.assertEquals(statusCode, 500);
            // xyz = responseObject.getStatusCode();
        }
        else
        {
            throw new SkipException("SKip");
        }
    }
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "To Update the Gift_Voucher Lead", priority = 163)
    public void updateLeadGiftVoucherWithInvalidEntityType () {
        if ((LEADSTATUSEXPECTEDMESSAGE.equals(LEADSTATUSACTUALMESSAGE)) || LEADSTATUSEXPECTEDMESSAGE_1.equals(LEADSTATUSACTUALMESSAGE) )
        {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "PUBLIC_LIMITED");
            queryParams.put("channel", "UMP_WEB");
            queryParams.put("solutionSubType", "GIFT_VOUCHER");
            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            headers.put("version", "7.3.0");
            headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantTokenUpdate);
            headers.put("ipAddress", "********");
            Map<String, String> body = new HashMap<String, String>();
            body.put("displayName", "name Display");
            body.put("mid", MID);
            body.put("pan", PAN);
            body.put("businessName", "Name Business");
            body.put("businessType", "INDIVIDUAL");
            body.put("planSelected", "DEFAULT");
            body.put("gstin", "09OPVPI6041A4Z5");
            body.put("kybId", KybId);

            Update v1Update = new Update("updateGiftVoucher");
            Response responseObject = middlewareServicesObject.v1ProfileUpdate(v1Update,queryParams, headers, body);
            int statusCode = responseObject.getStatusCode();
            Assert.assertEquals(statusCode, 500);
            // xyz = responseObject.getStatusCode();
        }
        else
        {
            throw new SkipException("SKip");
        }
    }
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "To Update the Gift_Voucher Lead", priority = 164)
    public void updateLeadGiftVoucher () throws SQLException {
        if ((LEADSTATUSEXPECTEDMESSAGE.equals(LEADSTATUSACTUALMESSAGE)) || LEADSTATUSEXPECTEDMESSAGE_1.equals(LEADSTATUSACTUALMESSAGE) )
        {
          /*  TestBase testBase = new TestBase();
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "UMP_WEB");
            queryParams.put("solutionSubType", "GIFT_VOUCHER");
            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            headers.put("version", "7.3.0");
            headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantTokenUpdate);
            headers.put("ipAddress", "********");
            Map<String, String> body = new HashMap<String, String>();
            body.put("displayName", "name Display");
            body.put("mid", MID);
            body.put("pan", PAN);
            body.put("businessName", "Name Business");
            body.put("businessType", "INDIVIDUAL");
            body.put("planSelected", "DEFAULT");
            body.put("gstin", "09"+PAN+"Z5");
            body.put("kybId", KybId);

            Update v1Update = new Update("updateGiftVoucher");
            Response responseObject = middlewareServicesObject.v1ProfileUpdate(v1Update,queryParams, headers, body);
            Assert.assertEquals(responseObject.statusCode(), 200);
            testBase.getUbmId(P.TESTDATA.get("mobileNumber"), P.TESTDATA.get("solutionTypelevel2ForGV"));
            // xyz = responseObject.getStatusCode(); */
        }
        else
        {
            throw new SkipException("SKip");
        }
    }

}
