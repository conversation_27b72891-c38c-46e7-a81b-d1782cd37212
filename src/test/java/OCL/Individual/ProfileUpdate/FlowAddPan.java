package OCL.Individual.ProfileUpdate;

import Request.MerchantService.v1.profile.update.AddPan;
import Request.PG.CreateMerchantOnPG;
import Request.oAuth.oAuthWormhole.CreateUser;
import Services.MechantService.MiddlewareServices;
import Services.PG.PGServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FlowAddPan extends BaseMethod

{
    Utilities utilities = new Utilities();
    private static final Logger LOGGER = LogManager.getLogger(FlowAddPan.class);
    PGServices createMerchant50k = new PGServices();
    MiddlewareServices middlewareServices = new MiddlewareServices();
    String Pan = utilities.randomIndividualPANValueGenerator();

    Map<String, String> requestHeaders;
    public static String MobileNo = "";
    public static String ApplicantCustId = "";
    public static String requestID = "";
    public static String ApplicantToken = "";
    public static String MID = "";
    public static String SessionToken = "";


    public Map<String,String> headers()
    {
        Map<String,String> setHeaders = new HashMap<>();

        setHeaders.put("Content-Type", "application/json");
        setHeaders.put("session_token", SessionToken);
        return setHeaders;
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 0, description = "Create Applicant on Oauth")

    public void TC000_CreateApplicantOauth() {
        //generating new Number
        Utilities accObj = new Utilities();
        MobileNo = accObj.randomMobileNumberGenerator();
        LOGGER.info("New Number is : " + MobileNo);

        CreateUser OauthObj = new CreateUser();
        OauthObj.setHeader("Content-Type", "application/json");
        OauthObj.getProperties().setProperty("mobile", MobileNo);
        OauthObj.getProperties().setProperty("loginPassword", "paytm@123");

        Response OauthResp = OauthObj.callAPI();
        Assert.assertEquals(OauthResp.getStatusCode(), 200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 1, description = "Create Merchant on PG", dependsOnMethods = "TC000_CreateApplicantOauth")

    public void TC001_CreateMerchantOnPG() {
        //getting CustID
        ApplicantCustId = FetchUserDetails(MobileNo, "phone");
        LOGGER.info("custID is  : " + ApplicantCustId);
        Utilities accObj = new Utilities();
        requestID = accObj.randomMobileNumberGenerator();

        CreateMerchantOnPG CreateMerch = new CreateMerchantOnPG(P.TESTDATA.get("CreateMerchantOnPG"));

        Map<String, String> Body = new HashMap<>();
        Body.put("REQUEST_ID", MobileNo);
        Body.put("USER_NAME", MobileNo);
        Body.put("CUST_ID", ApplicantCustId);
        Body.put("KYB_ID", ApplicantCustId);
        Body.put("BUSINESS_TYPE", "INDIVIDUAL");
        Body.put("MERCHANT_NAME", "Automation User");

        Response createMID50k = createMerchant50k.createMerchantOnPG(CreateMerch, Body);

        Assert.assertEquals(createMID50k.jsonPath().getJsonObject("STATUS"), "MID generation is in progress");
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 2,description = "Fetching Session Token and MID", dependsOnMethods = "TC001_CreateMerchantOnPG")

    public void TC002_GetPGMID() throws InterruptedException {
        ApplicantToken = ApplicantToken("9891497839", "paytm@123");
        SessionToken = ApplicantToken(MobileNo, "paytm@123");
        System.out.println("Applicant Session Token is" + ApplicantToken);
        LOGGER.info("Applicant Session Token is : " + ApplicantToken);
        ApplicantCustId = FetchUserDetails(MobileNo, "mobile");
        Thread.sleep(10000);
        MID = FetchMIDFromPG(ApplicantCustId, MobileNo,ApplicantToken);

        System.out.println("Mid is " + MID);

        requestHeaders = headers();
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 3,description = "Negative_Add PAN with empty solution", dependsOnMethods = "TC002_GetPGMID")
    public void TC003_AddPan() {

        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");

        Map<String, String> body = new HashMap<>();
        body.put("mid", MID);
        body.put("pan",Pan);

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        //Assert.assertEquals(addPanResponse.jsonPath().getString("displayMessage"),"Oops! Something went wrong. Please try again later.");
        Assert.assertEquals( addPanResponse.getStatusCode(),500);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 4,description = "Negative_Add PAN with empty entity type", dependsOnMethods = "TC002_GetPGMID")
    public void TC004_AddPan() {

        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");

        Map<String, String> body = new HashMap<>();
        body.put("mid", MID);
        body.put("pan",Pan);

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.jsonPath().getString("displayMessage"),"We noticed a problem with your entity type, please reach out to Merchant help desk for assistance.");
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 5,description = "Negative_Add PAN with different entity type ", dependsOnMethods = "TC002_GetPGMID")
    public void TC005_AddPan() {

        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","PUBLIC_LIMITED");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");

        Map<String, String> body = new HashMap<>();
        body.put("mid", MID);
        body.put("pan",Pan);

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.jsonPath().getString("displayMessage"),"Add PAN is only allowed for INDIVIDUAL entity type.");
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 6,description = "Negative_Add PAN with empty solutionSubType", dependsOnMethods = "TC002_GetPGMID")
    public void TC006_AddPan() {

        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","");

        Map<String, String> body = new HashMap<>();
        body.put("mid", MID);
        body.put("pan",Pan);

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.jsonPath().getString("displayMessage"),"solutionSubType can not be empty for solution type : pg_profile_update");
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 7,description = "Negative_Add PAN without body", dependsOnMethods = "TC002_GetPGMID")
    public void TC007_AddPan() {

        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");

        Map<String, String> body = new HashMap<>();

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 8,description = "Negative_Add PAN without MID", dependsOnMethods = "TC002_GetPGMID")
    public void TC008_AddPan() {

        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");

        Map<String, String> body = new HashMap<>();
        body.put("mid", "");
        body.put("pan",Pan);

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 9,description = "Negative_Add PAN without PAN", dependsOnMethods = "TC002_GetPGMID")
    public void TC009_AddPan() {

        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");

        Map<String, String> body = new HashMap<>();
        body.put("mid", MID);
        body.put("pan","");

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.jsonPath().getString("displayMessage"),"Only Proprietor PAN is allowed.");
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 10,description = "Negative_Add PAN with incorrect MID and PAN (PROPRIETORSHIP)", dependsOnMethods = "TC002_GetPGMID")
    public void TC010_AddPan() {

        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");

        Map<String, String> body = new HashMap<>();
        body.put("mid", "RKTqZF124091159746");
        body.put("pan","**********");

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.jsonPath().getString("displayMessage"),"Oops! Something went wrong. Please try again later.");
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 11,description = "Negative_Add PAN with incorrect MID and PAN (PUBLIC_LIMITED)", dependsOnMethods = "TC002_GetPGMID")
    public void TC011_AddPan() {

        String PublicEntityPAN = utilities.randomPublicPANValueGenerator();
        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");

        Map<String, String> body = new HashMap<>();
        body.put("mid", "RKTqZF124091159746");
        body.put("pan",PublicEntityPAN);

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.jsonPath().getString("displayMessage"),"Only Proprietor PAN is allowed.");
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 12,description = "Negative_Add PAN with correct MID and PAN (PUBLIC_LIMITED)", dependsOnMethods = "TC002_GetPGMID")
    public void TC012_AddPan() {

        String PublicEntityPAN = utilities.randomPublicPANValueGenerator();
        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");

        Map<String, String> body = new HashMap<>();
        body.put("mid", MID);
        body.put("pan",PublicEntityPAN);

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.jsonPath().getString("displayMessage"),"Only Proprietor PAN is allowed.");
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 13,description = "Negative_Add PAN with correct MID and incorrect PAN", dependsOnMethods = "TC002_GetPGMID")
    public void TC013_AddPan() {

        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");

        Map<String, String> body = new HashMap<>();
        body.put("mid", MID);
        body.put("pan","AWLPH772A");

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.jsonPath().getString("displayMessage"),"Only Proprietor PAN is allowed.");
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 14,description = "Negative_Add PAN with incorrect MID and incorrect PAN", dependsOnMethods = "TC002_GetPGMID")
    public void TC014_AddPan() {

        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");

        Map<String, String> body = new HashMap<>();
        body.put("mid", "gUIuRs64313035280");
        body.put("pan","AWLPH772A");

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.jsonPath().getString("displayMessage"),"Only Proprietor PAN is allowed.");
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 15,description = "Negative_Add PAN with MID (PUBLIC_LIMITED) and correct PAN", dependsOnMethods = "TC002_GetPGMID")
    public void TC015_AddPan()
    {
        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");

        Map<String, String> body = new HashMap<>();
        body.put("mid", "RKjdaK38842215078974");
        body.put("pan",Pan);

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.jsonPath().getString("displayMessage"),"Oops! Something went wrong. Please try again later.");
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 16,description = "Negative_Add PAN with MID (PUBLIC_LIMITED) and (PUBLIC_LIMITED) PAN", dependsOnMethods = "TC002_GetPGMID")
    public void TC016_AddPan()
    {
        String PublicEntityPAN = utilities.randomPublicPANValueGenerator();
        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");

        Map<String, String> body = new HashMap<>();
        body.put("mid", "RKjdaK38842215078974");
        body.put("pan",PublicEntityPAN);

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.jsonPath().getString("displayMessage"),"Only Proprietor PAN is allowed.");
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 17,description = "Negative_Add PAN with empty MID and PAN", dependsOnMethods = "TC002_GetPGMID")
    public void TC017_AddPan()
    {
        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");
        Map<String, String> body = new HashMap<>();
        body.put("mid", "");
        body.put("pan","");

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 18,description = "Negative_Add PAN with entity, MID and PAN as PUBLIC_LIMITED", dependsOnMethods = "TC002_GetPGMID")
    public void TC018_AddPan()
    {
        String PublicEntityPAN = utilities.randomPublicPANValueGenerator();
        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","PUBLIC_LIMITED");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");
        Map<String, String> body = new HashMap<>();
        body.put("mid", "vVQWmQ61676281998564");
        body.put("pan",PublicEntityPAN);

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.jsonPath().getString("displayMessage"),"Add PAN is only allowed for INDIVIDUAL entity type.");
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 19,description = "Negative_Add PAN with entity as PUBLIC_LIMITED, correct Mid and without PAN", dependsOnMethods = "TC002_GetPGMID")
    public void TC019_AddPan()
    {
        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","PUBLIC_LIMITED");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");

        Map<String, String> body = new HashMap<>();
        body.put("mid", MID);
        body.put("pan","");

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.jsonPath().getString("displayMessage"),"Add PAN is only allowed for INDIVIDUAL entity type.");
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 20,description = "Positive_Add PAN with correct Mid and correct PAN", dependsOnMethods = "TC002_GetPGMID")
    public void TC020_AddPan()
    {
        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");

        Map<String, String> body = new HashMap<>();
        body.put("mid", MID);
        body.put("pan",Pan);

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.jsonPath().getString("displayMessage"),"Update Request Submitted Successfully");
        Assert.assertEquals(addPanResponse.getStatusCode(),200);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(priority = 21,description = "Negative_Add PAN when already added to the MID", dependsOnMethods = "TC002_GetPGMID")
    public void TC021_AddPan()
    {
        AddPan addPan = new AddPan();

        Map<String,String > queryParams = new HashMap<>();
        queryParams.put("solution","pg_profile_update");
        queryParams.put("entityType","INDIVIDUAL");
        queryParams.put("channel","UMP_WEB");
        queryParams.put("solutionSubType","ADD_PAN");

        Map<String, String> body = new HashMap<>();
        body.put("mid", MID);
        body.put("pan",Pan);

        Response addPanResponse = middlewareServices.AddPan(addPan,queryParams,requestHeaders,body);
        //Assertions
        Assert.assertEquals(addPanResponse.jsonPath().getString("displayMessage"),"Your details could not be saved. We already have a application in process, please continue with same.");
        Assert.assertEquals(addPanResponse.getStatusCode(),400);
    }
}

