package OCL.Individual.ProfileUpdate;

import Request.KYB.KybEdit;
import Request.KYB.KybSave;
import Request.MerchantService.v1.profile.update.editBank;
import Request.MerchantService.v1.profile.update.lead.Status;
import Request.PG.CreateMerchantOnPG;
import Services.KYB.KybServices;
import Services.MechantService.MiddlewareServices;
import Services.PG.PGServices;
import Services.Utilities.TestBase;
import Services.Utilities.Utilities;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.opencsv.CSVWriter;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.AfterClass;
import org.testng.annotations.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/*
public class FlowInstrumentUpdate extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(FlowInstrumentUpdate.class);

    public static String MobileNo = "";
    public static String App1CustId = "";
    public static String PgMID = "";
    public static String PgMBID= "";
    public static String PgKey= "";
    public static String KybID= "";
    public static String GSTIN= "";
    public static String LeadStatus= "";
    public static String LeadId= "";
    public static String LeadClosed = "LEAD_CLOSED";
    public static String LeadNotPresent = "LEAD_NOT_PRESENT";
    public static String ImagesPending = "IMAGES_PENDING";
    public static String AwaitingClose = "AWAITING_CLOSE";
    public static String ApplicantToken = "";
    public static String PAN = "";
    Faker GenerateFake = new Faker();

    public String lineOne = GenerateFake.address().streetAddress();

    PGServices createMerchant = new PGServices();
    KybServices kybServices = new KybServices();
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    Utilities accObj = new Utilities();

    @Test(priority = 0, description = "Create Applicant on Oauth", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_CreateApplicantOauth() {
        //generating new Number

        MobileNo = accObj.randomMobileNumberGeneratorStartWith(5);
        LOGGER.info("New Number is : " + MobileNo);
        CreateApplicantOauth(MobileNo);

        App1CustId = FetchUserDetails(MobileNo, "phone");
        LOGGER.info("custID is  : " + App1CustId);

        ApplicantToken = ApplicantToken(MobileNo, "paytm@123");
        LOGGER.info("Applicant Token is : " +ApplicantToken);
    }

    @Test(priority = 0, description = "Create Merchant on Kyb", groups = {"Regression"},dependsOnMethods = "TC001_CreateApplicantOauth")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC002_createMerchantKyb() {
        KybSave kybSave = new KybSave();

        Map<String,String> Qurery = new HashMap<>();

        Map<String,String> Body = new HashMap<>();
        Body.put("userId",App1CustId);
        Body.put("accountNumber",MobileNo);

        Response KybSaveResp = kybServices.KybSave(kybSave,Qurery,Body);

        KybID = KybSaveResp.jsonPath().getString("profileId");
        LOGGER.info("KYB Id is : " +KybID);

        Assert.assertEquals(KybSaveResp.getStatusCode(),200);
    }


    @Test(priority = 0, description = "Create Merchant on PG", groups = {"Regression"},dependsOnMethods = "TC002_createMerchantKyb")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_createMerchantOnPG() {

        PAN =  accObj.randomIndividualPANValueGenerator();
        LOGGER.info("PAN no. is : " +PAN);
        GSTIN = "07"+PAN+"1Z0";
        LOGGER.info("This is GSTIN : " +GSTIN);

        CreateMerchantOnPG CreateMerch = new CreateMerchantOnPG(P.TESTDATA.get("CreateMerchantOnPG"));

        Map<String,String> Body = new HashMap<>();
        Body.put("REQUEST_ID", MobileNo);
        Body.put("USER_NAME", MobileNo);
        Body.put("CUST_ID", App1CustId);
        Body.put("KYB_ID", KybID);
        Body.put("PAN", PAN);
        Body.put("BUSINESS_TYPE", "INDIVIDUAL");
        Body.put("MERCHANT_NAME", "AASHIT SHARMA");

        Response createMID50k = createMerchant.createMerchantOnPG(CreateMerch,Body);
        String responeMessage = createMID50k.jsonPath().getJsonObject("STATUS");
        Assert.assertEquals(responeMessage, "MID generation is in progress");



    }

    @Test(priority = 0, description = "PG Callback", groups = {"Regression"},dependsOnMethods = "TC003_createMerchantOnPG")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_PgCallback() throws SQLException, JsonProcessingException {

        waitForLoad(3000);
        DbName = DbStaging6;
        String query = "SELECT DISTINCT callback_request from pg_callback_info where cust_id = " +App1CustId+ " LIMIT 1";
        LOGGER.info("Extpected Quesry is : "+query);

        try {
            /*TestBase testBase = new TestBase();
            String Result = testBase.getResult(query, "callback_request");
            Reporter.log("Output of DB Query : " + Result, true);
            ObjectMapper mapper = new ObjectMapper();
            Map<String, String> map = mapper.readValue(Result, new TypeReference<Map<String, String>>() {
            });
            LOGGER.info("Created Map is : " + map + "\n");

            PgMID = map.get("mid");
            PgMBID = map.get("mbid");
            PgKey = map.get("merchantKey");
            LOGGER.info("\n PG MBID is : " + PgMBID + "\n PG Key is : " + PgKey);
        }
        catch (Exception e)
        {
            LOGGER.info("This is Exception : " +e);
        }
    }

    @Test(priority = 0, description = "Edit Merchant on Kyb", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_EditMerchantKyb() {
        KybEdit kybEdit = new KybEdit(App1CustId,"unified_payment_merchant");
        Map<String,String> Query = new HashMap<>();

        Map<String,String> Body = new HashMap<>();
        Body.put("pgMid",PgMID);
        Body.put("pgMbid",PgMBID);
        Body.put("pgKey",PgKey);

        Response KybEditResp = kybServices.KybEdit(kybEdit,Query,Body);

        Assert.assertEquals(KybEditResp.getStatusCode(),200);
    }

    @Test(priority = 0, description = "Fetch Lead Status", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_01_FetchLeadStatusInvalidSol()
    {
        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);

        LOGGER.info("Instrument List : " +Instrument + "\n JSON Array of Inst : " +InstrumentJson);

        Status fetcStatus = new Status(P.TESTDATA.get("FetchInstrumentUpdate"));
        fetcStatus.getProperties().setProperty("pgInstruments", String.valueOf(InstrumentJson));

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_","INDIVIDUAL","INSTRUMENT_UPDATE","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantToken,PgMID);

        Assert.assertEquals(responseObject.getStatusCode(),500);

    }

    @Test(priority = 0, description = "Fetch Lead Status", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_02_FetchLeadStatusEmptySol()
    {
        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);

        LOGGER.info("Instrument List : " +Instrument + "\n JSON Array of Inst : " +InstrumentJson);

        Status fetcStatus = new Status(P.TESTDATA.get("FetchInstrumentUpdate"));
        fetcStatus.getProperties().setProperty("pgInstruments", String.valueOf(InstrumentJson));

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"","INDIVIDUAL","INSTRUMENT_UPDATE","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantToken,PgMID);

        Assert.assertEquals(responseObject.getStatusCode(),500);

    }

    @Test(priority = 0, description = "Fetch Lead Status", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_03_FetchLeadStatusDifferentSol()
    {
        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);

        LOGGER.info("Instrument List : " +Instrument + "\n JSON Array of Inst : " +InstrumentJson);

        Status fetcStatus = new Status(P.TESTDATA.get("FetchInstrumentUpdate"));
        fetcStatus.getProperties().setProperty("pgInstruments", String.valueOf(InstrumentJson));

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"offline_50k","INDIVIDUAL","INSTRUMENT_UPDATE","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantToken,PgMID);

        Assert.assertEquals(responseObject.getStatusCode(),200);

    }

    @Test(priority = 0, description = "Fetch Lead Status", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_04_FetchLeadStatusInvalidEntity()
    {
        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);

        LOGGER.info("Instrument List : " +Instrument + "\n JSON Array of Inst : " +InstrumentJson);

        Status fetcStatus = new Status(P.TESTDATA.get("FetchInstrumentUpdate"));
        fetcStatus.getProperties().setProperty("pgInstruments", String.valueOf(InstrumentJson));

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","INDIVID","INSTRUMENT_UPDATE","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantToken,PgMID);

        Assert.assertEquals(responseObject.getStatusCode(),500);
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("We noticed a problem with your entity type, please reach out to Merchant help desk for assistance"));

    }

    @Test(priority = 0, description = "Fetch Lead Status", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_05_FetchLeadStatusEmptyEntity()
    {
        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);

        LOGGER.info("Instrument List : " +Instrument + "\n JSON Array of Inst : " +InstrumentJson);

        Status fetcStatus = new Status(P.TESTDATA.get("FetchInstrumentUpdate"));
        fetcStatus.getProperties().setProperty("pgInstruments", String.valueOf(InstrumentJson));

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","","INSTRUMENT_UPDATE","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantToken,PgMID);

        Assert.assertEquals(responseObject.getStatusCode(),500);
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("We noticed a problem with your entity type, please reach out to Merchant help desk for assistance"));

    }

    @Test(priority = 0, description = "Fetch Lead Status", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_06_FetchLeadStatusDifferentEntity()
    {
        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);

        LOGGER.info("Instrument List : " +Instrument + "\n JSON Array of Inst : " +InstrumentJson);

        Status fetcStatus = new Status(P.TESTDATA.get("FetchInstrumentUpdate"));
        fetcStatus.getProperties().setProperty("pgInstruments", String.valueOf(InstrumentJson));

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","TRUST","INSTRUMENT_UPDATE","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantToken,PgMID);

        Assert.assertEquals(responseObject.getStatusCode(),200);

    }

    @Test(priority = 0, description = "Fetch Lead Status", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_07_FetchLeadStatusInvalidSolSub()
    {
        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);

        LOGGER.info("Instrument List : " +Instrument + "\n JSON Array of Inst : " +InstrumentJson);

        Status fetcStatus = new Status(P.TESTDATA.get("FetchInstrumentUpdate"));
        fetcStatus.getProperties().setProperty("pgInstruments", String.valueOf(InstrumentJson));

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","INDIVIDUAL","INSTRUMENT_","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantToken,PgMID);

        Assert.assertEquals(responseObject.getStatusCode(),500);
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("We are unable to process your request. Please try again after sometime to continue"));

    }

    @Test(priority = 0, description = "Fetch Lead Status", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_08_FetchLeadStatusEmptySolSub()
    {
        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);

        LOGGER.info("Instrument List : " +Instrument + "\n JSON Array of Inst : " +InstrumentJson);

        Status fetcStatus = new Status(P.TESTDATA.get("FetchInstrumentUpdate"));
        fetcStatus.getProperties().setProperty("pgInstruments", String.valueOf(InstrumentJson));

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","INDIVIDUAL","","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantToken,PgMID);

        Assert.assertEquals(responseObject.getStatusCode(),500);
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("We are unable to process your request. Please try again after sometime to continue"));

    }

    @Test(priority = 0, description = "Fetch Lead Status", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_09_FetchLeadStatusDifferentSolSub()
    {
        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);

        LOGGER.info("Instrument List : " +Instrument + "\n JSON Array of Inst : " +InstrumentJson);

        Status fetcStatus = new Status(P.TESTDATA.get("FetchInstrumentUpdate"));
        fetcStatus.getProperties().setProperty("pgInstruments", String.valueOf(InstrumentJson));

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","INDIVIDUAL","ADD_GSTIN","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantToken,PgMID);

        Assert.assertEquals(responseObject.getStatusCode(),200);

    }


    @Test(priority = 0, description = "Fetch Lead Status", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_10_FetchLeadStatusInvalidMid()
    {
        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);

        LOGGER.info("Instrument List : " +Instrument + "\n JSON Array of Inst : " +InstrumentJson);

        Status fetcStatus = new Status(P.TESTDATA.get("FetchInstrumentUpdate"));
        fetcStatus.getProperties().setProperty("pgInstruments", String.valueOf(InstrumentJson));

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","INDIVIDUAL","INSTRUMENT_UPDATE","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantToken,"PgMID");

        Assert.assertEquals(responseObject.getStatusCode(),200);

    }

    @Test(priority = 0, description = "Fetch Lead Status", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_11_FetchLeadStatusEmptydMid()
    {
        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);

        LOGGER.info("Instrument List : " +Instrument + "\n JSON Array of Inst : " +InstrumentJson);

        Status fetcStatus = new Status(P.TESTDATA.get("FetchInstrumentUpdate"));
        fetcStatus.getProperties().setProperty("pgInstruments", String.valueOf(InstrumentJson));

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","INDIVIDUAL","INSTRUMENT_UPDATE","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantToken,"");

        Assert.assertEquals(responseObject.getStatusCode(),400);

    }

    @Test(priority = 0, description = "Fetch Lead Status", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_12_FetchLeadStatusInvalidInstrument()
    {
        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DAC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);

        LOGGER.info("Instrument List : " +Instrument + "\n JSON Array of Inst : " +InstrumentJson);

        Status fetcStatus = new Status(P.TESTDATA.get("FetchInstrumentUpdate"));
        fetcStatus.getProperties().setProperty("pgInstruments", String.valueOf(InstrumentJson));

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","INDIVIDUAL","INSTRUMENT_UPDATE","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantToken,PgMID);

        Assert.assertEquals(responseObject.getStatusCode(),400);

    }

    @Test(priority = 0, description = "Fetch Lead Status", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_13_FetchLeadStatusEmptyInstrument()
    {
        List Instrument = new ArrayList();
     /*   Instrument.add("CC");
        Instrument.add("DAC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);

        LOGGER.info("Instrument List : " +Instrument + "\n JSON Array of Inst : " +InstrumentJson);

        Status fetcStatus = new Status(P.TESTDATA.get("FetchInstrumentUpdate"));
        fetcStatus.getProperties().setProperty("pgInstruments", String.valueOf(InstrumentJson));

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","INDIVIDUAL","INSTRUMENT_UPDATE","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantToken,PgMID);

        Assert.assertEquals(responseObject.getStatusCode(),400);

    }

    @Test(priority = 0, description = "Fetch Lead Status", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_14_FetchLeadStatusDifferentInstrument()
    {
        List Instrument = new ArrayList();
        Instrument.add("PPI");

        JSONArray InstrumentJson = new JSONArray(Instrument);

        LOGGER.info("Instrument List : " +Instrument + "\n JSON Array of Inst : " +InstrumentJson);

        Status fetcStatus = new Status(P.TESTDATA.get("FetchInstrumentUpdate"));
        fetcStatus.getProperties().setProperty("pgInstruments", String.valueOf(InstrumentJson));

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","INDIVIDUAL","INSTRUMENT_UPDATE","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantToken,PgMID);

        Assert.assertEquals(responseObject.getStatusCode(),200);

    }



    @Test(priority = 0, description = "Fetch Lead Status", groups = {"Regression"},dependsOnMethods = "TC004_PgCallback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_15_FetchLeadStatusInstrumentUpdate()
    {
        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);

        LOGGER.info("Instrument List : " +Instrument + "\n JSON Array of Inst : " +InstrumentJson);

        Status fetcStatus = new Status(P.TESTDATA.get("FetchInstrumentUpdate"));
        fetcStatus.getProperties().setProperty("pgInstruments", String.valueOf(InstrumentJson));

        Response responseObject = middlewareServicesObject.v1ProfileUpdateLeadStatus(fetcStatus,"pg_profile_update","INDIVIDUAL","INSTRUMENT_UPDATE","application/json","7.3.0","BabaBlackSheepWeAreInShitDeep",ApplicantToken,PgMID);

        LeadStatus = responseObject.jsonPath().getJsonObject("leadStatus").toString();

        if (responseObject.jsonPath().getJsonObject("leadId") != null)
        {
            LOGGER.info("Inside if Lead Id exist in fetch lead status");
            LeadId= responseObject.jsonPath().getJsonObject("leadId").toString();
        }
        LOGGER.info("Current Lead Status is : " +  LeadStatus );

    }

    @Test(priority = 0,description = "Create Lead Instrument Update",dependsOnMethods = "TC006_15_FetchLeadStatusInstrumentUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_01_CreateLeadInstrumentUpdateEmptyMid() {
        editBank createNewLead = new editBank(P.TESTDATA.get("SubmitInstrumentUpdate"));

            List Instrument = new ArrayList();
            Instrument.add("CC");
            Instrument.add("DC");
            Instrument.add("NB");
            JSONArray InstrumentJson = new JSONArray(Instrument);
            LOGGER.info("\n Instrument List : " +Instrument + "\n JSON Array of Instrument List : " +InstrumentJson);

            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "PAYTM_APP");
            queryParams.put("solutionType", "pg_profile_update");
            queryParams.put("solutionSubType", "INSTRUMENT_UPDATE");

            Map<String, String> body = new HashMap<String, String>();
            body.put("pgInstruments",String.valueOf(InstrumentJson));
            body.put("mid", "");
            body.put("bankAccountHolderName", "TOUCH WOOD LIMITED");
            body.put("pan", PAN);
            body.put("legalName", "AASHIT SHARMA");
            body.put("apiEnabled", "false");

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);

            Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 0,description = "Create Lead Instrument Update",dependsOnMethods = "TC006_15_FetchLeadStatusInstrumentUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_02_CreateLeadInstrumentUpdateNoMid() {
        editBank createNewLead = new editBank(P.TESTDATA.get("SubmitInstrumentUpdate"));

        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);
        LOGGER.info("\n Instrument List : " +Instrument + "\n JSON Array of Instrument List : " +InstrumentJson);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionType", "pg_profile_update");
        queryParams.put("solutionSubType", "INSTRUMENT_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pgInstruments",String.valueOf(InstrumentJson));
        //body.put("mid", "");
        body.put("bankAccountHolderName", "TOUCH WOOD LIMITED");
        body.put("pan", PAN);
        body.put("legalName", "AASHIT SHARMA");
        body.put("apiEnabled", "false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);

        Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 0,description = "Create Lead Instrument Update",dependsOnMethods = "TC006_15_FetchLeadStatusInstrumentUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_03_CreateLeadInstrumentUpdateWrongMid() {
        editBank createNewLead = new editBank(P.TESTDATA.get("SubmitInstrumentUpdate"));

        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);
        LOGGER.info("\n Instrument List : " +Instrument + "\n JSON Array of Instrument List : " +InstrumentJson);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionType", "pg_profile_update");
        queryParams.put("solutionSubType", "INSTRUMENT_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pgInstruments",String.valueOf(InstrumentJson));
        body.put("mid", "AASHITSAHRMA");
        body.put("bankAccountHolderName", "TOUCH WOOD LIMITED");
        body.put("pan", PAN);
        body.put("legalName", "AASHIT SHARMA");
        body.put("apiEnabled", "false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);

        Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }

    @Test(priority = 0,description = "Create Lead Instrument Update",dependsOnMethods = "TC006_15_FetchLeadStatusInstrumentUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_04_CreateLeadInstrumentUpdateEmptyInstrument() {
        editBank createNewLead = new editBank(P.TESTDATA.get("SubmitInstrumentUpdate"));

        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);
        LOGGER.info("\n Instrument List : " +Instrument + "\n JSON Array of Instrument List : " +InstrumentJson);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionType", "pg_profile_update");
        queryParams.put("solutionSubType", "INSTRUMENT_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pgInstruments","");
        body.put("mid", PgMID);
        body.put("bankAccountHolderName", "TOUCH WOOD LIMITED");
        body.put("pan", PAN);
        body.put("legalName", "AASHIT SHARMA");
        body.put("apiEnabled", "false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);

        Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 0,description = "Create Lead Instrument Update",dependsOnMethods = "TC006_15_FetchLeadStatusInstrumentUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_05_CreateLeadInstrumentUpdateNoInstrument() {
        editBank createNewLead = new editBank(P.TESTDATA.get("SubmitInstrumentUpdate"));

        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);
        LOGGER.info("\n Instrument List : " +Instrument + "\n JSON Array of Instrument List : " +InstrumentJson);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionType", "pg_profile_update");
        queryParams.put("solutionSubType", "INSTRUMENT_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        //body.put("pgInstruments","");
        body.put("mid", PgMID);
        body.put("bankAccountHolderName", "TOUCH WOOD LIMITED");
        body.put("pan", PAN);
        body.put("legalName", "AASHIT SHARMA");
        body.put("apiEnabled", "false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);

        Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 0,description = "Create Lead Instrument Update",dependsOnMethods = "TC006_15_FetchLeadStatusInstrumentUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_06_CreateLeadInstrumentUpdateInvalidInstrument() {
        editBank createNewLead = new editBank(P.TESTDATA.get("SubmitInstrumentUpdate"));

        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DAC");
        Instrument.add("NB");
        JSONArray InstrumentJson = new JSONArray(Instrument);
        LOGGER.info("\n Instrument List : " +Instrument + "\n JSON Array of Instrument List : " +InstrumentJson);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionType", "pg_profile_update");
        queryParams.put("solutionSubType", "INSTRUMENT_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pgInstruments",String.valueOf(InstrumentJson));
        body.put("mid", PgMID);
        body.put("bankAccountHolderName", "TOUCH WOOD LIMITED");
        body.put("pan", PAN);
        body.put("legalName", "AASHIT SHARMA");
        body.put("apiEnabled", "false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);

        Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 0,description = "Create Lead Instrument Update",dependsOnMethods = "TC006_15_FetchLeadStatusInstrumentUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_07_CreateLeadInstrumentUpdateDifferentInstrument() {
        editBank createNewLead = new editBank(P.TESTDATA.get("SubmitInstrumentUpdate"));

        List Instrument = new ArrayList();
        Instrument.add("POSTPAID");

        JSONArray InstrumentJson = new JSONArray(Instrument);
        LOGGER.info("\n Instrument List : " +Instrument + "\n JSON Array of Instrument List : " +InstrumentJson);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionType", "pg_profile_update");
        queryParams.put("solutionSubType", "INSTRUMENT_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pgInstruments",String.valueOf(InstrumentJson));
        body.put("mid", PgMID);
        body.put("bankAccountHolderName", "TOUCH WOOD LIMITED");
        body.put("pan", PAN);
        body.put("legalName", "AASHIT SHARMA");
        body.put("apiEnabled", "false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);

        Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

        int StatusCode = responseObject.getStatusCode();
        //Assert.assertEquals(StatusCode, 500);

    }

    @Test(priority = 0,description = "Create Lead Instrument Update",dependsOnMethods = "TC006_15_FetchLeadStatusInstrumentUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_08_CreateLeadInstrumentUpdateEmptyBankName() {
        editBank createNewLead = new editBank(P.TESTDATA.get("SubmitInstrumentUpdate"));

        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");

        JSONArray InstrumentJson = new JSONArray(Instrument);
        LOGGER.info("\n Instrument List : " +Instrument + "\n JSON Array of Instrument List : " +InstrumentJson);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionType", "pg_profile_update");
        queryParams.put("solutionSubType", "INSTRUMENT_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pgInstruments",String.valueOf(InstrumentJson));
        body.put("mid", PgMID);
        body.put("bankAccountHolderName", "");
        body.put("pan", PAN);
        body.put("legalName", "AASHIT SHARMA");
        body.put("apiEnabled", "false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);

        Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 0,description = "Create Lead Instrument Update",dependsOnMethods = "TC006_15_FetchLeadStatusInstrumentUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_09_CreateLeadInstrumentUpdateNoBankName() {
        editBank createNewLead = new editBank(P.TESTDATA.get("SubmitInstrumentUpdate"));

        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");

        JSONArray InstrumentJson = new JSONArray(Instrument);
        LOGGER.info("\n Instrument List : " +Instrument + "\n JSON Array of Instrument List : " +InstrumentJson);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionType", "pg_profile_update");
        queryParams.put("solutionSubType", "INSTRUMENT_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pgInstruments",String.valueOf(InstrumentJson));
        body.put("mid", PgMID);
        //body.put("bankAccountHolderName", "");
        body.put("pan", PAN);
        body.put("legalName", "AASHIT SHARMA");
        body.put("apiEnabled", "false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);

        Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 0,description = "Create Lead Instrument Update",dependsOnMethods = "TC006_15_FetchLeadStatusInstrumentUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_10_CreateLeadInstrumentUpdateInvalidPan() {
        editBank createNewLead = new editBank(P.TESTDATA.get("SubmitInstrumentUpdate"));

        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");

        JSONArray InstrumentJson = new JSONArray(Instrument);
        LOGGER.info("\n Instrument List : " +Instrument + "\n JSON Array of Instrument List : " +InstrumentJson);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionType", "pg_profile_update");
        queryParams.put("solutionSubType", "INSTRUMENT_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pgInstruments",String.valueOf(InstrumentJson));
        body.put("mid", PgMID);
        body.put("bankAccountHolderName", "TOUCH WOOD LIMITED");
        body.put("pan", "PAN");
        body.put("legalName", "AASHIT SHARMA");
        body.put("apiEnabled", "false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);

        Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("PAN verification failed. Please ensure PAN is valid and Name entered is exactly as mentioned on your PAN Card."));

    }

    @Test(priority = 0,description = "Create Lead Instrument Update",dependsOnMethods = "TC006_15_FetchLeadStatusInstrumentUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_11_CreateLeadInstrumentUpdateDiffEntityPan() {
        editBank createNewLead = new editBank(P.TESTDATA.get("SubmitInstrumentUpdate"));

        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");

        JSONArray InstrumentJson = new JSONArray(Instrument);
        LOGGER.info("\n Instrument List : " +Instrument + "\n JSON Array of Instrument List : " +InstrumentJson);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionType", "pg_profile_update");
        queryParams.put("solutionSubType", "INSTRUMENT_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pgInstruments",String.valueOf(InstrumentJson));
        body.put("mid", PgMID);
        body.put("bankAccountHolderName", "TOUCH WOOD LIMITED");
        body.put("pan", "**********");
        body.put("legalName", "AASHIT SHARMA");
        body.put("apiEnabled", "false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);

        Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("PAN verification failed. Please ensure PAN is valid and Name entered is exactly as mentioned on your PAN Card."));


    }

    @Test(priority = 0,description = "Create Lead Instrument Update",dependsOnMethods = "TC006_15_FetchLeadStatusInstrumentUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_12_CreateLeadInstrumentUpdateEmptyPan() {
        editBank createNewLead = new editBank(P.TESTDATA.get("SubmitInstrumentUpdate"));

        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");

        JSONArray InstrumentJson = new JSONArray(Instrument);
        LOGGER.info("\n Instrument List : " +Instrument + "\n JSON Array of Instrument List : " +InstrumentJson);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionType", "pg_profile_update");
        queryParams.put("solutionSubType", "INSTRUMENT_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pgInstruments",String.valueOf(InstrumentJson));
        body.put("mid", PgMID);
        body.put("bankAccountHolderName", "TOUCH WOOD LIMITED");
        body.put("pan", "");
        body.put("legalName", "AASHIT SHARMA");
        body.put("apiEnabled", "false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);

        Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        //Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("PAN verification failed. Please ensure PAN is valid and Name entered is exactly as mentioned on your PAN Card."));


    }

    @Test(priority = 0,description = "Create Lead Instrument Update",dependsOnMethods = "TC006_15_FetchLeadStatusInstrumentUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_13_CreateLeadInstrumentUpdateNoPan() {
        editBank createNewLead = new editBank(P.TESTDATA.get("SubmitInstrumentUpdate"));

        List Instrument = new ArrayList();
        Instrument.add("CC");
        Instrument.add("DC");
        Instrument.add("NB");

        JSONArray InstrumentJson = new JSONArray(Instrument);
        LOGGER.info("\n Instrument List : " +Instrument + "\n JSON Array of Instrument List : " +InstrumentJson);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", "pg_profile_update");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "PAYTM_APP");
        queryParams.put("solutionType", "pg_profile_update");
        queryParams.put("solutionSubType", "INSTRUMENT_UPDATE");

        Map<String, String> body = new HashMap<String, String>();
        body.put("pgInstruments",String.valueOf(InstrumentJson));
        body.put("mid", PgMID);
        body.put("bankAccountHolderName", "TOUCH WOOD LIMITED");
        //body.put("pan", "**********");
        body.put("legalName", "AASHIT SHARMA");
        body.put("apiEnabled", "false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);

        Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        //Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("PAN verification failed. Please ensure PAN is valid and Name entered is exactly as mentioned on your PAN Card."));


    }
    
    @Test(priority = 0,description = "Create Lead Instrument Update",dependsOnMethods = "TC006_15_FetchLeadStatusInstrumentUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_14_CreateLeadInstrumentUpdate() {
        editBank createNewLead = new editBank(P.TESTDATA.get("SubmitInstrumentUpdate"));

        if (LeadStatus.equals(LeadNotPresent)) {

            List Instrument = new ArrayList();
            Instrument.add("CC");
            Instrument.add("DC");
            Instrument.add("NB");
            JSONArray InstrumentJson = new JSONArray(Instrument);
            LOGGER.info("\n Instrument List : " +Instrument + "\n JSON Array of Instrument List : " +InstrumentJson);

            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", "pg_profile_update");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("channel", "PAYTM_APP");
            queryParams.put("solutionType", "pg_profile_update");
            queryParams.put("solutionSubType", "INSTRUMENT_UPDATE");

            Map<String, String> body = new HashMap<String, String>();
            body.put("pgInstruments",String.valueOf(InstrumentJson));
            body.put("mid", PgMID);
            body.put("bankAccountHolderName", "TOUCH WOOD LIMITED");
            body.put("pan", PAN);
            body.put("legalName", "AASHIT SHARMA");
            body.put("apiEnabled", "false");

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
            headers.put("session_token", ApplicantToken);

            Response responseObject = middlewareServicesObject.editBank(createNewLead, queryParams, headers, body);

            LeadId = responseObject.jsonPath().getString("leadId");
            LOGGER.info(" Created LeadId is " + " : " + LeadId);

            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode, 200);
        }
    }

    @Test(priority = 0,description = "PG Callback for Instrument Update",dependsOnMethods = "TC007_14_CreateLeadInstrumentUpdate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_PgCallbackInstrumentUpdate() throws SQLException {
        DbName = DbStagingSprint;
        String CustID = FetchUserDetails(MobileNo,"mobile");
        String Query = "SELECT sai.solution_value from user_business_mapping ubm join related_business_solution_mapping rbsm join solution_additional_info sai on rbsm.id = ubm.related_business_solution_mapping_id and sai.solution_id = rbsm.solution_id where ubm.lead_id ='"+LeadId+"' and sai.solution_key = 'PG_REQUEST_ID';";
        /*TestBase testBase = new TestBase();
        String Result =  testBase.getResult(Query,"solution_value");
        ManualPgCallBack(CustID,Result,PgMID);
    }

    @AfterClass(description = "Adding Details on CSV")
    public void AddingDataCsv() throws IOException {
        LOGGER.info("In After Test of " +getClass());

        File fileUpload = new File("OnboardedMerchant.csv") ;
        // Create csv file
        FileWriter outputfile = new FileWriter(fileUpload,true);

        // Write to CSV file which is open
        CSVWriter writer = new CSVWriter(outputfile);

        if(!PgMID.isEmpty()) {
            LOGGER.info("MID Is not Empty");
            // add data to csv
            String[] data1 = {PgMID, App1CustId, MobileNo, "ProfileUpdate", "InstrumentUpdate"};
            writer.writeNext(data1);
            writer.flush();
            writer.close();
        }
        else
        {
            LOGGER.info("MID is Empty");
        }
    }

}
*/