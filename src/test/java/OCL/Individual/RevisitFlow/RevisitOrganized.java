package OCL.Individual.RevisitFlow;
import Request.MerchantService.RevisitOrganized.GetmidReferenceNumber;
import Request.MerchantService.v1.Revisit.*;
import Request.SoundBox.AgentLogin.SbPinCode;
import com.github.javafaker.Bool;
import io.restassured.response.Response;
import OCL.Individual.SoundBox.FlowSoundBox;
import Services.MechantService.MiddlewareServices;
import TestingLogics.TEST;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import org.testng.Assert;
import org.testng.annotations.*;

import java.util.HashMap;
import java.util.Map;
// We are extending the Base Method in order to inherit it
// BaseMethod ---> Parent Class & RevisitOrganized ---> child class
public class RevisitOrganized extends BaseMethod {

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    //private static final org.apache.log4j.Logger LOGGER = org.apache.log4j.Logger.getLogger(FlowSoundBox.class);
    private static final org.apache.logging.log4j.Logger LOGGER = org.apache.logging.log4j.LogManager.getLogger(FlowSoundBox.class);
    TEST obj=new TEST();
    public String sessionToken ="" ;
    public String AgentNumber = "7771216290";
    public String LoginPassword = "paytm@123";
    public String solutionType = "revisit_merchant";
    public String version = "5.3.4";
    public String mid = "";
    public String fseRole = "";
    public String fseSubRole = "";
    public Boolean flag = false;
    public int retrycount ;
    public String fseECode = "802337";
    public String custId = "";
    private String entityType = "INDIVIDUAL";
    private String userType = "merchant";
    private String state = "";
    private String otp = "888888";
    private String pincode = "226020";
    private String State;
    public String City;
    public String Country;
    private String shopReferenceNumber = "Entp017111";
    private String statuscode = "";

    @BeforeMethod
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLoginSoundBox() throws Exception {
        sessionToken = AgentSessionToken(AgentNumber, LoginPassword);
        establishConnectiontoServer(sessionToken , 5);
        LOGGER.info("Agent Token  for Soundbox : " + sessionToken);

    }


    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid from the Shop Referece Number from merchants")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    @Parameters("count")
    public void getMidShopRefernce(@Optional("0")int count){
        Map <String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String> body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "jSGITyX41rnx7Z0iilG1tbuStNULKfPjrfxwv3wZEtVhRW9sRKmBzqEwiyqFzhWJEH8bQiGZ+moT+HTxmBN93BLvyXJ4L40GL6c8UhhNJQLO2sIlcRgC2ijKYsQ/8zRR");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/123.0.6312.42 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        GetmidReferenceNumber obj = new GetmidReferenceNumber(shopReferenceNumber);
        Response respobj = middlewareServicesObject.GetmidReferenceNumber(obj , headers);
        int StatusCode = respobj.getStatusCode();
        if(count<5 && StatusCode != 200) {
            count++;
            this.getMidShopRefernce(count);
        }

        Assert.assertEquals(StatusCode , 200);
        mid = respobj.jsonPath().getString("mids[0].mid").toString();
        LOGGER.info(mid);

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid from the Shop Referece Number from merchants")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidShopRefernce_sessionToken_missing(){
        Map <String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String> body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "jSGITyX41rnx7Z0iilG1tbuStNULKfPjrfxwv3wZEtVhRW9sRKmBzqEwiyqFzhWJEH8bQiGZ+moT+HTxmBN93BLvyXJ4L40GL6c8UhhNJQLO2sIlcRgC2ijKYsQ/8zRR");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", "");
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/123.0.6312.42 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        GetmidReferenceNumber obj = new GetmidReferenceNumber(shopReferenceNumber);
        Response respobj = middlewareServicesObject.GetmidReferenceNumber(obj , headers);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 401);
//        mid = respobj.jsonPath().getString("mids[0].mid").toString();
//        LOGGER.info(mid);


    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid from the Shop Referece Number from merchants")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidShopRefernce_sessiontoken_wrong(){
        Map <String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String> body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "jSGITyX41rnx7Z0iilG1tbuStNULKfPjrfxwv3wZEtVhRW9sRKmBzqEwiyqFzhWJEH8bQiGZ+moT+HTxmBN93BLvyXJ4L40GL6c8UhhNJQLO2sIlcRgC2ijKYsQ/8zRR");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", "Random");
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/123.0.6312.42 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        GetmidReferenceNumber obj = new GetmidReferenceNumber(shopReferenceNumber);
        Response respobj = middlewareServicesObject.GetmidReferenceNumber(obj , headers);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 410);
//        mid = respobj.jsonPath().getString("mids[0].mid").toString();
//        LOGGER.info(mid);


    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid from the Shop Referece Number from merchants")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidShopRefernce_olderversion(){
        Map <String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String> body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "jSGITyX41rnx7Z0iilG1tbuStNULKfPjrfxwv3wZEtVhRW9sRKmBzqEwiyqFzhWJEH8bQiGZ+moT+HTxmBN93BLvyXJ4L40GL6c8UhhNJQLO2sIlcRgC2ijKYsQ/8zRR");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/123.0.6312.42 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", "1.1.1");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        GetmidReferenceNumber obj = new GetmidReferenceNumber(shopReferenceNumber);
        Response respobj = middlewareServicesObject.GetmidReferenceNumber(obj , headers);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
//        mid = respobj.jsonPath().getString("mids[0].mid").toString();
//        LOGGER.info(mid);


    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid from the Shop Referece Number from merchants")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidShopRefernce_checksum_missed(){
        Map <String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String> body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "jSGITyX41rnx7Z0iilG1tbuStNULKfPjrfxwv3wZEtVhRW9sRKmBzqEwiyqFzhWJEH8bQiGZ+moT+HTxmBN93BLvyXJ4L40GL6c8UhhNJQLO2sIlcRgC2ijKYsQ/8zRR");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/123.0.6312.42 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
       // headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        GetmidReferenceNumber obj = new GetmidReferenceNumber(shopReferenceNumber);
        Response respobj = middlewareServicesObject.GetmidReferenceNumber(obj , headers);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
//        mid = respobj.jsonPath().getString("mids[0].mid").toString();
//        LOGGER.info(mid);


    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid from the Shop Referece Number from merchants")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidShopRefernce_higherversion(){
        Map <String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String> body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "jSGITyX41rnx7Z0iilG1tbuStNULKfPjrfxwv3wZEtVhRW9sRKmBzqEwiyqFzhWJEH8bQiGZ+moT+HTxmBN93BLvyXJ4L40GL6c8UhhNJQLO2sIlcRgC2ijKYsQ/8zRR");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/123.0.6312.42 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", "10.4.3");
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        GetmidReferenceNumber obj = new GetmidReferenceNumber(shopReferenceNumber);
        Response respobj = middlewareServicesObject.GetmidReferenceNumber(obj , headers);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
//        mid = respobj.jsonPath().getString("mids[0].mid").toString();
//        LOGGER.info(mid);


    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid from the Shop Referece Number from merchants")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidShopRefernce_shopRefersnceNumber_missing(){
        Map <String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String> body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "jSGITyX41rnx7Z0iilG1tbuStNULKfPjrfxwv3wZEtVhRW9sRKmBzqEwiyqFzhWJEH8bQiGZ+moT+HTxmBN93BLvyXJ4L40GL6c8UhhNJQLO2sIlcRgC2ijKYsQ/8zRR");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/123.0.6312.42 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        GetmidReferenceNumber obj = new GetmidReferenceNumber("");
        Response respobj = middlewareServicesObject.GetmidReferenceNumber(obj , headers);
          int StatusCode = respobj.getStatusCode();
          Assert.assertEquals(StatusCode , 200);
//        mid = respobj.jsonPath().getString("mids[0].mid").toString();
//        LOGGER.info(mid);


    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid from the Shop Referece Number from merchants")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidShopRefernce_wrongShopReference(){
        Map <String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String> body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "jSGITyX41rnx7Z0iilG1tbuStNULKfPjrfxwv3wZEtVhRW9sRKmBzqEwiyqFzhWJEH8bQiGZ+moT+HTxmBN93BLvyXJ4L40GL6c8UhhNJQLO2sIlcRgC2ijKYsQ/8zRR");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/123.0.6312.42 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        GetmidReferenceNumber obj = new GetmidReferenceNumber("Random");
        Response respobj = middlewareServicesObject.GetmidReferenceNumber(obj , headers);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 500);
//        mid = respobj.jsonPath().getString("mids[0].mid").toString();
//        LOGGER.info(mid);

    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid from the Shop Referece Number from merchants")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidShopRefernce_correctmidornot(){
        Map <String , String> headers = new HashMap<>();
        Map<String , String> queryParams = new HashMap<>();
        Map<String , String> body = new HashMap<>();

        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "jSGITyX41rnx7Z0iilG1tbuStNULKfPjrfxwv3wZEtVhRW9sRKmBzqEwiyqFzhWJEH8bQiGZ+moT+HTxmBN93BLvyXJ4L40GL6c8UhhNJQLO2sIlcRgC2ijKYsQ/8zRR");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/123.0.6312.42 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://oe-staging6.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging6.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        GetmidReferenceNumber obj = new GetmidReferenceNumber(shopReferenceNumber);
        Response respobj = middlewareServicesObject.GetmidReferenceNumber(obj , headers);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
        mid = respobj.jsonPath().getString("mids[0].mid").toString();
        LOGGER.info(mid);
        if(mid.equals("RYDunD35721734604300")){
            LOGGER.info("The mid which we got is correct");
        }
    }
    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisit(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("storeId" , shopReferenceNumber);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);
        statuscode = respObj.jsonPath().getString("statusCode").toString();

        fseRole = respObj.jsonPath().getString("fseRole").toString();
        fseSubRole = respObj.jsonPath().getString("fseSubRole").toString();
        custId = respObj.jsonPath().getString("custId").toString();
        fseECode = respObj.jsonPath().getString("fseECode").toString();

        LOGGER.info(fseRole + " " + fseSubRole + " " + custId + " " + fseECode);

    }

    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisit_shopRefernceNumber_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("storeId" , "");

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);
        statuscode = respObj.jsonPath().getString("statusCode").toString();

//        fseRole = respObj.jsonPath().getString("fseRole").toString();
//        fseSubRole = respObj.jsonPath().getString("fseSubRole").toString();
//        custId = respObj.jsonPath().getString("custId").toString();
//        fseECode = respObj.jsonPath().getString("fseECode").toString();
//
//        LOGGER.info(fseRole + " " + fseSubRole + " " + custId + " " + fseECode);

    }

    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisit_shopRefernceNumberRandom(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("storeId" , "Random");

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);
        statuscode = respObj.jsonPath().getString("statusCode").toString();

//        fseRole = respObj.jsonPath().getString("fseRole").toString();
//        fseSubRole = respObj.jsonPath().getString("fseSubRole").toString();
//        custId = respObj.jsonPath().getString("custId").toString();
//        fseECode = respObj.jsonPath().getString("fseECode").toString();
//
//        LOGGER.info(fseRole + " " + fseSubRole + " " + custId + " " + fseECode);

    }

    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisit_sessionToken_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("storeId" , shopReferenceNumber);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);
        statuscode = respObj.jsonPath().getString("statusCode").toString();

//        fseRole = respObj.jsonPath().getString("fseRole").toString();
//        fseSubRole = respObj.jsonPath().getString("fseSubRole").toString();
//        custId = respObj.jsonPath().getString("custId").toString();
//        fseECode = respObj.jsonPath().getString("fseECode").toString();
//
//        LOGGER.info(fseRole + " " + fseSubRole + " " + custId + " " + fseECode);

    }

    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisit_Random_session_token(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("storeId" , shopReferenceNumber);

        headers.put("session_token" , "RANDOM");
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 410);
//        statuscode = respObj.jsonPath().getString("statusCode").toString();

//        fseRole = respObj.jsonPath().getString("fseRole").toString();
//        fseSubRole = respObj.jsonPath().getString("fseSubRole").toString();
//        custId = respObj.jsonPath().getString("custId").toString();
//        fseECode = respObj.jsonPath().getString("fseECode").toString();
//
//        LOGGER.info(fseRole + " " + fseSubRole + " " + custId + " " + fseECode);

    }

    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisit_mid_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("storeId" , shopReferenceNumber);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress("");

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 404);
//        statuscode = respObj.jsonPath().getString("statusCode").toString();
//
//        fseRole = respObj.jsonPath().getString("fseRole").toString();
//        fseSubRole = respObj.jsonPath().getString("fseSubRole").toString();
//        custId = respObj.jsonPath().getString("custId").toString();
//        fseECode = respObj.jsonPath().getString("fseECode").toString();
//
//        LOGGER.info(fseRole + " " + fseSubRole + " " + custId + " " + fseECode);

    }
    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisit_olderversion(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("storeId" , shopReferenceNumber);

        headers.put("session_token" , sessionToken);
        headers.put("version" , "1.1.1");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);
        //statuscode = respObj.jsonPath().getString("statusCode").toString();

//        fseRole = respObj.jsonPath().getString("fseRole").toString();
//        fseSubRole = respObj.jsonPath().getString("fseSubRole").toString();
//        custId = respObj.jsonPath().getString("custId").toString();
//        fseECode = respObj.jsonPath().getString("fseECode").toString();
//
//        LOGGER.info(fseRole + " " + fseSubRole + " " + custId + " " + fseECode);

    }
    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisit_Higherversion(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("storeId" , shopReferenceNumber);

        headers.put("session_token" , sessionToken);
        headers.put("version" , "10.32.1");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);
       // statuscode = respObj.jsonPath().getString("statusCode").toString();

//        fseRole = respObj.jsonPath().getString("fseRole").toString();
//        fseSubRole = respObj.jsonPath().getString("fseSubRole").toString();
//        custId = respObj.jsonPath().getString("custId").toString();
//        fseECode = respObj.jsonPath().getString("fseECode").toString();
//
//        LOGGER.info(fseRole + " " + fseSubRole + " " + custId + " " + fseECode);

    }

    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisit_version_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("storeId" , shopReferenceNumber);

        headers.put("session_token" , sessionToken);
        headers.put("version" , "");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);
//        statuscode = respObj.jsonPath().getString("statusCode").toString();

//        fseRole = respObj.jsonPath().getString("fseRole").toString();
//        fseSubRole = respObj.jsonPath().getString("fseSubRole").toString();
//        custId = respObj.jsonPath().getString("custId").toString();
//        fseECode = respObj.jsonPath().getString("fseECode").toString();
//
//        LOGGER.info(fseRole + " " + fseSubRole + " " + custId + " " + fseECode);

    }
    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisit_checksum_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("storeId" , shopReferenceNumber);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
       // headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);
        statuscode = respObj.jsonPath().getString("statusCode").toString();

//        fseRole = respObj.jsonPath().getString("fseRole").toString();
//        fseSubRole = respObj.jsonPath().getString("fseSubRole").toString();
//        custId = respObj.jsonPath().getString("custId").toString();
//        fseECode = respObj.jsonPath().getString("fseECode").toString();
//
//        LOGGER.info(fseRole + " " + fseSubRole + " " + custId + " " + fseECode);

    }
    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetails(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , mid);
        body.put("fseRole" ,  fseRole);
        body.put("fseSubRole" , fseSubRole );
        body.put("fseECode" , fseECode);
        body.put("custId" , custId);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevisiteBeatDetails obj = new RevisiteBeatDetails();

        Response respObj = middlewareServicesObject.revisitBeatDetails(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the pincode details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchpincode_details(){
        SbPinCode PincodeObj = new SbPinCode(pincode);
        Map<String , String> headers = new HashMap<>();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("devicemac" , "60:6E:E8:D6:95:DB");
        headers.put("deviceIdentifier" , "Xiaomi-M2004J19C-131535fc93929702");
        headers.put("client", "androidapp");
        // headers.put("x-mw-url-checksum" ,"PjOdYg/FVxWwj/8E1zFuClX7qHIRlySz+DzCEKW48cfC+6LCj6Y6w+iMXlIaK8ueKT5LFmd4fy4Fifgnj2/I1vEMroSXZgcRM2RKsh10SO1pUrW5lGw3rkH2tmHGzhQU");
        headers.put("androidid" , "131535fc93929702");
        headers.put("osversion" , "10");
        headers.put("x-src" , "GGClient");
        headers.put("devicemanufacturer" , "Xiaomi");
        headers.put("applanguage" , "en");
        headers.put("latitude" , "26.9008434");
        headers.put("isdevicerooted" , "false");
        headers.put("session_token" , sessionToken);
        headers.put("devicename" , "M2004J19C");
        headers.put("longitude" , "80.9288271");
        headers.put("ipaddress" , "************");
        headers.put("user-agent" , "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/114.0.5735.60 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.32-10.14.0-MB-IB1");
        headers.put("isbusyboxfound" , "false");
        headers.put("accept" , "application/json, text/plain, */*");
        headers.put("version" , version);
        headers.put("origin" , "https://oe-staging5.paytm.com");
        headers.put("x-requested-with" , "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging5.paytm.com/");
        headers.put("accept-language" , "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        Response PincodeObjResp = middlewareServicesObject.SBFetchPinCodeDetails(PincodeObj , headers);
        int StatusCode = PincodeObjResp.getStatusCode();
        if (StatusCode == 200){
            Assert.assertEquals(StatusCode , 200);
            State = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].state").toString();
            City = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].city").toString();
            Country = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].country").toString();
            LOGGER.info(State + " " + City + " " + Country);
        } else if (StatusCode == 410) {
            String Error_Code = PincodeObjResp.jsonPath().getJsonObject("displayMessage").toString();
            if (Error_Code.equals("Please login again. Either your session has expired or you are already logged in on another device.")){
                LOGGER.info("Please login again your session was expired");
            }
        }

    }

    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the pincode details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchpincode_details_random_version(){
        SbPinCode PincodeObj = new SbPinCode(pincode);
        Map<String , String> headers = new HashMap<>();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("devicemac" , "60:6E:E8:D6:95:DB");
        headers.put("deviceIdentifier" , "Xiaomi-M2004J19C-131535fc93929702");
        headers.put("client", "androidapp");
        // headers.put("x-mw-url-checksum" ,"PjOdYg/FVxWwj/8E1zFuClX7qHIRlySz+DzCEKW48cfC+6LCj6Y6w+iMXlIaK8ueKT5LFmd4fy4Fifgnj2/I1vEMroSXZgcRM2RKsh10SO1pUrW5lGw3rkH2tmHGzhQU");
        headers.put("androidid" , "131535fc93929702");
        headers.put("osversion" , "10");
        headers.put("x-src" , "GGClient");
        headers.put("devicemanufacturer" , "Xiaomi");
        headers.put("applanguage" , "en");
        headers.put("latitude" , "26.9008434");
        headers.put("isdevicerooted" , "false");
        headers.put("session_token" , sessionToken);
        headers.put("devicename" , "M2004J19C");
        headers.put("longitude" , "80.9288271");
        headers.put("ipaddress" , "************");
        headers.put("user-agent" , "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/114.0.5735.60 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.32-10.14.0-MB-IB1");
        headers.put("isbusyboxfound" , "false");
        headers.put("accept" , "application/json, text/plain, */*");
        headers.put("version" , "Random");
        headers.put("origin" , "https://oe-staging5.paytm.com");
        headers.put("x-requested-with" , "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging5.paytm.com/");
        headers.put("accept-language" , "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        Response PincodeObjResp = middlewareServicesObject.SBFetchPinCodeDetails(PincodeObj , headers);
        int StatusCode = PincodeObjResp.getStatusCode();
        Assert.assertEquals(StatusCode , 500);

    }

    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the pincode details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchpincode_details_checksum_missing(){
        SbPinCode PincodeObj = new SbPinCode(pincode);
        Map<String , String> headers = new HashMap<>();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("devicemac" , "60:6E:E8:D6:95:DB");
        headers.put("deviceIdentifier" , "Xiaomi-M2004J19C-131535fc93929702");
        headers.put("client", "androidapp");
        // headers.put("x-mw-url-checksum" ,"PjOdYg/FVxWwj/8E1zFuClX7qHIRlySz+DzCEKW48cfC+6LCj6Y6w+iMXlIaK8ueKT5LFmd4fy4Fifgnj2/I1vEMroSXZgcRM2RKsh10SO1pUrW5lGw3rkH2tmHGzhQU");
        headers.put("androidid" , "131535fc93929702");
        headers.put("osversion" , "10");
        headers.put("x-src" , "GGClient");
        headers.put("devicemanufacturer" , "Xiaomi");
        headers.put("applanguage" , "en");
        headers.put("latitude" , "26.9008434");
        headers.put("isdevicerooted" , "false");
        headers.put("session_token" , sessionToken);
        headers.put("devicename" , "M2004J19C");
        headers.put("longitude" , "80.9288271");
        headers.put("ipaddress" , "************");
        headers.put("user-agent" , "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/114.0.5735.60 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.32-10.14.0-MB-IB1");
        headers.put("isbusyboxfound" , "false");
        headers.put("accept" , "application/json, text/plain, */*");
        headers.put("version" , version);
        headers.put("origin" , "https://oe-staging5.paytm.com");
        headers.put("x-requested-with" , "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging5.paytm.com/");
        headers.put("accept-language" , "en-GB,en-US;q=0.9,en;q=0.8");
       // headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        Response PincodeObjResp = middlewareServicesObject.SBFetchPinCodeDetails(PincodeObj , headers);
        int StatusCode = PincodeObjResp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the pincode details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchpincode_details_session_token_missing(){
        SbPinCode PincodeObj = new SbPinCode(pincode);
        Map<String , String> headers = new HashMap<>();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("devicemac" , "60:6E:E8:D6:95:DB");
        headers.put("deviceIdentifier" , "Xiaomi-M2004J19C-131535fc93929702");
        headers.put("client", "androidapp");
        // headers.put("x-mw-url-checksum" ,"PjOdYg/FVxWwj/8E1zFuClX7qHIRlySz+DzCEKW48cfC+6LCj6Y6w+iMXlIaK8ueKT5LFmd4fy4Fifgnj2/I1vEMroSXZgcRM2RKsh10SO1pUrW5lGw3rkH2tmHGzhQU");
        headers.put("androidid" , "131535fc93929702");
        headers.put("osversion" , "10");
        headers.put("x-src" , "GGClient");
        headers.put("devicemanufacturer" , "Xiaomi");
        headers.put("applanguage" , "en");
        headers.put("latitude" , "26.9008434");
        headers.put("isdevicerooted" , "false");
        headers.put("session_token" , "");
        headers.put("devicename" , "M2004J19C");
        headers.put("longitude" , "80.9288271");
        headers.put("ipaddress" , "************");
        headers.put("user-agent" , "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/114.0.5735.60 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.32-10.14.0-MB-IB1");
        headers.put("isbusyboxfound" , "false");
        headers.put("accept" , "application/json, text/plain, */*");
        headers.put("version" , version);
        headers.put("origin" , "https://oe-staging5.paytm.com");
        headers.put("x-requested-with" , "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging5.paytm.com/");
        headers.put("accept-language" , "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        Response PincodeObjResp = middlewareServicesObject.SBFetchPinCodeDetails(PincodeObj , headers);
        int StatusCode = PincodeObjResp.getStatusCode();
        Assert.assertEquals(StatusCode , 401);

    }

    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , entityType);
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , fseRole);
        queryParams.put("fseSubRole" , fseSubRole);
        queryParams.put("solutionTypeLevel3" , "organized");
        queryParams.put("fseDesignation" , "fse");
        queryParams.put("category" , "ENT");
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit_EntityTypeMissing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , "");
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , fseRole);
        queryParams.put("fseSubRole" , fseSubRole);
        queryParams.put("solutionTypeLevel3" , "organized");
        queryParams.put("fseDesignation" , "fse");
        queryParams.put("category" , "ENT");
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit_fseRole_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , entityType);
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , "");
        queryParams.put("fseSubRole" , fseSubRole);
        queryParams.put("solutionTypeLevel3" , "organized");
        queryParams.put("fseDesignation" , "fse");
        queryParams.put("category" , "ENT");
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit_fseSubRole_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , entityType);
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , fseRole);
        queryParams.put("fseSubRole" , "");
        queryParams.put("solutionTypeLevel3" , "organized");
        queryParams.put("fseDesignation" , "fse");
        queryParams.put("category" , "ENT");
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit_CategoryNot_Passed(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , entityType);
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , fseRole);
        queryParams.put("fseSubRole" , fseSubRole);
        queryParams.put("solutionTypeLevel3" , "organized");
        queryParams.put("fseDesignation" , "fse");
      //  queryParams.put("category" , "ENT");
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }

    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit_EntityTypeRandom(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , "Random");
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , fseRole);
        queryParams.put("fseSubRole" , fseSubRole);
        queryParams.put("solutionTypeLevel3" , "organized");
        queryParams.put("fseDesignation" , "fse");
        queryParams.put("category" , "ENT");
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit_fseRole_Random(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , entityType);
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , "Random");
        queryParams.put("fseSubRole" , fseSubRole);
        queryParams.put("solutionTypeLevel3" , "organized");
        queryParams.put("fseDesignation" , "fse");
        queryParams.put("category" , "ENT");
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit_solutiontype3_Random(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , entityType);
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , fseRole);
        queryParams.put("fseSubRole" , fseSubRole);
        queryParams.put("solutionTypeLevel3" , "Random");
        queryParams.put("fseDesignation" , "fse");
        queryParams.put("category" , "ENT");
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit_fseSubRole_Random(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , entityType);
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , fseRole);
        queryParams.put("fseSubRole" , "Random");
        queryParams.put("solutionTypeLevel3" , "organized");
        queryParams.put("fseDesignation" , "fse");
        queryParams.put("category" , "ENT");
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }

    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit_sessiontoken_Random(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , entityType);
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , fseRole);
        queryParams.put("fseSubRole" , fseSubRole);
        queryParams.put("solutionTypeLevel3" , "organized");
        queryParams.put("fseDesignation" , "fse");
        queryParams.put("category" , "ENT");
        headers.put("session_token" , "Random");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit_checksum_Random(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , entityType);
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , fseRole);
        queryParams.put("fseSubRole" , fseSubRole);
        queryParams.put("solutionTypeLevel3" , "organized");
        queryParams.put("fseDesignation" , "fse");
        queryParams.put("category" , "ENT");
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "Random");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit_fseDesignation_Random(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , entityType);
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , fseRole);
        queryParams.put("fseSubRole" , fseSubRole);
        queryParams.put("solutionTypeLevel3" , "organized");
        queryParams.put("fseDesignation" , "Random");
        queryParams.put("category" , "Random");
        headers.put("session_token" , sessionToken);
       // headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
       // headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
       // headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }

    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit_session_token_Random(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , entityType);
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , fseRole);
        queryParams.put("fseSubRole" , fseSubRole);
        queryParams.put("solutionTypeLevel3" , "organized");
        queryParams.put("fseDesignation" , "fse");
        queryParams.put("category" , "ENT");
        headers.put("session_token" , "Random");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , groups = {"Regression" },description = "This will help in creating lead for revisit flow")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void create_lead_revisit(){
        Map<String , String> headers = new HashMap();
        Map <String , String> queryParams = new HashMap();
        Map <String , String> body = new HashMap();
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("version" , version);

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        body.put("entityType", entityType);
        body.put("custId", custId);
        body.put("solution", "revisit_merchant");
        body.put("mobileNumber", "");
        body.put("channel", "GG_APP");
        body.put("oTPValidated", "false");
        body.put("locationChanged", "true");
        body.put("mid", mid);
        body.put("category", "ENT");
        body.put("competitorPresent", "true");
        body.put("blueOfferQrPhotoPresent", "false");
        body.put("solutionTypeLevel3", "organized");
        body.put("fseRole", fseRole);
        body.put("fseSubRole",  fseSubRole);
        body.put("agentPreOtpValidationLatitude", "26.8985683");
        body.put("agentPreOtpValidationLongitude", "80.9301967");
        body.put("revisitDistance", "9108899.63703239");
        body.put("businessDistance", "9108899.63703239");
        body.put("solutionSubType", "payments");
        body.put("storeReferenceID", "Entp017111");

        createLeadRevisit obj = new createLeadRevisit();
        Response respobj = middlewareServicesObject.createLeadRevisit(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        //Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , groups = {"Regression" },description = "This will help in creating lead for revisit flow")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void create_lead_revisit_version_missing(){
        Map<String , String> headers = new HashMap();
        Map <String , String> queryParams = new HashMap();
        Map <String , String> body = new HashMap();
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("version" , "");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        body.put("entityType", entityType);
        body.put("custId", custId);
        body.put("solution", "revisit_merchant");
        body.put("mobileNumber", "");
        body.put("channel", "GG_APP");
        body.put("oTPValidated", "false");
        body.put("locationChanged", "true");
        body.put("mid", mid);
        body.put("category", "ENT");
        body.put("competitorPresent", "true");
        body.put("blueOfferQrPhotoPresent", "false");
        body.put("solutionTypeLevel3", "organized");
        body.put("fseRole", fseRole);
        body.put("fseSubRole",  fseSubRole);
        body.put("agentPreOtpValidationLatitude", "26.8985683");
        body.put("agentPreOtpValidationLongitude", "80.9301967");
        body.put("revisitDistance", "9108899.63703239");
        body.put("businessDistance", "9108899.63703239");
        body.put("solutionSubType", "payments");
        body.put("storeReferenceID", "Entp017111");

        createLeadRevisit obj = new createLeadRevisit();
        Response respobj = middlewareServicesObject.createLeadRevisit(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , groups = {"Regression" },description = "This will help in creating lead for revisit flow")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void create_lead_revisit_mid_missing(){
        Map<String , String> headers = new HashMap();
        Map <String , String> queryParams = new HashMap();
        Map <String , String> body = new HashMap();
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("version" , version);

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        body.put("entityType", entityType);
        body.put("custId", custId);
        body.put("solution", "revisit_merchant");
        body.put("mobileNumber", "");
        body.put("channel", "GG_APP");
        body.put("oTPValidated", "false");
        body.put("locationChanged", "true");
        body.put("mid", "");
        body.put("category", "ENT");
        body.put("competitorPresent", "true");
        body.put("blueOfferQrPhotoPresent", "false");
        body.put("solutionTypeLevel3", "organized");
        body.put("fseRole", fseRole);
        body.put("fseSubRole",  fseSubRole);
        body.put("agentPreOtpValidationLatitude", "26.8985683");
        body.put("agentPreOtpValidationLongitude", "80.9301967");
        body.put("revisitDistance", "9108899.63703239");
        body.put("businessDistance", "9108899.63703239");
        body.put("solutionSubType", "payments");
        body.put("storeReferenceID", "Entp017111");

        createLeadRevisit obj = new createLeadRevisit();
        Response respobj = middlewareServicesObject.createLeadRevisit(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 500);
    }
    @Test(priority = 8 , groups = {"Regression" },description = "This will help in creating lead for revisit flow")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void create_lead_revisit_fseRole_missing(){
        Map<String , String> headers = new HashMap();
        Map <String , String> queryParams = new HashMap();
        Map <String , String> body = new HashMap();
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("version" , version);

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        body.put("entityType", entityType);
        body.put("custId", custId);
        body.put("solution", "revisit_merchant");
        body.put("mobileNumber", "");
        body.put("channel", "GG_APP");
        body.put("oTPValidated", "false");
        body.put("locationChanged", "true");
        body.put("mid", mid);
        body.put("category", "ENT");
        body.put("competitorPresent", "true");
        body.put("blueOfferQrPhotoPresent", "false");
        body.put("solutionTypeLevel3", "organized");
        body.put("fseRole", "");
        body.put("fseSubRole",  fseSubRole);
        body.put("agentPreOtpValidationLatitude", "26.8985683");
        body.put("agentPreOtpValidationLongitude", "80.9301967");
        body.put("revisitDistance", "9108899.63703239");
        body.put("businessDistance", "9108899.63703239");
        body.put("solutionSubType", "payments");
        body.put("storeReferenceID", "Entp017111");

        createLeadRevisit obj = new createLeadRevisit();
        Response respobj = middlewareServicesObject.createLeadRevisit(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 500);
    }
}