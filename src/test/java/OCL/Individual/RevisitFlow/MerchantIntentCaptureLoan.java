package OCL.Individual.RevisitFlow;

import OCL.Individual.SoundBox.FlowSoundBox;
import Request.MerchantService.v1.Revisit.*;
import Request.SoundBox.AgentLogin.SbPinCode;
import Services.MechantService.MiddlewareServices;
import TestingLogics.TEST;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MerchantIntentCaptureLoan extends BaseMethod {
    private static final org.apache.logging.log4j.Logger LOGGER = org.apache.logging.log4j.LogManager.getLogger(FlowSoundBox.class);
    TEST obj=new TEST();
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    public String sessionToken ="01sulgudgm8z45tb5xu88ufapbsi770q5600" ;
    public String solutionType = "revisit_merchant";
    public String merchantMobile = "7494794070";
    public String version = "5.3.4";
    public String mid = "";
    public String fseRole = "soundbox";
    public String fseSubRole = "device service";
    public String fseECode = "802337";
    public String custId = "1001788031";
    private String entityType = "INDIVIDUAL";
    private String userType = "merchant";
    private String state = "";
    private String otp = "888888";
    private String pincode = "226020";
    private String State;
    public String City;
    public String Country;
    public boolean loancapture = true;
    public String leadid = "";
    public String tagName = "r";


    @BeforeMethod
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLoginSoundBox() throws Exception{
        sessionToken = AgentSessionToken("8010630022", "paytm@123");
        establishConnectiontoServer(sessionToken , 5 );
        LOGGER.info("Agent Token  for Soundbox : " + sessionToken);

    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    @Parameters("count")
    public void getMidRevisit(@Optional("0")int count) throws Exception{
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("merchantMobile" , merchantMobile);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");
        GetMidRevisit obj = new GetMidRevisit();

        Response respObj = middlewareServicesObject.GetMidRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();
        Assert.assertEquals(StatusCode , 200);

        if (StatusCode == 200) {
            mid = respObj.jsonPath().getString("mids[0].mid").toString();
            LOGGER.info(mid);
        }


    }
    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisit(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("mobile" , merchantMobile);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetails(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , mid);
        body.put("fseRole" ,  fseRole);
        body.put("fseSubRole" , fseSubRole );
        body.put("fseECode" , fseECode);
        body.put("custId" , custId);
        body.put("beatTagName" , tagName);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevistLoancapture obj = new RevistLoancapture();

        Response respObj = middlewareServicesObject.revisitLoancapture(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetails_wrong_beat_tag(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , mid);
        body.put("fseRole" ,  fseRole);
        body.put("fseSubRole" , fseSubRole );
        body.put("fseECode" , fseECode);
        body.put("custId" , custId);
        body.put("beatTagName" , "loan intention");

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevistLoancapture obj = new RevistLoancapture();

        Response respObj = middlewareServicesObject.revisitLoancapture(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetails_beat_Tag_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , mid);
        body.put("fseRole" ,  fseRole);
        body.put("fseSubRole" , fseSubRole );
        body.put("fseECode" , fseECode);
        body.put("custId" , custId);
        body.put("beatTagName" , "");

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevistLoancapture obj = new RevistLoancapture();

        Response respObj = middlewareServicesObject.revisitLoancapture(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetails_session_token_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , mid);
        body.put("fseRole" ,  fseRole);
        body.put("fseSubRole" , fseSubRole );
        body.put("fseECode" , fseECode);
        body.put("custId" , custId);
        body.put("beatTagName" , tagName);

        headers.put("session_token" , "");
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevistLoancapture obj = new RevistLoancapture();

        Response respObj = middlewareServicesObject.revisitLoancapture(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 401);

    }


    @Test(priority = 4 , groups = {"Regression"}, description = "Send Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void sendOTP(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , entityType);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        body.put("mobile" , merchantMobile);
        body.put("userType" , userType);
        body.put("call" , "false");

        SendOtpRevisit obj = new SendOtpRevisit();

        Response respObj = middlewareServicesObject.sendOtpRevisit(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);
        state = respObj.jsonPath().getString("state").toString();

    }
    @Test(priority = 5 , groups = {"Regression"}, description = "Validate Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtpRevisit(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , entityType);
        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        body.put("userType" , userType);
        body.put("state" , state);
        body.put("otp" , otp);
        body.put("mobile" , merchantMobile);
        body.put("individaulMerchantKyc" , "true");

        ValidateOtpRevisit obj = new ValidateOtpRevisit();

        Response respObj = middlewareServicesObject.validateotpResp(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);


    }
    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the pincode details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchpincode_details(){
        SbPinCode PincodeObj = new SbPinCode(pincode);
        Map<String , String> headers = new HashMap<>();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("devicemac" , "60:6E:E8:D6:95:DB");
        headers.put("deviceIdentifier" , "Xiaomi-M2004J19C-131535fc93929702");
        headers.put("client", "androidapp");
        headers.put("androidid" , "131535fc93929702");
        headers.put("osversion" , "10");
        headers.put("x-src" , "GGClient");
        headers.put("devicemanufacturer" , "Xiaomi");
        headers.put("applanguage" , "en");
        headers.put("latitude" , "26.9008434");
        headers.put("isdevicerooted" , "false");
        headers.put("session_token" , sessionToken);
        headers.put("devicename" , "M2004J19C");
        headers.put("longitude" , "80.9288271");
        headers.put("ipaddress" , "************");
        headers.put("user-agent" , "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/114.0.5735.60 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.32-10.14.0-MB-IB1");
        headers.put("isbusyboxfound" , "false");
        headers.put("accept" , "application/json, text/plain, */*");
        headers.put("version" , version);
        headers.put("origin" , "https://oe-staging5.paytm.com");
        headers.put("x-requested-with" , "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging5.paytm.com/");
        headers.put("accept-language" , "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        Response PincodeObjResp = middlewareServicesObject.SBFetchPinCodeDetails(PincodeObj , headers);
        int StatusCode = PincodeObjResp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
        if (StatusCode != 200){this.fetchpincode_details();}
        if (StatusCode == 200){
        State = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].state").toString();
        City = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].city").toString();
        Country = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].country").toString();
        LOGGER.info(State + " " + City + " " + Country);
        }
    }
    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , entityType);
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "edc");
        queryParams.put("tagName" , tagName);
        queryParams.put("fseRole" , fseRole);
        queryParams.put("fseSubRole" , fseSubRole);
        queryParams.put("solutionTypeLevel3" , "unorganized");
        queryParams.put("fseDesignation" , "fse");
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , description = "Submitting questions for the revisit flow" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void submit_details(){
        Map<String, String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "YZwYJWrQsXaP5GRflgyTZbDweSQmFd2JgdxbORHUNYZiaR9Dawyzs4TSZDKVzwYkAz7A09frxtNDGOGw2bzy0YsJpccabiUz1DIDB0Brm9GeD0ZnNd80bhtmcFgECWvu");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "5WICsEb61oTlrTN7kN/CrwC0CkhWG/CDjJKOEu/0acToBsU7U0GpaKkws0IW5e+jXlfR14jKM7AnGDBtZU4I0fhGh6f1K07cBYVaZVpwHI250IInF0VUCpkJmpPozP7m");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("custId" , custId);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("beatTagName" , tagName);
        Submitquestions obj = new Submitquestions();
        Response objresp = middlewareServicesObject.submitquestionsRevisit(obj ,headers , body);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
        if(StatusCode == 200){
            leadid = objresp.jsonPath().getString("leadId").toString();
        }
        else{
            LOGGER.info("Submit_details failed due to which we are not able fetch the leadid");
        }
    }

    @Test(priority = 8 , description = "Submitting questions for the revisit flow" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void submit_details_custid_missing(){
        Map<String, String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "YZwYJWrQsXaP5GRflgyTZbDweSQmFd2JgdxbORHUNYZiaR9Dawyzs4TSZDKVzwYkAz7A09frxtNDGOGw2bzy0YsJpccabiUz1DIDB0Brm9GeD0ZnNd80bhtmcFgECWvu");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "5WICsEb61oTlrTN7kN/CrwC0CkhWG/CDjJKOEu/0acToBsU7U0GpaKkws0IW5e+jXlfR14jKM7AnGDBtZU4I0fhGh6f1K07cBYVaZVpwHI250IInF0VUCpkJmpPozP7m");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("beatTagName" , tagName);
        Submitquestions obj = new Submitquestions();
        Response objresp = middlewareServicesObject.submitquestionsRevisit(obj ,headers , body);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 8 , description = "Submitting questions for the revisit flow" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void submit_details_mobile_number_missing(){
        Map<String, String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "YZwYJWrQsXaP5GRflgyTZbDweSQmFd2JgdxbORHUNYZiaR9Dawyzs4TSZDKVzwYkAz7A09frxtNDGOGw2bzy0YsJpccabiUz1DIDB0Brm9GeD0ZnNd80bhtmcFgECWvu");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "5WICsEb61oTlrTN7kN/CrwC0CkhWG/CDjJKOEu/0acToBsU7U0GpaKkws0IW5e+jXlfR14jKM7AnGDBtZU4I0fhGh6f1K07cBYVaZVpwHI250IInF0VUCpkJmpPozP7m");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("custId" , custId);
        body.put("mid" , mid);
        body.put("beatTagName" , tagName);
        Submitquestions obj = new Submitquestions();
        Response objresp = middlewareServicesObject.submitquestionsRevisit(obj ,headers , body);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 8 , description = "Submitting questions for the revisit flow" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void submit_details_mid_missing(){
        Map<String, String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "YZwYJWrQsXaP5GRflgyTZbDweSQmFd2JgdxbORHUNYZiaR9Dawyzs4TSZDKVzwYkAz7A09frxtNDGOGw2bzy0YsJpccabiUz1DIDB0Brm9GeD0ZnNd80bhtmcFgECWvu");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "5WICsEb61oTlrTN7kN/CrwC0CkhWG/CDjJKOEu/0acToBsU7U0GpaKkws0IW5e+jXlfR14jKM7AnGDBtZU4I0fhGh6f1K07cBYVaZVpwHI250IInF0VUCpkJmpPozP7m");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("custId" , custId);
        body.put("mobileNumber" , merchantMobile);
        body.put("beatTagName" , tagName);
        Submitquestions obj = new Submitquestions();
        Response objresp = middlewareServicesObject.submitquestionsRevisit(obj ,headers , body);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 8 , description = "Submitting questions for the revisit flow" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void submit_details_beat_tag_missing(){
        Map<String, String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "YZwYJWrQsXaP5GRflgyTZbDweSQmFd2JgdxbORHUNYZiaR9Dawyzs4TSZDKVzwYkAz7A09frxtNDGOGw2bzy0YsJpccabiUz1DIDB0Brm9GeD0ZnNd80bhtmcFgECWvu");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "5WICsEb61oTlrTN7kN/CrwC0CkhWG/CDjJKOEu/0acToBsU7U0GpaKkws0IW5e+jXlfR14jKM7AnGDBtZU4I0fhGh6f1K07cBYVaZVpwHI250IInF0VUCpkJmpPozP7m");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("custId" , custId);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        Submitquestions obj = new Submitquestions();
        Response objresp = middlewareServicesObject.submitquestionsRevisit(obj ,headers , body);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 8 , description = "Submitting questions for the revisit flow" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void submit_details_version_missing(){
        Map<String, String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "YZwYJWrQsXaP5GRflgyTZbDweSQmFd2JgdxbORHUNYZiaR9Dawyzs4TSZDKVzwYkAz7A09frxtNDGOGw2bzy0YsJpccabiUz1DIDB0Brm9GeD0ZnNd80bhtmcFgECWvu");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "5WICsEb61oTlrTN7kN/CrwC0CkhWG/CDjJKOEu/0acToBsU7U0GpaKkws0IW5e+jXlfR14jKM7AnGDBtZU4I0fhGh6f1K07cBYVaZVpwHI250IInF0VUCpkJmpPozP7m");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", "");
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("custId" , custId);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("beatTagName" , tagName);
        Submitquestions obj = new Submitquestions();
        Response objresp = middlewareServicesObject.submitquestionsRevisit(obj ,headers , body);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 8 , description = "Submitting questions for the revisit flow" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void submit_details_higherversion_configured(){
        Map<String, String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "YZwYJWrQsXaP5GRflgyTZbDweSQmFd2JgdxbORHUNYZiaR9Dawyzs4TSZDKVzwYkAz7A09frxtNDGOGw2bzy0YsJpccabiUz1DIDB0Brm9GeD0ZnNd80bhtmcFgECWvu");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "5WICsEb61oTlrTN7kN/CrwC0CkhWG/CDjJKOEu/0acToBsU7U0GpaKkws0IW5e+jXlfR14jKM7AnGDBtZU4I0fhGh6f1K07cBYVaZVpwHI250IInF0VUCpkJmpPozP7m");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", "10.10.1");
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("custId" , custId);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("beatTagName" , tagName);
        Submitquestions obj = new Submitquestions();
        Response objresp = middlewareServicesObject.submitquestionsRevisit(obj ,headers , body);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 8 , description = "Submitting questions for the revisit flow" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void submit_details_custid_random(){
        Map<String, String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "YZwYJWrQsXaP5GRflgyTZbDweSQmFd2JgdxbORHUNYZiaR9Dawyzs4TSZDKVzwYkAz7A09frxtNDGOGw2bzy0YsJpccabiUz1DIDB0Brm9GeD0ZnNd80bhtmcFgECWvu");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "5WICsEb61oTlrTN7kN/CrwC0CkhWG/CDjJKOEu/0acToBsU7U0GpaKkws0IW5e+jXlfR14jKM7AnGDBtZU4I0fhGh6f1K07cBYVaZVpwHI250IInF0VUCpkJmpPozP7m");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("custId" , "random");
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("beatTagName" , tagName);
        Submitquestions obj = new Submitquestions();
        Response objresp = middlewareServicesObject.submitquestionsRevisit(obj ,headers , body);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 400);

    }
    @Test(priority = 8 , description = "Submitting questions for the revisit flow" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void submit_details_mobile_number_greater_length(){
        Map<String, String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "YZwYJWrQsXaP5GRflgyTZbDweSQmFd2JgdxbORHUNYZiaR9Dawyzs4TSZDKVzwYkAz7A09frxtNDGOGw2bzy0YsJpccabiUz1DIDB0Brm9GeD0ZnNd80bhtmcFgECWvu");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "5WICsEb61oTlrTN7kN/CrwC0CkhWG/CDjJKOEu/0acToBsU7U0GpaKkws0IW5e+jXlfR14jKM7AnGDBtZU4I0fhGh6f1K07cBYVaZVpwHI250IInF0VUCpkJmpPozP7m");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("custId" , custId);
        body.put("mobileNumber" , "12348237489237492834729837492834");
        body.put("mid" , mid);
        body.put("beatTagName" , tagName);
        Submitquestions obj = new Submitquestions();
        Response objresp = middlewareServicesObject.submitquestionsRevisit(obj ,headers , body);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 8 , description = "Submitting questions for the revisit flow" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void submit_details_smaller_length(){
        Map<String, String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "YZwYJWrQsXaP5GRflgyTZbDweSQmFd2JgdxbORHUNYZiaR9Dawyzs4TSZDKVzwYkAz7A09frxtNDGOGw2bzy0YsJpccabiUz1DIDB0Brm9GeD0ZnNd80bhtmcFgECWvu");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "5WICsEb61oTlrTN7kN/CrwC0CkhWG/CDjJKOEu/0acToBsU7U0GpaKkws0IW5e+jXlfR14jKM7AnGDBtZU4I0fhGh6f1K07cBYVaZVpwHI250IInF0VUCpkJmpPozP7m");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("custId" , custId);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("beatTagName" , tagName);
        Submitquestions obj = new Submitquestions();
        Response objresp = middlewareServicesObject.submitquestionsRevisit(obj ,headers , body);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , description = "Submitting questions for the revisit flow" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void submit_details_random_mid(){
        Map<String, String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "YZwYJWrQsXaP5GRflgyTZbDweSQmFd2JgdxbORHUNYZiaR9Dawyzs4TSZDKVzwYkAz7A09frxtNDGOGw2bzy0YsJpccabiUz1DIDB0Brm9GeD0ZnNd80bhtmcFgECWvu");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "5WICsEb61oTlrTN7kN/CrwC0CkhWG/CDjJKOEu/0acToBsU7U0GpaKkws0IW5e+jXlfR14jKM7AnGDBtZU4I0fhGh6f1K07cBYVaZVpwHI250IInF0VUCpkJmpPozP7m");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("custId" , custId);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , "random");
        body.put("beatTagName" , tagName);
        Submitquestions obj = new Submitquestions();
        Response objresp = middlewareServicesObject.submitquestionsRevisit(obj ,headers , body);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 8 , description = "Submitting questions for the revisit flow" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void submit_details_tagName_random(){
        Map<String, String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "YZwYJWrQsXaP5GRflgyTZbDweSQmFd2JgdxbORHUNYZiaR9Dawyzs4TSZDKVzwYkAz7A09frxtNDGOGw2bzy0YsJpccabiUz1DIDB0Brm9GeD0ZnNd80bhtmcFgECWvu");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "5WICsEb61oTlrTN7kN/CrwC0CkhWG/CDjJKOEu/0acToBsU7U0GpaKkws0IW5e+jXlfR14jKM7AnGDBtZU4I0fhGh6f1K07cBYVaZVpwHI250IInF0VUCpkJmpPozP7m");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        body.put("custId" , custId);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("beatTagName" , "random");
        Submitquestions obj = new Submitquestions();
        Response objresp = middlewareServicesObject.submitquestionsRevisit(obj ,headers , body);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 8 , description = "Submitting questions for the revisit flow" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void submit_details_checksum_missing(){
        Map<String, String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "YZwYJWrQsXaP5GRflgyTZbDweSQmFd2JgdxbORHUNYZiaR9Dawyzs4TSZDKVzwYkAz7A09frxtNDGOGw2bzy0YsJpccabiUz1DIDB0Brm9GeD0ZnNd80bhtmcFgECWvu");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "5WICsEb61oTlrTN7kN/CrwC0CkhWG/CDjJKOEu/0acToBsU7U0GpaKkws0IW5e+jXlfR14jKM7AnGDBtZU4I0fhGh6f1K07cBYVaZVpwHI250IInF0VUCpkJmpPozP7m");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("isbusyboxfound", "false");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        body.put("custId" , custId);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("beatTagName" , tagName);
        Submitquestions obj = new Submitquestions();
        Response objresp = middlewareServicesObject.submitquestionsRevisit(obj ,headers , body);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 412);

    }
    @Test(priority = 9 , description = "Fetching the required Documents" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchDocumentDetails(){

        Map<String, String> headers = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "+eaHAro9QLEFIWJXaoMX3+cAwRt+5humOUBfSuyVRzkcVJ2ZYrZuDyNt/mZiz70/t46G+ye+p8kqyMuuJQK6ZR+PrhIKBrEFcilp/UieXu/vKWraiUZPOEf5NTju8fLW");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("leadId", leadid );
        queryParams.put("channel", "GG_APP");
        queryParams.put("merchantCustId", custId);
        queryParams.put("solutionSubType", "edc");
        queryParams.put("fseRole", fseRole);
        queryParams.put("fseSubRole", fseSubRole);
        queryParams.put("solutionTypeLevel3", "unorganized");
        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();
        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj , headers , queryParams);
        int statusCode = respObj.statusCode();
        Assert.assertEquals(statusCode , 200);
        List<Map<String, Object>> docDetailsSet = respObj.jsonPath().getList("docDetailsSet");
        for (Map<String, Object> docDetails : docDetailsSet) {
            LOGGER.info("Document Details: " + docDetails);
        }
    }

    @Test(priority = 9 , description = "Fetching the required Documents" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchDocumentDetails_sessionToken_missing(){

        Map<String, String> headers = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "+eaHAro9QLEFIWJXaoMX3+cAwRt+5humOUBfSuyVRzkcVJ2ZYrZuDyNt/mZiz70/t46G+ye+p8kqyMuuJQK6ZR+PrhIKBrEFcilp/UieXu/vKWraiUZPOEf5NTju8fLW");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", "");
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", version);
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("leadId", leadid );
        queryParams.put("channel", "GG_APP");
        queryParams.put("merchantCustId", custId);
        queryParams.put("solutionSubType", "edc");
        queryParams.put("fseRole", fseRole);
        queryParams.put("fseSubRole", fseSubRole);
        queryParams.put("solutionTypeLevel3", "unorganized");
        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();
        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj , headers , queryParams);
        int statusCode = respObj.statusCode();
        Assert.assertEquals(statusCode , 200);

    }
    @Test(priority = 9 , description = "Fetching the required Documents" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchDocumentDetails_version_missing(){

        Map<String, String> headers = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        headers.put("Host", "goldengate-staging4.paytm.com");
        headers.put("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Android WebView\";v=\"126\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-b6e25f04b4409e0c");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "+eaHAro9QLEFIWJXaoMX3+cAwRt+5humOUBfSuyVRzkcVJ2ZYrZuDyNt/mZiz70/t46G+ye+p8kqyMuuJQK6ZR+PrhIKBrEFcilp/UieXu/vKWraiUZPOEf5NTju8fLW");
        headers.put("androidid", "b6e25f04b4409e0c");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");
        headers.put("session_token", sessionToken);
        headers.put("devicename", "M2004J19C");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.135 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("isbusyboxfound", "false");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("version", "");
        headers.put("origin", "https://ggapp-frontend-staging1.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-staging1.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");


        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("leadId", leadid );
        queryParams.put("channel", "GG_APP");
        queryParams.put("merchantCustId", custId);
        queryParams.put("solutionSubType", "edc");
        queryParams.put("fseRole", fseRole);
        queryParams.put("fseSubRole", fseSubRole);
        queryParams.put("solutionTypeLevel3", "unorganized");
        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();
        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj , headers , queryParams);
        int statusCode = respObj.statusCode();
        Assert.assertEquals(statusCode , 200);

    }
}
