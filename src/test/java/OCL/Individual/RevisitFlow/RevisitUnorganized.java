package OCL.Individual.RevisitFlow;
import Request.MerchantService.v1.Revisit.*;
import Request.SoundBox.AgentLogin.SbPinCode;
import io.restassured.response.Response;
import OCL.Individual.SoundBox.FlowSoundBox;
import Services.MechantService.MiddlewareServices;
import TestingLogics.TEST;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import org.testng.Assert;
import org.testng.annotations.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
public class RevisitUnorganized extends BaseMethod {
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    //private static final org.apache.log4j.Logger LOGGER = org.apache.log4j.Logger.getLogger(FlowSoundBox.class);
    private static final org.apache.logging.log4j.Logger LOGGER = org.apache.logging.log4j.LogManager.getLogger(FlowSoundBox.class);
    TEST obj=new TEST();
    public String sessionToken ="" ;// = "eyJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiZGlyIn0..RSX4jQTxTNsW34aN.56D-kS_fLW9lufFQIBX1oFvkz6kiNJUbV2h69V9GwxfXbd2h26Yo31BxCYsoEE7dsrQLo5BwUUOHc_x5rHabTNBYQ4lPibJPoHs1eqIsRVkCKwth7gIZUp6PrZx8GcOCFB8oeaECFWlxujn-53JgSLsH6Hr4Ze7kJMUX3XkExTIWF_i-Y7gkBdxX5B1hRyz9B26H24uzjJX25sOcnUZBKs_bRIXhtN-4chc_yOnxiEm2XfVzehbwFdpyJu0wfxPBpp93FRYskCX4SbLwEt0.C5fDtlu1Lo7yAmhXGJ6ybg5600";
    public String solutionType = "revisit_merchant";
    public String merchantMobile = "7771110999";
    public String version = "7.2.7";
    public String mid = "HySHnd27878673398759";
    public String fseRole = "soundbox";
    public String fseSubRole = "device service";
    public String fseECode = "802337";
    public String custId = "1001788031";
    private String entityType = "INDIVIDUAL";
    private String userType = "merchant";
    private String state = "";
    private String otp = "888888";
    private String pincode = "226020";
    private String State;
    public String City;
    public String Country;
    public String tagName;
    public String leadid;
    public String solutionTypeLevel3 = "unorganized";

    @BeforeMethod
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLoginSoundBox() throws Exception{
        sessionToken = AgentSessionToken("8010630022", "paytm@123");
        establishConnectiontoServer(sessionToken , 5);
        LOGGER.info("Agent Token  for Soundbox : " + sessionToken);

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    @Parameters("count")
    public void getMidRevisit(@Optional("0")int count){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("merchantMobile" , merchantMobile);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");
        GetMidRevisit obj = new GetMidRevisit();

        Response respObj = middlewareServicesObject.GetMidRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();
        if(count<5 && StatusCode != 200) {
            count++;
            this.getMidRevisit(count);
        }
        Assert.assertEquals(StatusCode , 200);
        mid = respObj.jsonPath().getString("mids[0].mid").toString();
        LOGGER.info(mid);

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidRevisitsolutionTypemissing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("solutionType" , "");
        queryParams.put("merchantMobile" , merchantMobile);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");
        GetMidRevisit obj = new GetMidRevisit();

        Response respObj = middlewareServicesObject.GetMidRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);
//        mid = respObj.jsonPath().getString("mids[0].mid").toString();
//        LOGGER.info(mid);

    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidRevisitsolutiontyperandom(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("solutionType" , "random");
        queryParams.put("merchantMobile" , merchantMobile);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");
        GetMidRevisit obj = new GetMidRevisit();

        Response respObj = middlewareServicesObject.GetMidRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);
//        mid = respObj.jsonPath().getString("mids[0].mid").toString();
//        LOGGER.info(mid);

    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidRevisitLesserlengthMobileNumber(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("merchantMobile" , "1");

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");
        GetMidRevisit obj = new GetMidRevisit();

        Response respObj = middlewareServicesObject.GetMidRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 400);
//        mid = respObj.jsonPath().getString("mids[0].mid").toString();
//        LOGGER.info(mid);

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidRevisitgreaterlengthMobileNumber(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("merchantMobile" , "74897238723984729834");

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");
        GetMidRevisit obj = new GetMidRevisit();

        Response respObj = middlewareServicesObject.GetMidRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 400);
//        mid = respObj.jsonPath().getString("mids[0].mid").toString();
//        LOGGER.info(mid);

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidRevisitsessiontokenMissing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("merchantMobile" , merchantMobile);

        headers.put("session_token" , "");
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");
        GetMidRevisit obj = new GetMidRevisit();

        Response respObj = middlewareServicesObject.GetMidRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 401);
//        mid = respObj.jsonPath().getString("mids[0].mid").toString();
//        LOGGER.info(mid);

    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidRevisitsessiontokenrandom(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("merchantMobile" , merchantMobile);

        headers.put("session_token" , "random");
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");
        GetMidRevisit obj = new GetMidRevisit();

        Response respObj = middlewareServicesObject.GetMidRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 410);
//        mid = respObj.jsonPath().getString("mids[0].mid").toString();
//        LOGGER.info(mid);

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidRevisitolderversion(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("merchantMobile" , merchantMobile);

        headers.put("session_token" , sessionToken);
        headers.put("version" , "1.1.0");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");
        GetMidRevisit obj = new GetMidRevisit();

        Response respObj = middlewareServicesObject.GetMidRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);
//        mid = respObj.jsonPath().getString("mids[0].mid").toString();
//        LOGGER.info(mid);

    }

    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidRevisitHigherversion(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("merchantMobile" , merchantMobile);

        headers.put("session_token" , sessionToken);
        headers.put("version" , "6.1.0");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");
        GetMidRevisit obj = new GetMidRevisit();

        Response respObj = middlewareServicesObject.GetMidRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);
//        mid = respObj.jsonPath().getString("mids[0].mid").toString();
//        LOGGER.info(mid);

    }
    @Test(priority = 1 , groups = {"Regression"}, description = "Get's Mid")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getMidRevisitchecksumMissing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("merchantMobile" , merchantMobile);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        //headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");
        GetMidRevisit obj = new GetMidRevisit();

        Response respObj = middlewareServicesObject.GetMidRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);
//        mid = respObj.jsonPath().getString("mids[0].mid").toString();
//        LOGGER.info(mid);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Get Device IDs for a merchant")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getDeviceIds() {
        Map<String, String> headers = new HashMap<>();
        
        // Set required headers
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua", "\"Not(A:Brand\";v=\"99\", \"Android WebView\";v=\"133\", \"Chromium\";v=\"133\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac", "08:25:25:99:13:38");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("devicename", "Redmi_Note_5_Pro");
        headers.put("ipaddress", "**************");
        headers.put("x-mw-url-checksum", "HsUnIN6A0P1Q81v5EmuiKkosWkedJpAMf6i1xvKmbZmZcTf/w/Al6/pQmnnYztlwhusA51JhshOwAlcuruKwuhWNDrvUIz81bmWh9JyrFd42dBg6ODhbTsfcWY2rh/cx");
        headers.put("osversion", "9");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 9; Redmi Note 5 Pro Build/PKQ1.180904.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/133.0.6943.50 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("version", "7.3.5");
        headers.put("deviceidentifier", "Xiaomi-RedmiNote5Pro-861181041338761");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        
    
        GetDeviceIds obj = new GetDeviceIds(mid);
        
        Response respObj = middlewareServicesObject.GetDeviceIds(obj, headers);
        
        int StatusCode = respObj.statusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    
    @Test(priority = 1, groups = {"Regression"}, description = "Get Device IDs with invalid MID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getDeviceIdsInvalidMid() {
        Map<String, String> headers = new HashMap<>();
        
        // Set required headers
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac", "08:25:25:99:13:38");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("version", "7.3.5");
        headers.put("deviceidentifier", "Xiaomi-RedmiNote5Pro-861181041338761");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        
        GetDeviceIds obj = new GetDeviceIds("INVALID_MID_12345");
        
        Response respObj = middlewareServicesObject.GetDeviceIds(obj, headers);
        
        int StatusCode = respObj.statusCode();
        Assert.assertEquals(StatusCode, 400);
    }
    

    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisit(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("mobile" , merchantMobile);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisitlesserlengthMobileNumber(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("mobile" , "11");

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisitgreaterlengthMobileNumber(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("mobile" , "89734509823475092384750923");

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisitlesserversion(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("mobile" , merchantMobile);

        headers.put("session_token" , sessionToken);
        headers.put("version" , "1.1.1");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisithigherversion(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("mobile" , merchantMobile);

        headers.put("session_token" , sessionToken);
        headers.put("version" , "6.1.0");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisitsessiontokenmissing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("mobile" , merchantMobile);

        headers.put("session_token" , "");
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 401);

    }

    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisitrandomsessiontoken(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("mobile" , merchantMobile);

        headers.put("session_token" , "random");
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 410);

    }
    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisitchecksummissing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("mobile" , merchantMobile);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
       // headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisitRandomchecksum(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("mobile" , merchantMobile);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "random");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 2 , groups = {"Regression"}, description = "Get's MPA")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void mpaAddressRevisitversionmissing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        queryParams.put("mobile" , merchantMobile);

        headers.put("session_token" , sessionToken);
       // headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        MPAaddress obj = new MPAaddress(mid);

        Response respObj = middlewareServicesObject.mpaAddressRevisit(obj , headers , queryParams);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetails(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , mid);
        body.put("fseRole" ,  fseRole);
        body.put("fseSubRole" , fseSubRole );
        body.put("fseECode" , fseECode);
        body.put("custId" , custId);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevisiteBeatDetails obj = new RevisiteBeatDetails();

         Response respObj = middlewareServicesObject.revisitBeatDetails(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetailsMidMissing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , "");
        body.put("fseRole" ,  fseRole);
        body.put("fseSubRole" , fseSubRole );
        body.put("fseECode" , fseECode);
        body.put("custId" , custId);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevisiteBeatDetails obj = new RevisiteBeatDetails();

        Response respObj = middlewareServicesObject.revisitBeatDetails(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetails_Random_mid(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , "Random");
        body.put("fseRole" ,  fseRole);
        body.put("fseSubRole" , fseSubRole );
        body.put("fseECode" , fseECode);
        body.put("custId" , custId);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevisiteBeatDetails obj = new RevisiteBeatDetails();

        Response respObj = middlewareServicesObject.revisitBeatDetails(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetailsfserolerandom(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , mid);
        body.put("fseRole" ,  "random");
        body.put("fseSubRole" , fseSubRole );
        body.put("fseECode" , fseECode);
        body.put("custId" , custId);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevisiteBeatDetails obj = new RevisiteBeatDetails();

        Response respObj = middlewareServicesObject.revisitBeatDetails(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetails_fse_Role_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , mid);
        body.put("fseRole" ,  "");
        body.put("fseSubRole" , fseSubRole );
        body.put("fseECode" , fseECode);
        body.put("custId" , custId);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevisiteBeatDetails obj = new RevisiteBeatDetails();

        Response respObj = middlewareServicesObject.revisitBeatDetails(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetails_fseCode_Random(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , mid);
        body.put("fseRole" ,  fseRole);
        body.put("fseSubRole" , fseSubRole );
        body.put("fseECode" , "Random");
        body.put("custId" , custId);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevisiteBeatDetails obj = new RevisiteBeatDetails();

        Response respObj = middlewareServicesObject.revisitBeatDetails(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetails_SubRole_Random(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , mid);
        body.put("fseRole" ,  fseRole);
        body.put("fseSubRole" , "Random" );
        body.put("fseECode" , fseECode);
        body.put("custId" , custId);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevisiteBeatDetails obj = new RevisiteBeatDetails();

        Response respObj = middlewareServicesObject.revisitBeatDetails(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetailschecksummissing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , mid);
        body.put("fseRole" ,  fseRole);
        body.put("fseSubRole" , fseSubRole );
        body.put("fseECode" , fseECode);
        body.put("custId" , custId);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        //headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevisiteBeatDetails obj = new RevisiteBeatDetails();

        Response respObj = middlewareServicesObject.revisitBeatDetails(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 412);

    }

    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetails_Subrolemissing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , mid);
        body.put("fseRole" ,  fseRole);
        body.put("fseSubRole" , "" );
        body.put("fseECode" , fseECode);
        body.put("custId" , custId);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevisiteBeatDetails obj = new RevisiteBeatDetails();

        Response respObj = middlewareServicesObject.revisitBeatDetails(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetails_fsecode_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , mid);
        body.put("fseRole" ,  fseRole);
        body.put("fseSubRole" , fseSubRole );
        body.put("fseECode" , "");
        body.put("custId" , custId);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevisiteBeatDetails obj = new RevisiteBeatDetails();

        Response respObj = middlewareServicesObject.revisitBeatDetails(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetails_version_older(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , mid);
        body.put("fseRole" ,  fseRole);
        body.put("fseSubRole" , fseSubRole );
        body.put("fseECode" , fseECode);
        body.put("custId" , custId);

        headers.put("session_token" , sessionToken);
        headers.put("version" , "1.1.1");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevisiteBeatDetails obj = new RevisiteBeatDetails();

        Response respObj = middlewareServicesObject.revisitBeatDetails(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }

    @Test(priority = 3 , groups = {"Regression"}, description = "Get's Beat Details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void BeatDetails_custid_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        body.put("pgMid" , mid);
        body.put("fseRole" ,  fseRole);
        body.put("fseSubRole" , fseSubRole );
        body.put("fseECode" , fseECode);
        body.put("custId" , "");

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        RevisiteBeatDetails obj = new RevisiteBeatDetails();

        Response respObj = middlewareServicesObject.revisitBeatDetails(obj , headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);

    }
    @Test(priority = 4 , groups = {"Regression"}, description = "Send Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void sendOTP(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , entityType);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        body.put("mobile" , merchantMobile);
        body.put("userType" , userType);
        body.put("call" , "false");

        SendOtpRevisit obj = new SendOtpRevisit();

        Response respObj = middlewareServicesObject.sendOtpRevisit(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);
        state = respObj.jsonPath().getString("state").toString();

    }

    @Test(priority = 4 , groups = {"Regression"}, description = "Send Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void sendOTP_mobileNumbermissing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , entityType);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        body.put("mobile" , "");
        body.put("userType" , userType);
        body.put("call" , "false");

        SendOtpRevisit obj = new SendOtpRevisit();

        Response respObj = middlewareServicesObject.sendOtpRevisit(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 400);
       // state = respObj.jsonPath().getString("state").toString();

    }
    @Test(priority = 4 , groups = {"Regression"}, description = "Send Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void sendOTP_userTypemissing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , entityType);

        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        headers.put("applanguage", "en");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("ipaddress", "************");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("accept", "application/json, text/plain, */*");

        body.put("mobile" , merchantMobile);
        body.put("userType" , "");
        body.put("call" , "false");

        SendOtpRevisit obj = new SendOtpRevisit();

        Response respObj = middlewareServicesObject.sendOtpRevisit(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);
        // state = respObj.jsonPath().getString("state").toString();

    }

    @Test(priority = 5 , groups = {"Regression"}, description = "Validate Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtpRevisit(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , entityType);
        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        body.put("userType" , userType);
        body.put("state" , state);
        body.put("otp" , otp);
        body.put("mobile" , merchantMobile);
        body.put("individaulMerchantKyc" , "true");

        ValidateOtpRevisit obj = new ValidateOtpRevisit();

        Response respObj = middlewareServicesObject.validateotpResp(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);


    }

    @Test(priority = 5 , groups = {"Regression"}, description = "Validate Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtpRevisit_wrongOtp(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , entityType);
        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        body.put("userType" , userType);
        body.put("state" , state);
        body.put("otp" , "12");
        body.put("mobile" , merchantMobile);
        body.put("individaulMerchantKyc" , "true");

        ValidateOtpRevisit obj = new ValidateOtpRevisit();

        Response respObj = middlewareServicesObject.validateotpResp(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);


    }
    @Test(priority = 5 , groups = {"Regression"}, description = "Validate Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtpRevisit_wrongversion(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , entityType);
        headers.put("session_token" , sessionToken);
        headers.put("version" , "1.1.1");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        body.put("userType" , userType);
        body.put("state" , state);
        body.put("otp" , otp);
        body.put("mobile" , merchantMobile);
        body.put("individaulMerchantKyc" , "true");

        ValidateOtpRevisit obj = new ValidateOtpRevisit();

        Response respObj = middlewareServicesObject.validateotpResp(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);


    }

    @Test(priority = 5 , groups = {"Regression"}, description = "Validate Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtpRevisit_checksum_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , entityType);
        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
      //  headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        body.put("userType" , userType);
        body.put("state" , state);
        body.put("otp" , otp);
        body.put("mobile" , merchantMobile);
        body.put("individaulMerchantKyc" , "true");

        ValidateOtpRevisit obj = new ValidateOtpRevisit();

        Response respObj = middlewareServicesObject.validateotpResp(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 412);


    }

    @Test(priority = 5 , groups = {"Regression"}, description = "Validate Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtpRevisit_solutiontypemissing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        queryParams.put("solutionType" , "");
        queryParams.put("entityType" , entityType);
        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        body.put("userType" , userType);
        body.put("state" , state);
        body.put("otp" , otp);
        body.put("mobile" , merchantMobile);
        body.put("individaulMerchantKyc" , "true");

        ValidateOtpRevisit obj = new ValidateOtpRevisit();

        Response respObj = middlewareServicesObject.validateotpResp(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 500);


    }

    @Test(priority = 5 , groups = {"Regression"}, description = "Validate Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtpRevisit_otp_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , entityType);
        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        body.put("userType" , userType);
        body.put("state" , state);
        body.put("otp" , "");
        body.put("mobile" , merchantMobile);
        body.put("individaulMerchantKyc" , "true");

        ValidateOtpRevisit obj = new ValidateOtpRevisit();

        Response respObj = middlewareServicesObject.validateotpResp(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);


    }

    @Test(priority = 5 , groups = {"Regression"}, description = "Validate Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtpRevisit_mobilenumber_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , entityType);
        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        body.put("userType" , userType);
        body.put("state" , state);
        body.put("otp" , otp);
        body.put("mobile" , "");
        body.put("individaulMerchantKyc" , "true");

        ValidateOtpRevisit obj = new ValidateOtpRevisit();

        Response respObj = middlewareServicesObject.validateotpResp(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);


    }

    @Test(priority = 5 , groups = {"Regression"}, description = "Validate Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtpRevisit_session_token_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , entityType);
        headers.put("session_token" , "");
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        body.put("userType" , userType);
        body.put("state" , state);
        body.put("otp" , otp);
        body.put("mobile" , merchantMobile);
        body.put("individaulMerchantKyc" , "true");

        ValidateOtpRevisit obj = new ValidateOtpRevisit();

        Response respObj = middlewareServicesObject.validateotpResp(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 401);


    }

    @Test(priority = 5 , groups = {"Regression"}, description = "Validate Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtpRevisit_state_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , entityType);
        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        body.put("userType" , userType);
        body.put("state" , "");
        body.put("otp" , otp);
        body.put("mobile" , merchantMobile);
        body.put("individaulMerchantKyc" , "true");

        ValidateOtpRevisit obj = new ValidateOtpRevisit();

        Response respObj = middlewareServicesObject.validateotpResp(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);


    }

    @Test(priority = 5 , groups = {"Regression"}, description = "Validate Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtpRevisit_higher_version(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , entityType);
        headers.put("session_token" , sessionToken);
        headers.put("version" , "11.1.1");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        body.put("userType" , userType);
        body.put("state" , state);
        body.put("otp" , otp);
        body.put("mobile" , merchantMobile);
        body.put("individaulMerchantKyc" , "true");

        ValidateOtpRevisit obj = new ValidateOtpRevisit();

        Response respObj = middlewareServicesObject.validateotpResp(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);


    }

    @Test(priority = 5 , groups = {"Regression"}, description = "Validate Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtpRevisit_version_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , entityType);
        headers.put("session_token" , sessionToken);
        headers.put("version" , "");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        body.put("userType" , userType);
        body.put("state" , state);
        body.put("otp" , otp);
        body.put("mobile" , merchantMobile);
        body.put("individaulMerchantKyc" , "true");

        ValidateOtpRevisit obj = new ValidateOtpRevisit();

        Response respObj = middlewareServicesObject.validateotpResp(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);


    }

    @Test(priority = 5 , groups = {"Regression"}, description = "Validate Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtpRevisit_entity_type_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , "");
        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        body.put("userType" , userType);
        body.put("state" , state);
        body.put("otp" , otp);
        body.put("mobile" , merchantMobile);
        body.put("individaulMerchantKyc" , "true");

        ValidateOtpRevisit obj = new ValidateOtpRevisit();

        Response respObj = middlewareServicesObject.validateotpResp(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);


    }
    @Test(priority = 5 , groups = {"Regression"}, description = "Validate Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtpRevisit_Wrong_Solution_Type(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        queryParams.put("solutionType" , "");
        queryParams.put("entityType" , entityType);
        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        body.put("userType" , userType);
        body.put("state" , state);
        body.put("otp" , otp);
        body.put("mobile" , merchantMobile);
        body.put("individaulMerchantKyc" , "true");

        ValidateOtpRevisit obj = new ValidateOtpRevisit();

        Response respObj = middlewareServicesObject.validateotpResp(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 500);


    }
    @Test(priority = 5 , groups = {"Regression"}, description = "Validate Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtpRevisit_wrong_entity_type(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , "Random");
        headers.put("session_token" , sessionToken);
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        body.put("userType" , userType);
        body.put("state" , state);
        body.put("otp" , otp);
        body.put("mobile" , merchantMobile);
        body.put("individaulMerchantKyc" , "true");

        ValidateOtpRevisit obj = new ValidateOtpRevisit();

        Response respObj = middlewareServicesObject.validateotpResp(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 200);


    }
    @Test(priority = 5 , groups = {"Regression"}, description = "Validate Otp Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void validateOtpRevisit_session_token_wrong(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();
        queryParams.put("solutionType" , solutionType);
        queryParams.put("entityType" , entityType);
        headers.put("session_token" , "random");
        headers.put("version" , version);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        body.put("userType" , userType);
        body.put("state" , state);
        body.put("otp" , otp);
        body.put("mobile" , merchantMobile);
        body.put("individaulMerchantKyc" , "true");

        ValidateOtpRevisit obj = new ValidateOtpRevisit();

        Response respObj = middlewareServicesObject.validateotpResp(obj , queryParams ,  headers , body);

        int StatusCode = respObj.statusCode();

        Assert.assertEquals(StatusCode , 410);


    }
    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the pincode details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchpincode_details(){
        SbPinCode PincodeObj = new SbPinCode(pincode);
        Map<String , String> headers = new HashMap<>();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("devicemac" , "60:6E:E8:D6:95:DB");
        headers.put("deviceIdentifier" , "Xiaomi-M2004J19C-131535fc93929702");
        headers.put("client", "androidapp");
        // headers.put("x-mw-url-checksum" ,"PjOdYg/FVxWwj/8E1zFuClX7qHIRlySz+DzCEKW48cfC+6LCj6Y6w+iMXlIaK8ueKT5LFmd4fy4Fifgnj2/I1vEMroSXZgcRM2RKsh10SO1pUrW5lGw3rkH2tmHGzhQU");
        headers.put("androidid" , "131535fc93929702");
        headers.put("osversion" , "10");
        headers.put("x-src" , "GGClient");
        headers.put("devicemanufacturer" , "Xiaomi");
        headers.put("applanguage" , "en");
        headers.put("latitude" , "26.9008434");
        headers.put("isdevicerooted" , "false");
        headers.put("session_token" , sessionToken);
        headers.put("devicename" , "M2004J19C");
        headers.put("longitude" , "80.9288271");
        headers.put("ipaddress" , "************");
        headers.put("user-agent" , "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/114.0.5735.60 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.32-10.14.0-MB-IB1");
        headers.put("isbusyboxfound" , "false");
        headers.put("accept" , "application/json, text/plain, */*");
        headers.put("version" , version);
        headers.put("origin" , "https://oe-staging5.paytm.com");
        headers.put("x-requested-with" , "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging5.paytm.com/");
        headers.put("accept-language" , "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        Response PincodeObjResp = middlewareServicesObject.SBFetchPinCodeDetails(PincodeObj , headers);
        int StatusCode = PincodeObjResp.getStatusCode();
       // Assert.assertEquals(StatusCode , 200);
//        State = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].state").toString();
//        City = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].city").toString();
//        Country = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].country").toString();
//        LOGGER.info(State + " " + City + " " + Country);
    }

    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the pincode details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchpincode_details_Pincode_missing(){
        SbPinCode PincodeObj = new SbPinCode("");
        Map<String , String> headers = new HashMap<>();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("devicemac" , "60:6E:E8:D6:95:DB");
        headers.put("deviceIdentifier" , "Xiaomi-M2004J19C-131535fc93929702");
        headers.put("client", "androidapp");
        // headers.put("x-mw-url-checksum" ,"PjOdYg/FVxWwj/8E1zFuClX7qHIRlySz+DzCEKW48cfC+6LCj6Y6w+iMXlIaK8ueKT5LFmd4fy4Fifgnj2/I1vEMroSXZgcRM2RKsh10SO1pUrW5lGw3rkH2tmHGzhQU");
        headers.put("androidid" , "131535fc93929702");
        headers.put("osversion" , "10");
        headers.put("x-src" , "GGClient");
        headers.put("devicemanufacturer" , "Xiaomi");
        headers.put("applanguage" , "en");
        headers.put("latitude" , "26.9008434");
        headers.put("isdevicerooted" , "false");
        headers.put("session_token" , sessionToken);
        headers.put("devicename" , "M2004J19C");
        headers.put("longitude" , "80.9288271");
        headers.put("ipaddress" , "************");
        headers.put("user-agent" , "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/114.0.5735.60 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.32-10.14.0-MB-IB1");
        headers.put("isbusyboxfound" , "false");
        headers.put("accept" , "application/json, text/plain, */*");
        headers.put("version" , version);
        headers.put("origin" , "https://oe-staging5.paytm.com");
        headers.put("x-requested-with" , "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging5.paytm.com/");
        headers.put("accept-language" , "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        Response PincodeObjResp = middlewareServicesObject.SBFetchPinCodeDetails(PincodeObj , headers);
        int StatusCode = PincodeObjResp.getStatusCode();
        Assert.assertEquals(StatusCode , 404);
//        State = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].state").toString();
//        City = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].city").toString();
//        Country = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].country").toString();
//        LOGGER.info(State + " " + City + " " + Country);
    }
    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the pincode details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchpincode_details_wrong_pincode(){
        SbPinCode PincodeObj = new SbPinCode("00000");
        Map<String , String> headers = new HashMap<>();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("devicemac" , "60:6E:E8:D6:95:DB");
        headers.put("deviceIdentifier" , "Xiaomi-M2004J19C-131535fc93929702");
        headers.put("client", "androidapp");
        // headers.put("x-mw-url-checksum" ,"PjOdYg/FVxWwj/8E1zFuClX7qHIRlySz+DzCEKW48cfC+6LCj6Y6w+iMXlIaK8ueKT5LFmd4fy4Fifgnj2/I1vEMroSXZgcRM2RKsh10SO1pUrW5lGw3rkH2tmHGzhQU");
        headers.put("androidid" , "131535fc93929702");
        headers.put("osversion" , "10");
        headers.put("x-src" , "GGClient");
        headers.put("devicemanufacturer" , "Xiaomi");
        headers.put("applanguage" , "en");
        headers.put("latitude" , "26.9008434");
        headers.put("isdevicerooted" , "false");
        headers.put("session_token" , sessionToken);
        headers.put("devicename" , "M2004J19C");
        headers.put("longitude" , "80.9288271");
        headers.put("ipaddress" , "************");
        headers.put("user-agent" , "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/114.0.5735.60 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.32-10.14.0-MB-IB1");
        headers.put("isbusyboxfound" , "false");
        headers.put("accept" , "application/json, text/plain, */*");
        headers.put("version" , version);
        headers.put("origin" , "https://oe-staging5.paytm.com");
        headers.put("x-requested-with" , "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging5.paytm.com/");
        headers.put("accept-language" , "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        Response PincodeObjResp = middlewareServicesObject.SBFetchPinCodeDetails(PincodeObj , headers);
        int StatusCode = PincodeObjResp.getStatusCode();
        Assert.assertEquals(StatusCode , 400);
//        State = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].state").toString();
//        City = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].city").toString();
//        Country = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].country").toString();
//        LOGGER.info(State + " " + City + " " + Country);
    }
    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the pincode details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchpincode_details_session_token_missing(){
        SbPinCode PincodeObj = new SbPinCode(pincode);
        Map<String , String> headers = new HashMap<>();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("devicemac" , "60:6E:E8:D6:95:DB");
        headers.put("deviceIdentifier" , "Xiaomi-M2004J19C-131535fc93929702");
        headers.put("client", "androidapp");
        // headers.put("x-mw-url-checksum" ,"PjOdYg/FVxWwj/8E1zFuClX7qHIRlySz+DzCEKW48cfC+6LCj6Y6w+iMXlIaK8ueKT5LFmd4fy4Fifgnj2/I1vEMroSXZgcRM2RKsh10SO1pUrW5lGw3rkH2tmHGzhQU");
        headers.put("androidid" , "131535fc93929702");
        headers.put("osversion" , "10");
        headers.put("x-src" , "GGClient");
        headers.put("devicemanufacturer" , "Xiaomi");
        headers.put("applanguage" , "en");
        headers.put("latitude" , "26.9008434");
        headers.put("isdevicerooted" , "false");
        headers.put("session_token" , "");
        headers.put("devicename" , "M2004J19C");
        headers.put("longitude" , "80.9288271");
        headers.put("ipaddress" , "************");
        headers.put("user-agent" , "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/114.0.5735.60 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.32-10.14.0-MB-IB1");
        headers.put("isbusyboxfound" , "false");
        headers.put("accept" , "application/json, text/plain, */*");
        headers.put("version" , version);
        headers.put("origin" , "https://oe-staging5.paytm.com");
        headers.put("x-requested-with" , "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging5.paytm.com/");
        headers.put("accept-language" , "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        Response PincodeObjResp = middlewareServicesObject.SBFetchPinCodeDetails(PincodeObj , headers);
        int StatusCode = PincodeObjResp.getStatusCode();
        Assert.assertEquals(StatusCode , 401);
//        State = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].state").toString();
//        City = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].city").toString();
//        Country = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].country").toString();
//        LOGGER.info(State + " " + City + " " + Country);
    }
    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the pincode details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchpincode_details_Invalid_charecters_in_pincode(){
        SbPinCode PincodeObj = new SbPinCode("226a02");
        Map<String , String> headers = new HashMap<>();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("devicemac" , "60:6E:E8:D6:95:DB");
        headers.put("deviceIdentifier" , "Xiaomi-M2004J19C-131535fc93929702");
        headers.put("client", "androidapp");
        // headers.put("x-mw-url-checksum" ,"PjOdYg/FVxWwj/8E1zFuClX7qHIRlySz+DzCEKW48cfC+6LCj6Y6w+iMXlIaK8ueKT5LFmd4fy4Fifgnj2/I1vEMroSXZgcRM2RKsh10SO1pUrW5lGw3rkH2tmHGzhQU");
        headers.put("androidid" , "131535fc93929702");
        headers.put("osversion" , "10");
        headers.put("x-src" , "GGClient");
        headers.put("devicemanufacturer" , "Xiaomi");
        headers.put("applanguage" , "en");
        headers.put("latitude" , "26.9008434");
        headers.put("isdevicerooted" , "false");
        headers.put("session_token" , sessionToken);
        headers.put("devicename" , "M2004J19C");
        headers.put("longitude" , "80.9288271");
        headers.put("ipaddress" , "************");
        headers.put("user-agent" , "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/114.0.5735.60 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.32-10.14.0-MB-IB1");
        headers.put("isbusyboxfound" , "false");
        headers.put("accept" , "application/json, text/plain, */*");
        headers.put("version" , version);
        headers.put("origin" , "https://oe-staging5.paytm.com");
        headers.put("x-requested-with" , "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging5.paytm.com/");
        headers.put("accept-language" , "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        Response PincodeObjResp = middlewareServicesObject.SBFetchPinCodeDetails(PincodeObj , headers);
        int StatusCode = PincodeObjResp.getStatusCode();
        Assert.assertEquals(StatusCode , 400);
//        State = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].state").toString();
//        City = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].city").toString();
//        Country = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].country").toString();
//        LOGGER.info(State + " " + City + " " + Country);
    }
    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the pincode details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchpincode_details_version(){
        SbPinCode PincodeObj = new SbPinCode(pincode);
        Map<String , String> headers = new HashMap<>();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("devicemac" , "60:6E:E8:D6:95:DB");
        headers.put("deviceIdentifier" , "Xiaomi-M2004J19C-131535fc93929702");
        headers.put("client", "androidapp");
        // headers.put("x-mw-url-checksum" ,"PjOdYg/FVxWwj/8E1zFuClX7qHIRlySz+DzCEKW48cfC+6LCj6Y6w+iMXlIaK8ueKT5LFmd4fy4Fifgnj2/I1vEMroSXZgcRM2RKsh10SO1pUrW5lGw3rkH2tmHGzhQU");
        headers.put("androidid" , "131535fc93929702");
        headers.put("osversion" , "10");
        headers.put("x-src" , "GGClient");
        headers.put("devicemanufacturer" , "Xiaomi");
        headers.put("applanguage" , "en");
        headers.put("latitude" , "26.9008434");
        headers.put("isdevicerooted" , "false");
        headers.put("session_token" , sessionToken);
        headers.put("devicename" , "M2004J19C");
        headers.put("longitude" , "80.9288271");
        headers.put("ipaddress" , "************");
        headers.put("user-agent" , "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/114.0.5735.60 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.32-10.14.0-MB-IB1");
        headers.put("isbusyboxfound" , "false");
        headers.put("accept" , "application/json, text/plain, */*");
        headers.put("version" , "");
        headers.put("origin" , "https://oe-staging5.paytm.com");
        headers.put("x-requested-with" , "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging5.paytm.com/");
        headers.put("accept-language" , "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        Response PincodeObjResp = middlewareServicesObject.SBFetchPinCodeDetails(PincodeObj , headers);
        int StatusCode = PincodeObjResp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
//        State = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].state").toString();
//        City = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].city").toString();
//        Country = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].country").toString();
//        LOGGER.info(State + " " + City + " " + Country);
    }

    @Test(priority = 6 , groups = {"Regression"}, description = "Fetch the pincode details")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchpincode_details_latlongmissing(){
        SbPinCode PincodeObj = new SbPinCode(pincode);
        Map<String , String> headers = new HashMap<>();
        headers.put("Host" , "goldengate-staging5.paytm.com");
        headers.put("devicemac" , "60:6E:E8:D6:95:DB");
        headers.put("deviceIdentifier" , "Xiaomi-M2004J19C-131535fc93929702");
        headers.put("client", "androidapp");
        // headers.put("x-mw-url-checksum" ,"PjOdYg/FVxWwj/8E1zFuClX7qHIRlySz+DzCEKW48cfC+6LCj6Y6w+iMXlIaK8ueKT5LFmd4fy4Fifgnj2/I1vEMroSXZgcRM2RKsh10SO1pUrW5lGw3rkH2tmHGzhQU");
        headers.put("androidid" , "131535fc93929702");
        headers.put("osversion" , "10");
        headers.put("x-src" , "GGClient");
        headers.put("devicemanufacturer" , "Xiaomi");
        headers.put("applanguage" , "en");
      //  headers.put("latitude" , "26.9008434");
        headers.put("isdevicerooted" , "false");
        headers.put("session_token" , sessionToken);
        headers.put("devicename" , "M2004J19C");
       // headers.put("longitude" , "80.9288271");
        headers.put("ipaddress" , "************");
        headers.put("user-agent" , "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/114.0.5735.60 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.32-10.14.0-MB-IB1");
        headers.put("isbusyboxfound" , "false");
        headers.put("accept" , "application/json, text/plain, */*");
        headers.put("version" , version);
        headers.put("origin" , "https://oe-staging5.paytm.com");
        headers.put("x-requested-with" , "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site" , "same-site");
        headers.put("sec-fetch-mode" , "cors");
        headers.put("sec-fetch-dest" , "empty");
        headers.put("referer" , "https://oe-staging5.paytm.com/");
        headers.put("accept-language" , "en-GB,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");

        Response PincodeObjResp = middlewareServicesObject.SBFetchPinCodeDetails(PincodeObj , headers);
        int StatusCode = PincodeObjResp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
//        State = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].state").toString();
//        City = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].city").toString();
//        Country = PincodeObjResp.jsonPath().getJsonObject("pincodeDetailsList[0].country").toString();
//        LOGGER.info(State + " " + City + " " + Country);
    }

    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , entityType);
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , "oil&gas");
        queryParams.put("fseSubRole" , "oil&gas");
        queryParams.put("solutionTypeLevel3" , "unorganized");
        queryParams.put("fseDesignation" , "fse");
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }

    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit_Role_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , entityType);
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , "");
        queryParams.put("fseSubRole" , "oil&gas");
        queryParams.put("solutionTypeLevel3" , "unorganized");
        queryParams.put("fseDesignation" , "fse");
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit_entity_type_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , "");
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , "oil&gas");
        queryParams.put("fseSubRole" , "oil&gas");
        queryParams.put("solutionTypeLevel3" , "unorganized");
        queryParams.put("fseDesignation" , "fse");
        headers.put("session_token" , sessionToken);
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }

    @Test(priority = 7 , groups = {"Regression"}, description = "Fetching Questions For Doing Revisit")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchQuestionsRevisit_session_token_missing(){
        Map<String , String> headers = new HashMap();
        Map<String , String> queryParams = new HashMap();
        Map<String , String> body = new HashMap<>();

        queryParams.put("entityType" , entityType);
        queryParams.put("solutionType" , "revisit_merchant");
        queryParams.put("questionType" , "additional");
        queryParams.put("solutionSubType" , "payments");
        queryParams.put("fseRole" , "oil&gas");
        queryParams.put("fseSubRole" , "oil&gas");
        queryParams.put("solutionTypeLevel3" , "unorganized");
        queryParams.put("fseDesignation" , "fse");
        headers.put("session_token" , "");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Android WebView\";v=\"121\", \"Chromium\";v=\"121\"");
        headers.put("devicemac", "60:6E:E8:D6:95:DB");
        headers.put("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        headers.put("client", "androidapp");
        headers.put("x-mw-url-checksum", "4da6h3Y0VZlGZpW2QjTSWCQNynvoP9jbaI8a9rcthd3KPe1ACbab7l+RAaFI4/fXSJKNFbGr/PAZrIflpypj2FrEAXK54kopd1A96p0kyXZ7KY0WrZ0d+AEqFKRNYmyv");
        headers.put("androidid", "6db875100ed7d7a4");
        headers.put("osversion", "10");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "Xiaomi");
        headers.put("x-mw-checksum", "LIve8Z6S2m3IN5cobVv/NZACQztAZSsoGLIc4A1mRx+wPpevzkGMh9JFmt1EU0cLYFCfYPWiGh/mEG3bU8w6vKV1DnnxJ5IE7b9PSReG+MgAXGE090BKBxGUQYlSDmxg");
        headers.put("applanguage", "en");
        headers.put("latitude", "12.8360957");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isdevicerooted", "false");

        headers.put("longitude", "77.6690907");
        headers.put("devicename", "M2004J19C");
        headers.put("ipaddress", "**************");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.178 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        headers.put("isbusyboxfound", "false");

        headers.put("origin", "https://oe-staging5.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://oe-staging5.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Fetchquestions obj = new Fetchquestions();
        Response objresp = middlewareServicesObject.fetchquestionsRevisit(obj , queryParams,headers);
        int StatusCode = objresp.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }

    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLead(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , custId);
        body.put("solution" , solutionType);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        if (StatusCode == 200){
            leadid = respobj.jsonPath().getString("leadId").toString();
            LOGGER.info("Lead Id is as follows : " + leadid);
        }
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadSessionTokenWrong(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", "ewiurhwe-kdjnldjjkfn-skdjn");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , custId);
        body.put("solution" , solutionType);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 410);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadsessionTokenEmpty(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", "");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , custId);
        body.put("solution" , solutionType);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 401);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadversionEmpty(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", "");
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , custId);
        body.put("solution" , solutionType);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();

        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadversionWrong(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", "1");
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , custId);
        body.put("solution" , solutionType);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadentitymissing(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , "");
        body.put("custId" , custId);
        body.put("solution" , solutionType);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadcustIdEmpty(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , "");
        body.put("solution" , solutionType);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadsolutionTypeEmpty(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , custId);
        body.put("solution" , "");
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadmerchantMobileEmpty(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , custId);
        body.put("solution" , solutionType);
        body.put("mobileNumber" , "");
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadmidEmpty(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , custId);
        body.put("solution" , solutionType);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , "");
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        if (StatusCode == 200){
            leadid = respobj.jsonPath().getString("leadId").toString();
            LOGGER.info("Lead Id is as follows : " + leadid);
        }
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadsolutiontypeEmpty(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , custId);
        body.put("solution" , solutionType);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , "");
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadfseRoleEmpty(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , custId);
        body.put("solution" , solutionType);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , "");
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        if (StatusCode == 200){
            leadid = respobj.jsonPath().getString("leadId").toString();
            LOGGER.info("Lead Id is as follows : " + leadid);
        }
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadfseSubRoleEmpty(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , custId);
        body.put("solution" , solutionType);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , "");
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();

        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadCheckSumEmpty(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , custId);
        body.put("solution" , solutionType);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 412);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadcustIdrandom(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , "203455");
        body.put("solution" , solutionType);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadsolutionRandom(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , custId);
        body.put("solution" , "Random");
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 500);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadmobileNumberrandom(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , custId);
        body.put("solution" , solutionType);
        body.put("mobileNumber" , "982347852345900");
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 500);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadwrongmid(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , custId);
        body.put("solution" , solutionType);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , "random");
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , fseRole);
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }
    @Test(priority = 8 , groups = {"Regression"}, description = "Submitting and generating lkead id")
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void revisitSubmitLeadfseRolerandom(){
        Map<String , String> headers = new HashMap<>();
        Map<String , String> body = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("x-mw-checksum", "/y0n0TNJ5GsoWzH3jo6Rv7xqB2sfKk2gssOmQhHG3g4c2S/2JPArKQ17A4MOFyEwd68gRg0dewyPtLV4xhRefoRfu9Ytpy1/tnVG+a775otPY0INkgv1dvrZHiFGJjhL");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac;", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "V5gpWTHZ5SdKd8GUFjHZpm04vEhZzQkyPLaqa0MqRj9Mqjk2QoojgUuE7CRFeXzjZtGQXJrdHwI4Nu7TKC/4GUZwVB3XbRHw5QG/R2mSUKSi/f7FZaRjG4PIc9LjgDo2");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("UncleScrooge" , "BabaBlackSheepWeAreInShitDeep");
        headers.put("priority", "u=1, i");
        body.put("entityType" , entityType);
        body.put("custId" , custId);
        body.put("solution" , solutionType);
        body.put("mobileNumber" , merchantMobile);
        body.put("mid" , mid);
        body.put("solutionTypeLevel3" , solutionTypeLevel3);
        body.put("fseRole" , "random");
        body.put("fseSubRole" , fseSubRole);
        RevisitSubmitDetails obj = new RevisitSubmitDetails();
        Response respobj = middlewareServicesObject.revisitSubmitDetails(obj , headers , body);
        int StatusCode = respobj.getStatusCode();
        Assert.assertEquals(StatusCode , 200);
    }

    @Test(priority = 9 , description = "Fetching the required Documents" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void fetchDocumentDetails() {

        Map<String, String> headers = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac", "");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "+AWc3o+G504fXF/Hgrwuxla9hduFZwqzBIY6EJQB9SkwDHNyG9feOpME70XIbWJUWQ0NGmzGW98yOb5rx2RrIPkOBksmHAYxTcotRMkjh9CBfr5piA4Z8+4F5x//uBgv");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("leadId", leadid);
        queryParams.put("channel", "GG_APP");
        queryParams.put("merchantCustId", custId);
        queryParams.put("solutionSubType", "edc");
        queryParams.put("fseRole", fseRole);
        queryParams.put("fseSubRole", fseSubRole);
        queryParams.put("solutionTypeLevel3", solutionTypeLevel3);
        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();
        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj, headers, queryParams);
        int statusCode = respObj.statusCode();
        Assert.assertEquals(statusCode, 200);
        if (statusCode == 200) {
             List<Map<String, Object>> docDetailsSet = respObj.jsonPath().getList("docDetailsSet");
            for (Map<String, Object> docDetails : docDetailsSet) {
                LOGGER.info("Document Details: " + docDetails);
            }
        }
    }

    @Test(priority = 10 , description = "Get kyb valid documents" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getKybValidDoc(){
        Map<String , String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac", "");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "OaAO28L7kAlihDhwFczIib1Zzo6V+pUz7ZCnNd/zru9OMBDKfpQlqNJ/g9CAaxLlatD4NpKmMqKza+7SoNjZHp4T4yJqXaZdNV/9jMSjudQwD3AKaxHlPypaWlpA86YB");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        RevisitGetKybValidDocs obj = new RevisitGetKybValidDocs(mid);
        Response respobj = middlewareServicesObject.getkybvaliddocsrevisit(obj , headers);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode , 200);
    }
    @Test(priority = 10 , description = "Get kyb valid documents" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getKybValidDocmidEmpty(){
        Map<String , String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac", "");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "OaAO28L7kAlihDhwFczIib1Zzo6V+pUz7ZCnNd/zru9OMBDKfpQlqNJ/g9CAaxLlatD4NpKmMqKza+7SoNjZHp4T4yJqXaZdNV/9jMSjudQwD3AKaxHlPypaWlpA86YB");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        RevisitGetKybValidDocs obj = new RevisitGetKybValidDocs("");
        Response respobj = middlewareServicesObject.getkybvaliddocsrevisit(obj , headers);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode , 400);
    }
    @Test(priority = 10 , description = "Get kyb valid documents" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getKybValidDocversionEmpty(){
        Map<String , String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", sessionToken);
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac", "");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "OaAO28L7kAlihDhwFczIib1Zzo6V+pUz7ZCnNd/zru9OMBDKfpQlqNJ/g9CAaxLlatD4NpKmMqKza+7SoNjZHp4T4yJqXaZdNV/9jMSjudQwD3AKaxHlPypaWlpA86YB");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", "");
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        RevisitGetKybValidDocs obj = new RevisitGetKybValidDocs(mid);
        Response respobj = middlewareServicesObject.getkybvaliddocsrevisit(obj , headers);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode , 200);
    }
    @Test(priority = 10 , description = "Get kyb valid documents" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getKybValidDocsessionTokenEmpty(){
        Map<String , String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", "");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac", "");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "OaAO28L7kAlihDhwFczIib1Zzo6V+pUz7ZCnNd/zru9OMBDKfpQlqNJ/g9CAaxLlatD4NpKmMqKza+7SoNjZHp4T4yJqXaZdNV/9jMSjudQwD3AKaxHlPypaWlpA86YB");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        RevisitGetKybValidDocs obj = new RevisitGetKybValidDocs(mid);
        Response respobj = middlewareServicesObject.getkybvaliddocsrevisit(obj , headers);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode , 401);
    }
    @Test(priority = 10 , description = "Get kyb valid documents" , groups = {"Regression"})
    @Owner(emailId = "<EMAIL>" , isAutomated = true)
    public void getKybValidDocrandomsessionToken(){
        Map<String , String> headers = new HashMap<>();
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("Cookie", "JSESSIONID=F1EAC68B9B0219645E27B752B4AABB91");
        headers.put("sec-ch-ua-platform", "\"Android\"");
        headers.put("isbusyboxfound", "false");
        headers.put("sec-ch-ua", "\"Android WebView\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        headers.put("sec-ch-ua-mobile", "?1");
        headers.put("client", "androidapp");
        headers.put("session_token", "wqerwerwefef");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("devicemac", "");
        headers.put("x-src", "GGClient");
        headers.put("devicemanufacturer", "vivo");
        headers.put("devicename", "V2143");
        headers.put("ipaddress", "*************");
        headers.put("x-mw-url-checksum", "OaAO28L7kAlihDhwFczIib1Zzo6V+pUz7ZCnNd/zru9OMBDKfpQlqNJ/g9CAaxLlatD4NpKmMqKza+7SoNjZHp4T4yJqXaZdNV/9jMSjudQwD3AKaxHlPypaWlpA86YB");
        headers.put("osversion", "13");
        headers.put("isdevicerooted", "false");
        headers.put("applanguage", "en");
        headers.put("user-agent", "Mozilla/5.0 (Linux; Android 13; V2143 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/2.0.3-10.43.0-RC3");
        headers.put("androidid", "9b30f13e9439c5bc");
        headers.put("version", version);
        headers.put("deviceidentifier", "vivo-V2143-9b30f13e9439c5bc");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        headers.put("priority", "u=1, i");
        RevisitGetKybValidDocs obj = new RevisitGetKybValidDocs(mid);
        Response respobj = middlewareServicesObject.getkybvaliddocsrevisit(obj , headers);
        int statusCode = respobj.getStatusCode();
        Assert.assertEquals(statusCode , 410);
    }

}