package OCL.Individual.Merchant100K;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.Resources.LanguagePreference;
import Request.MerchantService.v2.TnC;
import Request.MerchantService.v3.*;
import Request.Wallet.CreateUserWallet;
import Services.MechantService.MiddlewareServices;
import Services.UAD.UADServices;
import Services.Utilities.Utilities;
import Services.Wallet.WalletServices;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSchException;
import com.opencsv.CSVWriter;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/*
public class Flow100KTransportCategory extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(Flow100KTransportCategory.class);


    public static String AgentToken = "";
    public static  String AgentNo = "7011653794";
    public static  String version = "4.8.3";
    public static String mobileNo = "";
    public static String newState = "";
    public static String OTP = "";
    public static String nameMatchStatus = "";
    public static String CustId = "";
    public static String XMWToken = "";
    public static String leadId = "";

    ArrayList <String> Docum = new ArrayList<String>();
    public static String DocumentsArray = new String();
    public static String WorkFlowId = "";
    public static String addressUuid = "";
    public static String rrbUuid = "";
    public static String ownerAddressUUID = "";
    private static String MID = "";

    Faker GenerateFake = new Faker();
    public String lineOne = GenerateFake.address().streetAddress();
    public String lineTwo = GenerateFake.address().cityName();
    public String lineThree = GenerateFake.address().streetName();

    public static List<Object> DMS = new ArrayList<>();
    public static List<Object> DMSforUUID = new ArrayList<>();
    public static List<Object> DocType = new ArrayList<>();
    public static List<Object> DocProvided = new ArrayList<>();
    public static List<Object> DocumentRequest = new ArrayList<>();
    public static List<Object> NameOfDoc = new ArrayList<>();
    public static List<Object> TypeOfDoc = new ArrayList<>();
    public static String OePanelDocStatus = "REJECTED";
    public static String RejectionReason = "Wrong Photo";
    public static String DocumetRequestDeserialised = "";
    List <Object> GetDocuments = new ArrayList<>();

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    UADServices uadServicesObject = new UADServices();
    WalletServices walletServices = new WalletServices();

    TnC GetTnC = new TnC();
    GetDocStatus v3GetDocStat = new GetDocStatus();
    LanguagePreference getLanguage = new LanguagePreference();

    @BeforeClass(description = "Setting Agent token and generating new number")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void AgentLogin()
    {
        //AgentToken = AgentSessionToken("7771110007","paytm@123");
        AgentToken = CommonAgentToken;
        LOGGER.info("Agent Token is : " + AgentToken);

        //generating new Number
        Utilities accObj = new Utilities();
        mobileNo = accObj.randomMobileNumberGenerator();
        LOGGER.info("New Number for 100K Transport : " + mobileNo);
        CreateApplicantOauth(mobileNo);
    }


    @Test(description = "Positive SendOTP",priority = 0,groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_PositiveSendOtpSendOTP()
    {

        SendOtp SendOTPobj = new SendOtp();

        String expectedErrorMsg = "Otp sent to phone";
        LOGGER.info("This is Token " + AgentToken);
        Response ResObject = middlewareServicesObject.v3SentOtp(SendOTPobj,"INDIVIDUAL","p2p_100k",AgentToken,version,mobileNo,"merchant");
        SendOTPobj.setHttpStatus(ResObject.jsonPath().getString("httpStatus"));
        SendOTPobj.setMessage(ResObject.jsonPath().getString("message"));
        SendOTPobj.setState(ResObject.jsonPath().getString("state"));
        SendOTPobj.setStatus(ResObject.jsonPath().getString("status"));
        LOGGER.info("state" + " " + ResObject.jsonPath().getString("state"));
        newState = ResObject.jsonPath().getString("state");
        LOGGER.info("New State " + newState);
        String actualErrorMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        Assert.assertEquals(StatusCode,200);
        SendOTPobj.validateResponseAgainstJSONSchema("MerchantService/V3/SendOtp/SendOtpResponseSchema.json");
        //How to validate Schema ??
    }

    @Test(description = "Positive Get TnC",priority = 0,dependsOnMethods = "TC001_PositiveSendOtpSendOTP",groups ={"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_positiveGetTnc()
    {
        GetTnC.addParameter("tncSet","merchant");
        Response TnCResponse = middlewareServicesObject.v2GetTnC(GetTnC,"INDIVIDUAL","p2p_100k",version,AgentToken);
        String expectedMsg = "SUCCESS";
        String actualMsg = TnCResponse.jsonPath().getString("status");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        int statusCode = TnCResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        GetTnC.validateResponseAgainstJSONSchema("MerchantService/V2/TnC/TnCResponseSchema.json");
    }
    @Test(priority = 0,description = "Positive ValidateOTP",dependsOnMethods = "TC001_PositiveSendOtpSendOTP",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_PositiveValidateOtp() throws IOException, JSchException, InterruptedException {
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
        //OTP = getOTP(mobileNo);
       // OTP="888888";
        OTP = getOTPFromSellerPanel(mobileNo);

        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj,"INDIVIDUAL", "p2p_100k", AgentToken, version, mobileNo, "merchant", newState,OTP);
        leadId = ResObj.jsonPath().getString("leadId");
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");


        for (int i = 0; i<=3;i++)
        {

            if (leadId == null || leadId.isEmpty() || StatusCode!=200)
            {
                LOGGER.info("Lead Id is null, Inside loop : " +i);
                TC001_PositiveSendOtpSendOTP();
                waitForLoad(5000);
                ValidateOtp ValidateOTPobjErr = new ValidateOtp(P.TESTDATA.get("validateOtp"));
               // OTP = getOTP(mobileNo);
                OTP = getOTPFromSellerPanel(mobileNo);

                LOGGER.info("This is OTP " + OTP);
                Response ResNewObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobjErr,"INDIVIDUAL", "p2p_100k", AgentToken, version, mobileNo, "merchant", newState,OTP);
                leadId = ResNewObj.jsonPath().getString("leadId");
                 StatusCode = ResNewObj.getStatusCode();
                CustId = ResNewObj.jsonPath().getString("custId");
            }
            else
            {
                LOGGER.info("This is Lead ID : " + leadId);
                Assert.assertNotEquals(leadId,null);
                LOGGER.info("CustId " + CustId);
                Assert.assertNotEquals(CustId,null);
                Assert.assertEquals(StatusCode, 200);
                break;
            }
        }

    }

    @Test(priority = 0,dependsOnMethods = "TC003_PositiveValidateOtp",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_PositiveGetMerchantStatus() throws IOException, JSchException
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "p2p_100k", AgentToken, version);
        int StatusCode = resObjGetMerchant.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        callv3GetMerch.validateResponseAgainstJSONSchema("MerchantService/V3/GetMerchant/GetMerchantResponseSchema.json");
    }

    @Test(priority = 0,dependsOnMethods = "TC004_PositiveGetMerchantStatus",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_positiveGetDocStatus() {
        String files = "Paytm+Accepted+Here+Sticker+2,Paytm+Accepted+Here+Sticker,Paytm+Accepted+Here+Sticker+1,QR+Sticker,QR+Sticker+1,QR+Sticker+2,Cancelled+Cheque+Photo,businessOwnerPhoto";
        //  v3GetDocStat.addParameter("fileNames", files);
        v3GetDocStat.addUrlParameter("fileNames",files);
        Response v3GetSatatusResp = middlewareServicesObject.v3getDocStatus(v3GetDocStat, CustId, "INDIVIDUAL", "p2p_100k", AgentToken, version);
        int StatusCode = v3GetSatatusResp.getStatusCode();
        v3GetDocStat.validateResponseAgainstJSONSchema("MerchantService/V3/GetDocStatus/GetDocStatusSchema.json");
    }

    @Test(priority = 0,groups = {"Regression"},dependsOnMethods = "TC004_PositiveGetMerchantStatus",description = "Positive Penny Drop")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC006_positivePennyDrop()
    {
        PennyDrop doPennyDrop = new PennyDrop();
        doPennyDrop.getProperties().setProperty("mobile",mobileNo);
        doPennyDrop.getProperties().setProperty("ifsc","ANDB0002041");
        doPennyDrop.getProperties().setProperty("bankAccountNumber","***************");
        Response getBanksResponse = middlewareServicesObject.v3PennyDrop(doPennyDrop,version,"AASHIT SHARMA","INDUSIND BANK",CustId,AgentToken);
        nameMatchStatus = getBanksResponse.jsonPath().getString("nameMatchStatus");
        LOGGER.info("Name Match Status = " +nameMatchStatus);
        int StatusCode = getBanksResponse.getStatusCode();
        if(StatusCode == 200)
        {
            Assert.assertEquals(StatusCode,200);
        }

        if (StatusCode == 500)
        {
            String expectedFailureMsg = "We are unable to connect with your bank to verify your details. Please try after some time or provide a different bank account";
            String actualFailureMsg = getBanksResponse.jsonPath().getString("message");
            Assert.assertTrue(actualFailureMsg.contains(expectedFailureMsg));

            Assert.assertEquals(StatusCode,500);
        }
        doPennyDrop.validateResponseAgainstJSONSchema("MerchantService/V3/PennyDrop/PennyDropResponseSchema.json");
    }
    //Submit Merchant API

    @Test(priority = 0,description = "Positive Submit Merchant",groups = {"Regression"},dependsOnMethods = "TC004_PositiveGetMerchantStatus")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_positiveSubmitMerchant()
    {
        SubmitMerchant postMerchant = new SubmitMerchant(CustId,P.TESTDATA.get("SubmitMerchant100KTransport"));
        postMerchant.getProperties().setProperty("line1",lineOne);
        postMerchant.getProperties().setProperty("line2",lineTwo);
        postMerchant.getProperties().setProperty("line3",lineThree);
        Response submitMerchResponse = middlewareServicesObject.v3SubmitMerchant(postMerchant,mobileNo,mobileNo,"ANDB0002029",version,"INDIVIDUAL","p2p_100k",AgentToken);
        int StatusCode = submitMerchResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        postMerchant.validateResponseAgainstJSONSchema("MerchantService/V3/SubmitMerchant/SubmitMerchantResponseSchema.json");
    }

    @Test(priority = 0,description = "Positive Fetch Documents for 100K Transport",dependsOnMethods ="TC007_positiveSubmitMerchant",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_PositiveFetchDocs100K() throws JsonProcessingException
    {
        Map<String,String>queryDocUpload = new HashMap<>();
        queryDocUpload.put("type", "jpg");
        queryDocUpload.put("entityType", "INDIVIDUAL");
        queryDocUpload.put("solutionType", "p2p_100k");
        queryDocUpload.put("merchantCustId", CustId);
        queryDocUpload.put("leadId", leadId);
        queryDocUpload.put("solutionTypeLevel2","nonMinKyc");

        Map<String,String>queryFetchDoc = new HashMap<>();
        queryFetchDoc.put("entityType", "INDIVIDUAL");
        queryFetchDoc.put("solution", "p2p_100k");
        queryFetchDoc.put("leadId", leadId);
        queryFetchDoc.put("channel", "GG_APP");
        queryFetchDoc.put("merchantCustId", CustId);
        queryFetchDoc.put("category","Transport");
        queryFetchDoc.put("subCategory","Taxi");
        queryFetchDoc.put("solutionSubType","nonMinKyc");

        GgAppDynamicDocUpload(AgentToken,version,mobileNo,queryFetchDoc,queryDocUpload);

    }



    @Test(priority = 0,description = "Create User at Wallet",groups = {"Regression"},dependsOnMethods = "TC008_PositiveFetchDocs100K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC009_PositiveCreateUserWallet100K()
    {
        CreateUserWallet walletObj = new CreateUserWallet();

        walletObj.getProperties().setProperty("ssoId",CustId);
        walletObj.getProperties().setProperty("mobileNumber",mobileNo);

        Response walletResp = walletServices.createUserWallet(walletObj);

        String msg = walletResp.jsonPath().getString("statusMessage");
        Assert.assertTrue(msg.contains("Wallet Activated"));

        String statusMsg = walletResp.jsonPath().getString("statusCode");
        Assert.assertTrue(statusMsg.contains("SUCCESS"));

    }


 @Test(priority = 0,description = "Get Cookie for OE Panel",groups = {"Regression"},dependsOnMethods = "TC008_PositiveFetchDocs100K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC010_PositiveGetOEPanelCookie()
    {
        XMWToken=XMWCookie ;
        LOGGER.info("XMW Cookie is : " + XMWCookie);
    }

    @Test(priority = 0,description = "Fetch Lead Details on Panel",groups = {"Regression"},dependsOnMethods = "TC010_PositiveGetOEPanelCookie")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC011_PositiveFetchLeadPanel()
    {

        try
        {
            Map <String,String> RequestPanel = new HashMap<>();
            Map <String,String> ResponsePanel = new HashMap<>();

            RequestPanel.put("docStatus",OePanelDocStatus);
            RequestPanel.put("rejectionReason",RejectionReason);
            RequestPanel.put("leadId",leadId);


            ResponsePanel = FetchPanelLead(RequestPanel);

            DocumetRequestDeserialised = ResponsePanel.get("DocumenstArray");
            WorkFlowId = ResponsePanel.get("WorkFlowId");
            LOGGER. info("Lead Stage is : " + ResponsePanel.get("LeadStage"));

        }
        catch (Exception e)
        {
            LOGGER.info("Execption " + e);
            LOGGER.info(" Line No. at : " + e.getStackTrace()[0].getLineNumber());

        }
    }

    @Test(priority = 0,description = "Reallocating Agent to Rajan",groups = {"Regression"},dependsOnMethods = "TC011_PositiveFetchLeadPanel")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC012_ReallocatingAgent100KTransport()
    {
        LOGGER.info("Inside ReallocatingAgent Method ");
        ReallocatingAgent(leadId,"1152");

    }

    @Test(priority = 0,description = "Submit rejected lead from OE panel ",groups = {"Regression"},dependsOnMethods = "TC012_ReallocatingAgent100KTransport")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC013_PositiveRejectedLeadPanel()
    {
        EditLead v1EditLeadObj=new EditLead(leadId, P.TESTDATA.get("EditLead100KTransportRejected"));

        v1EditLeadObj.getProperties().setProperty("documents",DocumetRequestDeserialised);
        v1EditLeadObj.getProperties().setProperty("workflowStatusId",WorkFlowId);

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "**********", "**********", XMWToken,"application/json");

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode,200);

        String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
        Assert.assertTrue(LeadSatge.contains("DATA_ENTRY_REJECTED"));
    }

    @Test(priority = 0,description = "Positive fetch merchant status after rejection",dependsOnMethods = "TC013_PositiveRejectedLeadPanel",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC014_PositiveGetMerchantStatusAfterRejection() throws IOException, JSchException
    {
        LOGGER.info("Inside PositiveGetMerchantStatusAfterRejection Method");
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "p2p_100k", AgentToken, version);

        addressUuid = resObjGetMerchant.jsonPath().getJsonObject("merchantDetails.suggestedRelatedBusinesses[0].address.addressUuid").toString();
        rrbUuid =  resObjGetMerchant.jsonPath().getJsonObject("merchantDetails.relatedBusinessUuid").toString();
        ownerAddressUUID =  resObjGetMerchant.jsonPath().getJsonObject("merchantDetails.suggestedOwnerAddress[0].addressUuid").toString();

        LOGGER.info("Address UUID : " + addressUuid + " RRB UUID : " + rrbUuid + " Owner Address UUID : " + ownerAddressUUID);

        int StatusCode = resObjGetMerchant.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0,description = "Positive Submit Merchant After Rejection",groups = {"Regression"},dependsOnMethods = "TC014_PositiveGetMerchantStatusAfterRejection")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC015_positiveSubmitMerchantAfterRejection() throws JsonProcessingException {
        SubmitMerchant postMerchant = new SubmitMerchant(CustId,P.TESTDATA.get("Submit100KTransportAfterRejection"));

        postMerchant.getProperties().setProperty("addressUuid",addressUuid);
        postMerchant.getProperties().setProperty("relatedBusinessUuid",rrbUuid);
        postMerchant.getProperties().setProperty("ownerAddressUUID",ownerAddressUUID);
        postMerchant.getProperties().setProperty("line1",lineOne);
        postMerchant.getProperties().setProperty("line2",lineTwo);
        postMerchant.getProperties().setProperty("line3",lineThree);
        Response submitMerchResponse = middlewareServicesObject.v3SubmitMerchant(postMerchant,mobileNo,mobileNo,"ANDB0002029",version,"INDIVIDUAL","p2p_100k",AgentToken);
        int StatusCode = submitMerchResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        postMerchant.validateResponseAgainstJSONSchema("MerchantService/V3/SubmitMerchant/SubmitMerchantResponseSchema.json");

        if(StatusCode == 200)
        {
            TC008_PositiveFetchDocs100K();


        }
    }

    @Test(priority = 0,description = "Fetch Lead Details on Panel",groups = {"Regression"},dependsOnMethods = "TC015_positiveSubmitMerchantAfterRejection")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC016_PositiveSubmitLeadPanel()
    {
        OePanelDocStatus = "APPROVED";
        RejectionReason = null;
        TC011_PositiveFetchLeadPanel();

        TC012_ReallocatingAgent100KTransport();

            EditLead v1EditLeadObj = new EditLead(leadId, P.TESTDATA.get("EditLead100KTransport"));

        v1EditLeadObj.getProperties().setProperty("documents", DocumetRequestDeserialised);
        v1EditLeadObj.getProperties().setProperty("reEnterAccountNumber", mobileNo);
        v1EditLeadObj.getProperties().setProperty("workflowStatusId", WorkFlowId);

            Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "**********", "**********", XMWToken, "application/json");

            int statusCode = responseObject.getStatusCode();
            Assert.assertEquals(statusCode, 200);

            String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
            Assert.assertTrue(LeadSatge.contains("PANEL_SUCCESS"));

          //  v1EditLeadObj.validateResponseAgainstJSONSchema("MerchantServiceOEPanelV1EditLead/EditLead100KSchema.json");


    }

    @Test(priority = 0,description = "Fetch Lead Details on Panel",groups = {"Regression"},dependsOnMethods = "TC016_PositiveSubmitLeadPanel",timeOut = 60000)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC017_PgCallBack100K() throws SQLException, JsonProcessingException {
        DbName = DbStaging6;
        MID = PG_CallBack_Insatnt50K(CustId);
        PgCallBackFromPanelRef(leadId,CustId,MID);
    }


    @AfterClass(description = "Adding Details on CSV")
    public void AddingDataCsv() throws IOException {
        LOGGER.info("In After Test of " +getClass());

        File fileUpload = new File("OnboardedMerchant.csv") ;
        // Create csv file
        FileWriter outputfile = new FileWriter(fileUpload,true);

        // Write to CSV file which is open
        CSVWriter writer = new CSVWriter(outputfile);

        if(!MID.isEmpty()) {
            LOGGER.info("MID Is not Empty");
            // add data to csv
            String[] data1 = {MID, CustId, mobileNo, "p2p_100k", "Transport Category"};
            writer.writeNext(data1);
            writer.flush();
            writer.close();
        }
        else
        {
            LOGGER.info("MID is Empty");
        }
    }
}*/
