package OCL.Individual.Merchant100K;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.v1.Cart.PinCode;
import Request.MerchantService.v1.Resources.LanguagePreference;
import Request.MerchantService.v2.Banks;
import Request.MerchantService.v2.TnC;
import Request.MerchantService.v3.*;
import Request.UAD.Category;
import Request.UAD.SubCategory;
import Request.Wallet.CreateUserWallet;
import Services.MechantService.MiddlewareServices;
import Services.UAD.UADServices;
import Services.Utilities.Utilities;
import Services.Wallet.WalletServices;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSchException;
import com.opencsv.CSVWriter;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/*
public class Flow100K extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(Flow100K.class);


    public static String AgentToken = "";
    public static  String AgentNo = "7011653794";
    public static  String version = "4.8.3";
    public static String mobileNo = "";
    public static String newState = "";
    public static String OTP = "";
    public static String nameMatchStatus = "";
    public static String CustId = "";
    public static String invalidCustID = "AASHIT";
    public static String emptyCustID = "";
    public static String noLeadCustID = "1000527769";
    public static String noOauthCustID = "10000001";
    public static String XMWToken = "";
    public static String leadId = "";
    public static String MID = "";

    Faker GenerateFake = new Faker();
    public String lineOne = GenerateFake.address().streetAddress();
    public String lineTwo = GenerateFake.address().cityName();
    public String lineThree = GenerateFake.address().streetName();

    public static String WorkFlowId = "";
    public static String addressUuid = "";
    public static String rrbUuid = "";
    public static String ownerAddressUUID = "";
    public static List<String> docsToUpload = new ArrayList<>();

    public static List<Object> DMS = new ArrayList<>();
    public static List<Object> DMSforUUID = new ArrayList<>();
    public static List<Object> DocType = new ArrayList<>();
    public static List<Object> DocProvided = new ArrayList<>();
    public static List<Object> DocumentRequest = new ArrayList<>();
    public static List<Object> NameOfDoc = new ArrayList<>();
    public static List<Object> TypeOfDoc = new ArrayList<>();
    public static String OePanelDocStatus = "REJECTED";
    public static String RejectionReason = "Wrong Photo";
    public static String DocumetRequestDeserialised = "";
    List <Object> GetDocuments = new ArrayList<>();

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    UADServices uadServicesObject = new UADServices();
    WalletServices walletServices = new WalletServices();

    TnC GetTnC = new TnC();
    GetDocStatus v3GetDocStat = new GetDocStatus();
    LanguagePreference getLanguage = new LanguagePreference();


    @BeforeClass
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void AgentLogin100k()
    {
        //AgentToken=AgentSessionToken("9953828631","paytm@123");
       // AgentToken = CommonAgentToken;
        AgentToken = ApplicantToken("9953828631", "paytm@123");
        LOGGER.info("Agent Token is : " + AgentToken);

        Response responseObject=middlewareServicesObject.v1Token("7771216290","paytm@123");
        XMWCookie=responseObject.getHeader("Set-Cookie").toString();
        LOGGER.info(" OE Panel Cookie is  : " + XMWCookie);

        //generating new Number
        Utilities accObj = new Utilities();
        mobileNo = accObj.randomMobileNumberGenerator();
        LOGGER.info("New Number is for 100K : " + mobileNo);
        CreateApplicantOauth(mobileNo);
    }


    //V3 Send OTP API
    @Test(description = "Empty Mobile Number",priority = 21)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyMobileSendOTP()
    {
        SendOtp SendOTPobj = new SendOtp();


        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Response ResObject = middlewareServicesObject.v3SentOtp(SendOTPobj,"INDIVIDUAL","p2p_100k",AgentToken,version,"","merchant");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode,200);
    }
    @Test(description = "Mobile number is passed as null",priority = 22)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void mobileAsNullSendOTP()
    {
        SendOtp SendOTPobj = new SendOtp();

        String expectedMsg2 = "Please provide a Paytm registered mobile number";
        Response ResObject = middlewareServicesObject.v3SentOtp(SendOTPobj,"INDIVIDUAL","p2p_100k",AgentToken,version,"null","merchant");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg2));
        Assert.assertEquals(StatusCode,200);
    }
    @Test(description = "Mobile number is passed less then 10 digits",priority = 23)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void mobileNoValidationSendOTP()
    {
        SendOtp SendOTPobj = new SendOtp();

        String expectedMsg3 = "Please provide a Paytm registered mobile number";
        Response ResObject = middlewareServicesObject.v3SentOtp(SendOTPobj,"INDIVIDUAL","p2p_100k",AgentToken,version,"12345","merchant");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertTrue(actualMsg.contains(expectedMsg3));
        Assert.assertEquals(StatusCode,200);
    }
    @Test(description = "Where User Type is invalid",priority = 24)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidUserTypeSendOTP()
    {
        SendOtp SendOTPobj = new SendOtp();

        String expectedMsg4 = "Invalid action";
        Response ResObject = middlewareServicesObject.v3SentOtp(SendOTPobj,"INDIVIDUAL","p2p_100k",AgentToken,version,mobileNo,"merchan");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertTrue(actualMsg.contains(expectedMsg4));
        Assert.assertEquals(StatusCode,200);
    }
    @Test(description = "Where User Type is of different solution type",priority = 25)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffUserTypeSendOTP()
    {
        SendOtp SendOTPobj = new SendOtp();

        Response ResObject = middlewareServicesObject.v3SentOtp(SendOTPobj,"INDIVIDUAL","p2p_100k",AgentToken,version,mobileNo,"company");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        // Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode,200);
    }
    @Test(description = "Where Entity type is invalid",priority = 26)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidEntityTypeSendOTP()
    {
        SendOtp SendOTPobj = new SendOtp();

        // String expectedMsg = "Invalid Entity Type";
        Response ResObject = middlewareServicesObject.v3SentOtp(SendOTPobj,"INDI","p2p_100k",AgentToken,version,mobileNo,"merchant");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        // Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode,200,400);
    }
    @Test(description = "Where Entity type is empty",priority = 27)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyEntityTypeSendOTP()
    {
        SendOtp SendOTPobj = new SendOtp();

        //String expectedMsg = "Invalid Entity Type";
        Response ResObject = middlewareServicesObject.v3SentOtp(SendOTPobj,"","p2p_100k",AgentToken,version,mobileNo,"merchant");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        // Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode,200);
    }
    @Test(description = "Where Entity type different than required for solution",priority = 28)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffEntityTypeSendOTP()
    {
        SendOtp SendOTPobj = new SendOtp();

        //String expectedMsg = "Invalid Entity Type";
        Response ResObject = middlewareServicesObject.v3SentOtp(SendOTPobj,"PUBLIC_LIMITED","p2p_100k",AgentToken,version,mobileNo,"merchant");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode,200);
    }
    @Test(description = "Where solution type is invalid",priority = 29)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidSolutionTypeSendOTP()
    {
        SendOtp SendOTPobj = new SendOtp();

        String expectedMsg5 = "Invalid solution type";
        Response ResObject = middlewareServicesObject.v3SentOtp(SendOTPobj,"INDIVIDUAL","p2p_100",AgentToken,version,mobileNo,"merchant");
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertTrue(actualMsg.contains(expectedMsg5));
        Assert.assertEquals(StatusCode,200);
    }
    @Test(description = "Where solution type is different",priority = 30)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffSolutionTypeSendOTP()
    {
        SendOtp SendOTPobj = new SendOtp();

        String expectedMsg6 = "Invalid solution type";
        Response ResObject = middlewareServicesObject.v3SentOtp(SendOTPobj,"INDIVIDUAL","company_onboard",AgentToken,version,mobileNo,"merchant");
        SendOTPobj.setHttpStatus(ResObject.jsonPath().getString("httpStatus"));
        SendOTPobj.setMessage(ResObject.jsonPath().getString("message"));
        SendOTPobj.setState(ResObject.jsonPath().getString("state"));
        SendOTPobj.setStatus(ResObject.jsonPath().getString("status"));
        String actualMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertTrue(actualMsg.contains(expectedMsg6));
        Assert.assertEquals(StatusCode,200);
    }
    @Test(description = "Positive SendOTP",priority = 31,groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveSendOtpSendOTP()
    {

        SendOtp SendOTPobj = new SendOtp();

        String expectedErrorMsg = "Otp sent to phone";
        LOGGER.info("This is Token " + AgentToken);
        Response ResObject = middlewareServicesObject.v3SentOtp(SendOTPobj,"INDIVIDUAL","p2p_100k",AgentToken,version,mobileNo,"merchant");
        SendOTPobj.setHttpStatus(ResObject.jsonPath().getString("httpStatus"));
        SendOTPobj.setMessage(ResObject.jsonPath().getString("message"));
        SendOTPobj.setState(ResObject.jsonPath().getString("state"));
        SendOTPobj.setStatus(ResObject.jsonPath().getString("status"));
        LOGGER.info("state" + " " + ResObject.jsonPath().getString("state"));
        newState = ResObject.jsonPath().getString("state");
        LOGGER.info("New State " + newState);
        String actualErrorMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        Assert.assertEquals(StatusCode,200);
        SendOTPobj.validateResponseAgainstJSONSchema("MerchantService/V3/SendOtp/SendOtpResponseSchema.json");
        //How to validate Schema ??
    }
    //V2 TnC
    @Test(description = "Invalid Entity Get TnC",priority = 32)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidEntityGetTnc()
    {
        GetTnC.addParameter("tncSet","merchant");
        Response TnCResponse = middlewareServicesObject.v2GetTnC(GetTnC,"INDIVI","p2p_100k",version,AgentToken);
        Assert.assertFalse(TnCResponse.jsonPath().getJsonObject("").toString().contains("url"));
    }
    @Test(description = "Different Entity Get TnC",priority = 33)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffEntityGetTnc()
    {
        GetTnC.addParameter("tncSet","merchant");
        Response TnCResponse = middlewareServicesObject.v2GetTnC(GetTnC,"PUBLIC_LIMITED","p2p_100k",version,AgentToken);
        Assert.assertFalse(TnCResponse.jsonPath().getJsonObject("").toString().contains("url"));
    }
    @Test(description = "Empty Entity Get TnC",priority = 34)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyEntityGetTnc()
    {
        GetTnC.addParameter("tncSet","merchant");
        Response TnCResponse = middlewareServicesObject.v2GetTnC(GetTnC,"","p2p_100k",version,AgentToken);
        Assert.assertFalse(TnCResponse.jsonPath().getJsonObject("").toString().contains("url"));
    }
    @Test(description = "Invalid Solution Get TnC",priority = 35)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidSolutionGetTnc()
    {
        GetTnC.addParameter("tncSet","merchant");
        Response TnCResponse = middlewareServicesObject.v2GetTnC(GetTnC,"INDIVIDUAL","p2p_10",version,AgentToken);
        Assert.assertFalse(TnCResponse.jsonPath().getJsonObject("").toString().contains("url"));
    }
    @Test(description = "Invalid Solution Get TnC",priority = 36)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffSolutionGetTnc()
    {
        GetTnC.addParameter("tncSet","merchant");
        Response TnCResponse = middlewareServicesObject.v2GetTnC(GetTnC,"INDIVIDUAL","offline_50k",version,AgentToken);
        String expectedMsg = "SUCCESS";
        String actualMsg = TnCResponse.jsonPath().getString("status");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
    }
    @Test(description = "Empty Solution Get TnC",priority = 37)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptySolutionGetTnc()
    {
        GetTnC.addParameter("tncSet","merchant");
        Response TnCResponse = middlewareServicesObject.v2GetTnC(GetTnC,"INDIVIDUAL","",version,AgentToken);
        Assert.assertFalse(TnCResponse.jsonPath().getJsonObject("").toString().contains("url"));
    }
    @Test(description = "Invalid TnC set Get TnC",priority = 38)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidTncSetGetTnc()
    {
        GetTnC.addParameter("tncSet","merc");
        Response TnCResponse = middlewareServicesObject.v2GetTnC(GetTnC,"INDIVIDUAL","p2p_100k",version,AgentToken);
        int statusCode = TnCResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(description = "Empty TnC set Get TnC",priority = 39)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyTncSetGetTnc()
    {
        GetTnC.addParameter("tncSet","");
        Response TnCResponse = middlewareServicesObject.v2GetTnC(GetTnC,"INDIVIDUAL","p2p_100k",version,AgentToken);
        int statusCode = TnCResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }
    @Test(description = "Positive Get TnC",priority = 40,dependsOnMethods = "PositiveSendOtpSendOTP",groups ={"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void positiveGetTnc()
    {
        GetTnC.addParameter("tncSet","merchant");
        Response TnCResponse = middlewareServicesObject.v2GetTnC(GetTnC,"INDIVIDUAL","p2p_100k",version,AgentToken);
        String expectedMsg = "SUCCESS";
        String actualMsg = TnCResponse.jsonPath().getString("status");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        int statusCode = TnCResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        GetTnC.validateResponseAgainstJSONSchema("MerchantService/V2/TnC/TnCResponseSchema.json");
    }
    //V3 Validate OTP API
    @Test(priority = 41,description = "Provide Invalid OTP")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidOtpValidateOTP() throws IOException, JSchException {
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
        OTP = "000000";
        PositiveSendOtpSendOTP();
        String expectedMsg = "Invalid OTP entered. Please try again with correct OTP";
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj,"INDIVIDUAL", "p2p_100k", AgentToken, version, mobileNo, "merchant", newState,"888888");
        int StatusCode = ResObj.getStatusCode();
        String actualMsg = ResObj.jsonPath().getString("message");
//        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 42,description = "Provide 4 digit OTP")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void fourDigitOtpValidateOTP() throws IOException, JSchException {
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
        OTP = "0000";
        String expectedMsg = "Invalid OTP entered. Please try again with correct OTP";
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj,"INDIVIDUAL", "p2p_100k", AgentToken, version, mobileNo, "merchant", newState,OTP);
        int StatusCode = ResObj.getStatusCode();
        String actualMsg = ResObj.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 43,description = "Provide empty OTP")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyOtpValidateOTP() throws IOException, JSchException {
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
        OTP = "";
        String expectedMsg = "Server error. Please try again";
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj,"INDIVIDUAL", "p2p_100k", AgentToken, version, mobileNo, "merchant", newState,OTP);
        int StatusCode = ResObj.getStatusCode();
        String actualMsg = ResObj.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 44,description = "Provide empty mobile number in validate OTP",enabled = false)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyMobileValidateOTP() throws IOException, JSchException, InterruptedException {
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
        PositiveSendOtpSendOTP();
       // OTP = getOTP(mobileNo);
        OTP = getOTPFromSellerPanel(mobileNo);

        String expectedMsg = "Request Validation failed for create lead request";
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj,"INDIVIDUAL", "p2p_100k", AgentToken, version, "", "merchant", newState,OTP);
        int StatusCode = ResObj.getStatusCode();
        String actualMsg = ResObj.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 45,description = "Provide invalid state in validate OTP",enabled = false)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidStateValidateOTP() throws IOException, JSchException, InterruptedException {
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
       // OTP = getOTP(mobileNo);
        OTP = getOTPFromSellerPanel(mobileNo);

        String expectedMsg = "Dear user, please contact support at +91-7210972109 or <EMAIL>";
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj,"INDIVIDUAL", "p2p_100k", AgentToken, version, mobileNo, "merchant", AgentToken,OTP);
        int StatusCode = ResObj.getStatusCode();
        String actualMsg = ResObj.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 46,description = "Provide empty state in validate OTP",enabled = false)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyStateValidateOTP() throws IOException, JSchException, InterruptedException {
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
     //   OTP = getOTP(mobileNo);
        OTP = getOTPFromSellerPanel(mobileNo);

        String expectedMsg = "Server error. Please try again";
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj,"INDIVIDUAL", "p2p_100k", AgentToken, version, mobileNo, "merchant", "",OTP);
        int StatusCode = ResObj.getStatusCode();
        String actualMsg = ResObj.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 48,description = "Provide invalid user type in validate OTP",enabled = false)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidUserTypeValidateOTP() throws IOException, JSchException, InterruptedException {
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
        //OTP = getOTP(mobileNo);
        OTP = getOTPFromSellerPanel(mobileNo);

        String expectedMsg = "Invalid action";
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj,"INDIVIDUAL", "p2p_100k", AgentToken, version, mobileNo, "merchan", newState,OTP);
        int StatusCode = ResObj.getStatusCode();
        String actualMsg = ResObj.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 49,description = "Provide invalid different user type in validate OTP",enabled = false)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffUserTypeValidateOTP() throws IOException, JSchException, InterruptedException {
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
       // OTP = getOTP(mobileNo);
        OTP = getOTPFromSellerPanel(mobileNo);

        String expectedMsg = "Failed to validate OTP";
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj,"INDIVIDUAL", "p2p_100k", AgentToken, version, mobileNo, "company", newState,OTP);
        int StatusCode = ResObj.getStatusCode();
        String actualMsg = ResObj.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 500);
    }
    @Test(priority = 50,description = "Provide empty user type in validate OTP",enabled = false)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyUserTypeValidateOTP() throws IOException, JSchException, InterruptedException {
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
       // OTP = getOTP(mobileNo);
        OTP = getOTPFromSellerPanel(mobileNo);

        String expectedMsg = "Invalid action";
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj,"INDIVIDUAL", "p2p_100k", AgentToken, version, mobileNo, "", newState,OTP);
        int StatusCode = ResObj.getStatusCode();
        String actualMsg = ResObj.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 51,description = "Provide different entity type in validate OTP",enabled = false)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffEntityTypeValidateOTP() throws IOException, JSchException, InterruptedException {
        PositiveSendOtpSendOTP();
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
    //    OTP = getOTP(mobileNo);
        OTP = getOTPFromSellerPanel(mobileNo);

        String expectedMsg = "Your details could not be saved. Lead already exist with different EntityType, please continue with same.";
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj,"PROPRIETORSHIP", "p2p_100k", AgentToken, version, mobileNo, "merchant", newState,OTP);
        int StatusCode = ResObj.getStatusCode();
        String actualMsg = ResObj.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 52,description = "Provide invalid entity type in validate OTP",enabled = false)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidEntityTypeValidateOTP() throws IOException, JSchException, InterruptedException {
        PositiveSendOtpSendOTP();
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
      //  OTP = getOTP(mobileNo);
        OTP = getOTPFromSellerPanel(mobileNo);

        String expectedMsg = "Failed to validate OTP";
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj,"INDI", "p2p_100k", AgentToken, version, mobileNo, "merchant", newState,OTP);
        int StatusCode = ResObj.getStatusCode();
        String actualMsg = ResObj.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 500);
    }
    @Test(priority = 53,description = "Provide invalid entity type in validate OTP",enabled = false)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyEntityTypeValidateOTP() throws IOException, JSchException, InterruptedException {
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
       // OTP = getOTP(mobileNo);
        OTP = getOTPFromSellerPanel(mobileNo);

        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj,"", "p2p_100k", AgentToken, version, mobileNo, "merchant", newState,OTP);
        int StatusCode = ResObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 54,description = "Positive ValidateOTP",dependsOnMethods = "PositiveSendOtpSendOTP",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveValidateOtp() throws IOException, JSchException, InterruptedException {
        PositiveSendOtpSendOTP();
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
        //OTP = getOTP(mobileNo);
       // OTP="888888";
        OTP = getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj,"INDIVIDUAL", "p2p_100k", AgentToken, version, mobileNo, "merchant", newState,OTP);
        leadId = ResObj.jsonPath().getString("leadId");
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");


        for (int i = 0; i<=3;i++)
        {

            if (leadId == null || leadId.isEmpty() || StatusCode!=200)
            {
                LOGGER.info("Lead Id is null, Inside loop : " +i);
                PositiveSendOtpSendOTP();
                waitForLoad(5000);
                ValidateOtp ValidateOTPobjErr = new ValidateOtp(P.TESTDATA.get("validateOtp"));
                //OTP = getOTP(mobileNo);
                OTP = getOTPFromSellerPanel(mobileNo);

                LOGGER.info("This is OTP " + OTP);
                Response ResNewObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobjErr,"INDIVIDUAL", "p2p_100k", AgentToken, version, mobileNo, "merchant", newState,OTP);
                leadId = ResNewObj.jsonPath().getString("leadId");
                StatusCode = ResNewObj.getStatusCode();
                CustId = ResNewObj.jsonPath().getString("custId");
            }
            else
            {
                LOGGER.info("This is Lead ID : " + leadId);
                Assert.assertNotEquals(leadId,null);
                LOGGER.info("CustId " + CustId);
                Assert.assertNotEquals(CustId,null);
                Assert.assertEquals(StatusCode, 200);
                break;
            }
        }

    }
    //V3 Merchant Status API
    @Test(priority = 55)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffEntityTypesGetMerchStatus() throws IOException, JSchException //Where Entity type is different
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"PROPRIETORSHIP", "p2p_100k", AgentToken, version);
        String expectedMsg = "Data Not present for customer";
        int StatusCode = resObjGetMerchant.getStatusCode();
        String errorCode = "";
        errorCode = resObjGetMerchant.jsonPath().getString("errorCode");
        String actualMsg = resObjGetMerchant.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(errorCode,"404");
    }
    @Test(priority = 56)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidEntityTypesGetMerchStatus() throws IOException, JSchException //Where Entity type is invalid
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDI", "p2p_100k", AgentToken, version);
        String expectedMsg = "Failed to fetch merchant lead details";
        int StatusCode = resObjGetMerchant.getStatusCode();
        String actualMsg = resObjGetMerchant.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 500);
    }
    @Test(priority = 57)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyEntityTypesGetMerchStatus() throws IOException, JSchException //Where Entity type is empty
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDI", "p2p_100k", AgentToken, version);
        String expectedMsg = "Failed to fetch merchant lead details";
        int StatusCode = resObjGetMerchant.getStatusCode();
        String actualMsg = resObjGetMerchant.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 500);
    }
    @Test(priority = 58)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void custIdNotInOauthGetMerchStatus() throws IOException, JSchException //Where CustId is not registered in Oauth
    {
        GetMerchant callv3GetMerch = new GetMerchant(noOauthCustID);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "p2p_100k", AgentToken, version);
        String expectedMsg = "Data Not present for customer";
        int StatusCode = resObjGetMerchant.getStatusCode();
        String errorCode = "";
        errorCode = resObjGetMerchant.jsonPath().getString("errorCode");
        String actualMsg = resObjGetMerchant.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(errorCode,"404");
    }
    @Test(priority = 59)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void custIdLeadNotPresentGetMerchStatus() throws IOException, JSchException //Where 100K lead is not present for a CustId
    {
        GetMerchant callv3GetMerch = new GetMerchant(noLeadCustID);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "p2p_100k", AgentToken, version);
        String expectedMsg = "Data Not present for customer";
        int StatusCode = resObjGetMerchant.getStatusCode();
        String errorCode = resObjGetMerchant.jsonPath().getString("errorCode");
        String actualMsg = resObjGetMerchant.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(errorCode,"404");
    }
    @Test(priority = 60)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidCustIdGetMerchStatus() throws IOException, JSchException //Where CustId value is alphabetic
    {
        GetMerchant callv3GetMerch = new GetMerchant(invalidCustID);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "p2p_100k", AgentToken, version);
        String expectedMsg = "Failed to fetch merchant lead details";
        int StatusCode = resObjGetMerchant.getStatusCode();
        String actualMsg = resObjGetMerchant.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 500);
    }
    @Test(priority = 61,enabled = false)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyCustIdGetMerchStatus() throws IOException, JSchException //Where CustId value is empty
    {
        GetMerchant callv3GetMerch = new GetMerchant(emptyCustID);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "p2p_100k", AgentToken, version);
        int StatusCode = resObjGetMerchant.getStatusCode();
        LOGGER.info("This is Status Code" + resObjGetMerchant.getStatusCode());
        Assert.assertEquals(StatusCode, "404");
    }
    @Test(priority = 62)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidSolutionTypeGetMerchStatus() throws IOException, JSchException //Where Solution type value is invalid
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "p2p_100", AgentToken, version);
        String expectedMsg = "Failed to fetch merchant lead details";
        int StatusCode = resObjGetMerchant.getStatusCode();
        String actualMsg = resObjGetMerchant.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 500);
    }
    @Test(priority = 63)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffSolutionTypeGetMerchStatus() throws IOException, JSchException //Where Solution type value is different from Individual
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "company_onboard", AgentToken, version);
        String expectedMsg = "Data Not present for customer";
        int StatusCode = resObjGetMerchant.getStatusCode();
        String errorCode = "";
        errorCode = resObjGetMerchant.jsonPath().getString("errorCode");
        String actualMsg = resObjGetMerchant.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 200);
        Assert.assertEquals(errorCode,"404");
    }
    @Test(priority = 64)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptySolutionTypeGetMerchStatus() throws IOException, JSchException //Where Solution type value is empty
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "", AgentToken, version);
        String expectedMsg = "Failed to fetch merchant lead details";
        int StatusCode = resObjGetMerchant.getStatusCode();
        String actualMsg = resObjGetMerchant.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 500);
    }
    @Test(priority = 65,dependsOnMethods = "PositiveValidateOtp",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveGetMerchantStatus() throws IOException, JSchException
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "p2p_100k", AgentToken, version);
        int StatusCode = resObjGetMerchant.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        callv3GetMerch.validateResponseAgainstJSONSchema("MerchantService/V3/GetMerchant/GetMerchantResponseSchema.json");
    }
    // V1 Category
    @Test(priority = 66)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffEntityTypeGetCat()
    {
        Category getCat = new Category();
        Response ResObject = uadServicesObject.getCategory(getCat,"PROPRIETORSHIP", "100K", AgentToken, version);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "success";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 200);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
    }
    @Test(priority = 67)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidEntityTypeGetCat()
    {
        Category getCat = new Category();
        Response ResObject = uadServicesObject.getCategory(getCat,"INDI", "100K", AgentToken, version);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "No categories found for the given solution, entity pair";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 404);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
    }
    @Test(priority = 68)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyEntityTypeGetCat()
    {
        Category getCat = new Category();
        Response ResObject = uadServicesObject.getCategory(getCat,"", "100K", AgentToken, version);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "Required fields cannot be empty";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 400);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
    }
    @Test(priority = 69)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffSolutionTypeGetCat()
    {
        Category getCat = new Category();
        Response ResObject = uadServicesObject.getCategory(getCat,"INDIVIDUAL", "50k", AgentToken, version);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "success";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 200);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
    }
    @Test(priority = 70)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptySolutionTypeGetCat()
    {
        Category getCat = new Category();
        Response ResObject = uadServicesObject.getCategory(getCat,"INDIVIDUAL", "", AgentToken, version);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "Required fields cannot be empty";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 400);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
    }
    @Test(priority = 71)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidSolutionTypeGetCat()
    {
        Category getCat = new Category();
        Response ResObject = uadServicesObject.getCategory(getCat,"INDIVIDUAL", "100", AgentToken, version);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "No categories found for the given solution";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 404);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
    }
    @Test(priority = 72,dependsOnMethods = "PositiveGetMerchantStatus",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveGetCategoryGetCat()
    {
        Category getCat = new Category();
        Response ResObject = uadServicesObject.getCategory(getCat,"INDIVIDUAL", "100K", AgentToken, version);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "success";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 200);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
        getCat.validateResponseAgainstJSONSchema("UAD/V1/Category/CategoryResponseSchema.json");
    }
    // V1 Get Sub-Category
    @Test(priority = 73)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffEntityTypeGetSubCat()
    {
        SubCategory getSubCat = new SubCategory();
        Response ResObject = uadServicesObject.getSubCategory(getSubCat,"PROPRIETORSHIP", "100K", AgentToken, version, 1);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "success";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 200);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
    }
    @Test(priority = 74)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidEntityTypeGetSubCat()
    {
        SubCategory getSubCat = new SubCategory();
        Response ResObject = uadServicesObject.getSubCategory(getSubCat,"INDI", "100K", AgentToken, version, 1);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "No sub-categories found for the given category id";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 404);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
    }
    @Test(priority = 75)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyEntityTypeGetSubCat()
    {
        SubCategory getSubCat = new SubCategory();
        Response ResObject = uadServicesObject.getSubCategory(getSubCat,"", "100K", AgentToken, version, 1);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "Required fields cannot be empty";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 400);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
    }
    @Test(priority = 76)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffSolutionTypeGetSubCat()
    {
        SubCategory getSubCat = new SubCategory();
        Response ResObject = uadServicesObject.getSubCategory(getSubCat,"INDIVIDUAL", "50K", AgentToken, version, 1);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "success";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 200);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
    }
    @Test(priority = 77)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidSolutionTypeGetSubCat()
    {
        SubCategory getSubCat = new SubCategory();
        Response ResObject = uadServicesObject.getSubCategory(getSubCat,"INDIVIDUAL", "100", AgentToken, version, 1);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "No sub-categories found for the given category id";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 404);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
    }
    @Test(priority = 78)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptySolutionTypeGetSubCat()
    {
        SubCategory getSubCat = new SubCategory();
        Response ResObject = uadServicesObject.getSubCategory(getSubCat,"INDIVIDUAL", "", AgentToken, version, 1);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "Required fields cannot be empty";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 400);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
    }
    @Test(priority = 79)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidCategoryIdGetSubCat()
    {
        SubCategory getSubCat = new SubCategory();
        Response ResObject = uadServicesObject.getSubCategory(getSubCat,"INDIVIDUAL", "100K", AgentToken, version, 100);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "No sub-categories found for the given category id";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 404);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
    }
    @Test(priority = 80)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffCategoryIdGetSubCat()
    {
        SubCategory getSubCat = new SubCategory();
        Response ResObject = uadServicesObject.getSubCategory(getSubCat,"INDIVIDUAL", "100K", AgentToken, version, 2);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "success";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 200);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
    }
    @Test(priority = 81)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void nullCategoryIdGetSubCat()
    {
        SubCategory getSubCat = new SubCategory();
        Response ResObject = uadServicesObject.getSubCategory(getSubCat,"INDIVIDUAL", "100K", AgentToken, version, null);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "Required fields cannot be empty";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 400);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
    }
    @Test(priority = 82,dependsOnMethods = "PositiveGetCategoryGetCat",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveGetSubCategory()
    {
        SubCategory getSubCat = new SubCategory();
        Response ResObject = uadServicesObject.getSubCategory(getSubCat,"INDIVIDUAL", "100K", AgentToken, version, 1);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "success";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 200);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
        getSubCat.validateResponseAgainstJSONSchema("UAD/V1/SubCategory/SubCategoryResponseSchema.json");
    }
    // V3 DocStatus API
    @Test(priority = 83,dependsOnMethods = "PositiveGetMerchantStatus",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void positiveGetDocStatus() {
        String files = "Paytm+Accepted+Here+Sticker+2,Paytm+Accepted+Here+Sticker,Paytm+Accepted+Here+Sticker+1,QR+Sticker,QR+Sticker+1,QR+Sticker+2,Cancelled+Cheque+Photo,businessOwnerPhoto";
        //  v3GetDocStat.addParameter("fileNames", files);
        v3GetDocStat.addUrlParameter("fileNames",files);
        Response v3GetSatatusResp = middlewareServicesObject.v3getDocStatus(v3GetDocStat, CustId, "INDIVIDUAL", "p2p_100k", AgentToken, version);
        int StatusCode = v3GetSatatusResp.getStatusCode();
        v3GetDocStat.validateResponseAgainstJSONSchema("MerchantService/V3/GetDocStatus/GetDocStatusSchema.json");
    }
    //V1 PinCode
    @Test(priority = 84)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidPinCOdeGetPincode()
    {
        PinCode = "201";
        PinCode getPinCode = new PinCode(PinCode);
        Response getPinCodeResponse = middlewareServicesObject.v1PinCode(getPinCode,version,AgentToken);
        int StatusCode = getPinCodeResponse.getStatusCode();
        Assert.assertEquals(StatusCode,500);
        String expectedMsg = "Failed to fetch pincode details";
        String actualMsg = getPinCodeResponse.jsonPath().getJsonObject("").toString();
        //Assert.assertTrue(actualMsg.contains(expectedMsg));

    }
    @Test(priority = 85,enabled = false)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyPinCOdeGetPincode()
    {
        PinCode = "";
        PinCode getPinCode = new PinCode(PinCode);
        Response getPinCodeResponse = middlewareServicesObject.v1PinCode(getPinCode,version,AgentToken);
        int StatusCode = getPinCodeResponse.getStatusCode();
        Assert.assertEquals(StatusCode,404);
        /*String expectedMsg = "Failed to fetch pincode details";
        String actualMsg = getPinCodeResponse.jsonPath().getJsonObject("").toString();
        Assert.assertTrue(actualMsg.contains(expectedMsg));
    }
    @Test(priority = 86)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PinCodeNotExistGetPincode()
    {
        PinCode = "000990";
        PinCode getPinCode = new PinCode(PinCode);
        Response getPinCodeResponse = middlewareServicesObject.v1PinCode(getPinCode,version,AgentToken);
        int StatusCode = getPinCodeResponse.getStatusCode();
        Assert.assertEquals(StatusCode,500);
        String expectedMsg = "Failed to fetch pincode details";
        String actualMsg = getPinCodeResponse.jsonPath().getJsonObject("").toString();
    //    Assert.assertTrue(actualMsg.contains(expectedMsg));
    }
    @Test(priority = 87,groups = {"Regression"},dependsOnMethods = "positiveGetLanguage")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void positiveGetPincode()
    {
        PinCode = "201301";
        PinCode getPinCode = new PinCode(PinCode);
        Response getPinCodeResponse = middlewareServicesObject.v1PinCode(getPinCode,version,AgentToken);
        int StatusCode = getPinCodeResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        String expectedMsg1 = "city=Gautam Buddha Nagar";
        String expectedMsg2 = "state=Uttar Pradesh";
        String actualMsg = getPinCodeResponse.jsonPath().getJsonObject("").toString();
        Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertTrue(actualMsg.contains(expectedMsg2));
        getPinCode.validateResponseAgainstJSONSchema("MerchantService/V1/PinCode/PinCodeResponseSchema.json");
    }
    //V1 Get Language
    @Test(priority = 88,groups ={"Regression"},dependsOnMethods = "PositiveGetMerchantStatus")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void positiveGetLanguage()
    {
        Response v1GetLanguageRespose = middlewareServicesObject.v1LanguagePrefrence(getLanguage,version,AgentToken);
        int StatusCode = v1GetLanguageRespose.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        String expectedMsg = "English";
        String actualMsg = v1GetLanguageRespose.jsonPath().getJsonObject("dataValues").toString();
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        getLanguage.validateResponseAgainstJSONSchema("MerchantService/V1/Resources/LanguagePrefrence/LanguagePrefResponseSchema.json");
    }
    //V2 Get Banks
    @Test(priority = 89)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidBank()
    {
        IFSC = "INDB000058";
        Banks getBank = new Banks(IFSC);
        Response getBanksResponse = middlewareServicesObject.v2Banks(getBank,version,AgentToken);
        String expectedMsg = "Beneficiary Bank detail doesn't exist!!!";
        String actualMsg = getBanksResponse.jsonPath().getJsonObject("successMsg").toString();
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        int StatusCode = getBanksResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }
    @Test(priority = 90)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void IfscNotExistBank()
    {
        IFSC = "AASHIT01234";
        Banks getBank = new Banks(IFSC);
        Response getBanksResponse = middlewareServicesObject.v2Banks(getBank,version,AgentToken);
        String expectedMsg = "Beneficiary Bank detail doesn't exist!!!";
        String actualMsg = getBanksResponse.jsonPath().getJsonObject("successMsg").toString();
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        int StatusCode = getBanksResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }
    @Test(priority = 91)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyIfscBank()
    {
        // IFSC = "";
        Banks getBank = new Banks(emptyCustID);
        Response getBanksResponse = middlewareServicesObject.v2Banks(getBank,version,AgentToken);
        String expectedMsg = "ANDHRA BANK";
        String actualMsg = getBanksResponse.jsonPath().getJsonObject("bankDetails.list[6]").toString();
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        int StatusCode = getBanksResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }
    @Test(priority = 92,groups = {"Regression"},dependsOnMethods = "positiveGetLanguage",description = "Positive Get Bank Details")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void positiveBank()
    {
        IFSC = "INDB0000588";
        Banks getBank = new Banks(IFSC);
        Response getBanksResponse = middlewareServicesObject.v2Banks(getBank,version,AgentToken);
        String expectedMsg = "bankName=INDUSIND BANK";
        String actualMsg = getBanksResponse.jsonPath().getJsonObject("bankDetails").toString();
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        int StatusCode = getBanksResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        getBank.validateResponseAgainstJSONSchema("MerchantService/V2/Banks/BanksResponseSchema.json");
    }
    //V3 Penny Drop
    @Test(priority = 93,description = "Invalid Bank A/c in penny drop")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidBankPennyDrop()
    {
        PennyDrop doPennyDrop = new PennyDrop();
        doPennyDrop.getProperties().setProperty("mobile",mobileNo);
        doPennyDrop.getProperties().setProperty("ifsc","ANDB0001358");
        doPennyDrop.getProperties().setProperty("bankAccountNumber","**********");
        Response getBanksResponse = middlewareServicesObject.v3PennyDrop(doPennyDrop,version,"AASHIT SHARMA","INDUSIND BANK",CustId,AgentToken);
        String expectedMsg = "Verification failed due to invalid bank account number. Please try again";
        String actualMsg = getBanksResponse.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        int StatusCode = getBanksResponse.getStatusCode();
        Assert.assertEquals(StatusCode,500);
    }
    @Test(priority = 94,description = "Empty Bank A/c in penny drop")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void emptyBankPennyDrop()
    {
        PennyDrop doPennyDrop = new PennyDrop();
        doPennyDrop.getProperties().setProperty("mobile",mobileNo);
        doPennyDrop.getProperties().setProperty("ifsc","ANDB0002029");
        doPennyDrop.getProperties().setProperty("bankAccountNumber","");
        Response getBanksResponse = middlewareServicesObject.v3PennyDrop(doPennyDrop,version,"AASHIT SHARMA","INDUSIND BANK",CustId,AgentToken);
        String expectedMsg = "Bank detail already exists";
        String actualMsg = getBanksResponse.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        int StatusCode = getBanksResponse.getStatusCode();
        Assert.assertEquals(StatusCode,400);
    }
    @Test(priority = 95,description = "Bank A/C no and IFSC are of different bank")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void diffBankPennyDrop()
    {
        PennyDrop doPennyDrop = new PennyDrop();
        doPennyDrop.getProperties().setProperty("mobile",mobileNo);
        doPennyDrop.getProperties().setProperty("ifsc","ANDB0002028");
        doPennyDrop.getProperties().setProperty("bankAccountNumber","************");
        Response getBanksResponse = middlewareServicesObject.v3PennyDrop(doPennyDrop,version,"AASHIT SHARMA","INDUSIND BANK",CustId,AgentToken);
        String expectedMsg = "Verification failed due to invalid bank account number. Please try again";
        String actualMsg = getBanksResponse.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        int StatusCode = getBanksResponse.getStatusCode();
        Assert.assertEquals(StatusCode,500);
    }
    @Test(priority = 96,description = "Bank a/c no is not send in request")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void removeBankPennyDrop()
    {
        PennyDrop doPennyDrop = new PennyDrop();
        doPennyDrop.getProperties().setProperty("mobile",mobileNo);
        doPennyDrop.getProperties().setProperty("ifsc","ANDB0002029");
        Response getBanksResponse = middlewareServicesObject.v3PennyDrop(doPennyDrop,version,"AASHIT SHARMA","INDUSIND BANK",CustId,AgentToken);
        String expectedMsg = "Bank detail already exists";
        String actualMsg = getBanksResponse.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        int StatusCode = getBanksResponse.getStatusCode();
        Assert.assertEquals(StatusCode,400);
    }
    @Test(priority = 97,description = "Providing Invalid IFSC in request")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void invalidIfscPennyDrop()
    {
        PennyDrop doPennyDrop = new PennyDrop();
        doPennyDrop.getProperties().setProperty("mobile",mobileNo);
        doPennyDrop.getProperties().setProperty("ifsc","ANDB00020");
        doPennyDrop.getProperties().setProperty("bankAccountNumber","***************");
        Response getBanksResponse = middlewareServicesObject.v3PennyDrop(doPennyDrop,version,"AASHIT SHARMA","INDUSIND BANK",CustId,AgentToken);
        String expectedMsg = "Please enter your Bank's IFSC code to continue.";
        String actualMsg = getBanksResponse.jsonPath().getString("message");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        int StatusCode = getBanksResponse.getStatusCode();
        Assert.assertEquals(StatusCode,500);
    }
    @Test(priority = 98,groups = {"Regression"},dependsOnMethods = "PositiveValidateOtp",description = "Positive Penny Drop")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void positivePennyDrop()
    {
        PennyDrop doPennyDrop = new PennyDrop();
        doPennyDrop.getProperties().setProperty("mobile",mobileNo);
        doPennyDrop.getProperties().setProperty("ifsc","ANDB0002041");
        doPennyDrop.getProperties().setProperty("bankAccountNumber","***************");
        Response getBanksResponse = middlewareServicesObject.v3PennyDrop(doPennyDrop,version,"AASHIT SHARMA","INDUSIND BANK",CustId,AgentToken);
        nameMatchStatus = getBanksResponse.jsonPath().getString("nameMatchStatus");
        LOGGER.info("Name Match Status = " +nameMatchStatus);
        int StatusCode = getBanksResponse.getStatusCode();
        if(StatusCode == 200)
        {
            Assert.assertEquals(StatusCode,200);
        }

        if (StatusCode == 500)
        {
            String expectedFailureMsg = "We are unable to connect with your bank to verify your details. Please try after some time or provide a different bank account";
            String actualFailureMsg = getBanksResponse.jsonPath().getString("message");
            Assert.assertTrue(actualFailureMsg.contains(expectedFailureMsg));

            Assert.assertEquals(StatusCode,500);
        }
        doPennyDrop.validateResponseAgainstJSONSchema("MerchantService/V3/PennyDrop/PennyDropResponseSchema.json");
    }
    //Submit Merchant API

    @Test(priority = 99,description = "Positive Submit Merchant",groups = {"Regression"},dependsOnMethods = "positiveGetDocStatus")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void positiveSubmitMerchant()
    {
        SubmitMerchant postMerchant = new SubmitMerchant(CustId,P.TESTDATA.get("SubmitMerchant100K"));
        postMerchant.getProperties().setProperty("line1",lineOne);
        postMerchant.getProperties().setProperty("line2",lineTwo);
        postMerchant.getProperties().setProperty("line3",lineThree);
        Response submitMerchResponse = middlewareServicesObject.v3SubmitMerchant(postMerchant,mobileNo,mobileNo,"ANDB0002029",version,"INDIVIDUAL","p2p_100k",AgentToken);
        int StatusCode = submitMerchResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        postMerchant.validateResponseAgainstJSONSchema("MerchantService/V3/SubmitMerchant/SubmitMerchantResponseSchema.json");
    }

    @Test(priority = 100,description = "Positive Fetch Documents for P2P 100K",dependsOnMethods ="positiveSubmitMerchant",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveFetchDocs100K() throws JsonProcessingException
    {

        Map<String,String>queryDocUpload = new HashMap<>();
        queryDocUpload.put("type", "jpg");
        queryDocUpload.put("entityType", "INDIVIDUAL");
        queryDocUpload.put("solutionType", "p2p_100k");
        queryDocUpload.put("merchantCustId", CustId);
        queryDocUpload.put("leadId", leadId);
        queryDocUpload.put("solutionTypeLevel2","nonMinKyc");


        Map<String,String>queryFetchDoc = new HashMap<>();
        queryFetchDoc.put("entityType", "INDIVIDUAL");
        queryFetchDoc.put("solution", "p2p_100k");
        queryFetchDoc.put("leadId", leadId);
        queryFetchDoc.put("channel", "GG_APP");
        queryFetchDoc.put("merchantCustId", CustId);
        queryFetchDoc.put("category","Entertainment");
        queryFetchDoc.put("subCategory","Game Parlour");
        queryFetchDoc.put("solutionSubType","nonMinKyc");

        GgAppDynamicDocUpload(AgentToken,version,mobileNo,queryFetchDoc,queryDocUpload);

    }



    @Test(priority = 101,description = "Create User at Wallet",groups = {"Regression"},dependsOnMethods = "PositiveFetchDocs100K",timeOut = 5000)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveCreateUserWallet100K()
    {
        CreateUserWallet walletObj = new CreateUserWallet();

        walletObj.getProperties().setProperty("ssoId",CustId);
        walletObj.getProperties().setProperty("mobileNumber",mobileNo);

        Response walletResp = walletServices.createUserWallet(walletObj);

        String msg = walletResp.jsonPath().getString("statusMessage");
        Assert.assertTrue(msg.contains("Wallet Activated"));

        String statusMsg = walletResp.jsonPath().getString("statusCode");
        Assert.assertTrue(statusMsg.contains("SUCCESS"));

    }



    @Test(priority = 102,description = "Get Cookie for OE Panel",groups = {"Regression"},dependsOnMethods = "PositiveFetchDocs100K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveGetOEPanelCookie()
    {
        XMWToken=XMWCookie;
        LOGGER.info("XMW token is :"+XMWToken);
    }


    @Test(priority = 103,description = "Fetch Lead Details on Panel",groups = {"Regression"},dependsOnMethods = "PositiveGetOEPanelCookie")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveFetchLeadPanel() throws JsonProcessingException
    {
        Map <String,String> RequestPanel = new HashMap<>();
        Map <String,String> ResponsePanel = new HashMap<>();

        RequestPanel.put("docStatus",OePanelDocStatus);
        RequestPanel.put("rejectionReason",RejectionReason);
        RequestPanel.put("leadId",leadId);


        ResponsePanel = FetchPanelLead(RequestPanel);

        DocumetRequestDeserialised = ResponsePanel.get("DocumenstArray");
        WorkFlowId = ResponsePanel.get("WorkFlowId");
        LOGGER. info("Lead Stage is : " + ResponsePanel.get("LeadStage"));

    }


    @Test(priority = 104,description = "Reallocating Agent to Rajan",groups = {"Regression"},dependsOnMethods = "PositiveFetchLeadPanel")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void ReallocatingAgent100K()
    {
        LOGGER.info("Inside ReallocatingAgent Method ");
        ReallocatingAgent(leadId,"1152");

    }

    @Test(priority = 105,description = "Submit rejected lead from OE panel ",groups = {"Regression"},dependsOnMethods = "ReallocatingAgent100K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveRejectedLeadPanel()
    {
        EditLead v1EditLeadObj=new EditLead(leadId, P.TESTDATA.get("EditLead100KRejected"));

        v1EditLeadObj.getProperties().setProperty("documents",DocumetRequestDeserialised);
        // v1EditLeadObj.getProperties().setProperty("reEnterAccountNumber",mobileNo);
        v1EditLeadObj.getProperties().setProperty("workflowStatusId",WorkFlowId);

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "**********", "**********", XMWToken,"application/json");

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode,200);

        String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
        Assert.assertTrue(LeadSatge.contains("DATA_ENTRY_REJECTED"));
    }

    @Test(priority = 106,description = "Positive fetch merchant status after rejection",dependsOnMethods = "PositiveRejectedLeadPanel",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveGetMerchantStatusAfterRejection() throws IOException, JSchException
    {
        LOGGER.info("Inside PositiveGetMerchantStatusAfterRejection Method");
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "p2p_100k", AgentToken, version);

        addressUuid = resObjGetMerchant.jsonPath().getJsonObject("merchantDetails.suggestedRelatedBusinesses[0].address.addressUuid").toString();
        rrbUuid =  resObjGetMerchant.jsonPath().getJsonObject("merchantDetails.relatedBusinessUuid").toString();
        ownerAddressUUID =  resObjGetMerchant.jsonPath().getJsonObject("merchantDetails.suggestedOwnerAddress[0].addressUuid").toString();

        LOGGER.info("Address UUID : " + addressUuid + " RRB UUID : " + rrbUuid + " Owner Address UUID : " + ownerAddressUUID);

        int StatusCode = resObjGetMerchant.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 107,description = "Positive Submit Merchant After Rejection",groups = {"Regression"},dependsOnMethods = "PositiveGetMerchantStatusAfterRejection")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void positiveSubmitMerchantAfterRejection() throws JsonProcessingException {
        SubmitMerchant postMerchant = new SubmitMerchant(CustId,P.TESTDATA.get("Submit100KAfterRejection"));

        postMerchant.getProperties().setProperty("addressUuid",addressUuid);
        postMerchant.getProperties().setProperty("relatedBusinessUuid",rrbUuid);
        postMerchant.getProperties().setProperty("ownerAddressUUID",ownerAddressUUID);
        postMerchant.getProperties().setProperty("line1",lineOne);
        postMerchant.getProperties().setProperty("line2",lineTwo);
        postMerchant.getProperties().setProperty("line3",lineThree);
        Response submitMerchResponse = middlewareServicesObject.v3SubmitMerchant(postMerchant,mobileNo,mobileNo,"ANDB0002029",version,"INDIVIDUAL","p2p_100k",AgentToken);
        int StatusCode = submitMerchResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        postMerchant.validateResponseAgainstJSONSchema("MerchantService/V3/SubmitMerchant/SubmitMerchantResponseSchema.json");

        if(StatusCode == 200)
        {
            PositiveFetchDocs100K();
        }
    }

    @Test(priority = 108,description = "Fetch Lead Details on Panel",groups = {"Regression"},dependsOnMethods = "positiveSubmitMerchantAfterRejection")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveSubmitLeadPanel() throws JsonProcessingException {
        OePanelDocStatus = "APPROVED";
        RejectionReason = null;
        PositiveFetchLeadPanel();

        ReallocatingAgent100K();

        EditLead v1EditLeadObj = new EditLead(leadId, P.TESTDATA.get("EditLead100K"));

        v1EditLeadObj.getProperties().setProperty("documents", DocumetRequestDeserialised);
        v1EditLeadObj.getProperties().setProperty("reEnterAccountNumber", mobileNo);
        v1EditLeadObj.getProperties().setProperty("workflowStatusId", WorkFlowId);

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "**********", "**********", XMWToken, "application/json");

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
        Assert.assertTrue(LeadSatge.contains("PANEL_SUCCESS"));

        //v1EditLeadObj.validateResponseAgainstJSONSchema("MerchantServiceOEPanelV1EditLead/EditLead100KSchema.json");




    }

    @Test(enabled = true,priority = 108,description = "Fetch Lead Details on Panel",groups = {"Regression"},dependsOnMethods = "PositiveSubmitLeadPanel",timeOut = 60000)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PgCallBack100K() throws SQLException, JsonProcessingException {
        DbName = DbStaging6;
        MID = PG_CallBack_Insatnt50K(CustId);
        PgCallBackFromPanelRef(leadId,CustId,MID);
    }

    @AfterClass(description = "Adding Details on CSV")
    public void AddingDataCsv() throws IOException {
        LOGGER.info("In After Test of " +getClass());

        File fileUpload = new File("OnboardedMerchant.csv") ;
        // Create csv file
        FileWriter outputfile = new FileWriter(fileUpload,true);

        // Write to CSV file which is open
        CSVWriter writer = new CSVWriter(outputfile);

        if(!MID.isEmpty()) {
            LOGGER.info("MID Is not Empty");
            // add data to csv
            String[] data1 = {MID, CustId, mobileNo, "p2p_100k", ""};
            writer.writeNext(data1);
            writer.flush();
            writer.close();
        }
        else
        {
            LOGGER.info("MID is Empty");
        }
    }

}


*/


