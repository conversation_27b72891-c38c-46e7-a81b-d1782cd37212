package OCL.Individual.Merchant100K;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.v1.Cart.PinCode;
import Request.MerchantService.v1.Resources.LanguagePreference;
import Request.MerchantService.v2.Banks;
import Request.MerchantService.v2.TnC;
import Request.MerchantService.v3.*;
import Request.UAD.Category;
import Request.UAD.SubCategory;
import Request.Wallet.CreateUserWallet;
import Services.MechantService.MiddlewareServices;
import Services.UAD.UADServices;
import Services.Utilities.Utilities;
import Services.Wallet.WalletServices;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSchException;
import com.opencsv.CSVWriter;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/*
public  class Flow100KNameMatchSuccess extends BaseMethod{

    private static final Logger LOGGER = LogManager.getLogger(Flow100KNameMatchSuccess.class);

    public static String AgentToken = "";
    public static  String AgentNo = "7011653794";
    public static  String version = "4.8.3";
    public static String mobileNo = "";
    public static String newState = "";
    public static String OTP = "";
    public static String nameMatchStatus = "";
    public static String CustId = "";
    public static String XMWToken = "";
    public static String leadId = "";
    public static String MID = "";

    public static String WorkFlowId = "";
    public static String addressUuid = "";
    public static String rrbUuid = "";
    public static String ownerAddressUUID = "";

    Faker GenerateFake = new Faker();

    public String lineOne = GenerateFake.address().streetAddress();
    public String lineTwo = GenerateFake.address().cityName();
    public String lineThree = GenerateFake.address().streetName();

    public static List<Object> DocType = new ArrayList<>();
    public static List<Object> DocProvided = new ArrayList<>();
    public static String OePanelDocStatus = "REJECTED";
    public static String RejectionReason = "Wrong Photo";
    public static String DocumetRequestDeserialised = "";
    List <Object> GetDocuments = new ArrayList<>();
    public String PanelLeadStage = "";

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    UADServices uadServicesObject = new UADServices();
    WalletServices walletServices = new WalletServices();

    TnC GetTnC = new TnC();
    GetDocStatus v3GetDocStat = new GetDocStatus();
    LanguagePreference getLanguage = new LanguagePreference();


    @BeforeClass
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void AgentLogin100k()
    {

        //AgentToken = AgentSessionToken("7771110007","paytm@123");
        AgentToken = CommonAgentToken;

        Response responseObject=middlewareServicesObject.v1Token("**********","paytm@123");
        XMWCookie=responseObject.getHeader("Set-Cookie").toString();
        LOGGER.info(" OE Panel Cookie is  : " + XMWCookie);

        LOGGER.info("Agent Token is : " + AgentToken);
        //generating new Number
        Utilities accObj = new Utilities();
        mobileNo = accObj.randomMobileNumberGenerator();
        LOGGER.info("New Number is for 100K for Name Match True: " + mobileNo);
        CreateApplicantOauth(mobileNo);
        XMWToken=XMWCookie;
        LOGGER.info("XMW token is :"+XMWToken);
        Reporter.setEscapeHtml(false);
    }


    @Test(description = "Positive SendOTP",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_PositiveSendOtpSendOTP()
    {

        SendOtp SendOTPobj = new SendOtp();
        System.out.println(OTP);

        String expectedErrorMsg = "Otp sent to phone";
        LOGGER.info("This is Token " + AgentToken);
        Response ResObject = middlewareServicesObject.v3SentOtp(SendOTPobj,"INDIVIDUAL","p2p_100k",AgentToken,version,mobileNo,"merchant");
        SendOTPobj.setHttpStatus(ResObject.jsonPath().getString("httpStatus"));
        SendOTPobj.setMessage(ResObject.jsonPath().getString("message"));
        SendOTPobj.setState(ResObject.jsonPath().getString("state"));
        SendOTPobj.setStatus(ResObject.jsonPath().getString("status"));
        LOGGER.info("state" + " " + ResObject.jsonPath().getString("state"));
        newState = ResObject.jsonPath().getString("state");
        LOGGER.info("New State " + newState);
        String actualErrorMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        Assert.assertEquals(StatusCode,200);
        SendOTPobj.validateResponseAgainstJSONSchema("MerchantService/V3/SendOtp/SendOtpResponseSchema.json");
        //How to validate Schema ??
    }

    @Test(description = "Positive Get TnC",dependsOnMethods = "TC001_PositiveSendOtpSendOTP",groups ={"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_positiveGetTnc()
    {
        GetTnC.addParameter("tncSet","merchant");
        Response TnCResponse = middlewareServicesObject.v2GetTnC(GetTnC,"INDIVIDUAL","p2p_100k",version,AgentToken);
        String expectedMsg = "SUCCESS";
        String actualMsg = TnCResponse.jsonPath().getString("status");
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        int statusCode = TnCResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        GetTnC.validateResponseAgainstJSONSchema("MerchantService/V2/TnC/TnCResponseSchema.json");
    }
    //V3 Validate OTP API

    @Test(description = "Positive ValidateOTP",dependsOnMethods = "TC001_PositiveSendOtpSendOTP",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_PositiveValidateOtp() throws IOException, JSchException, InterruptedException {
        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
        OTP = getOTPFromSellerPanel(mobileNo);
       // OTP="888888";
        LOGGER.info("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj,"INDIVIDUAL", "p2p_100k", AgentToken, version, mobileNo, "merchant", newState,OTP);
        leadId = ResObj.jsonPath().getString("leadId");
        int StatusCode = ResObj.getStatusCode();
        CustId = ResObj.jsonPath().getString("custId");


        for (int i = 0; i<=3;i++)
        {

            if (leadId == null || leadId.isEmpty() || StatusCode!=200)
            {
                LOGGER.info("Lead Id is null, Inside loop : " +i);
                TC001_PositiveSendOtpSendOTP();
                waitForLoad(5000);
                ValidateOtp ValidateOTPobjErr = new ValidateOtp(P.TESTDATA.get("validateOtp"));
                OTP = getOTPFromSellerPanel(mobileNo);
                LOGGER.info("This is OTP " + OTP);
                Response ResNewObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobjErr,"INDIVIDUAL", "p2p_100k", AgentToken, version, mobileNo, "merchant", newState,OTP);
                leadId = ResNewObj.jsonPath().getString("leadId");
                StatusCode = ResNewObj.getStatusCode();
                CustId = ResNewObj.jsonPath().getString("custId");
            }
            else
            {
                Assert.assertEquals(StatusCode, 200);
                break;
            }
        }
        LOGGER.info("This is Lead ID : " + leadId);
        Assert.assertNotEquals(leadId,null);
        LOGGER.info("CustId " + CustId);
        Assert.assertNotEquals(CustId,null);

    }
    //V3 Merchant Status API

    @Test(dependsOnMethods = "TC003_PositiveValidateOtp",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_PositiveGetMerchantStatus() throws IOException, JSchException
    {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "p2p_100k", AgentToken, version);
        int StatusCode = resObjGetMerchant.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        callv3GetMerch.validateResponseAgainstJSONSchema("MerchantService/V3/GetMerchant/GetMerchantResponseSchema.json");
    }
    // V1 Category

    @Test(dependsOnMethods = "TC004_PositiveGetMerchantStatus",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_PositiveGetCategoryGetCat()
    {
        Category getCat = new Category();
        Response ResObject = uadServicesObject.getCategory(getCat,"INDIVIDUAL", "100K", AgentToken, version);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "success";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 200);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
        getCat.validateResponseAgainstJSONSchema("UAD/V1/Category/CategoryResponseSchema.json");
    }
    // V1 Get Sub-Category

    @Test(dependsOnMethods = "TC004_PositiveGetMerchantStatus",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC006_PositiveGetSubCategory()
    {
        SubCategory getSubCat = new SubCategory();
        Response ResObject = uadServicesObject.getSubCategory(getSubCat,"INDIVIDUAL", "100K", AgentToken, version, 1);
        int StatusCode = ResObject.getStatusCode();
        String expectedMsg = "success";
        String exactMsg = ResObject.jsonPath().getString("message");
        Assert.assertEquals(StatusCode, 200);
        Assert.assertTrue(exactMsg.contains(expectedMsg));
        getSubCat.validateResponseAgainstJSONSchema("UAD/V1/SubCategory/SubCategoryResponseSchema.json");
    }

    //V1 PinCode

    @Test(groups = {"Regression"},dependsOnMethods = "TC004_PositiveGetMerchantStatus")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_positiveGetPincode()
    {
        PinCode = "201301";
        PinCode getPinCode = new PinCode(PinCode);
        Response getPinCodeResponse = middlewareServicesObject.v1PinCode(getPinCode,version,AgentToken);
        int StatusCode = getPinCodeResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        String expectedMsg1 = "city=Gautam Buddha Nagar";
        String expectedMsg2 = "state=Uttar Pradesh";
        String actualMsg = getPinCodeResponse.jsonPath().getJsonObject("").toString();
        Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertTrue(actualMsg.contains(expectedMsg2));
        getPinCode.validateResponseAgainstJSONSchema("MerchantService/V1/PinCode/PinCodeResponseSchema.json");
    }
    //V1 Get Language
    @Test(groups ={"Regression"},dependsOnMethods = "TC004_PositiveGetMerchantStatus")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_positiveGetLanguage()
    {
        Response v1GetLanguageRespose = middlewareServicesObject.v1LanguagePrefrence(getLanguage,version,AgentToken);
        int StatusCode = v1GetLanguageRespose.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        String expectedMsg = "English";
        String actualMsg = v1GetLanguageRespose.jsonPath().getJsonObject("dataValues").toString();
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        getLanguage.validateResponseAgainstJSONSchema("MerchantService/V1/Resources/LanguagePrefrence/LanguagePrefResponseSchema.json");
    }
    //V2 Get Banks

    @Test(groups = {"Regression"},dependsOnMethods = "TC004_PositiveGetMerchantStatus",description = "Positive Get Bank Details")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC009_positiveBank()
    {
        IFSC = "INDB0000588";
        Banks getBank = new Banks(IFSC);
        Response getBanksResponse = middlewareServicesObject.v2Banks(getBank,version,AgentToken);
        String expectedMsg = "bankName=INDUSIND BANK";
        String actualMsg = getBanksResponse.jsonPath().getJsonObject("bankDetails").toString();
        Assert.assertTrue(actualMsg.contains(expectedMsg));
        int StatusCode = getBanksResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        getBank.validateResponseAgainstJSONSchema("MerchantService/V2/Banks/BanksResponseSchema.json");
    }
    //V3 Penny Drop

    @Test(groups = {"Regression"},dependsOnMethods = "TC004_PositiveGetMerchantStatus",description = "Positive Penny Drop")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC010_positivePennyDrop()
    {
        PennyDrop doPennyDrop = new PennyDrop();
        doPennyDrop.getProperties().setProperty("mobile",mobileNo);
        doPennyDrop.getProperties().setProperty("ifsc","ANDB0002041");
        doPennyDrop.getProperties().setProperty("bankAccountNumber","***************");
        Response getBanksResponse = middlewareServicesObject.v3PennyDrop(doPennyDrop,version,"AASHIT SHARMA","INDUSIND BANK",CustId,AgentToken);
        nameMatchStatus = getBanksResponse.jsonPath().getString("nameMatchStatus");
        LOGGER.info("Name Match Status = " +nameMatchStatus);
        int StatusCode = getBanksResponse.getStatusCode();
        if(StatusCode == 200)
        {
            Assert.assertEquals(StatusCode,200);
        }

        if (StatusCode == 500)
        {
            String expectedFailureMsg = "We are unable to connect with your bank to verify your details. Please try after some time or provide a different bank account";
            String actualFailureMsg = getBanksResponse.jsonPath().getString("message");
            Assert.assertTrue(actualFailureMsg.contains(expectedFailureMsg));

            Assert.assertEquals(StatusCode,500);
        }
        doPennyDrop.validateResponseAgainstJSONSchema("MerchantService/V3/PennyDrop/PennyDropResponseSchema.json");
    }
    //Submit Merchant API

    @Test(description = "Positive Submit Merchant",groups = {"Regression"},dependsOnMethods = "TC004_PositiveGetMerchantStatus")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC011_positiveSubmitMerchant()
    {
        SubmitMerchant postMerchant = new SubmitMerchant(CustId,P.TESTDATA.get("SubmitMerchant100KNameMatchSuccess"));

        postMerchant.getProperties().setProperty("line1",lineOne);
        postMerchant.getProperties().setProperty("line2",lineTwo);
        postMerchant.getProperties().setProperty("line3",lineThree);
        Response submitMerchResponse = middlewareServicesObject.v3SubmitMerchant(postMerchant,mobileNo,mobileNo,"ANDB0002029",version,"INDIVIDUAL","p2p_100k",AgentToken);
        int StatusCode = submitMerchResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        postMerchant.validateResponseAgainstJSONSchema("MerchantService/V3/SubmitMerchant/SubmitMerchantResponseSchema.json");
    }

    @Test(description = "Positive Fetch Documents for P2P 100K",dependsOnMethods ="TC011_positiveSubmitMerchant",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC012_PositiveFetchDocs100K() throws JsonProcessingException
    {

        Map<String,String>queryDocUpload = new HashMap<>();
        queryDocUpload.put("type", "jpg");
        queryDocUpload.put("entityType", "INDIVIDUAL");
        queryDocUpload.put("solutionType", "p2p_100k");
        queryDocUpload.put("merchantCustId", CustId);
        queryDocUpload.put("leadId", leadId);
        queryDocUpload.put("solutionTypeLevel2","nonMinKyc");

        Map<String,String>queryFetchDoc = new HashMap<>();
        queryFetchDoc.put("entityType", "INDIVIDUAL");
        queryFetchDoc.put("solution", "p2p_100k");
        queryFetchDoc.put("leadId", leadId);
        queryFetchDoc.put("channel", "GG_APP");
        queryFetchDoc.put("merchantCustId", CustId);
        queryFetchDoc.put("category","Entertainment");
        queryFetchDoc.put("subCategory","Game Parlour");
        queryFetchDoc.put("solutionSubType","nonMinKyc");

        GgAppDynamicDocUpload(AgentToken,version,mobileNo,queryFetchDoc,queryDocUpload);

    }

    @Test(description = "CallBack for Instant 50K",groups = {"Regression"},dependsOnMethods = "TC012_PositiveFetchDocs100K",timeOut = 140000)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC013_PGCallBackforInsatnt50K() throws SQLException, JsonProcessingException
    {
        DbName = DbStaging6;
        MID = PG_CallBack_Insatnt50K(CustId);
    }



    @Test(description = "Create User at Wallet",groups = {"Regression"},dependsOnMethods = "TC012_PositiveFetchDocs100K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC014_PositiveCreateUserWallet100K()
    {
        CreateUserWallet walletObj = new CreateUserWallet();

        walletObj.getProperties().setProperty("ssoId",CustId);
        walletObj.getProperties().setProperty("mobileNumber",mobileNo);

        Response walletResp = walletServices.createUserWallet(walletObj);

        String msg = walletResp.jsonPath().getString("statusMessage");
        Assert.assertTrue(msg.contains("Wallet Activated"));

        String statusMsg = walletResp.jsonPath().getString("statusCode");
        Assert.assertTrue(statusMsg.contains("SUCCESS"));

    }


    @Test(description = "Fetch Lead Details on Panel",groups = {"Regression"},dependsOnMethods = "TC012_PositiveFetchDocs100K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC015_PositiveFetchLeadPanel() throws JsonProcessingException
    {
        Map <String,String> RequestPanel = new HashMap<>();
        Map <String,String> ResponsePanel = new HashMap<>();

        RequestPanel.put("docStatus",OePanelDocStatus);
        RequestPanel.put("rejectionReason",RejectionReason);
        RequestPanel.put("leadId",leadId);


        ResponsePanel = FetchPanelLead(RequestPanel);

        DocumetRequestDeserialised = ResponsePanel.get("DocumenstArray");
        WorkFlowId = ResponsePanel.get("WorkFlowId");
        PanelLeadStage = ResponsePanel.get("LeadStage");
        LOGGER. info("Lead Stage is : " + PanelLeadStage);

    }



    @Test(description = "Submit rejected lead from OE panel ",groups = {"Regression"},dependsOnMethods = "TC015_PositiveFetchLeadPanel")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC016_PositiveRejectedLeadPanel()
    {
        for (int i = 0; i <3;i++)
        {
            if (!PanelLeadStage.equals("DATA_ENTRY_ACTION_PENDING"))
            {
                FetchLead v1FetchLeadObj = new FetchLead(leadId);
                waitForLoad(5000);
                Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
                PanelLeadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
                WorkFlowId = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.workflowStatusId").toString();
                LOGGER. info("Lead Stage is : " + PanelLeadStage);
            }
            else
                {
                ReallocatingAgent(leadId,"1152");
            }

        }
        Assert.assertEquals(PanelLeadStage,"DATA_ENTRY_ACTION_PENDING");
        EditLead v1EditLeadObj=new EditLead(leadId, P.TESTDATA.get("EditLead100KRejected"));

        v1EditLeadObj.getProperties().setProperty("documents",DocumetRequestDeserialised);
        // v1EditLeadObj.getProperties().setProperty("reEnterAccountNumber",mobileNo);
        v1EditLeadObj.getProperties().setProperty("workflowStatusId",WorkFlowId);

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "**********", "**********", XMWToken,"application/json");

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode,200);

        String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
        Assert.assertTrue(LeadSatge.contains("DATA_ENTRY_REJECTED"));
    }

    @Test(description = "Positive fetch merchant status after rejection",dependsOnMethods = "TC016_PositiveRejectedLeadPanel",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC017_PositiveGetMerchantStatusAfterRejection() throws IOException, JSchException
    {
        LOGGER.info("Inside PositiveGetMerchantStatusAfterRejection Method");
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,"INDIVIDUAL", "p2p_100k", AgentToken, version);

        addressUuid = resObjGetMerchant.jsonPath().getJsonObject("merchantDetails.suggestedRelatedBusinesses[0].address.addressUuid").toString();
        rrbUuid =  resObjGetMerchant.jsonPath().getJsonObject("merchantDetails.relatedBusinessUuid").toString();
        ownerAddressUUID =  resObjGetMerchant.jsonPath().getJsonObject("merchantDetails.suggestedOwnerAddress[0].addressUuid").toString();

        LOGGER.info("Address UUID : " + addressUuid + " RRB UUID : " + rrbUuid + " Owner Address UUID : " + ownerAddressUUID);

        int StatusCode = resObjGetMerchant.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(description = "Positive Submit Merchant After Rejection",groups = {"Regression"},dependsOnMethods = "TC017_PositiveGetMerchantStatusAfterRejection")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC018_positiveSubmitMerchantAfterRejection() throws JsonProcessingException {
        SubmitMerchant postMerchant = new SubmitMerchant(CustId,P.TESTDATA.get("SubmitMerchant100KNameMatchSuccessReject"));

        postMerchant.getProperties().setProperty("addressUuid",addressUuid);
        postMerchant.getProperties().setProperty("relatedBusinessUuid",rrbUuid);
        postMerchant.getProperties().setProperty("ownerAddressUUID",ownerAddressUUID);

        postMerchant.getProperties().setProperty("line1",lineOne);
        postMerchant.getProperties().setProperty("line2",lineTwo);
        postMerchant.getProperties().setProperty("line3",lineThree);

        Response submitMerchResponse = middlewareServicesObject.v3SubmitMerchant(postMerchant,mobileNo,mobileNo,"ANDB0002029",version,"INDIVIDUAL","p2p_100k",AgentToken);
        int StatusCode = submitMerchResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        postMerchant.validateResponseAgainstJSONSchema("MerchantService/V3/SubmitMerchant/SubmitMerchantResponseSchema.json");

        if(StatusCode == 200)
        {
            TC012_PositiveFetchDocs100K();
        }
    }



    @Test(description = "Fetch Lead Details on Panel",groups = {"Regression"},dependsOnMethods = "TC018_positiveSubmitMerchantAfterRejection")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC019_PositiveSubmitLeadPanel() throws JsonProcessingException {
        OePanelDocStatus = "APPROVED";
        RejectionReason = null;
        TC015_PositiveFetchLeadPanel();

        for (int i = 0; i <3;i++)
        {
            if (!PanelLeadStage.equals("DATA_ENTRY_ACTION_PENDING"))
            {
                FetchLead v1FetchLeadObj = new FetchLead(leadId);
                waitForLoad(5000);
                Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
                PanelLeadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
                WorkFlowId = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.workflowStatusId").toString();
                LOGGER. info("Lead Stage is : " + PanelLeadStage);
            }
            else
            {
                ReallocatingAgent(leadId,"1152");
                break;
            }

        }

        EditLead v1EditLeadObj = new EditLead(leadId, P.TESTDATA.get("EditLead100K"));

        v1EditLeadObj.getProperties().setProperty("documents", DocumetRequestDeserialised);
        v1EditLeadObj.getProperties().setProperty("reEnterAccountNumber", mobileNo);
        v1EditLeadObj.getProperties().setProperty("workflowStatusId", WorkFlowId);

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "**********", "**********", XMWToken, "application/json");

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
        Assert.assertTrue(LeadSatge.contains("PANEL_SUCCESS"));

        //v1EditLeadObj.validateResponseAgainstJSONSchema("MerchantServiceOEPanelV1EditLead/EditLead100KSchema.json");

    }

    @Test(description = "Fetch Lead Details on Panel",groups = {"Regression"},dependsOnMethods = "TC019_PositiveSubmitLeadPanel",timeOut = 60000)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC020_PgCallBack100K()
    {

        PgCallBackFromPanelRef(leadId,CustId,MID);
    }

    @AfterClass(description = "Adding Details on CSV")
    public void AddingDataCsv() throws IOException {
        LOGGER.info("In After Test of " +getClass());

        File fileUpload = new File("OnboardedMerchant.csv") ;
        // Create csv file
        FileWriter outputfile = new FileWriter(fileUpload,true);

        // Write to CSV file which is open
        CSVWriter writer = new CSVWriter(outputfile);

        if(!MID.isEmpty()) {
            LOGGER.info("MID Is not Empty");
            // add data to csv
            String[] data1 = {MID, CustId, mobileNo, "p2p_100k", "Name Match Success"};
            writer.writeNext(data1);
            writer.flush();
            writer.close();
        }
        else
        {
            LOGGER.info("MID is Empty");
        }
    }

}*/





