package OCL.Individual.PSA;

import Request.MerchantService.v1.EDC.FetchPayment;
import Request.MerchantService.v3.SendOtp;
import Request.MerchantService.v3.SubmitDocs;
import Request.MerchantService.v3.SubmitMerchant;
import Request.MerchantService.v3.ValidateOtp;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.PGP.PGPServices;
import Services.Utilities.TestBase;
import Services.Utilities.Utilities;
import Services.Wallet.WalletServices;
import com.goldengate.common.BaseMethod;
import com.google.zxing.NotFoundException;
import com.jcraft.jsch.JSchException;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowPSA extends BaseMethod
{

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    WalletServices walletServicesObj = new WalletServices();
    PGPServices pgpServicesObj = new PGPServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowPSA.class);

    public static String QrCodeId = "";
    public static String AgentToken = "";
    public static String CustId = "";
    public static String mobileNo = "9911801989";
    public static String kybBusinessId = "";
    public static String businessName = "";
    public static String leadId = "";
    public static String UserMID = "";
    public static String State = "";
    public static String OTP ="";
    public static String version = "7.1.1";
    public static String RRBId = "";
    public static String QrBase64 = "";
    public static String PaymentStatus = "";
    public static String PSAPayMID = "";
    public static String PSAPayMGUID = "";
    public static String PSAOrderId = "";
    public static String PSAAmount = "";
    public static String PositivePgResponse = "01";
    public static String FailurePgResponse = "501";
    public static String StatusCodePG = "";
    public static String pathQrCode = "output.jpg";

//    @BeforeClass
    @BeforeTest
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void AgentLoginPSA() throws Exception {

//        AgentToken = CommonAgentToken;
//        LOGGER.info("Agent Token is for PSA : " + AgentToken);
        AgentToken = AgentSessionToken("8010630022", "paytm@123");

        //generating new Number
       /* Utilities accObj = new Utilities();
        mobileNo = accObj.randomMobileNumberGenerator();*/
        LOGGER.info("New Number is for PSA : " + mobileNo);
        DBConnection.UpdateQueryToCloseLead(mobileNo,"psa");

        /*TestBase testBase =new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '"+mobileNo+"' and status = '0' and solution_type='psa';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes); */
    }

    @Test(description = "Create New Applicant PSAP",priority = 211,groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void CreateNewApplicantPSA()
    {
        //generating new Number
        Utilities accObj = new Utilities();
        mobileNo = accObj.randomMobileNumberGenerator();
        log.info("New Number is : " + mobileNo);

        CreateApplicantOauth(mobileNo);
    }

    @Test(description = "Positive SendOTP",priority = 212,dependsOnMethods = "CreateNewApplicantPSA",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void SendOtpPSA()
    {

        SendOtp SendOTPobj = new SendOtp();

        String expectedErrorMsg = "Otp sent to phone";
        Response ResObject = middlewareServicesObject.v3SentOtp(SendOTPobj,"INDIVIDUAL","psa",AgentToken,version,mobileNo,"merchant");
        SendOTPobj.setHttpStatus(ResObject.jsonPath().getString("httpStatus"));
        SendOTPobj.setMessage(ResObject.jsonPath().getString("message"));
        SendOTPobj.setState(ResObject.jsonPath().getString("state"));
        SendOTPobj.setStatus(ResObject.jsonPath().getString("status"));
        LOGGER.info("state" + " " + ResObject.jsonPath().getString("state"));
        State = ResObject.jsonPath().getString("state");
        LOGGER.info("New State " + State);
        String actualErrorMsg = ResObject.jsonPath().getString("message");
        int StatusCode = ResObject.getStatusCode();
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        Assert.assertEquals(StatusCode,200);
        SendOTPobj.validateResponseAgainstJSONSchema("MerchantService/V3/SendOtp/SendOtpResponseSchema.json");

    }
    @Test(priority = 213,description = "Positive ValidateOTP",dependsOnMethods = "SendOtpPSA",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void ValidateOtpPSA() throws IOException, JSchException
    {

        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
        //OTP = getOTP(mobileNo);
        OTP = "888888";
        System.out.println("This is OTP " + OTP);
        Response ResObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobj, "INDIVIDUAL", "psa", AgentToken, version, mobileNo, "merchant", State, OTP);
        leadId = ResObj.jsonPath().getString("leadId");
        LOGGER.info("This is Lead ID : " + leadId);
        Assert.assertNotEquals(leadId, null);

        int StatusCode = ResObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        //TODO:Setting Value of CustID
        CustId = ResObj.jsonPath().getString("custId");
        LOGGER.info("CustId " + CustId);
        Assert.assertNotEquals(CustId, null);
    }

    @Test(priority = 214,description = "Positive Submit Merchant",groups = {"Regression"},dependsOnMethods = "ValidateOtpPSA")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void SubmitLeadPSA()
    {
        SubmitMerchant postMerchant = new SubmitMerchant(CustId,P.TESTDATA.get("SubmitPSA"));
        postMerchant.addParameter("leadId",leadId);

        Response submitMerchResponse = middlewareServicesObject.v3SubmitMerchant(postMerchant,mobileNo,mobileNo,"ICIC0001070",version,"INDIVIDUAL","psa",AgentToken);
        int StatusCode = submitMerchResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        RRBId = submitMerchResponse.jsonPath().getJsonObject("relatedBusinessUuid").toString();
        LOGGER.info(" RRB ID is : " + RRBId);

        postMerchant.validateResponseAgainstJSONSchema("MerchantService/V3/SubmitMerchant/SubmitPSASchema.json");
    }
    @Test(priority = 215,description = "Fetch Payment Status",dependsOnMethods ="SubmitLeadPSA",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void FetchPaymentStatusPSA()
    {
        FetchPayment v1FetchPayment = new FetchPayment();

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId",leadId);
        queryParams.put("generateQR","true");

        Response v1PSAFetchPayment = middlewareServicesObject.v1EdcPayment(v1FetchPayment,queryParams,AgentToken,version);

        int StatusCode = v1PSAFetchPayment.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        PaymentStatus = v1PSAFetchPayment.jsonPath().getJsonObject("paymentDone").toString();
        LOGGER.info("This is Payment Status : " + PaymentStatus);

        if (!PaymentStatus.equals("true"))
        {
            LOGGER.info(" Inside if of Payment != true ");
            QrBase64 = v1PSAFetchPayment.jsonPath().getJsonObject("qrCodeBase64").toString();
            LOGGER.info("This is Base64 of QR Code : " + QrBase64);

            PaymentStatus = v1PSAFetchPayment.jsonPath().getJsonObject("paymentDone").toString();
            LOGGER.info("This is Payment New Status : " + PaymentStatus);
        }

        else
        {
            LOGGER.info(" Payment already Done ");
        }

        v1FetchPayment.validateResponseAgainstJSONSchema("MerchantService/V1/EDC/FetchPayment/FetchPaymentResponseSchema.json");

    }

//    @Test(priority = 216, description = "Extracting QR Code ID",dependsOnMethods = "FetchPaymentStatusPSA")
//    @Owner(emailId = "<EMAIL>",isAutomated = true)
//    public void ExtractQrCodeIdPSA() throws IOException, NotFoundException
//    {
//        if (!PaymentStatus.equals("true"))
//        {
//            QrCodeId = QrCodeExtractor(QrBase64,pathQrCode);
//            LOGGER.info("QR Code ID found inside MAP PSA Test : " + QrCodeId);
//            Assert.assertNotNull(QrCodeId);
//            Assert.assertFalse(QrCodeId.isEmpty());
//        }
//        else
//        {
//            LOGGER.info(" Payment already Done ");
//        }
//
//    }

    //@Test(priority = 217, description = "Fetching QR Code Details from Wallet",dependsOnMethods = "ExtractQrCodeIdPSA")
   // @Owner(emailId = "<EMAIL>",isAutomated = true)
  //  public  void FetchQrDetailsPSA()
 //   {
//        if (!PaymentStatus.equals("true"))
//        {
//            FetchQrDetails(QrCodeId);
//            PSAPayMID = PayMID;
//            LOGGER.info("MID to Pay : " + PSAPayMID);
//
//            PSAPayMGUID = PayMGUID;
//            LOGGER.info("MGUID to Pay : " + PSAPayMGUID);
//
//            PSAAmount = Amount;
//            LOGGER.info("Amount to be Payed : " + PSAAmount);
//
//            PSAOrderId = OrderId;
//            LOGGER.info("OrderId Generated : " + PSAOrderId);
//        }
//        else
//        {
//            LOGGER.info(" Payment already Done ");
//        }
//
    //    if (!PaymentStatus.equals("true"))
//        {
//            FetchQrDetails(QrCodeId);
//            PSAPayMID = PayMID;
//            LOGGER.info("MID to Pay : " + PSAPayMID);
//
//            PSAPayMGUID = PayMGUID;
//            LOGGER.info("MGUID to Pay : " + PSAPayMGUID);
//
//            PSAAmount = Amount;
//            LOGGER.info("Amount to be Payed : " + PSAAmount);
//
//            PSAOrderId = OrderId;
//            LOGGER.info("OrderId Generated : " + PSAOrderId);
//        }
//        else
//        {
//            LOGGER.info(" Payment already Done ");
//        }
//

//    }
//
//    @Test(priority = 218, description = "Making Payment for Map PSA Lead",dependsOnMethods = "FetchQrDetailsPSA")
//    @Owner(emailId = "<EMAIL>",isAutomated = true)
//    public void PayMerchantPSA() throws IOException, NotFoundException
//    {
//        if (!PaymentStatus.equals("true"))
//        {
//            StatusCodePG = PayMerchant(PSAOrderId, PSAAmount, PSAPayMID,"FalseOrderCreated");
//            LOGGER.info("This is PG Status Code is : " + StatusCodePG);
//            FetchPaymentStatusPSA();
//
//            for (int i = 0; i < 4; ++i) {
//                if (!StatusCodePG.equals(PositivePgResponse)) {
//                    FetchPaymentStatusPSA();
//                    ExtractQrCodeIdPSA();
//                    FetchQrDetailsPSA();
//                    StatusCodePG = PayMerchant(PSAOrderId, PSAAmount, PSAPayMID,"FalseOrderCreated");
//                } else {
//                    LOGGER.info("Payment Successfully Done for LeadID : " + leadId);
//                    break;
//                }
//
//            }
//        }
//
//        else
//        {
//            LOGGER.info("Payment already Done ");
//        }
//
//    }
//
//    @Test(priority = 219,description = "Positive Submit Merchant",groups = {"Regression"},dependsOnMethods = "PayMerchantPSA")
//    @Owner(emailId = "<EMAIL>",isAutomated = true)
//    public void UpdateLeadPSA()
//    {
//        SubmitMerchant postMerchant = new SubmitMerchant(CustId,P.TESTDATA.get("UpdatePSA"));
//        postMerchant.addParameter("leadId",leadId);
//        postMerchant.getProperties().setProperty("relatedBusinessUuid",RRBId);
//
//        Response submitMerchResponse = middlewareServicesObject.v3SubmitMerchant(postMerchant,mobileNo,mobileNo,"ANDB0002029",version,"INDIVIDUAL","psa",AgentToken);
//        int StatusCode = submitMerchResponse.getStatusCode();
//        Assert.assertEquals(StatusCode,200);
//
//        RRBId = submitMerchResponse.jsonPath().getJsonObject("relatedBusinessUuid").toString();
//        LOGGER.info(" RRB ID is : " + RRBId);
//
//        postMerchant.validateResponseAgainstJSONSchema("MerchantService/V3/SubmitMerchant/SubmitPSASchema.json");
//    }
//
//    @Test(priority = 220,description = "Positive Submit Merchant Documents",groups = {"Regression"},dependsOnMethods = "UpdateLeadPSA")
//    @Owner(emailId = "<EMAIL>",isAutomated = true)
//    public void UploadVoterIdPSA() {
//        // Next Document
//        SubmitDocs v3DocSubmit = new SubmitDocs();
//
//        Map<String,String>queryDocUpload = new HashMap<>();
//        queryDocUpload.put("type", "jpg");
//        queryDocUpload.put("entityType", "INDIVIDUAL");
//        queryDocUpload.put("solutionType", "psa");
//        queryDocUpload.put("merchantCustId", CustId);
//        queryDocUpload.put("leadId", leadId);
//        queryDocUpload.put("docId",mobileNo);
//        queryDocUpload.put("docType","voterIdAddressProof");
//        queryDocUpload.put("pageNo","0");
//        queryDocUpload.put("docCount","0");
//
//        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit,AgentToken,version,queryDocUpload);
//
//        int expectedErrorCode = 204;
//        int errorCode = submitDocs.jsonPath().getInt("errorCode");
//
//        if (errorCode == 204)
//        {
//            Assert.assertEquals(errorCode, expectedErrorCode);
//        }
//        else
//        {
//            Assert.assertEquals(errorCode, 406);
//        }
//        v3DocSubmit.validateResponseAgainstJSONSchema("MerchantService/V3/SubmitDocs/SubmitDocsResponseSchema.json");
//    }
//
//    @Test(priority = 221,description = "Positive Submit Merchant Documents",groups = {"Regression"},dependsOnMethods = "UpdateLeadPSA")
//    @Owner(emailId = "<EMAIL>",isAutomated = true)
//    public void UploadAgentPhotoPSA() {
//        // Next Document
//        SubmitDocs v3DocSubmit = new SubmitDocs();
//
//        Map<String,String>queryDocUpload = new HashMap<>();
//        queryDocUpload.put("type", "jpg");
//        queryDocUpload.put("entityType", "INDIVIDUAL");
//        queryDocUpload.put("solutionType", "psa");
//        queryDocUpload.put("merchantCustId", CustId);
//        queryDocUpload.put("leadId", leadId);
//        queryDocUpload.put("docId",mobileNo);
//        queryDocUpload.put("docType","agentPhoto");
//        queryDocUpload.put("pageNo","0");
//        queryDocUpload.put("docCount","0");
//
//        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit,AgentToken,version,queryDocUpload);
//
//        int expectedErrorCode = 204;
//        int errorCode = submitDocs.jsonPath().getInt("errorCode");
//
//        if (errorCode == 204)
//        {
//            Assert.assertEquals(errorCode, expectedErrorCode);
//        }
//        else
//        {
//            Assert.assertEquals(errorCode, 406);
//        }
//        v3DocSubmit.validateResponseAgainstJSONSchema("MerchantService/V3/SubmitDocs/SubmitDocsResponseSchema.json");
//    }
//
//    @Test(priority = 222,description = "Positive Submit Merchant Documents",groups = {"Regression"},dependsOnMethods = "UpdateLeadPSA")
//    @Owner(emailId = "<EMAIL>",isAutomated = true)
//    public void UploadEducationProofPSA() {
//        // Next Document
//        SubmitDocs v3DocSubmit = new SubmitDocs();
//
//        Map<String,String>queryDocUpload = new HashMap<>();
//        queryDocUpload.put("type", "jpg");
//        queryDocUpload.put("entityType", "INDIVIDUAL");
//        queryDocUpload.put("solutionType", "psa");
//        queryDocUpload.put("merchantCustId", CustId);
//        queryDocUpload.put("leadId", leadId);
//        queryDocUpload.put("docId",mobileNo);
//        queryDocUpload.put("docType","agentPhoto");
//        queryDocUpload.put("pageNo","0");
//        queryDocUpload.put("docCount","0");
//
//        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit,AgentToken,version,queryDocUpload);
//        int expectedErrorCode = 204;
//        int errorCode = submitDocs.jsonPath().getInt("errorCode");
//
//        if (errorCode == 204)
//        {
//            Assert.assertEquals(errorCode, expectedErrorCode);
//        }
//        else
//        {
//            Assert.assertEquals(errorCode, 406);
//        }
//        v3DocSubmit.validateResponseAgainstJSONSchema("MerchantService/V3/SubmitDocs/SubmitDocsResponseSchema.json");
//   }
  }
