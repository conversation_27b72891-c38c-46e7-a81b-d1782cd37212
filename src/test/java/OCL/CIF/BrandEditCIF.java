package OCL.CIF;

import Request.CIF.EditBrandEMI;
import Services.CIF.CIFServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class BrandEditCIF extends BaseMethod {
    CIFServices cifMiddlewareServices = new CIFServices();

    private static final Logger LOGGER = LogManager.getLogger(BrandEditCIF.class);

    // create a method to generate below token
    //   eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6IkJGRiIsImlzcyI6Ik9FIiwiY3VzdElkIjoiMTcwMTQyMzk3NCIsInRpbWVzdGFtcCI6IjIwMjMtMDgtMThUMTk6MzY6NTQuNzIyKzA1OjMwIn0.cPgDlkgHmiYt6RBX23nFVrx-chNyu2SnyOWa-R9Dvd4

    @Test(priority = 0, description = "Activate a brand for a MID on KYB", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001() {
        EditBrandEMI editbrandEMI = new EditBrandEMI(P.TESTDATA.get("BrandEMIEditRequest"));
        // Create a HashMap to store the data
        Map<String, String> body = new HashMap<>();

        // Parse the JSON payload and store the data in the HashMap
        String brandId = "brandId";
        String brandName = "brandName";
        String status = "status";
        String state = "state";
        String shopType = "shopType";
        String storeId = "storeId";
        String customerId = "customerId";
        String stateCode = "stateCode";
        String KYBId = "KYBId";
        String MID = "MID";
        body.put(brandId, "1000");
        body.put(brandName, "Paytm141");
        body.put(status, "Active");
        body.put(state, "Rajasthan");
        body.put(shopType, "Mynewshop");
        body.put(storeId, "2345");
        body.put(customerId, "fkEmsZ88492498898945");
        body.put(stateCode, "965");
        body.put(KYBId, "B02tncva12w3010");
        body.put(MID, "fkEmsZ88492498898945");

        // Print the HashMap to verify the data
        System.out.println(body);


        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }

        // Create a HashMap to store the general API  headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("x-jwt-token", KYBToken);
        headers.put("kybId", "B02tncva12w3010");

        // Hit the API and get the response
        Response respObj = cifMiddlewareServices.BrandEMIEditServices(editbrandEMI,body, headers);

        LOGGER.info("Activate a brand for a MID on KYB " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
    }
    @Test(priority = 1, description = "Deactivate a brand for a MID on KYB", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002() {
        EditBrandEMI editbrandEMI = new EditBrandEMI(P.TESTDATA.get("BrandEMIEditRequest"));
        // Create a HashMap to store the data
        Map<String, String> body = new HashMap<>();

        // Parse the JSON payload and store the data in the HashMap
        String brandId = "brandId";
        String brandName = "brandName";
        String status = "status";
        String state = "state";
        String shopType = "shopType";
        String storeId = "storeId";
        String customerId = "customerId";
        String stateCode = "stateCode";
        String KYBId = "KYBId";
        String MID = "MID";
        body.put(brandId, "1000");
        body.put(brandName, "Paytm141");
        body.put(status, "Inactive");
        body.put(state, "Rajasthan");
        body.put(shopType, "Mynewshop");
        body.put(storeId, "2345");
        body.put(customerId, "fkEmsZ88492498898945");
        body.put(stateCode, "965");
        body.put(KYBId, "B02tncva12w3010");
        body.put(MID, "fkEmsZ88492498898945");

        // Print the HashMap to verify the data
        System.out.println(body);
        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }
        // Create a HashMap to store the general API  headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("x-jwt-token", KYBToken);
        headers.put("kybId", "B02tncva12w3010");
        // Hit the API and get the response
        Response respObj = cifMiddlewareServices.BrandEMIEditServices(editbrandEMI,body, headers);

        LOGGER.info("Deactivate a brand for a MID on KYB " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
    }
    @Test(priority = 2, description = "Deactivate a brand for a MID on KYB without token", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003() {
        EditBrandEMI editbrandEMI = new EditBrandEMI(P.TESTDATA.get("BrandEMIEditRequest"));
        // Create a HashMap to store the data
        Map<String, String> body = new HashMap<>();

        // Parse the JSON payload and store the data in the HashMap
        String brandId = "brandId";
        String brandName = "brandName";
        String status = "status";
        String state = "state";
        String shopType = "shopType";
        String storeId = "storeId";
        String customerId = "customerId";
        String stateCode = "stateCode";
        String KYBId = "KYBId";
        String MID = "MID";
        body.put(brandId, "1000");
        body.put(brandName, "Paytm141");
        body.put(status, "Inactive");
        body.put(state, "Rajasthan");
        body.put(shopType, "Mynewshop");
        body.put(storeId, "2345");
        body.put(customerId, "fkEmsZ88492498898945");
        body.put(stateCode, "965");
        body.put(KYBId, "B02tncva12w3010");
        body.put(MID, "fkEmsZ88492498898945");

        // Print the HashMap to verify the data
        System.out.println(body);

        // Create a HashMap to store the general API  headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("kybId", "B02tncva12w3010");
        Response respObj = null;
        try {
            respObj =  cifMiddlewareServices.BrandEMIEditServices(editbrandEMI,body, headers);

        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {
            LOGGER.info("Deactivate a brand for a MID on KYB without token " + respObj.statusCode());
            Assert.assertEquals(respObj.statusCode(), 410);
        }
    }
    @Test(priority = 3, description = "Deactivate a brand for a MID on KYB with invalid token", groups = {"Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004() {
        EditBrandEMI editbrandEMI = new EditBrandEMI(P.TESTDATA.get("BrandEMIEditRequest"));
        // Create a HashMap to store the data
        Map<String, String> body = new HashMap<>();

        // Parse the JSON payload and store the data in the HashMap
        String brandId = "brandId";
        String brandName = "brandName";
        String status = "status";
        String state = "state";
        String shopType = "shopType";
        String storeId = "storeId";
        String customerId = "customerId";
        String stateCode = "stateCode";
        String KYBId = "KYBId";
        String MID = "MID";
        body.put(brandId, "1000");
        body.put(brandName, "Paytm141");
        body.put(status, "Inactive");
        body.put(state, "Rajasthan");
        body.put(shopType, "Mynewshop");
        body.put(storeId, "2345");
        body.put(customerId, "fkEmsZ88492498898945");
        body.put(stateCode, "965");
        body.put(KYBId, "B02tncva12w3010");
        body.put(MID, "fkEmsZ88492498898945");

        // Print the HashMap to verify the data
        System.out.println(body);

        // Create a HashMap to store the general API  headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("x-jwt-token", "invalid_token");
        headers.put("kybId", "B02tncva12w3010");
        Response respObj = null;
        try {
            respObj =  cifMiddlewareServices.BrandEMIEditServices(editbrandEMI,body, headers);

        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {
            LOGGER.info("Deactivate a brand for a MID on KYB with invalid token " + respObj.statusCode());
            Assert.assertEquals(respObj.statusCode(), 410);
        }
    }
    @Test(priority = 2, description = "Deactivate a brand for a MID on KYB without KYB ID in headers", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005() throws ParseException {
        EditBrandEMI editbrandEMI = new EditBrandEMI(P.TESTDATA.get("BrandEMIEditRequest"));
        // Create a HashMap to store the data
        Map<String, String> body = new HashMap<>();

        // Parse the JSON payload and store the data in the HashMap
        String brandId = "brandId";
        String brandName = "brandName";
        String status = "status";
        String state = "state";
        String shopType = "shopType";
        String storeId = "storeId";
        String customerId = "customerId";
        String stateCode = "stateCode";
        String KYBId = "KYBId";
        String MID = "MID";
        body.put(brandId, "1000");
        body.put(brandName, "Paytm141");
        body.put(status, "Inactive");
        body.put(state, "Rajasthan");
        body.put(shopType, "Mynewshop");
        body.put(storeId, "2345");
        body.put(customerId, "fkEmsZ88492498898945");
        body.put(stateCode, "965");
        body.put(KYBId, "B02tncva12w3010");
        body.put(MID, "fkEmsZ88492498898945");

        // Print the HashMap to verify the data
        System.out.println(body);


        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }
        // Create a HashMap to store the general API  headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("x-jwt-token", KYBToken);

        // Hit the API and get the response
        Response respObj = cifMiddlewareServices.BrandEMIEditServices(editbrandEMI,body, headers);

        respObj.getBody();
        JSONParser parser = new JSONParser();
        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());
        JSONObject jsObj2 = (JSONObject) jsObj1.get("error");

        LOGGER.info("Deactivate a brand for a MID on KYB without KYB ID in headers " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
        Assert.assertEquals(jsObj2.get("errorMsg"), "KYB id can not be empty");

    }
        @Test(priority = 1, description = "Activate a brand for a MID on KYB without brandId in request body", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006() throws ParseException {
        EditBrandEMI editbrandEMI = new EditBrandEMI(P.TESTDATA.get("BrandEMIEditRequest"));
        // Create a HashMap to store the data
        Map<String, String> body = new HashMap<>();

        // Parse the JSON payload and store the data in the HashMap
        String brandId = "";
        String brandName = "brandName";
        String status = "status";
        String state = "state";
        String shopType = "shopType";
        String storeId = "storeId";
        String customerId = "customerId";
        String stateCode = "stateCode";
        String KYBId = "KYBId";
        String MID = "MID";
        body.put(brandId, "");
        body.put(brandName, "Paytm141");
        body.put(status, "Active");
        body.put(state, "Rajasthan");
        body.put(shopType, "Mynewshop");
        body.put(storeId, "2345");
        body.put(customerId, "fkEmsZ88492498898945");
        body.put(stateCode, "965");
        body.put(KYBId, "B02tncva12w3010");
        body.put(MID, "fkEmsZ88492498898945");

        // Print the HashMap to verify the data
        System.out.println(body);


        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }

        // Create a HashMap to store the general API  headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("x-jwt-token", KYBToken);
        headers.put("kybId", "B02tncva12w3010");

        // Hit the API and get the response
        Response respObj = cifMiddlewareServices.BrandEMIEditServices(editbrandEMI,body, headers);

        respObj.getBody();
        JSONParser parser = new JSONParser();
        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());
        JSONObject jsObj2 = (JSONObject) jsObj1.get("error");

        LOGGER.info("Activate a brand for a MID on KYB without brandId in request body " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        Assert.assertEquals(jsObj2.get("errorMsg"), "Invalid or missing brand id");

    }
    @Test(priority = 1, description = "Activate a brand for a MID on KYB without STATUS in request body", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007() throws ParseException {
        EditBrandEMI editbrandEMI = new EditBrandEMI(P.TESTDATA.get("BrandEMIEditRequest"));
        // Create a HashMap to store the data
        Map<String, String> body = new HashMap<>();

        // Parse the JSON payload and store the data in the HashMap
        String brandId = "brandId";
        String brandName = "brandName";
        String status = "status";
        String state = "state";
        String shopType = "shopType";
        String storeId = "storeId";
        String customerId = "customerId";
        String stateCode = "stateCode";
        String KYBId = "KYBId";
        String MID = "MID";
        body.put(brandId, "1000");
        body.put(brandName, "Paytm141");
        body.put(status, "");
        body.put(state, "Rajasthan");
        body.put(shopType, "Mynewshop");
        body.put(storeId, "2345");
        body.put(customerId, "fkEmsZ88492498898945");
        body.put(stateCode, "965");
        body.put(KYBId, "B02tncva12w3010");
        body.put(MID, "fkEmsZ88492498898945");

        // Print the HashMap to verify the data
        System.out.println(body);


        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }

        // Create a HashMap to store the general API  headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("x-jwt-token", KYBToken);
        headers.put("kybId", "B02tncva12w3010");

        // Hit the API and get the response
        Response respObj = cifMiddlewareServices.BrandEMIEditServices(editbrandEMI,body, headers);

        respObj.getBody();
        JSONParser parser = new JSONParser();
        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());
        JSONObject jsObj2 = (JSONObject) jsObj1.get("error");

        LOGGER.info("Activate a brand for a MID on KYB without STATUS in request body " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        Assert.assertEquals(jsObj2.get("errorMsg"), "Invalid status provided for one of the brand emi. Please provide status as 'Active' or 'Inactive'");

    }
    @Test(priority = 1, description = "Activate a brand for a MID on KYB with invalid STATUS in request body", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008() throws ParseException {
        EditBrandEMI editbrandEMI = new EditBrandEMI(P.TESTDATA.get("BrandEMIEditRequest"));
        // Create a HashMap to store the data
        Map<String, String> body = new HashMap<>();

        // Parse the JSON payload and store the data in the HashMap
        String brandId = "brandId";
        String brandName = "brandName";
        String status = "status";
        String state = "state";
        String shopType = "shopType";
        String storeId = "storeId";
        String customerId = "customerId";
        String stateCode = "stateCode";
        String KYBId = "KYBId";
        String MID = "MID";
        body.put(brandId, "1000");
        body.put(brandName, "Paytm141");
        body.put(status, "sdda");
        body.put(state, "Rajasthan");
        body.put(shopType, "Mynewshop");
        body.put(storeId, "2345");
        body.put(customerId, "fkEmsZ88492498898945");
        body.put(stateCode, "965");
        body.put(KYBId, "B02tncva12w3010");
        body.put(MID, "fkEmsZ88492498898945");

        // Print the HashMap to verify the data
        System.out.println(body);


        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }

        // Create a HashMap to store the general API  headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("x-jwt-token", KYBToken);
        headers.put("kybId", "B02tncva12w3010");

        // Hit the API and get the response
        Response respObj = cifMiddlewareServices.BrandEMIEditServices(editbrandEMI,body, headers);

        respObj.getBody();
        JSONParser parser = new JSONParser();
        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());
        JSONObject jsObj2 = (JSONObject) jsObj1.get("error");

        LOGGER.info("Activate a brand for a MID on KYB with invalid STATUS in request body " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        Assert.assertEquals(jsObj2.get("errorMsg"), "Invalid status provided for one of the brand emi. Please provide status as 'Active' or 'Inactive'");

    }
    @Test(priority = 1, description = "Activate a brand for a MID on KYB without CUSTOMERID in request body", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009() throws ParseException {
        EditBrandEMI editbrandEMI = new EditBrandEMI(P.TESTDATA.get("BrandEMIEditRequest"));
        // Create a HashMap to store the data
        Map<String, String> body = new HashMap<>();

        // Parse the JSON payload and store the data in the HashMap
        String brandId = "brandId";
        String brandName = "brandName";
        String status = "status";
        String state = "state";
        String shopType = "shopType";
        String storeId = "storeId";
        String customerId = "customerId";
        String stateCode = "stateCode";
        String KYBId = "KYBId";
        String MID = "MID";
        body.put(brandId, "1000");
        body.put(brandName, "Paytm141");
        body.put(status, "Active");
        body.put(state, "Rajasthan");
        body.put(shopType, "Mynewshop");
        body.put(storeId, "2345");
        body.put(customerId, "");
        body.put(stateCode, "965");
        body.put(KYBId, "B02tncva12w3010");
        body.put(MID, "fkEmsZ88492498898945");

        // Print the HashMap to verify the data
        System.out.println(body);


        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }

        // Create a HashMap to store the general API  headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("x-jwt-token", KYBToken);
        headers.put("kybId", "B02tncva12w3010");

        // Hit the API and get the response
        Response respObj = cifMiddlewareServices.BrandEMIEditServices(editbrandEMI,body, headers);

        respObj.getBody();
        JSONParser parser = new JSONParser();
        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());
        JSONObject jsObj2 = (JSONObject) jsObj1.get("error");

        LOGGER.info("Activate a brand for a MID on KYB without CUSTOMERID in request body " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        Assert.assertEquals(jsObj2.get("errorMsg"), "Invalid or missing customer id");

    }
    @Test(priority = 1, description = "Activate a brand for a MID on KYB with Customer ID different from MID in request body", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010() throws ParseException {
        EditBrandEMI editbrandEMI = new EditBrandEMI(P.TESTDATA.get("BrandEMIEditRequest"));
        // Create a HashMap to store the data
        Map<String, String> body = new HashMap<>();

        // Parse the JSON payload and store the data in the HashMap
        String brandId = "brandId";
        String brandName = "brandName";
        String status = "status";
        String state = "state";
        String shopType = "shopType";
        String storeId = "storeId";
        String customerId = "customerId";
        String stateCode = "stateCode";
        String KYBId = "KYBId";
        String MID = "MID";
        body.put(brandId, "1000");
        body.put(brandName, "Paytm141");
        body.put(status, "sddd");
        body.put(state, "Rajasthan");
        body.put(shopType, "Mynewshop");
        body.put(storeId, "2345");
        body.put(customerId, "fkEmsZ88492498898945222");
        body.put(stateCode, "965");
        body.put(KYBId, "B02tncva12w3010");
        body.put(MID, "fkEmsZ88492498898945");

        // Print the HashMap to verify the data
        System.out.println(body);


        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }

        // Create a HashMap to store the general API  headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("x-jwt-token", KYBToken);
        headers.put("kybId", "B02tncva12w3010");

        // Hit the API and get the response
        Response respObj = cifMiddlewareServices.BrandEMIEditServices(editbrandEMI,body, headers);

        respObj.getBody();
        JSONParser parser = new JSONParser();
        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());
        JSONObject jsObj2 = (JSONObject) jsObj1.get("error");

        LOGGER.info("Activate a brand for a MID on KYB with Customer ID different from MID in request body " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        Assert.assertEquals(jsObj2.get("errorMsg"), "Customer Id and Mid should be same");

    }
    @Test(priority = 1, description = "Activate a brand for a MID on KYB without KYBID  in request body", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011() throws ParseException {
        EditBrandEMI editbrandEMI = new EditBrandEMI(P.TESTDATA.get("BrandEMIEditRequest"));
        // Create a HashMap to store the data
        Map<String, String> body = new HashMap<>();

        // Parse the JSON payload and store the data in the HashMap
        String brandId = "brandId";
        String brandName = "brandName";
        String status = "status";
        String state = "state";
        String shopType = "shopType";
        String storeId = "storeId";
        String customerId = "customerId";
        String stateCode = "stateCode";
        String KYBId = "KYBId";
        String MID = "MID";
        body.put(brandId, "1000");
        body.put(brandName, "Paytm141");
        body.put(status, "Active");
        body.put(state, "Rajasthan");
        body.put(shopType, "Mynewshop");
        body.put(storeId, "2345");
        body.put(customerId, "fkEmsZ88492498898945222");
        body.put(stateCode, "965");
        body.put(KYBId, "");
        body.put(MID, "fkEmsZ88492498898945");

        // Print the HashMap to verify the data
        System.out.println(body);


        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }

        // Create a HashMap to store the general API  headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("x-jwt-token", KYBToken);
        headers.put("kybId", "B02tncva12w3010");

        // Hit the API and get the response
        Response respObj = cifMiddlewareServices.BrandEMIEditServices(editbrandEMI,body, headers);

        respObj.getBody();
        JSONParser parser = new JSONParser();
        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());
        JSONObject jsObj2 = (JSONObject) jsObj1.get("error");

        LOGGER.info("Activate a brand for a MID on KYB without KYBID  in request body " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        Assert.assertEquals(jsObj2.get("errorMsg"), "KybId for BrandEmi and KybId in request does not match");
    }
    @Test(priority = 1, description = "Activate a brand for a MID on KYB without MID in request body", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012() throws ParseException {
        EditBrandEMI editbrandEMI = new EditBrandEMI(P.TESTDATA.get("BrandEMIEditRequest"));
        // Create a HashMap to store the data
        Map<String, String> body = new HashMap<>();

        // Parse the JSON payload and store the data in the HashMap
        String brandId = "brandId";
        String brandName = "brandName";
        String status = "status";
        String state = "state";
        String shopType = "shopType";
        String storeId = "storeId";
        String customerId = "customerId";
        String stateCode = "stateCode";
        String KYBId = "KYBId";
        String MID = "MID";
        body.put(brandId, "1000");
        body.put(brandName, "Paytm141");
        body.put(status, "Active");
        body.put(state, "Rajasthan");
        body.put(shopType, "Mynewshop");
        body.put(storeId, "2345");
        body.put(customerId, "fkEmsZ88492498898945");
        body.put(stateCode, "965");
        body.put(KYBId, "B02tncva12w3010");
        body.put(MID, "");

        // Print the HashMap to verify the data
        System.out.println(body);


        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }

        // Create a HashMap to store the general API  headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("x-jwt-token", KYBToken);
        headers.put("kybId", "B02tncva12w3010");

        // Hit the API and get the response
        Response respObj = cifMiddlewareServices.BrandEMIEditServices(editbrandEMI,body, headers);

        respObj.getBody();
        JSONParser parser = new JSONParser();
        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());
        JSONObject jsObj2 = (JSONObject) jsObj1.get("error");

        LOGGER.info("Activate a brand for a MID on KYB without MID in request body " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        Assert.assertEquals(jsObj2.get("errorMsg"), "Invalid or missing MID");
    }
    @Test(priority = 1, description = "Activate a brand for a MID on KYB with invalid MID in request body", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013() throws ParseException {
        EditBrandEMI editbrandEMI = new EditBrandEMI(P.TESTDATA.get("BrandEMIEditRequest"));
        // Create a HashMap to store the data
        Map<String, String> body = new HashMap<>();

        // Parse the JSON payload and store the data in the HashMap
        String brandId = "brandId";
        String brandName = "brandName";
        String status = "status";
        String state = "state";
        String shopType = "shopType";
        String storeId = "storeId";
        String customerId = "customerId";
        String stateCode = "stateCode";
        String KYBId = "KYBId";
        String MID = "MID";
        body.put(brandId, "1000");
        body.put(brandName, "Paytm141");
        body.put(status, "Active");
        body.put(state, "Rajasthan");
        body.put(shopType, "Mynewshop");
        body.put(storeId, "2345");
        body.put(customerId, "fkEmsZ8849249889894533");
        body.put(stateCode, "965");
        body.put(KYBId, "B02tncva12w3010");
        body.put(MID, "fkEmsZ8849249889894533");

        // Print the HashMap to verify the data
        System.out.println(body);


        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }

        // Create a HashMap to store the general API  headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("x-jwt-token", KYBToken);
        headers.put("kybId", "B02tncva12w3010");

        // Hit the API and get the response
        Response respObj = cifMiddlewareServices.BrandEMIEditServices(editbrandEMI,body, headers);

        respObj.getBody();
        JSONParser parser = new JSONParser();
        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());
        JSONObject jsObj2 = (JSONObject) jsObj1.get("error");

        LOGGER.info("Activate a brand for a MID on KYB with invalid MID in request body " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        Assert.assertEquals(jsObj2.get("errorMsg"), "Mid is not linked to the given KybId");
    }
    @Test(priority = 1, description = "Activate a brand for a MID on KYB with invalid KYBID in request body", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014() throws ParseException {
        EditBrandEMI editbrandEMI = new EditBrandEMI(P.TESTDATA.get("BrandEMIEditRequest"));
        // Create a HashMap to store the data
        Map<String, String> body = new HashMap<>();

        // Parse the JSON payload and store the data in the HashMap
        String brandId = "brandId";
        String brandName = "brandName";
        String status = "status";
        String state = "state";
        String shopType = "shopType";
        String storeId = "storeId";
        String customerId = "customerId";
        String stateCode = "stateCode";
        String KYBId = "KYBId";
        String MID = "MID";
        body.put(brandId, "1000");
        body.put(brandName, "Paytm141");
        body.put(status, "Active");
        body.put(state, "Rajasthan");
        body.put(shopType, "Mynewshop");
        body.put(storeId, "2345");
        body.put(customerId, "fkEmsZ88492498898945");
        body.put(stateCode, "965");
        body.put(KYBId, "B02tncva12w301022");
        body.put(MID, "fkEmsZ88492498898945");

        // Print the HashMap to verify the data
        System.out.println(body);


        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }

        // Create a HashMap to store the general API  headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("x-jwt-token", KYBToken);
        headers.put("kybId", "B02tncva12w301022");

        // Hit the API and get the response
        Response respObj = cifMiddlewareServices.BrandEMIEditServices(editbrandEMI,body, headers);

        respObj.getBody();
        JSONParser parser = new JSONParser();
        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());
        JSONObject jsObj2 = (JSONObject) jsObj1.get("error");

        LOGGER.info("Activate a brand for a MID on KYB with invalid KYBID in request body " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
        Assert.assertEquals(jsObj2.get("errorMsg"), "Invalid kybId");
    }
    @Test(priority = 1, description = "Activate a brand for a MID on KYB with company KYBID ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015() throws ParseException {
        EditBrandEMI editbrandEMI = new EditBrandEMI(P.TESTDATA.get("BrandEMIEditRequest"));
        // Create a HashMap to store the data
        Map<String, String> body = new HashMap<>();

        // Parse the JSON payload and store the data in the HashMap
        String brandId = "brandId";
        String brandName = "brandName";
        String status = "status";
        String state = "state";
        String shopType = "shopType";
        String storeId = "storeId";
        String customerId = "customerId";
        String stateCode = "stateCode";
        String KYBId = "KYBId";
        String MID = "MID";
        body.put(brandId, "1000");
        body.put(brandName, "Paytm141");
        body.put(status, "Active");
        body.put(state, "Rajasthan");
        body.put(shopType, "Mynewshop");
        body.put(storeId, "2345");
        body.put(customerId, "FrdXYK47724102540019");
        body.put(stateCode, "965");
        body.put(KYBId, "A09k0wisoxb416");
        body.put(MID, "FrdXYK47724102540019");

        // Print the HashMap to verify the data
        System.out.println(body);


        String KYBToken = null;
        try {
            KYBToken = generateTokenKYB();
            System.out.println("KYBTOKEN: " +KYBToken);
        } catch (Exception e) {
            // Handle the exception here
            e.printStackTrace();
        }

        // Create a HashMap to store the general API  headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("x-jwt-token", KYBToken);
        headers.put("kybId", "A09k0wisoxb416");

        // Hit the API and get the response
        Response respObj = cifMiddlewareServices.BrandEMIEditServices(editbrandEMI,body, headers);

        respObj.getBody();
        JSONParser parser = new JSONParser();
        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());
        JSONObject jsObj2 = (JSONObject) jsObj1.get("error");

        LOGGER.info("Activate a brand for a MID on KYB with company KYBID " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);

    }

}

