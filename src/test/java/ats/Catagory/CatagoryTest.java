package ats.Catagory;

import Services.MechantService.MiddlewareServices;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

public class CatagoryTest {

    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    //  LeadRequest LeadRequestRequestObject = new LeadRequest();
    // Utilities UtilitiesObject = new Utilities();

    String agent_session_token = "";
    public static final String ISSUER = "ATS";
   	public static final String CLIENT_ID = "ats-bc";
    Random rand = new Random();
       String custId="1106992015";
    @Test(priority = 1)
    public void createCatagoryWithAllMandatoryAttributes() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        String str="Test_category"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("barcodePrefix", "A");
        body.put("name", str);

        Response catagoryResponse = MiddlewareServicesObject.v1ATSCatagoryRequest(queryParams, headers, body);
        int httpcode = catagoryResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");

    }
    @Test(priority = 2)
    public void createCatagoryWithMissingNameAttributes() {
    	 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

         Map<String, String> queryParams = new HashMap<String, String>();

         Map<String, String> headers = new HashMap<String, String>();
         headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("barcodePrefix", "A");
        body.put("name", "");

        Response catagoryResponse = MiddlewareServicesObject.v1ATSCatagoryRequest(queryParams, headers, body);
        int httpcode = catagoryResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
        Assert.assertEquals(catagoryResponse.getBody().asString().contains("Name is required"),true,"Testcase failed");

    }
    @Test(priority = 3)
    public void createCatagoryWithMissingBarcodePrefixAttributes() {
    	 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

         Map<String, String> queryParams = new HashMap<String, String>();

         Map<String, String> headers = new HashMap<String, String>();
         headers.put("jwt", token);;
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("barcodePrefix", "");
        body.put("name", "TestCatagory");


        Response catagoryResponse = MiddlewareServicesObject.v1ATSCatagoryRequest(queryParams, headers, body);
        int httpcode = catagoryResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
        Assert.assertEquals(catagoryResponse.getBody().asString().contains("msg.error.invalid.barcode.prefix"),true,"Testcase failed");
        //System.out.println("Body is ----->" +catagoryResponse.getBody().asString().contains("msg.error.invalid.barcode.prefix"));

    }
    @Test(priority = 4)
    public void searchCatagoryWithValidName() {
   	 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("name", "asd1sd");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();

        Response catagoryResponse = MiddlewareServicesObject.v1ATSCatagoryFetchDetails(queryParams, headers, body);
        int httpcode = catagoryResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        Assert.assertEquals(catagoryResponse.getBody().asString().contains("SUCCESS"),true,"Testcase failed");
        //System.out.println("Body is ----->" +catagoryResponse.getBody().asString().contains("msg.error.invalid.barcode.prefix"));

    }
    @Test(priority = 5)
    public void searchCatagoryWithInvalidName() {
   	 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("name", "uiewioe");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();

        Response catagoryResponse = MiddlewareServicesObject.v1ATSCatagoryFetchDetails(queryParams, headers, body);
        int httpcode = catagoryResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        Assert.assertEquals(catagoryResponse.getBody().asString().contains("No category for given search criteria"),true,"Testcase failed");
        //System.out.println("Body is ----->" +catagoryResponse.getBody().asString().contains("msg.error.invalid.barcode.prefix"));

    }
    @Test(priority = 6)
    public void searchCatagoryWithoutName() {
   	 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("name", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();

        Response catagoryResponse = MiddlewareServicesObject.v1ATSCatagoryFetchDetails(queryParams, headers, body);
        int httpcode = catagoryResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        Assert.assertEquals(catagoryResponse.getBody().asString().contains("SUCCESS"),true,"Testcase failed");
        //System.out.println("Body is ----->" +catagoryResponse.getBody().asString().contains("msg.error.invalid.barcode.prefix"));

    }
    @Test(priority = 7)
    public void createCatagoryWithMissingPrefixAndCategoryName() {
    	 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

         Map<String, String> queryParams = new HashMap<String, String>();

         Map<String, String> headers = new HashMap<String, String>();
         headers.put("jwt", token);;
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("barcodePrefix", "");
        body.put("name", "");


        Response catagoryResponse = MiddlewareServicesObject.v1ATSCatagoryRequest(queryParams, headers, body);
        int httpcode = catagoryResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
        Assert.assertEquals(catagoryResponse.getBody().asString().contains("msg.error.invalid.barcode.prefix"),true,"Testcase failed");
        //System.out.println("Body is ----->" +catagoryResponse.getBody().asString().contains("msg.error.invalid.barcode.prefix"));

    }
    @Test(priority = 8)
    public void createCatagoryWithAlreadyExistingCategoryName() {
    	 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

         Map<String, String> queryParams = new HashMap<String, String>();

         Map<String, String> headers = new HashMap<String, String>();
         headers.put("jwt", token);;
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("barcodePrefix", "A");
        body.put("name", "TestCatagory");


        Response catagoryResponse = MiddlewareServicesObject.v1ATSCatagoryRequest(queryParams, headers, body);
        int httpcode = catagoryResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==400, "Testcase Failed");
        //System.out.println("Body is ----->" +catagoryResponse.getBody().asString().contains("msg.error.invalid.barcode.prefix"));

    }
    @Test(priority = 9)
    public void createCatagoryWithNumberInPrefix() {
    	 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
         String str="Test_category"+String.valueOf(rand.nextInt(1000));

         Map<String, String> queryParams = new HashMap<String, String>();

         Map<String, String> headers = new HashMap<String, String>();
         headers.put("jwt", token);;
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("barcodePrefix", "2");
        body.put("name",str);


        Response catagoryResponse = MiddlewareServicesObject.v1ATSCatagoryRequest(queryParams, headers, body);
        int httpcode = catagoryResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        //System.out.println("Body is ----->" +catagoryResponse.getBody().asString().contains("msg.error.invalid.barcode.prefix"));

    }
    @Test(priority = 10)
    public void createCatagoryWithoutAccept() {
    	 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
         String str="Test_category"+String.valueOf(rand.nextInt(1000));

         Map<String, String> queryParams = new HashMap<String, String>();

         Map<String, String> headers = new HashMap<String, String>();
         headers.put("jwt", token);;
        headers.put("accept", "");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("barcodePrefix", "B");
        body.put("name",str);


        Response catagoryResponse = MiddlewareServicesObject.v1ATSCatagoryRequest(queryParams, headers, body);
        int httpcode = catagoryResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        //System.out.println("Body is ----->" +catagoryResponse.getBody().asString().contains("msg.error.invalid.barcode.prefix"));

    }
    @Test(priority = 11)
    public void createCatagoryWithoutAcceptLanguage() {
    	 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
         String str="Test_category"+String.valueOf(rand.nextInt(1000));

         Map<String, String> queryParams = new HashMap<String, String>();

         Map<String, String> headers = new HashMap<String, String>();
         headers.put("jwt", token);;
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("barcodePrefix", "C");
        body.put("name",str);


        Response catagoryResponse = MiddlewareServicesObject.v1ATSCatagoryRequest(queryParams, headers, body);
        int httpcode = catagoryResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        //System.out.println("Body is ----->" +catagoryResponse.getBody().asString().contains("msg.error.invalid.barcode.prefix"));

    }
    @Test(priority = 12)
    public void searchCatagoryWithCategoryIdInplaceOfName() {
   	 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("name", "67");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();

        Response catagoryResponse = MiddlewareServicesObject.v1ATSCatagoryFetchDetails(queryParams, headers, body);
        int httpcode = catagoryResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        //System.out.println("Body is ----->" +catagoryResponse.getBody().asString().contains("msg.error.invalid.barcode.prefix"));

    }
    @Test(priority = 13)
    public void searchCatagoryWithValidCategotyId() {
   	 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("id", "67");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();

        Response catagoryResponse = MiddlewareServicesObject.v1ATSCatagoryFetchDetails(queryParams, headers, body);
        int httpcode = catagoryResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        //System.out.println("Body is ----->" +catagoryResponse.getBody().asString().contains("msg.error.invalid.barcode.prefix"));

    }
    @Test(priority = 14)
    public void searchCatagoryWithInValidCategotyId() {
   	 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("id", "6755");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();

        Response catagoryResponse = MiddlewareServicesObject.v1ATSCatagoryFetchDetails(queryParams, headers, body);
        int httpcode = catagoryResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        //System.out.println("Body is ----->" +catagoryResponse.getBody().asString().contains("msg.error.invalid.barcode.prefix"));

    }
    @Test(priority = 15)
    public void searchCatagoryWithCategoryNameInPlaceOfId() {
   	 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("id", "Fastag");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();

        Response catagoryResponse = MiddlewareServicesObject.v1ATSCatagoryFetchDetails(queryParams, headers, body);
        int httpcode = catagoryResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==400, "Testcase Failed");
        //System.out.println("Body is ----->" +catagoryResponse.getBody().asString().contains("msg.error.invalid.barcode.prefix"));

    }
    @Test(priority = 16)
    public void searchCatagoryWithCategoryIdNull() {
   	 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("id", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();

        Response catagoryResponse = MiddlewareServicesObject.v1ATSCatagoryFetchDetails(queryParams, headers, body);
        int httpcode = catagoryResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        //System.out.println("Body is ----->" +catagoryResponse.getBody().asString().contains("msg.error.invalid.barcode.prefix"));

    }
    
}