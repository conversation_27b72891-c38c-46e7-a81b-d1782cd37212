package ats.AssetDetailsController;

import Services.MechantService.MiddlewareServices;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class AssetDetailsControllerTest {
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    //  LeadRequest LeadRequestRequestObject = new LeadRequest();
    // Utilities UtilitiesObject = new Utilities();


    String timeStamp = "";
    String agent_session_token = "";
    public static final String ISSUER = "ATS";
	public static final String CLIENT_ID = "ats-bc";
    String custId="1000334963";

    @Test(priority = 1)
    public void getAssetCountForAvailable() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForAvailableResponse = MiddlewareServicesObject.v1AssetCountForAvailable(queryParams, headers, body);
        int httpcode = v1AssetCountForAvailableResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 2)
    public void getAssetCountForAssigned() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForAssignedResponse = MiddlewareServicesObject.v1AssetCountForAssigned(queryParams, headers, body);
        int httpcode = v1AssetCountForAssignedResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 3)
    public void getAssetCountForDeployed() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForDeployedResponse = MiddlewareServicesObject.v1AssetCountForDeployed(queryParams, headers, body);
        int httpcode = v1AssetCountForDeployedResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 4)
    public void getAssetCountForPending_Assign() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForPending_AssignResponse = MiddlewareServicesObject.v1AssetCountForPending_Assign(queryParams, headers, body);
        int httpcode = v1AssetCountForPending_AssignResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 5)
    public void getAssetCountForPending_Acknowledgement() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForPending_AcknowledgementResponse = MiddlewareServicesObject.v1AssetCountForPending_Acknowledgement(queryParams, headers, body);
        int httpcode = v1AssetCountForPending_AcknowledgementResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 6)
    public void getAssetCountForUnmapped() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForUnmappedResponse = MiddlewareServicesObject.v1AssetCountForUnmapped(queryParams, headers, body);
        int httpcode = v1AssetCountForUnmappedResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 7)
    public void getAssetCountForReturned() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForReturnedResponse = MiddlewareServicesObject.v1AssetCountForReturned(queryParams, headers, body);
        int httpcode = v1AssetCountForReturnedResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 8)
    public void getAssetCountForLost() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForLostResponse = MiddlewareServicesObject.v1AssetCountForLost(queryParams, headers, body);
        int httpcode = v1AssetCountForLostResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 9)
    public void getListOfAssetDetailsForAssigned() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForAssigned_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAssigned(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForAssigned_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForAssigned_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 10)
    public void getListOfAssetDetailsForAvailable() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForAvailable_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAvailable(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForAvailable_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForAvailable_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 11)
    public void getListOfAssetDetailsForDeployed() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForDeployed_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForDeployed(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForDeployed_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForDeployed_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 12)
    public void getListOfAssetDetailsForPending_Assign() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Assign_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForPending_Assign(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Assign_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Assign_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 13)
    public void getListOfAssetDetailsForPending_Acknowledgement() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Acknowledgement_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForPending_Acknowledgement(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 14)
    public void getListOfAssetDetailsForUnmapped() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForUnmapped_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForUnmapped(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForUnmapped_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForUnmapped_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 15)
    public void getListOfAssetDetailsForReturned() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForReturned_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForReturned(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForReturned_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForReturned_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 16)
    public void getListOfAssetDetailsForLost() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForLost_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForLost(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForLost_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForLost_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 17)
    public void getAssetCountForAllStates() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForAllStates_Response = MiddlewareServicesObject.v1AssetCountForAllStates(queryParams, headers, body);
        int httpcode = v1AssetCountForAllStates_Response.getStatusCode();
        //Assert.assertEquals(200, v1AssetCountForAllStates_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 18)
    public void getAssetCountForAValidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "ABC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForAllStates_Response = MiddlewareServicesObject.v1AssetCountForAllStates(queryParams, headers, body);
        int httpcode = v1AssetCountForAllStates_Response.getStatusCode();
        //Assert.assertEquals(200, v1AssetCountForAllStates_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 19)
    public void getAssetCountForAinvalidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "ABVUSOO");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForAllStates_Response = MiddlewareServicesObject.v1AssetCountForAllStates(queryParams, headers, body);
        int httpcode = v1AssetCountForAllStates_Response.getStatusCode();
        //Assert.assertEquals(200, v1AssetCountForAllStates_Response.getStatusCode());
        Assert.assertTrue(httpcode == 500, "Testcase Failed");

    }
    @Test(priority = 20)
    public void getAssetCountForAIntegerInCategoryName() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "33");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForAllStates_Response = MiddlewareServicesObject.v1AssetCountForAllStates(queryParams, headers, body);
        int httpcode = v1AssetCountForAllStates_Response.getStatusCode();
        //Assert.assertEquals(200, v1AssetCountForAllStates_Response.getStatusCode());
        Assert.assertTrue(httpcode == 500, "Testcase Failed");

    }
    @Test(priority = 21)
    public void getAssetCountForSpecialCharachtersInCategoryName() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "%@%^^");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForAllStates_Response = MiddlewareServicesObject.v1AssetCountForAllStates(queryParams, headers, body);
        int httpcode = v1AssetCountForAllStates_Response.getStatusCode();
        //Assert.assertEquals(200, v1AssetCountForAllStates_Response.getStatusCode());
        Assert.assertTrue(httpcode == 500, "Testcase Failed");

    }
    @Test(priority = 22)
    public void getAssetCountForNullInCategoryName() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForAllStates_Response = MiddlewareServicesObject.v1AssetCountForAllStates(queryParams, headers, body);
        int httpcode = v1AssetCountForAllStates_Response.getStatusCode();
        //Assert.assertEquals(200, v1AssetCountForAllStates_Response.getStatusCode());
        Assert.assertTrue(httpcode == 500, "Testcase Failed");

    }
    @Test(priority = 23)
    public void getAssetCountForValidSKUID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuIds", "100777");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForAllStates_Response = MiddlewareServicesObject.v1AssetCountForAllStates(queryParams, headers, body);
        int httpcode = v1AssetCountForAllStates_Response.getStatusCode();
        //Assert.assertEquals(200, v1AssetCountForAllStates_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 24)
    public void getAssetCountForMultipleSKUIDs() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuIds", "100777,100803");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForAllStates_Response = MiddlewareServicesObject.v1AssetCountForAllStates(queryParams, headers, body);
        int httpcode = v1AssetCountForAllStates_Response.getStatusCode();
        //Assert.assertEquals(200, v1AssetCountForAllStates_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 25)
    public void getAssetCountForCategoryAndSKUID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "ABC");
        queryParams.put("skuIds", "100777");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForAllStates_Response = MiddlewareServicesObject.v1AssetCountForAllStates(queryParams, headers, body);
        int httpcode = v1AssetCountForAllStates_Response.getStatusCode();
        //Assert.assertEquals(200, v1AssetCountForAllStates_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 26)
    public void getAssetCountWhenCategoryAndSKUIdDoesNotHaveMapping() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "Fastag");
        queryParams.put("skuIds", "100777");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForAllStates_Response = MiddlewareServicesObject.v1AssetCountForAllStates(queryParams, headers, body);
        int httpcode = v1AssetCountForAllStates_Response.getStatusCode();
        //Assert.assertEquals(200, v1AssetCountForAllStates_Response.getStatusCode());
        Assert.assertTrue(httpcode == 500, "Testcase Failed");

    }
    @Test(priority = 27)
    public void getAssetCountForNullSkuId() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuIds", "");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForAllStates_Response = MiddlewareServicesObject.v1AssetCountForAllStates(queryParams, headers, body);
        int httpcode = v1AssetCountForAllStates_Response.getStatusCode();
        //Assert.assertEquals(200, v1AssetCountForAllStates_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 28)
    public void getAssetCountForValidCategoryAndNullSkuId() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "Fastag");
        queryParams.put("skuIds", "");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForAllStates_Response = MiddlewareServicesObject.v1AssetCountForAllStates(queryParams, headers, body);
        int httpcode = v1AssetCountForAllStates_Response.getStatusCode();
        //Assert.assertEquals(200, v1AssetCountForAllStates_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 29)
    public void getAssetCountForInvalidSkuId() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuIds", "688677");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1AssetCountForAllStates_Response = MiddlewareServicesObject.v1AssetCountForAllStates(queryParams, headers, body);
        int httpcode = v1AssetCountForAllStates_Response.getStatusCode();
        //Assert.assertEquals(200, v1AssetCountForAllStates_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 30)
    public void getListOfAssetDetailsForAssignedAndValidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "Fastag");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForAssigned_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAssigned(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForAssigned_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForAssigned_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 31)
    public void getListOfAssetDetailsForAssignedAndInvalidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "I&YFUOu");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForAssigned_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAssigned(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForAssigned_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForAssigned_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 32)
    public void getListOfAssetDetailsForAvailableAndValidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "ABC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForAvailable_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAvailable(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForAvailable_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForAvailable_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 33)
    public void getListOfAssetDetailsForAvailableAndInvalidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "HU8979");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForAvailable_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAvailable(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForAvailable_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForAvailable_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 34)
    public void getListOfAssetDetailsForDeployedAndValidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "ABC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForDeployed_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForDeployed(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForDeployed_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForDeployed_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 35)
    public void getListOfAssetDetailsForDeployedAndInvalidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "t4t3t");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForDeployed_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForDeployed(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForDeployed_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForDeployed_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 36)
    public void getListOfAssetDetailsForPending_AssignAndValidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "ABC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Assign_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForPending_Assign(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Assign_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Assign_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 37)
    public void getListOfAssetDetailsForPending_AssignAndInvalidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "53hfhr");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Assign_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForPending_Assign(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Assign_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Assign_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 38)
    public void getListOfAssetDetailsForPending_AcknowledgementAndValidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "ABC");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Acknowledgement_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForPending_Acknowledgement(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 39)
    public void getListOfAssetDetailsForPending_AcknowledgementAndInvalidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "87999ghv");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Acknowledgement_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForPending_Acknowledgement(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 40)
    public void getListOfAssetDetailsForUnmappedAndValidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "ABC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForUnmapped_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForUnmapped(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForUnmapped_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForUnmapped_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 41)
    public void getListOfAssetDetailsForUnmappedAndInvalidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "76ffyy");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForUnmapped_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForUnmapped(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForUnmapped_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForUnmapped_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 42)
    public void getListOfAssetDetailsForReturnedAndValidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "ABC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForReturned_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForReturned(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForReturned_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForReturned_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 43)
    public void getListOfAssetDetailsForReturnedAndInvalidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "98ghvhv");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForReturned_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForReturned(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForReturned_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForReturned_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 44)
    public void getListOfAssetDetailsForAssignedAndValidBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AI100803000000050");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForAssigned_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAssigned(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForAssigned_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForAssigned_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 45)
    public void getListOfAssetDetailsForAssignedAndInvalidBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "YFIKDJA2");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForAssigned_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAssigned(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForAssigned_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForAssigned_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 46)
    public void getListOfAssetDetailsForAvailableAndValidBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AI100808000000027");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForAvailable_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAvailable(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForAvailable_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForAvailable_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 47)
    public void getListOfAssetDetailsForAvailableAndInvalidBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "UFFFYJJ099");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForAvailable_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAvailable(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForAvailable_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForAvailable_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 48)
    public void getListOfAssetDetailsForDeployedAndValidBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AI100777000000176");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForDeployed_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForDeployed(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForDeployed_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForDeployed_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 49)
    public void getListOfAssetDetailsForDeployedAndInvalidBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "UGJH*&^*(&");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForDeployed_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForDeployed(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForDeployed_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForDeployed_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 50)
    public void getListOfAssetDetailsForPending_AssignAndValidBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AI100755000000009");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Assign_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForPending_Assign(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Assign_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Assign_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 51)
    public void getListOfAssetDetailsForPending_AssignAndInvalidBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "HJGUIHL99");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Assign_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForPending_Assign(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Assign_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Assign_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 52)
    public void getListOfAssetDetailsForPending_AcknowledgementAndValidBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AI100801000000002");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Acknowledgement_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForPending_Acknowledgement(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 53)
    public void getListOfAssetDetailsForPending_AcknowledgementAndInvalidBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "HUHJJHG09");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Acknowledgement_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForPending_Acknowledgement(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 54)
    public void getListOfAssetDetailsForUnmappedAndValidBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AI100801000000007");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForUnmapped_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForUnmapped(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForUnmapped_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForUnmapped_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 55)
    public void getListOfAssetDetailsForUnmappedAndInvalidBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "KJGJKJLIJK008");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForUnmapped_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForUnmapped(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForUnmapped_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForUnmapped_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 56)
    public void getListOfAssetDetailsForReturnedAndValidBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AI100823000000007");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForReturned_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForReturned(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForReturned_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForReturned_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 57)
    public void getListOfAssetDetailsForReturnedAndInvalidBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "IUGKKLHJ007");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForReturned_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForReturned(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForReturned_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForReturned_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 58)
    public void getListOfAssetDetailsForAssignedAndValidPhoneNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "5171717173");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForAssigned_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAssigned(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForAssigned_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForAssigned_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 59)
    public void getListOfAssetDetailsForAssignedAndInvalidPhoneNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "5533556478");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForAssigned_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAssigned(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForAssigned_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForAssigned_Response.getStatusCode());
        Assert.assertTrue(httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 60)
    public void getListOfAssetDetailsForAvailableAndValidPhoneNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "8884211099");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForAvailable_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAvailable(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForAvailable_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForAvailable_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 61)
    public void getListOfAssetDetailsForAvailableAndInvalidPhoneNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "55663322456");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForAvailable_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAvailable(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForAvailable_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForAvailable_Response.getStatusCode());
        Assert.assertTrue(httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 62)
    public void getListOfAssetDetailsForDeployedAndValidPhoneNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "5222222226");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForDeployed_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForDeployed(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForDeployed_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForDeployed_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 63)
    public void getListOfAssetDetailsForDeployedAndInvalidPhoneNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "6556786745");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForDeployed_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForDeployed(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForDeployed_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForDeployed_Response.getStatusCode());
        Assert.assertTrue(httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 64)
    public void getListOfAssetDetailsForPending_AssignAndValidPhoneNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "7347237790");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Assign_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForPending_Assign(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Assign_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Assign_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 65)
    public void getListOfAssetDetailsForPending_AssignAndInvalidPhoneNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "6755453345");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Assign_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForPending_Assign(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Assign_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Assign_Response.getStatusCode());
        Assert.assertTrue(httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 66)
    public void getListOfAssetDetailsForPending_AcknowledgementAndValidPhoneNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "5171717173");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Acknowledgement_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForPending_Acknowledgement(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 67)
    public void getListOfAssetDetailsForPending_AcknowledgementAndInvalidPhoneNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "5175743773");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Acknowledgement_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForPending_Acknowledgement(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode());
        Assert.assertTrue(httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 68)
    public void getListOfAssetDetailsAvailableWithUserWithoutAcceptHeader() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "7838602638");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Acknowledgement_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAvailable(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 69)
    public void getListOfAssetDetailsAvailableWithUserWithoutAcceptLanguage() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "7838602638");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Acknowledgement_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAvailable(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 70)
    public void getListOfAssetDetailsAvailableWithUserWithoutContentType() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "7838602638");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");

        Map<String, String> body = new HashMap<String, String>();


        Response v1ListOfAssetDetailsForPending_Acknowledgement_Response = MiddlewareServicesObject.v1ListOfAssetDetailsForAvailable(queryParams, headers, body);
        int httpcode = v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode();
        //Assert.assertEquals(200, v1ListOfAssetDetailsForPending_Acknowledgement_Response.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
}
