package ats.SkuGroup;


import Services.LendingService.LendingBaseAPI;
import Services.MechantService.MiddlewareServices;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

public class SkuGroupTest {
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
    String agent_session_token = "";
    public static final String ISSUER = "ATS";
	public static final String CLIENT_ID = "ats-bc";
    String custId="1000334963";
    Random rand = new Random();
    @Test(priority = 1)
    public void PostCreateSkuGroupWithValidName() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        String str="sku_Test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("groupName", str);
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
   
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1SkuGroup(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 2)
    public void PostCreateSkuGroupWithInvalidName() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("groupName", "ABCDTH");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
   
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1SkuGroup(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 3)
    public void PostCreateSkuGroupWithInvalidNameStartingWithsku_() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("groupName", "sku_%$^&");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
   
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1SkuGroup(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 4)
    public void PostCreateSkuGroupWithNumbers() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("groupName", "77655");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
   
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1SkuGroup(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 5)
    public void PostCreateSkuGroupWhichAlreadyExists() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("groupName", "sku_Fastag");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
   
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1SkuGroup(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 6)
    public void PostCreateSkuGroupWithEmptyGroupName() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("groupName", " ");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
   
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1SkuGroup(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 7)
    public void GetFetchSkuGroupsWithValidPrefix() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("groupName", "sku_");
        queryParams.put("pageNumber", "1");
        queryParams.put("pageSize", "10");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
   
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1SkuGroupFetch(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 8)
    public void GetFetchSkuGroupsWithNull() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("groupName", " ");
        queryParams.put("pageNumber", "1");
        queryParams.put("pageSize", "10");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
   
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1SkuGroupFetch(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 9)
    public void GetFetchSkuGroupsWithValidGroupName() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("groupName", "sku_Fastag");
        queryParams.put("pageNumber", "1");
        queryParams.put("pageSize", "10");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
   
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1SkuGroupFetch(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 10)
    public void GetFetchSkuGroupWhichIsNotPresentInPageNumber2() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("groupName", "sku_Fastag");
        queryParams.put("pageNumber", "2");
        queryParams.put("pageSize", "10");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
   
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1SkuGroupFetch(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 11)
    public void GetFetchSkuGroupWithInvalidGroupName() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("groupName", "sk4244");
        queryParams.put("pageNumber", "2");
        queryParams.put("pageSize", "10");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
   
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1SkuGroupFetch(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
}
