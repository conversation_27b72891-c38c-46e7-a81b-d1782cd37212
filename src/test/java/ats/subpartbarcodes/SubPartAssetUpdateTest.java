
package ats.subpartbarcodes;


import Request.ats.Subpartassetupdate;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class SubPartAssetUpdateTest extends BaseMethod {
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(SubPartAssetUpdateTest.class);
    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
    String token = findXMWExTokenforPanel("8010630022", "paytm@123");
    String Invalid_token = "%2FzRPXoCRYQ4JNVowsYAX6VoNcUv1gM%2FeMImfc2oBA1ekUBqbFvVlgWr1DnKTPgerPADaxIdJoysgQ5Dr5%2FN1jw%3D%3D";

    @Test(priority = 0, description = " positive flow status code linking both sub parts")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_link_both_barcode() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "REGA910ASSET");
        body.put("deployedTo", "GWRauq68719480546528");
        body.put("userIdTo", "1701174773");//1700900453
        //subPartBarcodes
        //battery
        body.put("barcode1", "BATREG11");//BATT196
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "CGRREG11");//CGR196
        body.put("subPartType2", "CHARGER");


        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);

    }

    @Test(priority = 0, description = "linking battery barcode with missing charger")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_missing_charger_barcode() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "NEW777A910EDC753");
        body.put("deployedTo", "ADfyin30849684056371");
        body.put("userIdTo", "1700943239");
        //subPartBarcodes
        //battery
        body.put("barcode1", "BATT22");
        body.put("subPartType1", "BATTERY");

        body.put("barcode2", "");//CGR196
        body.put("subPartType2", "");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj,headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "linking charger barcode with missing battery")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_missing_battery_barcode() {
        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "NEW777A910EDC753");
        body.put("deployedTo", "ADfyin30849684056371");
        body.put("userIdTo", "1700943239");
        //subPartBarcodes
        body.put("barcode1", "");//CGR196
        body.put("subPartType1", "");
        //charger
        body.put("barcode2", "CGR22");
        body.put("subPartType2", "CHARGER");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "charger serial number is not mentioned")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_battery_barcode_not_mentioned() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "NEW777A910EDC753");
        body.put("deployedTo", "ADfyin30849684056371");
        body.put("userIdTo", "1700943239");
        //subPartBarcodes
        body.put("barcode1", "");//CGR196
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "CGR22");
        body.put("subPartType2", "CHARGER");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);

    }

    @Test(priority = 0, description = "charger serial number is not mentioned")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_charger_barcode_not_mentioned() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "NEW777A910EDC753");
        body.put("deployedTo", "ADfyin30849684056371");
        body.put("userIdTo", "1700943239");
        //subPartBarcodes
        body.put("barcode1", "BATT22");//CGR196
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "");
        body.put("subPartType2", "CHARGER");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);

    }

    @Test(priority = 0, description = "EDC barcode is not at ATS")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_no_edc_barcode_at_ats() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "ABCdummy123");
        body.put("deployedTo", "ADfyin30849684056371");
        body.put("userIdTo", "1700943239");
        //subPartBarcodes
        body.put("barcode1", "BATT23");
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "CGR23");
        body.put("subPartType2", "CHARGER");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj,headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);

    }

    @Test(priority = 0, description = "EDC barcode is missing")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_missing_edc_barcode() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "");
        body.put("deployedTo", "ADfyin30849684056371");
        body.put("userIdTo", "1700943239");
        //subPartBarcodes
        body.put("barcode1", "BATT23");
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "CGR23");
        body.put("subPartType2", "CHARGER");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);
        LOGGER.info("Display Message : " + respObj.jsonPath().getString("data.failedList[0]"));

    }
    @Test(priority = 0, description = "only battery barcode is linked")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_only_battery() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "NEW777A910EDC753");
        body.put("deployedTo", "ADfyin30849684056371");
        body.put("userIdTo", "1700943239");
        //subPartBarcodes
        //battery
        body.put("barcode1", "BATT24");
        body.put("subPartType1", "BATTERY");


        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj,headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);

    }


    @Test(priority = 0, description = "only charger barcode is used")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_only_charger() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "NEW777A910EDC753");
        body.put("deployedTo", "ADfyin30849684056371");
        body.put("userIdTo", "1700943239");
        //subPartBarcodes

        //charger
        body.put("barcode2", "CGR24");
        body.put("subPartType2", "CHARGER");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj,headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);

    }


    @Test(priority = 0, description = "device details are not specified")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_missing_device_details() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "NEW777A910EDC753");
        body.put("deployedTo", "");
        body.put("userIdTo", "");
        //subPartBarcodes
        //battery
        body.put("barcode1", "BATT25");
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "CGR25");
        body.put("subPartType2", "CHARGER");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj,headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);

    }

    @Test(priority = 0, description = "Both sub parts are not present at ATS")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_non_onboarded_ATS() {
        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "NEW777A910EDC753");
        body.put("deployedTo", "ADfyin30849684056371");
        body.put("userIdTo", "1700943239");
        //subPartBarcodes
        //battery
        body.put("barcode1", "dummybattery1");
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "dummycharger1");
        body.put("subPartType2", "CHARGER");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);
        LOGGER.info("Display Message : " + respObj.jsonPath().getString("data.failedList[0].errorMessage"));


    }

    @Test(priority = 0, description = "when battery is missing and charger is not present at ATS")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_non_onboarded_charger_ATS() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "HDSH2SX344");
        body.put("deployedTo", "CvAAQK22515370023443");
        body.put("userIdTo", "1701224549");
        //subPartBarcodes
        //battery
        body.put("barcode1", "");
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "dummycharger1");
        body.put("subPartType2", "CHARGER");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj,headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);
        LOGGER.info("Display Message : " + respObj.jsonPath().getString("resultInfo"));

    }

    @Test(priority = 0, description = "when charger is missing and the battery is not present at ATS")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_non_onboarded_battery_ATS() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "HDSH2SX344");
        body.put("deployedTo", "CvAAQK22515370023443");
        body.put("userIdTo", "1701224549");
        //subPartBarcodes
        //battery
        body.put("barcode1", "dummybattery1");
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "");
        body.put("subPartType2", "CHARGER");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);
        LOGGER.info("Display Message : " + respObj.jsonPath().getString("resultInfo"));

    }

    @Test(priority = 0, description = "changing the request from deployed to unmap")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014_Unmap_request() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "UNMAP");
        body.put("barcode", "HDSH2SX344");
        body.put("deployedTo", "CvAAQK22515370023443");
        body.put("userIdTo", "1701224549");
        //subPartBarcodes
        //battery
        body.put("barcode1", "BAT38");
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "CGR38");
        body.put("subPartType2", "CHARGER");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj,headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "changing the request from deployed to receive")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015_receive_request() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "RECEIVE");
        body.put("barcode", "HDSH2SX344");
        body.put("deployedTo", "CvAAQK22515370023443");
        body.put("userIdTo", "1701224549");
        //subPartBarcodes
        //battery
        body.put("barcode1", "BAT38");
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "CGR38");
        body.put("subPartType2", "CHARGER");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj,headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);
        LOGGER.info("Display Message : " + respObj.jsonPath().getString("data.failedList[0].errorMessage"));

    }

    @Test(priority = 0, description = "giving a false token")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016_invalid_token1() {
        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", "token");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "HDSH2SX344");
        body.put("deployedTo", "CvAAQK22515370023443");
        body.put("userIdTo", "1701224549");
        //subPartBarcodes
        //battery
        body.put("barcode1", "BAT38");
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "CGR38");
        body.put("subPartType2", "CHARGER");


        Response respObj = null;
        try {
            respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj,headers, body);
        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {

            int statusCode = respObj.getStatusCode();
            Assert.assertEquals(statusCode, 500);
        }

    }

    @Test(priority = 0, description = "giving an ivalid token")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017_invalid_token2() {
        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", Invalid_token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "HDSH2SX344");
        body.put("deployedTo", "CvAAQK22515370023443");
        body.put("userIdTo", "1701224549");
        //subPartBarcodes
        //battery
        body.put("barcode1", "BAT38");
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "CGR38");
        body.put("subPartType2", "CHARGER");

//        Response
//        Assert.assertEquals(respObj.statusCode(), 401);
        Response respObj = null;
        try {
            respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj,headers, body);
        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {

            int statusCode = respObj.getStatusCode();
            Assert.assertEquals(statusCode, 401);
        }

    }

    @Test(priority = 0, description = "missing headers")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_018_without_headers() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("accept","*/*");
//        headers.put("X-MW-TOKEN-EX",token);
//        headers.put("Accept-Language","en");
//        headers.put("Content-Type","application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "HDSH2SX344");
        body.put("deployedTo", "CvAAQK22515370023443");
        body.put("userIdTo", "1701224549");
        //subPartBarcodes
        //battery
        body.put("barcode1", "BAT38");
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "CGR38");
        body.put("subPartType2", "CHARGER");

//        Response
//        Assert.assertEquals(respObj.statusCode(), 401);
        Response respObj = null;
        try {
            respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj, headers, body);
        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {

            int statusCode = respObj.getStatusCode();
            Assert.assertEquals(statusCode, 401);
        }

    }
    @Test(priority = 0, description = "sub parts are not present in the request body")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_019_without_subPart_Barcodes() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept","*/*");
        headers.put("X-MW-TOKEN-EX",token);
        headers.put("Accept-Language","en");
        headers.put("Content-Type","application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "HDSH2SX344");
        body.put("deployedTo", "CvAAQK22515370023443");
        body.put("userIdTo", "1701224549");
        //subPartBarcodes
        //battery
        //body.put("barcode1", "BAT38");
        //body.put("subPartType1", "BATTERY");
        //charger
        //body.put("barcode2", "CGR38");
        //body.put("subPartType2", "CHARGER");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);
        LOGGER.info("Display Message : " + respObj.jsonPath().getString("data.failedList[0].errorMessage"));


    }

    @Test(priority = 0, description = "request body is not present")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_020_without_request_body() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept","*/*");
        headers.put("X-MW-TOKEN-EX",token);
        headers.put("Accept-Language","en");
        headers.put("Content-Type","application/json");


        Map<String, Object> body = new HashMap<String, Object>();
 /*       body.put("action", "DEPLOY");
        body.put("barcode", "HDSH2SX344");
        body.put("deployedTo", "CvAAQK22515370023443");
        body.put("userIdTo", "1701224549");
        //subPartBarcodes
        //battery
        body.put("barcode1", "BAT38");
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "CGR38");
        body.put("subPartType2", "CHARGER");

 */
        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj,headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);
        LOGGER.info("Display Message : " + respObj.jsonPath().getString("data.failedList[0].errorMessage"));

    }
    @Test(priority = 0, description = "receive_request_without_header")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_021_receive_request_without_headers() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("accept", "*/*");
//        headers.put("X-MW-TOKEN-EX", token);
//        headers.put("Accept-Language", "en");
//        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "RECEIVE");
        body.put("barcode", "HDSH2SX344");
        body.put("deployedTo", "CvAAQK22515370023443");
        body.put("userIdTo", "1701224549");
        //subPartBarcodes
        //battery
        body.put("barcode1", "BAT38");
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "CGR38");
        body.put("subPartType2", "CHARGER");

//        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(headers, body);
//        Assert.assertEquals(respObj.statusCode(), 200);
//        LOGGER.info("Display Message : " + respObj.jsonPath().getString("data.failedList[0].errorMessage"));
        Response respObj = null;
        try {
            respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj, headers, body);
        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {

            int statusCode = respObj.getStatusCode();
            Assert.assertEquals(statusCode, 401);
        }
    }


    @Test(priority = 0, description = "receive_request_without_token_header")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_022_receive_request_without_token() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
//        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "RECEIVE");
        body.put("barcode", "HDSH2SX344");
        body.put("deployedTo", "CvAAQK22515370023443");
        body.put("userIdTo", "1701224549");
        //subPartBarcodes
        //battery
        body.put("barcode1", "BAT38");
        body.put("subPartType1", "BATTERY");
        //charger
        body.put("barcode2", "CGR38");
        body.put("subPartType2", "CHARGER");

//        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(headers, body);
//        Assert.assertEquals(respObj.statusCode(), 200);
//        LOGGER.info("Display Message : " + respObj.jsonPath().getString("data.failedList[0].errorMessage"));
        Response respObj = null;
        try {
            respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj,headers, body);
        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {

            int statusCode = respObj.getStatusCode();
            Assert.assertEquals(statusCode, 401);
        }
    }

    @Test(priority = 0, description = "receive_request_without_subparts")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_023_receive_request_without_subparts() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "RECEIVE");
        body.put("barcode", "HDSH2SX344");
        body.put("deployedTo", "CvAAQK22515370023443");
        body.put("userIdTo", "1701224549");
//        //subPartBarcodes
//        //battery
//        body.put("barcode1", "BAT38");
//        body.put("subPartType1", "BATTERY");
//        //charger
//        body.put("barcode2", "CGR38");
//        body.put("subPartType2", "CHARGER");
        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj,headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);

    }

    @Test(priority = 0, description = "removing_the_merchantinfo")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_024_removing_the_merchantinfo() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("subpartassetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "HDSH2SX344");
        body.put("deployedTo", "");
        body.put("userIdTo", "");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj ,headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);
        LOGGER.info("Display Message : " + respObj.jsonPath().getString("data.failedList"));

    }

    @Test(priority = 0, description = "status  code of another request body")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_025_asset_update_request_body() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("assetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "BATTG11");
        body.put("state", "NOT_GOOD");
        body.put("subAction", "UNMAP");
        body.put("userIdTo", "1701224549");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj ,headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);
        LOGGER.info("Display Message : " + respObj.jsonPath().getString("data.failedList[0].errorMessage"));

    }

    @Test(priority = 0, description = "asset update for not-good state")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_026_update_for_not_good_state() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("assetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "BATTG11");
        body.put("state", "NOT_GOOD");
        body.put("subAction", "UNMAP");
        body.put("userIdTo", "1701224549");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj ,headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);
        LOGGER.info("Error Message : " + respObj.jsonPath().getString("data.failedList[0].errorMessage"));
        LOGGER.info("Error Message : " + respObj.jsonPath().getString("data.failedList[1].errorMessage"));

    }

    @Test(priority = 0, description = "asset update for state not mentioned")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_027_update_for_without_state() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("assetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "BATTG11");
        body.put("state", "GOOD");
        body.put("subAction", "UNMAP");
        body.put("userIdTo", "1701224549");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj ,headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);
        LOGGER.info("Error Message : " + respObj.jsonPath().getString("data.failedList[0].errorMessage"));
        LOGGER.info("Error Message : " + respObj.jsonPath().getString("data.failedList[1].errorMessage"));

    }
    @Test(priority = 0, description = "changing subaction from UNMAP to RECEIVE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_028_update_for_good_state() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("assetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "DEPLOY");
        body.put("barcode", "BATTG11");
        body.put("state", "");
        body.put("subAction", "RECEIVE");
        body.put("userIdTo", "1701224549");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj ,headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);


    }
    @Test(priority = 0, description = "CHANGING SUBACTION FROM UNMAP to DEPLOY AND ACTION FROM DEPLOT TO RECEIVE")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_029_asset_update_for_deployed() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("assetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
        headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "RECEIVE");
        body.put("barcode", "BATTG11");
        body.put("state", "GOOD");
        body.put("subAction", "DEPLOY");
        body.put("userIdTo", "1701224549");

        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj ,headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);


    }


    @Test(priority = 0, description = " REMOVING THE HEADERS")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_030_asset_update_header_removed() {

        Subpartassetupdate reqobj = new Subpartassetupdate(P.TESTDATA.get("assetupdateRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("accept", "*/*");
       // headers.put("X-MW-TOKEN-EX", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("action", "RECEIVE");
        body.put("barcode", "BATTG11");
        body.put("state", "GOOD");
        body.put("subAction", "DEPLOY");
        body.put("userIdTo", "1701224549");

//        Response respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj ,headers, body);
//        Assert.assertEquals(respObj.statusCode(), 400);
        Response respObj = null;
        try {
            respObj = MiddlewareServicesObject.subpartassetupdatetestmethod(reqobj,headers, body);
        } catch (PatternSyntaxException e) {

        }
        if (respObj != null) {

            int statusCode = respObj.getStatusCode();
            Assert.assertEquals(statusCode, 401);
        }


    }
}

