package ats.AssetTrackingController;

import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class AssetValidateTest {
    public static final String ISSUER = "ATS";
   	public static final String CLIENT_ID = "ats-bc";
    String custId="1001224519";
    @Test(priority = 1)
    public void OnboardedBarcodeValidationFor_Receive(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100777000000328");
        queryParams.put("action","RECEIVE");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 2)
    public void OnboardedBarcodeValidationFor_Assign(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100777000000328");
        queryParams.put("action","ASSIGN");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
     
        Map<String, String> body = new HashMap<String, String>();
       
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 3)
    public void OnboardedBarcodeValidationFor_Deploy(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100777000000328");
        queryParams.put("action","DEPLOY");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
     
        Map<String, String> body = new HashMap<String, String>();
       
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 4)
    public void OnboardedBarcodeValidationFor_FILE_UPLOAD(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100777000000328");
        queryParams.put("action","FILE_UPLOAD");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
     
        Map<String, String> body = new HashMap<String, String>();
       
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 5)
    public void OnboardedBarcodeValidationFor_NullAction(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100777000000328");
        queryParams.put("action","");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
     
        Map<String, String> body = new HashMap<String, String>();
       
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==500, "Testcase Failed");

    }
    @Test(priority = 6)
    public void AvailableBarcodeValidationFor_Receive(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","ANMNNH0968");
        queryParams.put("action","RECEIVE");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
     
        Map<String, String> body = new HashMap<String, String>();
       
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 7)
    public void AvailableBarcodeValidationFor_Assign(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100803000001509");
        queryParams.put("action","ASSIGN");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
     
        Map<String, String> body = new HashMap<String, String>();
       
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 8)
    public void AvailableBarcodeValidationFor_Deploy(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100803000001509");
        queryParams.put("action","ASSIGN");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
     
        Map<String, String> body = new HashMap<String, String>();
       
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
  

    
    @Test(priority = 9)
    public void AvailableBarcodeValidationFor_FILE_UPLOAD(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100803000001509");
        queryParams.put("action","ASSIGN");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
     
        Map<String, String> body = new HashMap<String, String>();
       
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 10)
    public void AssignedBarcodeValidationFor_Receive(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","ANMNNH0971");
        queryParams.put("action","RECEIVE");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 11)
    public void AssignedBarcodeValidationFor_Assign(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","ANMNNH0971");
        queryParams.put("action","ASSIGN");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 12)
    public void AssignedBarcodeValidationFor_Deploy(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","ANMNNH0971");
        queryParams.put("action","DEPLOY");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 13)
    public void AssignedBarcodeValidationFor_File_Uplaod(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","ANMNNH0971");
        queryParams.put("action","FILE_UPLOAD");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 14)
    public void PendingAckBarcodeValidationFor_Receive(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","ANMNNH0908");
        queryParams.put("action","RECEIVE");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 15)
    public void PendingAckBarcodeValidationFor_ASSIGN(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","ANMNNH0908");
        queryParams.put("action","ASSIGN");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 16)
    public void PendingAckBarcodeValidationFor_DEPLOY(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","ANMNNH0908");
        queryParams.put("action","DEPLOY");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 17)
    public void PendingAckBarcodeValidationFor_File_upload(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","ANMNNH0908");
        queryParams.put("action","FILE_UPLOAD");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 18)
    public void PendingAssignBarcodeValidationFor_Receive(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","ANMNNH0996");
        queryParams.put("action","RECEIVE");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 19)
    public void PendingAssignBarcodeValidationFor_Assign(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","ANMNNH0996");
        queryParams.put("action","ASSIGN");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 20)
    public void PendingAssignBarcodeValidationFor_Deploy(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","ANMNNH0996");
        queryParams.put("action","DEPLOY");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 21)
    public void PendingAssignBarcodeValidationFor_File_Upload(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","ANMNNH0996");
        queryParams.put("action","FILE_UPLOAD");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 22)
    public void DeployedBarcodeValidationFor_Receive(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100777000000343");
        queryParams.put("action","RECEIVE");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 23)
    public void DeployedBarcodeValidationFor_Assign(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100777000000343");
        queryParams.put("action","ASSIGN");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 24)
    public void DeployedBarcodeValidationFor_Deploy(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100777000000343");
        queryParams.put("action","DEPLOY");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 25)
    public void DeployedBarcodeValidationFor_File_upload(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100777000000343");
        queryParams.put("action","FILE_UPLOAD");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 26)
    public void UnmappedBarcodeValidationFor_Receive(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100777000000214");
        queryParams.put("action","RECEIVE");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 27)
    public void UnmappedBarcodeValidationFor_Assign(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100977000000021");
        queryParams.put("action","ASSIGN");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 28)
    public void UnmappedBarcodeValidationFor_Deploy(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100977000000021");
        queryParams.put("action","DEPLOY");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcases Failed");

    }
    @Test(priority = 29)
    public void UnmappedBarcodeValidationFor_DeployWithouAcceptHeader(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100977000000021");
        queryParams.put("action","DEPLOY");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcases Failed");

    }
    @Test(priority = 30)
    public void UnmappedBarcodeValidationFor_DeployWithoutAcceptLanguage(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode","AI100977000000021");
        queryParams.put("action","DEPLOY");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1AssetValidate(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcases Failed");

    }
    
}
