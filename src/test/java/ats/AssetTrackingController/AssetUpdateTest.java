package ats.AssetTrackingController;

import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

public class AssetUpdateTest {
	 public static final String ISSUER = "ATS";
	   	public static final String CLIENT_ID = "ats-bc";
	    String custId="1001224519";
	    int a;
	    Random rand = new Random();
	    @Test(priority = 1)
	    public void ReceiveBarcodeFromAvailableBucketOfUser() throws Exception{
	    	a=1;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	       
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "RECEIVE");
	        body.put("barcode", barcode1);

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
	    @SuppressWarnings("unchecked")
		@Test(priority = 2)
	    public void ReceiveBarcodeFromAvailableBucketOfUserWithoutAction() throws Exception{
	    	a=1;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
	    @Test(priority = 3)
	    public void ReceiveBarcodeFromAssignedBucketOfUser() throws Exception{
	    	a=2;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "RECEIVE");
	        body.put("barcode", barcode1);

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
	    @Test(priority = 4)
	    public void ReceiveBarcodeFromAssignedBucketOfUserWithoutAction() throws Exception{
	    	a=2;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
	    @Test(priority = 5)
	    public void ReceiveBarcodeFromDeployedBucketOfUser() {
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "RECEIVE");
	        body.put("barcode", "AAFFETEEE001");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
	    @Test(priority = 6)
	    public void ReceiveBarcodeFromDeployedBucketOfUserWithoutAction() {
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", "AAFFETEEE001");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
	    @Test(priority = 7)
	    public void ReceiveBarcodeFromUnmappedBucketOfUser() throws Exception {
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  a=3;
	    	  ArrayList<Object> arr= new ArrayList<Object>();
			    HashMap<String,Object> hss= new HashMap<String,Object>();
			    BaseMethod obj= new BaseMethod();
			    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
			    hss=(HashMap<String, Object>) arr.get(0);
			    String barcode1=(String) hss.get("barcode");
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "RECEIVE");
	        body.put("barcode", barcode1);
//	        body.put("state", "");
//	        body.put("subAction", "");
//	        body.put("userIdTo", "");
//
	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
	    @Test(priority = 8)
	    public void ReceiveBarcodeFromUnmappedBucketOfUserWithoutAction() throws Exception {
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  a=3;
	    	  ArrayList<Object> arr= new ArrayList<Object>();
			    HashMap<String,Object> hss= new HashMap<String,Object>();
			    BaseMethod obj= new BaseMethod();
			    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
			    hss=(HashMap<String, Object>) arr.get(0);
			    String barcode1=(String) hss.get("barcode");
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
	    @Test(priority = 9)
	    public void ReceiveBarcodeFromReturnBucketOfUser() throws Exception {
	    	a=4;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  
	    	  ArrayList<Object> arr= new ArrayList<Object>();
			    HashMap<String,Object> hss= new HashMap<String,Object>();
			    BaseMethod obj= new BaseMethod();
			    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
			    hss=(HashMap<String, Object>) arr.get(0);
			    String barcode1=(String) hss.get("barcode");
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "RECEIVE");
	        body.put("barcode", barcode1);
//	        body.put("state", "");
//	        body.put("subAction", "");
//	        body.put("userIdTo", "");
//
	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
	    @Test(priority = 10)
	    public void ReceiveBarcodeFromReturnBucketOfUserWithoutAction() throws Exception {
	    	a=4;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  
	    	  ArrayList<Object> arr= new ArrayList<Object>();
			    HashMap<String,Object> hss= new HashMap<String,Object>();
			    BaseMethod obj= new BaseMethod();
			    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
			    hss=(HashMap<String, Object>) arr.get(0);
			    String barcode1=(String) hss.get("barcode");
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", " ");
	        body.put("barcode", barcode1);
//	        body.put("state", "");
//	        body.put("subAction", "");
//	        body.put("userIdTo", "");
//
	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
	    @Test(priority = 11)
	    public void ReceiveBarcodeFromPendingAckBucketOfUser() throws Exception {
	    	a=5;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  
	    	  ArrayList<Object> arr= new ArrayList<Object>();
			    HashMap<String,Object> hss= new HashMap<String,Object>();
			    BaseMethod obj= new BaseMethod();
			    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
			    hss=(HashMap<String, Object>) arr.get(0);
			    String barcode1=(String) hss.get("barcode");
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "RECEIVE");
	        body.put("barcode", barcode1);
//	        body.put("state", "");
//	        body.put("subAction", "");
//	        body.put("userIdTo", "");
//
	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	       Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
	    @Test(priority = 12)
	    public void ReceiveBarcodeFromPendingAckBucketOfUserWithoutAction() throws Exception {
	    	a=5;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  
	    	  ArrayList<Object> arr= new ArrayList<Object>();
			    HashMap<String,Object> hss= new HashMap<String,Object>();
			    BaseMethod obj= new BaseMethod();
			    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
			    hss=(HashMap<String, Object>) arr.get(0);
			    String barcode1=(String) hss.get("barcode");
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);
//	        body.put("state", "");
//	        body.put("subAction", "");
//	        body.put("userIdTo", "");
//
	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
	    @Test(priority = 13)
	    public void ReceiveBarcodeFromPendingAssignBucketOfUser() throws Exception {
	    	a=6;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  
	    	  ArrayList<Object> arr= new ArrayList<Object>();
			    HashMap<String,Object> hss= new HashMap<String,Object>();
			    BaseMethod obj= new BaseMethod();
			    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
			    hss=(HashMap<String, Object>) arr.get(0);
			    String barcode1=(String) hss.get("barcode");
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "RECEIVE");
	        body.put("barcode", barcode1);
//	        body.put("state", "");
//	        body.put("subAction", "");
//	        body.put("userIdTo", "");
//
	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	       Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
	    @Test(priority = 14)
	    public void ReceiveBarcodeFromPendingAssignBucketOfUserWithoutAction() throws Exception {
	    	a=6;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  
	    	  ArrayList<Object> arr= new ArrayList<Object>();
			    HashMap<String,Object> hss= new HashMap<String,Object>();
			    BaseMethod obj= new BaseMethod();
			    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
			    hss=(HashMap<String, Object>) arr.get(0);
			    String barcode1=(String) hss.get("barcode");
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);
//	        body.put("state", "");
//	        body.put("subAction", "");
//	        body.put("userIdTo", "");
//
	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
	    @Test(priority = 15)
	    public void ReceiveBarcodeFromAvailableBucketOfUserWithoutAccept() throws Exception{
	    	a=1;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "RECEIVE");
	        body.put("barcode", barcode1);

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
	    @Test(priority = 16)
	    public void ReceiveBarcodeFromAvailableBucketOfUserWithoutAcceptLanguage() throws Exception{
	    	a=1;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "RECEIVE");
	        body.put("barcode", barcode1);

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
	    @Test(priority = 17)
	    public void AssignBarcodeFromAvailableBucketOfUser() throws Exception{
	    	a=1;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "ASSIGN");
	        body.put("barcode", barcode1);
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
	 
	    @Test(priority = 18)
	    public void AssignBarcodeFromAvailableBucketOfUserWithoutAction() throws Exception{
	    	a=1;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
	    
	    @Test(priority = 19)
	    public void AssignBarcodeFromAssignBucketOfUser() throws Exception{
	    	a=2;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "ASSIGN");
	        body.put("barcode", barcode1);
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));

}
	    
	    @Test(priority = 20)
	    public void AssignBarcodeFromAssignBucketOfUserWithoutAction() throws Exception{
	    	a=2;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");

}
	    @Test(priority = 21)
	    public void AssignBarcodeFromDeployedBucketOfUser() throws Exception{
	    	a=2;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "ASSIGN");
	        body.put("barcode", "AAFFETEEE001");
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));

}
	    @Test(priority = 22)
	    public void AssignBarcodeFromDeployedBucketOfUserWithoutAction() throws Exception{
	    	a=2;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", "AAFFETEEE001");
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");

}
	    
	    @Test(priority = 23)
	    public void AssignBarcodeFromUnmappedBucketOfUser() throws Exception{
	    	a=3;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "ASSIGN");
	        body.put("barcode", barcode1);
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));

}
	    @Test(priority = 24)
	    public void AssignBarcodeFromUnmappedBucketOfUserWithoutAction() throws Exception{
	    	a=3;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");

}
	    
	    @Test(priority = 25)
	    public void AssignBarcodeFromReturnedBucketOfUser() throws Exception{
	    	a=4;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "ASSIGN");
	        body.put("barcode", barcode1);
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));

}
	      
	    @Test(priority = 26)
	    public void AssignBarcodeFromReturnedBucketOfUserWithoutAction() throws Exception{
	    	a=4;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");

}
	    
	    @Test(priority = 27)
	    public void AssignBarcodeFromPending_AcknowledgementBucketOfUser() throws Exception{
	    	a=5;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "ASSIGN");
	        body.put("barcode", barcode1);
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));

}
	    
	    @Test(priority = 28)
	    public void AssignBarcodeFromPending_AcknowledgementBucketOfUserWithoutAssign() throws Exception{
	    	a=5;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");

}
	    
	    @Test(priority = 29)
	    public void AssignBarcodeFromPending_AssignBucketOfUser() throws Exception{
	    	a=6;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "ASSIGN");
	        body.put("barcode", barcode1);
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));

}
	    @Test(priority = 30)
	    public void AssignBarcodeFromPending_AssignBucketOfUserWithoutAction() throws Exception{
	    	a=6;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");

}
	    @Test(priority = 31)
	    public void DeployBarcodeFromAvailableBucketOfUser() throws Exception{
	    	a=1;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "DEPLOY");
	        body.put("barcode", barcode1);
	        body.put("deployedTo", "UFSRTED767");
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));

}    
	    @Test(priority = 32)
	    public void DeployBarcodeFromAvailableBucketOfUserWithoutAction() throws Exception{
	    	a=1;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);
	        body.put("deployedTo", "UFSRTED767");
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");

}    
	    @Test(priority = 33)
	    public void DeployBarcodeFromAvailableBucketOfUserWithoutUserIdto() throws Exception{
	    	a=1;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "DEPLOY");
	        body.put("barcode", barcode1);
	        body.put("deployedTo", "UFSRTED767");
	        body.put("userIdTo", "");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));

}
	    @Test(priority = 34)
	    public void DeployBarcodeFromAvailableBucketOfUserWithoutDeployedTo() throws Exception{
	    	a=1;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "DEPLOY");
	        body.put("barcode", barcode1);
	        body.put("deployedTo", "");
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));

}
	    @Test(priority = 35)
	    public void DeployBarcodeFromAvailableBucketOfUserWithOnlyBarcode() throws Exception{
	    	a=1;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);
	        body.put("deployedTo", "");
	        body.put("userIdTo", "");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");

}
	    @Test(priority = 36)
	    public void DeployBarcodeFromAssignedBucketOfUser() throws Exception{
	    	a=2;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "DEPLOY");
	        body.put("barcode", barcode1);
	        body.put("deployedTo", "USFSUSLK09");
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");
	        if(httpcode == 200)
	           Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
	        else 
		           Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));

}
	    @Test(priority = 37)
	    public void DeployBarcodeFromAssignedBucketOfUserWithoutAction() throws Exception{
	    	a=2;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);
	        body.put("deployedTo", "USFSUSLK09");
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");

}
	    @Test(priority = 38)
	    public void DeployBarcodeFromAssignedBucketOfUserWithoutDeployedTo() throws Exception{
	    	a=2;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "DEPLOY");
	        body.put("barcode", barcode1);
	        body.put("deployedTo", "");
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");
	        if(httpcode == 200)
	           Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
	        else 
		           Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));

}
	    @Test(priority = 39)
	    public void DeployBarcodeFromAssignedBucketOfUserWithoutUserIdTo() throws Exception{
	    	a=2;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "DEPLOY");
	        body.put("barcode", barcode1);
	        body.put("deployedTo", "USFSUSLK09");
	        body.put("userIdTo", "");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");
	        if(httpcode == 200)
	           Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
	        else 
		           Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));

}
	    @Test(priority = 40)
	    public void DeployBarcodeFromAssignedBucketOfUserWithOnlyBarcode() throws Exception{
	    	a=2;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	ArrayList<Object> arr= new ArrayList<Object>();
		    HashMap<String,Object> hss= new HashMap<String,Object>();
		    BaseMethod obj= new BaseMethod();
		    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
		    hss=(HashMap<String, Object>) arr.get(0);
		    String barcode1=(String) hss.get("barcode");	
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);
	        body.put("deployedTo", "");
	        body.put("userIdTo", "");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");

}
	    @Test(priority = 41)
	    public void DeployBarcodeFromDeployedBucketOfUser() {
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "DEPLOY");
	        body.put("barcode", "AAFFETEEE001");
	        body.put("deployedTo", "USFSUSLK09");
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
	    @Test(priority = 42)
	    public void DeployBarcodeFromDeployedBucketOfUserWithoutAction() {
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", "AAFFETEEE001");
	        body.put("deployedTo", "USFSUSLK09");
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
	    @Test(priority = 43)
	    public void DeployBarcodeFromDeployedBucketOfUserWithoutDeployedTo() {
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "DEPLOY");
	        body.put("barcode", "AAFFETEEE001");
	        body.put("deployedTo", "");
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
	    @Test(priority = 44)
	    public void DeployBarcodeFromDeployedBucketOfUserWithoutUserIdTo() {
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "DEPLOY");
	        body.put("barcode", "AAFFETEEE001");
	        body.put("deployedTo", "USFSUSLK09");
	        body.put("userIdTo", "");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
	    @Test(priority = 45)
	    public void DeployBarcodeFromDeployedBucketOfUserWithOnlyBarcode() {
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", "AAFFETEEE001");
	        body.put("deployedTo", "");
	        body.put("userIdTo", "");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
	    @Test(priority = 46)
	    public void DeployBarcodeFromUnmappedBucketOfUser() throws Exception {
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  a=3;
	    	  ArrayList<Object> arr= new ArrayList<Object>();
			    HashMap<String,Object> hss= new HashMap<String,Object>();
			    BaseMethod obj= new BaseMethod();
			    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
			    hss=(HashMap<String, Object>) arr.get(0);
			    String barcode1=(String) hss.get("barcode");
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "DEPLOY");
	        body.put("barcode", barcode1);
	        body.put("deployedTo", "USFSUSLK09");
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
	    @Test(priority = 47)
	    public void DeployBarcodeFromUnmappedBucketOfUserWithoutAction() throws Exception {
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  a=3;
	    	  ArrayList<Object> arr= new ArrayList<Object>();
			    HashMap<String,Object> hss= new HashMap<String,Object>();
			    BaseMethod obj= new BaseMethod();
			    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
			    hss=(HashMap<String, Object>) arr.get(0);
			    String barcode1=(String) hss.get("barcode");
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);
	        body.put("deployedTo", "USFSUSLK09");
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
	    @Test(priority = 48)
	    public void DeployBarcodeFromUnmappedBucketOfUserWithoutDeployedTo() throws Exception {
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  a=3;
	    	  ArrayList<Object> arr= new ArrayList<Object>();
			    HashMap<String,Object> hss= new HashMap<String,Object>();
			    BaseMethod obj= new BaseMethod();
			    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
			    hss=(HashMap<String, Object>) arr.get(0);
			    String barcode1=(String) hss.get("barcode");
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "DEPLOY");
	        body.put("barcode", barcode1);
	        body.put("deployedTo", "");
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
	    @Test(priority = 49)
	    public void DeployBarcodeFromUnmappedBucketOfUserWithoutUserIdTo() throws Exception {
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  a=3;
	    	  ArrayList<Object> arr= new ArrayList<Object>();
			    HashMap<String,Object> hss= new HashMap<String,Object>();
			    BaseMethod obj= new BaseMethod();
			    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
			    hss=(HashMap<String, Object>) arr.get(0);
			    String barcode1=(String) hss.get("barcode");
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "DEPLOY");
	        body.put("barcode", barcode1);
	        body.put("deployedTo", "USFSUSLK09");
	        body.put("userIdTo", "");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
	    @Test(priority = 50)
	    public void DeployBarcodeFromUnmappedBucketOfUserWithOnlyBarcode() throws Exception {
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  a=3;
	    	  ArrayList<Object> arr= new ArrayList<Object>();
			    HashMap<String,Object> hss= new HashMap<String,Object>();
			    BaseMethod obj= new BaseMethod();
			    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
			    hss=(HashMap<String, Object>) arr.get(0);
			    String barcode1=(String) hss.get("barcode");
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "");
	        body.put("barcode", barcode1);
	        body.put("deployedTo", "");
	        body.put("userIdTo", "");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
	    @Test(priority = 51)
	    public void DeployBarcodeFromReturnBucketOfUser() throws Exception {
	    	a=4;
	    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	    	  
	    	  ArrayList<Object> arr= new ArrayList<Object>();
			    HashMap<String,Object> hss= new HashMap<String,Object>();
			    BaseMethod obj= new BaseMethod();
			    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
			    hss=(HashMap<String, Object>) arr.get(0);
			    String barcode1=(String) hss.get("barcode");
	        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
	        Map<String, String> queryParams = new HashMap<String, String>();
	        Map<String, String> headers = new HashMap<String, String>();
	        headers.put("jwt", token);
	        headers.put("accept", "*/*");
	        headers.put("Accept-Language", "en");
	       
	        Map<String, String> body = new HashMap<String, String>();
	        body.put("action", "DEPLOY");
	        body.put("barcode", barcode1);
	        body.put("deployedTo", "USFSUSLK09");
	        body.put("userIdTo", "1001361880");

	        Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
	        int httpcode = UpDResponse.getStatusCode();
	        Assert.assertTrue(httpcode == 200 || httpcode == 400 , "Testcase Failed");
	        if(httpcode == 400)
		           Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
		        else 
			           Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));}
	    
	 
@Test(priority = 52)
public void DeployBarcodeFromReturnBucketOfUserWithoutAction() throws Exception {
	a=4;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	  
	  ArrayList<Object> arr= new ArrayList<Object>();
	    HashMap<String,Object> hss= new HashMap<String,Object>();
	    BaseMethod obj= new BaseMethod();
	    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
	    hss=(HashMap<String, Object>) arr.get(0);
	    String barcode1=(String) hss.get("barcode");
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "");
    body.put("barcode", barcode1);
    body.put("deployedTo", "USFSUSLK09");
    body.put("userIdTo", "1001361880");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode == 400 , "Testcase Failed");
}
@Test(priority = 53)
public void DeployBarcodeFromReturnBucketOfUserWithoutDeployTo() throws Exception {
	a=4;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	  
	  ArrayList<Object> arr= new ArrayList<Object>();
	    HashMap<String,Object> hss= new HashMap<String,Object>();
	    BaseMethod obj= new BaseMethod();
	    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
	    hss=(HashMap<String, Object>) arr.get(0);
	    String barcode1=(String) hss.get("barcode");
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "DEPLOY");
    body.put("barcode", barcode1);
    body.put("deployedTo", "");
    body.put("userIdTo", "1001361880");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode == 400 , "Testcase Failed");


}
@Test(priority = 54)
public void DeployBarcodeFromReturnBucketOfUserWithoutUserIdTo() throws Exception {
	a=4;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	  
	  ArrayList<Object> arr= new ArrayList<Object>();
	    HashMap<String,Object> hss= new HashMap<String,Object>();
	    BaseMethod obj= new BaseMethod();
	    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
	    hss=(HashMap<String, Object>) arr.get(0);
	    String barcode1=(String) hss.get("barcode");
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "DEPLOY");
    body.put("barcode", barcode1);
    body.put("deployedTo", "USFSUSLK09");
    body.put("userIdTo", "");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode == 400 , "Testcase Failed");
    if(httpcode == 400)
           Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
        else 
	           Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
    }
@Test(priority = 55)
public void DeployBarcodeFromReturnBucketOfUserWithOnlyBarcode() throws Exception {
	a=4;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	  
	  ArrayList<Object> arr= new ArrayList<Object>();
	    HashMap<String,Object> hss= new HashMap<String,Object>();
	    BaseMethod obj= new BaseMethod();
	    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
	    hss=(HashMap<String, Object>) arr.get(0);
	    String barcode1=(String) hss.get("barcode");
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "");
    body.put("barcode", barcode1);
    body.put("deployedTo", "");
    body.put("userIdTo", "");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode == 400 , "Testcase Failed");

}
@Test(priority = 56)
public void DeployBarcodeFromPendingAckBucketOfUser() throws Exception {
	a=5;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	  
	  ArrayList<Object> arr= new ArrayList<Object>();
	    HashMap<String,Object> hss= new HashMap<String,Object>();
	    BaseMethod obj= new BaseMethod();
	    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
	    hss=(HashMap<String, Object>) arr.get(0);
	    String barcode1=(String) hss.get("barcode");
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "DEPLOY");
    body.put("barcode", barcode1);
    body.put("deployedTo", "USFSUSLK09");
    body.put("userIdTo", "1001361880");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
   Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
@Test(priority = 57)
public void DeployBarcodeFromPendingAckBucketOfUserWithoutAction() throws Exception {
	a=5;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	  
	  ArrayList<Object> arr= new ArrayList<Object>();
	    HashMap<String,Object> hss= new HashMap<String,Object>();
	    BaseMethod obj= new BaseMethod();
	    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
	    hss=(HashMap<String, Object>) arr.get(0);
	    String barcode1=(String) hss.get("barcode");
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "");
    body.put("barcode", barcode1);
    body.put("deployedTo", "USFSUSLK09");
    body.put("userIdTo", "1001361880");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
@Test(priority = 58)
public void DeployBarcodeFromPendingAckBucketOfUserWithoutDeployedTo() throws Exception {
	a=5;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	  
	  ArrayList<Object> arr= new ArrayList<Object>();
	    HashMap<String,Object> hss= new HashMap<String,Object>();
	    BaseMethod obj= new BaseMethod();
	    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
	    hss=(HashMap<String, Object>) arr.get(0);
	    String barcode1=(String) hss.get("barcode");
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "DEPLOY");
    body.put("barcode", barcode1);
    body.put("deployedTo", "");
    body.put("userIdTo", "1001361880");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
   Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
@Test(priority = 59)
public void DeployBarcodeFromPendingAckBucketOfUserWithoutUserIdTo() throws Exception {
	a=5;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	  
	  ArrayList<Object> arr= new ArrayList<Object>();
	    HashMap<String,Object> hss= new HashMap<String,Object>();
	    BaseMethod obj= new BaseMethod();
	    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
	    hss=(HashMap<String, Object>) arr.get(0);
	    String barcode1=(String) hss.get("barcode");
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "DEPLOY");
    body.put("barcode", barcode1);
    body.put("deployedTo", "USFSUSLK09");
    body.put("userIdTo", "");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
   Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
@Test(priority = 60)
public void DeployBarcodeFromPendingAckBucketOfUserWithOnlyBarcode() throws Exception {
	a=5;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	  
	  ArrayList<Object> arr= new ArrayList<Object>();
	    HashMap<String,Object> hss= new HashMap<String,Object>();
	    BaseMethod obj= new BaseMethod();
	    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
	    hss=(HashMap<String, Object>) arr.get(0);
	    String barcode1=(String) hss.get("barcode");
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "");
    body.put("barcode", barcode1);
    body.put("deployedTo", "");
    body.put("userIdTo", "");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
@Test(priority = 61)
public void UnmapBarcodeFromAvailableBucketOfUserWithoutBarcode() throws Exception{
	a=1;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", "");
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
    Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
@Test(priority = 62)
public void UnmapBarcodeFromAvailableBucketOfUserWithBarcode() throws Exception{
	a=1;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
    Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
@Test(priority = 63)
public void UnmapBarcodeFromAvailableBucketOfUserWithStateGood() throws Exception{
	a=1;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
    Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
@Test(priority = 64)
public void UnmapBarcodeFromAvailableBucketOfUserWithStateNotGood() throws Exception{
	a=1;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "NOT_GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
    Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
@Test(priority = 65)
public void UnmapBarcodeFromAvailableBucketOfUserWithoutState() throws Exception{
	a=1;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
@Test(priority = 66)
public void UnmapBarcodeFromAvailableBucketOfUserWithInvalidState() throws Exception{
	a=1;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "XYZ");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
@Test(priority = 67)
public void UnmapBarcodeFromAvailableBucketOfUserWithInvalidSubAction() throws Exception{
	a=1;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "AGSU");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
@Test(priority = 68)
public void UnmapBarcodeFromAvailableBucketOfUserWithoutSubAction() throws Exception{
	a=1;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
@Test(priority = 69)
public void UnmapBarcodeFromAvailableBucketOfUserWithInvalidUserIdTo() throws Exception{
	a=1;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1234567890");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
}
@Test(priority = 70)
public void UnmapBarcodeWithInvalidbarcode() throws Exception{
	a=1;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", "AGGSUVSV333");
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
}
@Test(priority = 71)
public void UnmapBarcodeFromAvailableBucketOfUserWithoutUserIdTo() throws Exception{
	a=1;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
}
//Assigned
@Test(priority = 72)
public void UnmapBarcodeFromAssignedBucketOfUserWithoutBarcode() throws Exception{
	a=2;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", "");
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 73)
public void UnmapBarcodeFromAssignedBucketOfUserWithBarcode() throws Exception{
	a=2;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 74)
public void UnmapBarcodeFromAssignedBucketOfUserWithStateGood() throws Exception{
	a=2;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 75)
public void UnmapBarcodeFromAssignedBucketOfUserWithStateNotGood() throws Exception{
	a=2;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "NOT_GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
    Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
@Test(priority = 76)
public void UnmapBarcodeFromAssignedBucketOfUserWithoutState() throws Exception{
	a=2;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 77)
public void UnmapBarcodeFromAssignedBucketOfUserWithInvalidState() throws Exception{
	a=2;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "XYZ");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 78)
public void UnmapBarcodeFromAssignedBucketOfUserWithInvalidSubAction() throws Exception{
	a=2;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "AGSU");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 79)
public void UnmapBarcodeFromAssignedBucketOfUserWithoutSubAction() throws Exception{
	a=2;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
@Test(priority = 80)
public void UnmapBarcodeFromAssignedBucketOfUserWithInvalidUserIdTo() throws Exception{
	a=2;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1234567890");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}

   
@Test(priority = 81)
public void UnmapBarcodeFromAssignedBucketOfUserWithoutUserIdTo() throws Exception{
	a=2;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
}
//Unmapped Bucket
@Test(priority = 82)
public void UnmapBarcodeFromUnmappedBucketOfUserWithoutBarcode() throws Exception{
	a=3;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", "");
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 83)
public void UnmapBarcodeFromUnmappedBucketOfUserWithBarcode() throws Exception{
	a=3;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 84)
public void UnmapBarcodeFromUnmappedBucketOfUserWithStateGood() throws Exception{
	a=3;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 85)
public void UnmapBarcodeFromUnmappedBucketOfUserWithStateNotGood() throws Exception{
	a=3;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "NOT_GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
    Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
@Test(priority = 86)
public void UnmapBarcodeFromUnmappedBucketOfUserWithoutState() throws Exception{
	a=3;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 87)
public void UnmapBarcodeFromUnmappedBucketOfUserWithInvalidState() throws Exception{
	a=3;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "XYZ");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 88)
public void UnmapBarcodeFromUnmappedBucketOfUserWithInvalidSubAction() throws Exception{
	a=3;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "AGSU");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 89)
public void UnmapBarcodeFromUnmappedBucketOfUserWithoutSubAction() throws Exception{
	a=3;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
@Test(priority = 90)
public void UnmapBarcodeFromUnmappedBucketOfUserWithInvalidUserIdTo() throws Exception{
	a=3;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1234567890");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}

   
@Test(priority = 91)
public void UnmapBarcodeFromUnmappedBucketOfUserWithoutUserIdTo() throws Exception{
	a=3;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
}
//Deployed Bucket
@Test(priority = 92)
public void UnmapBarcodeFromDeployedBucketOfUserWithoutBarcode() throws Exception{
	a=7;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", "");
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 93)
public void UnmapBarcodeFromDeployedBucketOfUserWithBarcode() throws Exception{
	a=7;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 94)
public void UnmapBarcodeFromDeployedBucketOfUserWithStateGood() throws Exception{
	a=7;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 95)
public void UnmapBarcodeFromDeployedBucketOfUserWithStateNotGood() throws Exception{
	a=7;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "NOT_GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
    Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
@Test(priority = 96)
public void UnmapBarcodeFromDeployedBucketOfUserWithoutState() throws Exception{
	a=7;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 97)
public void UnmapBarcodeFromDeployedBucketOfUserWithInvalidState() throws Exception{
	a=7;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "XYZ");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 98)
public void UnmapBarcodeFromDrployedBucketOfUserWithInvalidSubAction() throws Exception{
	a=7;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "AGSU");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 99)
public void UnmapBarcodeFromDeployedBucketOfUserWithoutSubAction() throws Exception{
	a=7;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
@Test(priority = 100)
public void UnmapBarcodeFromDeployedBucketOfUserWithInvalidUserIdTo() throws Exception{
	a=7;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1234567890");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}

   
@Test(priority = 101)
public void UnmapBarcodeFromDeployedBucketOfUserWithoutUserIdTo() throws Exception{
	a=3;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
}
// Returned bucket
@Test(priority =102)
public void UnmapBarcodeFromReturnedBucketOfUserWithoutBarcode() throws Exception{
	a=4;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", "");
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 103)
public void UnmapBarcodeFromReturnedBucketOfUserWithBarcode() throws Exception{
	a=4;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 104)
public void UnmapBarcodeFromReturnedBucketOfUserWithStateGood() throws Exception{
	a=4;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 105)
public void UnmapBarcodeFromReturnedBucketOfUserWithStateNotGood() throws Exception{
	a=4;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "NOT_GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
    Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
@Test(priority = 106)
public void UnmapBarcodeFromReturnedBucketOfUserWithoutState() throws Exception{
	a=4;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 107)
public void UnmapBarcodeFromReturnedBucketOfUserWithInvalidState() throws Exception{
	a=4;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "XYZ");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 108)
public void UnmapBarcodeFromReturnedBucketOfUserWithInvalidSubAction() throws Exception{
	a=4;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "AGSU");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 109)
public void UnmapBarcodeFromReturnedBucketOfUserWithoutSubAction() throws Exception{
	a=4;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
@Test(priority = 110)
public void UnmapBarcodeFromReturnedBucketOfUserWithInvalidUserIdTo() throws Exception{
	a=4;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1234567890");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}

   
@Test(priority = 111)
public void UnmapBarcodeFromReturnedBucketOfUserWithoutUserIdTo() throws Exception{
	a=4;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
}
//Pending Ack bucket
@Test(priority =112)
public void UnmapBarcodeFromPendingAckBucketOfUserWithoutBarcode() throws Exception{
	a=5;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", "");
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 113)
public void UnmapBarcodeFromPendingAckBucketOfUserWithBarcode() throws Exception{
	a=5;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 114)
public void UnmapBarcodeFromPendingAckBucketOfUserWithStateGood() throws Exception{
	a=5;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 115)
public void UnmapBarcodeFromPendingAckBucketOfUserWithStateNotGood() throws Exception{
	a=5;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "NOT_GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
    Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
@Test(priority = 116)
public void UnmapBarcodeFromPendingAckBucketOfUserWithoutState() throws Exception{
	a=5;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 117)
public void UnmapBarcodeFromPendingAckBucketOfUserWithInvalidState() throws Exception{
	a=5;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "XYZ");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 118)
public void UnmapBarcodeFromPendingAckBucketOfUserWithInvalidSubAction() throws Exception{
	a=5;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "AGSU");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 119)
public void UnmapBarcodeFromPendingAckBucketOfUserWithoutSubAction() throws Exception{
	a=5;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
@Test(priority = 120)
public void UnmapBarcodeFromPendingAckBucketOfUserWithInvalidUserIdTo() throws Exception{
	a=5;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1234567890");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}

   
@Test(priority = 121)
public void UnmapBarcodeFromPendingAckBucketOfUserWithoutUserIdTo() throws Exception{
	a=5;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
}
//Pending Assign Bucket
@Test(priority =122)
public void UnmapBarcodeFromPendingAssignBucketOfUserWithoutBarcode() throws Exception{
	a=6;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", "");
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 123)
public void UnmapBarcodeFromPendingAssignBucketOfUserWithBarcode() throws Exception{
	a=6;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 124)
public void UnmapBarcodeFromPendingAssignBucketOfUserWithStateGood() throws Exception{
	a=6;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 125)
public void UnmapBarcodeFromPendingAssignBucketOfUserWithStateNotGood() throws Exception{
	a=6;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "NOT_GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
    Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
@Test(priority = 126)
public void UnmapBarcodeFromPendingAssignBucketOfUserWithoutState() throws Exception{
	a=6;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 127)
public void UnmapBarcodeFromPendingAssignBucketOfUserWithInvalidState() throws Exception{
	a=6;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "XYZ");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 128)
public void UnmapBarcodeFromPendingAssignBucketOfUserWithInvalidSubAction() throws Exception{
	a=6;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "AGSU");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}
@Test(priority = 129)
public void UnmapBarcodeFromPendingAssignBucketOfUserWithoutSubAction() throws Exception{
	a=6;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "");
    body.put("userIdTo", "1001160345");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 400, "Testcase Failed");
}
@Test(priority = 130)
public void UnmapBarcodeFromPendingAssignBucketOfUserWithInvalidUserIdTo() throws Exception{
	a=6;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "1234567890");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200 || httpcode==400, "Testcase Failed");
}

   
@Test(priority = 131)
public void UnmapBarcodeFromPendingAssignBucketOfUserWithoutUserIdTo() throws Exception{
	a=6;
	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
    HashMap<String,Object> hss= new HashMap<String,Object>();
    BaseMethod obj= new BaseMethod();
    arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
    hss=(HashMap<String, Object>) arr.get(0);
    String barcode1=(String) hss.get("barcode");	
    String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
   
    Map<String, String> queryParams = new HashMap<String, String>();
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("jwt", token);
    headers.put("accept", "*/*");
    headers.put("Accept-Language", "en");
   
    Map<String, String> body = new HashMap<String, String>();
    body.put("action", "RECEIVE");
    body.put("barcode", barcode1);
    body.put("state", "GOOD");
    body.put("subAction", "UNMAP");
    body.put("userIdTo", "");

    Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
    int httpcode = UpDResponse.getStatusCode();
    Assert.assertTrue(httpcode == 200, "Testcase Failed");
}
//Dispatch Asset Available Bucket
@Test(priority = 132)
public void DispatchBarcodeFromAvailableBucketOfUser() throws Exception{
	a=1;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==400) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
if(httpcode==200) {
	   Assert.assertEquals("SUCCESS",UpDResponse.jsonPath().get("data.successList[0].status"));
}
}
@Test(priority = 133)
public void DispatchBarcodeFromAvailableBucketOfUserWithBarcodeNull() throws Exception{
	a=1;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", "");
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 134)
public void DispatchBarcodeFromAvailableBucketOfUserWithAssetCountNull() throws Exception{
	a=1;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 135)
public void DispatchBarcodeFromAvailableBucketOfUserWithActionNull() throws Exception{
	a=1;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 136)
public void DispatchBarcodeFromAvailableBucketOfUserWithWrongAction() throws Exception{
	a=1;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "AGSYFSY");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 137)
public void DispatchBarcodeFromAvailableBucketOfUserWithBoxCountNull() throws Exception{
	a=1;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 138)
public void DispatchBarcodeFromAvailableBucketOfUserWithCourierNameNull() throws Exception{
	a=1;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 139)
public void DispatchBarcodeFromAvailableBucketOfUserWithPhoneNoNull() throws Exception{
	a=1;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 140)
public void DispatchBarcodeFromAvailableBucketOfUserWithWrongSubAction() throws Exception{
	a=1;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "YY(&TGG");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 141)
public void DispatchBarcodeFromAvailableBucketOfUserWithNullTrackingId() throws Exception{
	a=1;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", "");

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 142)
public void DispatchBarcodeFromAvailableBucketOfUserWithInvalidPhoneNo() throws Exception{
	a=1;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "7646282635");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 143)
public void DispatchBarcodeFromAvailableBucketOfUserWithInvalidPhoneNo_HavingAphabets() throws Exception{
	a=1;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "IIYYFIUOYUUG");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
//Dispatch Asset Assigned Bucket
@Test(priority = 144)
public void DispatchBarcodeFromAssignedBucketOfUser() throws Exception{
	a=2;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", barcode1);
 body.put("assetCount", "1");
 body.put("barcode", "ANMNNH0344");
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 145)
public void DispatchBarcodeFromAssignedBucketOfUserWithBarcodeNull() throws Exception{
	a=2;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", "");
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 146)
public void DispatchBarcodeFromAssignedBucketOfUserWithAssetCountNull() throws Exception{
	a=2;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 147)
public void DispatchBarcodeFromAssignedBucketOfUserWithActionNull() throws Exception{
	a=2;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 148)
public void DispatchBarcodeFromAssignedBucketOfUserWithWrongAction() throws Exception{
	a=2;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "AGSYFSY");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 149)
public void DispatchBarcodeFromAssignedBucketOfUserWithBoxCountNull() throws Exception{
	a=2;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 150)
public void DispatchBarcodeFromAssignedBucketOfUserWithCourierNameNull() throws Exception{
	a=2;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 151)
public void DispatchBarcodeFromAssignedBucketOfUserWithPhoneNoNull() throws Exception{
	a=2;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 152)
public void DispatchBarcodeFromAssignedBucketOfUserWithWrongSubAction() throws Exception{
	a=2;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "YY(&TGG");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 153)
public void DispatchBarcodeFromAssignedBucketOfUserWithNullTrackingId() throws Exception{
	a=2;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", "");

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 154)
public void DispatchBarcodeFromAssignedBucketOfUserWithInvalidPhoneNo() throws Exception{
	a=2;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "7646282635");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 155)
public void DispatchBarcodeFromAssignedBucketOfUserWithInvalidPhoneNo_HavingAphabets() throws Exception{
	a=2;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "IIYYFIUOYUUG");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
//Dispatch Asset for Unmapped Bucket
@Test(priority = 156)
public void DispatchBarcodeFromdBucketOfUser() throws Exception{
	a=3;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==400) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 157)
public void DispatchBarcodeFromUnmappedBucketOfUserWithBarcodeNull() throws Exception{
	a=3;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", "");
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 158)
public void DispatchBarcodeFromUnmappedBucketOfUserWithAssetCountNull() throws Exception{
	a=3;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 159)
public void DispatchBarcodeFromUnmappedBucketOfUserWithActionNull() throws Exception{
	a=3;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 160)
public void DispatchBarcodeFromUnmappedBucketOfUserWithWrongAction() throws Exception{
	a=3;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "AGSYFSY");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 161)
public void DispatchBarcodeFromUnmappedBucketOfUserWithBoxCountNull() throws Exception{
	a=3;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 162)
public void DispatchBarcodeFromUnmappedBucketOfUserWithCourierNameNull() throws Exception{
	a=3;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 163)
public void DispatchBarcodeFromUnmappedBucketOfUserWithPhoneNo_Null() throws Exception{
	a=3;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 164)
public void DispatchBarcodeFromUnmappedBucketOfUserWithWrongSubAction() throws Exception{
	a=3;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "YY(&TGG");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 165)
public void DispatchBarcodeFromUnmappedBucketOfUserWithNullTrackingId() throws Exception{
	a=3;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "9716954395");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", "");

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 166)
public void DispatchBarcodeFromUnmappedBucketOfUserWithInvalidPhoneNo() throws Exception{
	a=3;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "7646282635");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 167)
public void DispatchBarcodeFromUnmappedBucketOfUserWithInvalidPhoneNo_HavingAphabets() throws Exception{
	a=3;
 MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
 HashMap<String,Object> hss= new HashMap<String,Object>();
 BaseMethod obj= new BaseMethod();
 arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
 hss=(HashMap<String, Object>) arr.get(0);
 String barcode1=(String) hss.get("barcode");	
 String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
 String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
 Map<String, String> queryParams = new HashMap<String, String>();
 Map<String, String> headers = new HashMap<String, String>();
 headers.put("jwt", token);
 headers.put("accept", "*/*");
 headers.put("Accept-Language", "en");

 Map<String, String> body = new HashMap<String, String>();
 body.put("action", "ASSIGN");
 body.put("assetCount", "1");
 body.put("barcode", barcode1);
 body.put("boxCountSent", "1");
 body.put("courierName", "Blue Dart Express");
 body.put("phoneNumberTo", "IIYYFIUOYUUG");
 body.put("subAction", "DISPATCH");
 body.put("trackingId", Tid);

 Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
 int httpcode = UpDResponse.getStatusCode();
 Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
//Dispatch Asset for Returned Bucket
@Test(priority = 168)
public void DispatchBarcodeFromReturnedBucketOfUser() throws Exception{
	a=4;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==400) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 169)
public void DispatchBarcodeFromReturnedBucketOfUserWithBarcodeNull() throws Exception{
	a=4;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", "");
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}

@Test(priority = 170)
public void DispatchBarcodeFromReturnedBucketOfUserWithAssetCountNull() throws Exception{
	a=4;
  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
  HashMap<String,Object> hss= new HashMap<String,Object>();
  BaseMethod obj= new BaseMethod();
  arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
  hss=(HashMap<String, Object>) arr.get(0);
  String barcode1=(String) hss.get("barcode");	
  String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
  String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
  Map<String, String> queryParams = new HashMap<String, String>();
  Map<String, String> headers = new HashMap<String, String>();
  headers.put("jwt", token);
  headers.put("accept", "*/*");
  headers.put("Accept-Language", "en");
 
  Map<String, String> body = new HashMap<String, String>();
  body.put("action", "ASSIGN");
  body.put("assetCount", "");
  body.put("barcode", barcode1);
  body.put("boxCountSent", "1");
  body.put("courierName", "Blue Dart Express");
  body.put("phoneNumberTo", "9716954395");
  body.put("subAction", "DISPATCH");
  body.put("trackingId", Tid);

  Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
  int httpcode = UpDResponse.getStatusCode();
  Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
 if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
 }
}
@Test(priority = 172)
public void DispatchBarcodeFromReturnedBucketOfUserWithWrongAction() throws Exception{
	a=4;
  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
  HashMap<String,Object> hss= new HashMap<String,Object>();
  BaseMethod obj= new BaseMethod();
  arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
  hss=(HashMap<String, Object>) arr.get(0);
  String barcode1=(String) hss.get("barcode");	
  String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
  String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
  Map<String, String> queryParams = new HashMap<String, String>();
  Map<String, String> headers = new HashMap<String, String>();
  headers.put("jwt", token);
  headers.put("accept", "*/*");
  headers.put("Accept-Language", "en");
 
  Map<String, String> body = new HashMap<String, String>();
  body.put("action", "AGSYFSY");
  body.put("assetCount", "1");
  body.put("barcode", barcode1);
  body.put("boxCountSent", "1");
  body.put("courierName", "Blue Dart Express");
  body.put("phoneNumberTo", "9716954395");
  body.put("subAction", "DISPATCH");
  body.put("trackingId", Tid);

  Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
  int httpcode = UpDResponse.getStatusCode();
  Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
 if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
 }
}
@Test(priority = 173)
public void DispatchBarcodeFromReturnedBucketOfUserWithBoxCountNull() throws Exception{
	a=4;
  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
  HashMap<String,Object> hss= new HashMap<String,Object>();
  BaseMethod obj= new BaseMethod();
  arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
  hss=(HashMap<String, Object>) arr.get(0);
  String barcode1=(String) hss.get("barcode");	
  String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
  String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
  Map<String, String> queryParams = new HashMap<String, String>();
  Map<String, String> headers = new HashMap<String, String>();
  headers.put("jwt", token);
  headers.put("accept", "*/*");
  headers.put("Accept-Language", "en");
 
  Map<String, String> body = new HashMap<String, String>();
  body.put("action", "ASSIGN");
  body.put("assetCount", "1");
  body.put("barcode", barcode1);
  body.put("boxCountSent", "");
  body.put("courierName", "Blue Dart Express");
  body.put("phoneNumberTo", "9716954395");
  body.put("subAction", "DISPATCH");
  body.put("trackingId", Tid);

  Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
  int httpcode = UpDResponse.getStatusCode();
  Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
 if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
 }
}
@Test(priority = 174)
public void DispatchBarcodeFromReturnedBucketOfUserWithCourierNameNull() throws Exception{
	a=4;
  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
  HashMap<String,Object> hss= new HashMap<String,Object>();
  BaseMethod obj= new BaseMethod();
  arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
  hss=(HashMap<String, Object>) arr.get(0);
  String barcode1=(String) hss.get("barcode");	
  String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
  String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
  Map<String, String> queryParams = new HashMap<String, String>();
  Map<String, String> headers = new HashMap<String, String>();
  headers.put("jwt", token);
  headers.put("accept", "*/*");
  headers.put("Accept-Language", "en");
 
  Map<String, String> body = new HashMap<String, String>();
  body.put("action", "ASSIGN");
  body.put("assetCount", "1");
  body.put("barcode", barcode1);
  body.put("boxCountSent", "1");
  body.put("courierName", "");
  body.put("phoneNumberTo", "9716954395");
  body.put("subAction", "DISPATCH");
  body.put("trackingId", Tid);

  Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
  int httpcode = UpDResponse.getStatusCode();
  Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
 if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
 }
}
@Test(priority = 175)
public void DispatchBarcodeFromReturnedBucketOfUserWithPhoneNo_Null() throws Exception{
	a=4;
  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
  HashMap<String,Object> hss= new HashMap<String,Object>();
  BaseMethod obj= new BaseMethod();
  arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
  hss=(HashMap<String, Object>) arr.get(0);
  String barcode1=(String) hss.get("barcode");	
  String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
  String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
  Map<String, String> queryParams = new HashMap<String, String>();
  Map<String, String> headers = new HashMap<String, String>();
  headers.put("jwt", token);
  headers.put("accept", "*/*");
  headers.put("Accept-Language", "en");
 
  Map<String, String> body = new HashMap<String, String>();
  body.put("action", "ASSIGN");
  body.put("assetCount", "1");
  body.put("barcode", barcode1);
  body.put("boxCountSent", "1");
  body.put("courierName", "Blue Dart Express");
  body.put("phoneNumberTo", "");
  body.put("subAction", "DISPATCH");
  body.put("trackingId", Tid);

  Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
  int httpcode = UpDResponse.getStatusCode();
  Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
 if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
 }
}
@Test(priority = 176)
public void DispatchBarcodeFromReturnedBucketOfUserWithWrongSubAction() throws Exception{
	a=4;
  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
  HashMap<String,Object> hss= new HashMap<String,Object>();
  BaseMethod obj= new BaseMethod();
  arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
  hss=(HashMap<String, Object>) arr.get(0);
  String barcode1=(String) hss.get("barcode");	
  String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
  String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
  Map<String, String> queryParams = new HashMap<String, String>();
  Map<String, String> headers = new HashMap<String, String>();
  headers.put("jwt", token);
  headers.put("accept", "*/*");
  headers.put("Accept-Language", "en");
 
  Map<String, String> body = new HashMap<String, String>();
  body.put("action", "ASSIGN");
  body.put("assetCount", "1");
  body.put("barcode", barcode1);
  body.put("boxCountSent", "1");
  body.put("courierName", "Blue Dart Express");
  body.put("phoneNumberTo", "9716954395");
  body.put("subAction", "YY(&TGG");
  body.put("trackingId", Tid);

  Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
  int httpcode = UpDResponse.getStatusCode();
  Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
 if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
 }
}
@Test(priority = 177)
public void DispatchBarcodeFromReturnedBucketOfUserWithNullTrackingId() throws Exception{
	a=4;
  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
  HashMap<String,Object> hss= new HashMap<String,Object>();
  BaseMethod obj= new BaseMethod();
  arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
  hss=(HashMap<String, Object>) arr.get(0);
  String barcode1=(String) hss.get("barcode");	
  String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
  String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
  Map<String, String> queryParams = new HashMap<String, String>();
  Map<String, String> headers = new HashMap<String, String>();
  headers.put("jwt", token);
  headers.put("accept", "*/*");
  headers.put("Accept-Language", "en");
 
  Map<String, String> body = new HashMap<String, String>();
  body.put("action", "ASSIGN");
  body.put("assetCount", "1");
  body.put("barcode", barcode1);
  body.put("boxCountSent", "1");
  body.put("courierName", "Blue Dart Express");
  body.put("phoneNumberTo", "9716954395");
  body.put("subAction", "DISPATCH");
  body.put("trackingId", "");

  Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
  int httpcode = UpDResponse.getStatusCode();
  Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
 if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
 }
}
@Test(priority = 178)
public void DispatchBarcodeFromReturnedBucketOfUserWithInvalidPhoneNo() throws Exception{
	a=4;
  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
  HashMap<String,Object> hss= new HashMap<String,Object>();
  BaseMethod obj= new BaseMethod();
  arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
  hss=(HashMap<String, Object>) arr.get(0);
  String barcode1=(String) hss.get("barcode");	
  String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
  String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
  Map<String, String> queryParams = new HashMap<String, String>();
  Map<String, String> headers = new HashMap<String, String>();
  headers.put("jwt", token);
  headers.put("accept", "*/*");
  headers.put("Accept-Language", "en");
 
  Map<String, String> body = new HashMap<String, String>();
  body.put("action", "ASSIGN");
  body.put("assetCount", "1");
  body.put("barcode", barcode1);
  body.put("boxCountSent", "1");
  body.put("courierName", "Blue Dart Express");
  body.put("phoneNumberTo", "7646282635");
  body.put("subAction", "DISPATCH");
  body.put("trackingId", Tid);

  Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
  int httpcode = UpDResponse.getStatusCode();
  Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
 if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
 }
}
@Test(priority = 179)
public void DispatchBarcodeFromReturnedBucketOfUserWithInvalidPhoneNo_HavingAphabets() throws Exception{
	a=4;
  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
  HashMap<String,Object> hss= new HashMap<String,Object>();
  BaseMethod obj= new BaseMethod();
  arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
  hss=(HashMap<String, Object>) arr.get(0);
  String barcode1=(String) hss.get("barcode");	
  String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
  String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
  Map<String, String> queryParams = new HashMap<String, String>();
  Map<String, String> headers = new HashMap<String, String>();
  headers.put("jwt", token);
  headers.put("accept", "*/*");
  headers.put("Accept-Language", "en");
 
  Map<String, String> body = new HashMap<String, String>();
  body.put("action", "ASSIGN");
  body.put("assetCount", "1");
  body.put("barcode", barcode1);
  body.put("boxCountSent", "1");
  body.put("courierName", "Blue Dart Express");
  body.put("phoneNumberTo", "IIYYFIUOYUUG");
  body.put("subAction", "DISPATCH");
  body.put("trackingId", Tid);

  Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
  int httpcode = UpDResponse.getStatusCode();
  Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
 if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
 }
}
//Dispatch Asset for Pending Ack. Bucket
@Test(priority = 180)
public void DispatchBarcodeFromPendingAck_BucketOfUser() throws Exception{
	a=5;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==400) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 181)
public void DispatchBarcodeFromPendingAck_BucketOfUserWithBarcodeNull() throws Exception{
	a=5;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", "");
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 182)
public void DispatchBarcodeFromPendingAck_BucketOfUserWithAssetCountNull() throws Exception{
	a=5;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 183)
public void DispatchBarcodeFromPendingAck_BucketOfUserWithActionNull() throws Exception{
	a=5;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 184)
public void DispatchBarcodeFromPendingAck_BucketOfUserWithWrongAction() throws Exception{
	a=5;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "AGSYFSY");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 185)
public void DispatchBarcodeFromPendingAck_BucketOfUserWithBoxCountNull() throws Exception{
	a=5;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 186)
public void DispatchBarcodeFromPendingAck_BucketOfUserWithCourierNameNull() throws Exception{
	a=5;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 187)
public void DispatchBarcodeFromPendingAck_BucketOfUserWithPhoneNo_Null() throws Exception{
	a=5;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 188)
public void DispatchBarcodeFromPendingAck_BucketOfUserWithWrongSubAction() throws Exception{
	a=5;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "YY(&TGG");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 189)
public void DispatchBarcodeFromPendingAck_BucketOfUserWithNullTrackingId() throws Exception{
	a=5;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", "");

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 190)
public void DispatchBarcodeFromPendingAck_BucketOfUserWithInvalidPhoneNo() throws Exception{
	a=5;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "7646282635");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 191)
public void DispatchBarcodeFromPendingAck_BucketOfUserWithInvalidPhoneNo_HavingAphabets() throws Exception{
	a=5;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "IIYYFIUOYUUG");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
//Dispatch Asset for Pending Assign Bucket
@Test(priority = 192)
public void DispatchBarcodeFromPendingAssign_BucketOfUser() throws Exception{
	a=6;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==400) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 193)
public void DispatchBarcodeFromPendingAssign_BucketOfUserWithBarcodeNull() throws Exception{
	a=6;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", "");
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 194)
public void DispatchBarcodeFromPendingAssign_BucketOfUserWithAssetCountNull() throws Exception{
	a=6;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 195)
public void DispatchBarcodeFromPendingAssign_BucketOfUserWithActionNull() throws Exception{
	a=6;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 196)
public void DispatchBarcodeFromPendingAssign_BucketOfUserWithWrongAction() throws Exception{
	a=6;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "AGSYFSY");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 197)
public void DispatchBarcodeFromPendingAssign_BucketOfUserWithBoxCountNull() throws Exception{
	a=6;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 198)
public void DispatchBarcodeFromPendingAssign_BucketOfUserWithCourierNameNull() throws Exception{
	a=6;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 199)
public void DispatchBarcodeFromPendingAssign_BucketOfUserWithPhoneNo_Null() throws Exception{
	a=6;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 200)
public void DispatchBarcodeFromPendingAssign_BucketOfUserWithWrongSubAction() throws Exception{
	a=6;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "YY(&TGG");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 201)
public void DispatchBarcodeFromPendingAssign_BucketOfUserWithNullTrackingId() throws Exception{
	a=6;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "9716954395");
body.put("subAction", "DISPATCH");
body.put("trackingId", "");

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority = 202)
public void DispatchBarcodeFromPendingAssign_BucketOfUserWithInvalidPhoneNo() throws Exception{
	a=6;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "7646282635");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
@Test(priority =203)
public void DispatchBarcodeFromPendingAssign_BucketOfUserWithInvalidPhoneNo_HavingAphabets() throws Exception{
	a=6;
MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	ArrayList<Object> arr= new ArrayList<Object>();
HashMap<String,Object> hss= new HashMap<String,Object>();
BaseMethod obj= new BaseMethod();
arr=obj.getListOfAssetDetails(a).jsonPath().get("data.barcodes");
hss=(HashMap<String, Object>) arr.get(0);
String barcode1=(String) hss.get("barcode");	
String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
String Tid="TXNIDAWB"+String.valueOf(rand.nextInt(10000000));
Map<String, String> queryParams = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
headers.put("jwt", token);
headers.put("accept", "*/*");
headers.put("Accept-Language", "en");

Map<String, String> body = new HashMap<String, String>();
body.put("action", "ASSIGN");
body.put("assetCount", "1");
body.put("barcode", barcode1);
body.put("boxCountSent", "1");
body.put("courierName", "Blue Dart Express");
body.put("phoneNumberTo", "IIYYFIUOYUUG");
body.put("subAction", "DISPATCH");
body.put("trackingId", Tid);

Response UpDResponse = MiddlewareServicesObject.v1AssetUpdate(queryParams, headers, body);
int httpcode = UpDResponse.getStatusCode();
Assert.assertTrue(httpcode == 200 | httpcode == 400, "Testcase Failed");
if(httpcode==200) {
	   Assert.assertEquals("FAILURE",UpDResponse.jsonPath().get("data.failedList[0].status"));
}
}
}