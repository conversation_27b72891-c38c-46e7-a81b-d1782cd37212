package ats.UserController;

import Services.MechantService.MiddlewareServices;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class UserControllerTest {
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    //  LeadRequest LeadRequestRequestObject = new LeadRequest();
    // Utilities UtilitiesObject = new Utilities();


    public static final String ISSUER = "ATS";
   	public static final String CLIENT_ID = "ats-bc";
       String custId="1001224519";

    @Test(priority = 1)
    public void genrateQRCode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("token", "538d0bdf-b30c-4e0d-ac28-5f0ab960fc4f");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generate_QRCODE_Response = MiddlewareServicesObject.v1GenerateQRCode(queryParams, headers, body);
        int httpcode = generate_QRCODE_Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
        if(httpcode ==400)
            Assert.assertEquals(generate_QRCODE_Response.getBody().asString().contains("QR Code is invalid or expired"),true,"Testcase failed");

    }

    @Test(priority = 2)
    public void getUserDetailsWithOnlyMobileNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "9560526665");
        queryParams.put("custId", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generate_QRCODE_Response = MiddlewareServicesObject.v1GenerateQRCode(queryParams, headers, body);
        int httpcode = generate_QRCODE_Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");

    }
    @Test(priority = 3)
    public void getUserDetailsWithNotRegisterNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "5763574848");
        queryParams.put("custId", "");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generate_QRCODE_Response = MiddlewareServicesObject.v1GenerateQRCode(queryParams, headers, body);
        int httpcode = generate_QRCODE_Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
        if(httpcode ==400)
            Assert.assertEquals(generate_QRCODE_Response.getBody().asString().contains("Phone Number not registered"),true,"Testcase failed");

    }
    @Test(priority = 4)
    public void getUserDetailsWithIncorrectCustID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "");
        queryParams.put("custId", "5");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generate_QRCODE_Response = MiddlewareServicesObject.v1GenerateQRCode(queryParams, headers, body);
        int httpcode = generate_QRCODE_Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
        if(httpcode ==400)
            Assert.assertEquals(generate_QRCODE_Response.getBody().asString().contains("Customer Id not registered with Oauth"),true,"Testcase failed");

    }
    @Test(priority = 5)
    public void getUserDetailsWithoutMobileAndCustID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "");
        queryParams.put("custId", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();

        Response generate_QRCODE_Response = MiddlewareServicesObject.v1GenerateQRCode(queryParams, headers, body);
        int httpcode = generate_QRCODE_Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
       // Assert.assertEquals(generate_QRCODE_Response.getBody().asString().contains("PhoneNumber or UserId both not provided"),true,"Testcase failed");
    }
    @Test(priority =6)
    public void validateQRCode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("token", "538d0bdf-b30c-4e0d-ac28-5f0ab960fc4f");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response veirfied_QRCODE_Response = MiddlewareServicesObject.v1VerifiedQRCode(queryParams, headers, body);
        int httpcode = veirfied_QRCODE_Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
        if(httpcode ==400)
            Assert.assertEquals(veirfied_QRCODE_Response.getBody().asString().contains("QR Code is invalid or expired"),true,"Testcase failed");

    }
    @Test(priority = 7)
    public void getUserDetailsWithOnlyCustID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "");
        queryParams.put("custId", "1106992015");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response getUSerDetails_Response = MiddlewareServicesObject.v1GetUSerDetails(queryParams, headers, body);
        int httpcode = getUSerDetails_Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");

    }
    @Test(priority =8)
    public void getUserDetailsWithvalidMobileAndCustID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "7838602638");
        queryParams.put("custId", "1106992015");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response getUSerDetails_Response = MiddlewareServicesObject.v1GetUSerDetails(queryParams, headers, body);
        int httpcode = getUSerDetails_Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");

    }
    @Test(priority =9)
    public void getUserDetailsWithUnmatchedMobileAndCustID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "9871671365"); // Phone number is primary attributes
        queryParams.put("custId", "1106992015");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response getUSerDetails_Response = MiddlewareServicesObject.v1GetUSerDetails(queryParams, headers, body);
        int httpcode = getUSerDetails_Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");

    }
}
