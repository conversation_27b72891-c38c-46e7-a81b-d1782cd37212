package ats.Supplier;

import Services.MechantService.MiddlewareServices;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

public class SupplierTest {
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    //  LeadRequest LeadRequestRequestObject = new LeadRequest();
    // Utilities UtilitiesObject = new Utilities();

    public static final String ISSUER = "ATS";
   	public static final String CLIENT_ID = "ats-bc";
       String custId="1001224519";
    Random rand = new Random();
    String rand_userId = "test"+String.valueOf(rand.nextInt(10000));

    @Test(priority = 1)
    public void createSupplierWithAllMandatoryAttributesSourceIndia() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("city", "Noida");
        body.put("country", "INDIA");
        body.put("line1", "14A");
        body.put("line2", "Main Road");
        body.put("line3", "TechCentre");
        body.put("pincode", "201301");
        body.put("state", "Uttar Pradesh");
        body.put("name", supplierName);
        body.put("source", "INDIA");

        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierRequest(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 2)
    public void createSupplierWithoutPincodeAttributesSourceIndia() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("city", "Noida");
        body.put("country", "INDIA");
        body.put("line1", "14A");
        body.put("line2", "Main Road");
        body.put("line3", "TechCentre");
        body.put("pincode", "");
        body.put("state", "Uttar Pradesh");
        body.put("name", supplierName);
        body.put("source", "INDIA");

        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierRequest(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
        Assert.assertEquals(supplierResponse.getBody().asString().contains("pincode is mandatory when country is India"),true,"Testcase failed");
    }
    @Test(priority = 3)
    public void createSupplierWithoutStateAttributesSourceIndia() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("city", "Noida");
        body.put("country", "INDIA");
        body.put("line1", "14A");
        body.put("line2", "Main Road");
        body.put("line3", "TechCentre");
        body.put("pincode", "201301");
        body.put("state", "");
        body.put("name", supplierName);
        body.put("source", "INDIA");

        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierRequest(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
        Assert.assertEquals(supplierResponse.getBody().asString().contains("APPROVED"),true,"Testcase failed");
    }
    @Test(priority = 4)
    public void createSupplierWithoutCityAttributesSourceIndia() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("city", "");
        body.put("country", "INDIA");
        body.put("line1", "14A");
        body.put("line2", "Main Road");
        body.put("line3", "TechCentre");
        body.put("pincode", "201301");
        body.put("state", "Uttar Pradesh");
        body.put("name", supplierName);
        body.put("source", "INDIA");

        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierRequest(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
        Assert.assertEquals(supplierResponse.getBody().asString().contains("APPROVED"),true,"Testcase failed");
    }
    @Test(priority = 5)
    public void createSupplierWithoutCountryAttributesSourceIndia() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("city", "Noida");
        body.put("country", "");
        body.put("line1", "14A");
        body.put("line2", "Main Road");
        body.put("line3", "TechCentre");
        body.put("pincode", "201301");
        body.put("state", "Uttar Pradesh");
        body.put("name", supplierName);
        body.put("source", "INDIA");

        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierRequest(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
        Assert.assertEquals(!(supplierResponse.getBody().asString()).contains("APPROVED"),true,"Testcase failed");
    }
    @Test(priority = 6)
    public void createSupplierWithoutCountryAttributesSourceOther() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("name", supplierName);
        body.put("source", "OTHER");
        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierRequest(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
        Assert.assertEquals(supplierResponse.getBody().asString().contains("APPROVED"),true,"Testcase failed");
    }
    @Test(priority = 7)
    public void createSupplierWithoutSource() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("name", supplierName);
        body.put("source", "");
        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierRequest(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
        Assert.assertEquals(!(supplierResponse.getBody().asString()).contains("APPROVED"),true,"Testcase failed");
    }
    @Test(priority = 8)
    public void createSupplierNameIsAlreadyExist() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("name", supplierName);
        body.put("source", "OTHER");
        Response supplierResponse1 = MiddlewareServicesObject.v1ATSSupplierRequest(queryParams, headers, body);
        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierRequest(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
        Assert.assertEquals(supplierResponse.getBody().asString().contains("Supplier already exists with this name"),true,"Testcase failed");
    }
    @Test(priority = 9)
    public void createSupplierWithoutOptionalFieldsSourceIndia() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("city", "Noida");
        body.put("country", "INDIA");
        body.put("line1", "");
        body.put("line2", "");
        body.put("line3", "");
        body.put("pincode", "201301");
        body.put("state", "Uttar Pradesh");
        body.put("name", supplierName);
        body.put("source", "INDIA");

        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierRequest(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
        Assert.assertEquals(supplierResponse.getBody().asString().contains("APPROVED"),true,"Testcase failed");
    }
    @Test(priority = 10)
    public void searchSupplierWithoutParams() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierFetchDetails(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        Assert.assertEquals(supplierResponse.getBody().asString().contains("SUCCESS"),true,"Testcase failed");
    }
    @Test(priority = 11)
    public void searchSupplierWithId() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("id", "2");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierFetchDetails(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        Assert.assertEquals(supplierResponse.getBody().asString().contains("SUCCESS"),true,"Testcase failed");
    }
    @Test(priority = 12)
    public void searchSupplierWithName() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("name", "test");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierFetchDetails(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        Assert.assertEquals(supplierResponse.getBody().asString().contains("SUCCESS"),true,"Testcase failed");
    }
    @Test(priority = 13)
    public void searchSupplierWithPageNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("pageNumber", "1");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierFetchDetails(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        Assert.assertEquals(supplierResponse.getBody().asString().contains("SUCCESS"),true,"Testcase failed");
    }
    @Test(priority = 14)
    public void searchSupplierWithPageSize() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("pageSize", "5");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierFetchDetails(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        Assert.assertEquals(supplierResponse.getBody().asString().contains("SUCCESS"),true,"Testcase failed");
    }
    @Test(priority = 15)
    public void searchSupplierWithExactName() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("name", "test");
        queryParams.put("exactMatch", "true");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierFetchDetails(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        Assert.assertEquals(supplierResponse.getBody().asString().contains("SUCCESS"),true,"Testcase failed");
    }
    @Test(priority = 16)
    public void searchSupplierWithoutExactMatch() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("name", "test");
        queryParams.put("exactMatch", "false");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierFetchDetails(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        Assert.assertEquals(supplierResponse.getBody().asString().contains("SUCCESS"),true,"Testcase failed");
    }
    @Test(priority = 17)
    public void searchSupplierWithIncorrectName() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("name", "2345234532");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierFetchDetails(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        Assert.assertEquals(supplierResponse.getBody().asString().contains("No supplier for given search criteria"),true,"Testcase failed");
    }
    @Test(priority = 18)
    public void searchSupplierWithIncorrectId() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("id", "453234234334");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierFetchDetails(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        Assert.assertEquals(supplierResponse.getBody().asString().contains("No supplier for given search criteria"),true,"Testcase failed");
    }
    @Test(priority = 19)
    public void searchSupplierWithIncorrectPageNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String supplierName = "test"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("pageNumber", "45323");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response supplierResponse = MiddlewareServicesObject.v1ATSSupplierFetchDetails(queryParams, headers, body);
        int httpcode = supplierResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        Assert.assertEquals(supplierResponse.getBody().asString().contains("No supplier for given search criteria"),true,"Testcase failed");
    }

}
