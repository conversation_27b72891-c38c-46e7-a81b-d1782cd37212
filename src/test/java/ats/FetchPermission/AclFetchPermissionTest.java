package ats.FetchPermission;

import Services.MechantService.MiddlewareServices;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class AclFetchPermissionTest {
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    

    public static final String ISSUER = "ATS";
   	public static final String CLIENT_ID = "ats-bc";
       String custId="1001224519";

    @Test(priority = 1)
    public void FetchPermissionsFor_OCL_FSE() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchPermissionResponse = MiddlewareServicesObject.v1ACLFetchPermission(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 2)
    public void FetchPermissionsFor_OCL_WAREHOUSE() {
    	custId="1001700992";
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchPermissionResponse = MiddlewareServicesObject.v1ACLFetchPermission(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 3)
    public void FetchPermissionsFor_OCL_ADMIN() {
    	custId="1001596512";
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchPermissionResponse = MiddlewareServicesObject.v1ACLFetchPermission(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 4)
    public void FetchPermissionsFor_PPBL_ATS_BC() {
    	custId="1002036543";
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchPermissionResponse = MiddlewareServicesObject.v1ACLFetchPermission(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 5)
    public void FetchPermissionsFor_OCL_PSA() {
    	custId="1001749365";
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchPermissionResponse = MiddlewareServicesObject.v1ACLFetchPermission(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 6)
    public void FetchPermissionsFor_EXTERNAL() {
    	custId="1001750853";
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchPermissionResponse = MiddlewareServicesObject.v1ACLFetchPermission(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 7)
    public void FetchPermissionsFor_IMT() {
    	custId="1001669554";
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchPermissionResponse = MiddlewareServicesObject.v1ACLFetchPermission(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 8)
    public void FetchPermissionsFor_PPBL_ADMIN() {
    	custId="1001700819";
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchPermissionResponse = MiddlewareServicesObject.v1ACLFetchPermission(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 9)
    public void FetchPermissionsFor_PPBL_WAREHOUSE() {
    	custId="1001699832";
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchPermissionResponse = MiddlewareServicesObject.v1ACLFetchPermission(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 10)
    public void FetchPermissionsFor_PPBL_CITYSPOC() {
    	custId="1001700821";
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchPermissionResponse = MiddlewareServicesObject.v1ACLFetchPermission(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    
    @Test(priority = 11)
    public void FetchPermissionsFor_PPBL_BC_FSE() {
    	custId="1000110590";
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchPermissionResponse = MiddlewareServicesObject.v1ACLFetchPermission(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 12)
    public void FetchPermissionsFor_PPBL_BC() {
    	custId="1107208547";
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchPermissionResponse = MiddlewareServicesObject.v1ACLFetchPermission(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 13)
    public void FetchPermissionsFor_OCL_CITYSPOC() {
    	custId="1001699831";
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchPermissionResponse = MiddlewareServicesObject.v1ACLFetchPermission(queryParams, headers, body);
        int httpcode = fetchPermissionResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
}
