package ats.Barcode;

import Services.MechantService.MiddlewareServices;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FetchBarcodeDetailsTest {
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    //  LeadRequest LeadRequestRequestObject = new LeadRequest();
    // Utilities UtilitiesObject = new Utilities();
    public static final String ISSUER = "ATS";
   	public static final String CLIENT_ID = "ats-bc";
       String custId="1106992015";

    String timeStamp = "";
    String agent_session_token = "";
    String applicant_mobile_number = "5123123070";

    @Test(priority = 1)
    public void fetchBarcodeDetails() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "FAST0002");
        queryParams.put("fetchStrategy", "all");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 2)
    public void fetchBarcodeDetailsWithoutBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "");
        queryParams.put("fetchStrategy", "all");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
        Assert.assertEquals(fetchBarcodeDetailsV1Response.getBody().asString().contains("either invalid or rejected"),true,"Testcase failed");
    }
    @Test(priority = 3)
    public void fetchBarcodeDetailsWithIncorrectBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "garre");
        queryParams.put("fetchStrategy", "all");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue( httpcode == 400, "Testcase Failed");
        Assert.assertEquals(fetchBarcodeDetailsV1Response.getBody().asString().contains("invalid or rejected"),true,"Testcase failed");
    }
    @Test(priority = 4)
    public void fetchBarcodeDetailsWithoutFetchStrategy() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "FAST0002");
        queryParams.put("fetchStrategy", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");
        
    }
    @Test(priority = 5)
    public void fetchBarcodeDetailsWithInvalidFetchStrategy() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "FAST0002");
        queryParams.put("fetchStrategy", "test");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 6)
    public void fetchBarcodeDetailsCanAssign() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "FAST0002");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsCanAssignV1Response = MiddlewareServicesObject.v1FetchBarcodeDetailsCanAssign(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsCanAssignV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 7)
    public void fetchBarcodeDetailsCanAssignWithoutBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsCanAssignV1Response = MiddlewareServicesObject.v1FetchBarcodeDetailsCanAssign(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsCanAssignV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");
       // Assert.assertEquals(fetchBarcodeDetailsCanAssignV1Response.getBody().asString().contains("either invalid or rejected"),true,"Testcase failed");
    }
    @Test(priority = 8)
    public void fetchBarcodeDetailsCanAssignWithIncorrectBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "garre");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsCanAssignV1Response = MiddlewareServicesObject.v1FetchBarcodeDetailsCanAssign(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsCanAssignV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue( httpcode == 200 || httpcode == 400, "Testcase Failed");
       // Assert.assertEquals(fetchBarcodeDetailsCanAssignV1Response.getBody().asString().contains("invalid or rejected"),true,"Testcase failed");
    }
    @Test(priority = 9)
    public void v2FetchBarcodeDetails() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "FAST0002");
        queryParams.put("fetchStrategy", "all");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV2Response = MiddlewareServicesObject.v2FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV2Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 10)
    public void v2FetchBarcodeDetailsWithoutBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "");
        queryParams.put("fetchStrategy", "all");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV2Response = MiddlewareServicesObject.v2FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV2Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
        Assert.assertEquals(fetchBarcodeDetailsV2Response.getBody().asString().contains("either invalid or rejected"),true,"Testcase failed");
    }
    @Test(priority = 11)
    public void v2FetchBarcodeDetailsWithIncorrectBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "garre");
        queryParams.put("fetchStrategy", "all");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV2Response = MiddlewareServicesObject.v2FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV2Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue( httpcode == 200, "Testcase Failed");
        Assert.assertEquals(fetchBarcodeDetailsV2Response.getBody().asString().contains("invalid or rejected"),true,"Testcase failed");
    }
    @Test(priority = 12)
    public void v2FetchBarcodeDetailsWithoutFetchStrategy() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "FAST0002");
        queryParams.put("fetchStrategy", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV2Response = MiddlewareServicesObject.v2FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV2Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");

    }
    @Test(priority = 13)
    public void v2FetchBarcodeDetailsWithInvalidFetchStrategy() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "FAST0002");
        queryParams.put("fetchStrategy", "test");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV2Response = MiddlewareServicesObject.v2FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV2Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 14)
    public void downloadBarcodeDetails() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "FAST0002");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response downloadBarcodeDetailsResponse = MiddlewareServicesObject.v1DownloadBarcodeDetails(queryParams, headers, body);
        int httpcode = downloadBarcodeDetailsResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 15)
    public void downloadBarcodeDetailsWithoutBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response downloadBarcodeDetailsResponse = MiddlewareServicesObject.v1DownloadBarcodeDetails(queryParams, headers, body);
        int httpcode = downloadBarcodeDetailsResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue( httpcode == 400, "Testcase Failed");
        Assert.assertEquals(downloadBarcodeDetailsResponse.getBody().asString().contains("either invalid or rejected"),true,"Testcase failed");
    }
    @Test(priority = 16)
    public void downloadBarcodeDetailsWithIncorrectBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "garre");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response downloadBarcodeDetailsResponse = MiddlewareServicesObject.v1DownloadBarcodeDetails(queryParams, headers, body);
        int httpcode = downloadBarcodeDetailsResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(  httpcode == 400, "Testcase Failed");
        Assert.assertEquals(downloadBarcodeDetailsResponse.getBody().asString().contains("invalid or rejected"),true,"Testcase failed");
    }
    @Test(priority = 17)
    public void fetchBarcodeDetailsWithStrategyBarcodeDetails() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "FAST0002");
        queryParams.put("fetchStrategy", "barcodeDetails");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 18)
    public void fetchBarcodeDetailsWithStrategyLinkedBarcodes() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AI100777000000154");
        queryParams.put("fetchStrategy", "linkedBarcodes");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 19)
    public void fetchBarcodeDetailsWithStrategyBarcodeSkuDetails() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AI100777000000154");
        queryParams.put("fetchStrategy", "barcodeSkuDetails");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 20)
    public void fetchBarcodeDetailsWithoutBarcodeAndFetchStrategy() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "");
        queryParams.put("fetchStrategy", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 21)
    public void fetchBarcodeDetailsWithoutAccept() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AI100777000000154");
        queryParams.put("fetchStrategy", "all");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 22)
    public void fetchBarcodeDetailsWithoutAcceptLanguage() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AI100777000000154");
        queryParams.put("fetchStrategy", "all");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 23)
    public void fetchBarcodeDetailsWithoutContentype() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AI100777000000154");
        queryParams.put("fetchStrategy", "barcodeSkuDetails");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 24)
    public void fetchBarcodeDetailsWithTwoStrategies() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AI100777000000154");
        queryParams.put("fetchStrategy", "barcodeSkuDetails,all");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 25)
    public void fetchBarcodeDetailsOfChildBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AS100776000000004");
        queryParams.put("fetchStrategy", "all");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 26)
    public void fetchBarcodeDetailsOfChildBarcodeFetchStrategyBarcodeSkuDetails() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AS100776000000004");
        queryParams.put("fetchStrategy", "barcodeSkuDetails");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 27)
    public void fetchBarcodeDetailsOfChildBarcodeFetchStrategyLinkedBarcodes() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AS100776000000004");
        queryParams.put("fetchStrategy", "linkedBarcodes");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 28)
    public void fetchBarcodeDetailsOfMasterBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AM100777000000008");
        queryParams.put("fetchStrategy", "all");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 29)
    public void fetchBarcodeDetailsOfMasterBarcodeWithStrategyLinkedBarcodes() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AM100777000000008");
        queryParams.put("fetchStrategy", "linkedBarcodes");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 30)
    public void fetchBarcodeDetailsOfMasterBarcodeWithStrategyBarcodeSkuDetails() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AM100777000000008");
        queryParams.put("fetchStrategy", "barcodeSkuDetails");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    @Test(priority = 31)
    public void fetchBarcodeDetailsOfMasterBarcodeWithStrategy() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("barcode", "AM100777000000008");
        queryParams.put("fetchStrategy", "barcodeDetails");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response fetchBarcodeDetailsV1Response = MiddlewareServicesObject.v1FetchBarcodeDetails(queryParams, headers, body);
        int httpcode = fetchBarcodeDetailsV1Response.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200, "Testcase Failed");

    }
    
}
