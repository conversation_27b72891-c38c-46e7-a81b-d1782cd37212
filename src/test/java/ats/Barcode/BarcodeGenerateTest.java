package ats.Barcode;

import Services.MechantService.MiddlewareServices;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class BarcodeGenerateTest {
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();

    public static final String ISSUER = "ATS";
   	public static final String CLIENT_ID = "ats-bc";
       String custId="1001224519";

    @Test(priority = 1)
    public void generateIndividualBarcodeWithAllMandatoryAttributes() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100777");
        queryParams.put("supplierName", "TestSupSo");
        queryParams.put("count", "1");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",token );
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 2)
    public void generateIndividualBarcodeWithoutSupplierAttributes() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100452");
        queryParams.put("supplierName", "");
        queryParams.put("count", "1");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue( httpcode==400, "Testcase Failed");
        Assert.assertEquals(generateBarcodeResponse.getBody().asString().contains("Sku and Supplier mapping don't exist"),true,"Testcase failed");

    }
    @Test(priority = 3)
    public void generateIndividualBarcodeWithoutSKUID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "");
        queryParams.put("supplierName", "test");
        queryParams.put("count", "1");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue( httpcode==400, "Testcase Failed");
        Assert.assertEquals(generateBarcodeResponse.getBody().asString().contains("SkuId details isn't present"),true,"Testcase failed");

    }
    @Test(priority = 4)
    public void generateIndividualBarcodeWithoutMandatoryAttributes() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "");
        queryParams.put("supplierName", "");
        queryParams.put("count", "1");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue( httpcode==400, "Testcase Failed");
        Assert.assertEquals(generateBarcodeResponse.getBody().asString().contains("SkuId details isn't present"),true,"Testcase failed");

    }
    @Test(priority = 5)
    public void generateIndividualBarcodeWithBarcodeCountZero() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100452");
        queryParams.put("supplierName", "test");
        queryParams.put("count", "0");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue( httpcode==400, "Testcase Failed");
        Assert.assertEquals(generateBarcodeResponse.getBody().asString().contains("Invalid value"),true,"Testcase failed");

    }
    @Test(priority = 6)
    public void generateIndividualBarcodeWithBarcodeCount_200() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100803");
        queryParams.put("supplierName", "TestSupSo");
        queryParams.put("count", "200");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue( httpcode==200, "Testcase Failed");
        //Assert.assertEquals(generateBarcodeResponse.getBody().asString().contains("Invalid value"),true,"Testcase failed");

    }
    @Test(priority = 7)
    public void generateIndividualBarcodeWithBarcodeCount_Above200() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100452");
        queryParams.put("supplierName", "test");
        queryParams.put("count", "1000");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue( httpcode==400, "Testcase Failed");
        Assert.assertEquals(generateBarcodeResponse.getBody().asString().contains("Max Limit for Generate barcode is 200"),true,"Testcase failed");

    }
    //Generate Child Barcode
    @Test(priority = 8)
    //When Parent and child barcode mapping is done and correct.
    public void generateChildBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100777"); //Parent SKU ID
        queryParams.put("barcode", "AI100777000000199"); //parent Barcode


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1ChildBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");
        if (httpcode == 400)
        Assert.assertEquals(generateBarcodeResponse.getBody().asString().contains(" Child Barcodes Already Generated in the System"),true,"Testcase failed");

    }
    @Test(priority = 9)
    //When Parent and child barcode mapping isn't done.
    public void generateChildBarcodeWhenMappingIsNotDone() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100452"); //Parent SKU ID
        queryParams.put("barcode", "TI100452000000209"); //parent Barcode


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1ChildBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 400 , "Testcase Failed");
        Assert.assertEquals(generateBarcodeResponse.getBody().asString().contains("no child sku mapping present in DB for parent skuId"),true,"Testcase failed");
    }
    @Test(priority = 10)
    //When enter child barcode
    public void generateChildBarcodeWhenInputChildBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100010"); //Parent SKU ID
        queryParams.put("barcode", "CI100010000000298"); //parent Barcode


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1ChildBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");
        Assert.assertEquals(generateBarcodeResponse.getBody().asString().contains("Entered SKU and Barcode Mapping not present"),true,"Testcase failed");
    }
    @Test(priority = 11)
    //When enter child sku id
    public void generateChildBarcodeWhenEnterChildSkuID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100011"); //Parent SKU ID
        queryParams.put("barcode", "CI100010000000298"); //parent Barcode


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1ChildBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue( httpcode == 400, "Testcase Failed");
        Assert.assertEquals(generateBarcodeResponse.getBody().asString().contains("no child sku mapping present in DB for parent skuId"),true,"Testcase failed");

    }
    @Test(priority = 12)
    //When enter invalid sku id
    public void generateChildBarcodeWithInvalidSkuID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "24232323"); //Parent SKU ID
        queryParams.put("barcode", "CI100010000000298"); //parent Barcode


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1ChildBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue( httpcode == 400, "Testcase Failed");
        Assert.assertEquals(generateBarcodeResponse.getBody().asString().contains("SkuId details isn't present"),true,"Testcase failed");
    }
    @Test(priority = 13)
    //When enter invalid barcode
    public void generateChildBarcodeWithAllMandatoryAttributes() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100010"); //Parent SKU ID
        queryParams.put("barcode", "CI1298"); //parent Barcode


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1ChildBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 14)
    //When child barcode is already generated
    public void generateChildBarcodeWhichAlreadyGenerated() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100777"); //Parent SKU ID
        queryParams.put("barcode", "AI100777000000199"); //parent Barcode


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1ChildBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");
        if (httpcode == 400)
            Assert.assertEquals(generateBarcodeResponse.getBody().asString().contains(" Child Barcodes Already Generated in the System"),true,"Testcase failed");
}
    @Test(priority = 15)
    public void generateIndividualBarcodeWithoutAcceptParam() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100777");
        queryParams.put("supplierName", "TestSupSo");
        queryParams.put("count", "1");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",token );
        headers.put("accept", "");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 16)
    public void generateIndividualBarcodeWithoutAcceptlanguage() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100777");
        queryParams.put("supplierName", "TestSupSo");
        queryParams.put("count", "1");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",token );
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 17)
    public void generateIndividualBarcodeWithoutContentType() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100777");
        queryParams.put("supplierName", "TestSupSo");
        queryParams.put("count", "1");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",token );
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 18)
    public void generateIndividualBarcodeWithNegativeBarcodeCount() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100452");
        queryParams.put("supplierName", "test");
        queryParams.put("count", "-5");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",token );
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 19)
    public void generateIndividualBarcodeWithAplabetsInBarcodeCount() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100452");
        queryParams.put("supplierName", "test");
        queryParams.put("count", "HY");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",token );
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 20)
    public void generateIndividualBarcodeWithoutAnyAttribute() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "");
        queryParams.put("supplierName", "");
        queryParams.put("count", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",token );
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");

    }
    @Test(priority =21)
    public void generateIndividualBarcodeWithInvalidSupplierName() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100452");
        queryParams.put("supplierName", "YDUUGOYLKJfhj");
        queryParams.put("count", "1");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",token );
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 22)
    public void generateIndividualBarcodeWithInvalidSKUId() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "1029");
        queryParams.put("supplierName", "test");
        queryParams.put("count", "1");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt",token );
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 23)
    public void generateChildBarcodeWithNoAttributes() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", ""); //Parent SKU ID
        queryParams.put("barcode", ""); //parent Barcode


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1ChildBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");

    }
    
    @Test(priority = 24)
    public void generateChildBarcodeWithMissingSkuId() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", ""); //Parent SKU ID
        queryParams.put("barcode", "CI100010000000227"); //parent Barcode


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1ChildBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");

    }
    @Test(priority = 25)
    public void generateChildBarcodeWithMissingBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100777"); //Parent SKU ID
        queryParams.put("barcode", ""); //parent Barcode


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response generateBarcodeResponse = MiddlewareServicesObject.v1ChildBarcodeGenerate(queryParams, headers, body);
        int httpcode = generateBarcodeResponse.getStatusCode();
        Assert.assertTrue(httpcode == 200 || httpcode == 400, "Testcase Failed");

    }
}
