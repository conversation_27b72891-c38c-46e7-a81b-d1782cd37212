package ats.Barcode;

import Services.MechantService.MiddlewareServices;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

public class BarcodeOnboardTest {
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
 

    public static final String ISSUER = "ATS";
   	public static final String CLIENT_ID = "ats-bc";
       String custId="1001224519";
    Random rand = new Random();

    @Test(priority = 1)
    public void onboardIndividualBarcodeWithAllMandatoryAttributes() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        String barcode_id = "FAST0000000"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("barcode", barcode_id);
        body.put("skuId", "100054");
        body.put("supplierName", "Inspiry Technology Pte Ltd");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboard(queryParams, headers, body);
        int httpcode = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");

    }
    @Test(priority = 2)
    public void onboardIndividualBarcodeWithoutBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        String barcode_id = "FAST0000000"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("barcode", "");
        body.put("skuId", "100054");
        body.put("supplierName", "Inspiry Technology Pte Ltd");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboard(queryParams, headers, body);
        int httpcode = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");
        Assert.assertEquals(onboardBarcodeResponse.getBody().asString().contains("Invalid Barcode"),true,"Testcase failed");
    }
    @Test(priority = 3)
    public void onboardIndividualBarcodeWithoutskuId() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        String barcode_id = "FAST0000000"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("barcode", barcode_id);
        body.put("skuId", ""); //invalid.sku
        body.put("supplierName", "Inspiry Technology Pte Ltd");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboard(queryParams, headers, body);
        int httpcode = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");
        Assert.assertEquals(onboardBarcodeResponse.getBody().asString().contains("invalid.SKU.supplier"),true,"Testcase failed");
    }
    @Test(priority = 4)
    public void onboardIndividualBarcodeWithoutSupplierName() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        String barcode_id = "FAST0000000"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("barcode", barcode_id);
        body.put("skuId", "100054");
        body.put("supplierName", ""); //invalid.supplier


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboard(queryParams, headers, body);
        int httpcode = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");
        Assert.assertEquals(onboardBarcodeResponse.getBody().asString().contains("invalid.SKU.supplier"),true,"Testcase failed");
    }
    @Test(priority = 5)
    public void onboardIndividualBarcodeWithInvalidskuId() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        String barcode_id = "FAST0000000"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("barcode", barcode_id);
        body.put("skuId", "423");
        body.put("supplierName", "Inspiry Technology Pte Ltd");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboard(queryParams, headers, body);
        int httpcode = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");
        Assert.assertEquals(onboardBarcodeResponse.getBody().asString().contains("invalid.sku"),true,"Testcase failed");
    }
    @Test(priority = 6)
    public void onboardIndividualBarcodeWithInvalidSupplierName() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        String barcode_id = "FAST0000000"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("barcode", barcode_id);
        body.put("skuId", "100054");
        body.put("supplierName", "ASdfas");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboard(queryParams, headers, body);
        int httpcode = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");
        Assert.assertEquals(onboardBarcodeResponse.getBody().asString().contains("invalid.supplier"),true,"Testcase failed");
    }

    //Individual Barcode Onboard in Series
    @Test(priority = 7)
    public void onboardIndividualBarcodeInSeries() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        int num = rand.nextInt(10000);
        String barcode_startIndex = "FAST00000"+String.valueOf(num);
        String barcode_endIndex = "FAST00000" + String.valueOf(num+1);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("seriesStartIndex", barcode_startIndex);
        body.put("seriesEndIndex", barcode_endIndex);
        body.put("skuId", "100054");
        body.put("supplierName", "Inspiry Technology Pte Ltd");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboardInSeries(queryParams, headers, body);
        int httpcode = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");

    }
    @Test(priority = 8)
    public void onboardIndividualBarcodeInSeries_InvalidSkuID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        int num = rand.nextInt(10000);
        String barcode_startIndex = "FAST00000"+String.valueOf(num);
        String barcode_endIndex = "FAST00000" + String.valueOf(num+1);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("seriesStartIndex", barcode_startIndex);
        body.put("seriesEndIndex", barcode_endIndex);
        body.put("skuId", "797979");
        body.put("supplierName", "Inspiry Technology Pte Ltd");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboardInSeries(queryParams, headers, body);
        int httpcode = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");
        Assert.assertEquals(onboardBarcodeResponse.getBody().asString().contains("invalid.sku"),true,"Testcase failed");

    }
    @Test(priority = 9)
    public void onboardIndividualBarcodeInSeries_WithoutSkuID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        int num = rand.nextInt(10000);
        String barcode_startIndex = "FAST00000"+String.valueOf(num);
        String barcode_endIndex = "FAST00000" + String.valueOf(num+1);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("seriesStartIndex", barcode_startIndex);
        body.put("seriesEndIndex", barcode_endIndex);
        body.put("skuId", "");
        body.put("supplierName", "Inspiry Technology Pte Ltd");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboardInSeries(queryParams, headers, body);
        int httpcode = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");
        Assert.assertEquals(onboardBarcodeResponse.getBody().asString().contains("invalid.SKU.supplier"),true,"Testcase failed");

    }
    @Test(priority = 10)
    public void onboardIndividualBarcodeInSeries_InvalidSupplierName() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        int num = rand.nextInt(10000);
        String barcode_startIndex = "FAST00000"+String.valueOf(num);
        String barcode_endIndex = "FAST00000" + String.valueOf(num+1);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("seriesStartIndex", barcode_startIndex);
        body.put("seriesEndIndex", barcode_endIndex);
        body.put("skuId", "100054");
        body.put("supplierName", "Inspiry Pte Ltd");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboardInSeries(queryParams, headers, body);
        int httpcode = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");
        Assert.assertEquals(onboardBarcodeResponse.getBody().asString().contains("invalid.supplier"),true,"Testcase failed");

    }
    @Test(priority = 11)
    public void onboardIndividualBarcodeInSeries_WithoutSupplierName() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        int num = rand.nextInt(10000);
        String barcode_startIndex = "FAST00000"+String.valueOf(num);
        String barcode_endIndex = "FAST00000" + String.valueOf(num+1);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("seriesStartIndex", barcode_startIndex);
        body.put("seriesEndIndex", barcode_endIndex);
        body.put("skuId", "100054");
        body.put("supplierName", "");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboardInSeries(queryParams, headers, body);
        int httpcode = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");
        Assert.assertEquals(onboardBarcodeResponse.getBody().asString().contains("invalid.SKU.supplier"),true,"Testcase failed");

    }
    @Test(priority = 12)
    public void onboardIndividualBarcodeInSeries_WithoutStartIndex() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        int num = rand.nextInt(10000);
        String barcode_startIndex = "FAST00000"+String.valueOf(num);
        String barcode_endIndex = "FAST00000" + String.valueOf(num+1);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("seriesStartIndex", "");
        body.put("seriesEndIndex", barcode_endIndex);
        body.put("skuId", "100054");
        body.put("supplierName", "Inspiry Technology Pte Ltd");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboardInSeries(queryParams, headers, body);
        int httpcode = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 400 , "Testcase Failed");
        Assert.assertEquals(onboardBarcodeResponse.getBody().asString().contains("Value of fields start sequence length and end sequence length doesnt match"),true,"Testcase failed");

    }
    @Test(priority = 13)
    public void onboardIndividualBarcodeInSeries_WithoutEndIndex() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        int num = rand.nextInt(10000);
        String barcode_startIndex = "FAST00000"+String.valueOf(num);
        String barcode_endIndex = "FAST00000" + String.valueOf(num+1);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("seriesStartIndex", barcode_startIndex);
        body.put("seriesEndIndex", "");
        body.put("skuId", "100054");
        body.put("supplierName", "Inspiry Technology Pte Ltd");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboardInSeries(queryParams, headers, body);
        int httpcode = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 400 , "Testcase Failed");
        Assert.assertEquals(onboardBarcodeResponse.getBody().asString().contains("Value of fields start sequence length and end sequence length doesnt match"),true,"Testcase failed");

    }
    @Test(priority = 14)
    public void onboardIndividualBarcodeInSeries_IncorrectSeries() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        int num = rand.nextInt(10000);
        String barcode_startIndex = "FAST00000"+String.valueOf(num);
        String barcode_endIndex = "FAST00000" + String.valueOf(num-1);

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("seriesStartIndex", barcode_startIndex);
        body.put("seriesEndIndex", barcode_endIndex);
        body.put("skuId", "100054");
        body.put("supplierName", "Inspiry Technology Pte Ltd");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboardInSeries(queryParams, headers, body);
        int httpcode = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 400 , "Testcase Failed");
        Assert.assertEquals(onboardBarcodeResponse.getBody().asString().contains("Starting sequence for barcode generation is incorrect"),true,"Testcase failed");

    }
    //Child Barcode Onboard 100470
    @Test(priority = 15)
    public void onboardChildBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        String barcode_id = "FAST0000000"+String.valueOf(rand.nextInt(10000));
        String child_barcode_id = "*********"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams1 = new HashMap<String, String>();

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("jwt", token);
        headers1.put("accept", "*/*");
        headers1.put("Accept-Language", "en");
        headers1.put("Content-Type", "application/json");

        Map<String, String> body1 = new HashMap<String, String>();
        body1.put("barcode", barcode_id);
        body1.put("skuId", "100057");
        body1.put("supplierName", "Inspiry Technology Pte Ltd");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboard(queryParams1, headers1, body1);
        int httpcode1 = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode1 == 200 , "Testcase Failed");


        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("parentBarcode", barcode_id);
        body.put("childSkuId", "100056");
        body.put("childBarcode", child_barcode_id);


        Response onboardChildBarcodeResponse = MiddlewareServicesObject.v1ChildBarcodeOnboard(queryParams, headers, body);
        int httpcode = onboardChildBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");

    }
    @Test(priority = 16)
    public void onboardChildBarcodeInvalidParentBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        String child_barcode_id = "*********"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("parentBarcode", "fasdfsad");
        body.put("childSkuId", "100056");
        body.put("childBarcode", child_barcode_id);


        Response onboardChildBarcodeResponse = MiddlewareServicesObject.v1ChildBarcodeOnboard(queryParams, headers, body);
        int httpcode = onboardChildBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");
        Assert.assertEquals(onboardChildBarcodeResponse.getBody().asString().contains("Invalid Barcode"),true,"Testcase failed");
    }
    @Test(priority = 17)
    public void onboardChildBarcodeWithoutParentBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String child_barcode_id = "*********"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("parentBarcode", "");
        body.put("childSkuId", "100056");
        body.put("childBarcode", child_barcode_id);


        Response onboardChildBarcodeResponse = MiddlewareServicesObject.v1ChildBarcodeOnboard(queryParams, headers, body);
        int httpcode = onboardChildBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");
        Assert.assertEquals(onboardChildBarcodeResponse.getBody().asString().contains("Invalid SKU Id or Barcode details entered"),true,"Testcase failed");
    }
    @Test(priority = 18)
    public void onboardChildBarcodeWithIncorrectChildSkuID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        String barcode_id = "FAST0000000"+String.valueOf(rand.nextInt(10000));
        String child_barcode_id = "*********"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams1 = new HashMap<String, String>();

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("jwt", token);
        headers1.put("accept", "*/*");
        headers1.put("Accept-Language", "en");
        headers1.put("Content-Type", "application/json");

        Map<String, String> body1 = new HashMap<String, String>();
        body1.put("barcode", barcode_id);
        body1.put("skuId", "100057");
        body1.put("supplierName", "Inspiry Technology Pte Ltd");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboard(queryParams1, headers1, body1);
        int httpcode1 = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode1 == 200 , "Testcase Failed");


        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("parentBarcode", barcode_id);
        body.put("childSkuId", "233232");
        body.put("childBarcode", child_barcode_id);


        Response onboardChildBarcodeResponse = MiddlewareServicesObject.v1ChildBarcodeOnboard(queryParams, headers, body);
        int httpcode = onboardChildBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");
        Assert.assertEquals(onboardChildBarcodeResponse.getBody().asString().contains("Invalid SKU Id"),true,"Testcase failed");
    }
    @Test(priority = 19)
    public void onboardChildBarcodeWithoutChildSkuID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        String barcode_id = "FAST0000000"+String.valueOf(rand.nextInt(10000));
        String child_barcode_id = "*********"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams1 = new HashMap<String, String>();

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("jwt", token);
        headers1.put("accept", "*/*");
        headers1.put("Accept-Language", "en");
        headers1.put("Content-Type", "application/json");

        Map<String, String> body1 = new HashMap<String, String>();
        body1.put("barcode", barcode_id);
        body1.put("skuId", "100057");
        body1.put("supplierName", "Inspiry Technology Pte Ltd");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboard(queryParams1, headers1, body1);
        int httpcode1 = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode1 == 200 , "Testcase Failed");


        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("parentBarcode", barcode_id);
        body.put("childSkuId", "");
        body.put("childBarcode", child_barcode_id);


        Response onboardChildBarcodeResponse = MiddlewareServicesObject.v1ChildBarcodeOnboard(queryParams, headers, body);
        int httpcode = onboardChildBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");
        Assert.assertEquals(onboardChildBarcodeResponse.getBody().asString().contains("Invalid SKU Id or Barcode details entered"),true,"Testcase failed");
    }

    @Test(priority = 20)
    public void onboardChildBarcodeWithoutChildBarcode() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        String barcode_id = "FAST0000000"+String.valueOf(rand.nextInt(10000));
        String child_barcode_id = "*********"+String.valueOf(rand.nextInt(10000));
        Map<String, String> queryParams1 = new HashMap<String, String>();

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("jwt", token);
        headers1.put("accept", "*/*");
        headers1.put("Accept-Language", "en");
        headers1.put("Content-Type", "application/json");

        Map<String, String> body1 = new HashMap<String, String>();
        body1.put("barcode", barcode_id);
        body1.put("skuId", "100057");
        body1.put("supplierName", "Inspiry Technology Pte Ltd");


        Response onboardBarcodeResponse = MiddlewareServicesObject.v1IndividualBarcodeOnboard(queryParams1, headers1, body1);
        int httpcode1 = onboardBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode1 == 200 , "Testcase Failed");


        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("parentBarcode", barcode_id);
        body.put("childSkuId", "100056");
        body.put("childBarcode", "");


        Response onboardChildBarcodeResponse = MiddlewareServicesObject.v1ChildBarcodeOnboard(queryParams, headers, body);
        int httpcode = onboardChildBarcodeResponse.getStatusCode();
        //Assert.assertEquals(200, catagoryResponse.getStatusCode());
        Assert.assertTrue(httpcode == 200 , "Testcase Failed");
        Assert.assertEquals(onboardChildBarcodeResponse.getBody().asString().contains("Invalid SKU Id or Barcode details entered"),true,"Testcase failed");
    }
}
