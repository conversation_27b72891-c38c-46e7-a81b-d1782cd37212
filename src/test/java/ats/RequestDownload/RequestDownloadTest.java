package ats.RequestDownload;

import Services.LendingService.LendingBaseAPI;
import Services.MechantService.MiddlewareServices;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class RequestDownloadTest {
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
    String agent_session_token = "";
    public static final String ISSUER = "ATS";
	public static final String CLIENT_ID = "ats-bc";
    String custId="1001224519";
    String reqId=" ";
    @Test(priority = 1)
    public void PostRequestDownloadWithOnlyCustID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1106992015");
        body.put("phoneNumber","");
        body.put("status","");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        reqId =v1RequestDownloadRes.jsonPath().getString("data.requestId");
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 2)
    public void PostRequestDownloadWithCustIDAndCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1106992015");
        body.put("category","ABC");
        body.put("status","");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }

    @Test(priority = 3)
    public void PostRequestDownloadWithCustID_Category_Status() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1106992015");
        body.put("category","ABC");
        body.put("status","Assigned");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }

    @Test(priority = 4)
    public void PostRequestDownloadWithInvalidCustId() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","110695492015");
        body.put("category","ABC");
        body.put("status","Assigned");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    
    @Test(priority = 5)
    public void PostRequestDownloadWithInvalidCategory() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1106992015");
        body.put("category","ABrryC");
        body.put("status","Assigned");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    
    @Test(priority = 6)
    public void PostRequestDownloadWithInvalidStatus() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1106992015");
        body.put("category","ABC");
        body.put("status","XYZ");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    
    
    @Test(priority = 7)
    public void PostRequestDownloadWithoutCustId() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","");
        body.put("category","ABC");
        body.put("status","XYZ");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    
    @Test(priority = 8)
    public void PostRequestDownloadWithoutBody() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    
    @Test(priority = 9)
    public void PostRequestDownloadWithCustIdAndStatus() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1106992015");
        body.put("status","Unmapped");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 10)
    public void PostRequestDownloadWithStatusOnly() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("status","Unmapped");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 11)
    public void PostRequestDownloadWithCategoryOnly() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("category","ABC");
        body.put("status"," ");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 12)
    public void PostRequestDownloadWithoutRequestType() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1106992015");
        body.put("phoneNumber","");
        body.put("status","");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 13)
    public void CheckRquestStatusWithValidToken() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("process", "USER_INVENTORY_DETAILS");
        queryParams.put("pageNumber", "1");
        queryParams.put("pageSize", "5");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
       
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1CheckDownloadRequests(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 14)
    public void CheckRquestStatusWithoutAcceptHeader() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("process", "USER_INVENTORY_DETAILS");
        queryParams.put("pageNumber", "1");
        queryParams.put("pageSize", "5");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
       
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1CheckDownloadRequests(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 15)
    public void CheckRquestStatusWithoutAcceptLanguageHeader() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("process", "USER_INVENTORY_DETAILS");
        queryParams.put("pageNumber", "1");
        queryParams.put("pageSize", "5");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
       
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1CheckDownloadRequests(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 16)
    public void CheckRquestStatusWithoutContentType() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("process", "USER_INVENTORY_DETAILS");
        queryParams.put("pageNumber", "1");
        queryParams.put("pageSize", "5");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "");

        Map<String, String> body = new HashMap<String, String>();
       
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1CheckDownloadRequests(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 17)
    public void CheckRquestStatusWithoutPageNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("process", "USER_INVENTORY_DETAILS");
        queryParams.put("pageSize", "5");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
       
        // Fetches last 5 download requests
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1CheckDownloadRequests(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 18)
    public void CheckRquestStatusWithoutPageSize() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("process", "USER_INVENTORY_DETAILS");
        queryParams.put("pageNumber", "1");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
       
        // Fetches last 5 download requests
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1CheckDownloadRequests(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 19)
    public void CheckRquestStatusNewPageSizeAndPageNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("process", "USER_INVENTORY_DETAILS");
        queryParams.put("pageNumber", "2");
        queryParams.put("pageSize", "15");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
       
        // Fetches last 15 download requests with page number 2 and page size 15
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1CheckDownloadRequests(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 20)
    public void CheckRquestStatusWithInvalidPageNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("process", "USER_INVENTORY_DETAILS");
        queryParams.put("pageNumber", "Jolly");
        queryParams.put("pageSize", "15");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
       
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1CheckDownloadRequests(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 21)
    public void CheckRquestStatusWithInvalidPageSize() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("process", "USER_INVENTORY_DETAILS");
        queryParams.put("pageNumber", "1");
        queryParams.put("pageSize", "Jolly");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
       
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1CheckDownloadRequests(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 22)
    public void CheckRquestStatusWithInvalidProcess() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("process", "USER_INVENTORY_DETAILS_Jolly");
        queryParams.put("pageNumber", "1");
        queryParams.put("pageSize", "5");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
       
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1CheckDownloadRequests(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 23)
    public void DownloadFileWithInvalidRequestID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("RequestId","888" );
   

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
       
        Response v1DownloadFileRes = MiddlewareServicesObject.v1DownloadFile(queryParams, headers, body);
        int httpcode = v1DownloadFileRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 24)
    public void DownloadFileWithInvalidRequestID2() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("RequestId","xvgtd" );
   

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
       
        Response v1DownloadFileRes = MiddlewareServicesObject.v1DownloadFile(queryParams, headers, body);
        int httpcode = v1DownloadFileRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 25)
    public void DownloadFileWithBlankRequestID() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("RequestId","" );
   

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
       
        Response v1DownloadFileRes = MiddlewareServicesObject.v1DownloadFile(queryParams, headers, body);
        int httpcode = v1DownloadFileRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 26)
    public void DownloadFileWithRequestIDEqualTo0() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("RequestId","0" );
   

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
       
        Response v1DownloadFileRes = MiddlewareServicesObject.v1DownloadFile(queryParams, headers, body);
        int httpcode = v1DownloadFileRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 27)
    public void DownloadFileWithRequestIDEqualToNegativeNumber() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("RequestId","-77" );
   

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
       
        Response v1DownloadFileRes = MiddlewareServicesObject.v1DownloadFile(queryParams, headers, body);
        int httpcode = v1DownloadFileRes.getStatusCode();
        Assert.assertTrue(httpcode == 400, "Testcase Failed");
    }
    @Test(priority = 28)
    public void DownloadFileWithoutAcceptHeader() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("RequestId","33" );
   

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
       
        Response v1DownloadFileRes = MiddlewareServicesObject.v1DownloadFile(queryParams, headers, body);
        int httpcode = v1DownloadFileRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 29)
    public void DownloadFileWithoutContentTypeHeader() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("RequestId","33" );
   

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "");

        Map<String, String> body = new HashMap<String, String>();
       
        Response v1DownloadFileRes = MiddlewareServicesObject.v1DownloadFile(queryParams, headers, body);
        int httpcode = v1DownloadFileRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 30)
    public void DownloadFileWithValidRequestId() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("RequestId",reqId );
   

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "");

        Map<String, String> body = new HashMap<String, String>();
       
        Response v1DownloadFileRes = MiddlewareServicesObject.v1DownloadFile(queryParams, headers, body);
        int httpcode = v1DownloadFileRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 31)
    public void PostRequestDownloadWithStatusAvailable() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1107217419");
        body.put("category","ABC");
        body.put("status","Available");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 32)
    public void PostRequestDownloadWithStatusDeployed() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1107217419");
        body.put("category","ABC");
        body.put("status","Deployed");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 33)
    public void PostRequestDownloadWithStatusPending_Assign() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1107217419");
        body.put("category","ABC");
        body.put("status","Pending_Assign");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 34)
    public void PostRequestDownloadWithStatusPending_Acknowledgement() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1107217419");
        body.put("category","ABC");
        body.put("status","Pending_Acknowledgement");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 35)
    public void PostRequestDownloadWithStatusAssigned() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1107217419");
        body.put("category","ABC");
        body.put("status","Assigned");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 36)
    public void PostRequestDownloadWithStatusUnmapped() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1107217419");
        body.put("category","ABC");
        body.put("status","Unmapped");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 37)
    public void PostRequestDownloadWithStatusReturned() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1107217419");
        body.put("category","ABC");
        body.put("status","Returned");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 38)
    public void PostRequestDownloadWithStatusLost() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1107217419");
        body.put("category","ABC");
        body.put("status","Lost");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 39)
    public void PostRequestDownloadWithStatusDispatch() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1107217419");
        body.put("category","ABC");
        body.put("status","Dispatch");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 40)
    public void PostRequestDownloadWithStatusIn_Transit() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1107217419");
        body.put("category","ABC");
        body.put("status","In_Transit");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 41)
    public void PostRequestDownloadWithStatusReceive_Rejected() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1107217419");
        body.put("category","ABC");
        body.put("status","Receive_Rejected");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
    @Test(priority = 42)
    public void PostRequestDownloadWithStatusReceive_Denied() {
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("request type", "USER_INVENTORY_DETAILS");
        
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("custId","1107217419");
        body.put("category","ABC");
        body.put("status","Receive_Denied");
        
        Response v1RequestDownloadRes = MiddlewareServicesObject.v1DownloadRequest(queryParams, headers, body);
        int httpcode = v1RequestDownloadRes.getStatusCode();
        Assert.assertTrue(httpcode == 200, "Testcase Failed");
    }
}