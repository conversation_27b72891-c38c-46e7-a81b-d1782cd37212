package ats.SKU;

import Services.MechantService.MiddlewareServices;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;
import org.json.JSONArray;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.*;

public class SKUTest {

    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    //  LeadRequest LeadRequestRequestObject = new LeadRequest();
    // Utilities UtilitiesObject = new Utilities();

    public static final String ISSUER = "ATS";
   	public static final String CLIENT_ID = "ats-bc";
    String custId="1001224519";

    Random rand = new Random();
    String rand_userId = "test"+String.valueOf(rand.nextInt(10000));

    @Test(priority = 1)
    public void createSKUNotBarcodeEnable(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "DISABLE");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 2)
    public void createSKUBarcodeEnable(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "true");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "DISABLE");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 3)
    public void createSKUNotFactoryGenerated(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "DISABLE");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 4)
    public void createSKUFactoryGenerated(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "true");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "DISABLE");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
    }
    @Test(priority = 5)
    public void createSubPartSKUWithBarcodeEnable(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "1");
        body.put("barcodeEnable", "true");
        body.put("barcodeFactoryGenerated", "true");

        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSubSKURequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");


    }
    @Test(priority = 6)
    public void createSubPartSKUWithoutBarcodeEnable(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "1");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "true");

        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSubSKURequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");


    }
    @Test(priority = 7)
    public void createSubPartSKUFactoryGenerated(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "1");
        body.put("barcodeEnable", "true");
        body.put("barcodeFactoryGenerated", "true");

        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSubSKURequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");


    }
    @Test(priority = 8)
    public void createSubPartSKUWithoutFactoryGenerated(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "1");
        body.put("barcodeEnable", "true");
        body.put("barcodeFactoryGenerated", "false");

        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSubSKURequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");


    }
    @Test(priority = 9)
    public void createIndividualSKU(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "DISABLE");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 10)
    public void createIndividualSKUWithPack(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "DISABLE");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 11)
    public void createIndividualSKUWithoutPack(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "false");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "DISABLE");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 12)
    public void createIndividualSKUWithReturnable(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "true");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "DISABLE");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 13)
    public void createIndividualSKUWithoutReturnable(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "DISABLE");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 14)
    public void createSKUWithSubPartEnable_WithIncorrectSubpartID(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "true");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("subPartCount", "1");
        body.put("subpartsDisplayName", "testSKu565");
        body.put("isSubSku", "true");
        body.put("subpartsName", "testSKu565");
        body.put("skuId", "10039");
        body.put("skipValidationEnum", "DISABLE");




        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKURequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "SubSku ID is incorrect");

    }
    @Test(priority = 15)
    public void createSKUWithSubPartEnable_WithCorrectSubpartID(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "true");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "DISABLE");
        body.put("subPartCount", "1");
        body.put("subpartsDisplayName", "testSKu565");
        body.put("isSubSku", "true");
        body.put("subpartsName", "testSKu565");
        body.put("skuId", "100393");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKURequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 16)
    public void createSKUWithSubPartEnable_WithMorethanOneSubparts(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "true");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "DISABLE");
        body.put("subPartCount", "1");
        body.put("subpartsDisplayName", "testSKu565");
        body.put("isSubSku", "true");
        body.put("subpartsName", "testSKu565");
        body.put("skuId", "100393");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKURequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 17)
    public void createIndividualSKUWithoutSubPartEnable(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "DISABLE");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 18)
    public void createSKUWithSkipValidationDisable(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "DISABLE");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 19)
    public void createSKUWithSkipValidationBypassForExistingBarcode(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "BYPASS_FOR_EXISTING_BARCODE");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 20)
    public void createSKUWithSkipValidationBypassForAllBarcodes(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "BYPASS_FOR_ALL_BARCODES");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 21)
    public void createSKUWithoutSelectCategory(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "BYPASS_FOR_ALL_BARCODES");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==400 || httpcode==500, "Category is mandatory");

    }
    @Test(priority = 22)
    public void createSKUWithSelectCategory(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "BYPASS_FOR_ALL_BARCODES");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 23)
    public void createSKUWithAttribute_IMEI_Enable(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "BYPASS_FOR_ALL_BARCODES");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }

    @Test(priority = 24)
    public void createSKUWithAttribute_SIM_Enable(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "BYPASS_FOR_ALL_BARCODES");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }

    @Test(priority = 25)
    public void createSKUWithAttribute_MobileNumber_Enable(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "BYPASS_FOR_ALL_BARCODES");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 26)
    public void createSKUWith_WithCorrectSuplierID(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "BYPASS_FOR_ALL_BARCODES");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 27)
    public void createSKUWithSubPartEnable_WithIncorrectSuplierID(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "223232323232");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "BYPASS_FOR_ALL_BARCODES");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Invalid value supplier ID");

    }
    @Test(priority = 28)
    public void createSKUWith_WithMoreThanOneSupliers(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "false");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("subPartCount", "0");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "BYPASS_FOR_ALL_BARCODES");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKUWithoutSubPartRequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 29)
    public void createSKUWithSubPartEnable_WithPrice(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "true");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "1");
        body.put("supplierCount", "1");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "DISABLE");
        body.put("subPartCount", "1");
        body.put("subpartsDisplayName", "testSKu565");
        body.put("isSubSku", "true");
        body.put("subpartsName", "testSKu565");
        body.put("skuId", "100393");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKURequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 30)
    public void createSKUWithSubPartEnable_WithoutPrice(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuDisplayName", skuName);
        body.put("skuName", skuName);
        body.put("skuClass", "0");
        body.put("barcodeEnable", "false");
        body.put("barcodeFactoryGenerated", "false");
        body.put("categoryId", "5");
        body.put("isSubPartEnable", "true");
        body.put("createPackEnable", "true");
        body.put("isReturnable", "false");
        body.put("attributeEnable", "true");
        body.put("supplierPriceInfoId", "2");
        body.put("supplierPriceInfoName", "test");
        body.put("status", "APPROVED");
        body.put("supplierPriceInfoPrice", "");
        body.put("supplierCount", "1");
        body.put("skuParamdisplayName", "Price");
        body.put("skuParamName", "Price");
        body.put("regex", "^((?!(0))[0-9]{0,10})$");
        body.put("IMEIdisplayName", "IMEI Enable");
        body.put("IMEIName", "IMEI Enable");
        body.put("IMEIregex", "^[1-9][0-9]{14}$");
        body.put("MobileNumberdisplayName", "Mobile Number Enable");
        body.put("MobileNumberName", "Mobile Number Enable");
        body.put("MobileNumberregex", "^[1-9]{10}$");
        body.put("SIMdisplayName", "SIM Enable");
        body.put("SIMName", "SIM Enable");
        body.put("SIMregex", "^[1-9][0-9]{14}$");
        body.put("skipValidationEnum", "DISABLE");
        body.put("subPartCount", "1");
        body.put("subpartsDisplayName", "testSKu565");
        body.put("isSubSku", "true");
        body.put("subpartsName", "testSKu565");
        body.put("skuId", "100393");



        Response skuResponse = MiddlewareServicesObject.v1ATSCreateSKURequest(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==400 || httpcode==500, "Price is mandatory");

    }
    @Test(priority = 31)
    public void fetchSkuDetailsByCorrectSkuID(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100403");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response skuResponse = MiddlewareServicesObject.v1ATSFetchSKUDetails(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
    }
    @Test(priority = 32)
    public void fetchSkuDetailsByIncorrectSkuID(){

        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("skuId", "9");


            Map<String, String> headers = new HashMap<String, String>();
            headers.put("jwt", token);
            headers.put("accept", "*/*");
            headers.put("Accept-Language", "en");
            headers.put("Content-Type", "application/json");

            Map<String, String> body = new HashMap<String, String>();


            Response skuResponse = MiddlewareServicesObject.v1ATSFetchSKUDetails(queryParams, headers, body);
            int httpcode = skuResponse.getStatusCode();
            //Assert.assertEquals(200, skuResponse.getStatusCode());
            Assert.assertTrue(httpcode==400, "Testcase Failed");
            Assert.assertEquals(skuResponse.getBody().asString().contains("Invalid SKU ID"),true,"Testcase failed");
    }

    @Test(priority = 33)
    public void getSkuListParams(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response skuParamResponse = MiddlewareServicesObject.v1ATSGetSKUListOfParams(queryParams, headers, body);
        int httpcode = skuParamResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");

    }
    @Test(priority = 34)
    public void searchSKUWithCorrectSkuID(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "100403");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response skuResponse = MiddlewareServicesObject.v1ATSSearchSKU(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");


    }
    @Test(priority = 35)
    public void searchSKUWithIncorrectSkuID(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("skuId", "10");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response skuResponse = MiddlewareServicesObject.v1ATSSearchSKU(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 );
        Assert.assertEquals(skuResponse.getBody().asString().contains("No sku for given search criteria"),true,"Testcase failed");


    }
    @Test(priority = 36)
    public void searchSKUWithIncorrectName(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("name", "#@##");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response skuResponse = MiddlewareServicesObject.v1ATSSearchSKU(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        Assert.assertEquals(skuResponse.getBody().asString().contains("No sku for given search criteria"),true,"Testcase failed");

    }
    @Test(priority = 37)
    public void searchSKUWithExactName(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("name", "TestSKU");
        queryParams.put("exactMatch", "true");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response skuResponse = MiddlewareServicesObject.v1ATSSearchSKU(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");


    }
    @Test(priority = 38)
    public void searchSKUWithExactFalse(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("name", "TestSKU");
        queryParams.put("exactMatch", "False");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response skuResponse = MiddlewareServicesObject.v1ATSSearchSKU(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");


    }
    @Test(priority = 39)
    public void searchSKUWithCorrectCatagoryName(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "Fastag");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response skuResponse = MiddlewareServicesObject.v1ATSSearchSKU(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");

    }
    @Test(priority = 40)
    public void searchSKUWithIncorrectCatagoryName(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("categoryName", "zoom");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response skuResponse = MiddlewareServicesObject.v1ATSSearchSKU(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        Assert.assertEquals(skuResponse.getBody().asString().contains("No sku for given search criteria"),true,"Testcase failed");

    }
    @Test(priority = 41)
    public void searchSKUWithoutAnyQueryParam(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();


        Response skuResponse = MiddlewareServicesObject.v1ATSSearchSKU(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        //Assert.assertEquals(skuResponse.getBody().asString().contains("No sku for given search criteria"),true,"Testcase failed");

    }
    @Test(priority = 42)
    public void updateSKU_ChangeSkipValidation_BypassForAllBarcodes(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuID", "100422");
        body.put("skipValidationEnum", "BYPASS_FOR_ALL_BARCODES");

        Response skuResponse = MiddlewareServicesObject.v1ATSUpdateSKU(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200, "Testcase Failed");
        Assert.assertEquals(skuResponse.getBody().asString().contains("BYPASS_FOR_ALL_BARCODES"),true,"Testcase failed");

    }
    @Test(priority = 43)
    public void updateSKU_ChangeSkipValidation_BypassForExitingBarcode(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuID", "100422");
        body.put("skipValidationEnum", "BYPASS_FOR_EXISTING_BARCODE");

        Response skuResponse = MiddlewareServicesObject.v1ATSUpdateSKU(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 , "Testcase Failed");
        Assert.assertEquals(skuResponse.getBody().asString().contains("BYPASS_FOR_EXISTING_BARCODE"),true,"Testcase failed");

    }
    @Test(priority = 44)
    public void updateSKU_ChangeSkipValidation_Disable(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuID", "100422");
        body.put("skipValidationEnum", "DISABLE");

        Response skuResponse = MiddlewareServicesObject.v1ATSUpdateSKU(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 , "Testcase Failed");
        Assert.assertEquals(skuResponse.getBody().asString().contains("DISABLE"),true,"Testcase failed");

    }
    @Test(priority = 45)
    public void updateSKU_ChangeDomain_ATS_domain_sound_box(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuID", "100422");
        body.put("domain", "ats_domain_sound_box");


        Response skuResponse = MiddlewareServicesObject.v1ATSUpdateSKU(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
       // Assert.assertEquals(skuResponse.getBody().asString().contains("ats_domain_sound_box"),true,"Testcase failed");


    }
    @Test(priority = 46)
    public void updateSKU_ChangeDomain_ATS_domain_fastag(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuID", "100422");
        body.put("domain", "ats_domain_fast_tag");


        Response skuResponse = MiddlewareServicesObject.v1ATSUpdateSKU(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
      //  Assert.assertEquals(skuResponse.getBody().asString().contains("ats_domain_fast_tag"),true,"Testcase failed");


    }
    @Test(priority = 47)
    public void updateSKU_ChangeDomain_ATS_domain_EDC(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuID", "100422");
        body.put("domain", "ats_domain_edc");


        Response skuResponse = MiddlewareServicesObject.v1ATSUpdateSKU(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");
     //  Assert.assertEquals(skuResponse.getBody().asString().contains("ats_domain_edc"),true,"Testcase failed");


    }
    @Test(priority = 48)
    public void updateSKU_AddSupplier(){
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        String skuName = "testSKu"+String.valueOf(rand.nextInt(10000));

        Map<String, String> queryParams = new HashMap<String, String>();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        body.put("skuID", "100422");

        List<Object> Supply = new ArrayList<>();
        Map<String,String> Value = new HashMap<>();

        Value.put("id","2");
        Value.put("name","test");
        Value.put("status","APPROVED");
        Value.put("price","1");

        Supply.add(Value);
        JSONArray supplyJson = new JSONArray(Supply);
        body.put("suppliers", String.valueOf(supplyJson));


        Response skuResponse = MiddlewareServicesObject.v1ATSUpdateSKU(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        //Assert.assertEquals(200, skuResponse.getStatusCode());
        Assert.assertTrue(httpcode==200 || httpcode==400, "Supplier already exists for SKU");


    }
}
