package ats.UserProfileController;

import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FetchUserDetail {
	public static final String ISSUER = "ATS";
   	public static final String CLIENT_ID = "ats-bc";
    String custId="1001224519";
    @Test(priority = 1)
    public void SearchUserProfileUsingEntity(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entity","OCL");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 2)
    public void SearchUserProfileUsingStatusApproved(){
  	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
      String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


      Map<String, String> queryParams = new HashMap<String, String>();
      queryParams.put("status","APPROVED");
      Map<String, String> headers = new HashMap<String, String>();
      headers.put("jwt", token);
      headers.put("accept", "*/*");
      headers.put("Accept-Language", "en");
     
      Map<String, String> body = new HashMap<String, String>();
      Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
      int httpcode = skuResponse.getStatusCode();
      Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

  }
    @Test(priority = 3)
    public void SearchUserProfileUsingStatusRejected(){
  	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
      String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


      Map<String, String> queryParams = new HashMap<String, String>();
      queryParams.put("status","REJECTED");
      Map<String, String> headers = new HashMap<String, String>();
      headers.put("jwt", token);
      headers.put("accept", "*/*");
      headers.put("Accept-Language", "en");
     
      Map<String, String> body = new HashMap<String, String>();
      Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
      int httpcode = skuResponse.getStatusCode();
      Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

  }
    @Test(priority = 4)
    public void SearchUserProfileUsingSinglePincode(){
  	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
      String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
     

      Map<String, String> queryParams = new HashMap<String, String>();
      queryParams.put("pinCodesList","122001");
      Map<String, String> headers = new HashMap<String, String>();
      headers.put("jwt", token);
      headers.put("accept", "*/*");
      headers.put("Accept-Language", "en");
     
      Map<String, String> body = new HashMap<String, String>();
      Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
      int httpcode = skuResponse.getStatusCode();
      Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

  }
    @Test(priority = 5)
    public void SearchUserProfileUsingSingleName(){
  	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
      String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
     

      Map<String, String> queryParams = new HashMap<String, String>();
      queryParams.put("namesList","Sourav");
      Map<String, String> headers = new HashMap<String, String>();
      headers.put("jwt", token);
      headers.put("accept", "*/*");
      headers.put("Accept-Language", "en");
     
      Map<String, String> body = new HashMap<String, String>();
      Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
      int httpcode = skuResponse.getStatusCode();
      Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

  }
    @Test(priority = 6)
    public void SearchUserProfileUsingMultipleName(){
  	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
      String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
     

      Map<String, String> queryParams = new HashMap<String, String>();
      queryParams.put("namesList","Sourav,Mohit");
      Map<String, String> headers = new HashMap<String, String>();
      headers.put("jwt", token);
      headers.put("accept", "*/*");
      headers.put("Accept-Language", "en");
     
      Map<String, String> body = new HashMap<String, String>();
      Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
      int httpcode = skuResponse.getStatusCode();
      Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

  }
    @Test(priority = 7)
    public void SearchUserProfileUsingSingleCustId(){
  	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
      String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
     

      Map<String, String> queryParams = new HashMap<String, String>();
      queryParams.put("custIdList","1001361880");
      Map<String, String> headers = new HashMap<String, String>();
      headers.put("jwt", token);
      headers.put("accept", "*/*");
      headers.put("Accept-Language", "en");
     
      Map<String, String> body = new HashMap<String, String>();
      Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
      int httpcode = skuResponse.getStatusCode();
      Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

  }
    @Test(priority = 8)
    public void SearchUserProfileUsingMultipleCustId(){
  	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
      String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
     

      Map<String, String> queryParams = new HashMap<String, String>();
      queryParams.put("custIdList","1001361880,1107234087");
      Map<String, String> headers = new HashMap<String, String>();
      headers.put("jwt", token);
      headers.put("accept", "*/*");
      headers.put("Accept-Language", "en");
     
      Map<String, String> body = new HashMap<String, String>();
      Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
      int httpcode = skuResponse.getStatusCode();
      Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

  }
    @Test(priority = 9)
    public void SearchUserProfileUsingSingleEmpId(){
  	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
      String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
     

      Map<String, String> queryParams = new HashMap<String, String>();
      queryParams.put("employeeIdList","1001361880");
      Map<String, String> headers = new HashMap<String, String>();
      headers.put("jwt", token);
      headers.put("accept", "*/*");
      headers.put("Accept-Language", "en");
     
      Map<String, String> body = new HashMap<String, String>();
      Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
      int httpcode = skuResponse.getStatusCode();
      Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

  }
    @Test(priority = 10)
    public void SearchUserProfileUsingMultipleEmpId(){
  	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
      String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
     

      Map<String, String> queryParams = new HashMap<String, String>();
      queryParams.put("employeeIdList","1001361880,T23455");
      Map<String, String> headers = new HashMap<String, String>();
      headers.put("jwt", token);
      headers.put("accept", "*/*");
      headers.put("Accept-Language", "en");
     
      Map<String, String> body = new HashMap<String, String>();
      Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
      int httpcode = skuResponse.getStatusCode();
      Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

  }
    @Test(priority = 11)
    public void SearchUserProfileUsingSingleMobileNo(){
  	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
      String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
     

      Map<String, String> queryParams = new HashMap<String, String>();
      queryParams.put("contactInfoList","7347237790");
      Map<String, String> headers = new HashMap<String, String>();
      headers.put("jwt", token);
      headers.put("accept", "*/*");
      headers.put("Accept-Language", "en");
     
      Map<String, String> body = new HashMap<String, String>();
      Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
      int httpcode = skuResponse.getStatusCode();
      Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

  }
    @Test(priority = 12)
    public void SearchUserProfileUsingMultipleMobileNo(){
  	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
      String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
     

      Map<String, String> queryParams = new HashMap<String, String>();
      queryParams.put("contactInfoList","7347237790,8884211099");
      Map<String, String> headers = new HashMap<String, String>();
      headers.put("jwt", token);
      headers.put("accept", "*/*");
      headers.put("Accept-Language", "en");
     
      Map<String, String> body = new HashMap<String, String>();
      Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
      int httpcode = skuResponse.getStatusCode();
      Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

  }
    @Test(priority = 13)
    public void SearchUserProfileUsingSingleEmail(){
  	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
      String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
     

      Map<String, String> queryParams = new HashMap<String, String>();
      queryParams.put("emailList","<EMAIL>");
      Map<String, String> headers = new HashMap<String, String>();
      headers.put("jwt", token);
      headers.put("accept", "*/*");
      headers.put("Accept-Language", "en");
     
      Map<String, String> body = new HashMap<String, String>();
      Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
      int httpcode = skuResponse.getStatusCode();
      Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

  }
    @Test(priority = 14)
    public void SearchUserProfileUsingMultepleEmail(){
  	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
      String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);
     

      Map<String, String> queryParams = new HashMap<String, String>();
      queryParams.put("emailList","<EMAIL>,<EMAIL>");
      Map<String, String> headers = new HashMap<String, String>();
      headers.put("jwt", token);
      headers.put("accept", "*/*");
      headers.put("Accept-Language", "en");
     
      Map<String, String> body = new HashMap<String, String>();
      Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
      int httpcode = skuResponse.getStatusCode();
      Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

  }
    @Test(priority = 15)
    public void SearchUserProfileUsingEntityPPBL(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entity","PPBL");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 16)
    public void SearchUserProfileUsingEntityPSPL(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("entity","PSPL");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 17)
    public void SearchUserProfileUsingValidCategory(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("category","EDC");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 18)
    public void SearchUserProfileUsingInvalidCategory(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("category","GFHF");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 19)
    public void SearchUserProfileUsingValidRole(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("role","ATS_FSE");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 20)
    public void SearchUserProfileUsingInvalidRole(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("role","00000");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 21)
    public void SearchUserProfileUsingValidUsertype(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("userType","OCL_FSE");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 22)
    public void SearchUserProfileUsingInvalidUsertype(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("userType","OCADL_FSE");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 23)
    public void SearchUserProfileUsingValidPincode(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("pinCodesList","122001");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 24)
    public void SearchUserProfileUsingInvalidPincode(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("pinCodesList","CCDDD");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 25)
    public void SearchUserProfileWithoutPincode(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("pinCodesList","");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 26)
    public void SearchUserProfileWithValidState(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("state","Haryana");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 27)
    public void SearchUserProfileWithInvalidState(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("state","SDJKHF");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 28)
    public void SearchUserProfileWithoutState(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("state","");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 29)
    public void SearchUserProfileUsingMultipleFiltersPositive(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("state","Haryana");
        queryParams.put("status","APPROVED");
        queryParams.put("role","ATS_FSE");
        queryParams.put("userType","OCL_FSE");
        queryParams.put("custIdList","1001361880");
        queryParams.put("namesList","Sourav");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 30)
    public void SearchUserProfileUsingMultipleFiltersNegative(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("state","Haryana");
        queryParams.put("status","FSFFFS");
        queryParams.put("role","ATS_FSE");
        queryParams.put("userType","SFSFS");
        queryParams.put("custIdList","1001361880");
        queryParams.put("namesList","Sourav");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 31)
    public void SearchUserProfileWithoutAcceptHeader(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("state","Haryana");
        queryParams.put("status","APPROVED");
        queryParams.put("role","ATS_FSE");
        queryParams.put("userType","OCL_FSE");
        queryParams.put("custIdList","1001361880");
        queryParams.put("namesList","Sourav");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "");
        headers.put("Accept-Language", "en");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
    @Test(priority = 32)
    public void SearchUserProfileWithoutAcceptLanguageHeader(){
    	  MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("state","Haryana");
        queryParams.put("status","APPROVED");
        queryParams.put("role","ATS_FSE");
        queryParams.put("userType","OCL_FSE");
        queryParams.put("custIdList","1001361880");
        queryParams.put("namesList","Sourav");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "");
       
        Map<String, String> body = new HashMap<String, String>();
        Response skuResponse = MiddlewareServicesObject.v1UserProfile(queryParams, headers, body);
        int httpcode = skuResponse.getStatusCode();
        Assert.assertTrue(httpcode==200 || httpcode==400, "Testcase Failed");

    }
}
