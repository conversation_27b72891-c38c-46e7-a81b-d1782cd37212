package Devices;

import DevicesUtils.DevicesUtils;
import Request.MerchantService.v1.EDC.CreateUnmapEdcLead;
import Request.MerchantService.v3.*;
import Request.UpgradeEdcFulfilmentByPass.UpgradeEDCFulfilmentByPass;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.UpgradeServices.FulfilmentByPass;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class DeviceUpgradeFulfilmentBypass extends BaseMethod
{
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    DevicesUtils devicesUtils=new DevicesUtils();
    FulfilmentByPass Obj = new FulfilmentByPass();
    public static String AgentToken = "";
    public static String mobileNo = "7722127717";
//    public static String version = "5.0.8";
    public static String deviceId="";
    public static String UserMID = "BpqoRH01002607972307";
    public static String CustId = "1704740696";
    public static String OTP ="888888";
    public static String State = "";
    public static String kybBusinessId = "";
    public static String businessName = "";
    public static String leadId = "";
    public static String EntityType = "";
    public static String solution_type = "edc_device_upgrade";
    public static String companyLeadId = "edc_device_upgrade";
    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLoginUpgradeEDC() throws Exception
    {
        AgentToken = AgentSessionToken("7771216290", "paytm@123");
        waitForLoad(3000);
        DBConnection.UpdateQueryToCloseLead(mobileNo,solution_type);
    }
    @Test(priority = 1,groups = {"Regression"},description = "Send OTP for Business")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void UnMapEdcPositiveSendOtpBusiness() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        SendOtp v3SendOtp1 = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp1,"UNKNOWN","company_onboard",AgentToken,DevicesConfig.Version,mobileNo,"company");
        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        State = SendOtpResp.jsonPath().getString("state");
        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }

    @Test(priority = 1, description = "Positive Validate OTP for Unmapedc", dependsOnMethods = "UnMapEdcPositiveSendOtpBusiness", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnmapEDCPositiveValidateOtp() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        waitForLoad(2000);
        ValidateOtp validateOtpObj1 = new ValidateOtp(P.TESTDATA.get("ValidateOtpUnmapEDC"));
        Response validateOtp1 = middlewareServicesObject.v3ValidateOtp(validateOtpObj1, "INDIVIDUAL", "company_onboard", AgentToken, DevicesConfig.Version, mobileNo, "company", State, OTP);
        int StatusCode = validateOtp1.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }


    @Test(priority = 1,description = "Positive Get Business",groups = {"Regression"},dependsOnMethods = "UnmapEDCPositiveValidateOtp")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void UnMapEdcPositiveGetBusiness() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        GetBusinessv3 getBusObj1 = new GetBusinessv3();
        System.out.println("Merchant Cust id is : " +CustId);
        Response getBusResp1 = middlewareServicesObject.v3GetBusiness(getBusObj1,AgentToken,DevicesConfig.Version,CustId);
        kybBusinessId = getBusResp1.jsonPath().getJsonObject("businesses[0].kybBusinessId").toString();
        int statusCode = getBusResp1.getStatusCode();
       Assert.assertEquals(statusCode,200);
    }

    @Test(priority = 1,description = "Positive Get Business Profile",groups = {"Regression"} ,dependsOnMethods = "UnMapEdcPositiveGetBusiness")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void UnMapEdcPositiveGetBusinessProfile() throws Exception {
        establishConnectiontoServer(AgentToken,5);
        BusinessProfile v3BusPro1 = new BusinessProfile();
        System.out.println("Merchant Cust id is : " +CustId);
        System.out.println("Merchant kyb id is : " +kybBusinessId);
        System.out.println("Merchant lead id is : " +companyLeadId);
        Response v3BusProRes1 = middlewareServicesObject.v3BusinessProfile(v3BusPro1,CustId,
                companyLeadId,kybBusinessId,AgentToken,DevicesConfig.Version);
        EntityType = v3BusProRes1.jsonPath().getJsonObject("businessSRO.entityType").toString();
        int StatusCode = v3BusProRes1.getStatusCode();
       Assert.assertEquals(StatusCode,200);
    }

    @Test(priority = 1,description = "Fetch Applicant's MID",dependsOnMethods ="UnMapEdcPositiveGetBusinessProfile",groups = {"Regression"} )
    @Owner(emailId = "utkarsh10.singh@paytm",isAutomated = true)
    public void UnMapEdcPositiveFetchMID() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        MID v3FetchMid1 = new MID();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "edc_device_upgrade");
        System.out.println("Merchant Cust id is :" + CustId);
        Response v3FetchMIDResp1 = middlewareServicesObject.v3FetchMID(v3FetchMid1, AgentToken, DevicesConfig.Version, CustId, "EDC");

        if (v3FetchMIDResp1.getStatusCode()==400)
        {
            UnMapEdcPositiveFetchMID();
        }

        UserMID = v3FetchMIDResp1.jsonPath().getJsonObject("mids[0].mid").toString();
        int statusCode = v3FetchMIDResp1.getStatusCode();
            Assert.assertEquals(statusCode, 200);
        v3FetchMid1.validateResponseAgainstJSONSchema("MerchantService/V3/GetMID/GetMIDSchema.json");
    }
    @Test(priority = 1,groups = {"Regression"},description = "Create Lead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void UpgradeEDCEdcCreateLead() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        CreateUnmapEdcLead CreateUnmapEdcLeadObject=new CreateUnmapEdcLead(P.TESTDATA.get("UnmapEDCCreateLeadRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType","PROPRIETORSHIP");
        body.put("userCustId",CustId);
        body.put("agentCustId","22223");
        body.put("mid",UserMID);
        body.put("userMobile",mobileNo);
        body.put("kybId",kybBusinessId);
        body.put("businessLeadId","");
        body.put("solutionType",solution_type);
        body.put("requestType","UPGRADE_UNMAP");
        body.put("wfVersion","V2");
        body.put("serviceReason","Device not working at the time of device delivery");

        Map<String, String> Queryparams = new HashMap<String, String>();
        Queryparams.put("upgrade","device");
        Response getMerchantResponse=middlewareServicesObject.v1CreateUpgradeLead(CreateUnmapEdcLeadObject,body,Queryparams,AgentToken,DevicesConfig.Version);
        leadId= getMerchantResponse.jsonPath().getString("leadId");

        int statusCode = getMerchantResponse.getStatusCode();
        Assert.assertEquals(statusCode,200);
    }
    @Test(priority = 1,groups = {"Regression"},description = "Fetch All Terminal",dependsOnMethods ="UpgradeEDCEdcCreateLead")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void FetchAllTerminal()
    {
        Response res=devicesUtils.GetAllTerminal(UserMID);
        int statusCode = res.getStatusCode();
         Assert.assertEquals(statusCode,200);
         deviceId=devicesUtils.getActiveDevice(res);
        Assert.assertFalse(deviceId.isEmpty(), "Device ID should not be empty");



    }


    @Test(priority = 1,groups = {"Regression"},description = "Fulfilment By Pass",dependsOnMethods ="FetchAllTerminal")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void UpgradeEDCEdcFulfilmentBypass() throws Exception {
       establishConnectiontoServer(AgentToken,5);
        UpgradeEDCFulfilmentByPass upgradeEDCFulfilmentByPass=new UpgradeEDCFulfilmentByPass(P.TESTDATA.get("DeviceUpgradeFulfilmentBypass"));
        Map<String, String> headers = new HashMap<String, String>();
       headers.put("Content-Type", "application/json");
        headers.put("session_token","0etpmtpvp3kg5pu0o9jrwqyzknsvza1m5631");
        headers.put("deviceidentifier","vivo-V2140-826328fe25a0fe67" );


        Map<String,String>body=new HashMap<>();
        body.put("agentCustId","1107195733");
        body.put("userCustId","1704740696");
        body.put("merchantName","Test");
        body.put("userMobile","7722127717");
        body.put("mid","BpqoRH01002607972307");
        body.put( "entityType","PROPRIETORSHIP");
        body.put(  "solutionType","edc_device_upgrade");
        body.put("leadId",leadId);
        body.put("deviceId",deviceId);
        body.put("model","A910");
        body.put( "vendor", "PAX");
        Response res = Obj.FulfilmentBypass(upgradeEDCFulfilmentByPass,headers,body);
        int statusCode = res.getStatusCode();
        Assert.assertEquals(statusCode,200);
    }

}
