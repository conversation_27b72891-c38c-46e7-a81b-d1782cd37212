package AfterSuite;

import io.restassured.RestAssured;
import io.restassured.response.Response;
import org.testng.ISuite;
import org.testng.ISuiteListener;

import java.io.File;

public class ListenerTest implements ISuiteListener {


    private static final String slackChannel = "automationdemo";
    private static final String filepath = (System.getProperty("user.dir") + "/extentreport/extentreport.html");
    private static final String screenShotpath = (System.getProperty("user.dir") + "/extentreport/screenshots/reportscreenshot.png");
    private static final String userOauthToken = "******************************************************";
    private static String verticalName = "";

    public void onStart(ISuite suite) {
        verticalName = suite.getName();
        System.out.println("onStart function started " + suite.getName());
    }

    public void onFinish(ISuite suite) {

        File extentReport = new File(filepath);
        File screenShot = new File(screenShotpath);

        // Check if the extent report exists before uploading
        if (extentReport.exists()) {
            Response fileUpload = RestAssured.given()
                    .multiPart("file", extentReport, "multi-part/form-data")
                    .formParam("token", userOauthToken)
                    .formParam("filetype", "html")
                    .formParam("filename", "Extent Report")
                    .formParam("title", "Extent Report")
                    .formParam("initial_comment", "This is Extent Report of " + verticalName + " Team.")
                    .formParam("channels", slackChannel)
                    .post("https://slack.com/api/files.upload")
                    .thenReturn();

            System.out.println("Extent report uploaded successfully.");
        } else {
            System.err.println("Extent report file not found: " + filepath);
        }

        // Check if the screenshot exists before uploading
        if (screenShot.exists()) {
            Response screenShotUpload = RestAssured
                    .given()
                    .multiPart("file", screenShot, "multi-part/form-data")
                    .formParam("token", userOauthToken)
                    .formParam("filetype", "png")
                    .formParam("filename", "Extent Report Screenshot")
                    .formParam("title", "Extent Report Screenshot")
                    .formParam("initial_comment", "This is the Screenshot of your Extent Report of " + verticalName + " Team.")
                    .formParam("channels", slackChannel)
                    .post("https://slack.com/api/files.upload")
                    .thenReturn();

            System.out.println("Screenshot uploaded successfully.");
        } else {
            System.err.println("Screenshot file not found: " + screenShotpath);
        }
    }


}