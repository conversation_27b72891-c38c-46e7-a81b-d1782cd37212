package utils;

import java.util.HashMap;
import java.util.Map;

public class HeaderUtils {
    // Base static header constants
    private static final String X_SRC = "GGClient";
    private static final String DEVICE_NAME = "M2004J19C";
    private static final String DEVICE_IDENTIFIER = "Xiaomi-M2004J19C-131535fc93929702";
    private static final String OS_VERSION = "10";
    private static final String DEVICE_MANUFACTURER = "Xiaomi";
    private static final String ANDROID_ID = "131535fc93929702";
    private static final String CONTENT_TYPE = "application/json; charset=UTF-8";
    private static final String USER_AGENT = "Dalvik/2.1.0 (Linux; U; Android 10; M2004J19C MIUI/V12.0.3.0.QJCINXM)";
    private static final String HOST = "goldengate-staging5.paytm.com";
    private static final String UNCLE_SCROOGE = "BabaBlackSheepWeAreInShitDeep";
    private static final String DEVICE_MAC = "60:6E:E8:D6:95:DB";
    private static final String IP_ADDRESS = "************";

    /**
     * Gets the base headers required for all API calls
     */
    public static Map<String, String> getBaseHeaders(String agentToken, String version) {
        Map<String, String> headers = new HashMap<>();
        headers.put("version", version);
        headers.put("session_token", agentToken);
        headers.put("Content-Type", CONTENT_TYPE);
        headers.put("Host", HOST);
        headers.put("ipAddress", IP_ADDRESS);
        return headers;
    }

    /**
     * Gets device-related headers
     */
    public static Map<String, String> getDeviceHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("deviceName", DEVICE_NAME);
        headers.put("deviceIdentifier", DEVICE_IDENTIFIER);
        headers.put("osVersion", OS_VERSION);
        headers.put("deviceManufacturer", DEVICE_MANUFACTURER);
        headers.put("androidId", ANDROID_ID);
        headers.put("User-Agent", USER_AGENT);
        headers.put("deviceMac", DEVICE_MAC);
        return headers;
    }

    /**
     * Gets location-related headers
     */
    public static Map<String, String> getLocationHeaders(String latitude, String longitude) {
        Map<String, String> headers = new HashMap<>();
        headers.put("latitude", latitude);
        headers.put("longitude", longitude);
        headers.put("isLocationMocked", "false");
        return headers;
    }

    /**
     * Gets security-related headers
     */
    public static Map<String, String> getSecurityHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("isBusyBoxFound", "false");
        headers.put("isDeviceRooted", "false");
        headers.put("UncleScrooge", UNCLE_SCROOGE);
        return headers;
    }

    /**
     * Gets checksum headers
     */
    public static Map<String, String> getChecksumHeaders(String checksumV3, String urlChecksumV3) {
        Map<String, String> headers = new HashMap<>();
        if (checksumV3 != null) {
            headers.put("X-MW-CHECKSUM-V3", checksumV3);
        }
        if (urlChecksumV3 != null) {
            headers.put("X-MW-URL-CHECKSUM-V3", urlChecksumV3);
        }
        return headers;
    }

    /**
     * Gets headers specific to IOT device operations
     */
    public static Map<String, String> getIotHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("X-SRC", X_SRC);
        headers.put("client", "androidapp");
        headers.put("appLanguage", "en");
        return headers;
    }

    /**
     * Gets additional headers for web/mobile requests
     */
    public static Map<String, String> getWebHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("origin", "https://ggapp-frontend-qa.paytm.com");
        headers.put("x-requested-with", "com.paytm.goldengate.app.debug");
        headers.put("sec-fetch-site", "same-site");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-dest", "empty");
        headers.put("referer", "https://ggapp-frontend-qa.paytm.com/");
        headers.put("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        return headers;
    }

    /**
     * Combines all headers for a complete IOT request
     */
    public static Map<String, String> getCompleteIotHeaders(String agentToken, String version, 
            String latitude, String longitude, String checksumV3, String urlChecksumV3) {
        Map<String, String> headers = new HashMap<>();
        headers.putAll(getBaseHeaders(agentToken, version));
        headers.putAll(getDeviceHeaders());
        headers.putAll(getLocationHeaders(latitude, longitude));
        headers.putAll(getSecurityHeaders());
        headers.putAll(getChecksumHeaders(checksumV3, urlChecksumV3));
        headers.putAll(getIotHeaders());
        headers.putAll(getWebHeaders());
        return headers;
    }

    /**
     * Gets headers for merchant operations
     */
    public static Map<String, String> getMerchantHeaders(String agentToken, String version) {
        Map<String, String> headers = new HashMap<>();
        headers.putAll(getBaseHeaders(agentToken, version));
        headers.putAll(getDeviceHeaders());
        headers.putAll(getSecurityHeaders());
        headers.put("X-SRC", X_SRC);
        headers.put("appLanguage", "en");
        return headers;
    }

    /**
     * Utility method to add or update dynamic headers
     */
    public static Map<String, String> addDynamicHeaders(Map<String, String> existingHeaders, 
            Map<String, String> dynamicHeaders) {
        if (dynamicHeaders != null) {
            existingHeaders.putAll(dynamicHeaders);
        }
        return existingHeaders;
    }

    /**
     * Gets base headers for SoundBox operations
     */
    public static Map<String, String> getSoundBoxBaseHeaders(String agentToken, String version) {
        Map<String, String> headers = new HashMap<>();
        headers.put("version", version);
        headers.put("session_token", agentToken);
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("Host", "goldengate-staging5.paytm.com");
        headers.put("ipAddress", "***********");
        return headers;
    }

    /**
     * Gets device-related headers for SoundBox
     */
    public static Map<String, String> getSoundBoxDeviceHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("deviceName", "M2004J19C");
        headers.put("deviceIdentifier", "Xiaomi-M2004J19C-131535fc93929702");
        headers.put("deviceManufacturer", "Xiaomi");
        headers.put("androidId", "131535fc93929702");
        headers.put("osVersion", "10");
        headers.put("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 10; M2004J19C MIUI/V12.0.3.0.QJCINXM)");
        return headers;
    }

    /**
     * Gets location-related headers for SoundBox
     */
    public static Map<String, String> getSoundBoxLocationHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("latitude", "12.836047");
        headers.put("longitude", "77.6691948");
        headers.put("isLocationMocked", "false");
        return headers;
    }

    /**
     * Gets security-related headers for SoundBox
     */
    public static Map<String, String> getSoundBoxSecurityHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("isBusyBoxFound", "false");
        headers.put("isDeviceRooted", "false");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        return headers;
    }

    /**
     * Gets client-related headers for SoundBox
     */
    public static Map<String, String> getSoundBoxClientHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("X-SRC", "GGClient");
        headers.put("client", "androidapp");
        headers.put("appLanguage", "en");
        return headers;
    }

    /**
     * Gets checksum headers for SoundBox with custom values
     * @param checksumV3 The checksum value for X-MW-CHECKSUM-V3
     * @param urlChecksumV3 The checksum value for X-MW-URL-CHECKSUM-V3
     */
    public static Map<String, String> getSoundBoxChecksumHeaders(String checksumV3, String urlChecksumV3) {
        Map<String, String> headers = new HashMap<>();
        if (checksumV3 != null) {
            headers.put("X-MW-CHECKSUM-V3", checksumV3);
        }
        if (urlChecksumV3 != null) {
            headers.put("X-MW-URL-CHECKSUM-V3", urlChecksumV3);
        }
        return headers;
    }

   
    /**
     * Gets complete headers for SoundBox operations with custom checksums
     */
    public static Map<String, String> getCompleteSoundBoxHeaders(String agentToken, String version, String checksumV3, String urlChecksumV3) {
        Map<String, String> headers = new HashMap<>();
        headers.putAll(getSoundBoxBaseHeaders(agentToken, version));
        headers.putAll(getSoundBoxDeviceHeaders());
        headers.putAll(getSoundBoxLocationHeaders());
        headers.putAll(getSoundBoxSecurityHeaders());
        headers.putAll(getSoundBoxClientHeaders());
        headers.putAll(getSoundBoxChecksumHeaders(checksumV3, urlChecksumV3));
        return headers;
    }

    /**
     * Gets complete headers for SoundBox operations with default checksums
     */
    public static Map<String, String> getCompleteSoundBoxHeaders(String agentToken, String version) {
        return getCompleteSoundBoxHeaders(agentToken, version, null, null);
    }
} 