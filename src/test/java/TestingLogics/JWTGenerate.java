package TestingLogics;

import com.auth0.jwt.algorithms.Algorithm;
import com.goldengate.common.BaseMethod;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
public class JWTGenerate extends BaseMethod {

	
public static void main(String[] args) throws Exception
{
	//Generatin subscription client token
	String token=createAuth0JwsHMAC("oe-subscription-client","E7MYmjY7sZNONEGtcPbM5lYjk2sBG+RSsyJVeMqF8DQMd+ToQSJ3tU4ii3Sz/YD0/L6+CG4SoWccRgcuaKiasQ==");
	System.out.println(token);
	Map<String,String> jwtParams=new HashMap<String,String>();
	
	
	String token1=createAuth0JwsHMACTEST("OE","c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec");
	System.out.println(token1);
	
//	String token1=createAuth0JwsHMACnew("OE");
//	System.out.println(token1);

	
}

//public static String createAuth0JwsHMACTEST(String clientId, String key) throws IllegalArgumentException{
////    byte[] decodedKey = Base64.getDecoder().decode(key);
// 
//    Algorithm algorithm = Algorithm.HMAC256(key);
// 
//    String token = JWT  .create()
//                        .withIssuedAt(new Date())
//                        .withClaim("clientId", clientId)
//                        .sign(algorithm);
//    return token;
//}



public static String createAuth0JwsHMACnew(String key,String clientId) throws IllegalArgumentException{
    byte[] decodedKey = Base64.getDecoder().decode(key);
 
    Algorithm algorithm = Algorithm.HMAC256(decodedKey);
// 
//    String token = JWT  .create()
//                        .withIssuedAt(new Date())
//                        .withClaim("clientId", clientId);
//    
    SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;
    long epoch = System.currentTimeMillis()/ 1000;
    System.out.println("Epoch : " + epoch);
    

//jwt : c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec

    
   
    Map<String,Object> claims=new HashMap<String,Object>();
    claims.put("clientId", clientId);
    claims.put("iat", epoch);
    
    String jwtToken = Jwts.builder().setHeaderParam("alg", "HS256").
    		          setHeaderParam("typ", "JWT").
    		          setClaims(claims).compact();
    		
   return jwtToken;
}
	
}
