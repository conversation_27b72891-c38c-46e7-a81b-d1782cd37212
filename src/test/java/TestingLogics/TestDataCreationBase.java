
package TestingLogics;

//import OCL.Business.QRMerchant500K.FlowQRMerchant500K;
//import OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited;
//import OCL.Individual.Merchant100K.Flow100K;
/*
import OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated;
import OCL.Individual.UnifiedPaymentMerchant.FlowUnifiedPaymentNonIntegrated;*/

import com.goldengate.common.BaseMethod;
import org.apache.log4j.Logger;
/*
public class TestDataCreationBase extends BaseMethod {
    private static final Logger LOGGER = Logger.getLogger(TestDataCreationBase.class);

    void CreateTestData(String Solution, int Count) throws Exception {

        LOGGER.info("Creating Test Data of solution : " +Solution);
        LOGGER.info("Number of Data will be created : " +Count);

        for (int i = 1;i<=Count;i++)
        {
            LOGGER.info("Creating Test data for " +Solution + " Total Count is : " +Count  + " Current Count is  " +i);
            switch (Solution) {
                case "100k":
                    {
                    /*Flow100K Obj100K = new Flow100K();

                    Flow100K.OePanelDocStatus = "REJECTED";
                    Flow100K.RejectionReason = "Wrong Photo";
                    Obj100K.AgentLogin100k();
                    Obj100K.PositiveSendOtpSendOTP();
                    Obj100K.PositiveValidateOtp();
                    Obj100K.PositiveGetMerchantStatus();
                    Obj100K.positiveGetDocStatus();
                    Obj100K.positivePennyDrop();
                    Obj100K.positiveSubmitMerchant();
                    Obj100K.PositiveFetchDocs100K();
                    Obj100K.PositiveGetOEPanelCookie();
                    Obj100K.PositiveFetchLeadPanel();
                    Obj100K.ReallocatingAgent100K();
                    Obj100K.PositiveRejectedLeadPanel();
                    Obj100K.PositiveGetMerchantStatusAfterRejection();
                    Obj100K.positiveSubmitMerchantAfterRejection();
                    Obj100K.PositiveSubmitLeadPanel();
                    break;

                }/*
                case "QR500k":
                    {

                    FlowQRMerchant500K Obj500k = new FlowQRMerchant500K();

                    Obj500k.AgentLogin500K();
                    Obj500k.TC001_CreateApplicantOauth();
                    Obj500k.TC002_PositiveSendOtpBusiness500K();
                    Obj500k.TC003_PositiveGetBusiness500K();
                    Obj500k.TC006_PositiveGetComapny500K();
                    Obj500k.TC0018_PositivePostComapny500K();
                    Obj500k.TC0019_PositiveGetBusinessAfterCompany500K();
                    Obj500k.TC0020_PositiveGetBusinessProfile500K();
                    Obj500k.TC0021_PositiveSendOtpQRMerchant500K();
                    Obj500k.TC0022_PositiveGetTnC500K();
                    Obj500k.TC0023_PositiveValidateOtpQRMerchant500K();
                    Obj500k.TC0024_PositiveGetMerchant500K();
                    Obj500k.TC0027_PositiveSendOtpMerchantDeclare500K();
                    Obj500k.TC0028_PositiveValidateOtpMerchantDeclare500K();
                    Obj500k.TC0029_PositiveGetGstExemption500K();
                    Obj500k.TC0030_PositiveGetDocStatus500K();
                    Obj500k.TC0031_PositiveGetBanks500K();
                    Obj500k.TC0032_PositivePennyDropMultiNameMatch500K();
                    Obj500k.TC0033_PositiveSubmitLead500K();
                    Obj500k.TC0034_PositiveFetchDocs500K();
                    Obj500k.TC0036_PositiveGetOEPanelCookie500K();
                    Obj500k.TC0037_PositiveFetchLeadPanel500K();
                    Obj500k.TC0038_ReallocatingAgent500K();
                    Obj500k.TC0039_PositiveSubmitLeadPanel500K();
                    break;
                }
                case "50k":
                {
                  /*  Flow50k Obj50k = new Flow50k();

                    Obj50k.TC0001_CreateApplicantOauth();
                    Obj50k.TC0002_getApplicantToken();
                    Obj50k.TC0006_createLead50k();
                    Obj50k.TC00014_createBusiness50k();
                    Obj50k.TC00017_createAdditionalDetails50k();
                    Obj50k.TC00020_validateBankDetailsTest();
                    Obj50k.TC00021_updateBankDetailsTest();
                    break;
                }
                case"upm":
                {
               //     FlowUnifiedPaymentNonIntegrated ObjUpm = new FlowUnifiedPaymentNonIntegrated();

                   ObjUpm.TC0001_CreateApplicantOauth();
                    ObjUpm.TC0002_GetApplicantToken();
                    ObjUpm.TC0003_11_FetchDetails();
                    ObjUpm.TC0004_10_CreateLeadUMO();
                    ObjUpm.TC0005_16_CreateBusiness();
                    ObjUpm.TC0006_11_ValidateBankDetails();
                    ObjUpm.TC0007_05_UpdateBankDetails();
                    ObjUpm.TC0008_03_CreateAdditionalDetails();
                    ObjUpm.TC0009_12_UpdateIdentity();
                    ObjUpm.TC0010_PGCallBackforInsatntMid();
                    ObjUpm.TC0011_03_CreateAccount();
                    ObjUpm.AddingDataCsv();
                    break;
                }
                case"QRUL":
                {
                    FlowQRMerchantUnlimited ObjQrUl = new FlowQRMerchantUnlimited();

                    FlowQRMerchantUnlimited.OePanelDocStatus = "APPROVED";
                    FlowQRMerchantUnlimited.RejectionReason = null;
                    ObjQrUl.AgentLoginUnlimited();
                    ObjQrUl.TC0001_CreateApplicantOauth();
                    ObjQrUl.TC0002_PositiveSendOtpBusinessUnlimited();
                    ObjQrUl.TC0003_PositiveGetBusinessUnlimited();
                    ObjQrUl.TC0004_PositiveGetComapnyUnlimited();
                    ObjQrUl.TC0005_PositivePostComapnyUnlimited();
                    ObjQrUl.TC0006_PositiveGetBusinessAfterCompanyUnlimited();
                    ObjQrUl.TC0007_PositiveGetBusinessProfileUnlimited();
                    ObjQrUl.TC0008_PositiveValidateOtpQRMerchantUnlimited();
                    ObjQrUl.TC0008_PositiveSendOtpQRMerchantUnlimited();
                    ObjQrUl.TC0010_PositiveValidateOtpQRMerchantUnlimited();
                    ObjQrUl.TC0011_PositiveGetMerchantUnlimited();
                    ObjQrUl.TC0019_PositivePennyDropMultiNameMatchUnlimited();
                    ObjQrUl.TC0020_PositiveSubmitLeadUnlimited();
                    ObjQrUl.TC0021_PositiveFetchDocsUnlimited();
                    ObjQrUl.TC0022_PositiveGetOEPanelCookieUnlimited();
                    ObjQrUl.TC0023_PositiveFetchLeadPanelUnlimited();
                    ObjQrUl.TC0024_ReallocatingAgentUnlimited();
                    ObjQrUl.TC0028_PositiveSubmitLeadPanelUnlimited();
                    break;

                }
                case"upmInt":
                {
                    FlowUinifiedPaymentIntegrated ObjUpmInt = new FlowUinifiedPaymentIntegrated();

                    ObjUpmInt.TC0001_CreateApplicantOauth();
                    ObjUpmInt.TC0002_GetApplicantToken();
                    ObjUpmInt.TC0003_11_FetchDetails();
                    ObjUpmInt.TC0004_10_CreateLeadUMO();
                    ObjUpmInt.TC0005_16_CreateBusiness();
                    ObjUpmInt.TC0006_11_ValidateBankDetails();
                    ObjUpmInt.TC0007_05_UpdateBankDetails();
                    ObjUpmInt.TC0008_03_CreateAdditionalDetails();
                    ObjUpmInt.TC0009_12_UpdateIdentity();
                    ObjUpmInt.TC0010_PGCallBackforInsatntMid();
                    ObjUpmInt.TC0011_03_CreateAccount();
                    break;
                }
            }
        }
    }
}
*/
