package TestingLogics;

import Request.MerchantService.v1.Payments.OrderFullfillment;
import Request.MerchantService.v1.sdMerchant.Lead_fetch;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.google.zxing.NotFoundException;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

public class TEST extends BaseMethod
{
    private static final Logger LOGGER = LogManager.getLogger(TEST.class);


    public static String PaytmAppSsoToken = "";
    public static String QrCodeId = "";
    public static String QrBase64 ="iVBORw0KGgoAAAANSUhEUgAAAeAAAAHgAQAAAABVr4M4AAAB/klEQVR42u3cO5LCMAyAYTFbpOQIHGWPBkfjKByBkiKDIFhxpLyGYLPb/C4Tfy41sixbtGBIET5LHDvVszTPH3Lo/p9kr3eR39fc0WjAYDC4Cm5cVLom/ByqlzRlP4SstJ6NMxgMBtfDFpSOCavecpw65pkJtz7SgcFg8HewxTDtZnafJKdSYDAY/CfYYtihz67S9g0MBoO/iOOOzsZFfsaFpTe2g2AwGLwZL9e3W5ddNVuL42AwGPwenjnEj2FtY6sAGAwGb8Q5Or3yJklVJNvRtfmYbaYBQPx2EAwGgz/GPiYNn8N6O3fa3y6mUmAwGPwp9gErzrT11E7e4iYv5WFgMBhcin1hqeswulsqZfXtvMkb2iHBYDC4Mm7SmZrEGDaeect9SMMmDwwGgytgf7Sfh2/Bvk5P3nRUWAKDweAiPGl09KnUqb+QZuuFWhMYDAZXwDmVshjWNzrmT3692dN+MBgMLsKxmC3hJkgb76jFq7JgMBhcEa/1Pg4X0qywFM7owGAwuBQv3QTJfUiTyyGhOA4Gg8HleObNtElXdoxh/b4PDAaDa+HlV0JOMhnumA0MBoO/gnO/teaSt4wupOn6g2tgMBhciBv3MpF/v+iNVAoMBoM/wXFHp33Tke/Kjq1Ja083gsFg8Ea89GaauD6kxVQKDAaDS7EWjP/DDwR01Am5s7D9AAAAAElFTkSuQmCC";


    public static String pathQrCode = "output.jpg";

    public static String StatusCodePG ="";

    //PSA Solution Level
    public static String Res = "utility_service";
    public static String Psa = "business_service";


    public static void RevString()
    {
        System.out.println("Enter string to reverse:");

        Scanner read = new Scanner(System.in);
        String str = read.nextLine();

        StringBuilder sb = new StringBuilder(str);

        System.out.println("Reversed string is:");
        System.out.println(sb.reverse().toString());
    }
    public static void ArrayListSize()
    {
        ArrayList<String> listOfBanks = new ArrayList<String>() ;
        int size = listOfBanks.size();
        LOGGER.info("size of array list after creating: " + size);

      /*  listOfBanks.add("Citibank");
        listOfBanks.add("Chase");
        listOfBanks.add("Bank of America");*/
        size = listOfBanks.size() - 1;

        LOGGER.info("length of ArrayList after adding elements: " + size);

        listOfBanks.clear();
        size = listOfBanks.size();
        LOGGER.info("size of ArrayList after clearing elements: " + size);
    }

    public static void HashMapGet()
    {
        Map<String,String> AMC = new HashMap<>();

        AMC.put("label","AMC");
        AMC.put("amount","250");
        AMC.put("taxApplicable","true");

        Map<String,String> EMI = new HashMap<>();

        EMI.put("label","EMI");
        EMI.put("amount","250");
        EMI.put("taxApplicable","true");

        Map<String,Map<String,String>> getResponsePricing = new HashMap<>();

        getResponsePricing.put("amc",AMC);
        getResponsePricing.put("emiRentalCharge",EMI);


        LOGGER.info("This is Pricing Component Map + " +getResponsePricing);

        int MapSize = getResponsePricing.size();

        for ( Map.Entry<String, Map<String, String>> entry : getResponsePricing.entrySet())

        {
            String key = entry.getKey();

            Map<String,String> component = new HashMap<>();

            component = entry.getValue();

            for (Map.Entry<String,String> ent2 : component.entrySet())
            {
                LOGGER.info( "Entry 2 Key is : "+ ent2.getKey() + " Entry 2  Value is : " + ent2.getValue() );
            }
            LOGGER.info( "Key is : "+ entry.getKey() + " Value is : " + entry.getValue() );
        }



    }

    public static void GenerateFake()
    {
        Faker GenerateFake = new Faker();

        String lineOne = GenerateFake.address().streetAddress();
        String lineTwo = GenerateFake.address().cityName();
        String lineThree = GenerateFake.address().streetName();
        String lat = GenerateFake.address().latitude();
        String longi = GenerateFake.address().longitude();

        LOGGER.info(" \n Line one : "  + lineOne + "\n Line two : " +lineTwo + " \n Line Three : " + lineThree
                + "\n Latitude : " +lat + "\n Longitude : " +longi);

    }

    public static void DbConnection() throws SQLException {
        TestBase testBase = new TestBase();
        DbName = "online_merchant_v2";
      /*  testBase.getUbmId("9953828631","register_lead");
        String UbmId = TestBase.getUbmId;
        LOGGER.info("This is UBM ID : " +UbmId);

        testBase.getUbmId("7771110467","p2p_100k");
        String UbmIdTwo = TestBase.getUbmId;
        LOGGER.info("This is UBM ID : " +UbmIdTwo);


        DbName = "umo_payments";
        testBase.getQrMerchantPgReqId("1000806957");
        Map PgRe = TestBase.QrPgReqId;
        LOGGER.info("This is QR Merchant PG Req ID : " + PgRe + "\n");

        testBase.getRecentPgReqId("1000806957");
        String getRec = TestBase.PgReqId;
        LOGGER.info("This is Recent PG Req ID : " + getRec);


        DbName = "sprint15_3";
        testBase.getUbmId("7771110469","qr_merchant");
        String UbmIdThree = TestBase.getUbmId;
        LOGGER.info("This is UBM ID : " +UbmIdThree);

        testBase.UpdateQuery("UPDATE user_business_mapping set status = 4 where mobile_number = 7771110465 and solution_type = 'pg_profile_update' and solution_type_level_2 = 'INSTRUMENT_DISABLE' and solution_type_level_3 = 'PPI' ;");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes);*/

        //testBase.getQrMerchantPgReqId("1000969427");
        //testBase.getRecentPgReqId("1000969427");
        LOGGER.info("\n");
        String query = "select sai.solution_value from user_business_mapping ubm join related_business_solution_mapping rbsm join solution_additional_info sai on ubm.related_business_solution_mapping_id = rbsm.id and rbsm.solution_id = sai.solution_id where ubm.mobile_number = '5174875453' and sai.solution_key in ('PG_REQUEST_ID_50K')";
        testBase.getResult(query,"solution_value");



    }

    public static  void StringTrim() throws IOException, NotFoundException {
        String qr = "AASHITSHARMA";
        int abc = 63729922;

        qr.substring(2);
        qr.length();
        LOGGER.info("Sub string is : " + qr.substring(2));
        LOGGER.info("Length of string : " + qr.length());
        if (qr.length() == 1)
        {
            LOGGER.info("Lenght is 1 ");
        }
        else
        {
            int x = qr.length() - (qr.length()-1);
            LOGGER.info("New String Lenght : " +x);
            char y = String.valueOf(abc).charAt(0);
            LOGGER.info("Character at no. 1 is : " +y);
        }

        /*String PlanPrice = "345";
        String SecurityDep ="2000";
        int pri = Integer.parseInt(PlanPrice) + Integer.parseInt(SecurityDep);
        PlanPrice = String.valueOf(pri);
        LOGGER.info("Total Plan price is : " +PlanPrice);*/

       /* BinaryBitmap binaryBitmap = new BinaryBitmap(new HybridBinarizer(
                new BufferedImageLuminanceSource(
                        ImageIO.read(new FileInputStream("/Users/<USER>/Documents/oe-api-automation/src/main/resources/input.jpeg")))));
        Result qrCodeResult = new MultiFormatReader().decode(binaryBitmap);


        QrCodeId = qrCodeResult.toString();

        LOGGER.info("This is QR ID: " + qrCodeResult);
        LOGGER.info("This is QR Code ID in String : " + QrCodeId);

        FetchQrDetails(QrCodeId);*/

        String aashit = "AashIT";
        LOGGER.info("My name in Upper case : " + aashit.toUpperCase());
    }

    public static void TokenPaytmApp()
    {
        LOGGER.info(" Paytm App Login "); //7771116332 //paytm@123
        SsoToken = PaytmAppSsoToken("7771116332","test@123");
        LOGGER.info("This is SSO Token for Paytm App : " + SsoToken);
    }

    public static void GenerateOrderPsaDiy(String Mobile, String SolLvl2) throws InterruptedException {
        OrderFullfillment orderObj = new OrderFullfillment();
        Lead_fetch fetchLead = new Lead_fetch();
        MiddlewareServices middlewareServicesObject = new MiddlewareServices();

        String ApplicantToken =ApplicantToken(Mobile,"paytm@123");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
        headers.put("session_token", ApplicantToken);
        headers.put("deviceIdentifier","OPPO-CPH1859-869003037324211");
        headers.put("client","androidapp");
        headers.put("latitude","28.9342");
        headers.put("longitude","72.48543");
        headers.put("androidId","AashitAndroidId");

        Map<String, String> queryParamsFetch = new HashMap<String, String>();

        queryParamsFetch.put("solution", "psa_diy");
        queryParamsFetch.put("entityType", "INDIVIDUAL");
        queryParamsFetch.put("channel", "PAYTM_APP");
        queryParamsFetch.put("solutionTypeLevel2",SolLvl2);

        Response responseObject = middlewareServicesObject.FetchLead(fetchLead, queryParamsFetch, headers);
        String leadId = responseObject.jsonPath().getJsonObject("leadId").toString();
        LOGGER.info("Lead Id is : " +leadId);

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("leadId",leadId);
        queryParams.put("generateOrder","true");
        queryParams.put("channel","PAYTM_APP");

        Response resObj = middlewareServicesObject.OrderFullfillment(orderObj,queryParams,headers);

        String PSAOrderId = resObj.jsonPath().getJsonObject("ORDER_ID");
        String PsaMID = resObj.jsonPath().getJsonObject("MID");
        String PsaAmount = resObj.jsonPath().getString("TXN_AMOUNT");
        String PsapaymentDone = resObj.jsonPath().getJsonObject("paymentDone").toString();

        int statusCode = resObj.getStatusCode();
        Assert.assertEquals(200,statusCode);
        LOGGER.info("Status of Payment is : " +PsapaymentDone);

        //Making Payment
        String PgResponse = PayMerchant(PSAOrderId,PsaAmount,PsaMID,"FalseOrderCreated");

        if(PsapaymentDone.equals("false")) {
            for (int i = 0; i < 3; i++) {
                LOGGER.info("Inside Payment Loop");
                if (!PgResponse.equals("01")) {
                    LOGGER.info(" Performing Payment " + i + 1 + " Time");
                    PgResponse = PayMerchant(PSAOrderId, PsaAmount, PsaMID, "FalseOrderCreated");
                    LOGGER.info(" PG Response for payment is : " + PgResponse);
                } else {
                    LOGGER.info("Payment Done After Payment Loop");
                    OrderFullfillment orderNewObj = new OrderFullfillment();


                    Response resObjTwo = middlewareServicesObject.OrderFullfillment(orderNewObj, queryParams, headers);
                    PsapaymentDone = resObjTwo.jsonPath().getJsonObject("paymentDone").toString();
                    break;
                }
            }
        }


        Assert.assertTrue(PsapaymentDone.contains("true"));
        LOGGER.info("Payment Done");
    }

    public static void GenerateJWT()
    {
        LocalDateTime localDateTime=LocalDateTime.now(ZoneId.of("GMT+05:30"));
        // LocalDate localDate = localDateTime.toLocalDate();
        LOGGER.info("Date is :"+localDateTime);
        String ts= localDateTime.toString();

        Algorithm buildAlgorithm = Algorithm.HMAC256("b5ab9b19-7f36-4eae-a845-5bdae889dfb8");
        String FIStoken= com.auth0.jwt.JWT.create().withIssuer("OE")
                .withClaim("clientId", "FIS")
                .withClaim("iss", "OE").withClaim("custId", "1000876552")
                .withClaim("timestamp",ts+"+05:30").sign(buildAlgorithm);

        LOGGER.info("FIS JWT Token is : " + FIStoken);


        Algorithm buildAlgorithmPG = Algorithm.HMAC256("snoM+NsLPkwLj+Qi8gIHImHXh2u2Oja2DXNwspb1+8XOKiN8iafOG7vy+AhHEl6Fc07QPvngtqCKg/nX3zY/uQ==");
        long epoch = TimeEpoch();
        String epc = String.valueOf(epoch);
        String PGtoken= com.auth0.jwt.JWT.create().withIssuer("OE")
                .withClaim("client-id", "85c2e7e3-2f22-461c-854c-5f77d9f532be")
                .withClaim("iat",epc).sign(buildAlgorithmPG);
        LOGGER.info("FIS JWT Token is : " + PGtoken);
    }


    public static void DoPay() throws IOException, NotFoundException
    {
        //TokenPaytmApp();
        SsoToken = "57790bc5-66df-4d41-9249-e37b58f77400";

        /*BinaryBitmap binaryBitmap = new BinaryBitmap(new HybridBinarizer(
                new BufferedImageLuminanceSource(
                        ImageIO.read(new FileInputStream("/Users/<USER>/Downloads/QRIMG.jpg")))));
        Result qrCodeResult = new MultiFormatReader().decode(binaryBitmap);

        QrCodeId = qrCodeResult.toString();

        LOGGER.info("This is QR ID: " + qrCodeResult);
        LOGGER.info("This is QR Code ID in String : " + QrCodeId);*/


        QrCodeId = QrCodeExtractor(QrBase64,pathQrCode);
        FetchQrDetails(QrCodeId);
        LOGGER.info("MID to Pay : " + PayMID);
        LOGGER.info("MGUID to Pay : " + PayMGUID);
        LOGGER.info("Amount to be Payed : " + Amount);
        LOGGER.info("OrderId Generated : " + OrderId);

        StatusCodePG = PayMerchant(OrderId, Amount, PayMID,"FalseOrderCreated");
        LOGGER.info("PG Status Code : " + StatusCodePG);


    }

    public static void payForOms()
    {
        //TokenPaytmApp();
        SsoToken = "57790bc5-66df-4d41-9249-e37b58f77400";
        //SsoToken="15b99491-59d0-4f6f-98b6-e002425e7300";
        String PgPos = "01";
        //False Order
        //StatusCodePG = PayMerchant("100071890974","1.0","Rechar32004946353223","FalseOrderCreated");
        LOGGER.info("PG Status Code : " + StatusCodePG);

        if (!StatusCodePG.equals(PgPos))
        {
            //True Order
            StatusCodePG = PayMerchant("100072339368", "2413", "GGAPPP45716654658555", "TrueOrderCreated");
            LOGGER.info("PG Status Code : " + StatusCodePG);
        }

        //Fastag67603637067951
        //Rechar32004946353223
        //Mplace59064363019056
        //GGAPPP45716654658555
        //PTINST72403627065052


    }

    @Test
    public void UploadDocUpgradeMid() throws JsonProcessingException {
        File DocPath = new File(System.getProperty("user.dir")+"/PaytmImage.jpg");
        LOGGER.info("File Path is : " + DocPath.getAbsolutePath());

        //String Sso = "d0c8e378-7e11-40c5-a294-3e499ad26600";
        String LeadId = "41c159ee-c25f-4f85-bf8b-d4d0e1b81237";
        String SolLead = "1439773a-9763-4126-99e7-99e4fa4dc045";
        String Entity = "PRIVATE_LIMITED";
        String Solution = "online_merchant";
        String Sso = ApplicantToken("5556660399","paytm@123");

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", Solution);
        queryParams.put("entityType", Entity);
        queryParams.put("leadId", LeadId);
        queryParams.put("solutionLeadId",SolLead);
        queryParams.put("channel", "UMP_WEB");
        // queryParams.put("solutionTypeLevel2","business_service");


        Map<String, String> headerss = new HashMap<String, String>();

        headerss.put("session_token", Sso);
        headerss.put("version", "7.3.0");
        headerss.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        headerss.put("Content-Type", "multipart/json");

        Map<String, String> queryParamsUpload = new HashMap<String, String>();
        queryParamsUpload.put("solutionType", Solution);
        queryParamsUpload.put("entityType", Entity);
        queryParamsUpload.put("solutionLeadId",SolLead);
        queryParamsUpload.put("channel", "UMP_WEB");


        FetchUploadDiyDoc(queryParams,queryParamsUpload,headerss,DocPath);    }

        @Test
        public void DataSet()
        {
            List<Object> Supply = new ArrayList<>();
            Map<String,String> Value = new HashMap<>();

            Value.put("id","2");
            Value.put("name","Mohit");

            Supply.add(Value);

            JSONArray supplyJson = new JSONArray(Supply);

            LOGGER.info("This is JAVA List : " +Supply);
            LOGGER.info("This is JSON ArrayList : " +supplyJson);
        }

    @Test
    public  void FetchUploadPsaDoc() throws JsonProcessingException
    {
        String AgentToken = CommonAgentToken;
        String LeadId = "d010ab98-2960-48eb-8149-636d60159203";
        String CustId = "1001329470";
        String MobileNo = "6665550202";
        String Solution = "assisted_merchant_onboard";
        String SubSolution = "small_merchant";


        Map<String,String>queryDocUpload = new HashMap<>();
        queryDocUpload.put("type", "jpg");
        queryDocUpload.put("entityType", "INDIVIDUAL");
        queryDocUpload.put("solutionType", Solution);
        queryDocUpload.put("merchantCustId", CustId);
        queryDocUpload.put("leadId", LeadId);
        queryDocUpload.put("solutionTypeLevel2",SubSolution);


        Map<String,String>queryFetchDoc = new HashMap<>();
        queryFetchDoc.put("entityType", "INDIVIDUAL");
        queryFetchDoc.put("solution", Solution);
        queryFetchDoc.put("leadId", LeadId);
        queryFetchDoc.put("channel", "GG_APP");
        queryFetchDoc.put("merchantCustId", CustId);
        //queryFetchDoc.put("category","Retail and Shopping");
        //queryFetchDoc.put("subCategory","Game Parlour");
        queryFetchDoc.put("solutionSubType",SubSolution);

        GgAppDynamicDocUpload(AgentToken,"4.2.6",MobileNo,queryFetchDoc,queryDocUpload);

    }
    @Test()
    public void CreateUpiAccount()
    {
        AddUpiAccount("**********");
    }

    public static void main(String[] args) throws IOException, NotFoundException, SQLException, InterruptedException
    {
        //DbConnection();
        //DoPay();
        //payForOms();
        //GenerateOrderPsaDiy("**********",Res);
        //StringTrim();
        //GenerateJWT();



    }

    @Test(priority = 0)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void Jwt_Van_Mapping() throws Exception {
        Map<String, String> jwtParams = new HashMap<>();
        UUID uuid = UUID.randomUUID();
        System.out.println("seceret key: "+ uuid.toString() );
        jwtParams.put("custId", "**********");
        generateJwtToken("f6057739-eb22-45f7-820b-25a4ccc4ced0", "UMP",jwtParams,false );
    }
    
    
    

}
