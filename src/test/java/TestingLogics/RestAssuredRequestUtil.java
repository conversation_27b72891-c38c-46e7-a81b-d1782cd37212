package TestingLogics;
import java.util.Iterator;
/**
 *
 */
import java.util.Map;

import OCL.Lending.CreditCard.HDFC.ETB;
import org.json.JSONArray;
import org.json.JSONObject;
import org.kohsuke.rngom.parse.host.Base;
import org.testng.Assert;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;


public class RestAssuredRequestUtil extends Base {

    private static final Logger LOGGER = LogManager.getLogger(ETB.class);

    RequestSpecification spec;
    public RestAssuredRequestUtil(String baseURL) {
        // TODO Auto-generated constructor stub
        RestAssured.baseURI = baseURL;
        spec = new RequestSpecBuilder().setBaseUri(baseURL).build();

    }

    public String getRequest(String endPoint, int responseCode) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : GET");
        Response response = RestAssured.given().spec(spec).get(endPoint);
        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());

        Assert.assertTrue(response.getStatusCode() == responseCode, response.getBody().asString());

        return response.getBody().asString();
    }

    public String getRequest(String endPoint, Map<String, String> queryParam, int responseCode) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : GET");
        LOGGER.info("Query Param : "+queryParam);
        Response response = RestAssured.given().spec(spec).queryParams(queryParam).get(endPoint);
        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());

        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response.getBody().asString();
    }

    public String getRequest(String endPoint, Map<String, String> queryParam, Map<String, String> headers, int responseCode) throws Exception {
        Response response=null;
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : GET");
        if(queryParam!=null && headers!=null) {
            LOGGER.info("Query Param : "+queryParam);
            LOGGER.info("Request Headers : "+headers);
            response = RestAssured.given().spec(spec).queryParams(queryParam).headers(headers).get(endPoint);
        }else if(queryParam!=null) {
            LOGGER.info("Query Param : "+queryParam);
            response = RestAssured.given().spec(spec).queryParams(queryParam).get(endPoint);
        }else {
            LOGGER.info("Request Headers : "+headers);
            response = RestAssured.given().spec(spec).headers(headers).get(endPoint);
        }
        LOGGER.info(response.getStatusCode() + response.getBody().asString());
        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());
        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response.getBody().asString();


    }

    public Response getRequest(String endPoint, Map<String, String> queryParam, Map<String, String> headers) throws Exception {
        Response response=null;
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : GET");
        if(queryParam!=null && headers!=null) {
            LOGGER.info("Query Param : "+queryParam);
            LOGGER.info("Request Headers : "+headers);
            response = RestAssured.given().spec(spec).queryParams(queryParam).headers(headers).get(endPoint);
        }else if(queryParam!=null) {
            LOGGER.info("Query Param : "+queryParam);
            response = RestAssured.given().spec(spec).queryParams(queryParam).get(endPoint);
        }else {
            LOGGER.info("Request Headers : "+headers);
            response = RestAssured.given().spec(spec).headers(headers).get(endPoint);
        }
        LOGGER.info(response.getStatusCode() + response.getBody().asString());
        LOGGER.info("Response : "+response.getBody().asString());

        return response;


    }

    public String postRequest(String endPoint, JSONObject body, int responseCode) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : POST");
        LOGGER.info("Request body : "+body.toString());
        Response response = RestAssured.given().spec(spec).body(body.toString()).post(endPoint);

        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());

        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response.getBody().asString();
    }
    public String postRequest(String endPoint,Map<String, String> headers, JSONObject body, int responseCode) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : POST");
        LOGGER.info("Headers : "+ headers.toString());
        LOGGER.info("Request body : "+body.toString());
        Response response = RestAssured.given().spec(spec).headers(headers).body(body.toString()).post(endPoint);

        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());

        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response.getBody().asString();
    }
    public String postRequest(String endPoint,Map<String, String> headers, Object body, int responseCode) throws Exception {
        String s=RestAssured.baseURI+endPoint;
        LOGGER.info("Request URL : "+RestAssured.baseURI+endPoint);
        LOGGER.info("Request Method : POST");
        LOGGER.info("Headers : "+ headers.toString());
        // report.LOGGER.info("Request body : "+body.toString());
        Response response = RestAssured.given().headers(headers).body(body).log().all().post(endPoint);

        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());

        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response.getBody().asString();
    }

    public String postRequest(String endPoint,Map<String, String> headers, String body, int responseCode) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : POST");
        LOGGER.info("Request Headers : "+headers);
        LOGGER.info("Request body : "+body.toString());
        Response response = RestAssured.given().spec(spec).headers(headers).body(body).post(endPoint);
        LOGGER.info(response.getStatusCode()+response.asString());

        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());

        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response.getBody().asString();
    }

    public boolean postRequestWOAssert(String endPoint,Map<String, String> headers, String body, int responseCode) throws Exception {
        boolean flag=false;
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : POST");
        LOGGER.info("Request Headers : "+headers);
        LOGGER.info("Request body : "+body.toString());
        Response response = RestAssured.given().spec(spec).headers(headers).body(body).post(endPoint);
        LOGGER.info(response.getStatusCode()+response.asString());

        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());

        if(response.getStatusCode()==responseCode){
            flag=true;
        }

        return flag;
    }

    public Response postRequest(String endPoint,Map<String, String> headers, String body) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : POST");
        LOGGER.info("Request Headers : "+headers);
        LOGGER.info("Request body : "+body.toString());
        Response response = RestAssured.given().spec(spec).headers(headers).body(body).post(endPoint);
        LOGGER.info(response.getStatusCode()+response.asString());

        LOGGER.info("Response : "+response.getBody().asString());

        return response;
    }

    public String jwtToken(String endPoint,Map<String, String> headers, String body, int responseCode) throws Exception {
        Response response = RestAssured.given().spec(spec).headers(headers).body(body).post(endPoint);
        LOGGER.info(response.getStatusCode()+response.asString());

        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response.getBody().asString();
    }

    public String postRequest(String endPoint,Map<String, String> queryParam, Map<String, String> headers, String body, int responseCode) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : POST");
        Response response;
        if(queryParam!=null && headers!=null) {
            LOGGER.info("Query Param : "+queryParam);
            LOGGER.info("Request Headers : "+headers);
            LOGGER.info("Request body : "+body.toString());
            response = RestAssured.given().spec(spec).queryParams(queryParam).headers(headers).body(body.toString()).post(endPoint);
        }else if(queryParam==null && headers==null) {
            LOGGER.info("Request body : "+body.toString());
            response = RestAssured.given().spec(spec).body(body.toString()).post(endPoint);
        }else if(queryParam!=null) {
            LOGGER.info("Query Param : "+queryParam);
            LOGGER.info("Request body : "+body.toString());
            response = RestAssured.given().spec(spec).queryParams(queryParam).body(body.toString()).post(endPoint);
        }else {
            LOGGER.info("Request Headers : "+headers);
            LOGGER.info("Request body : "+body.toString());
            response = RestAssured.given().spec(spec).headers(headers).body(body.toString()).post(endPoint);
        }

        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());

        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response.getBody().asString();
    }

    public String   postRequest(String endPoint,Map<String, String> queryParam, Map<String, String> headers, int responseCode) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : POST");
        Response response;
        JSONObject body= new JSONObject();
        if(queryParam!=null && headers!=null) {
            LOGGER.info("Query Param : "+queryParam);
            LOGGER.info("Request Headers : "+headers);
            response = RestAssured.given().spec(spec).queryParams(queryParam).headers(headers).body(body.toString()).post(endPoint);
        }else if(queryParam!=null) {
            LOGGER.info("Query Param : "+queryParam);
            response = RestAssured.given().spec(spec).queryParams(queryParam).body(body.toString()).post(endPoint);
        }else {
            LOGGER.info("Request Headers : "+headers);
            response = RestAssured.given().spec(spec).headers(headers).body(body.toString()).post(endPoint);
        }
        LOGGER.info(response.getStatusCode()+ response.asString());

        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());
        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());
        System.out.println(response.getBody().asString());
        return response.getBody().asString();
    }

    public String postRequest_FormData(String endPoint,Map<String, String> queryParam, Map<String, String> headers, Map<String, String> bodyFormData, int responseCode) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : POST");
        Response response;
        if(queryParam!=null && headers!=null) {
            LOGGER.info("Query Param : "+queryParam);
            LOGGER.info("Request Headers : "+headers);
            LOGGER.info("Request body : "+bodyFormData);
            response = RestAssured.given().spec(spec).params(bodyFormData).queryParams(queryParam).headers(headers).body(bodyFormData.toString()).post(endPoint);
        }else if(queryParam!=null) {
            LOGGER.info("Query Param : "+queryParam);
            LOGGER.info("Request body : "+bodyFormData);
            response = RestAssured.given().spec(spec).params(bodyFormData).queryParams(queryParam).body(bodyFormData.toString()).post(endPoint);
        }else {
            LOGGER.info("Request Headers : "+headers);
            LOGGER.info("Request body : "+bodyFormData);
            response = RestAssured.given().spec(spec).params(bodyFormData).headers(headers).body(bodyFormData.toString()).post(endPoint);
        }

        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());

        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response.getBody().asString();
    }

    public String putRequest(String endPoint,Map<String, String> headers, String body, int responseCode) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : PUT");
        LOGGER.info("Request Headers : "+headers);
        LOGGER.info("Request body : "+body.toString());
        Response response = RestAssured.given().spec(spec).headers(headers).body(body).put(endPoint);
        LOGGER.info(response.getStatusCode()+ response.asString());

        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());

        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response.getBody().asString();
    }

    public String putRequest(String endPoint,Map<String, String> queryParam, Map<String, String> headers, String body, int responseCode) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : PUT");
        Response response;
        if(queryParam!=null && headers!=null) {
            LOGGER.info("Query Param : "+queryParam);
            LOGGER.info("Request Headers : "+headers);
            LOGGER.info("Request body : "+body.toString());
            response = RestAssured.given().spec(spec).queryParams(queryParam).headers(headers).body(body).put(endPoint);
        }else if(queryParam!=null) {
            LOGGER.info("Query Param : "+queryParam);
            LOGGER.info("Request body : "+body.toString());
            response = RestAssured.given().spec(spec).queryParams(queryParam).body(body).put(endPoint);
        }else {
            LOGGER.info("Request Headers : "+headers);
            LOGGER.info("Request body : "+body.toString());
            response = RestAssured.given().spec(spec).headers(headers).body(body).put(endPoint);
        }
        LOGGER.info(response.getStatusCode()+ response.asString());

        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());

        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response.getBody().asString();
    }

    public String putRequest(String endPoint,Map<String, String> queryParam, Map<String, String> headers, int responseCode) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : PUT");
        Response response;
        JSONObject body= new JSONObject();
        if(queryParam!=null && headers!=null) {
            LOGGER.info("Query Param : "+queryParam);
            LOGGER.info("Request Headers : "+headers);
            response = RestAssured.given().spec(spec).queryParams(queryParam).headers(headers).body(body.toString()).put(endPoint);
        }else if(queryParam!=null) {
            LOGGER.info("Query Param : "+queryParam);
            response = RestAssured.given().spec(spec).queryParams(queryParam).body(body.toString()).put(endPoint);
        }else {
            LOGGER.info("Request Headers : "+headers);
            response = RestAssured.given().spec(spec).headers(headers).body(body.toString()).put(endPoint);
        }
        LOGGER.info(response.getStatusCode()+ response.asString());

        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());

        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response.getBody().asString();
    }

    public String deleteRequest(String endPoint,Map<String, String> headers, int responseCode) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : DELETE");
        LOGGER.info("Request Headers : "+headers);
        Response response = RestAssured.given().spec(spec).headers(headers).delete(endPoint);

        LOGGER.info(response.getStatusCode()+ response.asString());

        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());

        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response.getBody().asString();
    }

    public String deleteRequest(String endPoint,Map<String, String> headers, Map<String, String> queryParam, int responseCode) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : DELETE");
        LOGGER.info("Query Param : "+queryParam);
        LOGGER.info("Request Headers : "+headers);
        Response response = RestAssured.given().spec(spec).queryParams(queryParam).headers(headers).delete(endPoint);

        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());

        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response.getBody().asString();
    }

    public String patchRequest(String endPoint,Map<String, String> headers, JSONObject body, int responseCode) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : PATCH");
        LOGGER.info("Request Headers : "+headers);
        LOGGER.info("Request body : "+body.toString());
        Response response = RestAssured.given().spec(spec).headers(headers).body(body.toString()).patch(endPoint);

        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());

        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response.getBody().asString();
    }

    public JSONObject updateJson(JSONObject obj1, String keyString, String newValue) throws Exception {
        // get the keys of json object
        Iterator iterator = obj1.keys();
        String key = null;
        while (iterator.hasNext()) {
            key = (String) iterator.next();
            // if the key is a string, then update the value
            if ((obj1.optJSONArray(key) == null) && (obj1.optJSONObject(key) == null)) {
                if ((key.equals(keyString))) {
                    // put new value
                    obj1.put(key, newValue);
                    return obj1;
                }
            }

            // if it's jsonobject
            if (obj1.optJSONObject(key) != null) {
                updateJson(obj1.getJSONObject(key), keyString, newValue);
            }

            // if it's jsonarray
            if (obj1.optJSONArray(key) != null) {
                JSONArray jArray = obj1.getJSONArray(key);
                for (int i = 0; i < jArray.length(); i++) {
                    try {
                        updateJson(jArray.getJSONObject(i), keyString, newValue);
                    }catch (Exception e) {
                        // TODO: handle exception
                        break;
                    }

                }
            }
        }
        return obj1;
    }
    public String putRequest(String endPoint,Map<String, String> queryParam, Map<String, String> headers, Map<String, String> body, int responseCode) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : PUT");
        Response response;
        if(queryParam!=null && headers!=null) {
            LOGGER.info("Query Param : "+queryParam);
            LOGGER.info("Request Headers : "+headers);
            LOGGER.info("Request body : "+body);
            response = RestAssured.given().spec(spec).queryParams(queryParam).headers(headers).body(body).put(endPoint);
        }else if(queryParam!=null) {
            LOGGER.info("Query Param : "+queryParam);
            LOGGER.info("Request body : "+body);
            response = RestAssured.given().spec(spec).queryParams(queryParam).body(body).put(endPoint);
        }else {
            LOGGER.info("Request Headers : "+headers);
            LOGGER.info("Request body : "+body);
            response = RestAssured.given().spec(spec).headers(headers).body(body).put(endPoint);
        }
        LOGGER.info(response.getStatusCode()+ response.asString());

        LOGGER.info("Response : "+response.getBody().asString());
        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());

        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response.getBody().asString();
    }


    public Response putRequest(String endPoint,Map<String, String> headers, String body) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : PUT");
        LOGGER.info("Request Headers : "+headers);
        LOGGER.info("Request body : "+body.toString());
        Response response = RestAssured.given().headers(headers).body(body).put(endPoint);
        LOGGER.info(response.getStatusCode()+ response.asString());

        LOGGER.info("Response : "+response.getBody().asString());
        //        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());
        //
        //        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response;
    }
    //method with comments
    public Response putRequest(String endPoint,Map<String, String> headers, String body,String comments) throws Exception {
        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
        LOGGER.info("Request Method : PUT");
        LOGGER.info("Request Headers : "+headers);
        LOGGER.info("Request body : "+body.toString());
        Response response = RestAssured.given().headers(headers).body(body).put(endPoint);
        LOGGER.info(response.getStatusCode()+ response.asString());
        LOGGER.info(response.getStatusCode()+ comments);

        LOGGER.info("Response : "+response.getBody().asString());
        //        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());
        //
        //        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());

        return response;
    }

    public Response postRequest_withParam(String endPoint, Map<String, String> headers, Map<String, String> queryParam, Map<String, String> body)
            throws Exception {
        String s = RestAssured.baseURI + endPoint;
        LOGGER.info("Request URL : " + RestAssured.baseURI + endPoint);
        LOGGER.info("Request Method : POST");
        LOGGER.info("Headers : " + headers.toString());
        LOGGER.info("Headers : " + queryParam.toString());
        // report.logInfo("Request body : "+body.toString());
        Response response = RestAssured
                .given()
                .headers(headers)
                .queryParams(queryParam)
                .body(body)
                .log()
                .all()
                .post(endPoint);

        LOGGER.info("Response : " + response.getBody().asString());
      //  LOGGER.info(responseCode + " expected response code validation with actual " + response.getStatusCode());

       // return response.getBody().asString();
        return response ;
    }



    //method with comments
//    public Response putRequest(String endPoint,Map<String, String> headers, String body,String comments) throws Exception {
//        LOGGER.info("Request URL : "+RestAssured.baseURI +endPoint);
//        LOGGER.info("Request Method : PUT");
//        LOGGER.info("Request Headers : "+headers);
//        LOGGER.info("Request body : "+body.toString());
//        Response response = RestAssured.given().headers(headers).body(body).put(endPoint);
//        LOGGER.info(response.getStatusCode()+ response.asString());
//        LOGGER.info(response.getStatusCode()+ comments);
//
//        LOGGER.info("Response : "+response.getBody().asString());
//        //        LOGGER.info(responseCode +" expected response code validation with actual "+response.getStatusCode());
//        //
//        //        Assert.assertTrue(response.getStatusCode()==responseCode,response.getBody().asString());
//
//        return response;
//    }

}