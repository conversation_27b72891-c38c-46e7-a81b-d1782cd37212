package TestingLogics;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.annotations.Test;

public class TestDataCreationMain extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(TestDataCreationMain.class);

    //TestDataCreationBase TestDataBse = new TestDataCreationBase();

    @Test(alwaysRun = true)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void CreateTestData() throws Exception {
        LOGGER.info("Creating Test Data :) ");

      //TODO - Put valid PSA App token of "**********" agent on "CommonAgentToken" variable present in "BaseMethod" class
      //TODO - Put valid XMW token of "**********" OE Panel Agent on "XMWCookie" variable present in "BaseMethod" class


   /*   TODO - Put solution reference name and the number of data/account needed, read instruction mentioned below
      TODO -- put "100k" in solution field for P2P 100K solution
      TODO -- put "QR500k" in solution field for QR Merchant 500K solution
      TODO -- put "QRUL" in solution field for QR Merchant Unlimited solution
      TODO -- put "50k" in solution field for offline 50k solution
      TODO -- put "upm" in solution field for unified payment merchant solution
      TODO -- put "upmInt" in solution field for unified payment INTEGRATED merchant solution*/



       // TestDataBse.CreateTestData("QR500k",5);

    }


}
