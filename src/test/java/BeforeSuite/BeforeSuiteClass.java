package BeforeSuite;

import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.annotations.AfterSuite;
import org.testng.annotations.BeforeSuite;

import java.io.IOException;
import java.sql.SQLException;

public class BeforeSuiteClass extends BaseMethod {

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    private static final Logger LOGGER = LogManager.getLogger(BeforeSuiteClass.class);

    //    static {
//       Reporter.report = new MultiReport(new PortalReport());
//           }
    String stagingServer = getCurrentStagingServer();

    public String getCurrentStagingServer() {


        String apiUrl = P.API.get("api_url");
        if (apiUrl.contains("https://goldengate-staging7.paytm.com/MerchantService")) {
            return "7";  // staging PAN
        } else if (apiUrl.contains("https://goldengate-staging6.paytm.com/MerchantService")) {
            return "6";  // preprod PAN
        }
        return "null"; // default
    }


    @BeforeSuite()
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void BeforeSuiteLogin() throws Exception {


        LOGGER.info(" Before Suite Method for Agent Login ");

        LOGGER.info(" GG App Agent Login");
        CommonAgentToken = AgentSessionToken("8010630022", "paytm@123");
        LOGGER.info(" Common Agent Token is : " + CommonAgentToken);

        LOGGER.info("OE Panel Agent Login");
        Response responseObject = middlewareServicesObject.v1Token("7771216290", "paytm@123");
        XMWCookie = responseObject.getHeader("Set-Cookie").toString();
        LOGGER.info(" OE Panel Cookie is  : " + XMWCookie);

        LOGGER.info(" Paytm App Login ");
        SsoToken = PaytmAppSsoToken("8010630022", "paytm@123");
        LOGGER.info("This is SSO Token for Paytm App : " + SsoToken);

        //Addig Money into Wallet A/C
        //AddMoneyToWallet(SsoToken, "10000");

        LOGGER.info(" Before Suite Method has completed ");
        // reads system IPAddress
/*
        URL url_name = new URL("http://bot.whatismyipaddress.com");
        BufferedReader sc = new BufferedReader(new InputStreamReader(url_name.openStream()));
        DeviceIP = sc.readLine().trim();
        LOGGER.info(" This is Device IP : " + DeviceIP);
*/

    }

    @AfterSuite
    public void AfterSuite() throws SQLException {
        //Addig Money into Wallet A/C
        //AddMoneyToWallet(SsoToken, "10000");
    }


}
