import com.goldengate.common.AIModel;
import com.goldengate.common.TestDataCollector;
import com.goldengate.common.TestPrioritizer;
import org.testng.TestNG;
import org.testng.xml.XmlClass;
import org.testng.xml.XmlSuite;
import org.testng.xml.XmlTest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class TestRunner {
    public static void main(String[] args) {
        // Collect data for prioritization
        TestDataCollector dataCollector = new TestDataCollector();
        Map<String, Object> testData = dataCollector.collectTestData();

        // Prioritize tests
        TestPrioritizer prioritizer = new TestPrioritizer(new AIModel());
        List<String> prioritizedTests = prioritizer.prioritizeTests(testData);

        // Create TestNG instance

        TestNG testng = new TestNG();

        // Run prioritized tests using TestNG

//        testng.setTestClasses(prioritizedTests.stream()
//                .map(TestRunner::getTestClassByName)
//                .toArray(Class[]::new));
//        testng.run();

        // Set parallel execution at the 'classes' level with a thread count of 10
        XmlSuite suite = new XmlSuite();
        suite.setName("Devices - Onboarding Engine");
        suite.setParallel(XmlSuite.ParallelMode.CLASSES);
        suite.setThreadCount(10);

        XmlTest test = new XmlTest(suite);
        test.setName("Devices");
        test.setPreserveOrder(true);

        List<XmlClass> xmlClasses = new ArrayList<>();
        for (String testClassName : prioritizedTests) {
            xmlClasses.add(new XmlClass(testClassName));
        }
        test.setXmlClasses(xmlClasses);

        List<XmlSuite> suites = new ArrayList<>();
        suites.add(suite);
        testng.setXmlSuites(suites);

        // Add listeners from the devices.xml
        testng.addListener(new com.paytm.apitools.listeners.SuiteListener());
        testng.addListener(new com.paytm.apitools.customreporter.CustomReporter());
        testng.addListener(new AfterSuite.ListenerTest());

        // Run the tests
        testng.run();
    }

    private static Class<?> getTestClassByName(String className) {
        try {
            // Adjusted the package name to correctly reflect your structure
            return Class.forName(className);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Test class not found: " + className, e);
        }
    }
}
