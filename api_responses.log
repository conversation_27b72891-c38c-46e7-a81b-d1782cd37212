Test: TC01_Create_Lead
Time: 2024-12-04T13:54:40.35647
Status Code: 307
Response Body: {"agentTncStatus":true,"displayMessage":"We are unable to fetch merchant information. Please try again. (Ref: E-27R-98-307)","moveBack":true,"walletPrime":false,"proceed":false,"abort":false,"kycDone":false,"loanConsent":false,"redirectToOldEDCFlow":false,"redirectToQRMappingStatusScreen":false,"qrMappingLeadRejected":false,"canRemoveEdcContext":false,"hasEdcContext":false,"hasEdcContextDropped":false,"hasTapNPayContext":false,"canRemoveTapNPayContext":false,"hasTapNPayContextDropped":false,"message":"We are unable to fetch merchant information. Please try again. (Ref: E-27R-98-307)"}
----------------------------------------
Test: TC01_Create_Lead
Time: 2024-12-04T13:54:45.224212
Status Code: 200
Response Body: {"agentTncStatus":true,"displayMessage":"Lead successfully created.","moveBack":false,"walletPrime":false,"leadId":"0fc05f1f-df28-4790-a3fd-a581524c2c64","proceed":false,"abort":false,"kycDone":false,"loanConsent":false,"entityType":"INDIVIDUAL","redirectToOldEDCFlow":false,"redirectToQRMappingStatusScreen":false,"qrMappingLeadRejected":false,"canRemoveEdcContext":false,"hasEdcContext":false,"hasEdcContextDropped":false,"hasTapNPayContext":false,"canRemoveTapNPayContext":false,"hasTapNPayContextDropped":false,"redirectToHomeScreen":false}
----------------------------------------
Test: TC01_Create_Lead
Time: 2024-12-04T13:54:51.301185
Status Code: 307
Response Body: {"agentTncStatus":true,"displayMessage":"We are unable to fetch merchant information. Please try again. (Ref: E-27S-98-307)","moveBack":true,"walletPrime":false,"proceed":false,"abort":false,"kycDone":false,"loanConsent":false,"redirectToOldEDCFlow":false,"redirectToQRMappingStatusScreen":false,"qrMappingLeadRejected":false,"canRemoveEdcContext":false,"hasEdcContext":false,"hasEdcContextDropped":false,"hasTapNPayContext":false,"canRemoveTapNPayContext":false,"hasTapNPayContextDropped":false,"message":"We are unable to fetch merchant information. Please try again. (Ref: E-27S-98-307)"}
----------------------------------------
Test: TC01_Create_Lead
Time: 2024-12-04T13:54:57.725318
Status Code: 200
Response Body: {"agentTncStatus":true,"displayMessage":"Lead successfully created.","moveBack":false,"walletPrime":false,"leadId":"c7bc9362-2819-4531-913e-b601c9165102","proceed":false,"abort":false,"kycDone":false,"loanConsent":false,"entityType":"INDIVIDUAL","redirectToOldEDCFlow":false,"redirectToQRMappingStatusScreen":false,"qrMappingLeadRejected":false,"canRemoveEdcContext":false,"hasEdcContext":false,"hasEdcContextDropped":false,"hasTapNPayContext":false,"canRemoveTapNPayContext":false,"hasTapNPayContextDropped":false,"redirectToHomeScreen":false}
----------------------------------------
Test: TC01_Create_Lead
Time: 2024-12-04T13:56:47.104642
Status Code: 307
Response Body: {"agentTncStatus":true,"displayMessage":"We are unable to fetch merchant information. Please try again. (Ref: E-27y-98-307)","moveBack":true,"walletPrime":false,"proceed":false,"abort":false,"kycDone":false,"loanConsent":false,"redirectToOldEDCFlow":false,"redirectToQRMappingStatusScreen":false,"qrMappingLeadRejected":false,"canRemoveEdcContext":false,"hasEdcContext":false,"hasEdcContextDropped":false,"hasTapNPayContext":false,"canRemoveTapNPayContext":false,"hasTapNPayContextDropped":false,"message":"We are unable to fetch merchant information. Please try again. (Ref: E-27y-98-307)"}
----------------------------------------
Test: TC01_Create_Lead
Time: 2024-12-04T13:56:53.560058
Status Code: 200
Response Body: {"agentTncStatus":true,"displayMessage":"Lead successfully created.","moveBack":false,"walletPrime":false,"leadId":"e949ab04-3d6d-49e9-b22a-7d2c2c7c6293","proceed":false,"abort":false,"kycDone":false,"loanConsent":false,"entityType":"INDIVIDUAL","redirectToOldEDCFlow":false,"redirectToQRMappingStatusScreen":false,"qrMappingLeadRejected":false,"canRemoveEdcContext":false,"hasEdcContext":false,"hasEdcContextDropped":false,"hasTapNPayContext":false,"canRemoveTapNPayContext":false,"hasTapNPayContextDropped":false,"redirectToHomeScreen":false}
----------------------------------------
Test: TC01_Create_Lead
Time: 2024-12-04T13:57:32.186772
Status Code: 200
Response Body: {"agentTncStatus":true,"displayMessage":"Lead successfully created.","moveBack":false,"walletPrime":false,"leadId":"07596533-27b0-4692-9401-a3d8b50d086f","proceed":false,"abort":false,"kycDone":false,"loanConsent":false,"entityType":"INDIVIDUAL","redirectToOldEDCFlow":false,"redirectToQRMappingStatusScreen":false,"qrMappingLeadRejected":false,"canRemoveEdcContext":false,"hasEdcContext":false,"hasEdcContextDropped":false,"hasTapNPayContext":false,"canRemoveTapNPayContext":false,"hasTapNPayContextDropped":false,"redirectToHomeScreen":false}
----------------------------------------
Test: TC01_Create_Lead
Time: 2024-12-04T13:57:33.469789
Status Code: 200
Response Body: {"agentTncStatus":true,"displayMessage":"Lead successfully created.","moveBack":false,"walletPrime":false,"leadId":"f0d15e3c-88c1-4443-b96c-7b3649878cda","proceed":false,"abort":false,"kycDone":false,"loanConsent":false,"entityType":"INDIVIDUAL","redirectToOldEDCFlow":false,"redirectToQRMappingStatusScreen":false,"qrMappingLeadRejected":false,"canRemoveEdcContext":false,"hasEdcContext":false,"hasEdcContextDropped":false,"hasTapNPayContext":false,"canRemoveTapNPayContext":false,"hasTapNPayContextDropped":false,"redirectToHomeScreen":false}
----------------------------------------
