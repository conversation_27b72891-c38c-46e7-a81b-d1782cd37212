<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">


<suite name="ATS -Onboarding Engine" parallel="tests" thread-count="10" preserve-order="true" verbose="30">

    <test name="Payments" preserve-order="true" thread-count="5" parallel="classes">


        <packages>

            <package name="BeforeSuite"/>
            <!--     <package name="OCL.Business.AssistedMerchantOnboard.*"/>
                <package name="OCL.Business.OrganisedMerchant.*"/>
                <package name="OCL.Business.QRMerchant500K.*"/>
                <package name="OCL.Business.QRMerchantUnlimited.*"/>

        <package name="OCL.Business.UpgradeMerchantPlan.*"/>
         <package name="OCL.DIY.OnlineMerchant.*"/>
         <package name="OCL.Individual.Merchant50K.*"/>
         <package name="OCL.Individual.Merchant100K.*"/>
         <package name="OCL.Individual.ProfileUpdate.*"/>
         <package name="OCL.Individual.QrMapping.*"/>
         <package name="OCL.Individual.RevisitMerchant.*"/>
         <package name="OCL.Individual.UnifiedPaymentMerchant.*"/> -->
        </packages>
    </test>
    <!--

    <test name="Lending" preserve-order="true" thread-count="5" parallel="classes" >

    <packages>
        <package name="OCL.ConsumerLending.*"/>
        <package name="OCL.Lending.*"/>
    </packages>

    </test>-->
    <!--
      <test name="Lending" preserve-order="true" thread-count="5" parallel="classes" >

          <packages>
               <package name="OCL.ConsumerLending.*"/>
           <package name="OCL.Lending.*"/>
       </packages>

   </test>-->

    <!--
        <test name="Devices" preserve-order="true" thread-count="5" parallel="classes">

          <packages>
              <package name="OCL.Business.MapEDC.*"/>
              <package name="OCL.Business.MapPOS.*"/>
              <package name="OCL.Business.UnmapEDC.*"/>
              <package name="OCL.DIY.CashAtPosDIY.*"/>
              <package name="OCL.DIY.EMIOfferingDIY.*"/>
              <package name="OCL.DIY.MapEdcDIY.*"/>
              <package name="OCL.DIY.MposDIY.*"/>
              <package name="OCL.Individual.FastagAssisted.*"/>
              <package name="OCL.Individual.FastagDiy.*"/>
              <package name="OCL.Individual.SoundBox.*"/>
          </packages>


          </test>-->

    <!--
      <test name="ATS" preserve-order="true" thread-count="5" parallel="classes">
          <packages>
           <package name="ats.*"/>
         </packages>
   </test> -->
    <!--
       <test name="Others" preserve-order="true" thread-count="5" parallel="classes">

           <packages>
               <package name="OCL.Individual.ChannelOnboarding.*"/>
               <package name="OCL.Individual.PSA.*"/>
               <package name="OCL.Individual.PSA_DIY.*"/>
               <package name="OCL.Individual.RegisterLead.*"/>
               <package name="OCL.Insurance.*"/>
               <package name="OCL.ManageAgent.*"/>
               <package name="OCL.Panel.*"/>
               <package name="OCL.GenericTestsForApis.*"/>
               <package name="OCL.CRM.*"/>

           </packages>

       </test>

            -->

    <!--   <test name="Lending" preserve-order="true" thread-count="5" parallel="classes" >
           <packages>
             <package name="OCL.Lending.*"/>
         </packages> -->


    <!--     </test> -->

    <!--  <test name="Devices" preserve-order="true" thread-count="5" parallel="classes">

         <packages>
             <package name="OCL.Business.MapEDC.*"/>
             <package name="OCL.Business.MapPOS.*"/>
             <package name="OCL.Business.UnmapEDC.*"/>
             <package name="OCL.DIY.CashAtPosDIY.*"/>
             <package name="OCL.DIY.EMIOfferingDIY.*"/>
             <package name="OCL.DIY.MapEdcDIY.*"/>
             <package name="OCL.DIY.MposDIY.*"/>
             <package name="OCL.Individual.FastagAssisted.*"/>
             <package name="OCL.Individual.FastagDiy.*"/>
             <package name="OCL.Individual.SoundBox.*"/>
         </packages>

     </test>
  -->
    <test name="ATS" preserve-order="true" thread-count="5" parallel="classes">
        <packages>
            <package name="ats.*"/>
        </packages>
    </test>

    <!-- <test name="Others" preserve-order="true" thread-count="5" parallel="classes">

        <packages>
            <package name="OCL.Individual.ChannelOnboarding.*"/>
            <package name="OCL.Individual.PSA.*"/>
            <package name="OCL.Individual.PSA_DIY.*"/>
            <package name="OCL.Individual.RegisterLead.*"/>
            <package name="OCL.Insurance.*"/>
            <package name="OCL.ManageAgent.*"/>
            <package name="OCL.Panel.*"/>
            <package name="OCL.GenericTestsForApis.*"/>
            <package name="OCL.CRM.*"/>

        </packages>

    </test> -->

    <listeners>
        <listener class-name="com.paytm.apitools.listeners.SuiteListener"/>
        <listener class-name="com.paytm.apitools.customreporter.CustomReporter"/>
        <!--        <listener class-name="com.paytm.framework.reporting.listeners.RPTestListener"/>-->
        <listener class-name="AfterSuite.ListenerTest"/>
    </listeners>
</suite>
